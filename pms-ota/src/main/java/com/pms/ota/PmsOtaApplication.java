package com.pms.ota;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Configuration;

@SpringBootApplication
@MapperScan({
        "com.pms.czmembership.dao",
        "com.pms.czhotelfoundation.dao",
        "com.pms.ota.domain.mapper"}
)
//@ComponentScan({"com.pms.*","com.pms.*.*"})
@EnableDiscoveryClient
@Configuration
public class PmsOtaApplication {
    public static void main(String[] args) {
        SpringApplication.run(PmsOtaApplication.class, args);
    }
}
