package com.pms.czabssalegoods;

import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.pmswarehouse.bean.GoodsClass;
import com.pms.pmswarehouse.bean.GoodsDumb;
import com.pms.pmswarehouse.dao.GoodsClassDao;
import com.pms.pmswarehouse.dao.GoodsDumbDao;
import net.sf.json.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@ComponentScan("com.pms.*")
@SpringBootTest
public class CzAbsSalegoodsApplicationTests {

	@Autowired
	private GoodsDumbDao goodsDumbDao;

	@Autowired
	private GoodsClassDao goodsClassDao;

	@Autowired
	private AccountDao accountDao;

	@Test
	public void contextLoads() {

		List<Account> accounts = accountDao.selectBySearch(null);

		List<GoodsDumb> goodsDumbs = goodsDumbDao.selectBySearch(null);

		System.out.println("yafangzaaa"+goodsDumbs.size());

		System.out.println(accounts.size());

		List<GoodsClass> goodsClasses = goodsClassDao.selectBySearch(null);

		for (GoodsClass gs : goodsClasses){

			System.out.println(JSONObject.fromObject(gs).toString());

		}




	}


}
