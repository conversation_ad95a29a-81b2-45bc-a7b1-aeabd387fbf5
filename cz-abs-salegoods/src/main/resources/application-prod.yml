# 数据库操作
spring:
  datasource:
    type: com.alibaba.druid.pool.xa.DruidXADataSource
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: none
  redis:
    host: **************
    port: 6379
    password: 643e145d0e5c42ef073f4f39c975ff0efeb959b7d4642fcb
    database: 5
    timeout: 30000
  application:
    name: CZ-ABS-SALEGOODS
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true

mybatis:
  mapper-locations: classpath*:mapper/*/*.xml,mapper/*.xml  #注意：一定要对应mapper映射xml文件的所在路径
  configuration:
    log-impl: com.pms.czpmsutils.MybatisLoggerImpl

eureka:
  client:
    service-url:
      defaultZone: http://**************:8761/eureka
  instance:
    hostname: ${eureka.instance.ip-address}
    prefer-ip-address: false
    ip-address: **************
    instance-id: CZ-ABS-SALEGOODS
server:
  port: 8131
  tomcat:
    max-http-post-size: 10485760

pmsorder:
  datasource:
    #druid相关配置
    druid:
      driverClassName: com.mysql.jdbc.Driver
      #配置基本属性
      url: 2222f27cfecd13ba718e9e7f1d7774f06f9aeb7b527a1575b83875683ae2414f0a3ddc2b4d0a3bf6b4086f762ef8bb61ba39d77f472a2a0f6d8a62ef0c76e3927dc9fe43ee8bb83532b6e400428422adf7dc2df747804c5fdef888e74dfca515c5597791d3eb88f8af1eef4628d36499
      username: 8336bf9f284f20c3
      password: d1f2341cd0a3bb9cfeb959b7d4642fcb
      #配置初始化大小/最小/最大
      initialSize: 1
      minIdle: 1
      maxActive: 20
      #获取连接等待超时时间
      maxWait: 60000
      #间隔多久进行一次检测，检测需要关闭的空闲连接
      timeBetweenEvictionRunsMillis: 60000
      #一个连接在池中最小生存的时间
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      #打开PSCache，并指定每个连接上PSCache的大小。oracle设为true，mysql设为false。分库分表较多推荐设置为false
      poolPreparedStatements: false
      maxPoolPreparedStatementPerConnectionSize: 20

account:
  datasource:
    #druid相关配置
    druid:
      driverClassName: com.mysql.jdbc.Driver
      #配置基本属性
      url: 2222f27cfecd13ba718e9e7f1d7774f06f9aeb7b527a1575b83875683ae2414f0a3ddc2b4d0a3bf6b4086f762ef8bb61ba39d77f472a2a0f6d8a62ef0c76e3927dc9fe43ee8bb83532b6e400428422adf7dc2df747804c5fdef888e74dfca515c5597791d3eb88f8af1eef4628d36499
      username: 8336bf9f284f20c3
      password: d1f2341cd0a3bb9cfeb959b7d4642fcb
      #配置初始化大小/最小/最大
      initialSize: 1
      minIdle: 1
      maxActive: 20
      #获取连接等待超时时间
      maxWait: 60000
      #间隔多久进行一次检测，检测需要关闭的空闲连接
      timeBetweenEvictionRunsMillis: 60000
      #一个连接在池中最小生存的时间
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      #打开PSCache，并指定每个连接上PSCache的大小。oracle设为true，mysql设为false。分库分表较多推荐设置为false
      poolPreparedStatements: false
      maxPoolPreparedStatementPerConnectionSize: 20

warehouse:
  datasource:
    #druid相关配置
    druid:
      driverClassName: com.mysql.jdbc.Driver
      #配置基本属性
      url: 2222f27cfecd13ba718e9e7f1d7774f06f9aeb7b527a1575b83875683ae2414f0a3ddc2b4d0a3bf6b4086f762ef8bb61ba39d77f472a2a0f6d8a62ef0c76e3927dc9fe43ee8bb83532b6e400428422adf7dc2df747804c5fdef888e74dfca515c5597791d3eb88f8af1eef4628d36499
      username: 8336bf9f284f20c3
      password: d1f2341cd0a3bb9cfeb959b7d4642fcb
      #配置初始化大小/最小/最大
      initialSize: 1
      minIdle: 1
      maxActive: 20
      #获取连接等待超时时间
      maxWait: 60000
      #间隔多久进行一次检测，检测需要关闭的空闲连接
      timeBetweenEvictionRunsMillis: 60000
      #一个连接在池中最小生存的时间
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      #打开PSCache，并指定每个连接上PSCache的大小。oracle设为true，mysql设为false。分库分表较多推荐设置为false
      poolPreparedStatements: false
      maxPoolPreparedStatementPerConnectionSize: 20
pagehelper:
  auto-dialect: true
  supportMethodsArguments: true
