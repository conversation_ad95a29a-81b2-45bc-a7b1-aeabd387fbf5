package com.pms.czabssalegoods.service;

import com.github.pagehelper.Page;
import com.pms.czabssalegoods.bean.AddGoodsForRegistParam;
import com.pms.czabssalegoods.bean.ReturnedGoodsParam;
import com.pms.czpmsutils.ResponseData;
import com.pms.pmswarehouse.bean.GoodsDumb;
import com.pms.pmswarehouse.bean.request.SelectGoodsStockParam;
import com.pms.pmswarehouse.bean.search.GoodsDumbSearch;
import net.sf.json.JSONObject;

public interface SalegoodsService {
    /**
     * 查询当前酒店所有哑房账信息
     * @param goodsDumbSearch
     * @return
     */
    public Page<GoodsDumb> findAllGoodsDumb(GoodsDumbSearch goodsDumbSearch) throws Exception;

    /**
     * 添加或删除当前酒店的哑房账信息
     * @param param
     * @return
     */
    public ResponseData addOrUpdateGoodsDumb(JSONObject param);

    /**
     * 现付账 冲账
     * @param param
     * @return
     */
    public ResponseData cancelGoodsDumb(JSONObject param);


    /**
     * 查询现付账明细
     * @param param
     * @return
     */
    public ResponseData findGoodsDumbInfo(JSONObject param);

    /**
     * 登记单或者预订单添加小商品
      * @param param
     * @return
     */
    public ResponseData registAddGoods(AddGoodsForRegistParam param);


    /**
     * 小商品退货
     * @return
     */
    public ResponseData returnedGoods(ReturnedGoodsParam param);

    ResponseData selectGoodsStock(SelectGoodsStockParam param);

//    void exportGoodsStock(SelectGoodsStockParam param, HttpServletResponse response);

}
