package com.pms.czabssalegoods;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication
@ComponentScan({"com.pms.czpmsutils.aspect.**","com.pms.czaccount.service.shouqianba","com.pms.czaccount.service.common","com.pms.czabssalegoods.*","com.pms.pmswarehouse.service.*","com.pms.czmembership.service.memeber.*","com.pms.czaccount.service.alipay.*","com.pms.czaccount.service.wechat.*"})
@EnableDiscoveryClient
public class CzAbsSalegoodsApplication {

	public static void main(String[] args) {
		SpringApplication.run(CzAbsSalegoodsApplication.class, args);
	}

	@LoadBalanced
	@Bean
	RestTemplate restTemplate() {
		return new RestTemplate();
	}

}
