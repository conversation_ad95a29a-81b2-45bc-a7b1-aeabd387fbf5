package com.pms.czabssalegoods.web;


import com.github.pagehelper.Page;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.BaseRequest;
import com.pms.pmswarehouse.bean.GoodsInfo;
import com.pms.pmswarehouse.bean.GoodsShoppingOrder;
import com.pms.pmswarehouse.bean.request.*;
import com.pms.pmswarehouse.bean.search.*;
import com.pms.pmswarehouse.service.*;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/good/")
public class GoodsClassController {
    private final static Logger logger = LoggerFactory.getLogger(GoodsClassController.class);

    @Autowired
    GoodsClassService goodsClassService;

    @Autowired
    GoodsSupplyClassService goodsSupplyClassService;

    @Autowired
    GoodsInfoService goodsInfoService;

    @Autowired
    GoodsSupplyService goodsSupplyService;

    @Autowired
    GoodsStockService goodsStockService;

    @Autowired
    GoodsStockDetailService goodsStockDetailService;

    @Autowired
    GoodsTranService goodsTranService;

    @Autowired
    GoodsSupplyAccountService goodsSupplyAccountService;

    @Autowired
    GoodsStockTranService goodsStockTranService;

    @Autowired
    GoodsShoppingOrderService goodsShoppingOrderService;


    @RequestMapping("findAllGoodsClass.do")
    @ResponseBody
    public ResponseData findAllGoodsClass(@RequestBody JSONObject param) {
        return goodsClassService.findAllGoodsClass(param);
    }

    @RequestMapping("searchHotelGoodsClass.do")
    @ResponseBody
    public ResponseData searchHotelGoodsClass(@RequestBody GoodsClassSearch goodsClassSearch) {
        return goodsClassService.searchHotelGoodsClass(goodsClassSearch);
    }

    @RequestMapping("addOrUpdateGoodsClass.do")
    @ResponseBody
    public ResponseData addOrUpdateGoodsClass(@RequestBody JSONObject param) {

        return goodsClassService.addOrUpdateGoodsClass(param);

    }


    @RequestMapping("findAllGoodsSupplyClass.do")
    @ResponseBody
    public ResponseData findAllGoodsSupplyClass(@RequestBody GoodsSupplyClassSearch goodsSupplyClassSearch) {

        return goodsSupplyClassService.findAllGoodsSupplyClass(goodsSupplyClassSearch);
    }

    @RequestMapping("addOrUpdateGoodsSupplyClass.do")
    @ResponseBody
    public ResponseData addOrUpdateGoodsSupplyClass(@RequestBody JSONObject param) {

        return goodsSupplyClassService.addOrUpdateGoodsSupplyClass(param);

    }


    @RequestMapping("addOrUpdateGoodsInfo.do")
    @ResponseBody
    public ResponseData addOrUpdateGoodsInfo(@RequestBody JSONObject param) {

        return goodsInfoService.addOrUpdateGoodsInfo(param);

    }

//    @RequestMapping("findAllGoodsInfo.do")
//    @ResponseBody
//    public Map<String, Object> findAllGoodsInfo(HttpServletRequest request) {
//
//        Map<String, Object> stringObjectMap = goodsInfoService.findAllGoodsInfo(JSONObject.fromObject(com.pms.czpmsutils.HotelUtils.decode(request.getHeader(ER.PARAM_MAP))));
//
//        return stringObjectMap;
//    }

    @RequestMapping("findAllGoodsInfo2.do")
    @ResponseBody
    public ResponseData findAllGoodsInfo2(@RequestBody GoodsInfoSearch search) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            Page<GoodsInfo> goodsInfoByPage = goodsInfoService.findAllGoodsInfo(search);
            responseData.setData(goodsInfoByPage);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }


    @RequestMapping("addOrUpdateGoodsSupply.do")
    @ResponseBody
    public ResponseData addOrUpdateGoodsSupply(@RequestBody JSONObject param) {

        return goodsSupplyService.addOrUpdateGoodsSupply(param);
    }

    @RequestMapping("findAllGoodsSupply.do")
    @ResponseBody
    public ResponseData findAllGoodsSupply(@RequestBody GoodsSupplySearch goodsSupplySearch) {

        return goodsSupplyService.findAllGoodsSupply(goodsSupplySearch);
    }

    @RequestMapping("searchGoodsShoppingOrderList.do")
    @ResponseBody
    public ResponseData searchGoodsShoppingOrderList(@RequestBody GoodsShoppingOrderSearch goodsShoppingOrderSearch) {

        return goodsShoppingOrderService.searchGoodsShoppingOrderList(goodsShoppingOrderSearch);
    }

    @RequestMapping("searchGoodsShoppingCarDetailList.do")
    @ResponseBody
    public ResponseData searchGoodsShoppingCarDetailList(@RequestBody GoodsShoppingcarDetailsSearch goodsShoppingcarDetailsSearch) {

        return goodsShoppingOrderService.searchGoodsShoppingCarDetailList(goodsShoppingcarDetailsSearch);
    }

    @RequestMapping("searchGoodsShoppingCarList.do")
    @ResponseBody
    public ResponseData searchGoodsShoppingCarList(@RequestBody GoodsShoppingcarSearch goodsShoppingcarSearch) {

        return goodsShoppingOrderService.searchGoodsShoppingCarList(goodsShoppingcarSearch);
    }

    @RequestMapping("updateGoodsShoppingOrder.do")
    @ResponseBody
    public ResponseData updateGoodsShoppingOrder(@RequestBody GoodsShoppingOrder goodsShoppingOrder) {

        return goodsShoppingOrderService.updateGoodsShoppingOrder(goodsShoppingOrder);
    }


    @RequestMapping("addOrUpdateGoodsStock.do")
    @ResponseBody
    public ResponseData addOrUpdateGoodsStock(@RequestBody JSONObject param) {

        return goodsStockService.addOrUpdateGoodsStock(param);
    }

    /**
     * 仓库列表
     *
     * @param goodsStockSearch
     * @return
     */
    @RequestMapping("findAllGoodsStock.do")
    @ResponseBody
    public ResponseData findAllGoodsStock(@RequestBody GoodsStockSearch goodsStockSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.searchByPage(goodsStockSearch));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 生成仓库报表
     *
     * @param request
     * @return
     */
    @RequestMapping("genReport.do")
    @ResponseBody
    public ResponseData genReport(@RequestBody BaseRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            goodsStockService.genReport(request);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 生成仓库报表记录
     *
     * @param request
     * @return
     */
    @RequestMapping("listStockNumReportRecord.do")
    @ResponseBody
    public ResponseData listStockNumReportRecord(@RequestBody GoodsStockGoodsNumReportRecordSearch request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.listStockNumReportRecord(request));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 生成仓库报表详情
     *
     * @param request
     * @return
     */
    @RequestMapping("listStockNumReport.do")
    @ResponseBody
    public ResponseData listStockNumReport(@RequestBody GoodsStockGoodsNumReportSearch request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.listStockNumReport(request));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 进货列表
     *
     * @param
     * @return
     */
    @RequestMapping("findGoodsPurchaseRecord.do")
    @ResponseBody
    public ResponseData findAllGoodsStock(@RequestBody GoodsPurchaseRecordSearch goodsPurchaseRecordSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.searchGoodsPurchaseRecord(goodsPurchaseRecordSearch));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 增加或者修改进货记录
     *
     * @param
     * @return
     */
    @RequestMapping("addOrUpdateGoodsPurchaseRecord.do")
    @ResponseBody
    public ResponseData addOrUpdateGoodsPurchaseRecord(@RequestBody PurchaseRecordRequest purchaseRecordRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.addOrUpdateGoodsPurchaseRecord(purchaseRecordRequest));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 增加或者修改进货记录
     *
     * @param
     * @return
     */
    @RequestMapping("addCzjl.do")
    @ResponseBody
    public ResponseData addCzjl(@RequestBody PurchaseRecordRequest purchaseRecordRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.addCzjl(purchaseRecordRequest));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 进货审核
     *
     * @param
     * @return
     */
    @RequestMapping("auditGoodsPurchaseRecord.do")
    @ResponseBody
    public ResponseData auditGoodsPurchaseRecord(@RequestBody PurchaseRecordRequest purchaseRecordRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            goodsStockService.auditGoodsPurchaseRecord(purchaseRecordRequest);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 库存数量列表
     *
     * @param goodsStockGoodsNumSearch
     * @return
     */
    @RequestMapping("findAllGoodsStockNum.do")
    @ResponseBody
    public ResponseData findAllGoodsStockNum(@RequestBody GoodsStockGoodsNumSearch goodsStockGoodsNumSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.findAllGoodsStockNum(goodsStockGoodsNumSearch));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 出入库记录列表
     *
     * @param goodsStockDetailRecordSearch
     * @return
     */
    @RequestMapping("findAllGoodsStockDetailRecord.do")
    @ResponseBody
    public ResponseData findAllGoodsStockDetailRecord(@RequestBody GoodsStockDetailRecordSearch goodsStockDetailRecordSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.findAllGoodsStockDetailRecord(goodsStockDetailRecordSearch));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 出入库记录列表
     *
     * @param goodsStockTranRecordSearch
     * @return
     */
    @RequestMapping("findGoodsStockTranRecord.do")
    @ResponseBody
    public ResponseData findGoodsStockTranRecord(@RequestBody GoodsStockTranRecordSearch goodsStockTranRecordSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.findGoodsStockTranRecord(goodsStockTranRecordSearch));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 增加库存调拨
     *
     * @param
     * @return
     */
    @RequestMapping("addGoodsStockTranRecord.do")
    @ResponseBody
    public ResponseData addGoodsStockTranRecord(@RequestBody GoodsStockTranRecordRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.addGoodsStockTranRecord(request));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 商品销售
     * curl -H "Content-Type: application/json" -H "X-Token: 8973D0B8568C48ECAC9067EC3CC0AEE7"  -X POST -d '{"hotelIsCheckGoodsNum":1,"goodsStockId":9,"remark":"测试备注","goodsInfoList":[{"id":55,"num":1,"price":5000}],"registId":63943,"roomNum":"8888","roomNumId":53338,"registPersionId":11432,"registPersionName":"陈希平","sessionToken":"8973D0B8568C48ECAC9067EC3CC0AEE7"}' 'http://127.0.0.1:8118/hotel/good/addGoodsSaleRecord.do'
     *
     * @param
     * @return
     */
    @RequestMapping("addGoodsSaleRecord.do")
    @ResponseBody
    public ResponseData addGoodsSaleRecord(@RequestBody GoodsSaleRecordRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.addGoodsSaleRecord(request));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 商品退货记录
     * curl -i -H "Content-Type: application/json" -H "X-Token: 8973D0B8568C48ECAC9067EC3CC0AEE7"  -X POST -d '{"goodsSaleRecordIdList":[5,6],"sessionToken":"8973D0B8568C48ECAC9067EC3CC0AEE7"}' 'http://127.0.0.1:8118/hotel/good/addGoodsReturnRecord.do'
     *
     * @param
     * @return
     */
    @RequestMapping("addGoodsReturnRecord.do")
    @ResponseBody
    public ResponseData addGoodsReturnRecord(@RequestBody GoodsSaleRecordRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.addGoodsReturnRecord(request));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 出入库记录列表
     *
     * @param search
     * @return
     */
    @RequestMapping("findGoodsSaleRecord.do")
    @ResponseBody
    public ResponseData findGoodsSaleRecord(@RequestBody GoodsSaleRecordSearch search) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.findGoodsSaleRecord(search));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 查询酒店仓库站点列表
     *
     * @param request
     * @return
     */
    @RequestMapping("findHotelGoodsStockSite.do")
    @ResponseBody
    public ResponseData findHotelGoodsStockSite(@RequestBody HotelGoodsRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.findHotelGoodsStockSite(request));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 查询酒店商品列表
     * curl -i -H "Content-Type: application/json" -H "X-Token: 8973D0B8568C48ECAC9067EC3CC0AEE7"  -X POST -d '{"goodsStockId":9,"hotelIsCheckGoodsNum":1,"sessionToken":"8973D0B8568C48ECAC9067EC3CC0AEE7"}' 'http://127.0.0.1:8118/hotel/good/findHotelGoods.do'
     * curl -i -H "Content-Type: application/json" -H "X-Token: 8973D0B8568C48ECAC9067EC3CC0AEE7"  -X POST -d '{"hotelIsCheckGoodsNum":0,"sessionToken":"8973D0B8568C48ECAC9067EC3CC0AEE7"}' 'http://127.0.0.1:8118/hotel/good/findHotelGoods.do'
     *
     * @param request
     * @return
     */
    @RequestMapping("findHotelGoods.do")
    @ResponseBody
    public ResponseData findHotelGoods(@RequestBody HotelGoodsRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.findHotelGoods(request));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 处理商品销售归档数据
     * * curl -i -H "Content-Type: application/json" -H "X-Token: 92B634FD858D42C78FC6A89FD5534B8F"  -X POST -d '{"sessionToken":"92B634FD858D42C78FC6A89FD5534B8F"}' 'http://127.0.0.1:8118/hotel/good/dealGoodsSaleRecodeArchive.do'
     *
     * @param request
     * @return
     */
    @RequestMapping("dealGoodsSaleRecodeArchive.do")
    @ResponseBody
    public ResponseData dealGoodsSaleRecodeArchive(@RequestBody HotelGoodsRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.dealGoodsSaleRecodeArchive(request));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 绑定仓库和部门
     * curl -i -H "Content-Type: application/json" -H "X-Token: 8973D0B8568C48ECAC9067EC3CC0AEE7"  -X POST -d '{"goodsStockId":10,"goodsStockName":"大堂吧仓库","stockSiteName":"大堂","departmentList":[{"departmentId":22,"departmentName":"房务部"},{"departmentId":21,"departmentName":"前台组"}],"sessionToken":"8973D0B8568C48ECAC9067EC3CC0AEE7"}' 'http://127.0.0.1:8118/hotel/good/addDepartmentGoodsStock.do'
     *
     * @param
     * @return
     */
    @RequestMapping("addDepartmentGoodsStock.do")
    @ResponseBody
    public ResponseData addDepartmentGoodsStock(@RequestBody DepartmentGoodsStockRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.addDepartmentGoodsStock(request));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 2. 解除绑定仓库和部门
     * curl -i -H "Content-Type: application/json" -H "X-Token: 8973D0B8568C48ECAC9067EC3CC0AEE7"  -X POST -d '{"departmentGoodsStockIds":[1,2],"sessionToken":"8973D0B8568C48ECAC9067EC3CC0AEE7"}' 'http://127.0.0.1:8118/hotel/good/delDepartmentGoodsStock.do'
     *
     * @param
     * @return
     */
    @RequestMapping("delDepartmentGoodsStock.do")
    @ResponseBody
    public ResponseData delDepartmentGoodsStock(@RequestBody DepartmentGoodsStockRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.delDepartmentGoodsStock(request));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 查询 仓库和部门绑定列表
     * curl -i -H "Content-Type: application/json" -H "X-Token: 8973D0B8568C48ECAC9067EC3CC0AEE7"  -X POST -d '{"departmentId":21,"sessionToken":"8973D0B8568C48ECAC9067EC3CC0AEE7"}' 'http://127.0.0.1:8118/hotel/good/findDepartmentGoodsStock.do'
     *
     * @param search
     * @return
     */
    @RequestMapping("findDepartmentGoodsStock.do")
    @ResponseBody
    public ResponseData findDepartmentGoodsStock(@RequestBody DepartmentGoodsStockSearch search) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(goodsStockService.findDepartmentGoodsStock(search));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("addOrUpdateGoodsStockDetail.do")
    @ResponseBody
    public ResponseData addOrUpdateGoodsStockDetail(@RequestBody JSONObject param) {

        return goodsStockDetailService.addOrUpdateGoodsStockDetail(param);
    }

    @RequestMapping("findAllGoodsStockDetail.do")
    @ResponseBody
    public ResponseData findAllGoodsStockDetail(@RequestBody JSONObject param) {

        return goodsStockDetailService.findAllGoodsStockDetail(param);
    }


    @RequestMapping("addOrUpdateWarehousing.do")
    @ResponseBody
    public ResponseData addOrUpdateWarehousing(@RequestBody JSONObject param) {

        return goodsTranService.addOrUpdateWarehousing(param);
    }


    @RequestMapping("findAllGoodsStockSaleRecord.do")
    @ResponseBody
    public ResponseData findAllGoodsStockSaleRecord(@RequestBody JSONObject param) {

        return goodsTranService.findAllGoodsStockSaleRecord(param);
    }


    @RequestMapping("findAllGoodsSupplyAccount.do")
    @ResponseBody
    public ResponseData findAllGoodsSupplyAccount(@RequestBody JSONObject param) {

        return goodsSupplyAccountService.findAllGoodsSupplyAccount(param);
    }


    @RequestMapping("addOrUpdateGoodsStockTran.do")
    @ResponseBody
    public ResponseData addOrUpdateGoodsStockTran(@RequestBody JSONObject param) {

        return goodsStockTranService.addOrUpdateGoodsStockTran(param);
    }


    @RequestMapping("findAllGoodsStockTran.do")
    @ResponseBody
    public ResponseData findAllGoodsStockTran(@RequestBody JSONObject param) {

        return goodsStockTranService.findAllGoodsStockTran(param);
    }


    @RequestMapping("findAllGoodsInfoStock.do")
    @ResponseBody
    public ResponseData findAllGoodsInfoStock(@RequestBody JSONObject param) {

        return goodsStockService.findAllGoodsInfoStock(param);
    }

    @RequestMapping("findGoodsByWechat.do")
    @ResponseBody
    public ResponseData findGoodsByWechat(@RequestBody HotelGoodsRequest param) {

        return goodsStockService.findGoodsByWechat(param);
    }


}
