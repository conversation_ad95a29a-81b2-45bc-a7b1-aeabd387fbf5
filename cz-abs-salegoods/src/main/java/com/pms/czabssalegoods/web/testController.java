package com.pms.czabssalegoods.web;

import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.dao.account.AccountDao;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller("test/")
@ResponseBody
public class testController {

    @Autowired
    private AccountDao accountDao;

    @RequestMapping("teskkkk.do")
    @ResponseBody
    public void teskkkk(){

        List<Account> accounts = accountDao.selectBySearch(null);

        for (Account account:accounts){

            System.out.println(JSONObject.fromObject(account));

        }

    }

}
