package com.pms.czabssalegoods.web;


import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.pmswarehouse.bean.request.BindCouponInfoRequest;
import com.pms.pmswarehouse.bean.request.CouponUseRecodeRequest;
import com.pms.pmswarehouse.bean.request.IssuedCouponRequest;
import com.pms.pmswarehouse.bean.search.CouponInfoSearch;
import com.pms.pmswarehouse.bean.search.IssuedCouponRecordSearch;
import com.pms.pmswarehouse.service.impl.CouponServiceImpl;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@RequestMapping("/hotel/good/")
public class CouponController {

    @Resource
    private CouponServiceImpl couponService;


    /**
     * 发券
     *
     * @param request
     * @return
     */
    @RequestMapping("addIssuedCouponRecord.do")
    @ResponseBody
    public ResponseData addIssuedCouponRecord(@RequestBody IssuedCouponRequest request) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            couponService.addIssuedCouponRecord(request);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }



    /**
     * 查询发券记录
     *
     * @param search
     * @return
     */
    @RequestMapping("findIssuedCouponRecord.do")
    @ResponseBody
    public ResponseData findIssuedCouponRecord(@RequestBody IssuedCouponRecordSearch search) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            responseData.setData(couponService.findIssuedCouponRecord(search));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 查询优惠券
     *
     * @param search
     * @return
     */
    @RequestMapping("findCouponInfo.do")
    @ResponseBody
    public ResponseData findCouponInfo(@RequestBody CouponInfoSearch search) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            responseData.setData(couponService.findCouponInfo(search));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 优惠券绑定到人
     *
     * @param request
     * @return
     */
    @RequestMapping("bindCouponInfoForRegister.do")
    @ResponseBody
    public ResponseData bindCouponInfoForRegister(@RequestBody BindCouponInfoRequest request) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            couponService.bindCouponInfoForRegister(request);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 使用优惠券
     *
     * @param request
     * @return
     */
    @RequestMapping("addCouponUseRecode.do")
    @ResponseBody
    public ResponseData addCouponUseRecode(@RequestBody CouponUseRecodeRequest request) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            responseData.setData(couponService.addCouponUseRecode(request));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


}
