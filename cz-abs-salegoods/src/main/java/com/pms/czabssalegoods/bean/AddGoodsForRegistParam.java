package com.pms.czabssalegoods.bean;

import com.pms.czpmsutils.request.BaseRequest;

import java.util.List;

public class AddGoodsForRegistParam  extends BaseRequest  {
    private Integer registId;
    private Integer bookingOrderId;
    private Integer registPersonId;
    private Integer money;
    private String remark;
    private List<GoodInfo> goodInfoList;
    private String roomNum;
    private Integer roomNumId;
    private Integer roomTypeId;
    private String registPersonName;
    private Integer hotelIsCheckGoodsNum;
    private Integer goodsStockId;



    public Integer getRegistId() {
        return registId;
    }

    public void setRegistId(Integer registId) {
        this.registId = registId;
    }

    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }

    public List<GoodInfo> getGoodInfoList() {
        return goodInfoList;
    }

    public void setGoodInfoList(List<GoodInfo> goodInfoList) {
        this.goodInfoList = goodInfoList;
    }

    public Integer getRegistPersonId() {
        return registPersonId;
    }

    public void setRegistPersonId(Integer registPersonId) {
        this.registPersonId = registPersonId;
    }

    public Integer getMoney() {
        return money;
    }

    public void setMoney(Integer money) {
        this.money = money;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public Integer getRoomNumId() {
        return roomNumId;
    }

    public void setRoomNumId(Integer roomNumId) {
        this.roomNumId = roomNumId;
    }

    public Integer getRoomTypeId() {
        return roomTypeId;
    }

    public void setRoomTypeId(Integer roomTypeId) {
        this.roomTypeId = roomTypeId;
    }

    public String getRegistPersonName() {
        return registPersonName;
    }

    public void setRegistPersonName(String registPersonName) {
        this.registPersonName = registPersonName;
    }

    public Integer getHotelIsCheckGoodsNum() {
        return hotelIsCheckGoodsNum;
    }

    public void setHotelIsCheckGoodsNum(Integer hotelIsCheckGoodsNum) {
        this.hotelIsCheckGoodsNum = hotelIsCheckGoodsNum;
    }

    public Integer getGoodsStockId() {
        return goodsStockId;
    }

    public void setGoodsStockId(Integer goodsStockId) {
        this.goodsStockId = goodsStockId;
    }

    public static class GoodInfo{
        private String goodsInfoName;
        private Integer goodsInfoId;
        private Integer goodsClassId;
        private String goodsClassName;
        private Integer amount;
        private Integer goodsStockId;
        private String goodsStockName;
        //单价
        private Integer price;
        //总价
        private Integer money;
        //
        private String direction;

        private Integer type;

        private Integer state;

        public Integer getGoodsInfoId() {
            return goodsInfoId;
        }

        public void setGoodsInfoId(Integer goodsInfoId) {
            this.goodsInfoId = goodsInfoId;
        }

        public Integer getGoodsClassId() {
            return goodsClassId;
        }

        public void setGoodsClassId(Integer goodsClassId) {
            this.goodsClassId = goodsClassId;
        }

        public String getGoodsClassName() {
            return goodsClassName;
        }

        public void setGoodsClassName(String goodsClassName) {
            this.goodsClassName = goodsClassName;
        }

        public Integer getAmount() {
            return amount;
        }

        public void setAmount(Integer amount) {
            this.amount = amount;
        }

        public Integer getGoodsStockId() {
            return goodsStockId;
        }

        public void setGoodsStockId(Integer goodsStockId) {
            this.goodsStockId = goodsStockId;
        }

        public String getGoodsStockName() {
            return goodsStockName;
        }

        public void setGoodsStockName(String goodsStockName) {
            this.goodsStockName = goodsStockName;
        }

        public Integer getPrice() {
            return price;
        }

        public void setPrice(Integer price) {
            this.price = price;
        }

        public Integer getMoney() {
            return money;
        }

        public void setMoney(Integer money) {
            this.money = money;
        }

        public String getDirection() {
            return direction;
        }

        public void setDirection(String direction) {
            this.direction = direction;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public Integer getState() {
            return state;
        }

        public void setState(Integer state) {
            this.state = state;
        }

        public String getGoodsInfoName() {
            return goodsInfoName;
        }

        public void setGoodsInfoName(String goodsInfoName) {
            this.goodsInfoName = goodsInfoName;
        }
    }
}

