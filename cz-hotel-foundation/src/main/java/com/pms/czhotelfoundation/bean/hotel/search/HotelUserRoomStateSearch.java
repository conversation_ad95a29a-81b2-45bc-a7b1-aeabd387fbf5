package com.pms.czhotelfoundation.bean.hotel.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("HotelUserRoomStateSearch")
public class HotelUserRoomStateSearch extends BaseSearch {
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer userId;
	private String vcColor;
	private String vdColor;
	private String ocColor;
	private String odColor;
	private String ooColor;
	private String osColor;
	private Integer roomStateWidth;
	private Integer roomStateHeight;
	private Integer roomNoFontSize;
	private Integer roomTypeFontSize;
	private Integer guestInfoFontSize;
	private java.util.Date createTime;
	private Integer createUserId;
	private java.util.Date updateTime;
	private Integer updateUserId;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setUserId(Integer value) {
		this.userId = value;
	}
	
	public Integer getUserId() {
		return this.userId;
	}
	public void setVcColor(String value) {
		this.vcColor = value;
	}
	
	public String getVcColor() {
		return this.vcColor;
	}
	public void setVdColor(String value) {
		this.vdColor = value;
	}
	
	public String getVdColor() {
		return this.vdColor;
	}
	public void setOcColor(String value) {
		this.ocColor = value;
	}
	
	public String getOcColor() {
		return this.ocColor;
	}
	public void setOdColor(String value) {
		this.odColor = value;
	}
	
	public String getOdColor() {
		return this.odColor;
	}
	public void setOoColor(String value) {
		this.ooColor = value;
	}
	
	public String getOoColor() {
		return this.ooColor;
	}
	public void setOsColor(String value) {
		this.osColor = value;
	}
	
	public String getOsColor() {
		return this.osColor;
	}
	public void setRoomStateWidth(Integer value) {
		this.roomStateWidth = value;
	}
	
	public Integer getRoomStateWidth() {
		return this.roomStateWidth;
	}
	public void setRoomStateHeight(Integer value) {
		this.roomStateHeight = value;
	}
	
	public Integer getRoomStateHeight() {
		return this.roomStateHeight;
	}
	public void setRoomNoFontSize(Integer value) {
		this.roomNoFontSize = value;
	}
	
	public Integer getRoomNoFontSize() {
		return this.roomNoFontSize;
	}
	public void setRoomTypeFontSize(Integer value) {
		this.roomTypeFontSize = value;
	}
	
	public Integer getRoomTypeFontSize() {
		return this.roomTypeFontSize;
	}
	public void setGuestInfoFontSize(Integer value) {
		this.guestInfoFontSize = value;
	}
	
	public Integer getGuestInfoFontSize() {
		return this.guestInfoFontSize;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer value) {
		this.createUserId = value;
	}
	
	public Integer getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer value) {
		this.updateUserId = value;
	}
	
	public Integer getUpdateUserId() {
		return this.updateUserId;
	}

}

