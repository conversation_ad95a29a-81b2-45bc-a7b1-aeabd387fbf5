package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;

/**
 * 
 */
public class HotelOrgRoomType implements Serializable{
	//
	private Integer id;
	//房型ID
	private Integer roomTypeId;
	//排序
	private Integer sort;
	//
	private Integer hid;
	//
	private Integer hotelGroupId;
	//
	private Integer hotelOrgPriceId;

	public HotelOrgRoomType(){
	}

	public HotelOrgRoomType(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	
	public Integer getSort() {
		return this.sort;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setHotelOrgPriceId(Integer hotelOrgPriceId) {
		this.hotelOrgPriceId = hotelOrgPriceId;
	}
	
	public Integer getHotelOrgPriceId() {
		return this.hotelOrgPriceId;
	}

}

