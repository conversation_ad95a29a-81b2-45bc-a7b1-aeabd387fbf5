package com.pms.czhotelfoundation.bean.otarep.search;
import com.pms.czpmsutils.request.PageBaseRequest;

import java.util.Date;

public class RepRoomTypeSearch extends PageBaseRequest{
	private Integer id;
	private String name;
	private String code;
	private Integer repHid;
	private String otaRtid;
	private String otaMyrtid;
	private Integer state;
	private java.util.Date createTime;
	private String url;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Integer getRepHid() {
		return repHid;
	}

	public void setRepHid(Integer repHid) {
		this.repHid = repHid;
	}

	public String getOtaRtid() {
		return otaRtid;
	}

	public void setOtaRtid(String otaRtid) {
		this.otaRtid = otaRtid;
	}

	public String getOtaMyrtid() {
		return otaMyrtid;
	}

	public void setOtaMyrtid(String otaMyrtid) {
		this.otaMyrtid = otaMyrtid;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}
}

