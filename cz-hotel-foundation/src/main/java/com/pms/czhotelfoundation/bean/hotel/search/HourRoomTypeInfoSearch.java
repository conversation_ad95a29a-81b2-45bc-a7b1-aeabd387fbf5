package com.pms.czhotelfoundation.bean.hotel.search;

import org.apache.ibatis.type.Alias;

@<PERSON><PERSON>("HourRoomTypeInfoSearch")
public class HourRoomTypeInfoSearch {
	private Integer hourRoomTypeInfoId;
	private Integer hourRoomId;
	private Integer roomTypeId;
	private String roomTypeName;

	private Integer hid  ;
	private Integer hotelGroupId  ;


	public void setHourRoomId(Integer value) {
		this.hourRoomId = value;
	}

	public Integer getHourRoomId() {
		return this.hourRoomId;
	}
	public void setRoomTypeId(Integer value) {
		this.roomTypeId = value;
	}

	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setRoomTypeName(String value) {
		this.roomTypeName = value;
	}

	public String getRoomTypeName() {
		return this.roomTypeName;
	}

	public Integer getHourRoomTypeInfoId() {
		return hourRoomTypeInfoId;
	}

	public void setHourRoomTypeInfoId(Integer hourRoomTypeInfoId) {
		this.hourRoomTypeInfoId = hourRoomTypeInfoId;
	}

	public Integer getHid() {
		return hid;
	}

	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHotelGroupId() {
		return hotelGroupId;
	}

	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
}

