package com.pms.czhotelfoundation.bean.ota;

import java.util.Date;
import java.util.List;

public class OtaOrderRoomUpdateInfo {
    //订单Id
    private Date checkInTime;
    private Date checkOutTime;
    private Integer roomTypeId;
    private Integer bookingOrderId;
    private List<Integer> delBookOrderRoomNumIdList;
    private List<BookingOrderRoomNumUpdateInfo> bookingOrderRoomNumUpdateInfos;
    private String sessionToken;
    //预定房间修改信息
    public static class BookingOrderRoomNumUpdateInfo{
        //修改的订单开始时间
        private String startTime;
        //修改的订单结束时间
        private String endTime;
        //需要修改时间的BookingOrderRoomNumId
        private List<Integer> ids;
        public BookingOrderRoomNumUpdateInfo() {
        }
        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }

        public List<Integer> getIds() {
            return ids;
        }

        public void setIds(List<Integer> ids) {
            this.ids = ids;
        }
    }

    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }

    public List<BookingOrderRoomNumUpdateInfo> getBookingOrderRoomNumUpdateInfos() {
        return bookingOrderRoomNumUpdateInfos;
    }
    public void setBookingOrderRoomNumUpdateInfos(List<BookingOrderRoomNumUpdateInfo> bookingOrderRoomNumUpdateInfos) {
        this.bookingOrderRoomNumUpdateInfos = bookingOrderRoomNumUpdateInfos;
    }

    public List<Integer> getDelBookOrderRoomNumIdList() {
        return delBookOrderRoomNumIdList;
    }

    public void setDelBookOrderRoomNumIdList(List<Integer> delBookOrderRoomNumIdList) {
        this.delBookOrderRoomNumIdList = delBookOrderRoomNumIdList;
    }

    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }

    public Date getCheckInTime() {
        return checkInTime;
    }

    public void setCheckInTime(Date checkInTime) {
        this.checkInTime = checkInTime;
    }

    public Date getCheckOutTime() {
        return checkOutTime;
    }

    public void setCheckOutTime(Date checkOutTime) {
        this.checkOutTime = checkOutTime;
    }

    public Integer getRoomTypeId() {
        return roomTypeId;
    }

    public void setRoomTypeId(Integer roomTypeId) {
        this.roomTypeId = roomTypeId;
    }
}
