package com.pms.czhotelfoundation.bean.request;

import com.pms.czpmsutils.CosFileUtil;
import com.pms.czpmsutils.request.BaseRequest;
import org.springframework.web.multipart.MultipartFile;

public class FileRequest extends BaseRequest {

    private MultipartFile multipartFile;
    private Integer fileInfoId;
    private CosFileUtil.UploadObjectRsp uploadObjectRsp;
    private Integer bid;
    private boolean isMutil;
    private String btype;
    //
    private Integer hotelGroupId;
    //
    private Integer roomTypeId;

    private Integer type;

    public String getBtype() {
        return btype;
    }

    public void setBtype(String btype) {
        this.btype = btype;
    }

    public Integer getBid() {
        return bid;
    }

    public void setBid(Integer bid) {
        this.bid = bid;
    }

    public boolean isMutil() {
        return isMutil;
    }

    public void setMutil(boolean mutil) {
        isMutil = mutil;
    }

    public CosFileUtil.UploadObjectRsp getUploadObjectRsp() {
        return uploadObjectRsp;
    }

    public void setUploadObjectRsp(CosFileUtil.UploadObjectRsp uploadObjectRsp) {
        this.uploadObjectRsp = uploadObjectRsp;
    }

    public MultipartFile getMultipartFile() {
        return multipartFile;
    }

    public void setMultipartFile(MultipartFile multipartFile) {
        this.multipartFile = multipartFile;
    }

    public Integer getFileInfoId() {
        return fileInfoId;
    }

    public void setFileInfoId(Integer fileInfoId) {
        this.fileInfoId = fileInfoId;
    }

    public Integer getHotelGroupId() {
        return hotelGroupId;
    }

    public void setHotelGroupId(Integer hotelGroupId) {
        this.hotelGroupId = hotelGroupId;
    }

    public Integer getRoomTypeId() {
        return roomTypeId;
    }

    public void setRoomTypeId(Integer roomTypeId) {
        this.roomTypeId = roomTypeId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
