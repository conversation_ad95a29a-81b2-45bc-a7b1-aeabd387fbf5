package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;

/**
 * 
 */
public class HotelOrgRateCode implements Serializable{
	//
	private Integer id;
	//房价码ID
	private Integer rateCodeId;
	//排序
	private Integer sort;
	//
	private Integer hotelOrgPriceId;
	//
	private Integer hid;
	//
	private Integer hotelGroupId;

	public HotelOrgRateCode(){
	}

	public HotelOrgRateCode(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setRateCodeId(Integer rateCodeId) {
		this.rateCodeId = rateCodeId;
	}
	
	public Integer getRateCodeId() {
		return this.rateCodeId;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	
	public Integer getSort() {
		return this.sort;
	}
	public void setHotelOrgPriceId(Integer hotelOrgPriceId) {
		this.hotelOrgPriceId = hotelOrgPriceId;
	}
	
	public Integer getHotelOrgPriceId() {
		return this.hotelOrgPriceId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

}

