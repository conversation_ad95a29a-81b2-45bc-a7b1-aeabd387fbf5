package com.pms.czhotelfoundation.bean.room;

import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;
import java.util.Date;

/**
 *  做卡记录
 * <AUTHOR>
 */
public class RoomRepairRecord extends BaseBean implements Serializable {
	private Integer repairCheckRoomRecordId  ;
	private Integer roomId  ;
	private String roomNum  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer registId  ;
	private Integer type  ;
	private String des  ;
	private Integer businessDay  ;
	//房型
	private Integer roomTypeId;
	private Date begintime = new Date() ;
	private Date endtime  = new Date() ;
	private Integer classId  ;
	private Integer state  ;
	private Integer oldRoomState;
	private Integer newRoomState;

	private Integer cleaningUserId  ;
	private String cleaningUserName  ;

	public RoomRepairRecord(){
	}

	public RoomRepairRecord(Integer repairCheckRoomRecordId){
		this.repairCheckRoomRecordId = repairCheckRoomRecordId;
	}

	public void setRepairCheckRoomRecordId(Integer repairCheckRoomRecordId) {
		this.repairCheckRoomRecordId = repairCheckRoomRecordId;
	}
	
	public Integer getRepairCheckRoomRecordId() {
		return this.repairCheckRoomRecordId;
	}
	public void setRoomId(Integer roomId) {
		this.roomId = roomId;
	}
	
	public Integer getRoomId() {
		return this.roomId;
	}
	public void setRoomNum(String roomNum) {
		this.roomNum = roomNum;
	}
	
	public String getRoomNum() {
		return this.roomNum;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRegistId(Integer registId) {
		this.registId = registId;
	}
	
	public Integer getRegistId() {
		return this.registId;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	
	public Integer getType() {
		return this.type;
	}
	public void setDes(String des) {
		this.des = des;
	}
	
	public String getDes() {
		return this.des;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public void setBegintime(Date begintime) {
		this.begintime = begintime;
	}
	
	public Date getBegintime() {
		return this.begintime;
	}

	public void setEndtime(Date endtime) {
		this.endtime = endtime;
	}
	
	public Date getEndtime() {
		return this.endtime;
	}
	public void setClassId(Integer classId) {
		this.classId = classId;
	}
	
	public Integer getClassId() {
		return this.classId;
	}

	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}

	public void setCleaningUserId(Integer cleaningUserId){this.cleaningUserId = cleaningUserId;}
	public Integer getCleaningUserId(){return this.cleaningUserId;}

	public void setCleaningUserName(String cleaningUserName){this.cleaningUserName = cleaningUserName;}
	public String getCleaningUserName(){return this.cleaningUserName;}

	public Integer getOldRoomState() {
		return oldRoomState;
	}

	public void setOldRoomState(Integer oldRoomState) {
		this.oldRoomState = oldRoomState;
	}

	public Integer getNewRoomState() {
		return newRoomState;
	}

	public void setNewRoomState(Integer newRoomState) {
		this.newRoomState = newRoomState;
	}

	public Integer getRoomTypeId() {
		return roomTypeId;
	}

	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
}

