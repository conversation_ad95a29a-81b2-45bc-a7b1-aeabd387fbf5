package com.pms.czhotelfoundation.bean.chess;

public class GetHotelHourRoomTypePriceResponse {
    private Integer id  ;
    private Integer roomTypeId  ;
    private String roomTypeName  ;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getRoomTypeId() {
        return roomTypeId;
    }

    public void setRoomTypeId(Integer roomTypeId) {
        this.roomTypeId = roomTypeId;
    }

    public String getRoomTypeName() {
        return roomTypeName;
    }

    public void setRoomTypeName(String roomTypeName) {
        this.roomTypeName = roomTypeName;
    }

    public Integer getHourRoomInfoId() {
        return hourRoomInfoId;
    }

    public void setHourRoomInfoId(Integer hourRoomInfoId) {
        this.hourRoomInfoId = hourRoomInfoId;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public Integer getHourPrice() {
        return hourPrice;
    }

    public void setHourPrice(Integer hourPrice) {
        this.hourPrice = hourPrice;
    }

    public Integer getHourLenght() {
        return hourLenght;
    }

    public void setHourLenght(Integer hourLenght) {
        this.hourLenght = hourLenght;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(Integer maxPrice) {
        this.maxPrice = maxPrice;
    }

    public Integer getToDayRoom() {
        return toDayRoom;
    }

    public void setToDayRoom(Integer toDayRoom) {
        this.toDayRoom = toDayRoom;
    }

    public Integer getBreakfastId() {
        return breakfastId;
    }

    public void setBreakfastId(Integer breakfastId) {
        this.breakfastId = breakfastId;
    }

    public String getReamark() {
        return reamark;
    }

    public void setReamark(String reamark) {
        this.reamark = reamark;
    }

    public Integer getHourRoomCode() {
        return hourRoomCode;
    }

    public void setHourRoomCode(Integer hourRoomCode) {
        this.hourRoomCode = hourRoomCode;
    }

    private Integer hourRoomInfoId  ;
    private Integer price  ;
    private Integer hourPrice  ;
    private Integer hourLenght  ;
    private Integer sort  ;
    private Integer state  ;
    private Integer maxPrice  ;
    private Integer toDayRoom  ;
    private Integer breakfastId  ;
    private String reamark  ;
    private Integer hourRoomCode  ;
}
