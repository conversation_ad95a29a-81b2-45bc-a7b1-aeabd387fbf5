package com.pms.czhotelfoundation.bean.ota;


import java.util.List;

public class OtaBookRoomTypeInfo {
    // 房型id
    private int roomTypeId;
    // 房型名称
    private String roomTypeName;
    // 房间id
    private int rateId;
    // 房价代码
    private String rateCode;
    // 房间数
    private int num;
    // 价格信息
    private List<OtaOrderDailyPrice> priceList;

    public int getRoomTypeId() {
        return roomTypeId;
    }

    public void setRoomTypeId(int roomTypeId) {
        this.roomTypeId = roomTypeId;
    }

    public String getRoomTypeName() {
        return roomTypeName;
    }

    public void setRoomTypeName(String roomTypeName) {
        this.roomTypeName = roomTypeName;
    }

    public int getRateId() {
        return rateId;
    }

    public void setRateId(int rateId) {
        this.rateId = rateId;
    }

    public String getRateCode() {
        return rateCode;
    }

    public void setRateCode(String rateCode) {
        this.rateCode = rateCode;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public List<OtaOrderDailyPrice> getPriceList() {
        return priceList;
    }

    public void setPriceList(List<OtaOrderDailyPrice> priceList) {
        this.priceList = priceList;
    }
}
