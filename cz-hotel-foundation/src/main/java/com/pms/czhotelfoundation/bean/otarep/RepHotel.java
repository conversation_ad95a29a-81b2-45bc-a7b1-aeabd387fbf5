package com.pms.czhotelfoundation.bean.otarep;

import java.io.Serializable;
import java.util.Date;

public class RepHotel implements Serializable{
	//
	private Integer id;
	//
	private String hotelName;
	//
	private String hotelCode;
	//平台中对应的酒店hid
	private String otaHid;
	//1.携程 2.美团 3.飞猪 4.去哪儿
	private Integer otaType;
	//1.启用 0.停用 2.售房完成
	private Integer state;
	//总房数
	private Integer sumRoom;
	//已售房数
	private Integer saleRoom;
	//已售金额
	private Integer saleMoney;
	//创建时间
	private Date createTime;

	public RepHotel(){
	}

	public RepHotel(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHotelName(String hotelName) {
		this.hotelName = hotelName;
	}
	
	public String getHotelName() {
		return this.hotelName;
	}
	public void setHotelCode(String hotelCode) {
		this.hotelCode = hotelCode;
	}
	
	public String getHotelCode() {
		return this.hotelCode;
	}
	public void setOtaHid(String otaHid) {
		this.otaHid = otaHid;
	}
	
	public String getOtaHid() {
		return this.otaHid;
	}
	public void setOtaType(Integer otaType) {
		this.otaType = otaType;
	}
	
	public Integer getOtaType() {
		return this.otaType;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setSumRoom(Integer sumRoom) {
		this.sumRoom = sumRoom;
	}
	
	public Integer getSumRoom() {
		return this.sumRoom;
	}
	public void setSaleRoom(Integer saleRoom) {
		this.saleRoom = saleRoom;
	}
	
	public Integer getSaleRoom() {
		return this.saleRoom;
	}
	public void setSaleMoney(Integer saleMoney) {
		this.saleMoney = saleMoney;
	}
	
	public Integer getSaleMoney() {
		return this.saleMoney;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getCreateTime() {
		return this.createTime;
	}

}

