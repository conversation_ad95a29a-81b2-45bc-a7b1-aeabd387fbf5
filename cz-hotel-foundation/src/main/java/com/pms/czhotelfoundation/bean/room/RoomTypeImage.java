package com.pms.czhotelfoundation.bean.room;

import java.io.Serializable;

/**
 * 房型图片表
 * <AUTHOR>
 */
public class RoomTypeImage implements Serializable {
	//主键
	private Integer imageId;
	//酒店id
	private Integer hid;
	//房型id
	private Integer roomTypeId;
	//图片MD5
	private String images;
	//状态 0停用
	private Integer state;
	//
	private Integer fileId;
	//
	private String url;

	public RoomTypeImage(){
	}

	public RoomTypeImage(Integer imageId){
		this.imageId = imageId;
	}

	public void setImageId(Integer imageId) {
		this.imageId = imageId;
	}
	
	public Integer getImageId() {
		return this.imageId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setImages(String images) {
		this.images = images;
	}
	
	public String getImages() {
		return this.images;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}

	public Integer getFileId() {
		return fileId;
	}

	public void setFileId(Integer fileId) {
		this.fileId = fileId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}
}

