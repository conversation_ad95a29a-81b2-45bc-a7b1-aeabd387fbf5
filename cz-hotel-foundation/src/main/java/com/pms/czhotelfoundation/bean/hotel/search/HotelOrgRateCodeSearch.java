package com.pms.czhotelfoundation.bean.hotel.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class HotelOrgRateCodeSearch extends PageBaseRequest{
	private Integer id;
	private Integer rateCodeId;
	private Integer sort;
	private Integer hotelOrgPriceId;
	private Integer hid;
	private Integer hotelGroupId;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setRateCodeId(Integer value) {
		this.rateCodeId = value;
	}
	
	public Integer getRateCodeId() {
		return this.rateCodeId;
	}
	public void setSort(Integer value) {
		this.sort = value;
	}
	
	public Integer getSort() {
		return this.sort;
	}
	public void setHotelOrgPriceId(Integer value) {
		this.hotelOrgPriceId = value;
	}
	
	public Integer getHotelOrgPriceId() {
		return this.hotelOrgPriceId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

}

