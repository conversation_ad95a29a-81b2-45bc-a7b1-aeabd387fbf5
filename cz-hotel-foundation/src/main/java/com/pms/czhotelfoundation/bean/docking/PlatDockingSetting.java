package com.pms.czhotelfoundation.bean.docking;

public class PlatDockingSetting {
    private Integer id;

    private Integer groupId;

    private Integer hid;

    private String platType;

    private String appKey;

    private String appSecret;

    private String contactUser;

    private String contactPhone;

    private Integer accessType;

    private String platUrl;

    private String otherConf;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Integer getHid() {
        return hid;
    }

    public void setHid(Integer hid) {
        this.hid = hid;
    }

    public String getPlatType() {
        return platType;
    }

    public void setPlatType(String platType) {
        this.platType = platType;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getContactUser() {
        return contactUser;
    }

    public void setContactUser(String contactUser) {
        this.contactUser = contactUser;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public Integer getAccessType() {
        return accessType;
    }

    public void setAccessType(Integer accessType) {
        this.accessType = accessType;
    }

    public String getPlatUrl() {
        return platUrl;
    }

    public void setPlatUrl(String platUrl) {
        this.platUrl = platUrl;
    }

    public String getOtherConf() {
        return otherConf;
    }

    public void setOtherConf(String otherConf) {
        this.otherConf = otherConf == null ? null : otherConf.trim();
    }
}