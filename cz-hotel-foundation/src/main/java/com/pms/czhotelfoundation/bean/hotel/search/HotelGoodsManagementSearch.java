package com.pms.czhotelfoundation.bean.hotel.search;

import com.pms.czpmsutils.request.PageBaseRequest;
import org.apache.ibatis.type.Alias;

@Alias("HotelGoodsManagementSearch")
public class HotelGoodsManagementSearch extends PageBaseRequest {
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private String code;
	private String name;
	private Integer state;
	private String remark;
	private Integer accountId;
	private Integer money;
	private String guestName;
	private Integer guestId;
	private Integer registId;
	private Integer bookingOrderId;
	private String roomNum;
	private Integer roomInfoId;
	private Integer type;
	private java.util.Date createTime;
	private Integer createUserId;
	private String createUserName;
	private java.util.Date updateTime;
	private Integer updateUserId;
	private String updateUserName;
	private Integer classId;
	private Integer accountYear;
	private Integer accountYearMonth;
	private Integer businessDay;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setCode(String value) {
		this.code = value;
	}
	
	public String getCode() {
		return this.code;
	}
	public void setName(String value) {
		this.name = value;
	}
	
	public String getName() {
		return this.name;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setRemark(String value) {
		this.remark = value;
	}
	
	public String getRemark() {
		return this.remark;
	}
	public void setAccountId(Integer value) {
		this.accountId = value;
	}
	
	public Integer getAccountId() {
		return this.accountId;
	}
	public void setMoney(Integer value) {
		this.money = value;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setGuestName(String value) {
		this.guestName = value;
	}
	
	public String getGuestName() {
		return this.guestName;
	}
	public void setGuestId(Integer value) {
		this.guestId = value;
	}
	
	public Integer getGuestId() {
		return this.guestId;
	}
	public void setRegistId(Integer value) {
		this.registId = value;
	}
	
	public Integer getRegistId() {
		return this.registId;
	}
	public void setBookingOrderId(Integer value) {
		this.bookingOrderId = value;
	}
	
	public Integer getBookingOrderId() {
		return this.bookingOrderId;
	}
	public void setRoomNum(String value) {
		this.roomNum = value;
	}
	
	public String getRoomNum() {
		return this.roomNum;
	}
	public void setRoomInfoId(Integer value) {
		this.roomInfoId = value;
	}
	
	public Integer getRoomInfoId() {
		return this.roomInfoId;
	}
	public void setType(Integer value) {
		this.type = value;
	}
	
	public Integer getType() {
		return this.type;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer value) {
		this.createUserId = value;
	}
	
	public Integer getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer value) {
		this.updateUserId = value;
	}
	
	public Integer getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}
	public void setClassId(Integer value) {
		this.classId = value;
	}
	
	public Integer getClassId() {
		return this.classId;
	}
	public void setAccountYear(Integer value) {
		this.accountYear = value;
	}
	
	public Integer getAccountYear() {
		return this.accountYear;
	}
	public void setAccountYearMonth(Integer value) {
		this.accountYearMonth = value;
	}
	
	public Integer getAccountYearMonth() {
		return this.accountYearMonth;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

}

