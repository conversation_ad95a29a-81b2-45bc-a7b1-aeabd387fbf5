package com.pms.czhotelfoundation.bean.jjb.search;

import com.pms.czpmsutils.request.PageBaseRequest;
import org.apache.ibatis.type.Alias;

import java.util.List;

@Alias("HotelChangeShiftsSearch")
public class HotelChangeShiftsSearch extends PageBaseRequest {
	private Integer id;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer cashMoney;
	private Integer wechatMoney;
	private Integer alipayMoney;
	private Integer cardMoney;
	private Integer reserveMoney;
	private Integer lastReserveMoney;
	private Integer realReserveMoney;
	private Integer state;
	private Integer classType  ;
	private Integer classId;
	private Integer businessDay;
	//班次
	private Integer handoverClassId;
	//营业日
	private Integer handoverBusinessDay;
	private java.util.Date createTime;
	private String createUserId;
	private String createUserName;
	private java.util.Date updateTime;
	private String updateUserId;
	private String updateUserName;

	private List<Long> businessDays;

	// 上次交班数据
	private String lastData;

	public void setId(Integer value) {
		this.id = value;
	}

	public Integer getId() {
		return this.id;
	}
	public void setCashMoney(Integer value) {
		this.cashMoney = value;
	}

	public Integer getCashMoney() {
		return this.cashMoney;
	}
	public void setWechatMoney(Integer value) {
		this.wechatMoney = value;
	}

	public Integer getWechatMoney() {
		return this.wechatMoney;
	}
	public void setAlipayMoney(Integer value) {
		this.alipayMoney = value;
	}

	public Integer getAlipayMoney() {
		return this.alipayMoney;
	}
	public void setCardMoney(Integer value) {
		this.cardMoney = value;
	}

	public Integer getCardMoney() {
		return this.cardMoney;
	}
	public void setReserveMoney(Integer value) {
		this.reserveMoney = value;
	}

	public Integer getReserveMoney() {
		return this.reserveMoney;
	}
	public void setLastReserveMoney(Integer value) {
		this.lastReserveMoney = value;
	}

	public Integer getLastReserveMoney() {
		return this.lastReserveMoney;
	}
	public void setRealReserveMoney(Integer value) {
		this.realReserveMoney = value;
	}

	public Integer getRealReserveMoney() {
		return this.realReserveMoney;
	}
	public void setState(Integer value) {
		this.state = value;
	}

	public Integer getState() {
		return this.state;
	}
	public void setClassId(Integer value) {
		this.classId = value;
	}

	public Integer getClassId() {
		return this.classId;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}

	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}

	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}

	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}

	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}

	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}

	public String getUpdateUserName() {
		return this.updateUserName;
	}

	public Integer getHid() {
		return hid;
	}

	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHotelGroupId() {
		return hotelGroupId;
	}

	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public String getLastData() {
		return lastData;
	}

	public void setLastData(String lastData) {
		this.lastData = lastData;
	}

	public Integer getClassType() {
		return classType;
	}

	public void setClassType(Integer classType) {
		this.classType = classType;
	}

	public List<Long> getBusinessDays() {
		return businessDays;
	}

	public void setBusinessDays(List<Long> businessDays) {
		this.businessDays = businessDays;
	}

	public Integer getHandoverClassId() {
		return handoverClassId;
	}

	public void setHandoverClassId(Integer handoverClassId) {
		this.handoverClassId = handoverClassId;
	}

	public Integer getHandoverBusinessDay() {
		return handoverBusinessDay;
	}

	public void setHandoverBusinessDay(Integer handoverBusinessDay) {
		this.handoverBusinessDay = handoverBusinessDay;
	}
}

