package com.pms.czhotelfoundation.bean.room.search;

import com.pms.czpmsutils.request.PageBaseRequest;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("RoomCardRecordSearch")
public class RoomCardRecordSearch extends PageBaseRequest {
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomInfoId;
	private String roomNo;
	private String lockCode;
	private String mode;
	private String startTime;
	private String endTime;
	private String registId;
	private String userName;
	private String userId;
	private String guestName;
	private String roomType;
	private String isGroup;
	private Integer businessDay;
	private String makeStatus;
	private String source;
	private String guestNo;
	private String msg  ;
	private Date createTime  ;

	private String registPersonName;

	public String getRegistPersonName() {
		return registPersonName;
	}

	public void setRegistPersonName(String registPersonName) {
		this.registPersonName = registPersonName;
	}

	public Integer getRegistPersonId() {
		return registPersonId;
	}

	public void setRegistPersonId(Integer registPersonId) {
		this.registPersonId = registPersonId;
	}

	private Integer registPersonId;

	// 开始操作实际
	private Date startCreateTime  ;

	// 结束操作时间
	private Date endCreateTime  ;


	public void setId(Integer value) {
		this.id = value;
	}

	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomInfoId(Integer value) {
		this.roomInfoId = value;
	}

	public Integer getRoomInfoId() {
		return this.roomInfoId;
	}
	public void setRoomNo(String value) {
		this.roomNo = value;
	}

	public String getRoomNo() {
		return this.roomNo;
	}
	public void setLockCode(String value) {
		this.lockCode = value;
	}

	public String getLockCode() {
		return this.lockCode;
	}
	public void setMode(String value) {
		this.mode = value;
	}

	public String getMode() {
		return this.mode;
	}
	public void setStartTime(String value) {
		this.startTime = value;
	}

	public String getStartTime() {
		return this.startTime;
	}
	public void setEndTime(String value) {
		this.endTime = value;
	}

	public String getEndTime() {
		return this.endTime;
	}
	public void setRegistId(String value) {
		this.registId = value;
	}

	public String getRegistId() {
		return this.registId;
	}
	public void setUserName(String value) {
		this.userName = value;
	}

	public String getUserName() {
		return this.userName;
	}
	public void setUserId(String value) {
		this.userId = value;
	}

	public String getUserId() {
		return this.userId;
	}
	public void setGuestName(String value) {
		this.guestName = value;
	}

	public String getGuestName() {
		return this.guestName;
	}
	public void setRoomType(String value) {
		this.roomType = value;
	}

	public String getRoomType() {
		return this.roomType;
	}
	public void setIsGroup(String value) {
		this.isGroup = value;
	}

	public String getIsGroup() {
		return this.isGroup;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setMakeStatus(String value) {
		this.makeStatus = value;
	}

	public String getMakeStatus() {
		return this.makeStatus;
	}
	public void setSource(String value) {
		this.source = value;
	}

	public String getSource() {
		return this.source;
	}
	public void setGuestNo(String value) {
		this.guestNo = value;
	}

	public String getGuestNo() {
		return this.guestNo;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getStartCreateTime() {
		return startCreateTime;
	}

	public void setStartCreateTime(Date startCreateTime) {
		this.startCreateTime = startCreateTime;
	}

	public Date getEndCreateTime() {
		return endCreateTime;
	}

	public void setEndCreateTime(Date endCreateTime) {
		this.endCreateTime = endCreateTime;
	}
}

