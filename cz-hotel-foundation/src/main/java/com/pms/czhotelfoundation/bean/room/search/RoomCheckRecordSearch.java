package com.pms.czhotelfoundation.bean.room.search;

import com.pms.czpmsutils.request.PageBaseRequest;
import lombok.Data;
import org.apache.ibatis.type.Alias;

@Alias("RoomCheckRecordSearch")
@Data
public class RoomCheckRecordSearch extends PageBaseRequest {
	private Integer id;
	private Integer bookingId;
	private Integer registId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomInfoId;
	private String roomNum;
	private Integer roomTypeId;
	private Integer state;
	private java.util.Date createTime;
	private String createUserId;
	private String createUserName;
	private java.util.Date updateTime;
	private String updateUserId;
	private String updateUserName;
	private String noticePersonName;
	private Integer noticePersonId;
	private Integer isCancel;
	private java.util.Date overCheckTime;
	private String overCheckPersonName;
	private String remark;
	private Integer overCheckPersonId;
	private Integer businessDay;
	private Integer checkRoomYear;
	private Integer checkRoomYearMonth;
	private Integer isOverTime;
	private Integer classId;
	private Integer fromType;
	private String teamCodeName;
	private Integer teamCodeId;

	private String startDate;
	private String endDate;
}

