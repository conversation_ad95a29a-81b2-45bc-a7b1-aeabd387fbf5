package com.pms.czhotelfoundation.bean.jjb.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("HotelChangeShiftsSalesSearch")
public class HotelChangeShiftsSalesSearch extends BaseSearch {
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomMoney;
	private Integer foodMoney;
	private Integer goodMoney;
	private Integer hotelChangeShiftsId;
	private Integer state;
	private Integer classId;
	private Integer businessDay;
	private java.util.Date createTime;
	private String createUserId;
	private String createUserName;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomMoney(Integer value) {
		this.roomMoney = value;
	}
	
	public Integer getRoomMoney() {
		return this.roomMoney;
	}
	public void setFoodMoney(Integer value) {
		this.foodMoney = value;
	}
	
	public Integer getFoodMoney() {
		return this.foodMoney;
	}
	public void setGoodMoney(Integer value) {
		this.goodMoney = value;
	}
	
	public Integer getGoodMoney() {
		return this.goodMoney;
	}
	public void setHotelChangeShiftsId(Integer value) {
		this.hotelChangeShiftsId = value;
	}
	
	public Integer getHotelChangeShiftsId() {
		return this.hotelChangeShiftsId;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setClassId(Integer value) {
		this.classId = value;
	}
	
	public Integer getClassId() {
		return this.classId;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

}

