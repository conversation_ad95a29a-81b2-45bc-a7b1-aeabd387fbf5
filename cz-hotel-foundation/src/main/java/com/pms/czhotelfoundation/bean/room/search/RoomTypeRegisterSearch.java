package com.pms.czhotelfoundation.bean.room.search;

import com.pms.czpmsutils.request.BaseRequest;
import lombok.Data;
import org.apache.ibatis.type.Alias;

@Alias("RoomTypeRegisterSearch")
@Data
public class RoomTypeRegisterSearch extends BaseRequest {
	private Integer nightAuditRoomTypeId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomTypeId;
	private String roomTypeName;
	private Integer totalRoomCount;
	private String openRoomCount;
	private Integer repairRoomCount;
	private Integer noserviceRoomCount;
	private Integer selfRoomCount;
	private Integer totalAmount;
	private Integer averagePrice;
	//开房率
	private Double openRate;
	//单房贡献率
	private Double revpar;
	private Integer goodsMoney  ;
	private Integer foodsMoney  ;
	private Integer businessDay;
	private Integer auditYear;
	private Integer auditYearMonth;

	private Integer businessDayMin;
	private Integer businessDayMax;

	private Integer groupType;
}

