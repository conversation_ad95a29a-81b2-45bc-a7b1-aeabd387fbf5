package com.pms.czhotelfoundation.bean.hotel.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("HotelBrandInfoSearch")
public class HotelBrandInfoSearch extends BaseSearch {
	private Integer id;
	private Integer hotelGroupId;
	private String brandName;
	private String brandNameEn;
	private String brandDesc;
	private String brandDescEn;
	private String telephone;
	private String fax;
	private String contact;
	private String contactPhone;
	private java.util.Date createTime;
	private String createUserId;
	private String createUserName;
	private java.util.Date updateTime;
	private String updateUserId;
	private String updateUserName;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setBrandName(String value) {
		this.brandName = value;
	}
	
	public String getBrandName() {
		return this.brandName;
	}
	public void setBrandNameEn(String value) {
		this.brandNameEn = value;
	}
	
	public String getBrandNameEn() {
		return this.brandNameEn;
	}
	public void setBrandDesc(String value) {
		this.brandDesc = value;
	}
	
	public String getBrandDesc() {
		return this.brandDesc;
	}
	public void setBrandDescEn(String value) {
		this.brandDescEn = value;
	}
	
	public String getBrandDescEn() {
		return this.brandDescEn;
	}
	public void setTelephone(String value) {
		this.telephone = value;
	}
	
	public String getTelephone() {
		return this.telephone;
	}
	public void setFax(String value) {
		this.fax = value;
	}
	
	public String getFax() {
		return this.fax;
	}
	public void setContact(String value) {
		this.contact = value;
	}
	
	public String getContact() {
		return this.contact;
	}
	public void setContactPhone(String value) {
		this.contactPhone = value;
	}
	
	public String getContactPhone() {
		return this.contactPhone;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

}

