package com.pms.czhotelfoundation.bean.hotel.search;

import com.pms.czpmsutils.request.PageBaseRequest;
import org.apache.ibatis.type.Alias;

@Alias("HotelBaseInfoSearch")
public class HotelBaseInfoSearch extends PageBaseRequest {
	private Integer hid;
	private String hotelName;
	private String hotelNameEn;
	private String shortName;
	private String shortNameEn;
	private Double lon;
	private Double lat;
	private String poi;
	private String addr;
	private String des;
	private String desEn;
	private Integer star;
	private Integer city;
	private Integer area;
	private Integer provice;
	private Integer country;
	private String telephone;
	private String fax;
	private String contact;
	private String contactPhone;
	private Integer hotelType;
	private String attention;
	private Integer lockType;
	private String firstPinyin;
	private Integer environmentalState;
	private Integer pmsType;
	private String email;
	private Integer hotelStatus;
	private java.util.Date cancelTime;
	private String cancelReason;
	private Integer isChain;
	private Integer roomCount;
	private Integer hotelGroupId;
	private Integer theamId;
	private java.util.Date createTime;
	private String createUserId;
	private String createUserName;
	private java.util.Date updateTime;
	private String updateUserId;
	private java.util.Date exprieDate;
	private String reviewUserId;
	private java.util.Date reviewTime;
	private Integer checkLimit;
	private Integer currencyRoomTime;
	private Integer leaveTime;
	private Integer systemUserId;
	private String openId;
	private String updateUserName;

	private Integer locType = 2 ;

	// 微信类型
	private Integer wxType = 2;

	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelName(String value) {
		this.hotelName = value;
	}

	public String getHotelName() {
		return this.hotelName;
	}
	public void setHotelNameEn(String value) {
		this.hotelNameEn = value;
	}

	public String getHotelNameEn() {
		return this.hotelNameEn;
	}
	public void setShortName(String value) {
		this.shortName = value;
	}

	public String getShortName() {
		return this.shortName;
	}
	public void setShortNameEn(String value) {
		this.shortNameEn = value;
	}

	public String getShortNameEn() {
		return this.shortNameEn;
	}
	public void setLon(Double value) {
		this.lon = value;
	}

	public Double getLon() {
		return this.lon;
	}
	public void setLat(Double value) {
		this.lat = value;
	}

	public Double getLat() {
		return this.lat;
	}
	public void setPoi(String value) {
		this.poi = value;
	}

	public String getPoi() {
		return this.poi;
	}
	public void setAddr(String value) {
		this.addr = value;
	}

	public String getAddr() {
		return this.addr;
	}
	public void setDes(String value) {
		this.des = value;
	}

	public String getDes() {
		return this.des;
	}
	public void setDesEn(String value) {
		this.desEn = value;
	}

	public String getDesEn() {
		return this.desEn;
	}
	public void setStar(Integer value) {
		this.star = value;
	}

	public Integer getStar() {
		return this.star;
	}
	public void setCity(Integer value) {
		this.city = value;
	}

	public Integer getCity() {
		return this.city;
	}
	public void setArea(Integer value) {
		this.area = value;
	}

	public Integer getArea() {
		return this.area;
	}
	public void setProvice(Integer value) {
		this.provice = value;
	}

	public Integer getProvice() {
		return this.provice;
	}
	public void setCountry(Integer value) {
		this.country = value;
	}

	public Integer getCountry() {
		return this.country;
	}
	public void setTelephone(String value) {
		this.telephone = value;
	}

	public String getTelephone() {
		return this.telephone;
	}
	public void setFax(String value) {
		this.fax = value;
	}

	public String getFax() {
		return this.fax;
	}
	public void setContact(String value) {
		this.contact = value;
	}

	public String getContact() {
		return this.contact;
	}
	public void setContactPhone(String value) {
		this.contactPhone = value;
	}

	public String getContactPhone() {
		return this.contactPhone;
	}
	public void setHotelType(Integer value) {
		this.hotelType = value;
	}

	public Integer getHotelType() {
		return this.hotelType;
	}
	public void setAttention(String value) {
		this.attention = value;
	}

	public String getAttention() {
		return this.attention;
	}
	public void setLockType(Integer value) {
		this.lockType = value;
	}

	public Integer getLockType() {
		return this.lockType;
	}
	public void setFirstPinyin(String value) {
		this.firstPinyin = value;
	}

	public String getFirstPinyin() {
		return this.firstPinyin;
	}
	public void setEnvironmentalState(Integer value) {
		this.environmentalState = value;
	}

	public Integer getEnvironmentalState() {
		return this.environmentalState;
	}
	public void setPmsType(Integer value) {
		this.pmsType = value;
	}

	public Integer getPmsType() {
		return this.pmsType;
	}
	public void setEmail(String value) {
		this.email = value;
	}

	public String getEmail() {
		return this.email;
	}
	public void setHotelStatus(Integer value) {
		this.hotelStatus = value;
	}

	public Integer getHotelStatus() {
		return this.hotelStatus;
	}

	public void setCancelTime(java.util.Date value) {
		this.cancelTime = value;
	}

	public java.util.Date getCancelTime() {
		return this.cancelTime;
	}
	public void setCancelReason(String value) {
		this.cancelReason = value;
	}

	public String getCancelReason() {
		return this.cancelReason;
	}
	public void setIsChain(Integer value) {
		this.isChain = value;
	}

	public Integer getIsChain() {
		return this.isChain;
	}
	public void setRoomCount(Integer value) {
		this.roomCount = value;
	}

	public Integer getRoomCount() {
		return this.roomCount;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setTheamId(Integer value) {
		this.theamId = value;
	}

	public Integer getTheamId() {
		return this.theamId;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}

	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}

	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}

	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}

	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}

	public String getUpdateUserId() {
		return this.updateUserId;
	}

	public void setExprieDate(java.util.Date value) {
		this.exprieDate = value;
	}

	public java.util.Date getExprieDate() {
		return this.exprieDate;
	}
	public void setReviewUserId(String value) {
		this.reviewUserId = value;
	}

	public String getReviewUserId() {
		return this.reviewUserId;
	}

	public void setReviewTime(java.util.Date value) {
		this.reviewTime = value;
	}

	public java.util.Date getReviewTime() {
		return this.reviewTime;
	}
	public void setCheckLimit(Integer value) {
		this.checkLimit = value;
	}

	public Integer getCheckLimit() {
		return this.checkLimit;
	}
	public void setCurrencyRoomTime(Integer value) {
		this.currencyRoomTime = value;
	}

	public Integer getCurrencyRoomTime() {
		return this.currencyRoomTime;
	}
	public void setLeaveTime(Integer value) {
		this.leaveTime = value;
	}

	public Integer getLeaveTime() {
		return this.leaveTime;
	}
	public void setSystemUserId(Integer value) {
		this.systemUserId = value;
	}

	public Integer getSystemUserId() {
		return this.systemUserId;
	}
	public void setOpenId(String value) {
		this.openId = value;
	}

	public String getOpenId() {
		return this.openId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}

	public String getUpdateUserName() {
		return this.updateUserName;
	}

	public Integer getLocType() {
		return locType;
	}

	public void setLocType(Integer locType) {
		this.locType = locType;
	}

	public Integer getWxType() {
		return wxType;
	}

	public void setWxType(Integer wxType) {
		this.wxType = wxType;
	}
}

