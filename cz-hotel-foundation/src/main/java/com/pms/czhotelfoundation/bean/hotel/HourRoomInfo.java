package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;
import java.util.Date;

public class HourRoomInfo implements Serializable{
	private Integer hourRoomId  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private String hourRoomName  ;
	private Integer hourRoomCode  ;
	private String startTime  ;
	private String endTime  ;
	private Integer selfmachineCanUse  ;
	private Integer state  ;
	private String des  ;
	private Date createTime  ;
	private Integer createUserId  ;
	private Date updateTime  ;
	private Integer updateUserId  ;
	private Integer sort  ;

	public HourRoomInfo(){
	}

	public HourRoomInfo(Integer hourRoomId){
		this.hourRoomId = hourRoomId;
	}

	public void setHourRoomId(Integer hourRoomId) {
		this.hourRoomId = hourRoomId;
	}

	public Integer getHourRoomId() {
		return this.hourRoomId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setHourRoomName(String hourRoomName) {
		this.hourRoomName = hourRoomName;
	}

	public String getHourRoomName() {
		return this.hourRoomName;
	}
	public void setHourRoomCode(Integer hourRoomCode) {
		this.hourRoomCode = hourRoomCode;
	}

	public Integer getHourRoomCode() {
		return this.hourRoomCode;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getStartTime() {
		return this.startTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getEndTime() {
		return this.endTime;
	}
	public void setSelfmachineCanUse(Integer selfmachineCanUse) {
		this.selfmachineCanUse = selfmachineCanUse;
	}

	public Integer getSelfmachineCanUse() {
		return this.selfmachineCanUse;
	}
	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getState() {
		return this.state;
	}
	public void setDes(String des) {
		this.des = des;
	}

	public String getDes() {
		return this.des;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}

	public Integer getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}

	public Integer getUpdateUserId() {
		return this.updateUserId;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public Integer getSort() {
		return this.sort;
	}

}

