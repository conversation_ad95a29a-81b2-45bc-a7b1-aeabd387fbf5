package com.pms.czhotelfoundation.bean.hotel.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class HotelOrgPriceImageSearch extends PageBaseRequest{
	private Integer id;
	private String imagePath;
	private Integer imageType;
	private String imageName;
	private Integer hotelGroupId;
	private Integer systemCode;
	private Integer state;
	private java.util.Date createTime;
	private String createUserId;
	private java.util.Date updateTime;
	private String updateUserId;
	private String remark;
	private Integer hid;
	private Integer businessType;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setImagePath(String value) {
		this.imagePath = value;
	}
	
	public String getImagePath() {
		return this.imagePath;
	}
	public void setImageType(Integer value) {
		this.imageType = value;
	}
	
	public Integer getImageType() {
		return this.imageType;
	}
	public void setImageName(String value) {
		this.imageName = value;
	}
	
	public String getImageName() {
		return this.imageName;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setSystemCode(Integer value) {
		this.systemCode = value;
	}
	
	public Integer getSystemCode() {
		return this.systemCode;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setRemark(String value) {
		this.remark = value;
	}
	
	public String getRemark() {
		return this.remark;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setBusinessType(Integer value) {
		this.businessType = value;
	}
	
	public Integer getBusinessType() {
		return this.businessType;
	}

}

