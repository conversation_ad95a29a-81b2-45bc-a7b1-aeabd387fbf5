package com.pms.czhotelfoundation.bean.room.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("RoomAuxiliaryRelationSearch")
public class RoomAuxiliaryRelationSearch extends BaseSearch {
	private Integer relationId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomId;
	private String roomNum;
	private Integer registId;
	private Integer bookingOrderId;
	private Integer roomAuxiliaryId;
	private Integer sort  ;

	private String roomIds;

	public void setRelationId(Integer value) {
		this.relationId = value;
	}
	
	public Integer getRelationId() {
		return this.relationId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomId(Integer value) {
		this.roomId = value;
	}
	
	public Integer getRoomId() {
		return this.roomId;
	}
	public void setRoomNum(String value) {
		this.roomNum = value;
	}
	
	public String getRoomNum() {
		return this.roomNum;
	}
	public void setRegistId(Integer value) {
		this.registId = value;
	}
	
	public Integer getRegistId() {
		return this.registId;
	}
	public void setBookingOrderId(Integer value) {
		this.bookingOrderId = value;
	}
	
	public Integer getBookingOrderId() {
		return this.bookingOrderId;
	}
	public void setRoomAuxiliaryId(Integer value) {
		this.roomAuxiliaryId = value;
	}
	
	public Integer getRoomAuxiliaryId() {
		return this.roomAuxiliaryId;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public String getRoomIds() {
		return roomIds;
	}

	public void setRoomIds(String roomIds) {
		this.roomIds = roomIds;
	}
}

