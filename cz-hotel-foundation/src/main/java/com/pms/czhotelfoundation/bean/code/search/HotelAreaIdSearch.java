package com.pms.czhotelfoundation.bean.code.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("HotelAreaIdSearch")
public class HotelAreaIdSearch extends BaseSearch {
	private Integer id;
	private Integer hotelGroupId;
	private String hotelGroupName;
	private String hotelAreaName;
	private String hotelAreaNameEn;
	private Integer city;
	private Integer area;
	private Integer province;
	private Integer country;
	private Integer parentAreaId;
	private Integer systemParam;
	private String remark;
	private String updateUserName;
	private java.util.Date updateUserTime;
	private String updateUserId;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setHotelGroupName(String value) {
		this.hotelGroupName = value;
	}
	
	public String getHotelGroupName() {
		return this.hotelGroupName;
	}
	public void setHotelAreaName(String value) {
		this.hotelAreaName = value;
	}
	
	public String getHotelAreaName() {
		return this.hotelAreaName;
	}
	public void setHotelAreaNameEn(String value) {
		this.hotelAreaNameEn = value;
	}
	
	public String getHotelAreaNameEn() {
		return this.hotelAreaNameEn;
	}
	public void setCity(Integer value) {
		this.city = value;
	}
	
	public Integer getCity() {
		return this.city;
	}
	public void setArea(Integer value) {
		this.area = value;
	}
	
	public Integer getArea() {
		return this.area;
	}
	public void setProvince(Integer value) {
		this.province = value;
	}
	
	public Integer getProvince() {
		return this.province;
	}
	public void setCountry(Integer value) {
		this.country = value;
	}
	
	public Integer getCountry() {
		return this.country;
	}
	public void setParentAreaId(Integer value) {
		this.parentAreaId = value;
	}
	
	public Integer getParentAreaId() {
		return this.parentAreaId;
	}
	public void setSystemParam(Integer value) {
		this.systemParam = value;
	}
	
	public Integer getSystemParam() {
		return this.systemParam;
	}
	public void setRemark(String value) {
		this.remark = value;
	}
	
	public String getRemark() {
		return this.remark;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

	public void setUpdateUserTime(java.util.Date value) {
		this.updateUserTime = value;
	}
	
	public java.util.Date getUpdateUserTime() {
		return this.updateUserTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}

}

