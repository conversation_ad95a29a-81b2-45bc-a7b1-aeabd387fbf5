package com.pms.czhotelfoundation.bean.ota;

import java.util.Date;
import java.util.List;

public class OtaPmsOrderInfo {

    private Integer hid;
    private Integer bookingOrderId;
    private String thirdPlatformOrderCode;
    private String sn;
    private Integer roomCount;
    private Date orderTime;

    private String bookingName;
    private String bookingPhone;
    private String bookingIdCode;
    private Integer cardId;
    private String cardNo;
    private Integer companyId = 0;
    private String companyName;
    private Integer companyAccountId;

    private String checkinName;
    private String checkinPhone;
    private Date checkinTime;
    private Date checkoutTime;

    private Integer preferentialPrice;
    private Integer totalPrice;
    private Integer payPrice;
    private Integer dayCount;
    private Integer unitPrice;
    private String keepTime;
    private Integer orderType;
    private Date acceptTime;

    private Integer orderStatus;
    private Integer otaStatus;
    private Integer fromType;
    private Integer resourceId;
    private Integer payType;
    private Integer rateCodeId;
    private Integer type;

    private String roomTypeSummary;

    private String remark;

    private List<OtaBookRoomTypeInfo> bookRoomTypeInfoList;

    private List<OtaOrderDailyPrice> dailyPriceList;

    private List<GustInfo> gustList;

    public List<GustInfo> getGustList() {
        return gustList;
    }

    public Integer getResourceId() {
        return resourceId;
    }

    public void setResourceId(Integer resourceId) {
        this.resourceId = resourceId;
    }

    public void setGustList(List<GustInfo> gustList) {
        this.gustList = gustList;
    }

    public Integer getHid() {
        return hid;
    }

    public void setHid(Integer hid) {
        this.hid = hid;
    }

    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getRoomCount() {
        return roomCount;
    }

    public void setRoomCount(Integer roomCount) {
        this.roomCount = roomCount;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public String getBookingName() {
        return bookingName;
    }

    public void setBookingName(String bookingName) {
        this.bookingName = bookingName;
    }

    public String getBookingPhone() {
        return bookingPhone;
    }

    public void setBookingPhone(String bookingPhone) {
        this.bookingPhone = bookingPhone;
    }

    public String getBookingIdCode() {
        return bookingIdCode;
    }

    public void setBookingIdCode(String bookingIdCode) {
        this.bookingIdCode = bookingIdCode;
    }

    public Integer getCardId() {
        return cardId;
    }

    public void setCardId(Integer cardId) {
        this.cardId = cardId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Integer getCompanyAccountId() {
        return companyAccountId;
    }

    public void setCompanyAccountId(Integer companyAccountId) {
        this.companyAccountId = companyAccountId;
    }

    public String getCheckinName() {
        return checkinName;
    }

    public void setCheckinName(String checkinName) {
        this.checkinName = checkinName;
    }

    public String getCheckinPhone() {
        return checkinPhone;
    }

    public void setCheckinPhone(String checkinPhone) {
        this.checkinPhone = checkinPhone;
    }

    public Date getCheckinTime() {
        return checkinTime;
    }

    public void setCheckinTime(Date checkinTime) {
        this.checkinTime = checkinTime;
    }

    public Date getCheckoutTime() {
        return checkoutTime;
    }

    public void setCheckoutTime(Date checkoutTime) {
        this.checkoutTime = checkoutTime;
    }

    public Integer getPreferentialPrice() {
        return preferentialPrice;
    }

    public void setPreferentialPrice(Integer preferentialPrice) {
        this.preferentialPrice = preferentialPrice;
    }

    public Integer getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Integer totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Integer getPayPrice() {
        return payPrice;
    }

    public void setPayPrice(Integer payPrice) {
        this.payPrice = payPrice;
    }

    public Integer getDayCount() {
        return dayCount;
    }

    public void setDayCount(Integer dayCount) {
        this.dayCount = dayCount;
    }

    public Integer getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Integer unitPrice) {
        this.unitPrice = unitPrice;
    }

    public String getKeepTime() {
        return keepTime;
    }

    public void setKeepTime(String keepTime) {
        this.keepTime = keepTime;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Date getAcceptTime() {
        return acceptTime;
    }

    public void setAcceptTime(Date acceptTime) {
        this.acceptTime = acceptTime;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getOtaStatus() {
        return otaStatus;
    }

    public void setOtaStatus(Integer otaStatus) {
        this.otaStatus = otaStatus;
    }

    public Integer getFromType() {
        return fromType;
    }

    public void setFromType(Integer fromType) {
        this.fromType = fromType;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public Integer getRateCodeId() {
        return rateCodeId;
    }

    public void setRateCodeId(Integer rateCodeId) {
        this.rateCodeId = rateCodeId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getRoomTypeSummary() {
        return roomTypeSummary;
    }

    public void setRoomTypeSummary(String roomTypeSummary) {
        this.roomTypeSummary = roomTypeSummary;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<OtaBookRoomTypeInfo> getBookRoomTypeInfoList() {
        return bookRoomTypeInfoList;
    }

    public void setBookRoomTypeInfoList(List<OtaBookRoomTypeInfo> bookRoomTypeInfoList) {
        this.bookRoomTypeInfoList = bookRoomTypeInfoList;
    }

    public String getThirdPlatformOrderCode() {
        return thirdPlatformOrderCode;
    }

    public void setThirdPlatformOrderCode(String thirdPlatformOrderCode) {
        this.thirdPlatformOrderCode = thirdPlatformOrderCode;
    }

    public List<OtaOrderDailyPrice> getDailyPriceList() {
        return dailyPriceList;
    }

    public void setDailyPriceList(List<OtaOrderDailyPrice> dailyPriceList) {
        this.dailyPriceList = dailyPriceList;
    }
}
