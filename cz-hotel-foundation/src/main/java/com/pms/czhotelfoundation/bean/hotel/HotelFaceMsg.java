package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;
import java.util.Date;

public class HotelFaceMsg implements Serializable {
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Date startTime  ;
	private Date endTime  ;
	private Integer state  ;
	private String facePeople  ;
	private String facePhone  ;
	private Integer faceId  ;
	private String faceName  ;
	private String faceMsg  ;
	private String faceUnit  ;
	private Integer faceMoney  ;
	private String remark  ;
	private Date updateTime  ;
	private String updateUserId  ;
	private String updateUserName  ;

	public HotelFaceMsg(){
	}

	public HotelFaceMsg(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getStartTime() {
		return this.startTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Date getEndTime() {
		return this.endTime;
	}
	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getState() {
		return this.state;
	}
	public void setFacePeople(String facePeople) {
		this.facePeople = facePeople;
	}

	public String getFacePeople() {
		return this.facePeople;
	}
	public void setFacePhone(String facePhone) {
		this.facePhone = facePhone;
	}

	public String getFacePhone() {
		return this.facePhone;
	}
	public void setFaceId(Integer faceId) {
		this.faceId = faceId;
	}

	public Integer getFaceId() {
		return this.faceId;
	}
	public void setFaceName(String faceName) {
		this.faceName = faceName;
	}

	public String getFaceName() {
		return this.faceName;
	}
	public void setFaceMsg(String faceMsg) {
		this.faceMsg = faceMsg;
	}

	public String getFaceMsg() {
		return this.faceMsg;
	}
	public void setFaceUnit(String faceUnit) {
		this.faceUnit = faceUnit;
	}

	public String getFaceUnit() {
		return this.faceUnit;
	}
	public void setFaceMoney(Integer faceMoney) {
		this.faceMoney = faceMoney;
	}

	public Integer getFaceMoney() {
		return this.faceMoney;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

}

