package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;
import java.util.Date;

/**
 * 酒店班次信息
 */
public class HotelClassInfo implements Serializable{
	private Integer hotelClassId  ;
	private Integer hotelClassType  ;
	private Integer pettyCash  ;
	private Integer lastCash  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer state  ;
	private Date createTime  ;
	private String createUserId  ;
	private String createUserName  ;
	private Date updateTime  ;
	private String updateUserId  ;
	private String updateUserName  ;

	public HotelClassInfo(){
	}

	public HotelClassInfo(Integer hotelClassId){
		this.hotelClassId = hotelClassId;
	}

	public void setHotelClassId(Integer hotelClassId) {
		this.hotelClassId = hotelClassId;
	}
	
	public Integer getHotelClassId() {
		return this.hotelClassId;
	}
	public void setHotelClassType(Integer hotelClassType) {
		this.hotelClassType = hotelClassType;
	}
	
	public Integer getHotelClassType() {
		return this.hotelClassType;
	}
	public void setPettyCash(Integer pettyCash) {
		this.pettyCash = pettyCash;
	}
	
	public Integer getPettyCash() {
		return this.pettyCash;
	}
	public void setLastCash(Integer lastCash) {
		this.lastCash = lastCash;
	}
	
	public Integer getLastCash() {
		return this.lastCash;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

}

