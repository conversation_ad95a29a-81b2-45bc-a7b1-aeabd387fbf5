package com.pms.czhotelfoundation.bean.code.search;

import com.pms.czpmsutils.constant.BaseBean;
import org.apache.ibatis.type.Alias;

@Alias("AreaSearch")
public class AreaSearch extends BaseBean {
	private Integer id;
	private String code;
	private String name;
	private String citycode;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setCode(String value) {
		this.code = value;
	}
	
	public String getCode() {
		return this.code;
	}
	public void setName(String value) {
		this.name = value;
	}
	
	public String getName() {
		return this.name;
	}
	public void setCitycode(String value) {
		this.citycode = value;
	}
	
	public String getCitycode() {
		return this.citycode;
	}

}

