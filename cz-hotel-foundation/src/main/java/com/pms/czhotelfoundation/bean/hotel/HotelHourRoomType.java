package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;
import java.util.Date;

public class HotelHourRoomType implements Serializable{
	private Integer id  ;
	private Integer roomTypeId  ;
	private String roomTypeName  ;
	private Integer hourRoomInfoId  ;
	private Integer price  ;
	private Integer hourPrice  ;
	private Integer hourLenght  ;
	private Integer sort  ;
	private Integer state  ;
	private Integer maxPrice  ;
	private Integer toDayRoom  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer breakfastId  ;
	private String reamark  ;
	private Date createTime  ;
	private Integer createUserId  ;
	private Date updateTime  ;
	private Integer updateUserId  ;
	private Integer hourRoomCode  ;

	public HotelHourRoomType(){
	}

	public HotelHourRoomType(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setRoomTypeName(String roomTypeName) {
		this.roomTypeName = roomTypeName;
	}
	
	public String getRoomTypeName() {
		return this.roomTypeName;
	}
	public void setHourRoomInfoId(Integer hourRoomInfoId) {
		this.hourRoomInfoId = hourRoomInfoId;
	}
	
	public Integer getHourRoomInfoId() {
		return this.hourRoomInfoId;
	}
	public void setPrice(Integer price) {
		this.price = price;
	}
	
	public Integer getPrice() {
		return this.price;
	}
	public void setHourPrice(Integer hourPrice) {
		this.hourPrice = hourPrice;
	}
	
	public Integer getHourPrice() {
		return this.hourPrice;
	}
	public void setHourLenght(Integer hourLenght) {
		this.hourLenght = hourLenght;
	}
	
	public Integer getHourLenght() {
		return this.hourLenght;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	
	public Integer getSort() {
		return this.sort;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setMaxPrice(Integer maxPrice) {
		this.maxPrice = maxPrice;
	}
	
	public Integer getMaxPrice() {
		return this.maxPrice;
	}
	public void setToDayRoom(Integer toDayRoom) {
		this.toDayRoom = toDayRoom;
	}
	
	public Integer getToDayRoom() {
		return this.toDayRoom;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setBreakfastId(Integer breakfastId) {
		this.breakfastId = breakfastId;
	}
	
	public Integer getBreakfastId() {
		return this.breakfastId;
	}
	public void setReamark(String reamark) {
		this.reamark = reamark;
	}
	
	public String getReamark() {
		return this.reamark;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	
	public Integer getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public Integer getUpdateUserId() {
		return this.updateUserId;
	}
	public void setHourRoomCode(Integer hourRoomCode) {
		this.hourRoomCode = hourRoomCode;
	}
	
	public Integer getHourRoomCode() {
		return this.hourRoomCode;
	}

}

