package com.pms.czhotelfoundation.bean.room.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class RoomInfoQrSearch extends PageBaseRequest{
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomInfoId;
	private String remark;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomInfoId(Integer value) {
		this.roomInfoId = value;
	}
	
	public Integer getRoomInfoId() {
		return this.roomInfoId;
	}
	public void setRemark(String value) {
		this.remark = value;
	}
	
	public String getRemark() {
		return this.remark;
	}

}

