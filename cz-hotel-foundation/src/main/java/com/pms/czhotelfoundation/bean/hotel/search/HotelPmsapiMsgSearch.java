package com.pms.czhotelfoundation.bean.hotel.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("HotelPmsapiMsgSearch")
public class HotelPmsapiMsgSearch extends BaseSearch {
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private java.util.Date startTime;
	private java.util.Date endTime;
	private Integer state;
	private String pmsPeople;
	private String pmsPhone;
	private Integer pmsId;
	private String pmsName;
	private String pmsMsg;
	private String pmsUnit;
	private Integer pmsMoney;
	private String remark;
	private java.util.Date updateTime;
	private String updateUserId;
	private String updateUserName;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

	public void setStartTime(java.util.Date value) {
		this.startTime = value;
	}
	
	public java.util.Date getStartTime() {
		return this.startTime;
	}

	public void setEndTime(java.util.Date value) {
		this.endTime = value;
	}
	
	public java.util.Date getEndTime() {
		return this.endTime;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setPmsPeople(String value) {
		this.pmsPeople = value;
	}
	
	public String getPmsPeople() {
		return this.pmsPeople;
	}
	public void setPmsPhone(String value) {
		this.pmsPhone = value;
	}
	
	public String getPmsPhone() {
		return this.pmsPhone;
	}
	public void setPmsId(Integer value) {
		this.pmsId = value;
	}
	
	public Integer getPmsId() {
		return this.pmsId;
	}
	public void setPmsName(String value) {
		this.pmsName = value;
	}
	
	public String getPmsName() {
		return this.pmsName;
	}
	public void setPmsMsg(String value) {
		this.pmsMsg = value;
	}
	
	public String getPmsMsg() {
		return this.pmsMsg;
	}
	public void setPmsUnit(String value) {
		this.pmsUnit = value;
	}
	
	public String getPmsUnit() {
		return this.pmsUnit;
	}
	public void setPmsMoney(Integer value) {
		this.pmsMoney = value;
	}
	
	public Integer getPmsMoney() {
		return this.pmsMoney;
	}
	public void setRemark(String value) {
		this.remark = value;
	}
	
	public String getRemark() {
		return this.remark;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

}

