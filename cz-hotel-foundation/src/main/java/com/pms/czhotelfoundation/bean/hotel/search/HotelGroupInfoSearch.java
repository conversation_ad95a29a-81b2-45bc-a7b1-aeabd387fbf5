package com.pms.czhotelfoundation.bean.hotel.search;

import com.pms.czpmsutils.request.PageBaseRequest;
import org.apache.ibatis.type.Alias;

@Alias("HotelGroupInfoSearch")
public class HotelGroupInfoSearch extends PageBaseRequest {
	private Integer chainId;
	private String chainName;
	private String leader;
	private String chainDesc;
	private String chainDescEn;
	private String telephone;
	private String addr;
	private String phone;
	private Integer enable;
	private java.util.Date createTime;
	private String createUserId;
	private java.util.Date updateTime;
	private String updateUserId;

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	private String uuid;

	public void setChainId(Integer value) {
		this.chainId = value;
	}
	
	public Integer getChainId() {
		return this.chainId;
	}
	public void setChainName(String value) {
		this.chainName = value;
	}
	
	public String getChainName() {
		return this.chainName;
	}
	public void setLeader(String value) {
		this.leader = value;
	}
	
	public String getLeader() {
		return this.leader;
	}
	public void setChainDesc(String value) {
		this.chainDesc = value;
	}
	
	public String getChainDesc() {
		return this.chainDesc;
	}
	public void setChainDescEn(String value) {
		this.chainDescEn = value;
	}
	
	public String getChainDescEn() {
		return this.chainDescEn;
	}
	public void setTelephone(String value) {
		this.telephone = value;
	}
	
	public String getTelephone() {
		return this.telephone;
	}
	public void setAddr(String value) {
		this.addr = value;
	}
	
	public String getAddr() {
		return this.addr;
	}
	public void setPhone(String value) {
		this.phone = value;
	}
	
	public String getPhone() {
		return this.phone;
	}
	public void setEnable(Integer value) {
		this.enable = value;
	}
	
	public Integer getEnable() {
		return this.enable;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}

}

