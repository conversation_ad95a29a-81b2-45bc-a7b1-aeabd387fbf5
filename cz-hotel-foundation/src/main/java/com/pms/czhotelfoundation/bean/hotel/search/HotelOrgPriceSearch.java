package com.pms.czhotelfoundation.bean.hotel.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class HotelOrgPriceSearch extends PageBaseRequest{
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer state;
	private java.util.Date createTime;
	private Integer createUserId;
	private java.util.Date updateTime;
	private Integer updateUserId;
	private Integer orgType;
	private String orgBackgroudColor;
	private String orgBackgroudImage;
	private String hotelName;
	private Integer hotelFontSize;
	private String hotelDesc;
	private Integer hotelDescFontSize;
	private Integer priceBodyFontSize;
	private Integer priceHeadFontSize;
	private Integer pageNum;
	private Integer pageChangeTime;
	private String remark;
	private Integer isRotation;
	private Integer rotationTimes;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer value) {
		this.createUserId = value;
	}
	
	public Integer getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer value) {
		this.updateUserId = value;
	}
	
	public Integer getUpdateUserId() {
		return this.updateUserId;
	}
	public void setOrgType(Integer value) {
		this.orgType = value;
	}
	
	public Integer getOrgType() {
		return this.orgType;
	}
	public void setOrgBackgroudColor(String value) {
		this.orgBackgroudColor = value;
	}
	
	public String getOrgBackgroudColor() {
		return this.orgBackgroudColor;
	}
	public void setOrgBackgroudImage(String value) {
		this.orgBackgroudImage = value;
	}
	
	public String getOrgBackgroudImage() {
		return this.orgBackgroudImage;
	}
	public void setHotelName(String value) {
		this.hotelName = value;
	}
	
	public String getHotelName() {
		return this.hotelName;
	}
	public void setHotelFontSize(Integer value) {
		this.hotelFontSize = value;
	}
	
	public Integer getHotelFontSize() {
		return this.hotelFontSize;
	}
	public void setHotelDesc(String value) {
		this.hotelDesc = value;
	}
	
	public String getHotelDesc() {
		return this.hotelDesc;
	}
	public void setHotelDescFontSize(Integer value) {
		this.hotelDescFontSize = value;
	}
	
	public Integer getHotelDescFontSize() {
		return this.hotelDescFontSize;
	}
	public void setPriceBodyFontSize(Integer value) {
		this.priceBodyFontSize = value;
	}
	
	public Integer getPriceBodyFontSize() {
		return this.priceBodyFontSize;
	}
	public void setPriceHeadFontSize(Integer value) {
		this.priceHeadFontSize = value;
	}
	
	public Integer getPriceHeadFontSize() {
		return this.priceHeadFontSize;
	}
	public void setPageNum(Integer value) {
		this.pageNum = value;
	}
	
	public Integer getPageNum() {
		return this.pageNum;
	}
	public void setPageChangeTime(Integer value) {
		this.pageChangeTime = value;
	}
	
	public Integer getPageChangeTime() {
		return this.pageChangeTime;
	}
	public void setRemark(String value) {
		this.remark = value;
	}
	
	public String getRemark() {
		return this.remark;
	}
	public void setIsRotation(Integer value) {
		this.isRotation = value;
	}
	
	public Integer getIsRotation() {
		return this.isRotation;
	}
	public void setRotationTimes(Integer value) {
		this.rotationTimes = value;
	}
	
	public Integer getRotationTimes() {
		return this.rotationTimes;
	}

}

