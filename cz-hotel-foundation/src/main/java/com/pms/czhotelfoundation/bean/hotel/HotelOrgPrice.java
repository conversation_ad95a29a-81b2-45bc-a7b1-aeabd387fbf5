package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 */
public class HotelOrgPrice implements Serializable{
	//
	private Integer id;
	//
	private Integer hid;
	//
	private Integer hotelGroupId;
	//0-停用 1-可用
	private Integer state;
	//
	private Date createTime;
	//
	private Integer createUserId;
	//
	private Date updateTime;
	//
	private Integer updateUserId;
	//1-横板 2-竖版
	private Integer orgType;
	//背景色
	private String orgBackgroudColor;
	//背景图片
	private String orgBackgroudImage;
	//酒店名称
	private String hotelName;
	//酒店名称字号
	private Integer hotelFontSize;
	//酒店提示
	private String hotelDesc;
	//酒店提示字号
	private Integer hotelDescFontSize;
	//房型内容字号
	private Integer priceBodyFontSize;
	//房型标题字号
	private Integer priceHeadFontSize;
	//每屏展示的条数
	private Integer pageNum;
	//每屏幕切换的时间
	private Integer pageChangeTime;
	//备注信息
	private String remark;
	//0-不轮播广告图片  1-轮播广告图片
	private Integer isRotation;
	//轮播的时间
	private Integer rotationTimes;

	public HotelOrgPrice(){
	}

	public HotelOrgPrice(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	
	public Integer getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public Integer getUpdateUserId() {
		return this.updateUserId;
	}
	public void setOrgType(Integer orgType) {
		this.orgType = orgType;
	}
	
	public Integer getOrgType() {
		return this.orgType;
	}
	public void setOrgBackgroudColor(String orgBackgroudColor) {
		this.orgBackgroudColor = orgBackgroudColor;
	}
	
	public String getOrgBackgroudColor() {
		return this.orgBackgroudColor;
	}
	public void setOrgBackgroudImage(String orgBackgroudImage) {
		this.orgBackgroudImage = orgBackgroudImage;
	}
	
	public String getOrgBackgroudImage() {
		return this.orgBackgroudImage;
	}
	public void setHotelName(String hotelName) {
		this.hotelName = hotelName;
	}
	
	public String getHotelName() {
		return this.hotelName;
	}
	public void setHotelFontSize(Integer hotelFontSize) {
		this.hotelFontSize = hotelFontSize;
	}
	
	public Integer getHotelFontSize() {
		return this.hotelFontSize;
	}
	public void setHotelDesc(String hotelDesc) {
		this.hotelDesc = hotelDesc;
	}
	
	public String getHotelDesc() {
		return this.hotelDesc;
	}
	public void setHotelDescFontSize(Integer hotelDescFontSize) {
		this.hotelDescFontSize = hotelDescFontSize;
	}
	
	public Integer getHotelDescFontSize() {
		return this.hotelDescFontSize;
	}
	public void setPriceBodyFontSize(Integer priceBodyFontSize) {
		this.priceBodyFontSize = priceBodyFontSize;
	}
	
	public Integer getPriceBodyFontSize() {
		return this.priceBodyFontSize;
	}
	public void setPriceHeadFontSize(Integer priceHeadFontSize) {
		this.priceHeadFontSize = priceHeadFontSize;
	}
	
	public Integer getPriceHeadFontSize() {
		return this.priceHeadFontSize;
	}
	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}
	
	public Integer getPageNum() {
		return this.pageNum;
	}
	public void setPageChangeTime(Integer pageChangeTime) {
		this.pageChangeTime = pageChangeTime;
	}
	
	public Integer getPageChangeTime() {
		return this.pageChangeTime;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getRemark() {
		return this.remark;
	}
	public void setIsRotation(Integer isRotation) {
		this.isRotation = isRotation;
	}
	
	public Integer getIsRotation() {
		return this.isRotation;
	}
	public void setRotationTimes(Integer rotationTimes) {
		this.rotationTimes = rotationTimes;
	}
	
	public Integer getRotationTimes() {
		return this.rotationTimes;
	}

}

