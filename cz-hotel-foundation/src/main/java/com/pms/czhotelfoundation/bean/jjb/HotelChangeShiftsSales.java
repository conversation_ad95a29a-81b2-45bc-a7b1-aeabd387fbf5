package com.pms.czhotelfoundation.bean.jjb;

import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;

public class HotelChangeShiftsSales extends BaseBean implements Serializable{
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer roomMoney  ;
	private Integer foodMoney  ;
	private Integer goodMoney  ;
	private Integer hotelChangeShiftsId  ;
	private Integer state  ;
	private Integer classId  ;
	private Integer businessDay  ;


	public HotelChangeShiftsSales(){
	}

	public HotelChangeShiftsSales(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomMoney(Integer roomMoney) {
		this.roomMoney = roomMoney;
	}
	
	public Integer getRoomMoney() {
		return this.roomMoney;
	}
	public void setFoodMoney(Integer foodMoney) {
		this.foodMoney = foodMoney;
	}
	
	public Integer getFoodMoney() {
		return this.foodMoney;
	}
	public void setGoodMoney(Integer goodMoney) {
		this.goodMoney = goodMoney;
	}
	
	public Integer getGoodMoney() {
		return this.goodMoney;
	}
	public void setHotelChangeShiftsId(Integer hotelChangeShiftsId) {
		this.hotelChangeShiftsId = hotelChangeShiftsId;
	}
	
	public Integer getHotelChangeShiftsId() {
		return this.hotelChangeShiftsId;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setClassId(Integer classId) {
		this.classId = classId;
	}
	
	public Integer getClassId() {
		return this.classId;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}



}

