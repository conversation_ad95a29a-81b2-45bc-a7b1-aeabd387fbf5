package com.pms.czhotelfoundation.bean.room.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("RoomFeatureSearch")
public class RoomFeatureSearch extends BaseSearch {
	private Long roomFeatureId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomId;
	private String roomNum;
	private Integer initialId;
	private String code;
	private String codeType;

	public void setRoomFeatureId(Long value) {
		this.roomFeatureId = value;
	}
	
	public Long getRoomFeatureId() {
		return this.roomFeatureId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomId(Integer value) {
		this.roomId = value;
	}
	
	public Integer getRoomId() {
		return this.roomId;
	}
	public void setRoomNum(String value) {
		this.roomNum = value;
	}
	
	public String getRoomNum() {
		return this.roomNum;
	}
	public void setInitialId(Integer value) {
		this.initialId = value;
	}
	
	public Integer getInitialId() {
		return this.initialId;
	}
	public void setCode(String value) {
		this.code = value;
	}
	
	public String getCode() {
		return this.code;
	}
	public void setCodeType(String value) {
		this.codeType = value;
	}
	
	public String getCodeType() {
		return this.codeType;
	}

}

