package com.pms.czhotelfoundation.bean.code;


import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;
import java.util.Date;

public class SysResource extends BaseBean implements Serializable {

	private final static long   serialVersionUID =  123443422489023464L;

	private Integer id  ;
	private String resourceName  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer roomRateId  ;
	private Integer state  ;
	private String memo  ;
	private Date updateUserTime;


	public SysResource(){
	}

	public SysResource(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return this.id;
	}
	public void setResourceName(String resourceName) {
		this.resourceName = resourceName;
	}

	public String getResourceName() {
		return this.resourceName;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomRateId(Integer roomRateId) {
		this.roomRateId = roomRateId;
	}

	public Integer getRoomRateId() {
		return this.roomRateId;
	}
	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getState() {
		return this.state;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getMemo() {
		return this.memo;
	}

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public Date getUpdateUserTime() {
		return updateUserTime;
	}

	public void setUpdateUserTime(Date updateUserTime) {
		this.updateUserTime = updateUserTime;
	}
}

