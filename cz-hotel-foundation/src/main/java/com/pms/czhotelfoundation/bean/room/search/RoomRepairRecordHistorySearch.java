package com.pms.czhotelfoundation.bean.room.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("RoomRepairRecordHistorySearch")
public class RoomRepairRecordHistorySearch extends BaseSearch {
	private Integer repairCheckRoomRecordId;
	private Integer roomId;
	private String roomNum;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer registId;
	private String cleaningUserName;
	private Integer cleaningUserId;
	private Integer type;
	private String des;
	//房型
	private Integer roomTypeId;
	private Integer businessDay;
	private Date begintime;

	private Date minBeginTime;
	private Date maxBeginTime;

	private Date endtime;

	private Date maxEndTime;
	private Date minEndTime;


	private Integer classId;
	private Date createTime;
	private Integer createUserId;
	private String createUserName;
	private Date updateTime;
	private Integer updateUserId;
	private String updateUserName;
	private Integer state;

	private Integer oldRoomState;
	private Integer newRoomState;

	public void setRepairCheckRoomRecordId(Integer value) {
		this.repairCheckRoomRecordId = value;
	}

	public Integer getRepairCheckRoomRecordId() {
		return this.repairCheckRoomRecordId;
	}
	public void setRoomId(Integer value) {
		this.roomId = value;
	}

	public Integer getRoomId() {
		return this.roomId;
	}
	public void setRoomNum(String value) {
		this.roomNum = value;
	}

	public String getRoomNum() {
		return this.roomNum;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRegistId(Integer value) {
		this.registId = value;
	}

	public Integer getRegistId() {
		return this.registId;
	}
	public void setCleaningUserName(String value) {
		this.cleaningUserName = value;
	}

	public String getCleaningUserName() {
		return this.cleaningUserName;
	}
	public void setCleaningUserId(Integer value) {
		this.cleaningUserId = value;
	}

	public Integer getCleaningUserId() {
		return this.cleaningUserId;
	}
	public void setType(Integer value) {
		this.type = value;
	}

	public Integer getType() {
		return this.type;
	}
	public void setDes(String value) {
		this.des = value;
	}

	public String getDes() {
		return this.des;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public void setBegintime(Date value) {
		this.begintime = value;
	}

	public Date getBegintime() {
		return this.begintime;
	}

	public void setEndtime(Date value) {
		this.endtime = value;
	}

	public Date getEndtime() {
		return this.endtime;
	}
	public void setClassId(Integer value) {
		this.classId = value;
	}

	public Integer getClassId() {
		return this.classId;
	}

	public void setCreateTime(Date value) {
		this.createTime = value;
	}

	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer value) {
		this.createUserId = value;
	}

	public Integer getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}

	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer value) {
		this.updateUserId = value;
	}
	
	public Integer getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}

	public Date getMinBeginTime() {
		return minBeginTime;
	}

	public void setMinBeginTime(Date minBeginTime) {
		this.minBeginTime = minBeginTime;
	}

	public Date getMaxBeginTime() {
		return maxBeginTime;
	}

	public void setMaxBeginTime(Date maxBeginTime) {
		this.maxBeginTime = maxBeginTime;
	}

	public Date getMaxEndTime() {
		return maxEndTime;
	}

	public void setMaxEndTime(Date maxEndTime) {
		this.maxEndTime = maxEndTime;
	}

	public Date getMinEndTime() {
		return minEndTime;
	}

	public void setMinEndTime(Date minEndTime) {
		this.minEndTime = minEndTime;
	}

	public Integer getOldRoomState() {
		return oldRoomState;
	}

	public void setOldRoomState(Integer oldRoomState) {
		this.oldRoomState = oldRoomState;
	}

	public Integer getNewRoomState() {
		return newRoomState;
	}

	public void setNewRoomState(Integer newRoomState) {
		this.newRoomState = newRoomState;
	}

	public Integer getRoomTypeId() {
		return roomTypeId;
	}

	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
}

