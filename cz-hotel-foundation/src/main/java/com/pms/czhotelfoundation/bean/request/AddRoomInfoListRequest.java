package com.pms.czhotelfoundation.bean.request;

import com.pms.czhotelfoundation.bean.room.RoomFeature;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czpmsutils.request.BaseRequest;

import java.util.List;

public class AddRoomInfoListRequest extends BaseRequest {
    public List<RoomInfo> getRoomInfoList() {
        return roomInfoList;
    }

    public void setRoomInfoList(List<RoomInfo> roomInfoList) {
        this.roomInfoList = roomInfoList;
    }

    List<RoomInfo> roomInfoList;


    public List<RoomFeature> getRoomFeatures() {
        return roomFeatures;
    }

    public void setRoomFeatures(List<RoomFeature> roomFeatures) {
        this.roomFeatures = roomFeatures;
    }

    List<RoomFeature> roomFeatures;


}
