package com.pms.czhotelfoundation.bean.jjb;

import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;

public class HotelChangeShifts extends BaseBean implements Serializable{
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer cashMoney  ;
	private Integer wechatMoney  ;
	private Integer alipayMoney  ;
	private Integer cardMoney  ;
	private Integer classType  ;
	private Integer reserveMoney = 0 ;
	private Integer lastReserveMoney = 0  ;
	private Integer realReserveMoney = 0 ;
	private Integer state  ;
	//班次
	private Integer handoverClassId;
	//营业日
	private Integer handoverBusinessDay;
	private Integer classId  ;
	private Integer businessDay  ;

	public HotelChangeShifts(){
	}

	public HotelChangeShifts(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return this.id;
	}
	public void setCashMoney(Integer cashMoney) {
		this.cashMoney = cashMoney;
	}

	public Integer getCashMoney() {
		return this.cashMoney;
	}
	public void setWechatMoney(Integer wechatMoney) {
		this.wechatMoney = wechatMoney;
	}

	public Integer getWechatMoney() {
		return this.wechatMoney;
	}
	public void setAlipayMoney(Integer alipayMoney) {
		this.alipayMoney = alipayMoney;
	}

	public Integer getAlipayMoney() {
		return this.alipayMoney;
	}
	public void setCardMoney(Integer cardMoney) {
		this.cardMoney = cardMoney;
	}

	public Integer getCardMoney() {
		return this.cardMoney;
	}
	public void setReserveMoney(Integer reserveMoney) {
		this.reserveMoney = reserveMoney;
	}

	public Integer getReserveMoney() {
		return this.reserveMoney;
	}
	public void setLastReserveMoney(Integer lastReserveMoney) {
		this.lastReserveMoney = lastReserveMoney;
	}

	public Integer getLastReserveMoney() {
		return this.lastReserveMoney;
	}
	public void setRealReserveMoney(Integer realReserveMoney) {
		this.realReserveMoney = realReserveMoney;
	}

	public Integer getRealReserveMoney() {
		return this.realReserveMoney;
	}
	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getState() {
		return this.state;
	}
	public void setClassId(Integer classId) {
		this.classId = classId;
	}

	public Integer getClassId() {
		return this.classId;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public Integer getHid() {
		return hid;
	}

	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHotelGroupId() {
		return hotelGroupId;
	}

	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getClassType() {
		return classType;
	}

	public void setClassType(Integer classType) {
		this.classType = classType;
	}

	public Integer getHandoverClassId() {
		return handoverClassId;
	}

	public void setHandoverClassId(Integer handoverClassId) {
		this.handoverClassId = handoverClassId;
	}

	public Integer getHandoverBusinessDay() {
		return handoverBusinessDay;
	}

	public void setHandoverBusinessDay(Integer handoverBusinessDay) {
		this.handoverBusinessDay = handoverBusinessDay;
	}
}

