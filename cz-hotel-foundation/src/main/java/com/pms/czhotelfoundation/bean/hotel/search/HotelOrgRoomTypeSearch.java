package com.pms.czhotelfoundation.bean.hotel.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class HotelOrgRoomTypeSearch extends PageBaseRequest{
	private Integer id;
	private Integer roomTypeId;
	private Integer sort;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer hotelOrgPriceId;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setRoomTypeId(Integer value) {
		this.roomTypeId = value;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setSort(Integer value) {
		this.sort = value;
	}
	
	public Integer getSort() {
		return this.sort;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setHotelOrgPriceId(Integer value) {
		this.hotelOrgPriceId = value;
	}
	
	public Integer getHotelOrgPriceId() {
		return this.hotelOrgPriceId;
	}

}

