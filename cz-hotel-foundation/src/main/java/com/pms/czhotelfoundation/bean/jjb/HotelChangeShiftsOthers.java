package com.pms.czhotelfoundation.bean.jjb;

import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;

public class HotelChangeShiftsOthers extends BaseBean implements Serializable{
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer cashMoney  ;
	private Integer wechatMoney  ;
	private Integer alipayMoney  ;
	private Integer cardMoney  ;
	private Integer otherMoney  ;
	private Integer sumNum  ;
	private Integer type  ;
	private Integer hotelChangeShiftsId  ;
	private Integer state  ;
	private Integer classId  ;
	private Integer businessDay  ;


	public HotelChangeShiftsOthers(){
	}

	public HotelChangeShiftsOthers(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setCashMoney(Integer cashMoney) {
		this.cashMoney = cashMoney;
	}
	
	public Integer getCashMoney() {
		return this.cashMoney;
	}
	public void setWechatMoney(Integer wechatMoney) {
		this.wechatMoney = wechatMoney;
	}
	
	public Integer getWechatMoney() {
		return this.wechatMoney;
	}
	public void setAlipayMoney(Integer alipayMoney) {
		this.alipayMoney = alipayMoney;
	}
	
	public Integer getAlipayMoney() {
		return this.alipayMoney;
	}
	public void setCardMoney(Integer cardMoney) {
		this.cardMoney = cardMoney;
	}
	
	public Integer getCardMoney() {
		return this.cardMoney;
	}
	public void setOtherMoney(Integer otherMoney) {
		this.otherMoney = otherMoney;
	}
	
	public Integer getOtherMoney() {
		return this.otherMoney;
	}
	public void setSumNum(Integer sumNum) {
		this.sumNum = sumNum;
	}
	
	public Integer getSumNum() {
		return this.sumNum;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	
	public Integer getType() {
		return this.type;
	}
	public void setHotelChangeShiftsId(Integer hotelChangeShiftsId) {
		this.hotelChangeShiftsId = hotelChangeShiftsId;
	}
	
	public Integer getHotelChangeShiftsId() {
		return this.hotelChangeShiftsId;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setClassId(Integer classId) {
		this.classId = classId;
	}
	
	public Integer getClassId() {
		return this.classId;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}


}

