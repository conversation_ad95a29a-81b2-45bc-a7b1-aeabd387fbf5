package com.pms.czhotelfoundation.bean.code.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("SysResourceSearch")
public class SysResourceSearch extends BaseSearch {
	private Integer id;
	private String resourceName;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomRateId;
	private Integer state;
	private String memo;
	private String createUserId;
	private String createUserName;
	private java.util.Date createTime;
	private String updateUserId;
	private String updateUserName;
	private java.util.Date updateUserTime;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setResourceName(String value) {
		this.resourceName = value;
	}
	
	public String getResourceName() {
		return this.resourceName;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomRateId(Integer value) {
		this.roomRateId = value;
	}
	
	public Integer getRoomRateId() {
		return this.roomRateId;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setMemo(String value) {
		this.memo = value;
	}
	
	public String getMemo() {
		return this.memo;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

	public void setUpdateUserTime(java.util.Date value) {
		this.updateUserTime = value;
	}
	
	public java.util.Date getUpdateUserTime() {
		return this.updateUserTime;
	}

}

