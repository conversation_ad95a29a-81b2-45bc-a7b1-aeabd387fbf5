package com.pms.czhotelfoundation.bean.price.search;

import com.pms.czpmsutils.request.BaseRequest;
import org.apache.ibatis.type.Alias;

@<PERSON>as("RoomDayPriceSearch")
public class RoomDayPriceSearch extends BaseRequest {
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomTypeId;
	private Integer dayTime;
	private Integer dayTimeMin;
	private Integer dayTimeMax;
	private Integer roomRateId;
	private Integer weekDay;
	private Integer price;
	private java.util.Date createTime;
	private String createUserId;
	private java.util.Date updateTime;
	private String updateUserId;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomTypeId(Integer value) {
		this.roomTypeId = value;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setDayTime(Integer value) {
		this.dayTime = value;
	}
	
	public Integer getDayTime() {
		return this.dayTime;
	}
	public void setRoomRateId(Integer value) {
		this.roomRateId = value;
	}
	
	public Integer getRoomRateId() {
		return this.roomRateId;
	}
	public void setWeekDay(Integer value) {
		this.weekDay = value;
	}
	
	public Integer getWeekDay() {
		return this.weekDay;
	}
	public void setPrice(Integer value) {
		this.price = value;
	}
	
	public Integer getPrice() {
		return this.price;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}

	public Integer getDayTimeMin() {
		return dayTimeMin;
	}

	public void setDayTimeMin(Integer dayTimeMin) {
		this.dayTimeMin = dayTimeMin;
	}

	public Integer getDayTimeMax() {
		return dayTimeMax;
	}

	public void setDayTimeMax(Integer dayTimeMax) {
		this.dayTimeMax = dayTimeMax;
	}
}

