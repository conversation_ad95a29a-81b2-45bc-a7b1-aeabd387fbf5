package com.pms.czhotelfoundation.bean.jjb;

import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;

public class HotelChangeShiftsPays extends BaseBean implements Serializable{
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer cashMoney  ;
	private Integer cardMoney  ;
	private Integer wechatMoney  ;
	private Integer alipayMoney  ;
	private Integer vipMoney  ;
	private Integer arMoney  ;
	private Integer couponMoney  ;
	private Integer insideMoney  ;
	private Integer thirdMoney  ;
	private Integer creditMoney  ;
	private Integer remnantMoney  ;
	private Integer hotelChangeShiftsId  ;
	private Integer state  ;
	private Integer classId  ;
	private Integer businessDay  ;


	public HotelChangeShiftsPays(){
	}

	public HotelChangeShiftsPays(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setCashMoney(Integer cashMoney) {
		this.cashMoney = cashMoney;
	}
	
	public Integer getCashMoney() {
		return this.cashMoney;
	}
	public void setCardMoney(Integer cardMoney) {
		this.cardMoney = cardMoney;
	}
	
	public Integer getCardMoney() {
		return this.cardMoney;
	}
	public void setWechatMoney(Integer wechatMoney) {
		this.wechatMoney = wechatMoney;
	}
	
	public Integer getWechatMoney() {
		return this.wechatMoney;
	}
	public void setAlipayMoney(Integer alipayMoney) {
		this.alipayMoney = alipayMoney;
	}
	
	public Integer getAlipayMoney() {
		return this.alipayMoney;
	}
	public void setVipMoney(Integer vipMoney) {
		this.vipMoney = vipMoney;
	}
	
	public Integer getVipMoney() {
		return this.vipMoney;
	}
	public void setArMoney(Integer arMoney) {
		this.arMoney = arMoney;
	}
	
	public Integer getArMoney() {
		return this.arMoney;
	}
	public void setCouponMoney(Integer couponMoney) {
		this.couponMoney = couponMoney;
	}
	
	public Integer getCouponMoney() {
		return this.couponMoney;
	}
	public void setInsideMoney(Integer insideMoney) {
		this.insideMoney = insideMoney;
	}
	
	public Integer getInsideMoney() {
		return this.insideMoney;
	}
	public void setThirdMoney(Integer thirdMoney) {
		this.thirdMoney = thirdMoney;
	}
	
	public Integer getThirdMoney() {
		return this.thirdMoney;
	}
	public void setCreditMoney(Integer creditMoney) {
		this.creditMoney = creditMoney;
	}
	
	public Integer getCreditMoney() {
		return this.creditMoney;
	}
	public void setRemnantMoney(Integer remnantMoney) {
		this.remnantMoney = remnantMoney;
	}
	
	public Integer getRemnantMoney() {
		return this.remnantMoney;
	}
	public void setHotelChangeShiftsId(Integer hotelChangeShiftsId) {
		this.hotelChangeShiftsId = hotelChangeShiftsId;
	}
	
	public Integer getHotelChangeShiftsId() {
		return this.hotelChangeShiftsId;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setClassId(Integer classId) {
		this.classId = classId;
	}
	
	public Integer getClassId() {
		return this.classId;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}


}

