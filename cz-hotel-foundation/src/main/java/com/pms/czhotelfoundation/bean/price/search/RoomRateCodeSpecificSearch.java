package com.pms.czhotelfoundation.bean.price.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("RoomRateCodeSpecificSearch")
public class RoomRateCodeSpecificSearch extends BaseSearch {
	private Integer rateCodeSpecificId;
	private Integer rateId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomTypeId;
	private Double discount;
	private Integer hourRoomDiscount;
	//超时每小时加收多少钱
	private Integer overtimeAddMoney;
	private Integer breakfastNum;
	private Integer parkingNum;
	private String addHalfDay  ;
	private String addAllDay  ;
	private String leavingTime;
	private String keepingTime;
	private String roomTypeName;
	private Integer roundOffType;
	private Integer canDelete;
	private Integer rateState;
	private java.util.Date createTime;
	private String createUserId;
	private java.util.Date updateTime;
	private String updateUserId;

	public void setRateCodeSpecificId(Integer value) {
		this.rateCodeSpecificId = value;
	}
	
	public Integer getRateCodeSpecificId() {
		return this.rateCodeSpecificId;
	}
	public void setRateId(Integer value) {
		this.rateId = value;
	}
	
	public Integer getRateId() {
		return this.rateId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomTypeId(Integer value) {
		this.roomTypeId = value;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setDiscount(Double value) {
		this.discount = value;
	}
	
	public Double getDiscount() {
		return this.discount;
	}
	public void setHourRoomDiscount(Integer value) {
		this.hourRoomDiscount = value;
	}
	
	public Integer getHourRoomDiscount() {
		return this.hourRoomDiscount;
	}
	public void setBreakfastNum(Integer value) {
		this.breakfastNum = value;
	}
	
	public Integer getBreakfastNum() {
		return this.breakfastNum;
	}
	public void setParkingNum(Integer value) {
		this.parkingNum = value;
	}
	
	public Integer getParkingNum() {
		return this.parkingNum;
	}
	public void setLeavingTime(String value) {
		this.leavingTime = value;
	}
	
	public String getLeavingTime() {
		return this.leavingTime;
	}
	public void setKeepingTime(String value) {
		this.keepingTime = value;
	}
	
	public String getKeepingTime() {
		return this.keepingTime;
	}
	public void setRoomTypeName(String value) {
		this.roomTypeName = value;
	}
	
	public String getRoomTypeName() {
		return this.roomTypeName;
	}
	public void setRoundOffType(Integer value) {
		this.roundOffType = value;
	}
	
	public Integer getRoundOffType() {
		return this.roundOffType;
	}
	public void setCanDelete(Integer value) {
		this.canDelete = value;
	}
	
	public Integer getCanDelete() {
		return this.canDelete;
	}
	public void setRateState(Integer value) {
		this.rateState = value;
	}
	
	public Integer getRateState() {
		return this.rateState;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}

	public String getAddHalfDay() {
		return addHalfDay;
	}

	public void setAddHalfDay(String addHalfDay) {
		this.addHalfDay = addHalfDay;
	}

	public String getAddAllDay() {
		return addAllDay;
	}

	public void setAddAllDay(String addAllDay) {
		this.addAllDay = addAllDay;
	}

	public Integer getOvertimeAddMoney() {
		return overtimeAddMoney;
	}

	public void setOvertimeAddMoney(Integer overtimeAddMoney) {
		this.overtimeAddMoney = overtimeAddMoney;
	}
}

