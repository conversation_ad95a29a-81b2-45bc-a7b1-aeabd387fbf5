package com.pms.czhotelfoundation.bean.hotel.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("HotelClassInfoSearch")
public class HotelClassInfoSearch extends BaseSearch {
	private Integer hotelClassId;
	private Integer hotelClassType;
	private Integer pettyCash;
	private Integer lastCash;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer state;
	private java.util.Date createTime;
	private String createUserId;
	private String createUserName;
	private java.util.Date updateTime;
	private String updateUserId;
	private String updateUserName;

	public void setHotelClassId(Integer value) {
		this.hotelClassId = value;
	}
	
	public Integer getHotelClassId() {
		return this.hotelClassId;
	}
	public void setHotelClassType(Integer value) {
		this.hotelClassType = value;
	}
	
	public Integer getHotelClassType() {
		return this.hotelClassType;
	}
	public void setPettyCash(Integer value) {
		this.pettyCash = value;
	}
	
	public Integer getPettyCash() {
		return this.pettyCash;
	}
	public void setLastCash(Integer value) {
		this.lastCash = value;
	}
	
	public Integer getLastCash() {
		return this.lastCash;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

}

