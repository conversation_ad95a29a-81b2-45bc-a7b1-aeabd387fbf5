package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;
import java.util.Date;

public class HotelPmsapiMsg implements Serializable {
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Date startTime  ;
	private Date endTime  ;
	private Integer state  ;
	private String pmsPeople  ;
	private String pmsPhone  ;
	private Integer pmsId  ;
	private String pmsName  ;
	private String pmsMsg  ;
	private String pmsUnit  ;
	private Integer pmsMoney  ;
	private String remark  ;
	private Date updateTime  ;
	private String updateUserId  ;
	private String updateUserName  ;

	public HotelPmsapiMsg(){
	}

	public HotelPmsapiMsg(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getStartTime() {
		return this.startTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Date getEndTime() {
		return this.endTime;
	}
	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getState() {
		return this.state;
	}
	public void setPmsPeople(String pmsPeople) {
		this.pmsPeople = pmsPeople;
	}

	public String getPmsPeople() {
		return this.pmsPeople;
	}
	public void setPmsPhone(String pmsPhone) {
		this.pmsPhone = pmsPhone;
	}

	public String getPmsPhone() {
		return this.pmsPhone;
	}
	public void setPmsId(Integer pmsId) {
		this.pmsId = pmsId;
	}

	public Integer getPmsId() {
		return this.pmsId;
	}
	public void setPmsName(String pmsName) {
		this.pmsName = pmsName;
	}

	public String getPmsName() {
		return this.pmsName;
	}
	public void setPmsMsg(String pmsMsg) {
		this.pmsMsg = pmsMsg;
	}

	public String getPmsMsg() {
		return this.pmsMsg;
	}
	public void setPmsUnit(String pmsUnit) {
		this.pmsUnit = pmsUnit;
	}

	public String getPmsUnit() {
		return this.pmsUnit;
	}
	public void setPmsMoney(Integer pmsMoney) {
		this.pmsMoney = pmsMoney;
	}

	public Integer getPmsMoney() {
		return this.pmsMoney;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

}

