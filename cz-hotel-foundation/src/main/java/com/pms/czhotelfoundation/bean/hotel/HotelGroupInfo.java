package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;
import java.util.Date;

public class HotelGroupInfo implements Serializable{
	private Integer chainId  ;
	private String chainName  ;
	private String leader  ;
	private String chainDesc  ;
	private String chainDescEn  ;
	private String telephone  ;
	private String addr  ;
	private String phone  ;
	private Integer enable  ;
	private Date createTime  ;
	private String createUserId  ;
	private Date updateTime  ;
	private String updateUserId  ;

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	private String uuid;

	public HotelGroupInfo(){
	}

	public HotelGroupInfo(Integer chainId){
		this.chainId = chainId;
	}

	public void setChainId(Integer chainId) {
		this.chainId = chainId;
	}

	public Integer getChainId() {
		return this.chainId;
	}
	public void setChainName(String chainName) {
		this.chainName = chainName;
	}

	public String getChainName() {
		return this.chainName;
	}
	public void setLeader(String leader) {
		this.leader = leader;
	}

	public String getLeader() {
		return this.leader;
	}
	public void setChainDesc(String chainDesc) {
		this.chainDesc = chainDesc;
	}

	public String getChainDesc() {
		return this.chainDesc;
	}
	public void setChainDescEn(String chainDescEn) {
		this.chainDescEn = chainDescEn;
	}

	public String getChainDescEn() {
		return this.chainDescEn;
	}
	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getTelephone() {
		return this.telephone;
	}
	public void setAddr(String addr) {
		this.addr = addr;
	}

	public String getAddr() {
		return this.addr;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getPhone() {
		return this.phone;
	}
	public void setEnable(Integer enable) {
		this.enable = enable;
	}

	public Integer getEnable() {
		return this.enable;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	public String getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}

}

