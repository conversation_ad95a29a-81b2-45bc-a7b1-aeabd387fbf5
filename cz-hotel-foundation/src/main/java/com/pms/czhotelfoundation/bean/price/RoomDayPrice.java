package com.pms.czhotelfoundation.bean.price;

import java.io.Serializable;
import java.util.Date;

public class RoomDayPrice implements Serializable{
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer roomTypeId  ;
	private Integer dayTime  ;
	private Integer roomRateId  ;
	private Integer weekDay  ;
	private Integer price  ;
	private Date createTime  ;
	private String createUserId  ;
	private Date updateTime  ;
	private String updateUserId  ;
	private Integer customPrice ;

	public Integer getCustomPrice() {
		return customPrice;
	}

	public void setCustomPrice(Integer customPrice) {
		this.customPrice = customPrice;
	}

	public RoomDayPrice(){
	}

	public RoomDayPrice(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setDayTime(Integer dayTime) {
		this.dayTime = dayTime;
	}
	
	public Integer getDayTime() {
		return this.dayTime;
	}
	public void setRoomRateId(Integer roomRateId) {
		this.roomRateId = roomRateId;
	}
	
	public Integer getRoomRateId() {
		return this.roomRateId;
	}
	public void setWeekDay(Integer weekDay) {
		this.weekDay = weekDay;
	}
	
	public Integer getWeekDay() {
		return this.weekDay;
	}
	public void setPrice(Integer price) {
		this.price = price;
	}
	
	public Integer getPrice() {
		return this.price;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}

}

