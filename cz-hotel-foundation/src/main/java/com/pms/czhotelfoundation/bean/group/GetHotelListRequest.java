package com.pms.czhotelfoundation.bean.group;

import com.pms.czpmsutils.request.BaseRequest;

public class GetHotelListRequest extends BaseRequest {
    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    public Integer getStar() {
        return star;
    }

    public void setStar(Integer star) {
        this.star = star;
    }

    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }

    public Integer getArea() {
        return area;
    }

    public void setArea(Integer area) {
        this.area = area;
    }

    public Integer getProvice() {
        return provice;
    }

    public void setProvice(Integer provice) {
        this.provice = provice;
    }

    public Integer getCountry() {
        return country;
    }

    public void setCountry(Integer country) {
        this.country = country;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public Integer getHotelType() {
        return hotelType;
    }

    public void setHotelType(Integer hotelType) {
        this.hotelType = hotelType;
    }

    public String getAttention() {
        return attention;
    }

    public void setAttention(String attention) {
        this.attention = attention;
    }

    public Integer getEnvironmentalState() {
        return environmentalState;
    }

    public void setEnvironmentalState(Integer environmentalState) {
        this.environmentalState = environmentalState;
    }

    public Integer getPmsType() {
        return pmsType;
    }

    public void setPmsType(Integer pmsType) {
        this.pmsType = pmsType;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getHotelStatus() {
        return hotelStatus;
    }

    public void setHotelStatus(Integer hotelStatus) {
        this.hotelStatus = hotelStatus;
    }

    public Integer getIsChain() {
        return isChain;
    }

    public void setIsChain(Integer isChain) {
        this.isChain = isChain;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    private String hotelName;
    private Integer star;
    private Integer city;
    private Integer area;
    private Integer provice;
    private Integer country;
    private String telephone;
    private String fax;
    private String contact;
    private String contactPhone;
    private Integer hotelType;
    private String attention;
    private Integer environmentalState;
    private Integer pmsType;
    private String email;
    private Integer hotelStatus;
    private Integer isChain;
    private String openId;
}
