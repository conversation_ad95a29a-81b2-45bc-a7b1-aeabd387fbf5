package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;
import java.util.Date;

public class HotelPoliceMsg implements Serializable {
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Date startTime  ;
	private Date endTime  ;
	private Integer state  ;
	private String policePeople;
	private String policePhone;
	private Integer policeId  ;
	private String policeName  ;
	private String policeMsg  ;
	private String policeUnit  ;
	private Integer policeMoney  ;
	private String remark  ;
	private Date updateTime  ;
	private String updateUserId  ;
	private String updateUserName  ;

	public HotelPoliceMsg(){
	}

	public HotelPoliceMsg(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getStartTime() {
		return this.startTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Date getEndTime() {
		return this.endTime;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setPoliceId(Integer policeId) {
		this.policeId = policeId;
	}
	
	public Integer getPoliceId() {
		return this.policeId;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	
	public String getPoliceName() {
		return this.policeName;
	}
	public void setPoliceMsg(String policeMsg) {
		this.policeMsg = policeMsg;
	}
	
	public String getPoliceMsg() {
		return this.policeMsg;
	}
	public void setPoliceUnit(String policeUnit) {
		this.policeUnit = policeUnit;
	}
	
	public String getPoliceUnit() {
		return this.policeUnit;
	}
	public void setPoliceMoney(Integer policeMoney) {
		this.policeMoney = policeMoney;
	}
	
	public Integer getPoliceMoney() {
		return this.policeMoney;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getRemark() {
		return this.remark;
	}

	public String getPolicePeople() {
		return policePeople;
	}

	public void setPolicePeople(String policePeople) {
		this.policePeople = policePeople;
	}

	public String getPolicePhone() {
		return policePhone;
	}

	public void setPolicePhone(String policePhone) {
		this.policePhone = policePhone;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
}

