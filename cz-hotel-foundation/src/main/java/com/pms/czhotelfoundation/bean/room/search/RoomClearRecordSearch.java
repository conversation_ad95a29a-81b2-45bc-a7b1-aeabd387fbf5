package com.pms.czhotelfoundation.bean.room.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class RoomClearRecordSearch extends PageBaseRequest{
	private Integer clearRecordId;
	private Integer bookingId;
	private Integer registId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomInfoId;
	private String roomNum;
	private Integer roomTypeId;
	private Integer requestStatus;
	private java.util.Date createTime;
	private String createUserId;
	private String createUserName;
	private java.util.Date updateTime;
	private String updateUserId;
	private String updateUserName;
	private java.util.Date startTime;
	private java.util.Date endTime;
	private String clearUserId;
	private String clearUserName;
	private Integer checkAndClearId;

	public void setClearRecordId(Integer value) {
		this.clearRecordId = value;
	}
	
	public Integer getClearRecordId() {
		return this.clearRecordId;
	}
	public void setBookingId(Integer value) {
		this.bookingId = value;
	}
	
	public Integer getBookingId() {
		return this.bookingId;
	}
	public void setRegistId(Integer value) {
		this.registId = value;
	}
	
	public Integer getRegistId() {
		return this.registId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomInfoId(Integer value) {
		this.roomInfoId = value;
	}
	
	public Integer getRoomInfoId() {
		return this.roomInfoId;
	}
	public void setRoomNum(String value) {
		this.roomNum = value;
	}
	
	public String getRoomNum() {
		return this.roomNum;
	}
	public void setRoomTypeId(Integer value) {
		this.roomTypeId = value;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setRequestStatus(Integer value) {
		this.requestStatus = value;
	}
	
	public Integer getRequestStatus() {
		return this.requestStatus;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

	public void setStartTime(java.util.Date value) {
		this.startTime = value;
	}
	
	public java.util.Date getStartTime() {
		return this.startTime;
	}

	public void setEndTime(java.util.Date value) {
		this.endTime = value;
	}
	
	public java.util.Date getEndTime() {
		return this.endTime;
	}
	public void setClearUserId(String value) {
		this.clearUserId = value;
	}
	
	public String getClearUserId() {
		return this.clearUserId;
	}
	public void setClearUserName(String value) {
		this.clearUserName = value;
	}
	
	public String getClearUserName() {
		return this.clearUserName;
	}
	public void setCheckAndClearId(Integer value) {
		this.checkAndClearId = value;
	}
	
	public Integer getCheckAndClearId() {
		return this.checkAndClearId;
	}

}

