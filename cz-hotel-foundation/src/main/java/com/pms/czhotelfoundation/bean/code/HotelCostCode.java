package com.pms.czhotelfoundation.bean.code;


import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;

public class HotelCostCode extends BaseBean implements Serializable {
	private Integer costId  ;
	private String costName  ;
	private String costNameEn  ;
	private String costNum  ;
	private Integer parentId  ;
	private Integer hid  ;
	private Integer systemCod  ;
	private Integer enable  ;
	private Integer costType  ;
	private Integer accountType  ;
	private Integer moneyType;
	private Integer hotelGroupId  ;


	public HotelCostCode(){
	}

	public HotelCostCode(Integer costId){
		this.costId = costId;
	}

	public void setCostId(Integer costId) {
		this.costId = costId;
	}
	
	public Integer getCostId() {
		return this.costId;
	}
	public void setCostName(String costName) {
		this.costName = costName;
	}
	
	public String getCostName() {
		return this.costName;
	}
	public void setCostNameEn(String costNameEn) {
		this.costNameEn = costNameEn;
	}
	
	public String getCostNameEn() {
		return this.costNameEn;
	}
	public void setCostNum(String costNum) {
		this.costNum = costNum;
	}
	
	public String getCostNum() {
		return this.costNum;
	}
	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}
	
	public Integer getParentId() {
		return this.parentId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setSystemCod(Integer systemCod) {
		this.systemCod = systemCod;
	}
	
	public Integer getSystemCod() {
		return this.systemCod;
	}
	public void setEnable(Integer enable) {
		this.enable = enable;
	}
	
	public Integer getEnable() {
		return this.enable;
	}
	public void setCostType(Integer costType) {
		this.costType = costType;
	}
	
	public Integer getCostType() {
		return this.costType;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}



	public Integer getAccountType() {
		return accountType;
	}

	public void setAccountType(Integer accountType) {
		this.accountType = accountType;
	}

	public Integer getMoneyType() {
		return moneyType;
	}

	public void setMoneyType(Integer moneyType) {
		this.moneyType = moneyType;
	}
}

