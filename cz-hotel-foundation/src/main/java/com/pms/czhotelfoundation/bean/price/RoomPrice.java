package com.pms.czhotelfoundation.bean.price;


import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;


public class RoomPrice extends BaseBean implements Serializable {
	private Integer priceId  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer roomTypeId  ;
	private Date dayTime = new Date() ;
	private Integer weekDay  ;
	private Integer price  ;
	private Integer day;
	private Integer month;
	private Integer year;

	DateFormat df = new SimpleDateFormat("yyyy-MM-dd");

	public RoomPrice(){
	}

	public RoomPrice(Integer priceId){
		this.priceId = priceId;
	}

	public void setPriceId(Integer priceId) {
		this.priceId = priceId;
	}
	
	public Integer getPriceId() {
		return this.priceId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}

	public void setDayTime(Date dayTime) {
		this.dayTime = dayTime;
	}
	
	public Date getDayTime() {
		return this.dayTime;
	}


	public String getDayTimeStr() {
		return df.format(this.dayTime);
	}

	public void setWeekDay(Integer weekDay) {
		this.weekDay = weekDay;
	}
	
	public Integer getWeekDay() {
		return this.weekDay;
	}
	public void setPrice(Integer price) {
		this.price = price;
	}
	
	public Integer getPrice() {
		return this.price;
	}



	public Integer getDay() {
		return day;
	}

	public void setDay(Integer day) {
		this.day = day;
	}

	public Integer getMonth() {
		return month;
	}

	public void setMonth(Integer month) {
		this.month = month;
	}

	public Integer getYear() {
		return year;
	}

	public void setYear(Integer year) {
		this.year = year;
	}
}

