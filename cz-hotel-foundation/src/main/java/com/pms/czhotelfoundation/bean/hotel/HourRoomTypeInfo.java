package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;

public class HourRoomTypeInfo implements Serializable{
	private Integer hourRoomTypeInfoId  ;
	private Integer hourRoomId  ;
	private Integer roomTypeId  ;
	private String roomTypeName  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;

	public HourRoomTypeInfo(){
	}

	public HourRoomTypeInfo(Integer hourRoomTypeInfoId){
		this.hourRoomTypeInfoId = hourRoomTypeInfoId;
	}

	public void setHourRoomId(Integer hourRoomId) {
		this.hourRoomId = hourRoomId;
	}

	public Integer getHourRoomId() {
		return this.hourRoomId;
	}
	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}

	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setRoomTypeName(String roomTypeName) {
		this.roomTypeName = roomTypeName;
	}

	public String getRoomTypeName() {
		return this.roomTypeName;
	}

	public Integer getHourRoomTypeInfoId() {
		return hourRoomTypeInfoId;
	}

	public void setHourRoomTypeInfoId(Integer hourRoomTypeInfoId) {
		this.hourRoomTypeInfoId = hourRoomTypeInfoId;
	}

	public Integer getHid() {
		return hid;
	}

	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHotelGroupId() {
		return hotelGroupId;
	}

	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
}

