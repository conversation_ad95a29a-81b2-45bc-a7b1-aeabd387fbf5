package com.pms.czhotelfoundation.bean.price;

import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 房价方案
 * <AUTHOR>
 */
public class RoomRateCode extends BaseBean implements Serializable {
	private Integer rateId  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer rataGroupId  ;
	private Integer rateCodeType  ;
	private String rateCodeName  ;
	private String rateCode  ;
	private Integer resourceType  ;
	private String resourceName  ;
	private Integer canDelete  ;
	private Integer rateState  ;
	private Integer groupType  ;
	private Integer authorizeState  ;
	private String authorizeCode  ;

	public Integer getDiscount() {
		return discount;
	}

	public void setDiscount(Integer discount) {
		this.discount = discount;
	}

	private Integer discount;



	private Map<String,RoomRateCodeSpecific> rateByRooType = new HashMap<>();

	private List<RoomRateCodeSpecific> roomRateCodeSpecifics = new ArrayList<>();

	public RoomRateCode(){
	}

	public RoomRateCode(Integer rateId){
		this.rateId = rateId;
	}

	public void setRateId(Integer rateId) {
		this.rateId = rateId;
	}

	public Integer getRateId() {
		return this.rateId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}

	public String getHidStr() {
		return this.hid+"";
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRateCodeType(Integer rateCodeType) {
		this.rateCodeType = rateCodeType;
	}

	public Integer getRateCodeType() {
		return this.rateCodeType;
	}
	public void setRateCodeName(String rateCodeName) {
		this.rateCodeName = rateCodeName;
	}

	public String getRateCodeName() {
		return this.rateCodeName;
	}
	public void setRateCode(String rateCode) {
		this.rateCode = rateCode;
	}

	public String getRateCode() {
		return this.rateCode;
	}
	public void setResourceType(Integer resourceType) {
		this.resourceType = resourceType;
	}

	public Integer getResourceType() {
		return this.resourceType;
	}
	public void setResourceName(String resourceName) {
		this.resourceName = resourceName;
	}

	public String getResourceName() {
		return this.resourceName;
	}
	public void setCanDelete(Integer canDelete) {
		this.canDelete = canDelete;
	}

	public Integer getCanDelete() {
		return this.canDelete;
	}
	public void setRateState(Integer rateState) {
		this.rateState = rateState;
	}

	public Integer getRateState() {
		return this.rateState;
	}


	public List<RoomRateCodeSpecific> getRoomRateCodeSpecifics() {
		return roomRateCodeSpecifics;
	}

	public void setRoomRateCodeSpecifics(List<RoomRateCodeSpecific> roomRateCodeSpecifics) {
		this.roomRateCodeSpecifics = roomRateCodeSpecifics;
	}

	public Map<String, RoomRateCodeSpecific> getRateByRooType() {
		return rateByRooType;
	}

	public void setRateByRooType(Map<String, RoomRateCodeSpecific> rateByRooType) {
		this.rateByRooType = rateByRooType;
	}

	public Integer getAuthorizeState() {
		return authorizeState;
	}

	public void setAuthorizeState(Integer authorizeState) {
		this.authorizeState = authorizeState;
	}

	public String getAuthorizeCode() {
		return authorizeCode;
	}

	public void setAuthorizeCode(String authorizeCode) {
		this.authorizeCode = authorizeCode;
	}

	public Integer getRataGroupId() {
		return rataGroupId;
	}

	public void setRataGroupId(Integer rataGroupId) {
		this.rataGroupId = rataGroupId;
	}

	public Integer getGroupType() {
		return groupType;
	}

	public void setGroupType(Integer groupType) {
		this.groupType = groupType;
	}


}

