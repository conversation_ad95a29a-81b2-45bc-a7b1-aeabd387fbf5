package com.pms.czhotelfoundation.bean.room.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("RoomTypeImageSearch")
public class RoomTypeImageSearch extends BaseSearch {
	private Integer imageId;
	private Integer hid;
	private Integer roomTypeId;
	private String images;
	private Integer state;
	private Integer fileId;
	private String url;

	public void setImageId(Integer value) {
		this.imageId = value;
	}
	
	public Integer getImageId() {
		return this.imageId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setRoomTypeId(Integer value) {
		this.roomTypeId = value;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setImages(String value) {
		this.images = value;
	}
	
	public String getImages() {
		return this.images;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}

	public Integer getFileId() {
		return fileId;
	}

	public void setFileId(Integer fileId) {
		this.fileId = fileId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}
}

