package com.pms.czhotelfoundation.bean.hotel.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class HotelMiniproSettingSearch extends PageBaseRequest{
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer tabO;
	private Integer tabW;
	private Integer tabT;
	private Integer tabF;
	private Integer tabFi;
	private Integer tabSi;
	private Integer tabSe;
	private Integer tabE;
	private Integer tabN;
	private Integer tabTe;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setTabO(Integer value) {
		this.tabO = value;
	}
	
	public Integer getTabO() {
		return this.tabO;
	}
	public void setTabW(Integer value) {
		this.tabW = value;
	}
	
	public Integer getTabW() {
		return this.tabW;
	}
	public void setTabT(Integer value) {
		this.tabT = value;
	}
	
	public Integer getTabT() {
		return this.tabT;
	}
	public void setTabF(Integer value) {
		this.tabF = value;
	}
	
	public Integer getTabF() {
		return this.tabF;
	}
	public void setTabFi(Integer value) {
		this.tabFi = value;
	}
	
	public Integer getTabFi() {
		return this.tabFi;
	}
	public void setTabSi(Integer value) {
		this.tabSi = value;
	}
	
	public Integer getTabSi() {
		return this.tabSi;
	}
	public void setTabSe(Integer value) {
		this.tabSe = value;
	}
	
	public Integer getTabSe() {
		return this.tabSe;
	}
	public void setTabE(Integer value) {
		this.tabE = value;
	}
	
	public Integer getTabE() {
		return this.tabE;
	}
	public void setTabN(Integer value) {
		this.tabN = value;
	}
	
	public Integer getTabN() {
		return this.tabN;
	}
	public void setTabTe(Integer value) {
		this.tabTe = value;
	}
	
	public Integer getTabTe() {
		return this.tabTe;
	}

}

