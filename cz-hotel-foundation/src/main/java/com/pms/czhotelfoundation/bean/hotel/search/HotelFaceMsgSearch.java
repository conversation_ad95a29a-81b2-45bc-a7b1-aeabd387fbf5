package com.pms.czhotelfoundation.bean.hotel.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("HotelFaceMsgSearch")
public class HotelFaceMsgSearch extends BaseSearch {
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private java.util.Date startTime;
	private java.util.Date endTime;
	private Integer state;
	private String facePeople;
	private String facePhone;
	private Integer faceId;
	private String faceName;
	private String faceMsg;
	private String faceUnit;
	private Integer faceMoney;
	private String remark;
	private java.util.Date updateTime;
	private String updateUserId;
	private String updateUserName;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

	public void setStartTime(java.util.Date value) {
		this.startTime = value;
	}
	
	public java.util.Date getStartTime() {
		return this.startTime;
	}

	public void setEndTime(java.util.Date value) {
		this.endTime = value;
	}
	
	public java.util.Date getEndTime() {
		return this.endTime;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setFacePeople(String value) {
		this.facePeople = value;
	}
	
	public String getFacePeople() {
		return this.facePeople;
	}
	public void setFacePhone(String value) {
		this.facePhone = value;
	}
	
	public String getFacePhone() {
		return this.facePhone;
	}
	public void setFaceId(Integer value) {
		this.faceId = value;
	}
	
	public Integer getFaceId() {
		return this.faceId;
	}
	public void setFaceName(String value) {
		this.faceName = value;
	}
	
	public String getFaceName() {
		return this.faceName;
	}
	public void setFaceMsg(String value) {
		this.faceMsg = value;
	}
	
	public String getFaceMsg() {
		return this.faceMsg;
	}
	public void setFaceUnit(String value) {
		this.faceUnit = value;
	}
	
	public String getFaceUnit() {
		return this.faceUnit;
	}
	public void setFaceMoney(Integer value) {
		this.faceMoney = value;
	}
	
	public Integer getFaceMoney() {
		return this.faceMoney;
	}
	public void setRemark(String value) {
		this.remark = value;
	}
	
	public String getRemark() {
		return this.remark;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

}

