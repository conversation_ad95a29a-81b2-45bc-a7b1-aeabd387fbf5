package com.pms.czhotelfoundation.bean.code.search;
import com.pms.czpmsutils.request.PageBaseRequest;
import org.apache.ibatis.type.Alias;

@Alias("HotelGateSearch")
public class HotelGateSearch extends PageBaseRequest{
	private Integer id;
	private String gateName;
	private String uuid;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer confTemplateHotelId;
	private Integer robotType;
	private Integer status;
	private String remark;
	private String bk1;
	private String bk2;
	private String bk3;
	private java.util.Date createTime;
	private Integer createUserId;
	private java.util.Date updateTime;
	private Integer updateUserId;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setGateName(String value) {
		this.gateName = value;
	}
	
	public String getGateName() {
		return this.gateName;
	}
	public void setUuid(String value) {
		this.uuid = value;
	}
	
	public String getUuid() {
		return this.uuid;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setConfTemplateHotelId(Integer value) {
		this.confTemplateHotelId = value;
	}
	
	public Integer getConfTemplateHotelId() {
		return this.confTemplateHotelId;
	}
	public void setRobotType(Integer value) {
		this.robotType = value;
	}
	
	public Integer getRobotType() {
		return this.robotType;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}
	
	public Integer getStatus() {
		return this.status;
	}
	public void setRemark(String value) {
		this.remark = value;
	}
	
	public String getRemark() {
		return this.remark;
	}
	public void setBk1(String value) {
		this.bk1 = value;
	}
	
	public String getBk1() {
		return this.bk1;
	}
	public void setBk2(String value) {
		this.bk2 = value;
	}
	
	public String getBk2() {
		return this.bk2;
	}
	public void setBk3(String value) {
		this.bk3 = value;
	}
	
	public String getBk3() {
		return this.bk3;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer value) {
		this.createUserId = value;
	}
	
	public Integer getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer value) {
		this.updateUserId = value;
	}
	
	public Integer getUpdateUserId() {
		return this.updateUserId;
	}

}

