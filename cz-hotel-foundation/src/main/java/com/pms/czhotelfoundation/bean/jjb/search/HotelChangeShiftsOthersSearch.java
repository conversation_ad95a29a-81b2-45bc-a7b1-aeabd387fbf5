package com.pms.czhotelfoundation.bean.jjb.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("HotelChangeShiftsOthersSearch")
public class HotelChangeShiftsOthersSearch extends BaseSearch {
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer cashMoney;
	private Integer wechatMoney;
	private Integer alipayMoney;
	private Integer cardMoney;
	private Integer otherMoney;
	private Integer sumNum;
	private Integer type;
	private Integer hotelChangeShiftsId;
	private Integer state;
	private Integer classId;
	private Integer businessDay;
	private java.util.Date createTime;
	private String createUserId;
	private String createUserName;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setCashMoney(Integer value) {
		this.cashMoney = value;
	}
	
	public Integer getCashMoney() {
		return this.cashMoney;
	}
	public void setWechatMoney(Integer value) {
		this.wechatMoney = value;
	}
	
	public Integer getWechatMoney() {
		return this.wechatMoney;
	}
	public void setAlipayMoney(Integer value) {
		this.alipayMoney = value;
	}
	
	public Integer getAlipayMoney() {
		return this.alipayMoney;
	}
	public void setCardMoney(Integer value) {
		this.cardMoney = value;
	}
	
	public Integer getCardMoney() {
		return this.cardMoney;
	}
	public void setOtherMoney(Integer value) {
		this.otherMoney = value;
	}
	
	public Integer getOtherMoney() {
		return this.otherMoney;
	}
	public void setSumNum(Integer value) {
		this.sumNum = value;
	}
	
	public Integer getSumNum() {
		return this.sumNum;
	}
	public void setType(Integer value) {
		this.type = value;
	}
	
	public Integer getType() {
		return this.type;
	}
	public void setHotelChangeShiftsId(Integer value) {
		this.hotelChangeShiftsId = value;
	}
	
	public Integer getHotelChangeShiftsId() {
		return this.hotelChangeShiftsId;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setClassId(Integer value) {
		this.classId = value;
	}
	
	public Integer getClassId() {
		return this.classId;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

}

