package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;
import java.util.Date;

public class HotelGoodsManagement implements Serializable{
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private String code  ;
	private String name  ;
	private Integer state  ;
	private String remark  ;
	private Integer accountId  ;
	private Integer money  ;
	private String guestName  ;
	private Integer guestId  ;
	private Integer registId  ;
	private Integer bookingOrderId  ;
	private String roomNum  ;
	private Integer roomInfoId  ;
	private Integer type  ;
	private Date createTime  ;
	private Integer createUserId  ;
	private String createUserName  ;
	private Date updateTime  ;
	private Integer updateUserId  ;
	private String updateUserName  ;
	private Integer classId  ;
	private Integer accountYear  ;
	private Integer accountYearMonth  ;
	private Integer businessDay  ;

	public HotelGoodsManagement(){
	}

	public HotelGoodsManagement(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setCode(String code) {
		this.code = code;
	}
	
	public String getCode() {
		return this.code;
	}
	public void setName(String name) {
		this.name = name;
	}
	
	public String getName() {
		return this.name;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getRemark() {
		return this.remark;
	}
	public void setAccountId(Integer accountId) {
		this.accountId = accountId;
	}
	
	public Integer getAccountId() {
		return this.accountId;
	}
	public void setMoney(Integer money) {
		this.money = money;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setGuestName(String guestName) {
		this.guestName = guestName;
	}
	
	public String getGuestName() {
		return this.guestName;
	}
	public void setGuestId(Integer guestId) {
		this.guestId = guestId;
	}
	
	public Integer getGuestId() {
		return this.guestId;
	}
	public void setRegistId(Integer registId) {
		this.registId = registId;
	}
	
	public Integer getRegistId() {
		return this.registId;
	}
	public void setBookingOrderId(Integer bookingOrderId) {
		this.bookingOrderId = bookingOrderId;
	}
	
	public Integer getBookingOrderId() {
		return this.bookingOrderId;
	}
	public void setRoomNum(String roomNum) {
		this.roomNum = roomNum;
	}
	
	public String getRoomNum() {
		return this.roomNum;
	}
	public void setRoomInfoId(Integer roomInfoId) {
		this.roomInfoId = roomInfoId;
	}
	
	public Integer getRoomInfoId() {
		return this.roomInfoId;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	
	public Integer getType() {
		return this.type;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	
	public Integer getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public Integer getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}
	public void setClassId(Integer classId) {
		this.classId = classId;
	}
	
	public Integer getClassId() {
		return this.classId;
	}
	public void setAccountYear(Integer accountYear) {
		this.accountYear = accountYear;
	}
	
	public Integer getAccountYear() {
		return this.accountYear;
	}
	public void setAccountYearMonth(Integer accountYearMonth) {
		this.accountYearMonth = accountYearMonth;
	}
	
	public Integer getAccountYearMonth() {
		return this.accountYearMonth;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

}

