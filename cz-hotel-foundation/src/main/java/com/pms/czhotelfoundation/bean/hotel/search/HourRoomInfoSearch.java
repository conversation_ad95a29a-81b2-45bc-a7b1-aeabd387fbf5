package com.pms.czhotelfoundation.bean.hotel.search;

import org.apache.ibatis.type.Alias;

@<PERSON><PERSON>("HourRoomInfoSearch")
public class HourRoomInfoSearch {
	private Integer hourRoomId;
	private Integer hid;
	private Integer hotelGroupId;
	private String hourRoomName;
	private Integer hourRoomCode;
	private String startTime;
	private String endTime;
	private Integer selfmachineCanUse;
	private Integer state;
	private String des;
	private java.util.Date createTime;
	private Integer createUserId;
	private java.util.Date updateTime;
	private Integer updateUserId;
	private Integer sort;

	public void setHourRoomId(Integer value) {
		this.hourRoomId = value;
	}

	public Integer getHourRoomId() {
		return this.hourRoomId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setHourRoomName(String value) {
		this.hourRoomName = value;
	}

	public String getHourRoomName() {
		return this.hourRoomName;
	}
	public void setHourRoomCode(Integer value) {
		this.hourRoomCode = value;
	}

	public Integer getHourRoomCode() {
		return this.hourRoomCode;
	}
	public void setStartTime(String value) {
		this.startTime = value;
	}

	public String getStartTime() {
		return this.startTime;
	}
	public void setEndTime(String value) {
		this.endTime = value;
	}

	public String getEndTime() {
		return this.endTime;
	}
	public void setSelfmachineCanUse(Integer value) {
		this.selfmachineCanUse = value;
	}

	public Integer getSelfmachineCanUse() {
		return this.selfmachineCanUse;
	}
	public void setState(Integer value) {
		this.state = value;
	}

	public Integer getState() {
		return this.state;
	}
	public void setDes(String value) {
		this.des = value;
	}

	public String getDes() {
		return this.des;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}

	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer value) {
		this.createUserId = value;
	}

	public Integer getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}

	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer value) {
		this.updateUserId = value;
	}

	public Integer getUpdateUserId() {
		return this.updateUserId;
	}
	public void setSort(Integer value) {
		this.sort = value;
	}

	public Integer getSort() {
		return this.sort;
	}

}

