package com.pms.czhotelfoundation.bean.request;

import com.pms.czpmsutils.request.BaseRequest;

public class AuditApplyRequest extends BaseRequest {
    //唯一id
    private String uuid;
    //申请状态:1.待审核,2.审核已通过,3.补充信息,4.审核未通过
    private Integer status;
    //审核备注
    private String auditRemark;
    //审核原因
    private String auditReason;
    //审核人姓名
    private String auditUserName;

    private Integer hid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public String getAuditReason() {
        return auditReason;
    }

    public void setAuditReason(String auditReason) {
        this.auditReason = auditReason;
    }

    public String getAuditUserName() {
        return auditUserName;
    }

    public void setAuditUserName(String auditUserName) {
        this.auditUserName = auditUserName;
    }

    @Override
    public Integer getHid() {
        return hid;
    }

    @Override
    public void setHid(Integer hid) {
        this.hid = hid;
    }
}
