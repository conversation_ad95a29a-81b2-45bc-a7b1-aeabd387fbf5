package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;

/**
 * 酒店品牌信息
 */
public class HotelBrandInfo implements Serializable{
	private Integer id  ;
	private Integer hotelGroupId  ;
	private String brandName  ;
	private String brandNameEn  ;
	private String brandDesc  ;
	private String brandDescEn  ;
	private String telephone  ;
	private String fax  ;
	private String contact  ;
	private String contactPhone  ;
	private java.util.Date createTime  ;
	private String createUserId  ;
	private String createUserName  ;
	private java.util.Date updateTime  ;
	private String updateUserId  ;
	private String updateUserName  ;

	public HotelBrandInfo(){
	}

	public HotelBrandInfo(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}
	
	public String getBrandName() {
		return this.brandName;
	}
	public void setBrandNameEn(String brandNameEn) {
		this.brandNameEn = brandNameEn;
	}
	
	public String getBrandNameEn() {
		return this.brandNameEn;
	}
	public void setBrandDesc(String brandDesc) {
		this.brandDesc = brandDesc;
	}
	
	public String getBrandDesc() {
		return this.brandDesc;
	}
	public void setBrandDescEn(String brandDescEn) {
		this.brandDescEn = brandDescEn;
	}
	
	public String getBrandDescEn() {
		return this.brandDescEn;
	}
	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}
	
	public String getTelephone() {
		return this.telephone;
	}
	public void setFax(String fax) {
		this.fax = fax;
	}
	
	public String getFax() {
		return this.fax;
	}
	public void setContact(String contact) {
		this.contact = contact;
	}
	
	public String getContact() {
		return this.contact;
	}
	public void setContactPhone(String contactPhone) {
		this.contactPhone = contactPhone;
	}
	
	public String getContactPhone() {
		return this.contactPhone;
	}

	public void setCreateTime(java.util.Date createTime) {
		this.createTime = createTime;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(java.util.Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

}

