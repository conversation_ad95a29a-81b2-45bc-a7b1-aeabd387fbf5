package com.pms.czhotelfoundation.bean.hotel.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("HotelPoliceMsgSearch")
public class HotelPoliceMsgSearch extends BaseSearch {
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private Date startTime;
	private Date endTime;
	private Integer state;
	private String policePeople;
	private String policePhone;
	private Integer policeId;
	private String policeName;
	private String policeMsg;
	private String policeUnit;
	private Integer policeMoney;
	private String remark;
	private Date updateTime  ;
	private String updateUserId  ;
	private String updateUserName  ;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

	public void setStartTime(Date value) {
		this.startTime = value;
	}
	
	public Date getStartTime() {
		return this.startTime;
	}

	public void setEndTime(Date value) {
		this.endTime = value;
	}
	
	public Date getEndTime() {
		return this.endTime;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setPoliceId(Integer value) {
		this.policeId = value;
	}
	
	public Integer getPoliceId() {
		return this.policeId;
	}
	public void setPoliceName(String value) {
		this.policeName = value;
	}
	
	public String getPoliceName() {
		return this.policeName;
	}
	public void setPoliceMsg(String value) {
		this.policeMsg = value;
	}
	
	public String getPoliceMsg() {
		return this.policeMsg;
	}
	public void setPoliceUnit(String value) {
		this.policeUnit = value;
	}
	
	public String getPoliceUnit() {
		return this.policeUnit;
	}
	public void setPoliceMoney(Integer value) {
		this.policeMoney = value;
	}
	
	public Integer getPoliceMoney() {
		return this.policeMoney;
	}
	public void setRemark(String value) {
		this.remark = value;
	}
	
	public String getRemark() {
		return this.remark;
	}

	public String getPolicePeople() {
		return policePeople;
	}

	public void setPolicePeople(String policePeople) {
		this.policePeople = policePeople;
	}

	public String getPolicePhone() {
		return policePhone;
	}

	public void setPolicePhone(String policePhone) {
		this.policePhone = policePhone;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getUpdateUserId() {
		return updateUserId;
	}

	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}

	public String getUpdateUserName() {
		return updateUserName;
	}

	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
}

