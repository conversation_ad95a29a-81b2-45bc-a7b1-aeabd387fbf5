package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 */
public class HotelOrgPriceImage implements Serializable{
	//
	private Integer id;
	//
	private String imagePath;
	//1-横板 2-竖版
	private Integer imageType;
	//
	private String imageName;
	//集团编号
	private Integer hotelGroupId;
	//0-系统默认  1-新增的
	private Integer systemCode;
	//是否可用1,0
	private Integer state;
	//创建时间
	private Date createTime;
	//创建人
	private String createUserId;
	//修改时间
	private Date updateTime;
	//修改人
	private String updateUserId;
	//备注
	private String remark;
	//
	private Integer hid;
	//1-背景图片 2-广告轮播图
	private Integer businessType;

	public HotelOrgPriceImage(){
	}

	public HotelOrgPriceImage(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setImagePath(String imagePath) {
		this.imagePath = imagePath;
	}
	
	public String getImagePath() {
		return this.imagePath;
	}
	public void setImageType(Integer imageType) {
		this.imageType = imageType;
	}
	
	public Integer getImageType() {
		return this.imageType;
	}
	public void setImageName(String imageName) {
		this.imageName = imageName;
	}
	
	public String getImageName() {
		return this.imageName;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setSystemCode(Integer systemCode) {
		this.systemCode = systemCode;
	}
	
	public Integer getSystemCode() {
		return this.systemCode;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getRemark() {
		return this.remark;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setBusinessType(Integer businessType) {
		this.businessType = businessType;
	}
	
	public Integer getBusinessType() {
		return this.businessType;
	}

}

