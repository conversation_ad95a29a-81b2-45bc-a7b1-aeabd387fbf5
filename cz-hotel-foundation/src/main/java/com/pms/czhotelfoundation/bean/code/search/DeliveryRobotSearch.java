package com.pms.czhotelfoundation.bean.code.search;
import com.pms.czpmsutils.request.PageBaseRequest;
import org.apache.ibatis.type.Alias;

@Alias("DeliveryRobotSearch")
public class DeliveryRobotSearch extends PageBaseRequest{
	private Integer id;
	private String robotName;
	private String uuid;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer confTemplateHotelId;
	private Integer robotType;
	private Integer status;
	private String remark;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setRobotName(String value) {
		this.robotName = value;
	}
	
	public String getRobotName() {
		return this.robotName;
	}
	public void setUuid(String value) {
		this.uuid = value;
	}
	
	public String getUuid() {
		return this.uuid;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setConfTemplateHotelId(Integer value) {
		this.confTemplateHotelId = value;
	}
	
	public Integer getConfTemplateHotelId() {
		return this.confTemplateHotelId;
	}
	public void setRobotType(Integer value) {
		this.robotType = value;
	}
	
	public Integer getRobotType() {
		return this.robotType;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}
	
	public Integer getStatus() {
		return this.status;
	}
	public void setRemark(String value) {
		this.remark = value;
	}
	
	public String getRemark() {
		return this.remark;
	}

}

