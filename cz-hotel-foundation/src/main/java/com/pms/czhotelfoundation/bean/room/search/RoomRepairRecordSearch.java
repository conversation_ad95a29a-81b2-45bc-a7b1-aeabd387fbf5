package com.pms.czhotelfoundation.bean.room.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("RoomRepairRecordSearch")
public class RoomRepairRecordSearch extends BaseSearch {
	private Integer repairCheckRoomRecordId;
	private Integer roomId;
	private String roomNum;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer registId;
	private Integer type;
	//房型
	private Integer roomTypeId;
	private String des;
	private Integer businessDay;
	private java.util.Date begintime;
	private java.util.Date endtime;
	private Integer classId;
	private java.util.Date createTime;
	private Integer createUserId;
	private String createUserName;
	private java.util.Date updateTime;
	private Integer updateUserId;
	private String updateUserName;
	private Integer state;
	private Integer oldRoomState;
	private Integer newRoomState;
	private Integer cleaningUserId  ;
	private String cleaningUserName  ;

	public void setRepairCheckRoomRecordId(Integer value) {
		this.repairCheckRoomRecordId = value;
	}
	
	public Integer getRepairCheckRoomRecordId() {
		return this.repairCheckRoomRecordId;
	}
	public void setRoomId(Integer value) {
		this.roomId = value;
	}
	
	public Integer getRoomId() {
		return this.roomId;
	}
	public void setRoomNum(String value) {
		this.roomNum = value;
	}
	
	public String getRoomNum() {
		return this.roomNum;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRegistId(Integer value) {
		this.registId = value;
	}
	
	public Integer getRegistId() {
		return this.registId;
	}
	public void setType(Integer value) {
		this.type = value;
	}
	
	public Integer getType() {
		return this.type;
	}
	public void setDes(String value) {
		this.des = value;
	}
	
	public String getDes() {
		return this.des;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public void setBegintime(java.util.Date value) {
		this.begintime = value;
	}
	
	public java.util.Date getBegintime() {
		return this.begintime;
	}

	public void setEndtime(java.util.Date value) {
		this.endtime = value;
	}
	
	public java.util.Date getEndtime() {
		return this.endtime;
	}
	public void setClassId(Integer value) {
		this.classId = value;
	}
	
	public Integer getClassId() {
		return this.classId;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer value) {
		this.createUserId = value;
	}
	
	public Integer getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer value) {
		this.updateUserId = value;
	}
	
	public Integer getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}

	public void setCleaningUserId(Integer cleaningUserId){this.cleaningUserId = cleaningUserId;}
	public Integer getCleaningUserId(){return this.cleaningUserId;}

	public void setCleaningUserName(String cleaningUserName){this.cleaningUserName = cleaningUserName;}
	public String getCleaningUserName(){return this.cleaningUserName;}

	public Integer getOldRoomState() {
		return oldRoomState;
	}

	public void setOldRoomState(Integer oldRoomState) {
		this.oldRoomState = oldRoomState;
	}

	public Integer getNewRoomState() {
		return newRoomState;
	}

	public void setNewRoomState(Integer newRoomState) {
		this.newRoomState = newRoomState;
	}

	public Integer getRoomTypeId() {
		return roomTypeId;
	}

	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
}

