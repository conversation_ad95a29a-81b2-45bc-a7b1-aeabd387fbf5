package com.pms.czhotelfoundation.bean.otarep.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class RepHotelSearch extends PageBaseRequest{
	private Integer id;
	private String hotelName;
	private String hotelCode;
	private String otaHid;
	private Integer otaType;
	private Integer state;
	private Integer sumRoom;
	private Integer saleRoom;
	private Integer saleMoney;
	private java.util.Date createTime;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHotelName(String value) {
		this.hotelName = value;
	}
	
	public String getHotelName() {
		return this.hotelName;
	}
	public void setHotelCode(String value) {
		this.hotelCode = value;
	}
	
	public String getHotelCode() {
		return this.hotelCode;
	}
	public void setOtaHid(String value) {
		this.otaHid = value;
	}
	
	public String getOtaHid() {
		return this.otaHid;
	}
	public void setOtaType(Integer value) {
		this.otaType = value;
	}
	
	public Integer getOtaType() {
		return this.otaType;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setSumRoom(Integer value) {
		this.sumRoom = value;
	}
	
	public Integer getSumRoom() {
		return this.sumRoom;
	}
	public void setSaleRoom(Integer value) {
		this.saleRoom = value;
	}
	
	public Integer getSaleRoom() {
		return this.saleRoom;
	}
	public void setSaleMoney(Integer value) {
		this.saleMoney = value;
	}
	
	public Integer getSaleMoney() {
		return this.saleMoney;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}

}

