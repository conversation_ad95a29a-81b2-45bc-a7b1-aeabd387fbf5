package com.pms.czhotelfoundation.bean.hotel;


import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * @Author: 陈星宇
 * @CreateTime: 2025-05-15
 * @Description:
 */
public class HotelShiftRecord implements Serializable {
    private Integer id;

    private Integer hid  ;

    private Integer hotelGroupId  ;

    private Date createTime  ;

    private String createUserId  ;

    private Date updateTime  ;

    private String updateUserId  ;

    private String roomCardHandover;

    private String guestRelated;

    private String handoverMessage;

    private String createUserName;

    private String updateUserName;

    @Transient
    private String cardExchange;

    @Transient
    private String handoverNote;

    @Transient
    private String leaver;

    @Transient
    private String leaveTime;

    public String getCardExchange() {
        return cardExchange;
    }

    public void setCardExchange(String cardExchange) {
        this.cardExchange = cardExchange;
    }

    public String getHandoverNote() {
        return handoverNote;
    }

    public void setHandoverNote(String handoverNote) {
        this.handoverNote = handoverNote;
    }

    public String getLeaver() {
        return leaver;
    }

    public void setLeaver(String leaver) {
        this.leaver = leaver;
    }

    public String getLeaveTime() {
        return leaveTime;
    }

    public void setLeaveTime(String leaveTime) {
        this.leaveTime = leaveTime;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getHid() {
        return hid;
    }

    public void setHid(Integer hid) {
        this.hid = hid;
    }

    public Integer getHotelGroupId() {
        return hotelGroupId;
    }

    public void setHotelGroupId(Integer hotelGroupId) {
        this.hotelGroupId = hotelGroupId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    public String getRoomCardHandover() {
        return roomCardHandover;
    }

    public void setRoomCardHandover(String roomCardHandover) {
        this.roomCardHandover = roomCardHandover;
    }

    public String getGuestRelated() {
        return guestRelated;
    }

    public void setGuestRelated(String guestRelated) {
        this.guestRelated = guestRelated;
    }

    public String getHandoverMessage() {
        return handoverMessage;
    }

    public void setHandoverMessage(String handoverMessage) {
        this.handoverMessage = handoverMessage;
    }
}
