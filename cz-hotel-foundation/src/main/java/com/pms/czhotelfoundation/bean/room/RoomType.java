package com.pms.czhotelfoundation.bean.room;


import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;
import java.util.List;


/**
 * 房型
 * <AUTHOR>
 */
public class RoomType extends BaseBean implements Serializable {
	private Integer roomTypeId  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private String roomTypeName  ;
	private String roomTypeNameEn  ;
	private String shortName  ;
	private String area  ;
	private String bedtype  ;
	private Integer canOverbooking  ;
	private Integer canOverbookingNum  ;
	private Integer price  ;
	private Integer maxCheckinNum  ;
	private String des  ;
	private String desEn  ;
	private Integer state  ;
	private Integer isHourRoom  ;
	private Integer deposit  ;
	private Integer onlySelfmachineUse  ;
	private Integer selfmachinePromotionStart  ;
	private Integer selfmachinePromotionEnd  ;
	private Integer sort  ;
	private String hourRoomStartTime  ;
	private String hourRoomEndTime  ;
	private Integer discount  ;
	private Integer duration  ;

	private List<RoomTypeImage> roomTypeImages;

	public RoomType(){
	}

	public RoomType(Integer roomTypeId){
		this.roomTypeId = roomTypeId;
	}

	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomTypeName(String roomTypeName) {
		this.roomTypeName = roomTypeName;
	}
	
	public String getRoomTypeName() {
		return this.roomTypeName;
	}
	public void setRoomTypeNameEn(String roomTypeNameEn) {
		this.roomTypeNameEn = roomTypeNameEn;
	}
	
	public String getRoomTypeNameEn() {
		return this.roomTypeNameEn;
	}
	public void setShortName(String shortName) {
		this.shortName = shortName;
	}
	
	public String getShortName() {
		return this.shortName;
	}
	public void setArea(String area) {
		this.area = area;
	}
	
	public String getArea() {
		return this.area;
	}
	public void setBedtype(String bedtype) {
		this.bedtype = bedtype;
	}
	
	public String getBedtype() {
		return this.bedtype;
	}
	public void setCanOverbooking(Integer canOverbooking) {
		this.canOverbooking = canOverbooking;
	}
	
	public Integer getCanOverbooking() {
		return this.canOverbooking;
	}
	public void setCanOverbookingNum(Integer canOverbookingNum) {
		this.canOverbookingNum = canOverbookingNum;
	}
	
	public Integer getCanOverbookingNum() {
		return this.canOverbookingNum;
	}
	public void setPrice(Integer price) {
		this.price = price;
	}
	
	public Integer getPrice() {
		return this.price;
	}
	public void setMaxCheckinNum(Integer maxCheckinNum) {
		this.maxCheckinNum = maxCheckinNum;
	}
	
	public Integer getMaxCheckinNum() {
		return this.maxCheckinNum;
	}
	public void setDes(String des) {
		this.des = des;
	}
	
	public String getDes() {
		return this.des;
	}
	public void setDesEn(String desEn) {
		this.desEn = desEn;
	}
	
	public String getDesEn() {
		return this.desEn;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setIsHourRoom(Integer isHourRoom) {
		this.isHourRoom = isHourRoom;
	}
	
	public Integer getIsHourRoom() {
		return this.isHourRoom;
	}
	public void setDeposit(Integer deposit) {
		this.deposit = deposit;
	}
	
	public Integer getDeposit() {
		return this.deposit;
	}
	public void setOnlySelfmachineUse(Integer onlySelfmachineUse) {
		this.onlySelfmachineUse = onlySelfmachineUse;
	}
	
	public Integer getOnlySelfmachineUse() {
		return this.onlySelfmachineUse;
	}
	public void setSelfmachinePromotionStart(Integer selfmachinePromotionStart) {
		this.selfmachinePromotionStart = selfmachinePromotionStart;
	}
	
	public Integer getSelfmachinePromotionStart() {
		return this.selfmachinePromotionStart;
	}
	public void setSelfmachinePromotionEnd(Integer selfmachinePromotionEnd) {
		this.selfmachinePromotionEnd = selfmachinePromotionEnd;
	}
	
	public Integer getSelfmachinePromotionEnd() {
		return this.selfmachinePromotionEnd;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	
	public Integer getSort() {
		return this.sort;
	}

	public void setHourRoomStartTime(String hourRoomStartTime) {
		this.hourRoomStartTime = hourRoomStartTime;
	}
	
	public String getHourRoomStartTime() {
		return this.hourRoomStartTime;
	}
	public void setHourRoomEndTime(String hourRoomEndTime) {
		this.hourRoomEndTime = hourRoomEndTime;
	}
	
	public String getHourRoomEndTime() {
		return this.hourRoomEndTime;
	}
	public void setDiscount(Integer discount) {
		this.discount = discount;
	}
	
	public Integer getDiscount() {
		return this.discount;
	}
	public void setDuration(Integer duration) {
		this.duration = duration;
	}
	
	public Integer getDuration() {
		return this.duration;
	}

	public List<RoomTypeImage> getRoomTypeImages() {
		return roomTypeImages;
	}

	public void setRoomTypeImages(List<RoomTypeImage> roomTypeImages) {
		this.roomTypeImages = roomTypeImages;
	}
}

