package com.pms.czhotelfoundation.bean.room.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("RoomImageSearch")
public class RoomImageSearch extends BaseSearch {
	private Integer imageId;
	private Integer hid;
	private Integer roomInfoId;
	private String images;
	private Integer state;

	public void setImageId(Integer value) {
		this.imageId = value;
	}
	
	public Integer getImageId() {
		return this.imageId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setRoomInfoId(Integer value) {
		this.roomInfoId = value;
	}
	
	public Integer getRoomInfoId() {
		return this.roomInfoId;
	}
	public void setImages(String value) {
		this.images = value;
	}
	
	public String getImages() {
		return this.images;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}

}

