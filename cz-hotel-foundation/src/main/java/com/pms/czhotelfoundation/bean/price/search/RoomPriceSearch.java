package com.pms.czhotelfoundation.bean.price.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("RoomPriceSearch")
public class RoomPriceSearch extends BaseSearch {
	private Integer priceId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomTypeId;
	private Date dayTime;
	private Integer weekDay;
	private Integer price;
	private Date createTime;
	private String createUserId;
	private Date updateTime ;
	private String updateUserId;
	//最小时间
	private Date minTime;

	//最大时间
	private Date maxTime;

	public void setPriceId(Integer value) {
		this.priceId = value;
	}
	
	public Integer getPriceId() {
		return this.priceId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomTypeId(Integer value) {
		this.roomTypeId = value;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}

	public void setDayTime(Date value) {
		this.dayTime = value;
	}
	
	public Date getDayTime() {
		return this.dayTime;
	}
	public void setWeekDay(Integer value) {
		this.weekDay = value;
	}
	
	public Integer getWeekDay() {
		return this.weekDay;
	}
	public void setPrice(Integer value) {
		this.price = value;
	}
	
	public Integer getPrice() {
		return this.price;
	}

	public void setCreateTime(Date value) {
		this.createTime = value;
	}
	
	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(Date value) {
		this.updateTime = value;
	}
	
	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}


	public Date getMinTime() {
		return minTime;
	}

	public void setMinTime(Date minTime) {
		this.minTime = minTime;
	}

	public Date getMaxTime() {
		return maxTime;
	}

	public void setMaxTime(Date maxTime) {
		this.maxTime = maxTime;
	}
}

