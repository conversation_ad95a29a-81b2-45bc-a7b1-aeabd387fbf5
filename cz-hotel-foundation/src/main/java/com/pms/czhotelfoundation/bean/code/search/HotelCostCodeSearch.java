package com.pms.czhotelfoundation.bean.code.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("HotelCostCodeSearch")
public class HotelCostCodeSearch extends BaseSearch {
	private Integer costId;
	private String costName;
	private String costNameEn;
	private String costNum;
	private Integer parentId;
	private Integer hid;
	private Integer systemCod;
	private Integer enable;
	private Integer costType;
	private Integer accountType  ;
	private Integer moneyType;
	private Integer hotelGroupId;
	private java.util.Date createTime;
	private String createUserId;
	private java.util.Date updateTime;
	private String updateUserId;

	public void setCostId(Integer value) {
		this.costId = value;
	}
	
	public Integer getCostId() {
		return this.costId;
	}
	public void setCostName(String value) {
		this.costName = value;
	}
	
	public String getCostName() {
		return this.costName;
	}
	public void setCostNameEn(String value) {
		this.costNameEn = value;
	}
	
	public String getCostNameEn() {
		return this.costNameEn;
	}
	public void setCostNum(String value) {
		this.costNum = value;
	}
	
	public String getCostNum() {
		return this.costNum;
	}
	public void setParentId(Integer value) {
		this.parentId = value;
	}
	
	public Integer getParentId() {
		return this.parentId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setSystemCod(Integer value) {
		this.systemCod = value;
	}
	
	public Integer getSystemCod() {
		return this.systemCod;
	}
	public void setEnable(Integer value) {
		this.enable = value;
	}
	
	public Integer getEnable() {
		return this.enable;
	}
	public void setCostType(Integer value) {
		this.costType = value;
	}
	
	public Integer getCostType() {
		return this.costType;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}

	public Integer getAccountType() {
		return accountType;
	}

	public void setAccountType(Integer accountType) {
		this.accountType = accountType;
	}

	public Integer getMoneyType() {
		return moneyType;
	}

	public void setMoneyType(Integer moneyType) {
		this.moneyType = moneyType;
	}
}

