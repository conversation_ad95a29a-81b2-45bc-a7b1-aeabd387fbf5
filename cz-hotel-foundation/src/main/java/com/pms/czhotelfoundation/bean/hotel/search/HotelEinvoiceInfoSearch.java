package com.pms.czhotelfoundation.bean.hotel.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("HotelEinvoiceInfoSearch")
public class HotelEinvoiceInfoSearch extends BaseSearch {
	private Integer hid;
	private Integer hotelGroupId;
	private String saleTaxno;
	private String saleAddress;
	private String updateUserId;
	private java.util.Date updateTime;
	private java.util.Date createTime;
	private String createUserId;
	private String salePhone;
	private String clerk;
	private String saleAccount;
	private String payee;
	private String checker;
	private String einvoiceUrl;
	private String einvoiceCode;
	private String einvoiceKey;
	private String goodsCode;
	private String taxRate;
	private Integer id;

	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setSaleTaxno(String value) {
		this.saleTaxno = value;
	}
	
	public String getSaleTaxno() {
		return this.saleTaxno;
	}
	public void setSaleAddress(String value) {
		this.saleAddress = value;
	}
	
	public String getSaleAddress() {
		return this.saleAddress;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setSalePhone(String value) {
		this.salePhone = value;
	}
	
	public String getSalePhone() {
		return this.salePhone;
	}
	public void setClerk(String value) {
		this.clerk = value;
	}
	
	public String getClerk() {
		return this.clerk;
	}
	public void setSaleAccount(String value) {
		this.saleAccount = value;
	}
	
	public String getSaleAccount() {
		return this.saleAccount;
	}
	public void setPayee(String value) {
		this.payee = value;
	}
	
	public String getPayee() {
		return this.payee;
	}
	public void setChecker(String value) {
		this.checker = value;
	}
	
	public String getChecker() {
		return this.checker;
	}
	public void setEinvoiceUrl(String value) {
		this.einvoiceUrl = value;
	}
	
	public String getEinvoiceUrl() {
		return this.einvoiceUrl;
	}
	public void setEinvoiceCode(String value) {
		this.einvoiceCode = value;
	}
	
	public String getEinvoiceCode() {
		return this.einvoiceCode;
	}
	public void setEinvoiceKey(String value) {
		this.einvoiceKey = value;
	}
	
	public String getEinvoiceKey() {
		return this.einvoiceKey;
	}
	public void setGoodsCode(String value) {
		this.goodsCode = value;
	}
	
	public String getGoodsCode() {
		return this.goodsCode;
	}
	public void setTaxRate(String value) {
		this.taxRate = value;
	}
	
	public String getTaxRate() {
		return this.taxRate;
	}
	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}

}

