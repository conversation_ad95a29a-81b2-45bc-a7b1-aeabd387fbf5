package com.pms.czhotelfoundation.bean.room.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("RoomAuxiliarySearch")
public class RoomAuxiliarySearch extends BaseSearch {
	private Integer roomAuxiliaryId;
	private String roomAuxiliaryName;
	private String roomAuxiliaryDesc;
	private String roomAuxiliaryIcon;
	private String roomAuxiliaryIconColor  ;
	private Integer sort  ;
	private Integer roomAuxiliaryType  ;
	private String spareOne  ;
	private String spareTwo  ;
	private String spareThree  ;

	public void setRoomAuxiliaryId(Integer value) {
		this.roomAuxiliaryId = value;
	}
	
	public Integer getRoomAuxiliaryId() {
		return this.roomAuxiliaryId;
	}
	public void setRoomAuxiliaryName(String value) {
		this.roomAuxiliaryName = value;
	}
	
	public String getRoomAuxiliaryName() {
		return this.roomAuxiliaryName;
	}
	public void setRoomAuxiliaryDesc(String value) {
		this.roomAuxiliaryDesc = value;
	}
	
	public String getRoomAuxiliaryDesc() {
		return this.roomAuxiliaryDesc;
	}
	public void setRoomAuxiliaryIcon(String value) {
		this.roomAuxiliaryIcon = value;
	}
	
	public String getRoomAuxiliaryIcon() {
		return this.roomAuxiliaryIcon;
	}

	public String getRoomAuxiliaryIconColor() {
		return roomAuxiliaryIconColor;
	}

	public void setRoomAuxiliaryIconColor(String roomAuxiliaryIconColor) {
		this.roomAuxiliaryIconColor = roomAuxiliaryIconColor;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public Integer getRoomAuxiliaryType() {
		return roomAuxiliaryType;
	}

	public void setRoomAuxiliaryType(Integer roomAuxiliaryType) {
		this.roomAuxiliaryType = roomAuxiliaryType;
	}

	public String getSpareOne() {
		return spareOne;
	}

	public void setSpareOne(String spareOne) {
		this.spareOne = spareOne;
	}

	public String getSpareTwo() {
		return spareTwo;
	}

	public void setSpareTwo(String spareTwo) {
		this.spareTwo = spareTwo;
	}

	public String getSpareThree() {
		return spareThree;
	}

	public void setSpareThree(String spareThree) {
		this.spareThree = spareThree;
	}
}

