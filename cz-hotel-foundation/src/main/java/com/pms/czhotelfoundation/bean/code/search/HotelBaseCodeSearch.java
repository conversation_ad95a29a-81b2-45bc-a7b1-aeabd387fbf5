package com.pms.czhotelfoundation.bean.code.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("HotelBaseCodeSearch")
public class HotelBaseCodeSearch extends BaseSearch {
	private Integer id;
	private Integer codeId  ;
	private Integer type;
	private String name;
	private Integer hid;
	private Integer systemCode;
	private Integer enable;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setType(Integer value) {
		this.type = value;
	}
	
	public Integer getType() {
		return this.type;
	}
	public void setName(String value) {
		this.name = value;
	}
	
	public String getName() {
		return this.name;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setSystemCode(Integer value) {
		this.systemCode = value;
	}
	
	public Integer getSystemCode() {
		return this.systemCode;
	}
	public void setEnable(Integer value) {
		this.enable = value;
	}
	
	public Integer getEnable() {
		return this.enable;
	}

	public Integer getCodeId() {
		return codeId;
	}

	public void setCodeId(Integer codeId) {
		this.codeId = codeId;
	}
}

