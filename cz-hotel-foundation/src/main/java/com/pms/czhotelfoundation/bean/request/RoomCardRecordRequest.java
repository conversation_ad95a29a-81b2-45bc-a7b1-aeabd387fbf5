package com.pms.czhotelfoundation.bean.request;

import com.pms.czpmsutils.request.BaseRequest;

import java.util.Date;

public class RoomCardRecordRequest extends BaseRequest {
    private Integer id  ;
    private Integer hid  ;
    private Integer hotelGroupId  ;
    private Integer roomInfoId  ;
    private String roomNo  ;
    private String lockCode  ;
    private String mode  ;
    private String startTime  ;
    private String endTime  ;
    private String registId  ;
    private String userName  ;
    private String userId  ;
    private String guestName  ;
    private String roomType  ;
    private String isGroup  ;
    private Integer businessDay  ;
    private String makeStatus  ;
    private String source  ;
    private String guestNo  ;
    private String msg  ;
    private Date createTime  ;

    private String registPersonName;

    public String getRegistPersonName() {
        return registPersonName;
    }

    public void setRegistPersonName(String registPersonName) {
        this.registPersonName = registPersonName;
    }

    public Integer getRegistPersonId() {
        return registPersonId;
    }

    public void setRegistPersonId(Integer registPersonId) {
        this.registPersonId = registPersonId;
    }

    private Integer registPersonId;


    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return this.id;
    }
    public void setHid(Integer hid) {
        this.hid = hid;
    }

    public Integer getHid() {
        return this.hid;
    }
    public void setHotelGroupId(Integer hotelGroupId) {
        this.hotelGroupId = hotelGroupId;
    }

    public Integer getHotelGroupId() {
        return this.hotelGroupId;
    }
    public void setRoomInfoId(Integer roomInfoId) {
        this.roomInfoId = roomInfoId;
    }

    public Integer getRoomInfoId() {
        return this.roomInfoId;
    }
    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    public String getRoomNo() {
        return this.roomNo;
    }
    public void setLockCode(String lockCode) {
        this.lockCode = lockCode;
    }

    public String getLockCode() {
        return this.lockCode;
    }
    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getMode() {
        return this.mode;
    }
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getStartTime() {
        return this.startTime;
    }
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getEndTime() {
        return this.endTime;
    }
    public void setRegistId(String registId) {
        this.registId = registId;
    }

    public String getRegistId() {
        return this.registId;
    }
    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserName() {
        return this.userName;
    }
    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserId() {
        return this.userId;
    }
    public void setGuestName(String guestName) {
        this.guestName = guestName;
    }

    public String getGuestName() {
        return this.guestName;
    }
    public void setRoomType(String roomType) {
        this.roomType = roomType;
    }

    public String getRoomType() {
        return this.roomType;
    }
    public void setIsGroup(String isGroup) {
        this.isGroup = isGroup;
    }

    public String getIsGroup() {
        return this.isGroup;
    }
    public void setBusinessDay(Integer businessDay) {
        this.businessDay = businessDay;
    }

    public Integer getBusinessDay() {
        return this.businessDay;
    }
    public void setMakeStatus(String makeStatus) {
        this.makeStatus = makeStatus;
    }

    public String getMakeStatus() {
        return this.makeStatus;
    }
    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return this.source;
    }
    public void setGuestNo(String guestNo) {
        this.guestNo = guestNo;
    }

    public String getGuestNo() {
        return this.guestNo;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
