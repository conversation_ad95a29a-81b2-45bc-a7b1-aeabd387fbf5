package com.pms.czhotelfoundation.bean.ota;

public class OtaOrderStatusUpdate {
    private  Integer orderId;
    private  Integer hid;
    //订单状态 1.有效 2.NoShow 3.部分入住 4.全部入住 5.已取消 6.入住完成
    private  Integer status;
    //1.待确认 2.预订失败 3.已确认 4.商家拒单 5.订单取消中 6.订单已取消 7.已入住 8.已离店
    private  Integer otaStatus;

    private  String  sessionToken;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getOtaStatus() {
        return otaStatus;
    }

    public void setOtaStatus(Integer otaStatus) {
        this.otaStatus = otaStatus;
    }

    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }


    public Integer getHid() {
        return hid;
    }

    public void setHid(Integer hid) {
        this.hid = hid;
    }
}
