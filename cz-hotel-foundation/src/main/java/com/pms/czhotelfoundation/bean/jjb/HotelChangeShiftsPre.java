package com.pms.czhotelfoundation.bean.jjb;

import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;

public class HotelChangeShiftsPre extends BaseBean implements Serializable{
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer arAddMoney  ;
	private Integer arFinishMoney  ;
	private Integer vipAddMoney  ;
	private Integer vipFinishMoney  ;
	private Integer hotelChangeShiftsId  ;
	private Integer state  ;
	private Integer classId  ;
	private Integer businessDay  ;


	public HotelChangeShiftsPre(){
	}

	public HotelChangeShiftsPre(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setArAddMoney(Integer arAddMoney) {
		this.arAddMoney = arAddMoney;
	}
	
	public Integer getArAddMoney() {
		return this.arAddMoney;
	}
	public void setArFinishMoney(Integer arFinishMoney) {
		this.arFinishMoney = arFinishMoney;
	}
	
	public Integer getArFinishMoney() {
		return this.arFinishMoney;
	}
	public void setVipAddMoney(Integer vipAddMoney) {
		this.vipAddMoney = vipAddMoney;
	}
	
	public Integer getVipAddMoney() {
		return this.vipAddMoney;
	}
	public void setVipFinishMoney(Integer vipFinishMoney) {
		this.vipFinishMoney = vipFinishMoney;
	}
	
	public Integer getVipFinishMoney() {
		return this.vipFinishMoney;
	}
	public void setHotelChangeShiftsId(Integer hotelChangeShiftsId) {
		this.hotelChangeShiftsId = hotelChangeShiftsId;
	}
	
	public Integer getHotelChangeShiftsId() {
		return this.hotelChangeShiftsId;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setClassId(Integer classId) {
		this.classId = classId;
	}
	
	public Integer getClassId() {
		return this.classId;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}



}

