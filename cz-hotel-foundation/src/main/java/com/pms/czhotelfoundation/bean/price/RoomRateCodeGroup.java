package com.pms.czhotelfoundation.bean.price;

import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;

public class RoomRateCodeGroup extends BaseBean implements Serializable {

    private Integer rateId  ;
    private Integer hid  ;
    private Integer hotelGroupId  ;
    private Integer rateCodeType  ;
    private String rateCodeName  ;
    private String rateCode  ;
    private Integer resourceType  ;
    private String resourceName  ;
    private Integer canDelete  ;
    private Integer rateState  ;
    private Integer authorizeState  ;
    private String authorizeCode  ;
    private java.util.Date createTime  ;
    private String createUserId  ;
    private java.util.Date updateTime  ;
    private String updateUserId  ;
    public Integer getDiscount() {
        return discount;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }

    private Integer discount;

    public RoomRateCodeGroup(){
    }

    public RoomRateCodeGroup(Integer rateId){
        this.rateId = rateId;
    }

    public void setRateId(Integer rateId) {
        this.rateId = rateId;
    }

    public Integer getRateId() {
        return this.rateId;
    }
    public void setHid(Integer hid) {
        this.hid = hid;
    }

    public Integer getHid() {
        return this.hid;
    }
    public void setHotelGroupId(Integer hotelGroupId) {
        this.hotelGroupId = hotelGroupId;
    }

    public Integer getHotelGroupId() {
        return this.hotelGroupId;
    }
    public void setRateCodeType(Integer rateCodeType) {
        this.rateCodeType = rateCodeType;
    }

    public Integer getRateCodeType() {
        return this.rateCodeType;
    }
    public void setRateCodeName(String rateCodeName) {
        this.rateCodeName = rateCodeName;
    }

    public String getRateCodeName() {
        return this.rateCodeName;
    }
    public void setRateCode(String rateCode) {
        this.rateCode = rateCode;
    }

    public String getRateCode() {
        return this.rateCode;
    }
    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    public Integer getResourceType() {
        return this.resourceType;
    }
    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getResourceName() {
        return this.resourceName;
    }
    public void setCanDelete(Integer canDelete) {
        this.canDelete = canDelete;
    }

    public Integer getCanDelete() {
        return this.canDelete;
    }
    public void setRateState(Integer rateState) {
        this.rateState = rateState;
    }

    public Integer getRateState() {
        return this.rateState;
    }
    public void setAuthorizeState(Integer authorizeState) {
        this.authorizeState = authorizeState;
    }

    public Integer getAuthorizeState() {
        return this.authorizeState;
    }
    public void setAuthorizeCode(String authorizeCode) {
        this.authorizeCode = authorizeCode;
    }

    public String getAuthorizeCode() {
        return this.authorizeCode;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    public String getUpdateUserId() {
        return this.updateUserId;
    }


}
