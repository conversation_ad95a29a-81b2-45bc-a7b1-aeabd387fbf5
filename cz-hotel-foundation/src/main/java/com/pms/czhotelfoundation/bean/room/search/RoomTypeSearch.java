package com.pms.czhotelfoundation.bean.room.search;

import com.pms.czpmsutils.request.PageBaseRequest;
import org.apache.ibatis.type.Alias;

@Alias("RoomTypeSearch")
public class RoomTypeSearch extends PageBaseRequest {
	private Integer roomTypeId;
	private Integer hid;
	private Integer hotelGroupId;
	private String roomTypeName;
	private String roomTypeNameEn;
	private String shortName;
	private String area;
	private String bedtype;
	private Integer canOverbooking;
	private Integer canOverbookingNum;
	private Integer price;
	private Integer maxCheckinNum;
	private String des;
	private String desEn;
	private Integer state;
	private Integer isHourRoom;
	private Integer deposit;
	private Integer onlySelfmachineUse;
	private Integer selfmachinePromotionStart;
	private Integer selfmachinePromotionEnd;
	private Integer sort;
	private java.util.Date createTime;
	private String createUserId;
	private java.util.Date updateTime;
	private String updateUserId;
	private String hourRoomStartTime;
	private String hourRoomEndTime;
	private Integer discount;
	private Integer duration;

	public void setRoomTypeId(Integer value) {
		this.roomTypeId = value;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomTypeName(String value) {
		this.roomTypeName = value;
	}
	
	public String getRoomTypeName() {
		return this.roomTypeName;
	}
	public void setRoomTypeNameEn(String value) {
		this.roomTypeNameEn = value;
	}
	
	public String getRoomTypeNameEn() {
		return this.roomTypeNameEn;
	}
	public void setShortName(String value) {
		this.shortName = value;
	}
	
	public String getShortName() {
		return this.shortName;
	}
	public void setArea(String value) {
		this.area = value;
	}
	
	public String getArea() {
		return this.area;
	}
	public void setBedtype(String value) {
		this.bedtype = value;
	}
	
	public String getBedtype() {
		return this.bedtype;
	}
	public void setCanOverbooking(Integer value) {
		this.canOverbooking = value;
	}
	
	public Integer getCanOverbooking() {
		return this.canOverbooking;
	}
	public void setCanOverbookingNum(Integer value) {
		this.canOverbookingNum = value;
	}
	
	public Integer getCanOverbookingNum() {
		return this.canOverbookingNum;
	}
	public void setPrice(Integer value) {
		this.price = value;
	}
	
	public Integer getPrice() {
		return this.price;
	}
	public void setMaxCheckinNum(Integer value) {
		this.maxCheckinNum = value;
	}
	
	public Integer getMaxCheckinNum() {
		return this.maxCheckinNum;
	}
	public void setDes(String value) {
		this.des = value;
	}
	
	public String getDes() {
		return this.des;
	}
	public void setDesEn(String value) {
		this.desEn = value;
	}
	
	public String getDesEn() {
		return this.desEn;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setIsHourRoom(Integer value) {
		this.isHourRoom = value;
	}
	
	public Integer getIsHourRoom() {
		return this.isHourRoom;
	}
	public void setDeposit(Integer value) {
		this.deposit = value;
	}
	
	public Integer getDeposit() {
		return this.deposit;
	}
	public void setOnlySelfmachineUse(Integer value) {
		this.onlySelfmachineUse = value;
	}
	
	public Integer getOnlySelfmachineUse() {
		return this.onlySelfmachineUse;
	}
	public void setSelfmachinePromotionStart(Integer value) {
		this.selfmachinePromotionStart = value;
	}
	
	public Integer getSelfmachinePromotionStart() {
		return this.selfmachinePromotionStart;
	}
	public void setSelfmachinePromotionEnd(Integer value) {
		this.selfmachinePromotionEnd = value;
	}
	
	public Integer getSelfmachinePromotionEnd() {
		return this.selfmachinePromotionEnd;
	}
	public void setSort(Integer value) {
		this.sort = value;
	}
	
	public Integer getSort() {
		return this.sort;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setHourRoomStartTime(String value) {
		this.hourRoomStartTime = value;
	}
	
	public String getHourRoomStartTime() {
		return this.hourRoomStartTime;
	}
	public void setHourRoomEndTime(String value) {
		this.hourRoomEndTime = value;
	}
	
	public String getHourRoomEndTime() {
		return this.hourRoomEndTime;
	}
	public void setDiscount(Integer value) {
		this.discount = value;
	}
	
	public Integer getDiscount() {
		return this.discount;
	}
	public void setDuration(Integer value) {
		this.duration = value;
	}
	
	public Integer getDuration() {
		return this.duration;
	}

}

