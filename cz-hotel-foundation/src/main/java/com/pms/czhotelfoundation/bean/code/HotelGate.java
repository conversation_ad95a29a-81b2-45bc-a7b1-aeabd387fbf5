package com.pms.czhotelfoundation.bean.code;

import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;

/**
 * 
 */
public class HotelGate extends BaseBean implements Serializable{
	//
	private Integer id;
	//
	private String gateName;
	//闸机uuid
	private String uuid;
	//
	private Integer hid;
	//
	private Integer hotelGroupId;
	//配置信息表
	private Integer confTemplateHotelId;
	//机器人类型 1.洛必达
	private Integer robotType;
	//状态 1.启用 0停用
	private Integer status;
	//备注
	private String remark;
	//备注字段1
	private String bk1;
	//备注字段1
	private String bk2;
	//备注字段1
	private String bk3;


	private String sessionToken;

	public HotelGate(){
	}

	public HotelGate(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setGateName(String gateName) {
		this.gateName = gateName;
	}
	
	public String getGateName() {
		return this.gateName;
	}
	public void setUuid(String uuid) {
		this.uuid = uuid;
	}
	
	public String getUuid() {
		return this.uuid;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setConfTemplateHotelId(Integer confTemplateHotelId) {
		this.confTemplateHotelId = confTemplateHotelId;
	}
	
	public Integer getConfTemplateHotelId() {
		return this.confTemplateHotelId;
	}
	public void setRobotType(Integer robotType) {
		this.robotType = robotType;
	}
	
	public Integer getRobotType() {
		return this.robotType;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	public Integer getStatus() {
		return this.status;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getRemark() {
		return this.remark;
	}
	public void setBk1(String bk1) {
		this.bk1 = bk1;
	}
	
	public String getBk1() {
		return this.bk1;
	}
	public void setBk2(String bk2) {
		this.bk2 = bk2;
	}
	
	public String getBk2() {
		return this.bk2;
	}
	public void setBk3(String bk3) {
		this.bk3 = bk3;
	}
	
	public String getBk3() {
		return this.bk3;
	}

	public String getSessionToken() {
		return sessionToken;
	}

	public void setSessionToken(String sessionToken) {
		this.sessionToken = sessionToken;
	}
}

