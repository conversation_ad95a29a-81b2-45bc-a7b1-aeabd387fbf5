package com.pms.czhotelfoundation.bean.request;

import com.pms.czpmsutils.request.BaseRequest;

// 控制客控
public class ControlRoomDeviceRequest extends BaseRequest {


    private Integer deviceNo ;

    private String className;

    private Integer roomId;

    private String paramCode;

    private String paramValue ;

    private Integer confHotelDataId;

    public Integer getDeviceNo() {
        return deviceNo;
    }

    public void setDeviceNo(Integer deviceNo) {
        this.deviceNo = deviceNo;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    public String getParamCode() {
        return paramCode;
    }

    public void setParamCode(String paramCode) {
        this.paramCode = paramCode;
    }

    public String getParamValue() {
        return paramValue;
    }

    public void setParamValue(String paramValue) {
        this.paramValue = paramValue;
    }

    public Integer getConfHotelDataId() {
        return confHotelDataId;
    }

    public void setConfHotelDataId(Integer confHotelDataId) {
        this.confHotelDataId = confHotelDataId;
    }
}
