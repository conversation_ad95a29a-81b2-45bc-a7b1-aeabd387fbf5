package com.pms.czhotelfoundation.bean.price.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("RoomRateCodeGroupSearch")
public class RoomRateCodeGroupSearch extends BaseSearch {

    private Integer rateId;
    private Integer hid;
    private Integer hotelGroupId;
    private Integer rateCodeType;
    private String rateCodeName;
    private String rateCode;
    private Integer resourceType;
    private String resourceName;
    private Integer canDelete;
    private Integer rateState;
    private Integer authorizeState;
    private String authorizeCode;
    private java.util.Date createTime;
    private String createUserId;
    private java.util.Date updateTime;
    private String updateUserId;
    public Integer getDiscount() {
        return discount;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }

    private Integer discount;

    public void setRateId(Integer value) {
        this.rateId = value;
    }

    public Integer getRateId() {
        return this.rateId;
    }
    public void setHid(Integer value) {
        this.hid = value;
    }

    public Integer getHid() {
        return this.hid;
    }
    public void setHotelGroupId(Integer value) {
        this.hotelGroupId = value;
    }

    public Integer getHotelGroupId() {
        return this.hotelGroupId;
    }
    public void setRateCodeType(Integer value) {
        this.rateCodeType = value;
    }

    public Integer getRateCodeType() {
        return this.rateCodeType;
    }
    public void setRateCodeName(String value) {
        this.rateCodeName = value;
    }

    public String getRateCodeName() {
        return this.rateCodeName;
    }
    public void setRateCode(String value) {
        this.rateCode = value;
    }

    public String getRateCode() {
        return this.rateCode;
    }
    public void setResourceType(Integer value) {
        this.resourceType = value;
    }

    public Integer getResourceType() {
        return this.resourceType;
    }
    public void setResourceName(String value) {
        this.resourceName = value;
    }

    public String getResourceName() {
        return this.resourceName;
    }
    public void setCanDelete(Integer value) {
        this.canDelete = value;
    }

    public Integer getCanDelete() {
        return this.canDelete;
    }
    public void setRateState(Integer value) {
        this.rateState = value;
    }

    public Integer getRateState() {
        return this.rateState;
    }
    public void setAuthorizeState(Integer value) {
        this.authorizeState = value;
    }

    public Integer getAuthorizeState() {
        return this.authorizeState;
    }
    public void setAuthorizeCode(String value) {
        this.authorizeCode = value;
    }

    public String getAuthorizeCode() {
        return this.authorizeCode;
    }

    public void setCreateTime(java.util.Date value) {
        this.createTime = value;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }
    public void setCreateUserId(String value) {
        this.createUserId = value;
    }

    public String getCreateUserId() {
        return this.createUserId;
    }

    public void setUpdateTime(java.util.Date value) {
        this.updateTime = value;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }
    public void setUpdateUserId(String value) {
        this.updateUserId = value;
    }

    public String getUpdateUserId() {
        return this.updateUserId;
    }

}
