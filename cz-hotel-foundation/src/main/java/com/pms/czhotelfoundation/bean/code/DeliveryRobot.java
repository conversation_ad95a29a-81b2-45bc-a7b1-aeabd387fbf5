package com.pms.czhotelfoundation.bean.code;

import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;

/**
 * 
 */
public class DeliveryRobot extends BaseBean implements Serializable{
	//
	private Integer id;
	//
	private String robotName;
	//机器人uuid
	private String uuid;
	//
	private Integer hid;
	//
	private Integer hotelGroupId;
	//配置信息表
	private Integer confTemplateHotelId;
	//机器人类型 1.洛必达
	private Integer robotType;
	//状态 1.启用 0停用
	private Integer status;
	//备注
	private String remark;

	private String sessionToken;

	public DeliveryRobot(){
	}

	public DeliveryRobot(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setRobotName(String robotName) {
		this.robotName = robotName;
	}
	
	public String getRobotName() {
		return this.robotName;
	}
	public void setUuid(String uuid) {
		this.uuid = uuid;
	}
	
	public String getUuid() {
		return this.uuid;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setConfTemplateHotelId(Integer confTemplateHotelId) {
		this.confTemplateHotelId = confTemplateHotelId;
	}
	
	public Integer getConfTemplateHotelId() {
		return this.confTemplateHotelId;
	}
	public void setRobotType(Integer robotType) {
		this.robotType = robotType;
	}
	
	public Integer getRobotType() {
		return this.robotType;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	public Integer getStatus() {
		return this.status;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getRemark() {
		return this.remark;
	}


	public String getSessionToken() {
		return sessionToken;
	}

	public void setSessionToken(String sessionToken) {
		this.sessionToken = sessionToken;
	}
}

