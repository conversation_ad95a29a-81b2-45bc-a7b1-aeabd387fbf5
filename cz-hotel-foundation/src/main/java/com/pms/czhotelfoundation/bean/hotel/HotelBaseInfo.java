package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;

/**
 * 酒店基础信息
 */
public class HotelBaseInfo implements Serializable {
	private Integer hid  ;
	private String hotelName  ;
	private String hotelNameEn  ;
	private String shortName  ;
	private String shortNameEn  ;
	private Double lon  ;
	private Double lat  ;
	private String poi  ;
	private String addr ="" ;
	private String des  ;
	private String desEn  ;
	private Integer star  ;
	private Integer city  ;
	private Integer area  ;
	private Integer provice  ;
	private Integer country  ;
	private String telephone ="" ;
	private String fax  ;
	private String contact  ;
	private String contactPhone ="" ;
	private Integer hotelType  ;
	private String attention  ;
	private Integer lockType  ;
	private String firstPinyin  ;
	private Integer environmentalState  ;
	private Integer pmsType  ;
	private String email  ;
	private Integer hotelStatus  ;
	private java.util.Date cancelTime  ;
	private String cancelReason  ;
	private Integer isChain  ;
	private Integer roomCount  ;
	private Integer hotelGroupId  ;
	private Integer theamId  ;
	private java.util.Date createTime  ;
	private String createUserId  ;
	private String createUserName  ;
	private java.util.Date updateTime  ;
	private String updateUserId  ;
	private java.util.Date exprieDate  ;
	private String reviewUserId  ;
	private java.util.Date reviewTime  ;
	private Integer checkLimit  ;
	private Integer currencyRoomTime  ;
	private Integer leaveTime  ;
	private Integer systemUserId  ;
	private String openId  ;
	private String updateUserName  ;

	private String accountManager;

	private String accountManagerPhone;

	public HotelBaseInfo(){
	}

	public HotelBaseInfo(Integer hid){
		this.hid = hid;
	}

	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelName(String hotelName) {
		this.hotelName = hotelName;
	}

	public String getHotelName() {
		return this.hotelName;
	}
	public void setHotelNameEn(String hotelNameEn) {
		this.hotelNameEn = hotelNameEn;
	}

	public String getHotelNameEn() {
		return this.hotelNameEn;
	}
	public void setShortName(String shortName) {
		this.shortName = shortName;
	}

	public String getShortName() {
		return this.shortName;
	}
	public void setShortNameEn(String shortNameEn) {
		this.shortNameEn = shortNameEn;
	}

	public String getShortNameEn() {
		return this.shortNameEn;
	}
	public void setLon(Double lon) {
		this.lon = lon;
	}

	public Double getLon() {
		return this.lon;
	}
	public void setLat(Double lat) {
		this.lat = lat;
	}

	public Double getLat() {
		return this.lat;
	}
	public void setPoi(String poi) {
		this.poi = poi;
	}

	public String getPoi() {
		return this.poi;
	}
	public void setAddr(String addr) {
		this.addr = addr;
	}

	public String getAddr() {
		return this.addr;
	}
	public void setDes(String des) {
		this.des = des;
	}

	public String getDes() {
		return this.des;
	}
	public void setDesEn(String desEn) {
		this.desEn = desEn;
	}

	public String getDesEn() {
		return this.desEn;
	}
	public void setStar(Integer star) {
		this.star = star;
	}

	public Integer getStar() {
		return this.star;
	}
	public void setCity(Integer city) {
		this.city = city;
	}

	public Integer getCity() {
		return this.city;
	}
	public void setArea(Integer area) {
		this.area = area;
	}

	public Integer getArea() {
		return this.area;
	}
	public void setProvice(Integer provice) {
		this.provice = provice;
	}

	public Integer getProvice() {
		return this.provice;
	}
	public void setCountry(Integer country) {
		this.country = country;
	}

	public Integer getCountry() {
		return this.country;
	}
	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getTelephone() {
		return this.telephone;
	}
	public void setFax(String fax) {
		this.fax = fax;
	}

	public String getFax() {
		return this.fax;
	}
	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getContact() {
		return this.contact;
	}
	public void setContactPhone(String contactPhone) {
		this.contactPhone = contactPhone;
	}

	public String getContactPhone() {
		return this.contactPhone;
	}
	public void setHotelType(Integer hotelType) {
		this.hotelType = hotelType;
	}

	public Integer getHotelType() {
		return this.hotelType;
	}
	public void setAttention(String attention) {
		this.attention = attention;
	}

	public String getAttention() {
		return this.attention;
	}
	public void setLockType(Integer lockType) {
		this.lockType = lockType;
	}

	public Integer getLockType() {
		return this.lockType;
	}
	public void setFirstPinyin(String firstPinyin) {
		this.firstPinyin = firstPinyin;
	}

	public String getFirstPinyin() {
		return this.firstPinyin;
	}
	public void setEnvironmentalState(Integer environmentalState) {
		this.environmentalState = environmentalState;
	}

	public Integer getEnvironmentalState() {
		return this.environmentalState;
	}
	public void setPmsType(Integer pmsType) {
		this.pmsType = pmsType;
	}

	public Integer getPmsType() {
		return this.pmsType;
	}
	public void setEmail(String email) {
		this.email = email;
	}

	public String getEmail() {
		return this.email;
	}
	public void setHotelStatus(Integer hotelStatus) {
		this.hotelStatus = hotelStatus;
	}

	public Integer getHotelStatus() {
		return this.hotelStatus;
	}

	public void setCancelTime(java.util.Date cancelTime) {
		this.cancelTime = cancelTime;
	}

	public java.util.Date getCancelTime() {
		return this.cancelTime;
	}
	public void setCancelReason(String cancelReason) {
		this.cancelReason = cancelReason;
	}

	public String getCancelReason() {
		return this.cancelReason;
	}
	public void setIsChain(Integer isChain) {
		this.isChain = isChain;
	}

	public Integer getIsChain() {
		return this.isChain;
	}
	public void setRoomCount(Integer roomCount) {
		this.roomCount = roomCount;
	}

	public Integer getRoomCount() {
		return this.roomCount;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setTheamId(Integer theamId) {
		this.theamId = theamId;
	}

	public Integer getTheamId() {
		return this.theamId;
	}

	public void setCreateTime(java.util.Date createTime) {
		this.createTime = createTime;
	}

	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(java.util.Date updateTime) {
		this.updateTime = updateTime;
	}

	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}

	public String getUpdateUserId() {
		return this.updateUserId;
	}

	public void setExprieDate(java.util.Date exprieDate) {
		this.exprieDate = exprieDate;
	}

	public java.util.Date getExprieDate() {
		return this.exprieDate;
	}
	public void setReviewUserId(String reviewUserId) {
		this.reviewUserId = reviewUserId;
	}

	public String getReviewUserId() {
		return this.reviewUserId;
	}

	public void setReviewTime(java.util.Date reviewTime) {
		this.reviewTime = reviewTime;
	}

	public java.util.Date getReviewTime() {
		return this.reviewTime;
	}
	public void setCheckLimit(Integer checkLimit) {
		this.checkLimit = checkLimit;
	}

	public Integer getCheckLimit() {
		return this.checkLimit;
	}
	public void setCurrencyRoomTime(Integer currencyRoomTime) {
		this.currencyRoomTime = currencyRoomTime;
	}

	public Integer getCurrencyRoomTime() {
		return this.currencyRoomTime;
	}
	public void setLeaveTime(Integer leaveTime) {
		this.leaveTime = leaveTime;
	}

	public Integer getLeaveTime() {
		return this.leaveTime;
	}
	public void setSystemUserId(Integer systemUserId) {
		this.systemUserId = systemUserId;
	}

	public Integer getSystemUserId() {
		return this.systemUserId;
	}
	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getOpenId() {
		return this.openId;
	}
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}

	public String getUpdateUserName() {
		return this.updateUserName;
	}

	public String getAccountManager() {
		return accountManager;
	}

	public void setAccountManager(String accountManager) {
		this.accountManager = accountManager;
	}

	public String getAccountManagerPhone() {
		return accountManagerPhone;
	}

	public void setAccountManagerPhone(String accountManagerPhone) {
		this.accountManagerPhone = accountManagerPhone;
	}
}

