package com.pms.czhotelfoundation.bean.price;


import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;

/**
 * 房型 房价方案关联表
 * <AUTHOR>
 */
public class RoomRateCodeSpecific extends BaseBean implements Serializable {
	//主键
	private Integer rateCodeSpecificId;
	//房价方案Id
	private Integer rateId;
	//酒店id
	private Integer hid;
	//集团id
	private Integer hotelGroupId;
	//房型id
	private Integer roomTypeId;
	//折扣 1%----1
	private Double discount;
	//钟点房折扣
	private Integer hourRoomDiscount;
	//超时每小时加收多少钱
	private Integer overtimeAddMoney;
	//免费早餐券数量
	private Integer breakfastNum=0;
	//免费停车券
	private Integer parkingNum=0;
	//加收半天房费时间
	private String addHalfDay;
	//加收全天房费时间
	private String addAllDay;
	//最晚离店时间----默认离店时间12：00   HH：mm
	private String leavingTime;
	//保留时间 16:00
	private String keepingTime;
	//房型名称
	private String roomTypeName;
	//0向上取整 1向下取整 2维持原样
	private Integer roundOffType;
	//是否可删除 默认方案
	private Integer canDelete;
	//状态 1可以 0停用
	private Integer rateState;

	public RoomRateCodeSpecific(){
	}

	public RoomRateCodeSpecific(Integer rateCodeSpecificId){
		this.rateCodeSpecificId = rateCodeSpecificId;
	}

	public void setRateCodeSpecificId(Integer rateCodeSpecificId) {
		this.rateCodeSpecificId = rateCodeSpecificId;
	}
	
	public Integer getRateCodeSpecificId() {
		return this.rateCodeSpecificId;
	}
	public void setRateId(Integer rateId) {
		this.rateId = rateId;
	}
	
	public Integer getRateId() {
		return this.rateId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setDiscount(Double discount) {
		this.discount = discount;
	}
	
	public Double getDiscount() {
		return this.discount;
	}
	public void setHourRoomDiscount(Integer hourRoomDiscount) {
		this.hourRoomDiscount = hourRoomDiscount;
	}
	
	public Integer getHourRoomDiscount() {
		return this.hourRoomDiscount;
	}
	public void setBreakfastNum(Integer breakfastNum) {
		this.breakfastNum = breakfastNum;
	}
	
	public Integer getBreakfastNum() {
		return this.breakfastNum;
	}
	public void setParkingNum(Integer parkingNum) {
		this.parkingNum = parkingNum;
	}
	
	public Integer getParkingNum() {
		return this.parkingNum;
	}
	public void setLeavingTime(String leavingTime) {
		this.leavingTime = leavingTime;
	}
	
	public String getLeavingTime() {
		return this.leavingTime;
	}
	public void setKeepingTime(String keepingTime) {
		this.keepingTime = keepingTime;
	}
	
	public String getKeepingTime() {
		return this.keepingTime;
	}
	public void setRoomTypeName(String roomTypeName) {
		this.roomTypeName = roomTypeName;
	}
	
	public String getRoomTypeName() {
		return this.roomTypeName;
	}
	public void setRoundOffType(Integer roundOffType) {
		this.roundOffType = roundOffType;
	}
	
	public Integer getRoundOffType() {
		return this.roundOffType;
	}
	public void setCanDelete(Integer canDelete) {
		this.canDelete = canDelete;
	}
	
	public Integer getCanDelete() {
		return this.canDelete;
	}
	public void setRateState(Integer rateState) {
		this.rateState = rateState;
	}
	
	public Integer getRateState() {
		return this.rateState;
	}


	public String getAddHalfDay() {
		return addHalfDay;
	}

	public void setAddHalfDay(String addHalfDay) {
		this.addHalfDay = addHalfDay;
	}

	public String getAddAllDay() {
		return addAllDay;
	}

	public void setAddAllDay(String addAllDay) {
		this.addAllDay = addAllDay;
	}

	public Integer getOvertimeAddMoney() {
		return overtimeAddMoney;
	}

	public void setOvertimeAddMoney(Integer overtimeAddMoney) {
		this.overtimeAddMoney = overtimeAddMoney;
	}
}

