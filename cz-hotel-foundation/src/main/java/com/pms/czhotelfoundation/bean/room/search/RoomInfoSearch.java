package com.pms.czhotelfoundation.bean.room.search;

import com.pms.czpmsutils.request.PageBaseRequest;
import org.apache.ibatis.type.Alias;

import java.util.List;

@Alias("RoomInfoSearch")
public class RoomInfoSearch extends PageBaseRequest {
	private Integer roomInfoId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer buildingId;
	private String buildingName;
	private Integer floorId;
	private String floorName;
	private String roomNum;
	private String roomNumEn;
	private Integer lockBrand;
	private String lockBrandName;
	private Integer lockType;
	private String lockNum;
	private String policeNum;
	private String guestNum;
	private Integer roomTypeId;
	private String roomTypeName;
	private Integer roomNumState;
	private Integer selfmachineCanSale;
	private Integer state;
	private String roomPhone;
	private String roomShortPhone;
	private String roomDescription;
	private String roomDescriptionEn;
	private java.util.Date createTime;
	private String createUserId;
	private java.util.Date updateTime;
	private String updateUserId;

	private List<Integer> roomInfoIds;

	public String getBckStr() {
		return bckStr;
	}

	public void setBckStr(String bckStr) {
		this.bckStr = bckStr;
	}

	public Integer getRoomSort() {
		return roomSort;
	}

	public void setRoomSort(Integer roomSort) {
		this.roomSort = roomSort;
	}

	private String bckStr;

	private Integer roomSort;

	public void setRoomInfoId(Integer value) {
		this.roomInfoId = value;
	}
	
	public Integer getRoomInfoId() {
		return this.roomInfoId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setBuildingId(Integer value) {
		this.buildingId = value;
	}
	
	public Integer getBuildingId() {
		return this.buildingId;
	}
	public void setBuildingName(String value) {
		this.buildingName = value;
	}
	
	public String getBuildingName() {
		return this.buildingName;
	}
	public void setFloorId(Integer value) {
		this.floorId = value;
	}
	
	public Integer getFloorId() {
		return this.floorId;
	}
	public void setFloorName(String value) {
		this.floorName = value;
	}
	
	public String getFloorName() {
		return this.floorName;
	}
	public void setRoomNum(String value) {
		this.roomNum = value;
	}
	
	public String getRoomNum() {
		return this.roomNum;
	}
	public void setRoomNumEn(String value) {
		this.roomNumEn = value;
	}
	
	public String getRoomNumEn() {
		return this.roomNumEn;
	}
	public void setLockBrand(Integer value) {
		this.lockBrand = value;
	}
	
	public Integer getLockBrand() {
		return this.lockBrand;
	}
	public void setLockBrandName(String value) {
		this.lockBrandName = value;
	}
	
	public String getLockBrandName() {
		return this.lockBrandName;
	}
	public void setLockType(Integer value) {
		this.lockType = value;
	}
	
	public Integer getLockType() {
		return this.lockType;
	}
	public void setLockNum(String value) {
		this.lockNum = value;
	}
	
	public String getLockNum() {
		return this.lockNum;
	}
	public void setPoliceNum(String value) {
		this.policeNum = value;
	}
	
	public String getPoliceNum() {
		return this.policeNum;
	}
	public void setGuestNum(String value) {
		this.guestNum = value;
	}
	
	public String getGuestNum() {
		return this.guestNum;
	}
	public void setRoomTypeId(Integer value) {
		this.roomTypeId = value;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setRoomTypeName(String value) {
		this.roomTypeName = value;
	}
	
	public String getRoomTypeName() {
		return this.roomTypeName;
	}
	public void setRoomNumState(Integer value) {
		this.roomNumState = value;
	}
	
	public Integer getRoomNumState() {
		return this.roomNumState;
	}
	public void setSelfmachineCanSale(Integer value) {
		this.selfmachineCanSale = value;
	}
	
	public Integer getSelfmachineCanSale() {
		return this.selfmachineCanSale;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setRoomPhone(String value) {
		this.roomPhone = value;
	}
	
	public String getRoomPhone() {
		return this.roomPhone;
	}
	public void setRoomShortPhone(String value) {
		this.roomShortPhone = value;
	}
	
	public String getRoomShortPhone() {
		return this.roomShortPhone;
	}
	public void setRoomDescription(String value) {
		this.roomDescription = value;
	}
	
	public String getRoomDescription() {
		return this.roomDescription;
	}
	public void setRoomDescriptionEn(String value) {
		this.roomDescriptionEn = value;
	}
	
	public String getRoomDescriptionEn() {
		return this.roomDescriptionEn;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}

	public List<Integer> getRoomInfoIds() {
		return roomInfoIds;
	}

	public void setRoomInfoIds(List<Integer> roomInfoIds) {
		this.roomInfoIds = roomInfoIds;
	}

}

