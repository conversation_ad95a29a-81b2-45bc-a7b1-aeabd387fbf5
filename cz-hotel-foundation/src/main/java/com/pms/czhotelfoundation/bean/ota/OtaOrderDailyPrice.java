package com.pms.czhotelfoundation.bean.ota;

public class OtaOrderDailyPrice {
    private Integer bookingOrderId;
    private Integer bookingOrderRoomNumId;
    private Integer roomTypeId  ;
    private Integer dailyTime  ;
    private String  priceDate;
    private Integer rateCodeId;
    private Integer price;
    private Integer dailyState;
    private Integer breakNum;
    private Integer breakAddNum;
    private Integer breakUseNum;
    private Integer isStayover;
    private Integer companyId;
    private Integer companyAccountId;

    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }

    public Integer getBookingOrderRoomNumId() {
        return bookingOrderRoomNumId;
    }

    public void setBookingOrderRoomNumId(Integer bookingOrderRoomNumId) {
        this.bookingOrderRoomNumId = bookingOrderRoomNumId;
    }

    public Integer getRoomTypeId() {
        return roomTypeId;
    }

    public void setRoomTypeId(Integer roomTypeId) {
        this.roomTypeId = roomTypeId;
    }

    public Integer getDailyTime() {
        return dailyTime;
    }

    public void setDailyTime(Integer dailyTime) {
        this.dailyTime = dailyTime;
    }

    public String getPriceDate() {
        return priceDate;
    }

    public void setPriceDate(String priceDate) {
        this.priceDate = priceDate;
    }

    public Integer getRateCodeId() {
        return rateCodeId;
    }

    public void setRateCodeId(Integer rateCodeId) {
        this.rateCodeId = rateCodeId;
    }

    public Integer getPrice() {
        return price;
    }

    public void setPrice(Integer price) {
        this.price = price;
    }

    public Integer getDailyState() {
        return dailyState;
    }

    public void setDailyState(Integer dailyState) {
        this.dailyState = dailyState;
    }

    public Integer getBreakNum() {
        return breakNum;
    }

    public void setBreakNum(Integer breakNum) {
        this.breakNum = breakNum;
    }

    public Integer getBreakAddNum() {
        return breakAddNum;
    }

    public void setBreakAddNum(Integer breakAddNum) {
        this.breakAddNum = breakAddNum;
    }

    public Integer getBreakUseNum() {
        return breakUseNum;
    }

    public void setBreakUseNum(Integer breakUseNum) {
        this.breakUseNum = breakUseNum;
    }

    public Integer getIsStayover() {
        return isStayover;
    }

    public void setIsStayover(Integer isStayover) {
        this.isStayover = isStayover;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getCompanyAccountId() {
        return companyAccountId;
    }

    public void setCompanyAccountId(Integer companyAccountId) {
        this.companyAccountId = companyAccountId;
    }
}
