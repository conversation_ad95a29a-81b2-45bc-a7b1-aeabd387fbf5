package com.pms.czhotelfoundation.bean.jjb.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("HotelChangeShiftsPaysSearch")
public class HotelChangeShiftsPaysSearch extends BaseSearch {
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer cashMoney;
	private Integer cardMoney;
	private Integer wechatMoney;
	private Integer alipayMoney;
	private Integer vipMoney;
	private Integer arMoney;
	private Integer couponMoney;
	private Integer insideMoney;
	private Integer thirdMoney;
	private Integer creditMoney;
	private Integer remnantMoney;
	private Integer hotelChangeShiftsId;
	private Integer state;
	private Integer classId;
	private Integer businessDay;
	private java.util.Date createTime;
	private String createUserId;
	private String createUserName;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setCashMoney(Integer value) {
		this.cashMoney = value;
	}
	
	public Integer getCashMoney() {
		return this.cashMoney;
	}
	public void setCardMoney(Integer value) {
		this.cardMoney = value;
	}
	
	public Integer getCardMoney() {
		return this.cardMoney;
	}
	public void setWechatMoney(Integer value) {
		this.wechatMoney = value;
	}
	
	public Integer getWechatMoney() {
		return this.wechatMoney;
	}
	public void setAlipayMoney(Integer value) {
		this.alipayMoney = value;
	}
	
	public Integer getAlipayMoney() {
		return this.alipayMoney;
	}
	public void setVipMoney(Integer value) {
		this.vipMoney = value;
	}
	
	public Integer getVipMoney() {
		return this.vipMoney;
	}
	public void setArMoney(Integer value) {
		this.arMoney = value;
	}
	
	public Integer getArMoney() {
		return this.arMoney;
	}
	public void setCouponMoney(Integer value) {
		this.couponMoney = value;
	}
	
	public Integer getCouponMoney() {
		return this.couponMoney;
	}
	public void setInsideMoney(Integer value) {
		this.insideMoney = value;
	}
	
	public Integer getInsideMoney() {
		return this.insideMoney;
	}
	public void setThirdMoney(Integer value) {
		this.thirdMoney = value;
	}
	
	public Integer getThirdMoney() {
		return this.thirdMoney;
	}
	public void setCreditMoney(Integer value) {
		this.creditMoney = value;
	}
	
	public Integer getCreditMoney() {
		return this.creditMoney;
	}
	public void setRemnantMoney(Integer value) {
		this.remnantMoney = value;
	}
	
	public Integer getRemnantMoney() {
		return this.remnantMoney;
	}
	public void setHotelChangeShiftsId(Integer value) {
		this.hotelChangeShiftsId = value;
	}
	
	public Integer getHotelChangeShiftsId() {
		return this.hotelChangeShiftsId;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setClassId(Integer value) {
		this.classId = value;
	}
	
	public Integer getClassId() {
		return this.classId;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

}

