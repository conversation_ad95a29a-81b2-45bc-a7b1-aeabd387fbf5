package com.pms.czhotelfoundation.bean.code;

import java.io.Serializable;

public class HotelBaseCode implements Serializable {
	private Integer id  ;
	private Integer codeId  ;
	private Integer type  ;
	private String name  ;
	private Integer hid  ;
	private Integer systemCode  ;
	private Integer enable  ;

	public HotelBaseCode(){
	}

	public HotelBaseCode(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	
	public Integer getType() {
		return this.type;
	}
	public void setName(String name) {
		this.name = name;
	}
	
	public String getName() {
		return this.name;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setSystemCode(Integer systemCode) {
		this.systemCode = systemCode;
	}
	
	public Integer getSystemCode() {
		return this.systemCode;
	}
	public void setEnable(Integer enable) {
		this.enable = enable;
	}
	
	public Integer getEnable() {
		return this.enable;
	}

	public Integer getCodeId() {
		return codeId;
	}

	public void setCodeId(Integer codeId) {
		this.codeId = codeId;
	}
}

