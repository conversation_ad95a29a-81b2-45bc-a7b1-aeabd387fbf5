package com.pms.czhotelfoundation.bean.room;

import java.io.Serializable;

public class RoomInfoQr implements Serializable{
	//
	private Integer id;
	//
	private Integer hid;
	//
	private Integer hotelGroupId;
	//
	private Integer roomInfoId;
	//
	private String remark;

	private String sessionToken;

	public RoomInfoQr(){
	}

	public RoomInfoQr(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomInfoId(Integer roomInfoId) {
		this.roomInfoId = roomInfoId;
	}
	
	public Integer getRoomInfoId() {
		return this.roomInfoId;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	public String getRemark() {
		return this.remark;
	}

	public String getSessionToken() {
		return sessionToken;
	}

	public void setSessionToken(String sessionToken) {
		this.sessionToken = sessionToken;
	}

}

