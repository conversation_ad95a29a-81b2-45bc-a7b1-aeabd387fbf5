package com.pms.czhotelfoundation.bean.chess;

import java.io.Serializable;

/**
 * 
 */
public class ChessRoomInfoState implements Serializable{
	//
	private Integer id;
	//20170101
	private Integer businessDay;
	//
	private Integer chessRoomInfoId;
	//小时
	private Integer hoursRoomNo;
	//1,2,3,......59
	private String minuteRoomNo;

	public ChessRoomInfoState(){
	}

	public ChessRoomInfoState(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setChessRoomInfoId(Integer chessRoomInfoId) {
		this.chessRoomInfoId = chessRoomInfoId;
	}
	
	public Integer getChessRoomInfoId() {
		return this.chessRoomInfoId;
	}
	public void setHoursRoomNo(Integer hoursRoomNo) {
		this.hoursRoomNo = hoursRoomNo;
	}
	
	public Integer getHoursRoomNo() {
		return this.hoursRoomNo;
	}
	public void setMinuteRoomNo(String minuteRoomNo) {
		this.minuteRoomNo = minuteRoomNo;
	}
	
	public String getMinuteRoomNo() {
		return this.minuteRoomNo;
	}

}

