package com.pms.czhotelfoundation.bean.chess.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class ChessRoomInfoStateSearch extends PageBaseRequest{
	private Integer id;
	private Integer businessDay;
	private Integer chessRoomInfoId;
	private Integer hoursRoomNo;
	private String minuteRoomNo;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setChessRoomInfoId(Integer value) {
		this.chessRoomInfoId = value;
	}
	
	public Integer getChessRoomInfoId() {
		return this.chessRoomInfoId;
	}
	public void setHoursRoomNo(Integer value) {
		this.hoursRoomNo = value;
	}
	
	public Integer getHoursRoomNo() {
		return this.hoursRoomNo;
	}
	public void setMinuteRoomNo(String value) {
		this.minuteRoomNo = value;
	}
	
	public String getMinuteRoomNo() {
		return this.minuteRoomNo;
	}

}

