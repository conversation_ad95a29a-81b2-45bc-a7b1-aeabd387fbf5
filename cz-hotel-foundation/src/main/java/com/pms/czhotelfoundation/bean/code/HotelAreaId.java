package com.pms.czhotelfoundation.bean.code;

import java.io.Serializable;
import java.util.Date;

public class HotelAreaId implements Serializable{
	private Integer id  ;
	private Integer hotelGroupId  ;
	private String hotelGroupName  ;
	private String hotelAreaName  ;
	private String hotelAreaNameEn  ;
	private Integer city  ;
	private Integer area  ;
	private Integer province  ;
	private Integer country  ;
	private Integer parentAreaId  ;
	private Integer systemParam  ;
	private String remark  ;
	private String updateUserName  ;
	private Date updateUserTime  ;
	private String updateUserId  ;

	public HotelAreaId(){
	}

	public HotelAreaId(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return this.id;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setHotelGroupName(String hotelGroupName) {
		this.hotelGroupName = hotelGroupName;
	}

	public String getHotelGroupName() {
		return this.hotelGroupName;
	}
	public void setHotelAreaName(String hotelAreaName) {
		this.hotelAreaName = hotelAreaName;
	}

	public String getHotelAreaName() {
		return this.hotelAreaName;
	}
	public void setHotelAreaNameEn(String hotelAreaNameEn) {
		this.hotelAreaNameEn = hotelAreaNameEn;
	}

	public String getHotelAreaNameEn() {
		return this.hotelAreaNameEn;
	}
	public void setCity(Integer city) {
		this.city = city;
	}

	public Integer getCity() {
		return this.city;
	}
	public void setArea(Integer area) {
		this.area = area;
	}

	public Integer getArea() {
		return this.area;
	}
	public void setProvince(Integer province) {
		this.province = province;
	}

	public Integer getProvince() {
		return this.province;
	}
	public void setCountry(Integer country) {
		this.country = country;
	}

	public Integer getCountry() {
		return this.country;
	}
	public void setParentAreaId(Integer parentAreaId) {
		this.parentAreaId = parentAreaId;
	}

	public Integer getParentAreaId() {
		return this.parentAreaId;
	}
	public void setSystemParam(Integer systemParam) {
		this.systemParam = systemParam;
	}

	public Integer getSystemParam() {
		return this.systemParam;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getRemark() {
		return this.remark;
	}
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}

	public String getUpdateUserName() {
		return this.updateUserName;
	}

	public void setUpdateUserTime(Date updateUserTime) {
		this.updateUserTime = updateUserTime;
	}

	public Date getUpdateUserTime() {
		return this.updateUserTime;
	}
	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}

}

