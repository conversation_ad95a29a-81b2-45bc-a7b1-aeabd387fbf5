package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;
import java.util.Date;

public class HotelUserRoomState implements Serializable{
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer userId  ;
	private String vcColor  ;
	private String vdColor  ;
	private String ocColor  ;
	private String odColor  ;
	private String ooColor  ;
	private String osColor  ;
	private Integer roomStateWidth  ;
	private Integer roomStateHeight  ;
	private Integer roomNoFontSize  ;
	private Integer roomTypeFontSize  ;
	private Integer guestInfoFontSize  ;
	private Date createTime  ;
	private Integer createUserId  ;
	private Date updateTime  ;
	private Integer updateUserId  ;

	public HotelUserRoomState(){
	}

	public HotelUserRoomState(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setUserId(Integer userId) {
		this.userId = userId;
	}
	
	public Integer getUserId() {
		return this.userId;
	}
	public void setVcColor(String vcColor) {
		this.vcColor = vcColor;
	}
	
	public String getVcColor() {
		return this.vcColor;
	}
	public void setVdColor(String vdColor) {
		this.vdColor = vdColor;
	}
	
	public String getVdColor() {
		return this.vdColor;
	}
	public void setOcColor(String ocColor) {
		this.ocColor = ocColor;
	}
	
	public String getOcColor() {
		return this.ocColor;
	}
	public void setOdColor(String odColor) {
		this.odColor = odColor;
	}
	
	public String getOdColor() {
		return this.odColor;
	}
	public void setOoColor(String ooColor) {
		this.ooColor = ooColor;
	}
	
	public String getOoColor() {
		return this.ooColor;
	}
	public void setOsColor(String osColor) {
		this.osColor = osColor;
	}
	
	public String getOsColor() {
		return this.osColor;
	}
	public void setRoomStateWidth(Integer roomStateWidth) {
		this.roomStateWidth = roomStateWidth;
	}
	
	public Integer getRoomStateWidth() {
		return this.roomStateWidth;
	}
	public void setRoomStateHeight(Integer roomStateHeight) {
		this.roomStateHeight = roomStateHeight;
	}
	
	public Integer getRoomStateHeight() {
		return this.roomStateHeight;
	}
	public void setRoomNoFontSize(Integer roomNoFontSize) {
		this.roomNoFontSize = roomNoFontSize;
	}
	
	public Integer getRoomNoFontSize() {
		return this.roomNoFontSize;
	}
	public void setRoomTypeFontSize(Integer roomTypeFontSize) {
		this.roomTypeFontSize = roomTypeFontSize;
	}
	
	public Integer getRoomTypeFontSize() {
		return this.roomTypeFontSize;
	}
	public void setGuestInfoFontSize(Integer guestInfoFontSize) {
		this.guestInfoFontSize = guestInfoFontSize;
	}
	
	public Integer getGuestInfoFontSize() {
		return this.guestInfoFontSize;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}
	
	public Integer getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public Integer getUpdateUserId() {
		return this.updateUserId;
	}

}

