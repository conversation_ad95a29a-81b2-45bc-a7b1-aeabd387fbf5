package com.pms.czhotelfoundation.bean.hotel;

import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;

public class HotelEinvoiceInfo extends BaseBean implements Serializable {
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private String saleTaxno  ;
	private String saleAddress  ;

	private String salePhone  ;
	private String clerk  ;
	private String saleAccount  ;
	private String payee  ;
	private String checker  ;
	private String einvoiceUrl  ;
	private String einvoiceCode  ;
	private String einvoiceKey  ;
	private String goodsCode  ;
	private String taxRate  ;
	private Integer id  ;

	public HotelEinvoiceInfo(){
	}

	public HotelEinvoiceInfo(Integer id){
		this.id = id;
	}

	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setSaleTaxno(String saleTaxno) {
		this.saleTaxno = saleTaxno;
	}
	
	public String getSaleTaxno() {
		return this.saleTaxno;
	}
	public void setSaleAddress(String saleAddress) {
		this.saleAddress = saleAddress;
	}
	
	public String getSaleAddress() {
		return this.saleAddress;
	}

	public void setSalePhone(String salePhone) {
		this.salePhone = salePhone;
	}
	
	public String getSalePhone() {
		return this.salePhone;
	}
	public void setClerk(String clerk) {
		this.clerk = clerk;
	}
	
	public String getClerk() {
		return this.clerk;
	}
	public void setSaleAccount(String saleAccount) {
		this.saleAccount = saleAccount;
	}
	
	public String getSaleAccount() {
		return this.saleAccount;
	}
	public void setPayee(String payee) {
		this.payee = payee;
	}
	
	public String getPayee() {
		return this.payee;
	}
	public void setChecker(String checker) {
		this.checker = checker;
	}
	
	public String getChecker() {
		return this.checker;
	}
	public void setEinvoiceUrl(String einvoiceUrl) {
		this.einvoiceUrl = einvoiceUrl;
	}
	
	public String getEinvoiceUrl() {
		return this.einvoiceUrl;
	}
	public void setEinvoiceCode(String einvoiceCode) {
		this.einvoiceCode = einvoiceCode;
	}
	
	public String getEinvoiceCode() {
		return this.einvoiceCode;
	}
	public void setEinvoiceKey(String einvoiceKey) {
		this.einvoiceKey = einvoiceKey;
	}
	
	public String getEinvoiceKey() {
		return this.einvoiceKey;
	}
	public void setGoodsCode(String goodsCode) {
		this.goodsCode = goodsCode;
	}
	
	public String getGoodsCode() {
		return this.goodsCode;
	}
	public void setTaxRate(String taxRate) {
		this.taxRate = taxRate;
	}
	
	public String getTaxRate() {
		return this.taxRate;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}

}

