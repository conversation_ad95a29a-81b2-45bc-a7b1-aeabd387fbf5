package com.pms.czhotelfoundation.bean.ota;

public class GustInfo {
    //入住人姓名
    private String name;
    //入住人证件号
    private String idNumber;
    //证件类型：Identity_Card（身份证）、Passport（护照）、MTP（台胞证）、TaiwanPass（大陆居民往来台湾通行证）、
    // HKMacPass（港澳通行证）、HomePermit（回乡证）
    private String idType;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIdNumber() {
        return idNumber;
    }

    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }
}
