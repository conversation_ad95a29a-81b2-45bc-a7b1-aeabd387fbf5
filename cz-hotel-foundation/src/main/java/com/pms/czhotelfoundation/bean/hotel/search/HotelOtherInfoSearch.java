package com.pms.czhotelfoundation.bean.hotel.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("HotelOtherInfoSearch")
public class HotelOtherInfoSearch extends BaseSearch {
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private String hotelName;
	private Integer pmsId;
	private String pmsValue;
	private java.util.Date pmsValidityTime;
	private Integer policeId;
	private String policeValue;
	private java.util.Date policeValidityTime;
	private Integer faceId;
	private String faceValue;
	private java.util.Date faceValidityTime;
	private java.util.Date updateTime;
	private String updateUserId;
	private String updateUserName;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setHotelName(String value) {
		this.hotelName = value;
	}
	
	public String getHotelName() {
		return this.hotelName;
	}
	public void setPmsId(Integer value) {
		this.pmsId = value;
	}
	
	public Integer getPmsId() {
		return this.pmsId;
	}
	public void setPmsValue(String value) {
		this.pmsValue = value;
	}
	
	public String getPmsValue() {
		return this.pmsValue;
	}

	public void setPmsValidityTime(java.util.Date value) {
		this.pmsValidityTime = value;
	}
	
	public java.util.Date getPmsValidityTime() {
		return this.pmsValidityTime;
	}
	public void setPoliceId(Integer value) {
		this.policeId = value;
	}
	
	public Integer getPoliceId() {
		return this.policeId;
	}
	public void setPoliceValue(String value) {
		this.policeValue = value;
	}
	
	public String getPoliceValue() {
		return this.policeValue;
	}

	public void setPoliceValidityTime(java.util.Date value) {
		this.policeValidityTime = value;
	}
	
	public java.util.Date getPoliceValidityTime() {
		return this.policeValidityTime;
	}
	public void setFaceId(Integer value) {
		this.faceId = value;
	}
	
	public Integer getFaceId() {
		return this.faceId;
	}
	public void setFaceValue(String value) {
		this.faceValue = value;
	}
	
	public String getFaceValue() {
		return this.faceValue;
	}

	public void setFaceValidityTime(java.util.Date value) {
		this.faceValidityTime = value;
	}
	
	public java.util.Date getFaceValidityTime() {
		return this.faceValidityTime;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

}

