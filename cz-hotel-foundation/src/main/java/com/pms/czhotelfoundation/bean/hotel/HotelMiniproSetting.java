package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;

public class HotelMiniproSetting implements Serializable{
	//
	private Integer id;
	//hid 为0，则是整个集团的信息
	private Integer hid;
	//
	private Integer hotelGroupId;
	//菜单1 是否显示 酒店预订功能   0.不开启 1.开启 
	private Integer tabO;
	//菜单2 商品功能是否开启
	private Integer tabW;
	//菜单3 会员充值
	private Integer tabT;
	//菜单 4 会员储值消费可以订房
	private Integer tabF;
	//
	private Integer tabFi;
	//
	private Integer tabSi;
	//
	private Integer tabSe;
	//
	private Integer tabE;
	//
	private Integer tabN;
	//
	private Integer tabTe;

	public HotelMiniproSetting(){
	}

	public HotelMiniproSetting(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setTabO(Integer tabO) {
		this.tabO = tabO;
	}
	
	public Integer getTabO() {
		return this.tabO;
	}
	public void setTabW(Integer tabW) {
		this.tabW = tabW;
	}
	
	public Integer getTabW() {
		return this.tabW;
	}
	public void setTabT(Integer tabT) {
		this.tabT = tabT;
	}
	
	public Integer getTabT() {
		return this.tabT;
	}
	public void setTabF(Integer tabF) {
		this.tabF = tabF;
	}
	
	public Integer getTabF() {
		return this.tabF;
	}
	public void setTabFi(Integer tabFi) {
		this.tabFi = tabFi;
	}
	
	public Integer getTabFi() {
		return this.tabFi;
	}
	public void setTabSi(Integer tabSi) {
		this.tabSi = tabSi;
	}
	
	public Integer getTabSi() {
		return this.tabSi;
	}
	public void setTabSe(Integer tabSe) {
		this.tabSe = tabSe;
	}
	
	public Integer getTabSe() {
		return this.tabSe;
	}
	public void setTabE(Integer tabE) {
		this.tabE = tabE;
	}
	
	public Integer getTabE() {
		return this.tabE;
	}
	public void setTabN(Integer tabN) {
		this.tabN = tabN;
	}
	
	public Integer getTabN() {
		return this.tabN;
	}
	public void setTabTe(Integer tabTe) {
		this.tabTe = tabTe;
	}
	
	public Integer getTabTe() {
		return this.tabTe;
	}

}

