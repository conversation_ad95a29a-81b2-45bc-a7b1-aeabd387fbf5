package com.pms.czhotelfoundation.bean.request;

import com.pms.czhotelfoundation.bean.ConfTemplateItem;
import com.pms.czpmsutils.request.PageBaseRequest;

import java.util.ArrayList;
import java.util.List;

public class ConfTemplateRequest extends PageBaseRequest {


    /**
     * 发送模板id
     */
    private Integer id;

    private String name;

    private List<ConfTemplateItem> confTemplateItemList = new ArrayList<>();

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<ConfTemplateItem> getConfTemplateItemList() {
        return confTemplateItemList;
    }

    public void setConfTemplateItemList(List<ConfTemplateItem> confTemplateItemList) {
        this.confTemplateItemList = confTemplateItemList;
    }
}
