package com.pms.czhotelfoundation.bean.hotel;

import java.io.Serializable;
import java.util.Date;

public class HotelOtherInfo implements Serializable {
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private String hotelName  ;
	private Integer pmsId  ;
	private String pmsValue  ;
	private Date pmsValidityTime  ;
	private Integer policeId  ;
	private String policeValue  ;
	private Date policeValidityTime  ;
	private Integer faceId  ;
	private String faceValue  ;
	private Date faceValidityTime  ;
	private Date updateTime  ;
	private String updateUserId  ;
	private String updateUserName  ;

	public HotelOtherInfo(){
	}

	public HotelOtherInfo(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setHotelName(String hotelName) {
		this.hotelName = hotelName;
	}

	public String getHotelName() {
		return this.hotelName;
	}
	public void setPmsId(Integer pmsId) {
		this.pmsId = pmsId;
	}

	public Integer getPmsId() {
		return this.pmsId;
	}
	public void setPmsValue(String pmsValue) {
		this.pmsValue = pmsValue;
	}

	public String getPmsValue() {
		return this.pmsValue;
	}

	public void setPmsValidityTime(Date pmsValidityTime) {
		this.pmsValidityTime = pmsValidityTime;
	}

	public Date getPmsValidityTime() {
		return this.pmsValidityTime;
	}
	public void setPoliceId(Integer policeId) {
		this.policeId = policeId;
	}

	public Integer getPoliceId() {
		return this.policeId;
	}
	public void setPoliceValue(String policeValue) {
		this.policeValue = policeValue;
	}

	public String getPoliceValue() {
		return this.policeValue;
	}

	public void setPoliceValidityTime(Date policeValidityTime) {
		this.policeValidityTime = policeValidityTime;
	}

	public Date getPoliceValidityTime() {
		return this.policeValidityTime;
	}
	public void setFaceId(Integer faceId) {
		this.faceId = faceId;
	}

	public Integer getFaceId() {
		return this.faceId;
	}
	public void setFaceValue(String faceValue) {
		this.faceValue = faceValue;
	}

	public String getFaceValue() {
		return this.faceValue;
	}

	public void setFaceValidityTime(Date faceValidityTime) {
		this.faceValidityTime = faceValidityTime;
	}

	public Date getFaceValidityTime() {
		return this.faceValidityTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String updateUserName) {
		this.updateUserName = updateUserName;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

}

