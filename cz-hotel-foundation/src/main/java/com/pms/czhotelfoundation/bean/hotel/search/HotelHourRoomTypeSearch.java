package com.pms.czhotelfoundation.bean.hotel.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("HotelHourRoomTypeSearch")
public class HotelHourRoomTypeSearch extends BaseSearch {
	private Integer id;
	private Integer roomTypeId;
	private String roomTypeName;
	private Integer hourRoomInfoId;
	private Integer price;
	private Integer hourPrice;
	private Integer hourLenght;
	private Integer sort;
	private Integer state;
	private Integer maxPrice;
	private Integer toDayRoom;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer breakfastId;
	private String reamark;
	private java.util.Date createTime;
	private Integer createUserId;
	private java.util.Date updateTime;
	private Integer updateUserId;
	private Integer hourRoomCode;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setRoomTypeId(Integer value) {
		this.roomTypeId = value;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setRoomTypeName(String value) {
		this.roomTypeName = value;
	}
	
	public String getRoomTypeName() {
		return this.roomTypeName;
	}
	public void setHourRoomInfoId(Integer value) {
		this.hourRoomInfoId = value;
	}
	
	public Integer getHourRoomInfoId() {
		return this.hourRoomInfoId;
	}
	public void setPrice(Integer value) {
		this.price = value;
	}
	
	public Integer getPrice() {
		return this.price;
	}
	public void setHourPrice(Integer value) {
		this.hourPrice = value;
	}
	
	public Integer getHourPrice() {
		return this.hourPrice;
	}
	public void setHourLenght(Integer value) {
		this.hourLenght = value;
	}
	
	public Integer getHourLenght() {
		return this.hourLenght;
	}
	public void setSort(Integer value) {
		this.sort = value;
	}
	
	public Integer getSort() {
		return this.sort;
	}
	public void setState(Integer value) {
		this.state = value;
	}
	
	public Integer getState() {
		return this.state;
	}
	public void setMaxPrice(Integer value) {
		this.maxPrice = value;
	}
	
	public Integer getMaxPrice() {
		return this.maxPrice;
	}
	public void setToDayRoom(Integer value) {
		this.toDayRoom = value;
	}
	
	public Integer getToDayRoom() {
		return this.toDayRoom;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setBreakfastId(Integer value) {
		this.breakfastId = value;
	}
	
	public Integer getBreakfastId() {
		return this.breakfastId;
	}
	public void setReamark(String value) {
		this.reamark = value;
	}
	
	public String getReamark() {
		return this.reamark;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(Integer value) {
		this.createUserId = value;
	}
	
	public Integer getCreateUserId() {
		return this.createUserId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(Integer value) {
		this.updateUserId = value;
	}
	
	public Integer getUpdateUserId() {
		return this.updateUserId;
	}
	public void setHourRoomCode(Integer value) {
		this.hourRoomCode = value;
	}
	
	public Integer getHourRoomCode() {
		return this.hourRoomCode;
	}

}

