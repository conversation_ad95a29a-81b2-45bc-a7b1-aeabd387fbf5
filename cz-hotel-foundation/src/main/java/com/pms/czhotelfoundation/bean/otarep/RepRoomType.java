package com.pms.czhotelfoundation.bean.otarep;

import java.io.Serializable;
import java.util.Date;

public class RepRoomType implements Serializable{
	//
	private Integer id;
	//
	private String name;
	//
	private String code;
	//酒店id
	private Integer repHid;
	//ota平台房型id
	private String otaRtid;
	//ota平台  代理id
	private String otaMyrtid;
	//1.启用 0.停用 2.已售完 
	private Integer state;
	//创建时间
	private Date createTime;

	public RepRoomType(){
	}

	public RepRoomType(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setName(String name) {
		this.name = name;
	}
	
	public String getName() {
		return this.name;
	}
	public void setCode(String code) {
		this.code = code;
	}
	
	public String getCode() {
		return this.code;
	}
	public void setRepHid(Integer repHid) {
		this.repHid = repHid;
	}
	
	public Integer getRepHid() {
		return this.repHid;
	}
	public void setOtaRtid(String otaRtid) {
		this.otaRtid = otaRtid;
	}
	
	public String getOtaRtid() {
		return this.otaRtid;
	}
	public void setOtaMyrtid(String otaMyrtid) {
		this.otaMyrtid = otaMyrtid;
	}
	
	public String getOtaMyrtid() {
		return this.otaMyrtid;
	}
	public void setState(Integer state) {
		this.state = state;
	}
	
	public Integer getState() {
		return this.state;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getCreateTime() {
		return this.createTime;
	}

}

