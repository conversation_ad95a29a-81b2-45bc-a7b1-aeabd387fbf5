package com.pms.czhotelfoundation.bean.request;

import com.pms.czhotelfoundation.bean.ConfHotelItemData;
import com.pms.czpmsutils.request.BaseRequest;

import java.util.ArrayList;
import java.util.List;

public class ConfDataRequest extends BaseRequest {


    /**
     * 发送模板id
     */
    private Integer id;

    private Integer confTemplateId;

    private String name;

    private List<ConfHotelItemData> confItemDataList = new ArrayList<>();

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getConfTemplateId() {
        return confTemplateId;
    }

    public void setConfTemplateId(Integer confTemplateId) {
        this.confTemplateId = confTemplateId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<ConfHotelItemData> getConfItemDataList() {
        return confItemDataList;
    }

    public void setConfItemDataList(List<ConfHotelItemData> confItemDataList) {
        this.confItemDataList = confItemDataList;
    }
}
