package com.pms.czhotelfoundation.bean.code;

import java.io.Serializable;
import java.util.List;

public class City implements Serializable {
	private Integer id  ;
	private String code  ;
	private String name  ;
	private String provincecode  ;

	private List<Area> areaList;

	public City(){
	}

	public City(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setCode(String code) {
		this.code = code;
	}
	
	public String getCode() {
		return this.code;
	}
	public void setName(String name) {
		this.name = name;
	}
	
	public String getName() {
		return this.name;
	}
	public void setProvincecode(String provincecode) {
		this.provincecode = provincecode;
	}
	
	public String getProvincecode() {
		return this.provincecode;
	}

	public List<Area> getAreaList() {
		return areaList;
	}

	public void setAreaList(List<Area> areaList) {
		this.areaList = areaList;
	}
}

