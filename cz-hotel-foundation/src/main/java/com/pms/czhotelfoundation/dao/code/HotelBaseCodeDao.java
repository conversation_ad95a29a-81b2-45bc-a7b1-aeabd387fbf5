package com.pms.czhotelfoundation.dao.code;





import com.pms.czhotelfoundation.bean.code.HotelBaseCode;
import com.pms.czhotelfoundation.bean.code.search.HotelBaseCodeSearch;

import java.util.List;

public interface HotelBaseCodeDao {

    public Integer saveHotelBaseCode(HotelBaseCode hotelBaseCode);

    public Integer editHotelBaseCode(HotelBaseCode hotelBaseCode);

    public Integer deleteHotelBaseCode(Integer hotelBaseCodeId);

    public HotelBaseCode selectById(Integer hotelBaseCodeId);

    public List<HotelBaseCode> selectBySearch(HotelBaseCodeSearch hotelBaseCodeSearch);

}
