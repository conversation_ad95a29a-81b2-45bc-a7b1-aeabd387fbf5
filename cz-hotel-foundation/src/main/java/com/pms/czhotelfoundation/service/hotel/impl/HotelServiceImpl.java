package com.pms.czhotelfoundation.service.hotel.impl;

import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.Page;
import com.pms.czhotelfoundation.bean.FileInfo;
import com.pms.czhotelfoundation.bean.code.*;
import com.pms.czhotelfoundation.bean.code.search.*;
import com.pms.czhotelfoundation.bean.hotel.*;
import com.pms.czhotelfoundation.bean.hotel.search.*;
import com.pms.czhotelfoundation.bean.price.RoomDayPrice;
import com.pms.czhotelfoundation.bean.price.RoomRateCode;
import com.pms.czhotelfoundation.bean.price.search.RoomDayPriceSearch;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSearch;
import com.pms.czhotelfoundation.bean.room.RoomImage;
import com.pms.czhotelfoundation.bean.room.RoomType;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeSearch;
import com.pms.czhotelfoundation.bean.search.FileInfoSearch;
import com.pms.czhotelfoundation.bean.setting.HotelNightType;
import com.pms.czhotelfoundation.bean.setting.search.HotelNightTypeSearch;
import com.pms.czhotelfoundation.bean.user.NewUserHotel;
import com.pms.czhotelfoundation.dao.FileInfoDao;
import com.pms.czhotelfoundation.dao.code.*;
import com.pms.czhotelfoundation.dao.hotel.*;
import com.pms.czhotelfoundation.dao.price.RoomDayPriceDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeDao;
import com.pms.czhotelfoundation.dao.room.RoomImageDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czhotelfoundation.dao.setting.HotelNightTypeDao;
import com.pms.czhotelfoundation.dao.user.FounNewUserHotelDao;
import com.pms.czhotelfoundation.dao.user.FounTbUserSessionDao;
import com.pms.czhotelfoundation.service.hotel.HotelService;
import com.pms.czhotelfoundation.service.out.OutPlatService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.WebClientUtil;
import com.pms.czpmsutils.conf.HotelIotPlatConfig;
import com.pms.czpmsutils.constant.*;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.HourRoomInfoSearch;
import com.pms.czpmsutils.request.*;
import com.pms.czpmsutils.thirdauth.HotelIotStrategy;
import com.pms.czpmsutils.view.HotelOrgPriceShowView;
import com.pms.czpmsutils.view.HotelOrgPriceView;
import lombok.extern.log4j.Log4j2;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Log4j2
@Service
@Primary
public class HotelServiceImpl extends BaseService implements HotelService {

    @Autowired
    private HotelBaseInfoDao hotelBaseInfoDao;

    @Autowired
    private ProvinceDao provinceDao;

    @Autowired
    private CityDao cityDao;

    @Autowired
    private AreaDao areaDao;

    @Autowired
    private FounTbUserSessionDao tbUserSessionDao;

    @Autowired
    private FounNewUserHotelDao newUserHotelDao;

    @Autowired
    private HotelBusinessDayDao hotelBusinessDayDao;

    @Autowired
    private HotelClassInfoDao hotelClassInfoDao;

    @Autowired
    private HotelNightTypeDao hotelNightTypeDao;

    @Autowired
    private HourRoomInfoDao hourRoomInfoDao;
    @Autowired
    private HourRoomTypeInfoDao hourRoomTypeInfoDao;
    @Autowired
    private HotelUserRoomStateDao hotelUserRoomStateDao;
    @Autowired
    private HotelHourRoomTypeDao hotelHourRoomTypeDao;
    @Autowired
    private HotelGoodsManagementDao hotelGoodsManagementDao;

    @Autowired
    private HotelGroupInfoDao hotelGroupInfoDao;

    @Autowired
    private RoomImageDao roomImageDao;

    @Autowired
    private HotelMiniproSettingDao hotelMiniproSettingDao;

    @Autowired
    private DeliveryRobotDao deliveryRobotDao;
    @Autowired
    private HotelGateDao hotelGateDao;

    @Autowired
    private FileInfoDao fileInfoDao;

    @Autowired
    private HotelOrgPriceDao hotelOrgPriceDao;

    @Autowired
    private HotelOrgRoomTypeDao hotelOrgRoomTypeDao;

    @Autowired
    private HotelOrgRateCodeDao hotelOrgRateCodeDao;

    @Autowired
    private HotelOrgPriceImageDao hotelOrgPriceImageDao;

    @Autowired
    private RoomTypeDao roomTypeDao;
    @Autowired
    private RoomRateCodeDao roomRateCodeDao;

    @Autowired
    private RoomDayPriceDao roomDayPriceDao;

    @Resource
    private HotelIotPlatConfig iotPlatConfig;

    @Resource
    private WebClientUtil webClientUtil;


    @Autowired
    OutPlatService outPlatService;


    @Override
    public ResponseData findAllHotel(HotelBaseInfoSearch hotelBaseInfoSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken = hotelBaseInfoSearch.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Double lat = hotelBaseInfoSearch.getLat();
            Double lon = hotelBaseInfoSearch.getLon();

            hotelBaseInfoSearch.setLat(null);
            hotelBaseInfoSearch.setLon(null);
            Page<HotelBaseInfo> hotelBaseInfos = hotelBaseInfoDao.selectBySearch(hotelBaseInfoSearch);
//            Object j = param.get("lng");
//            Object w = param.get("lat");
//
//            // 经纬度不等于空
//            if(j!=null&&w!=null){
//                double lng = param.getDouble("lng");
//                double lat = param.getDouble("lat");
//
//                for (HotelBaseInfo hb :hotelBaseInfos){
//
//                }
//            }
            List<HotelBaseInfo> hotelBaseInfos1 = new ArrayList<>();
            java.text.DecimalFormat df = new java.text.DecimalFormat("#.0");
            if (1 == hotelBaseInfoSearch.getLocType()) {

                Integer wxType = hotelBaseInfoSearch.getWxType();

                Map<Integer, HotelMiniproSetting> wxsetMap = new HashMap<>();

                // 1.则验证微信是否开启酒店查询
                if (wxType == 1) {
                    HotelMiniproSettingSearch hotelMiniproSettingSearch = new HotelMiniproSettingSearch();
                    hotelMiniproSettingSearch.setHotelGroupId(user.getHotelGroupId());
                    Page<HotelMiniproSetting> hotelMiniproSettings = hotelMiniproSettingDao.selectBySearch(hotelMiniproSettingSearch);

                    wxsetMap = hotelMiniproSettings.stream().collect(Collectors.toMap(HotelMiniproSetting::getHid, a -> a, (k1, k2) -> k1));

                }

                for (HotelBaseInfo hb : hotelBaseInfos) {


                    if (wxType == 1) {

                        HotelMiniproSetting hotelMiniproSetting = wxsetMap.get(hb.getHid());

                        // 值小于1 说明是0 0代表不开启
                        if (hotelMiniproSetting != null && hotelMiniproSetting.getTabO() < 1) {

                            continue;

                        }

                    }

                    if (lat == null || lon == null) {
                        hb.setAttention("0");
                        hb.setArea(0);
                    } else {
                        Double distance = HotelUtils.getDistance(lat, lon, hb.getLat(), hb.getLon());
                        double v = distance.intValue() / 1000.0;
                        if (v < 0.1) {
                            hb.setAttention("0.1");
                        } else {
                            hb.setAttention(df.format(v));
                        }

                        hb.setArea(distance.intValue());
                    }

                    hotelBaseInfos1.add(hb);
                }
                hotelBaseInfos1.sort(Comparator.comparing(HotelBaseInfo::getArea));
                responseData.setData(hotelBaseInfos1);
            } else {
                responseData.setData(hotelBaseInfos);
            }


        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData findHotelGroupInfo(HotelGroupInfoSearch hotelGroupInfoSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Page<HotelGroupInfo> hotelGroupInfos = hotelGroupInfoDao.selectBySearch(hotelGroupInfoSearch);
            responseData.setData(hotelGroupInfos);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 获取省市县数据
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData proviceAndCityAnaArea(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /**
             * 2.分别获取省 市 县
             */
            ProvinceSearch provinceSearch = new ProvinceSearch();
            List<Province> provinces = provinceDao.selectBySearch(provinceSearch);

            HashMap<String, Object> provincesMap = new HashMap<>();

            for (Province province : provinces) {
                provincesMap.put(province.getCode(), province.getName());
            }

            CitySearch citySearch = new CitySearch();
            List<City> cities = cityDao.selectBySearch(citySearch);

            HashMap<String, Object> citiesMap = new HashMap<>();
            HashMap<String, List<City>> citiesListMap = new HashMap<>();

            for (City city : cities) {
                citiesMap.put(city.getCode(), city.getName());
                List<City> cities1 = citiesListMap.get(city.getProvincecode());
                if (cities1 == null) {
                    cities1 = new ArrayList<>();
                }
                cities1.add(city);
                citiesListMap.put(city.getProvincecode(), cities1);
            }

            AreaSearch areaSearch = new AreaSearch();
            List<Area> areas = areaDao.selectBySearch(areaSearch);

            HashMap<String, Object> areaMap = new HashMap<>();
            HashMap<String, List<Area>> areaListMap = new HashMap<>();
            for (Area area : areas) {
                areaMap.put(area.getCode(), area.getName());
                List<Area> areas1 = areaListMap.get(area.getCitycode());
                if (areas1 == null) {
                    areas1 = new ArrayList<>();
                }
                areas1.add(area);
                areaListMap.put(area.getCitycode(), areas1);
            }

            HashMap<String, Object> resultMap = new HashMap<>();
            resultMap.put("provinces", provinces);
            resultMap.put("provincesMap", provincesMap);
            resultMap.put("cities", citiesListMap);
            resultMap.put("citiesMap", citiesMap);
            resultMap.put("areas", areaListMap);
            resultMap.put("areaMap", areaMap);
            responseData.setData(resultMap);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 添加或修改酒店
     *
     * @param param
     * @return
     */
    @Transactional
    @Override
    public ResponseData addOrUpdateHotel(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);


            /**
             * 2.添加或修改酒店
             */
            String hotelParamStr = param.getString("hotelParam");
            JSONObject jsonObject = JSONObject.fromObject(hotelParamStr);

            HotelBaseInfo hotelBaseInfo = (HotelBaseInfo) JSONObject.toBean(JSONObject.fromObject(hotelParamStr), HotelBaseInfo.class);
            hotelBaseInfo.setReviewTime(HotelUtils.parseStr2Date(jsonObject.getString("reviewTime")));
            hotelBaseInfo.setExprieDate(HotelUtils.parseStr2Date(jsonObject.getString("exprieDate")));
            Date date = new Date();
            hotelBaseInfo.setUpdateTime(date);
            hotelBaseInfo.setUpdateUserId(user.getUserId());
            hotelBaseInfo.setUpdateUserName(user.getUserName());
            /**
             * iot数据推送准备
             */
            IotEnterprise iotEnterprise = new IotEnterprise();
            iotEnterprise.setCityCode(hotelBaseInfo.getCity() + "");
            iotEnterprise.setEnterpriseName(hotelBaseInfo.getHotelName());
            iotEnterprise.setExpireTime(jsonObject.getString("exprieDate"));
            iotEnterprise.setMark(hotelBaseInfo.getDes());
            iotEnterprise.setPmsHotelId(hotelBaseInfo.getHid() + "");
            iotEnterprise.setProvinceCode(hotelBaseInfo.getProvice() + "");
            HotelGroupInfo hotelGroupInfo = hotelGroupInfoDao.selectById(hotelBaseInfo.getHotelGroupId());
            int updateValue = 0;
            if (hotelBaseInfo.getHid() == null || hotelBaseInfo.getHid() < 0) {
                hotelBaseInfo.setHid(null);
                hotelBaseInfo.setCreateTime(date);
                hotelBaseInfo.setCreateUserId(user.getUserId());
                hotelBaseInfo.setCreateUserName(user.getUserName());
                updateValue = hotelBaseInfoDao.saveHotelBaseInfo(hotelBaseInfo);
                //这里查询酒店集团信息，
                iotEnterprise.setPmsHotelGroup(hotelGroupInfo.getChainId());
                HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
                Map<String, String> authParamMap = new HashMap<>();
                authParamMap.put("eid", hotelBaseInfo.getHid() + "");
                authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
                authParamMap.put("appSecret", iotPlatConfig.getAppSecret());

                hotelIotStrategy.initStrategy(authParamMap);
                hotelIotStrategy.initStrategy(authParamMap);
                webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.NEW_ENTERPRISE, iotEnterprise, hotelIotStrategy);
            } else {
                outPlatService.syncIotHotelEdit(iotEnterprise);
                updateValue = hotelBaseInfoDao.editHotelBaseInfo(hotelBaseInfo);
                if (updateValue > 0) {
                    TbUserSession tbUserSession = new TbUserSession();
                    tbUserSession.setHotelName(hotelBaseInfo.getHotelName());
                    tbUserSession.setHid(hotelBaseInfo.getHid());
                    tbUserSession.setHotelGroupId(hotelBaseInfo.getHotelGroupId());
                    tbUserSessionDao.updateSessionHotelName(tbUserSession);

                    NewUserHotel newUserHotel = new NewUserHotel();
                    newUserHotel.setHotelName(hotelBaseInfo.getHotelName());
                    newUserHotel.setHid(hotelBaseInfo.getHid());
                    newUserHotel.setHotelGroupId(hotelBaseInfo.getHotelGroupId());
                    newUserHotelDao.updateHotelName(newUserHotel);

                    Oprecord oprecord = new Oprecord(user);
                    oprecord.setDescription("修改酒店基础信息");
                    this.addOprecords(oprecord);
                }
            }
            if (updateValue < 1) {
                throw new Exception("操作失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }


    /**
     * 根据酒店 id 获取酒店信息
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData findHotelMsgByHid(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            Integer hid = null;
            try {
                String sessionToken = param.getString(ER.SESSION_TOKEN);
                if (!StringUtils.isEmpty(sessionToken)) {
                    final TbUserSession user = this.getTbUserSession(sessionToken);
                    hid = user.getHid();
                }
            }catch (Exception e){
                log.info("无法获取sessionToken信息，异常信息为{}",e);
            }
            if (param.containsKey("hid") && param.getInt("hid") > 0) {
                hid = param.getInt("hid");
            }
            HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(hid);
            responseData.setData(hotelBaseInfo);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 根据userId获取酒店
     *
     * @param map
     * @return
     */
    @Override
    public ResponseData findHotelByUserId(Map<String, Object> map) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            TbUserSession tbUserSession = this.getTbUserSession(sessionToken);

            /*2.查询酒店信息*/
           /* TbUserHotelSearch tbUserHotelSearch = new TbUserHotelSearch();
            tbUserHotelSearch.setUserId(tbUserSession.getUserId());

            List<TbUserHotel> tbUserHotels = tbUserHotelDao.selectBySearch(tbUserHotelSearch);

            ArrayList<TbUserHotel> newUserHotels = new ArrayList<>();

            for (TbUserHotel tbUserHotel : tbUserHotels) {

                HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(tbUserHotel.getHid());
                tbUserHotel.setHotelName(hotelBaseInfo.getHotelName());
                tbUserHotel.setHotelNameEn(hotelBaseInfo.getHotelNameEn());
                tbUserHotel.setAddress(hotelBaseInfo.getAddr());
                tbUserHotel.setDesc(hotelBaseInfo.getDes());
                tbUserHotel.setDescEn(hotelBaseInfo.getDesEn());

                newUserHotels.add(tbUserHotel);

            }*/

            /*3.返回数据*/
            // resultMap.put("data", newUserHotels);
            //  resultMap.put(ER.MSG, SUCCESS_MSG.MSG_查询成功);

            responseData.setMsg(SUCCESS_MSG.MSG_查询成功);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    @Override
    public ResponseData findHotelImage(BaseRequest baseRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            FileInfoSearch fileInfoSearch = new FileInfoSearch();
            String sessionToken = baseRequest.getSessionToken();
            TbUserSession tbUserSession = this.getTbUserSession(sessionToken);
            fileInfoSearch.setHid(tbUserSession.getHid());
            if (null != baseRequest.getHid()) {
                fileInfoSearch.setHid(baseRequest.getHid());
            }
            fileInfoSearch.setFileType(2);
            fileInfoSearch.setPageSize(0);
            Page<FileInfo> fileInfos = fileInfoDao.selectBySearch(fileInfoSearch);
            List<String> imageList = new ArrayList<>();
            for (int i = 0; i < fileInfos.size(); i++) {
                imageList.add(fileInfos.get(i).getUrl());
            }
            JSONObject data = new JSONObject();
            data.put("imageList", imageList);
            responseData.setData(data);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 根据用户名获取绑定的酒店信息
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData findHotelByUserName(JSONObject param) {
        //返回参数
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /**
             * 1.根据用户名获取酒店
             */
            String userName = HotelUtils.validaStr(param.get("userName"));
            if ("".equals(userName)) {
                throw new Exception("用户名不能为空");
            }

       /*     TbUserSearch tbUserSearch = new TbUserSearch();
            tbUserSearch.setUid(userName);
            List<TbUser> tbUsers = tbUserDao.selectBySearchTbUser(tbUserSearch);

            if (tbUsers.size() < 1) {
                throw new Exception("未查到相关的酒店信息");
            }
            TbUser tbUser = tbUsers.get(0);

            *//**
             * 2.查询酒店信息
             *//*
            TbUserHotelSearch tbUserHotelSearch = new TbUserHotelSearch();
            tbUserHotelSearch.setUserId(tbUser.getUserId().toString());

            List<TbUserHotel> tbUserHotels = tbUserHotelDao.selectBySearch(tbUserHotelSearch);

            ArrayList<TbUserHotel> newUserHotels = new ArrayList<>();

            for (TbUserHotel tbUserHotel : tbUserHotels) {

                HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(tbUserHotel.getHid());
                tbUserHotel.setHotelName(hotelBaseInfo.getHotelName());
                tbUserHotel.setHotelNameEn(hotelBaseInfo.getHotelNameEn());
                tbUserHotel.setAddress(hotelBaseInfo.getAddr());
                tbUserHotel.setDesc(hotelBaseInfo.getDes());
                tbUserHotel.setDescEn(hotelBaseInfo.getDesEn());

                newUserHotels.add(tbUserHotel);

            }

            *//*3.返回数据*//*
            resultMap.put("data", newUserHotels);*/
            // resultMap.put(ER.MSG, SUCCESS_MSG.MSG_查询成功);
            responseData.setData(SUCCESS_MSG.MSG_查询成功);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;

    }

    @Override
    public ResponseData findHotelBusinessDay(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
          /*  String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);*/
            int hid = param.getInt("hid");
            HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
            hotelBusinessDaySearch.setHid(hid);
            HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);
            if (hotelBusinessDay == null) {
                responseData.setData(HotelUtils.currentDate().replace("-", ""));
            } else {
                responseData.setData(hotelBusinessDay.getBusinessDay());
            }

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData findHotelClassInfo(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            HotelClassInfoSearch hotelClassInfoSearch = new HotelClassInfoSearch();

            hotelClassInfoSearch.setHid(user.getHid());
            hotelClassInfoSearch.setState(1);
            List<HotelClassInfo> hotelClassInfos = hotelClassInfoDao.selectBySearch(hotelClassInfoSearch);

            if (hotelClassInfos == null || hotelClassInfos.size() != 1) {
                throw new Exception("查询不到酒店交接班信息");
            }
            responseData.setData(hotelClassInfos.get(0));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData addOrUpdateHotelClassInfo(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (param.get("hotelClassData") == null) {
                throw new Exception("班次数据不能空");
            }
            JSONObject hotelClassData = param.getJSONObject("hotelClassData");

            if (hotelClassData.get("hotelClassId") == null || hotelClassData.getInt("hotelClassId") <= 0) {

                Date date = new Date();
                HotelClassInfo hotelClassInfo = new HotelClassInfo();

                hotelClassInfo.setCreateTime(date);
                hotelClassInfo.setCreateUserName(user.getUserName());
                hotelClassInfo.setCreateUserId(user.getUserId());

                hotelClassInfo.setHid(user.getHid());
                hotelClassInfo.setHotelGroupId(user.getHotelGroupId());
                hotelClassInfo.setState(1);
                hotelClassInfo.setHotelClassType(hotelClassData.getInt("hotelClassType"));

                if (hotelClassData.get("pettyCash") != null) {
                    hotelClassInfo.setPettyCash(hotelClassData.getInt("pettyCash"));
                }
                if (hotelClassData.get("lastCash") != null) {
                    hotelClassInfo.setPettyCash(hotelClassData.getInt("pettyCash"));
                }

                Integer insert = hotelClassInfoDao.insert(hotelClassInfo);
                if (insert < 1) {
                    throw new Exception("添加失败");
                }


            } else {
                Date date = new Date();
                HotelClassInfo hotelClassInfo = new HotelClassInfo();

                hotelClassInfo.setHotelClassId(hotelClassData.getInt("hotelClassId"));
                hotelClassInfo.setUpdateTime(date);
                hotelClassInfo.setUpdateUserName(user.getUserName());
                hotelClassInfo.setUpdateUserId(user.getUserId());

                hotelClassInfo.setHid(user.getHid());
                hotelClassInfo.setHotelGroupId(user.getHotelGroupId());
                hotelClassInfo.setState(1);
                hotelClassInfo.setHotelClassType(hotelClassData.getInt("hotelClassType"));

                if (hotelClassData.get("pettyCash") != null && !hotelClassData.getString("pettyCash").equals("null")) {
                    hotelClassInfo.setPettyCash(hotelClassData.getInt("pettyCash"));
                }
                if (hotelClassData.get("lastCash") != null && !hotelClassData.getString("lastCash").equals("null")) {
                    hotelClassInfo.setPettyCash(hotelClassData.getInt("pettyCash"));
                }

                Integer update = hotelClassInfoDao.update(hotelClassInfo);

                if (update < 1) {
                    throw new Exception("修改失败");
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData findHotelNightType(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            HotelNightTypeSearch hotelNightTypeSearch = new HotelNightTypeSearch();

            hotelNightTypeSearch.setHid(user.getHid());
            hotelNightTypeSearch.setState(1);
            List<HotelNightType> hotelNightTypes = hotelNightTypeDao.selectBySearch(hotelNightTypeSearch);

            if (hotelNightTypes == null || hotelNightTypes.size() != 1) {
                throw new Exception("查询不到酒店的夜审方式");
            }
            responseData.setData(hotelNightTypes.get(0));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData addOrUpdateHotelNightType(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (param.get("hotelNightTypeData") == null) {
                throw new Exception("夜审方式数据不能空");
            }
            JSONObject hotelNightTypeData = param.getJSONObject("hotelNightTypeData");

            if (hotelNightTypeData.get("hotelNightTypeId") == null) {

                Date date = new Date();
                HotelNightType hotelNightType = new HotelNightType();

                hotelNightType.setCreateTime(date);
                hotelNightType.setCreateUserName(user.getUserName());
                hotelNightType.setCreateUserId(user.getUserId());

                hotelNightType.setHid(user.getHid());
                hotelNightType.setHotelGroupId(user.getHotelGroupId());
                hotelNightType.setState(1);
                hotelNightType.setHotelNightType(hotelNightTypeData.getInt("hotelNightType"));
                Integer insert = hotelNightTypeDao.insert(hotelNightType);
                if (insert < 1) {
                    throw new Exception("添加失败");
                }


            } else {
                Date date = new Date();
                HotelNightType hotelNightType = new HotelNightType();

                hotelNightType.setHotelNightTypeId(hotelNightTypeData.getInt("hotelNightTypeId"));
                hotelNightType.setUpdateTime(date);
                hotelNightType.setUpdateUserName(user.getUserName());
                hotelNightType.setUpdateUserId(user.getUserId());

                hotelNightType.setHid(user.getHid());
                hotelNightType.setHotelGroupId(user.getHotelGroupId());
                hotelNightType.setState(1);
                hotelNightType.setHotelNightType(hotelNightTypeData.getInt("hotelNightType"));
                Integer update = hotelNightTypeDao.update(hotelNightType);

                if (update < 1) {
                    throw new Exception("修改失败");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public Page<HourRoomInfo> searchHourRoomInfoList(HourRoomInfoSearch hourRoomInfoSearch) throws Exception {
        TbUserSession userSession = getTbUserSession(hourRoomInfoSearch);
        if (userSession == null) {
            throw new Exception(ERROR_MSG.INVALID_SESSION);
        }
        return hourRoomInfoDao.selectBySearch(hourRoomInfoSearch);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData saveOrUpdateHourRoomInfo(HourRoomInfoRequest hourRoomInfoRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            HourRoomInfo hourRoomInfo = new HourRoomInfo();
            TbUserSession userSession = getTbUserSession(hourRoomInfoRequest);
            if (userSession == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            BeanUtils.copyProperties(hourRoomInfoRequest, hourRoomInfo);
            hourRoomInfo.setHid(userSession.getHid());
            hourRoomInfo.setHotelGroupId(userSession.getHotelGroupId());
            if (hourRoomInfo.getHourRoomId() != null && hourRoomInfo.getHourRoomId() >= 0) {
                hourRoomInfo.setUpdateUserId(Integer.parseInt(userSession.getUserId()));
                hourRoomInfo.setUpdateTime(new Date());
                hourRoomInfo.setUpdateUserId(Integer.parseInt(userSession.getUserId()));
                Integer rt = hourRoomInfoDao.update(hourRoomInfo);
                if (rt == 0) {
                    throw new Exception("修改钟点房信息失败");
                }
            } else {
                hourRoomInfo.setCreateUserId(Integer.parseInt(userSession.getUserId()));
                hourRoomInfo.setCreateTime(new Date());
                Integer rt = hourRoomInfoDao.insert(hourRoomInfo);
                if (rt == 0) {
                    throw new Exception("修改钟点房信息失败");
                }
            }
            HourRoomTypeInfoSearch hourRoomTypeInfoSearch = new HourRoomTypeInfoSearch();
            hourRoomTypeInfoSearch.setHourRoomId(hourRoomInfo.getHourRoomId());
            Page<HourRoomTypeInfo> hourRoomTypeInfos = hourRoomTypeInfoDao.selectBySearch(hourRoomTypeInfoSearch);
            for (int i = 0; i < hourRoomTypeInfos.size(); i++) {
                Integer delete = hourRoomTypeInfoDao.delete(hourRoomTypeInfos.get(i).getHourRoomTypeInfoId());
                if (delete < 1) {
                    throw new Exception("删除钟点房关联信息失败");
                }
            }
            List<HourRoomInfoRequest.HourRoomTypeInfoRequest> hourRoomTypeInfoRequestList = hourRoomInfoRequest.getHourRoomTypeInfoRequestList();
            for (int i = 0; i < hourRoomTypeInfoRequestList.size(); i++) {
                HourRoomInfoRequest.HourRoomTypeInfoRequest hourRoomTypeInfoRequest = hourRoomTypeInfoRequestList.get(i);
                HourRoomTypeInfo hourRoomTypeInfo = new HourRoomTypeInfo();
                hourRoomTypeInfo.setHourRoomId(hourRoomInfo.getHourRoomId());
                hourRoomTypeInfo.setRoomTypeId(hourRoomTypeInfoRequest.getRoomTypeId());
                hourRoomTypeInfo.setRoomTypeName(hourRoomTypeInfoRequest.getRoomTypeName());
                hourRoomTypeInfo.setHid(userSession.getHid());
                hourRoomTypeInfo.setHotelGroupId(userSession.getHotelGroupId());
                Integer insert = hourRoomTypeInfoDao.insert(hourRoomTypeInfo);
                if (insert < 1) {
                    throw new Exception("插入钟点房关联信息失败");
                }
            }

            //处理房型加各种的相关设置
            HotelHourRoomTypeSearch hotelHourRoomTypeSearch = new HotelHourRoomTypeSearch();
            hotelHourRoomTypeSearch.setHid(userSession.getHid());
            hotelHourRoomTypeSearch.setHourRoomInfoId(hourRoomInfoRequest.getHourRoomId());
            List<HotelHourRoomType> hotelHourRoomTypes = hotelHourRoomTypeDao.selectBySearch(hotelHourRoomTypeSearch);
            for (int i = 0; i < hotelHourRoomTypes.size(); i++) {
                HotelHourRoomType hotelHourRoomType = hotelHourRoomTypes.get(i);
                hotelHourRoomType.setHourLenght(hourRoomInfoRequest.getHourRoomCode());
                hotelHourRoomType.setHourRoomCode(hourRoomInfoRequest.getHourRoomCode());
                Integer update = hotelHourRoomTypeDao.update(hotelHourRoomType);
                if (update < 1) {
                    throw new Exception("修改钟点房信息失败");
                }
            }


            responseData.setData(hourRoomInfo.getHourRoomId());
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public void deleteHourRoomInfo(HourRoomInfoRequest hourRoomInfoRequest) throws Exception {
        TbUserSession userSession = getTbUserSession(hourRoomInfoRequest);
        if (userSession == null || userSession.getHid() == null) {
            throw new Exception(ERROR_MSG.INVALID_SESSION);
        }
        HourRoomInfo hourRoomInfo = hourRoomInfoDao.selectById(hourRoomInfoRequest.getHourRoomId());
        if (hourRoomInfo == null || hourRoomInfo.getHid() == null || !hourRoomInfo.getHid().equals(userSession.getHid())) {
            throw new Exception("删除失败");
        }
        Integer rt = hourRoomInfoDao.delete(hourRoomInfo.getHourRoomId());
        if (rt == 0) {
            throw new Exception("删除失败");
        }
    }

    @Override
    public ResponseData searchHotelHourRoomTypeInfoList(BaseRequest baseRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession userSession = getTbUserSession(baseRequest);
            if (userSession == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            HourRoomTypeInfoSearch hourRoomTypeInfoSearch = new HourRoomTypeInfoSearch();
            hourRoomTypeInfoSearch.setHid(userSession.getHid());
            Page<HourRoomTypeInfo> hourRoomTypeInfos = hourRoomTypeInfoDao.selectBySearch(hourRoomTypeInfoSearch);
            Map<Integer, List<HourRoomTypeInfo>> collect = hourRoomTypeInfos.stream().collect(Collectors.groupingBy(HourRoomTypeInfo::getHourRoomId));
            responseData.setData(collect);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData saveOrUpdateHotelUserRoomState(HotelUserRoomStateRequest hotelUserRoomStateRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession userSession = getTbUserSession(hotelUserRoomStateRequest);
            if (userSession == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            HotelUserRoomState hotelUserRoomState = new HotelUserRoomState();
            BeanUtils.copyProperties(hotelUserRoomStateRequest, hotelUserRoomState);
            if (hotelUserRoomState.getId() != null) {
                hotelUserRoomState.setUpdateTime(new Date());
                hotelUserRoomState.setUpdateUserId(Integer.parseInt(userSession.getUserId()));
                Integer update = hotelUserRoomStateDao.update(hotelUserRoomState);
                if (update < 1) {
                    throw new Exception("修改房态方案失败");
                }
            } else {
                hotelUserRoomState.setCreateTime(new Date());
                hotelUserRoomState.setCreateUserId(Integer.parseInt(userSession.getUserId()));
                hotelUserRoomState.setUserId(Integer.parseInt(userSession.getUserId()));
                hotelUserRoomState.setHid(userSession.getHid());
                hotelUserRoomState.setHotelGroupId(userSession.getHotelGroupId());
                Integer insert = hotelUserRoomStateDao.insert(hotelUserRoomState);
                if (insert < 1) {
                    throw new Exception("新增房态方案失败");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData searchHotelUserRoomState(HotelUserRoomStateRequest hotelUserRoomStateRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession userSession = getTbUserSession(hotelUserRoomStateRequest);
            if (userSession == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            HotelUserRoomStateSearch hotelUserRoomStateSearch = new HotelUserRoomStateSearch();
            hotelUserRoomStateSearch.setHid(userSession.getHid());
            hotelUserRoomStateSearch.setHotelGroupId(userSession.getHotelGroupId());
            hotelUserRoomStateSearch.setUserId(Integer.parseInt(userSession.getUserId()));
            List<HotelUserRoomState> hotelUserRoomStates = hotelUserRoomStateDao.selectBySearch(hotelUserRoomStateSearch);
            responseData.setData(hotelUserRoomStates.get(0));
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }

    @Override
    public ResponseData saveOrUpdateHotelHourRoomType(HotelHourRoomTypeRequest hotelHourRoomTypeRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = getTbUserSession(hotelHourRoomTypeRequest);
            if (user == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            HotelHourRoomType hotelHourRoomType = new HotelHourRoomType();
            BeanUtils.copyProperties(hotelHourRoomTypeRequest, hotelHourRoomType);

            if (hotelHourRoomType.getId() != null) {
                hotelHourRoomType.setUpdateTime(new Date());
                hotelHourRoomType.setUpdateUserId(Integer.parseInt(user.getUserId()));
                Integer update = hotelHourRoomTypeDao.update(hotelHourRoomType);
                if (update < 1) {
                    throw new Exception("修改房型钟点房价格失败");
                }
            } else {
                hotelHourRoomType.setHid(user.getHid());
                hotelHourRoomType.setHotelGroupId(user.getHotelGroupId());
                hotelHourRoomType.setCreateTime(new Date());
                hotelHourRoomType.setCreateUserId(Integer.parseInt(user.getUserId()));
                Integer insert = hotelHourRoomTypeDao.insert(hotelHourRoomType);
                if (insert < 1) {
                    throw new Exception("新增钟点房价格方案失败");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData searchHotelHourRoomTypeList(HotelHourRoomTypeRequest hotelHourRoomTypeRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = getTbUserSession(hotelHourRoomTypeRequest);
            if (user == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            HotelHourRoomTypeSearch hotelHourRoomTypeSearch = new HotelHourRoomTypeSearch();
            hotelHourRoomTypeSearch.setHid(user.getHid());
            hotelHourRoomTypeSearch.setHourRoomInfoId(hotelHourRoomTypeRequest.getHourRoomInfoId());
            List<HotelHourRoomType> hotelHourRoomTypes = hotelHourRoomTypeDao.selectBySearch(hotelHourRoomTypeSearch);
            responseData.setData(hotelHourRoomTypes);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData saveOrUpdateHotelGoodsManagement(HotelGoodsManagementRequest hotelGoodsManagementRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = getTbUserSession(hotelGoodsManagementRequest);
            if (user == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            HotelGoodsManagement hotelGoodsManagement = new HotelGoodsManagement();
            BeanUtils.copyProperties(hotelGoodsManagementRequest, hotelGoodsManagement);

            if (hotelGoodsManagement.getId() != null) {
                if (hotelGoodsManagement.getState() == 1) {
                    hotelGoodsManagement.setUpdateTime(new Date());
                    hotelGoodsManagement.setUpdateUserName(user.getUserName());
                    hotelGoodsManagement.setUpdateUserId(Integer.parseInt(user.getUserId()));
                }
                Integer update = hotelGoodsManagementDao.update(hotelGoodsManagement);
                if (update < 1) {
                    throw new Exception("修改物品管理信息失败");
                }
            } else {
                hotelGoodsManagement.setHid(user.getHid());
                hotelGoodsManagement.setHotelGroupId(user.getHotelGroupId());
                hotelGoodsManagement.setCreateTime(new Date());
                hotelGoodsManagement.setCreateUserName(user.getUserName());
                hotelGoodsManagement.setBusinessDay(user.getBusinessDay());
                hotelGoodsManagement.setClassId(user.getClassId());
                hotelGoodsManagement.setAccountYear(user.getBusinessYear());
                hotelGoodsManagement.setAccountYearMonth(user.getBusinessMonth());
                hotelGoodsManagement.setState(0);
                hotelGoodsManagement.setCreateUserId(Integer.parseInt(user.getUserId()));
                Integer insert = hotelGoodsManagementDao.insert(hotelGoodsManagement);
                if (insert < 1) {
                    throw new Exception("新增钟点房价格方案失败");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public Page<HotelGoodsManagement> searchHotelGoodsManagementList(HotelGoodsManagementSearch hotelGoodsManagementSearch) throws Exception {
        TbUserSession user = getTbUserSession(hotelGoodsManagementSearch);
        if (user == null) {
            throw new Exception(ERROR_MSG.INVALID_SESSION);
        }
        hotelGoodsManagementSearch.setHid(user.getHid());
        return hotelGoodsManagementDao.selectBySearch(hotelGoodsManagementSearch);
    }

    @Override
    public ResponseData createHotel(CreateHotelInfoRequest createHotelInfoRequest) {
//        1.集团
//        2.酒店
//        3.pms-right-manager.tb_user 用户表和用户关联表
//        4.把sys_permission的权限信息 添加到酒店 sys_hotel_permission表里
//        5.新建角色 sys_role ，添加对应的sys_role_permission 权限
//        6.pms-hotel-foundation.room_rate_code_group 查询集团下的房价码，拷贝到 room_rate_code 里，添加酒店新建的房价码
//        7.pms-membership.card_group_type , card_group_level 会员级别，会员类型，会员级别下的会员都复制到当前酒店里
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            //判断是否有集团酒店
            int res = 0;
            Date date = new Date();
            //操作酒店表
            HotelBaseInfo hotelBaseInfo = new HotelBaseInfo();
            BeanUtils.copyProperties(createHotelInfoRequest, hotelBaseInfo);
            /**
             * 说明是集团酒店
             */
            if (hotelBaseInfo.getHotelGroupId() == null) {
                HotelGroupInfo hotelGroupInfo = new HotelGroupInfo();
                hotelGroupInfo.setChainName(hotelBaseInfo.getHotelName());
                hotelGroupInfo.setEnable(1);
                hotelGroupInfo.setCreateTime(new Date());
                hotelGroupInfo.setCreateUserId("cz");
                res = hotelGroupInfoDao.insert(hotelGroupInfo);
                if (res < 1) {
                    throw new Exception("创建酒店集团失败");
                }
                //赋值集团编号
                hotelBaseInfo.setHotelGroupId(hotelGroupInfo.getChainId());
            }

            //创建酒店
            if (hotelBaseInfo.getHid() == null) {
                hotelBaseInfo.setCreateTime(date);
                hotelBaseInfo.setCreateUserName("cz");
                res = hotelBaseInfoDao.saveHotelBaseInfo(hotelBaseInfo);
                if (res < 1) {
                    throw new Exception("创建酒店失败");
                }
            } else {
                res = hotelBaseInfoDao.editHotelBaseInfo(hotelBaseInfo);
                if (res < 1) {
                    throw new Exception("更新酒店信息失败");
                }
            }

            //创建基础特征


            //


        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData searchHotelList() {
        return null;
    }


    @Override
    public ResponseData saveZhiwen(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String str = param.getString("str");
            RoomImage roomImage = new RoomImage();
            roomImage.setHid(0);
            roomImage.setState(0);
            roomImage.setRoomInfoId(0);
            roomImage.setImages(str);
            Integer integer = roomImageDao.saveRoomImage(roomImage);


        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData searchZhiwen() {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            List<RoomImage> roomImages = roomImageDao.selectBySearch(null);

            responseData.setData(roomImages);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData dayDiff(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            String beginTime = param.getString("beginTime").substring(0, 10) + " 00:00:00";
            String endTime = param.getString("endTime").substring(0, 10) + " 00:00:00";

            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(beginTime, endTime);

            JSONObject jsonObject = new JSONObject();

            JSONArray jsonArray = new JSONArray();

            Calendar cal = Calendar.getInstance();

            String[] week = new String[]{"周末", "周一", "周二", "周三", "周四", "周五", "周六"};

            for (String day : allDayListBetweenDate) {

                Date date = HotelUtils.parseStr2Date(day + " 00:00:00");
                cal.setTime(date);
                int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
                jsonObject = new JSONObject();
                jsonObject.put("date", day);
                jsonObject.put("week", w);
                jsonObject.put("weekName", week[w]);
                jsonObject.put("businessDay", HotelUtils.parseDate2Int(date));

                jsonArray.add(jsonObject);
            }

            responseData.setData(jsonArray);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData findDeliveryRobot(DeliveryRobotSearch deliveryRobotSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = deliveryRobotSearch.getSessionToken();
            TbUserSession user = getTbUserSession(sessionToken);
            if (user == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            if (deliveryRobotSearch.getHid() == null) {
                deliveryRobotSearch.setHid(user.getHid());
            }
            Page<DeliveryRobot> deliveryRobots = deliveryRobotDao.selectBySearch(deliveryRobotSearch);
            responseData.setData(deliveryRobots);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updateDataDeliveryRobot(DeliveryRobot deliveryRobot) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = deliveryRobot.getSessionToken();
            TbUserSession user = getTbUserSession(sessionToken);
            if (user == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            if (deliveryRobot.getId() == null) {
                deliveryRobot.setHid(user.getHid());
                deliveryRobot.setHotelGroupId(user.getHotelGroupId());
                Integer insert = deliveryRobotDao.insert(deliveryRobot);
                if (insert < 1) {
                    throw new Exception("插入数据失败");
                }
            } else {
                Integer update = deliveryRobotDao.update(deliveryRobot);
                if (update < 1) {
                    throw new Exception("更新数据失败");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData findHotelGate(HotelGateSearch hotelGateSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = hotelGateSearch.getSessionToken();
            TbUserSession user = getTbUserSession(sessionToken);
            if (user == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            if (hotelGateSearch.getHid() == null) {
                hotelGateSearch.setHid(user.getHid());
            }
            Page<HotelGate> hotelGates = hotelGateDao.selectBySearch(hotelGateSearch);
            responseData.setData(hotelGates);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updateHotelGate(HotelGate hotelGate) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = hotelGate.getSessionToken();
            TbUserSession user = getTbUserSession(sessionToken);
            if (user == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            if (hotelGate.getId() == null) {
                hotelGate.setHid(user.getHid());
                hotelGate.setHotelGroupId(user.getHotelGroupId());
                Integer insert = hotelGateDao.insert(hotelGate);
                if (insert < 1) {
                    throw new Exception("插入数据失败");
                }
            } else {
                Integer update = hotelGateDao.update(hotelGate);
                if (update < 1) {
                    throw new Exception("更新数据失败");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    public ResponseData addOrUpdateHotelGroupInfo(HotelGroupInfoRequest hotelGroupInfoRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = hotelGroupInfoRequest.getSessionToken();
            TbUserSession user = getTbUserSession(sessionToken);


            if (null == hotelGroupInfoRequest.getUuid() || hotelGroupInfoRequest.getUuid().equals("")) {
                throw new Exception("集团编码不能空");
            }

            HotelGroupInfoSearch hotelGroupInfoSearch = new HotelGroupInfoSearch();
            hotelGroupInfoSearch.setUuid(hotelGroupInfoRequest.getUuid());
            Page<HotelGroupInfo> hotelGroupInfos = hotelGroupInfoDao.selectBySearch(hotelGroupInfoSearch);

            Integer res = 0;
            HotelGroupInfo hotelGroupInfo = new HotelGroupInfo();
            HotelUtils.classCopy(hotelGroupInfoRequest, hotelGroupInfo);
            if (null == hotelGroupInfoRequest.getChainId() || hotelGroupInfoRequest.getChainId() < 1) {
                if (null != hotelGroupInfos && hotelGroupInfos.size() > 0) {
                    throw new Exception("集团编号不能重复");
                }

                hotelGroupInfo.setCreateTime(new Date());
                hotelGroupInfo.setCreateUserId(user.getUserId());
                res = hotelGroupInfoDao.insert(hotelGroupInfo);
                if (res < 1) {
                    throw new Exception(HOTEL_CONST.INSERTERR);
                }
                //todo
                outPlatService.syncIotGroupAndUser(hotelGroupInfo);
            } else {
                if (hotelGroupInfos.size() > 1) {
                    throw new Exception("集团编号不能重复");
                }
                if (hotelGroupInfos.size() == 1) {
                    HotelGroupInfo hotelGroupInfo1 = hotelGroupInfos.get(0);
                    if (!hotelGroupInfo1.getChainId().equals(hotelGroupInfoRequest.getChainId())) {
                        throw new Exception("集团编号不能重复");
                    }
                }

                hotelGroupInfo.setUpdateUserId(user.getUserId());
                res = hotelGroupInfoDao.update(hotelGroupInfo);
                if (res < 1) {
                    throw new Exception(HOTEL_CONST.UPDATEERR);
                }
                IotEnterprise iotEnterprise = new IotEnterprise();
                iotEnterprise.setEnterpriseName(hotelGroupInfo.getChainName());
                iotEnterprise.setPmsHotelId(hotelGroupInfo.getChainId() + "");
                iotEnterprise.setMark(hotelGroupInfo.getChainDesc());
                outPlatService.syncIotHotelEdit(iotEnterprise);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }

    @Override
    public ResponseData getHotelGroupInfo(GetHotelGroupInfoRequest getHotelGroupInfoRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = getHotelGroupInfoRequest.getSessionToken();
            TbUserSession user = getTbUserSession(sessionToken);
            if(null == user){
                responseData.setResult(ER.ERR);
                responseData.setMsg("无效的sessionToken");
                return responseData;
            }
            Integer userHotelGroupId = user.getHotelGroupId();
            Integer hotelGroupId = getHotelGroupInfoRequest.getHotelGroupId();
            if(!hotelGroupId.equals(userHotelGroupId)){
                responseData.setResult(ER.ERR);
                responseData.setMsg("无权限操作");
                return responseData;
            }
            HotelGroupInfo hotelGroupInfo = hotelGroupInfoDao.selectById(hotelGroupId);
            responseData.setData(hotelGroupInfo);
        } catch (Exception e) {
            log.error("getHotelGroupInfo error:{}", e.getMessage(), e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getHotelOrgPrice(BaseRequest baseRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(baseRequest);
            HotelOrgPriceSearch hotelOrgPriceSearch = new HotelOrgPriceSearch();
            hotelOrgPriceSearch.setHid(tbUserSession.getHid());
            Page<HotelOrgPrice> hotelOrgPrices = hotelOrgPriceDao.selectBySearch(hotelOrgPriceSearch);
            if (null == hotelOrgPrices || hotelOrgPrices.size() != 1) {
                throw new Exception(HOTEL_CONST.DATA_LIST_IS_NULL);
            }
            HotelOrgPrice hotelOrgPrice = hotelOrgPrices.get(0);
            HotelOrgRoomTypeSearch hotelOrgRoomTypeSearch = new HotelOrgRoomTypeSearch();
            hotelOrgRoomTypeSearch.setHotelOrgPriceId(hotelOrgPrice.getId());
            Page<HotelOrgRoomType> hotelOrgRoomTypes = hotelOrgRoomTypeDao.selectBySearch(hotelOrgRoomTypeSearch);
            HotelOrgRateCodeSearch hotelOrgRateCodeSearch = new HotelOrgRateCodeSearch();
            hotelOrgRateCodeSearch.setHotelOrgPriceId(hotelOrgPrice.getId());
            Page<HotelOrgRateCode> hotelOrgRateCodes = hotelOrgRateCodeDao.selectBySearch(hotelOrgRateCodeSearch);
            HotelOrgPriceView hotelOrgPriceView = new HotelOrgPriceView();
            HotelUtils.classCopy(hotelOrgPrice, hotelOrgPriceView);
            List<HotelOrgPriceView.HotelOrgRoomTypeView> roomTypeList = new ArrayList<>();
            for (int i = 0; i < hotelOrgRoomTypes.size(); i++) {
                HotelOrgPriceView.HotelOrgRoomTypeView hotelOrgRoomTypeView = new HotelOrgPriceView.HotelOrgRoomTypeView();
                HotelUtils.classCopy(hotelOrgRoomTypes.get(i), hotelOrgRoomTypeView);
                roomTypeList.add(hotelOrgRoomTypeView);
            }
            List<HotelOrgPriceView.HotelOrgRateCodeView> rateCodeViews = new ArrayList<>();
            for (int i = 0; i < hotelOrgRateCodes.size(); i++) {
                HotelOrgPriceView.HotelOrgRateCodeView hotelOrgRateCodeView = new HotelOrgPriceView.HotelOrgRateCodeView();
                HotelUtils.classCopy(hotelOrgRateCodes.get(i), hotelOrgRateCodeView);
                rateCodeViews.add(hotelOrgRateCodeView);
            }
            hotelOrgPriceView.setRoomTypeList(roomTypeList);
            hotelOrgPriceView.setRateCodeList(rateCodeViews);
            responseData.setData(hotelOrgPriceView);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData addOrUpdateHotelOrgPrice(AddOrUpdateHotelOrgPriceRequest addOrUpdateHotelOrgPriceRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(addOrUpdateHotelOrgPriceRequest);
            Integer id = addOrUpdateHotelOrgPriceRequest.getId();
            Integer result = 0;

            List<HotelOrgRoomType> hotelOrgRoomTypeList = new ArrayList<>();
            List<HotelOrgRateCode> hotelOrgRateCodeList = new ArrayList<>();

            //新增
            if (null == id || id < 1) {
                HotelOrgPrice hotelOrgPrice = new HotelOrgPrice();
                HotelUtils.classCopy(addOrUpdateHotelOrgPriceRequest, hotelOrgPrice);
                hotelOrgPrice.setCreateTime(new Date());
                hotelOrgPrice.setCreateUserId(Integer.parseInt(tbUserSession.getUserId()));
                hotelOrgPrice.setState(1);
                if (null != addOrUpdateHotelOrgPriceRequest.getRoomTypeList() && addOrUpdateHotelOrgPriceRequest.getRoomTypeList().size() > 0) {
                    for (int i = 0; i < addOrUpdateHotelOrgPriceRequest.getRoomTypeList().size(); i++) {
                        HotelOrgRoomType hotelOrgRoomType = new HotelOrgRoomType();
                        hotelOrgRoomType.setHid(tbUserSession.getHid());
                        hotelOrgRoomType.setHotelGroupId(tbUserSession.getHotelGroupId());
                        hotelOrgRoomType.setRoomTypeId(addOrUpdateHotelOrgPriceRequest.getRoomTypeList().get(i).getRoomTypeId());
                        hotelOrgRoomType.setSort(addOrUpdateHotelOrgPriceRequest.getRoomTypeList().get(i).getSort());
                        hotelOrgRoomTypeList.add(hotelOrgRoomType);
                    }
                }

                if (null != addOrUpdateHotelOrgPriceRequest.getRateCodeList() && addOrUpdateHotelOrgPriceRequest.getRateCodeList().size() > 0) {
                    for (int i = 0; i < addOrUpdateHotelOrgPriceRequest.getRateCodeList().size(); i++) {
                        HotelOrgRateCode hotelOrgRateCode = new HotelOrgRateCode();
                        hotelOrgRateCode.setHid(tbUserSession.getHid());
                        hotelOrgRateCode.setHotelGroupId(tbUserSession.getHotelGroupId());
                        hotelOrgRateCode.setRateCodeId(addOrUpdateHotelOrgPriceRequest.getRateCodeList().get(i).getRateCodeId());
                        hotelOrgRateCode.setSort(addOrUpdateHotelOrgPriceRequest.getRateCodeList().get(i).getSort());
                        hotelOrgRateCodeList.add(hotelOrgRateCode);
                    }
                }

                this.execteAddOrUpdateHotelOrgPrice(hotelOrgPrice, new ArrayList<>(), new ArrayList<>(), hotelOrgRoomTypeList, hotelOrgRateCodeList);
            } else {
                Page<HotelOrgRoomType> hotelOrgRoomTypes = new Page<>();
                Page<HotelOrgRateCode> hotelOrgRateCodes = new Page<>();
                if (addOrUpdateHotelOrgPriceRequest.getType() != null) {
                    HotelOrgRoomTypeSearch hotelOrgRoomTypeSearch = new HotelOrgRoomTypeSearch();
                    hotelOrgRoomTypeSearch.setHotelOrgPriceId(id);
                    hotelOrgRoomTypes = hotelOrgRoomTypeDao.selectBySearch(hotelOrgRoomTypeSearch);
                    HotelOrgRateCodeSearch hotelOrgRateCodeSearch = new HotelOrgRateCodeSearch();
                    hotelOrgRateCodeSearch.setHotelOrgPriceId(id);
                    hotelOrgRateCodes = hotelOrgRateCodeDao.selectBySearch(hotelOrgRateCodeSearch);
                }
                HotelOrgPrice hotelOrgPrice = new HotelOrgPrice();
                HotelUtils.classCopy(addOrUpdateHotelOrgPriceRequest, hotelOrgPrice);
                hotelOrgPrice.setUpdateTime(new Date());
                hotelOrgPrice.setUpdateUserId(Integer.parseInt(tbUserSession.getUserId()));


                if (addOrUpdateHotelOrgPriceRequest.getType() != null && null != addOrUpdateHotelOrgPriceRequest.getRoomTypeList() && addOrUpdateHotelOrgPriceRequest.getRoomTypeList().size() > 0) {
                    for (int i = 0; i < addOrUpdateHotelOrgPriceRequest.getRoomTypeList().size(); i++) {
                        HotelOrgRoomType hotelOrgRoomType = new HotelOrgRoomType();
                        hotelOrgRoomType.setHid(tbUserSession.getHid());
                        hotelOrgRoomType.setHotelGroupId(tbUserSession.getHotelGroupId());
                        hotelOrgRoomType.setRoomTypeId(addOrUpdateHotelOrgPriceRequest.getRoomTypeList().get(i).getRoomTypeId());
                        hotelOrgRoomType.setSort(addOrUpdateHotelOrgPriceRequest.getRoomTypeList().get(i).getSort());
                        hotelOrgRoomTypeList.add(hotelOrgRoomType);
                    }
                }

                if (addOrUpdateHotelOrgPriceRequest.getType() != null && null != addOrUpdateHotelOrgPriceRequest.getRateCodeList() && addOrUpdateHotelOrgPriceRequest.getRateCodeList().size() > 0) {
                    for (int i = 0; i < addOrUpdateHotelOrgPriceRequest.getRateCodeList().size(); i++) {
                        HotelOrgRateCode hotelOrgRateCode = new HotelOrgRateCode();
                        hotelOrgRateCode.setHid(tbUserSession.getHid());
                        hotelOrgRateCode.setHotelGroupId(tbUserSession.getHotelGroupId());
                        hotelOrgRateCode.setRateCodeId(addOrUpdateHotelOrgPriceRequest.getRateCodeList().get(i).getRateCodeId());
                        hotelOrgRateCode.setSort(addOrUpdateHotelOrgPriceRequest.getRateCodeList().get(i).getSort());
                        hotelOrgRateCodeList.add(hotelOrgRateCode);
                    }
                }

                this.execteAddOrUpdateHotelOrgPrice(hotelOrgPrice, hotelOrgRoomTypes, hotelOrgRateCodes, hotelOrgRoomTypeList, hotelOrgRateCodeList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void execteAddOrUpdateHotelOrgPrice(HotelOrgPrice hotelOrgPrice, List<HotelOrgRoomType> delHotelOrgRoomTypeList, List<HotelOrgRateCode> delHotelOrgRateCodeList, List<HotelOrgRoomType> addHotelOrgRoomTypeList, List<HotelOrgRateCode> addHotelOrgRateCodeList) throws Exception {
        Integer result = 0;
        if (hotelOrgPrice.getId() != null) {
            result = hotelOrgPriceDao.update(hotelOrgPrice);
            if (result < 1) {
                throw new Exception(HOTEL_CONST.INSERTERR);
            }

        } else {
            result = hotelOrgPriceDao.insert(hotelOrgPrice);
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }


        for (int i = 0; i < delHotelOrgRoomTypeList.size(); i++) {
            result = hotelOrgRoomTypeDao.delete(delHotelOrgRoomTypeList.get(i).getId());

            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        for (int i = 0; i < delHotelOrgRateCodeList.size(); i++) {
            result = hotelOrgRateCodeDao.delete(delHotelOrgRateCodeList.get(i).getId());
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        for (int i = 0; i < addHotelOrgRoomTypeList.size(); i++) {
            addHotelOrgRoomTypeList.get(i).setHotelOrgPriceId(hotelOrgPrice.getId());
            result = hotelOrgRoomTypeDao.insert(addHotelOrgRoomTypeList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.INSERTERR);
            }
        }

        for (int i = 0; i < addHotelOrgRateCodeList.size(); i++) {
            addHotelOrgRateCodeList.get(i).setHotelOrgPriceId(hotelOrgPrice.getId());
            result = hotelOrgRateCodeDao.insert(addHotelOrgRateCodeList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.INSERTERR);
            }
        }
    }

    @Override
    public ResponseData getHotelOrgPriceImage(GetHotelOrgPriceImageRuquest getHotelOrgPriceImageRuquest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(getHotelOrgPriceImageRuquest);
            HotelOrgPriceImageSearch hotelOrgPriceImageSearch = new HotelOrgPriceImageSearch();
            BeanUtils.copyProperties(getHotelOrgPriceImageRuquest, hotelOrgPriceImageSearch);
            hotelOrgPriceImageSearch.setHid(tbUserSession.getHid());
            Page<HotelOrgPriceImage> hotelOrgPriceImages = hotelOrgPriceImageDao.selectBySearch(hotelOrgPriceImageSearch);
            responseData.setData(hotelOrgPriceImages);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    public ResponseData getHotelOrgPriceShowInfo(BaseRequest baseRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(baseRequest);
            HotelOrgPriceSearch hotelOrgPriceSearch = new HotelOrgPriceSearch();
            hotelOrgPriceSearch.setHid(tbUserSession.getHid());
            Page<HotelOrgPrice> hotelOrgPrices = hotelOrgPriceDao.selectBySearch(hotelOrgPriceSearch);
            if (null == hotelOrgPrices || hotelOrgPrices.size() != 1) {
                throw new Exception(HOTEL_CONST.DATA_LIST_IS_NULL);
            }
            HotelOrgPrice hotelOrgPrice = hotelOrgPrices.get(0);
            HotelOrgPriceShowView hotelOrgPriceShowView = new HotelOrgPriceShowView();
            hotelOrgPriceShowView.setHotelName(hotelOrgPrice.getHotelName());
            hotelOrgPriceShowView.setDirection(hotelOrgPrice.getOrgType() == 1 ? "x" : "y");
            hotelOrgPriceShowView.setTips(hotelOrgPrice.getHotelDesc());
            hotelOrgPriceShowView.setFontSizeLogo(hotelOrgPrice.getHotelFontSize());
            hotelOrgPriceShowView.setFontSizeTips(hotelOrgPrice.getHotelDescFontSize());
            hotelOrgPriceShowView.setIsRotation(hotelOrgPrice.getIsRotation());
            hotelOrgPriceShowView.setRotationTimes(hotelOrgPrice.getRotationTimes());
            hotelOrgPriceShowView.setPageNum(hotelOrgPrice.getPageNum());
            hotelOrgPriceShowView.setPageChangeTime(hotelOrgPrice.getPageChangeTime());
            List<String> bgList = new ArrayList<>();
            bgList.add(hotelOrgPrice.getOrgBackgroudImage());
            hotelOrgPriceShowView.setBgFirst(bgList);
            /**
             * 查询酒店轮播图和酒店订房二维码等信息
             */

            List<HotelOrgPriceShowView.GroupQrImage> groupQrImageList = new ArrayList<>();
            HotelOrgPriceImageSearch hotelOrgPriceImageSearch = new HotelOrgPriceImageSearch();
            hotelOrgPriceImageSearch.setHid(tbUserSession.getHid());
            Page<HotelOrgPriceImage> hotelOrgPriceImages = hotelOrgPriceImageDao.selectBySearch(hotelOrgPriceImageSearch);
            List<String> lbList = new ArrayList<>();
            hotelOrgPriceImages.forEach(a -> {
                if (a.getBusinessType() == 2) {
                    lbList.add(a.getImagePath());
                } else if (a.getBusinessType() == 3) {
                    HotelOrgPriceShowView.GroupQrImage groupQrImage = new HotelOrgPriceShowView.GroupQrImage();
                    groupQrImage.setSrc(a.getImagePath());
                    groupQrImage.setBottomText(a.getImageName());

                    groupQrImageList.add(groupQrImage);
                }

            });
            hotelOrgPriceShowView.setBgSecond(lbList);
            hotelOrgPriceShowView.setHotelQrCode(groupQrImageList);
            /**
             * 查询展示的房型以及房价码
             */

            HotelOrgRoomTypeSearch hotelOrgRoomTypeSearch = new HotelOrgRoomTypeSearch();
            hotelOrgRoomTypeSearch.setHid(tbUserSession.getHid());
            Page<HotelOrgRoomType> hotelOrgRoomTypes = hotelOrgRoomTypeDao.selectBySearch(hotelOrgRoomTypeSearch);

            HotelOrgPriceShowView.HotelOrgPriceVo hotelOrgPriceVo = new HotelOrgPriceShowView.HotelOrgPriceVo();
            hotelOrgPriceVo.setColumnHeader("房型");
            List<HotelOrgPriceShowView.PriceShowData> priceShowDataList = new ArrayList<>();
            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setState(1);
            roomTypeSearch.setHid(tbUserSession.getHid());
            Page<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);
            Map<Integer, RoomType> roomTypeMap = roomTypes.stream().collect(Collectors.toMap(RoomType::getRoomTypeId, a -> a, (k1, k2) -> k2));
            hotelOrgRoomTypes.forEach(a -> {
                HotelOrgPriceShowView.PriceShowData priceShowData = new HotelOrgPriceShowView.PriceShowData();
                priceShowData.setText(roomTypeMap.get(a.getRoomTypeId()).getRoomTypeName());
                priceShowDataList.add(priceShowData);
            });
            hotelOrgPriceVo.setData(priceShowDataList);
            List<HotelOrgPriceShowView.HotelOrgPriceVo> priceVoList = new ArrayList<>();
            priceVoList.add(hotelOrgPriceVo);

            HotelOrgRateCodeSearch hotelOrgRateCodeSearch = new HotelOrgRateCodeSearch();
            hotelOrgRateCodeSearch.setHid(tbUserSession.getHid());
            Page<HotelOrgRateCode> hotelOrgRateCodes = hotelOrgRateCodeDao.selectBySearch(hotelOrgRateCodeSearch);


            RoomRateCodeSearch roomRateCodeSearch = new RoomRateCodeSearch();
            roomRateCodeSearch.setHid(tbUserSession.getHid());
            roomRateCodeSearch.setRateState(1);
            Page<RoomRateCode> roomRateCodes = roomRateCodeDao.selectBySearch(roomRateCodeSearch);

            Map<Integer, RoomRateCode> rateCodeMap = roomRateCodes.stream().collect(Collectors.toMap(RoomRateCode::getRateId, a -> a, (k1, k2) -> k2));

            /**
             * 查询房价
             */
            RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
            roomDayPriceSearch.setHid(tbUserSession.getHid());
            roomDayPriceSearch.setDayTime(tbUserSession.getBusinessDay());
            List<RoomDayPrice> roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);
            Map<Integer, List<RoomDayPrice>> collect = roomDayPrices.stream().collect(Collectors.groupingBy(RoomDayPrice::getRoomRateId));
            JSONObject data = new JSONObject();
            for (Integer item :
                    collect.keySet()) {
                List<RoomDayPrice> roomDayPrices1 = collect.get(item);
                Map<Integer, List<RoomDayPrice>> collect1 = roomDayPrices1.stream().collect(Collectors.groupingBy(RoomDayPrice::getRoomTypeId));
                JSONObject roomType = new JSONObject();
                for (Integer i : collect1.keySet()) {
                    List<RoomDayPrice> roomDayPrices2 = collect1.get(i);
                    Integer price = 0;
                    if (roomDayPrices2.size() == 1) {
                        price = roomDayPrices2.get(0).getPrice();
                    }
                    roomType.put(i, price);
                    data.put(item, roomType);
                }
            }


            hotelOrgRateCodes.forEach(a -> {
                HotelOrgPriceShowView.HotelOrgPriceVo hotelOrgPriceVo1 = new HotelOrgPriceShowView.HotelOrgPriceVo();
                List<HotelOrgPriceShowView.PriceShowData> priceShowDataList1 = new ArrayList<>();
                hotelOrgPriceVo1.setColumnHeader(rateCodeMap.get(a.getRateCodeId()).getRateCodeName());
                hotelOrgRoomTypes.forEach(roomType -> {
                    HotelOrgPriceShowView.PriceShowData priceShowData = new HotelOrgPriceShowView.PriceShowData();
                    int price = data.containsKey(a.getRateCodeId().toString()) && data.getJSONObject(a.getRateCodeId().toString()).containsKey(roomType.getRoomTypeId().toString()) ? data.getJSONObject(a.getRateCodeId().toString()).getInt(roomType.getRoomTypeId().toString()) : 0;
                    double v = price / 100.0;
                    priceShowData.setText(String.valueOf(v));
                    priceShowDataList1.add(priceShowData);
                });
                hotelOrgPriceVo1.setData(priceShowDataList1);
                priceVoList.add(hotelOrgPriceVo1);
            });
            hotelOrgPriceShowView.setPriceList(priceVoList);
            responseData.setData(hotelOrgPriceShowView);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
