<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="10 seconds">
    <!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
    <!-- scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true -->
    <!-- scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
    <!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
    <!--    <springProperty name="SERVICE_NAME" scope="context" source="spring.application.name"/>-->

    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>
    <springProperty name="LOG_PATH" source="logging.path" defaultValue="/opt/pms/night/logs"/>
    <property name="SERVICE_NAME" value="cz-abs-night" />
<!--    <property name="LOG_PATH" value="/opt/pms/night/logs"/>-->
    <!--文日志件名 -->
    <property name="LOG_NAME" value="${SERVICE_NAME}"/>


    <!-- 颜色设置在本地开发模式用可以用 -->
    <!-- 引用 Spring Boot 的 logback 基础配置 -->
    <!--<include resource="org/springframework/boot/logging/logback/defaults.xml"/>-->
    <!-- 使用SpringBoot 的默认配置修改 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <property name="LOG_LEVEL_PATTERN" value="%5p"/>
    <!-- 带链路追踪日志，先注释。格式化输出：%d 表示日期，%X{tid} SkWalking 链路追踪编号，%thread 表示线程名，%-5level：级别从左显示 5 个字符宽度，%msg：日志消息，%n是换行符 -->
    <!-- <property name="PATTERN_SKWALKING" value="%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } -&#45;&#45; [%thread] [%tid] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>-->

    <!--  console 日志模式  -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- } [%X{traceId}]){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <property name="CONSOLE_LOG_CHARSET" value="${CONSOLE_LOG_CHARSET:-${file.encoding:-UTF-8}}"/>

    <!--  文件日志模式  -->
    <property name="FILE_LOG_PATTERN"
              value="${FILE_LOG_PATTERN:-%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <property name="FILE_LOG_CHARSET" value="${FILE_LOG_CHARSET:-${file.encoding:-UTF-8}}"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>${FILE_LOG_CHARSET}</charset>
        </encoder>
    </appender>

    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>${FILE_LOG_CHARSET}</charset>
        </encoder>
        <!-- 日志文件名 -->
        <file>${LOG_PATH}/${LOG_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 滚动后的日志文件名，使用gz压缩历史日志，节约磁盘-->
            <fileNamePattern>
                ${LOGBACK_ROLLINGPOLICY_FILE_NAME_PATTERN:-${LOG_PATH}/${LOG_NAME}.%d{yyyy-MM-dd}.%i.gz}
            </fileNamePattern>
            <!-- 启动服务时，是否清理历史日志，一般不建议清理 -->
            <cleanHistoryOnStart>${LOGBACK_ROLLINGPOLICY_CLEAN_HISTORY_ON_START:-false}</cleanHistoryOnStart>
            <!-- 日志文件，到达多少容量，进行滚动 -->
            <maxFileSize>${LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE:-50MB}</maxFileSize>
            <!-- 日志文件的总大小，0 表示不限制 -->
            <totalSizeCap>${LOGBACK_ROLLINGPOLICY_TOTAL_SIZE_CAP:-0}</totalSizeCap>
            <!-- 日志文件的保留天数 -->
            <maxHistory>${LOGBACK_ROLLINGPOLICY_MAX_HISTORY:-30}</maxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>INFO</level>
            <!-- 放开下面注释，将会开启强匹配模式 只会记上上面的日志级别；注释掉之后INFO级别日志也可以打出来WARN和ERROR的-->
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>ACCEPT</onMismatch>
        </filter>
    </appender>

    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>${FILE_LOG_CHARSET}</charset>
        </encoder>
        <!-- 日志文件名 -->
        <file>${LOG_PATH}/${LOG_NAME}-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 滚动后的日志文件名，使用gz压缩历史日志，节约磁盘-->
            <fileNamePattern>
                ${LOG_PATH}/${LOGBACK_ROLLINGPOLICY_FILE_NAME_PATTERN:-${LOG_PATH}/${LOG_NAME}-error.%d{yyyy-MM-dd}.%i.gz}
            </fileNamePattern>
            <!-- 启动服务时，是否清理历史日志，一般不建议清理 -->
            <cleanHistoryOnStart>${LOGBACK_ROLLINGPOLICY_CLEAN_HISTORY_ON_START:-false}</cleanHistoryOnStart>
            <!-- 日志文件，到达多少容量，进行滚动 -->
            <maxFileSize>${LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE:-50MB}</maxFileSize>
            <!-- 日志文件的总大小，0 表示不限制 -->
            <totalSizeCap>${LOGBACK_ROLLINGPOLICY_TOTAL_SIZE_CAP:-0}</totalSizeCap>
            <!-- 日志文件的保留天数 -->
            <maxHistory>${LOGBACK_ROLLINGPOLICY_MAX_HISTORY:-30}</maxHistory>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 异步写入日志，提升性能 -->
    <appender name="async_file_info" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志。默认的，如果队列的 80% 已满,则会丢弃 TRACT、DEBUG、INFO 级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度，该值会影响性能。默认值为 256 -->
        <queueSize>256</queueSize>
        <appender-ref ref="file_info"/>
    </appender>
    <!-- 异步写入日志，提升性能 -->
    <appender name="async_file_error" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 不丢失日志。默认的，如果队列的 80% 已满,则会丢弃 TRACT、DEBUG、INFO 级别的日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 更改默认的队列的深度，该值会影响性能。默认值为 256 -->
        <queueSize>256</queueSize>
        <appender-ref ref="file_error"/>
    </appender>

    <logger name="ch.qos.logback" level="info"/>
    <!-- Spring日志级别控制  -->
    <logger name="org.springframework" level="info"/>
    <logger name="com.zaxxer.hikari" level="info"/>
    <logger name="logging.level.com.baomidou.mybatisplus" level="debug"/>
    <logger name="org.mybatis.spring.mapper" level="info"/>
    <logger name="org.apache.ibatis.logging.slf4j.Slf4jImpl" level="debug"/>
    <logger name="org.apache.ibatis" level="DEBUG"/>
    <logger name="org.reflections.Reflections" level="error"/>
    <logger name="org.hibernate" level="info"/>
    <logger name="io.netty" level="info"/>
    <logger name="io.lettuce.core" level="info"/>
    <logger name="com.netflix" level="info"/>
    <logger name="org.apache.http.impl.conn" level="error"/>
    <logger name="org.apache.shiro" level="warn"/>
    <logger name="reactor.netty.resources" level="error"/>
    <logger name="reactor.netty.transport" level="error"/>
    <logger name="reactor.netty.http" level="error"/>
    <logger name="reactor.netty.channel" level="error"/>

    <!-- 本地环境 -->
    <springProfile name="dev">
        <root level="DEBUG">
            <appender-ref ref="console"/>
            <!--            <appender-ref ref="GRPC"/> &lt;!&ndash; 本地环境下，如果不想接入 SkyWalking 日志服务，可以注释掉本行 &ndash;&gt;-->
            <appender-ref ref="async_file_info"/>
            <appender-ref ref="async_file_error"/>
        </root>
    </springProfile>
    
    <!-- 其它环境 -->
    <springProfile name="test,stage,prod,default">
        <root level="INFO">
            <appender-ref ref="console"/>
            <!--            <appender-ref ref="GRPC"/> &lt;!&ndash; 本地环境下，如果不想接入 SkyWalking 日志服务，可以注释掉本行 &ndash;&gt;-->
            <appender-ref ref="async_file_info"/>
            <appender-ref ref="async_file_error"/>
        </root>
    </springProfile>

    <!-- 控制台输出 -->
</configuration>



