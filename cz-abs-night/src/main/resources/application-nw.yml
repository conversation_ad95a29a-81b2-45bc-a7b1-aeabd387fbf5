# 数据库操作
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: 2222f27cfecd13ba718e9e7f1d7774f09338249798125467684a17e22d46e7d426ffc1c96e90ffc0b825c0c467d20613c103381dc73c4869d279eb6718394d7025026fd935d5663adfa9c4daf6f12fe72a562b4f9a8aefeffa3808d5d5da0bb41eb423d212f58436
    username: 172640dd50807625
    password: f874c16987bdd62e351c90dfb9d9da8f
    #配置初始化大小/最小/最大
    initialSize: 1
    minIdle: 1
    maxActive: 20
    #获取连接等待超时时间
    maxWait: 60000
    #间隔多久进行一次检测，检测需要关闭的空闲连接
    timeBetweenEvictionRunsMillis: 60000
    #一个连接在池中最小生存的时间
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 'x'
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    #打开PSCache，并指定每个连接上PSCache的大小。oracle设为true，mysql设为false。分库分表较多推荐设置为false
    poolPreparedStatements: false
    maxPoolPreparedStatementPerConnectionSize: 20
  redis:
    host: ***********
    port: 6379
    password: 7c4fe0d8ccc32af4f67e298a1c80535c
    database: 0
    timeout: 30000
  application:
    name: CZ-ABS-NIGHT
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true


mybatis:
  mapper-locations: classpath*:mapper/*/*.xml,mapper/*.xml  #注意：一定要对应mapper映射xml文件的所在路径
  configuration:
    log-impl: com.pms.czpmsutils.MybatisLoggerImpl

eureka:
  client:
    service-url:
      defaultZone: ************************************/eureka
  instance:
    hostname: CZ-ABS-NIGHT
    prefer-ip-address: true
server:
  port: 8135
  tomcat:
    max-http-post-size: 10485760
logging:
  level:
    org.springframework.scheduling: INFO

pagehelper:
  auto-dialect: true
  supportMethodsArguments: true
