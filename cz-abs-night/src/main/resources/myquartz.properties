# ????org.quartz
# ????scheduler?threadPool?jobStore?plugin???
#
#
#org.quartz.scheduler.instanceName = DefaultQuartzScheduler
org.quartz.scheduler.instanceId = AUTO

#?????Quartz Scheduler??RMI?????????????rmi.export??????true?
#??????????'org.quartz.scheduler.rmi.export'?'org.quartz.scheduler.rmi.proxy'????'true'???????,???????'export '??????
org.quartz.scheduler.rmi.export = false
#??????????????????????org.quartz.scheduler.rmi.proxy??????true???????RMI??????????? - ????localhost???1099?
org.quartz.scheduler.rmi.proxy = false

org.quartz.scheduler.wrapJobExecutionInUserTransaction = false

# ???ThreadPool?????????SimpleThreadPool
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool

# threadCount?threadPriority??setter?????ThreadPool??
# ????  ??????????????? ??1??????,????????????????????? ?????50-100??.
# ??1?100???????????
org.quartz.threadPool.threadCount = 5

# ??? ????5
org.quartz.threadPool.threadPriority = 5

#????true???false?????false?
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread = true

#?????misfired?(??)?????????tolerate(??)???Triggers(???)???????????????????????????????????60000?60??
org.quartz.jobStore.misfireThreshold = 5000

# ????????,RAMJobStore?????????????????????????
#org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore

#???
org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX

#????JobStore????DriverDelegate?????DriverDelegate????????????????JDBC???
# StdJDBCDelegate??????vanilla?JDBC????SQL????????????,??????JDBC?????
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate

#????org.quartz.jobStore.useProperties?????????true?????false?????JDBCJobStore?JobDataMaps????????????
# ???????? - ????????BLOB????????????????????????????????????????String?????BLOB???????
org.quartz.jobStore.useProperties=true

#???
org.quartz.jobStore.tablePrefix = QRTZ_

#?????JobStore??????DataSource?
org.quartz.jobStore.dataSource = qzDS
# ????HikariCP
org.quartz.dataSource.qzDS.provider = hikaricp  
org.quartz.dataSource.qzDS.driver = com.mysql.cj.jdbc.Driver
org.quartz.dataSource.qzDS.URL = 2222f27cfecd13ba718e9e7f1d7774f09338249798125467684a17e22d46e7d426ffc1c96e90ffc0b825c0c467d20613c103381dc73c4869d279eb6718394d7025026fd935d5663adfa9c4daf6f12fe72a562b4f9a8aefeffa3808d5d5da0bb41eb423d212f58436

org.quartz.dataSource.qzDS.user = 172640dd50807625

org.quartz.dataSource.qzDS.password = f874c16987bdd62e351c90dfb9d9da8f

org.quartz.dataSource.qzDS.maxConnections = 10

#????true???????????????Quartz??????????????????????true???????????
org.quartz.jobStore.isClustered=true

#??????:  https://www.w3cschool.cn/quartz_doc/quartz_doc-3x7u2doc.html
