# ????org.quartz
# ????scheduler?threadPool?jobStore?plugin???
#
#
#org.quartz.scheduler.instanceName = DefaultQuartzScheduler
org.quartz.scheduler.instanceId = AUTO

#?????Quartz Scheduler??RMI?????????????rmi.export??????true?
#??????????'org.quartz.scheduler.rmi.export'?'org.quartz.scheduler.rmi.proxy'????'true'???????,???????'export '??????
org.quartz.scheduler.rmi.export = false
#??????????????????????org.quartz.scheduler.rmi.proxy??????true???????RMI??????????? - ????localhost???1099?
org.quartz.scheduler.rmi.proxy = false

org.quartz.scheduler.wrapJobExecutionInUserTransaction = false

# ???ThreadPool?????????SimpleThreadPool
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool

# threadCount?threadPriority??setter?????ThreadPool??
# ????  ??????????????? ??1??????,????????????????????? ?????50-100??.
# ??1?100???????????
org.quartz.threadPool.threadCount = 5

# ??? ????5
org.quartz.threadPool.threadPriority = 5

#????true???false?????false?
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread = true

#?????misfired?(??)?????????tolerate(??)???Triggers(???)???????????????????????????????????60000?60??
org.quartz.jobStore.misfireThreshold = 5000

# ????????,RAMJobStore?????????????????????????
#org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore

#???
org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX

#????JobStore????DriverDelegate?????DriverDelegate????????????????JDBC???
# StdJDBCDelegate??????vanilla?JDBC????SQL????????????,??????JDBC?????
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate

#????org.quartz.jobStore.useProperties?????????true?????false?????JDBCJobStore?JobDataMaps????????????
# ???????? - ????????BLOB????????????????????????????????????????String?????BLOB???????
org.quartz.jobStore.useProperties=true

#???
org.quartz.jobStore.tablePrefix = QRTZ_

#?????JobStore??????DataSource?
org.quartz.jobStore.dataSource = qzDS

org.quartz.dataSource.qzDS.provider = hikaricp  
org.quartz.dataSource.qzDS.driver = com.mysql.cj.jdbc.Driver

#org.quartz.dataSource.qzDS.URL = 2222f27cfecd13ba146f595ba63fd3bd93e2608996a398b2415c39b5192dfcaa68c829723b5aef564240b63955e1e4a9008f16962e87c92ba6f27aa6c03fc6311aa037641b5d01a3e41ec71fa2b989d808053432fe3365915587fb7a73a58789582f8423d2cb3eef
#
#org.quartz.dataSource.qzDS.user = 750a2aa24bf8807efeb959b7d4642fcb
#
#org.quartz.dataSource.qzDS.password = f5dd7cf74eb6fb0afeb959b7d4642fcb

org.quartz.dataSource.qzDS.URL = 2222f27cfecd13ba146f595ba63fd3bd93e2608996a398b2415c39b5192dfcaa68c829723b5aef564240b63955e1e4a9008f16962e87c92ba6f27aa6c03fc6311aa037641b5d01a3e41ec71fa2b989d808053432fe3365915587fb7a73a58789582f8423d2cb3eef

org.quartz.dataSource.qzDS.user = 750a2aa24bf8807efeb959b7d4642fcb

org.quartz.dataSource.qzDS.password = f5dd7cf74eb6fb0afeb959b7d4642fcb

org.quartz.dataSource.qzDS.maxConnections = 10

#????true???????????????Quartz??????????????????????true???????????
org.quartz.jobStore.isClustered=false

#??????:  https://www.w3cschool.cn/quartz_doc/quartz_doc-3x7u2doc.html
