# ????org.quartz
# ????scheduler?threadPool?jobStore?plugin???
#
#
#org.quartz.scheduler.instanceName = DefaultQuartzScheduler
org.quartz.scheduler.instanceId = AUTO

#?????Quartz Scheduler??RMI?????????????rmi.export??????true?
#??????????'org.quartz.scheduler.rmi.export'?'org.quartz.scheduler.rmi.proxy'????'true'???????,???????'export '??????
org.quartz.scheduler.rmi.export = false
#??????????????????????org.quartz.scheduler.rmi.proxy??????true???????RMI??????????? - ????localhost???1099?
org.quartz.scheduler.rmi.proxy = false

org.quartz.scheduler.wrapJobExecutionInUserTransaction = false

# ???ThreadPool?????????SimpleThreadPool
org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool

# threadCount?threadPriority??setter?????ThreadPool??
# ????  ??????????????? ??1??????,????????????????????? ?????50-100??.
# ??1?100???????????
org.quartz.threadPool.threadCount = 5

# ??? ????5
org.quartz.threadPool.threadPriority = 5

#????true???false?????false?
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread = true

#?????misfired?(??)?????????tolerate(??)???Triggers(???)???????????????????????????????????60000?60??
org.quartz.jobStore.misfireThreshold = 5000

# ????????,RAMJobStore?????????????????????????
#org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore

#???
org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX

#????JobStore????DriverDelegate?????DriverDelegate????????????????JDBC???
# StdJDBCDelegate??????vanilla?JDBC????SQL????????????,??????JDBC?????
org.quartz.jobStore.driverDelegateClass=org.quartz.impl.jdbcjobstore.StdJDBCDelegate

#????org.quartz.jobStore.useProperties?????????true?????false?????JDBCJobStore?JobDataMaps????????????
# ???????? - ????????BLOB????????????????????????????????????????String?????BLOB???????
org.quartz.jobStore.useProperties=true

#???
org.quartz.jobStore.tablePrefix = QRTZ_

#?????JobStore??????DataSource?
org.quartz.jobStore.dataSource = qzDS

org.quartz.dataSource.qzDS.provider = hikaricp  
org.quartz.dataSource.qzDS.driver = com.mysql.cj.jdbc.Driver

org.quartz.dataSource.qzDS.URL = 2222f27cfecd13ba718e9e7f1d7774f06f9aeb7b527a1575b83875683ae2414f0a3ddc2b4d0a3bf6b4086f762ef8bb61ba39d77f472a2a0f6d8a62ef0c76e3927dc9fe43ee8bb83532b6e400428422adf7dc2df747804c5fdef888e74dfca515c5597791d3eb88f8af1eef4628d36499

org.quartz.dataSource.qzDS.user = 8336bf9f284f20c3

org.quartz.dataSource.qzDS.password = d1f2341cd0a3bb9cfeb959b7d4642fcb

org.quartz.dataSource.qzDS.maxConnections = 10

#????true???????????????Quartz??????????????????????true???????????
org.quartz.jobStore.isClustered=true

#??????:  https://www.w3cschool.cn/quartz_doc/quartz_doc-3x7u2doc.html
