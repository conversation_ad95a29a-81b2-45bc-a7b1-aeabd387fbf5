<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditResourceDao">
	<resultMap id="NightAuditResourceMap" type="com.pms.czabsnight.bean.NightAuditResource">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="resourceId" jdbcType="INTEGER" column="resource_id"/>
		<result property="resourceName" jdbcType="VARCHAR" column="resource_name"/>
		<result property="checkinNum" jdbcType="INTEGER" column="checkin_num"/>
		<result property="checkinNumMonth" jdbcType="INTEGER" column="checkin_num_month"/>
		<result property="checkinNumYear" jdbcType="INTEGER" column="checkin_num_year"/>
		<result property="money" jdbcType="INTEGER" column="money"/>
		<result property="goodsMoney" jdbcType="INTEGER" column="goods_money"/>
		<result property="foodsMoney" jdbcType="INTEGER" column="foods_money"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="moneyMonth" jdbcType="INTEGER" column="money_month"/>
		<result property="goodsMoneyMonth" jdbcType="INTEGER" column="goods_money_month"/>
		<result property="foodsMoneyMonth" jdbcType="INTEGER" column="foods_money_month"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="moneyYear" jdbcType="INTEGER" column="money_year"/>
		<result property="goodsMoneyYear" jdbcType="INTEGER" column="goods_money_year"/>
		<result property="foodsMoneyYear" jdbcType="INTEGER" column="foods_money_year"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="auditYear" jdbcType="INTEGER" column="audit_year"/>
		<result property="auditYearMonth" jdbcType="INTEGER" column="audit_year_month"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		resource_id,
		resource_name,
		checkin_num,
		checkin_num_month,
		checkin_num_year,
		money,
		goods_money,
		foods_money,
		hid,
		money_month,
		goods_money_month,
		foods_money_month,
		hotel_group_id,
		money_year,
		goods_money_year,
		foods_money_year,
		business_day,
		audit_year,
		audit_year_month
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditResourceMap">
		SELECT
			<include refid="BaseColumn" />
		FROM night_audit_resource
		WHERE
			id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_resource WHERE
		id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditResource" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO night_audit_resource (
			id,
			resource_id,
			resource_name,
			checkin_num,
			checkin_num_month,
			checkin_num_year,
			money,
			goods_money,
			foods_money,
			hid,
			money_month,
			goods_money_month,
			foods_money_month,
			hotel_group_id,
			money_year,
			goods_money_year,
			foods_money_year,
			business_day,
			audit_year,
			audit_year_month
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{resourceId,jdbcType=INTEGER},
			#{resourceName,jdbcType=VARCHAR},
			#{checkinNum,jdbcType=INTEGER},
			#{checkinNumMonth,jdbcType=INTEGER},
			#{checkinNumYear,jdbcType=INTEGER},
			#{money,jdbcType=INTEGER},
			#{goodsMoney,jdbcType=INTEGER},
			#{foodsMoney,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{moneyMonth,jdbcType=INTEGER},
			#{goodsMoneyMonth,jdbcType=INTEGER},
			#{foodsMoneyMonth,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{moneyYear,jdbcType=INTEGER},
			#{goodsMoneyYear,jdbcType=INTEGER},
			#{foodsMoneyYear,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{auditYear,jdbcType=INTEGER},
			#{auditYearMonth,jdbcType=INTEGER}
		)


	</insert>

	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditResourceSearch" resultMap="NightAuditResourceMap">
		SELECT
			<include refid="BaseColumn" />
		FROM night_audit_resource
		<where>
			<if test="id!=null">
				and id = #{id}
			</if>
			<if test="resourceId!=null">
				and resource_id = #{resourceId}
			</if>
			<if test="resourceName!=null and resourceName!=''">
				and resource_name = #{resourceName}
			</if>
			<if test="checkinNum!=null">
				and checkin_num = #{checkinNum}
			</if>
			<if test="checkinNumMonth!=null">
				and checkin_num_month = #{checkinNumMonth}
			</if>
			<if test="checkinNumYear!=null">
				and checkin_num_year = #{checkinNumYear}
			</if>
			<if test="money!=null">
				and money = #{money}
			</if>
			<if test="goodsMoney!=null">
				and goods_money = #{goodsMoney}
			</if>
			<if test="foodsMoney!=null">
				and foods_money = #{foodsMoney}
			</if>
			<if test="hid!=null">
				and hid = #{hid}
			</if>
			<if test="moneyMonth!=null">
				and money_month = #{moneyMonth}
			</if>
			<if test="goodsMoneyMonth!=null">
				and goods_money_month = #{goodsMoneyMonth}
			</if>
			<if test="foodsMoneyMonth!=null">
				and foods_money_month = #{foodsMoneyMonth}
			</if>
			<if test="hotelGroupId!=null">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="moneyYear!=null">
				and money_year = #{moneyYear}
			</if>
			<if test="goodsMoneyYear!=null">
				and goods_money_year = #{goodsMoneyYear}
			</if>
			<if test="foodsMoneyYear!=null">
				and foods_money_year = #{foodsMoneyYear}
			</if>
			<if test="businessDay!=null">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
	</select>


	<select id="selectBySearchGroup" parameterType="com.pms.czabsnight.bean.search.NightAuditResourceSearch" resultMap="NightAuditResourceMap">
		SELECT
		id,
		IFNULL(resource_id,-1) as resource_id,
		resource_name,
		SUM(checkin_num) as checkin_num,
		checkin_num_month,
		checkin_num_year,
		SUM(money) as money,
		SUM(goods_money) as goods_money,
		SUM(foods_money) as foods_money,
		hid,
		SUM(money_month) as money_month,
		SUM(goods_money_month) as goods_money_month,
		SUM(foods_money_month) as foods_money_month,
		hotel_group_id,
		SUM(money_year) as money_year,
		SUM(goods_money_year) as goods_money_year,
		SUM(foods_money_year) as foods_money_year,
		business_day,
		audit_year,
		audit_year_month
		FROM night_audit_resource
		<where>
			<if test="id!=null">
				and id = #{id}
			</if>
			<if test="resourceId!=null">
				and resource_id = #{resourceId}
			</if>
			<if test="resourceName!=null and resourceName!=''">
				and resource_name = #{resourceName}
			</if>
			<if test="checkinNum!=null">
				and checkin_num = #{checkinNum}
			</if>
			<if test="checkinNumMonth!=null">
				and checkin_num_month = #{checkinNumMonth}
			</if>
			<if test="checkinNumYear!=null">
				and checkin_num_year = #{checkinNumYear}
			</if>
			<if test="money!=null">
				and money = #{money}
			</if>
			<if test="goodsMoney!=null">
				and goods_money = #{goodsMoney}
			</if>
			<if test="foodsMoney!=null">
				and foods_money = #{foodsMoney}
			</if>
			<if test="hid!=null">
				and hid = #{hid}
			</if>
			<if test="businessDayMin!=null">
				and business_day >= #{businessDayMin}
			</if>
			<if test="businessDayMax!=null">
				and #{businessDayMax}  >= business_day
			</if>
			<if test="moneyMonth!=null">
				and money_month = #{moneyMonth}
			</if>
			<if test="goodsMoneyMonth!=null">
				and goods_money_month = #{goodsMoneyMonth}
			</if>
			<if test="foodsMoneyMonth!=null">
				and foods_money_month = #{foodsMoneyMonth}
			</if>
			<if test="hotelGroupId!=null">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="moneyYear!=null">
				and money_year = #{moneyYear}
			</if>
			<if test="goodsMoneyYear!=null">
				and goods_money_year = #{goodsMoneyYear}
			</if>
			<if test="foodsMoneyYear!=null">
				and foods_money_year = #{foodsMoneyYear}
			</if>
			<if test="businessDay!=null">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
		group by resource_id
	</select>

	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditResource">
		UPDATE night_audit_resource
		<set>
			<if test="resourceId!=null">
				resource_id = #{resourceId,jdbcType=INTEGER},
			</if>
			<if test="resourceName!=null and resourceName!=''">
				resource_name = #{resourceName,jdbcType=VARCHAR},
			</if>
			<if test="checkinNum!=null">
				checkin_num = #{checkinNum,jdbcType=INTEGER},
			</if>
			<if test="checkinNumMonth!=null">
				checkin_num_month = #{checkinNumMonth,jdbcType=INTEGER},
			</if>
			<if test="checkinNumYear!=null">
				checkin_num_year = #{checkinNumYear,jdbcType=INTEGER},
			</if>
			<if test="money!=null">
				money = #{money,jdbcType=INTEGER},
			</if>
			<if test="goodsMoney!=null">
				goods_money = #{goodsMoney,jdbcType=INTEGER},
			</if>
			<if test="foodsMoney!=null">
				foods_money = #{foodsMoney,jdbcType=INTEGER},
			</if>
			<if test="hid!=null">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="moneyMonth!=null">
				money_month = #{moneyMonth,jdbcType=INTEGER},
			</if>
			<if test="goodsMoneyMonth!=null">
				goods_money_month = #{goodsMoneyMonth,jdbcType=INTEGER},
			</if>
			<if test="foodsMoneyMonth!=null">
				foods_money_month = #{foodsMoneyMonth,jdbcType=INTEGER},
			</if>
			<if test="hotelGroupId!=null">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
			</if>
			<if test="moneyYear!=null">
				money_year = #{moneyYear,jdbcType=INTEGER},
			</if>
			<if test="goodsMoneyYear!=null">
				goods_money_year = #{goodsMoneyYear,jdbcType=INTEGER},
			</if>
			<if test="foodsMoneyYear!=null">
				foods_money_year = #{foodsMoneyYear,jdbcType=INTEGER},
			</if>
			<if test="businessDay!=null">
				business_day = #{businessDay,jdbcType=INTEGER},
			</if>
			<if test="auditYear!=null">
				audit_year = #{auditYear,jdbcType=INTEGER},
			</if>
			<if test="auditYearMonth!=null">
				audit_year_month = #{auditYearMonth,jdbcType=INTEGER}
			</if>
		</set>
		WHERE
			id = #{id,jdbcType=INTEGER}
	</update>

</mapper>
