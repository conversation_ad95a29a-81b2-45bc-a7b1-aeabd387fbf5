<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditAutopreDao">
	<resultMap id="NightAuditAutopreMap" type="com.pms.czabsnight.bean.NightAuditAutopre">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="money" jdbcType="INTEGER" column="money"/>
		<result property="finishMoney" jdbcType="INTEGER" column="finish_money"/>
		<result property="type" jdbcType="INTEGER" column="type"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="auditYear" jdbcType="INTEGER" column="audit_year"/>
		<result property="auditYearMonth" jdbcType="INTEGER" column="audit_year_month"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		hotel_group_id,
		money,
		finish_money,
		type,
		business_day,
		audit_year,
		audit_year_month
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditAutopreMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_audit_autopre 
		WHERE 
			id = #{id} 
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_autopre WHERE
		id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditAutopre" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO night_audit_autopre (
			id,
			hid,
			hotel_group_id,
			money,
			finish_money,
			type,
			business_day,
			audit_year,
			audit_year_month
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{money,jdbcType=INTEGER},
			#{finishMoney,jdbcType=INTEGER},
			#{type,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{auditYear,jdbcType=INTEGER},
			#{auditYearMonth,jdbcType=INTEGER}
		)
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditAutopreSearch" resultMap="NightAuditAutopreMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_audit_autopre
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="money!=null and money!=''">
				and money = #{money}
			</if>
			<if test="finishMoney!=null and finishMoney!=''">
				and finish_money = #{finishMoney}
			</if>
			<if test="type!=null and type!=''">
				and type = #{type}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null and auditYear!=''">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null and auditYearMonth!=''">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditAutopre">
		UPDATE night_audit_autopre 
			<set>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="money!=null and money!=''">
				money = #{money,jdbcType=INTEGER},
				</if>
				<if test="finishMoney!=null and finishMoney!=''">
				finish_money = #{finishMoney,jdbcType=INTEGER},
				</if>
				<if test="type!=null and type!=''">
				type = #{type,jdbcType=INTEGER},
				</if>
				<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER},
				</if>
				<if test="auditYear!=null and auditYear!=''">
				audit_year = #{auditYear,jdbcType=INTEGER},
				</if>
				<if test="auditYearMonth!=null and auditYearMonth!=''">
				audit_year_month = #{auditYearMonth,jdbcType=INTEGER}
				</if>
			</set>
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</update>
	
</mapper>