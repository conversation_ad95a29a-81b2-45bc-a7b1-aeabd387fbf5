<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditRoomTypeDao">
	<resultMap id="NightAuditRoomTypeMap" type="com.pms.czabsnight.bean.NightAuditRoomType">
		<result property="nightAuditRoomTypeId" jdbcType="INTEGER" column="night_audit_room_type_id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="roomTypeId" jdbcType="INTEGER" column="room_type_id"/>
		<result property="roomTypeName" jdbcType="VARCHAR" column="room_type_name"/>
		<result property="totalRoomCount" jdbcType="INTEGER" column="total_room_count"/>
		<result property="openRoomCount" jdbcType="INTEGER" column="open_room_count"/>
		<result property="repairRoomCount" jdbcType="INTEGER" column="repair_room_count"/>
		<result property="noserviceRoomCount" jdbcType="INTEGER" column="noservice_room_count"/>
		<result property="selfRoomCount" jdbcType="INTEGER" column="self_room_count"/>
		<result property="totalAmount" jdbcType="INTEGER" column="total_amount"/>
		<result property="averagePrice" jdbcType="INTEGER" column="average_price"/>
		<result property="openRate" jdbcType="DECIMAL" column="open_rate"/>
		<result property="revpar" jdbcType="DECIMAL" column="revpar"/>
		<result property="goodsMoney" jdbcType="INTEGER" column="goods_money"/>
		<result property="foodsMoney" jdbcType="INTEGER" column="foods_money"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="auditYear" jdbcType="INTEGER" column="audit_year"/>
		<result property="auditYearMonth" jdbcType="INTEGER" column="audit_year_month"/>
	</resultMap>
	<sql id="BaseColumn">
		night_audit_room_type_id,
		hid,
		hotel_group_id,
		room_type_id,
		room_type_name,
		total_room_count,
		open_room_count,
		repair_room_count,
		noservice_room_count,
		self_room_count,
		total_amount,
		average_price,
		open_rate,
		revpar,
		goods_money,
		foods_money,
		business_day,
		audit_year,
		audit_year_month
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditRoomTypeMap">
		SELECT
			<include refid="BaseColumn" />
		FROM night_audit_room_type
		WHERE
			night_audit_room_type_id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_room_type WHERE
		night_audit_room_type_id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditRoomType" useGeneratedKeys="true" keyProperty="nightAuditRoomTypeId">
		INSERT INTO night_audit_room_type (
			night_audit_room_type_id,
			hid,
			hotel_group_id,
			room_type_id,
			room_type_name,
			total_room_count,
			open_room_count,
			repair_room_count,
			noservice_room_count,
			self_room_count,
			total_amount,
			average_price,
			open_rate,
			revpar,
			goods_money,
			foods_money,
			business_day,
			audit_year,
			audit_year_month
		) VALUES (
			#{nightAuditRoomTypeId,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{roomTypeId,jdbcType=INTEGER},
			#{roomTypeName,jdbcType=VARCHAR},
			#{totalRoomCount,jdbcType=INTEGER},
			#{openRoomCount,jdbcType=INTEGER},
			#{repairRoomCount,jdbcType=INTEGER},
			#{noserviceRoomCount,jdbcType=INTEGER},
			#{selfRoomCount,jdbcType=INTEGER},
			#{totalAmount,jdbcType=INTEGER},
			#{averagePrice,jdbcType=INTEGER},
			#{openRate,jdbcType=DECIMAL},
			#{revpar,jdbcType=DECIMAL},
			#{goodsMoney,jdbcType=INTEGER},
			#{foodsMoney,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{auditYear,jdbcType=INTEGER},
			#{auditYearMonth,jdbcType=INTEGER}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditRoomTypeSearch" resultMap="NightAuditRoomTypeMap">
		SELECT
			<include refid="BaseColumn" />
		FROM night_audit_room_type
		<where>
			<if test="nightAuditRoomTypeId!=null and nightAuditRoomTypeId!=''">
				and night_audit_room_type_id = #{nightAuditRoomTypeId}
			</if>
			<if test="hid!=null">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="roomTypeId!=null">
				and room_type_id = #{roomTypeId}
			</if>
			<if test="roomTypeName!=null and roomTypeName!=''">
				and room_type_name = #{roomTypeName}
			</if>
			<if test="totalRoomCount!=null and totalRoomCount!=''">
				and total_room_count = #{totalRoomCount}
			</if>
			<if test="openRoomCount!=null and openRoomCount!=''">
				and open_room_count = #{openRoomCount}
			</if>
			<if test="repairRoomCount!=null and repairRoomCount!=''">
				and repair_room_count = #{repairRoomCount}
			</if>
			<if test="noserviceRoomCount!=null and noserviceRoomCount!=''">
				and noservice_room_count = #{noserviceRoomCount}
			</if>
			<if test="selfRoomCount!=null and selfRoomCount!=''">
				and self_room_count = #{selfRoomCount}
			</if>
			<if test="totalAmount!=null and totalAmount!=''">
				and total_amount = #{totalAmount}
			</if>
			<if test="averagePrice!=null and averagePrice!=''">
				and average_price = #{averagePrice}
			</if>
			<if test="openRate!=null and openRate!=''">
				and open_rate = #{openRate}
			</if>
			<if test="revpar!=null and revpar!=''">
				and revpar = #{revpar}
			</if>
			<if test="goodsMoney!=null and goodsMoney!=''">
				and goods_money = #{goodsMoney}
			</if>
			<if test="foodsMoney!=null and foodsMoney!=''">
				and foods_money = #{foodsMoney}
			</if>
			<if test="businessDay!=null">
				and business_day = #{businessDay}
			</if>
			<if test="businessDayMin!=null">
				and business_day >= #{businessDayMin}
			</if>
			<if test="businessDayMax!=null">
				and #{businessDayMax}  >= business_day
			</if>
			<if test="auditYear!=null ">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
	</select>

	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditRoomType">
		UPDATE night_audit_room_type
			<set>
				<if test="hid!=null">
					hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null">
					hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="roomTypeId!=null">
					room_type_id = #{roomTypeId,jdbcType=INTEGER},
				</if>
				<if test="roomTypeName!=null and roomTypeName!=''">
					room_type_name = #{roomTypeName,jdbcType=VARCHAR},
				</if>
				<if test="totalRoomCount!=null">
					total_room_count = #{totalRoomCount,jdbcType=INTEGER},
				</if>
				<if test="openRoomCount!=null">
					open_room_count = #{openRoomCount,jdbcType=INTEGER},
				</if>
				<if test="repairRoomCount!=null">
					repair_room_count = #{repairRoomCount,jdbcType=INTEGER},
				</if>
				<if test="noserviceRoomCount!=null">
					noservice_room_count = #{noserviceRoomCount,jdbcType=INTEGER},
				</if>
				<if test="selfRoomCount!=null">
					self_room_count = #{selfRoomCount,jdbcType=INTEGER},
				</if>
				<if test="totalAmount!=null">
					total_amount = #{totalAmount,jdbcType=INTEGER},
				</if>
				<if test="averagePrice!=null">
					average_price = #{averagePrice,jdbcType=INTEGER},
				</if>
				<if test="openRate!=null">
					open_rate = #{openRate,jdbcType=DECIMAL},
				</if>
				<if test="revpar!=null">
					revpar = #{revpar,jdbcType=DECIMAL},
				</if>
				<if test="goodsMoney!=null">
					goods_money = #{goodsMoney,jdbcType=INTEGER},
				</if>
				<if test="foodsMoney!=null">
					foods_money = #{foodsMoney,jdbcType=INTEGER},
				</if>
				<if test="businessDay!=null">
					business_day = #{businessDay,jdbcType=INTEGER},
				</if>
				<if test="auditYear!=null">
					audit_year = #{auditYear,jdbcType=INTEGER},
				</if>
				<if test="auditYearMonth!=null">
					audit_year_month = #{auditYearMonth,jdbcType=INTEGER}
				</if>
			</set>
		WHERE
			night_audit_room_type_id = #{nightAuditRoomTypeId,jdbcType=INTEGER}
	</update>

	<!--查询当前房型的营业汇总-->
	<select id="searchRoomTypeRevenueSummary" parameterType="com.pms.czaccount.bean.account.search.AccountSearch" resultType="com.pms.czaccount.bean.account.Account">
		SELECT SUM(account.price) as price,room_type_id as roomTypeId,
		 pay_class_id as payClassId,pay_class_name as payClassName,pay_code_id as payCodeId,pay_code_name as payCodeName
		 FROM account
		WHERE
			hid = ${hid}
			<if test="accountType!=null">
				and account_type =${accountType}
			</if>
			and pay_type = ${payType}
			AND business_day = ${businessDay}
			<if test="payType==1">
				GROUP BY room_type_id
			</if>
			<if test="payType==2">
				GROUP BY
				<if test="payClassId==1">
					pay_class_id
				</if>
				<if test="payCodeId==1">
					pay_code_id
				</if>
			</if>
	</select>


	<select id="selectBySearchSummary" parameterType="com.pms.czabsnight.bean.search.NightAuditRoomTypeSearch" resultMap="NightAuditRoomTypeMap">
		SELECT
		night_audit_room_type_id,
		hid,
		hotel_group_id,
		room_type_id,
		room_type_name,
		SUM(total_room_count) as total_room_count,
		SUM(open_room_count) as open_room_count,
		SUM(repair_room_count) as repair_room_count,
		SUM(noservice_room_count) as noservice_room_count,
		SUM(self_room_count) as self_room_count,
		SUM(total_amount) as total_amount,
		SUM(average_price) as average_price,
		count(1) as revpar,
		SUM(open_rate) as open_rate,
		revpar,
		SUM(goods_money) as goods_money,
		SUM(foods_money) as foods_money,
		business_day,
		audit_year,
		audit_year_month
		FROM night_audit_room_type
		<where>
			<if test="nightAuditRoomTypeId!=null and nightAuditRoomTypeId!=''">
				and night_audit_room_type_id = #{nightAuditRoomTypeId}
			</if>
			<if test="hid!=null">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="roomTypeId!=null">
				and room_type_id = #{roomTypeId}
			</if>
			<if test="roomTypeName!=null and roomTypeName!=''">
				and room_type_name = #{roomTypeName}
			</if>
			<if test="totalRoomCount!=null and totalRoomCount!=''">
				and total_room_count = #{totalRoomCount}
			</if>
			<if test="openRoomCount!=null and openRoomCount!=''">
				and open_room_count = #{openRoomCount}
			</if>
			<if test="repairRoomCount!=null and repairRoomCount!=''">
				and repair_room_count = #{repairRoomCount}
			</if>
			<if test="noserviceRoomCount!=null and noserviceRoomCount!=''">
				and noservice_room_count = #{noserviceRoomCount}
			</if>
			<if test="selfRoomCount!=null and selfRoomCount!=''">
				and self_room_count = #{selfRoomCount}
			</if>
			<if test="totalAmount!=null and totalAmount!=''">
				and total_amount = #{totalAmount}
			</if>
			<if test="averagePrice!=null and averagePrice!=''">
				and average_price = #{averagePrice}
			</if>
			<if test="openRate!=null and openRate!=''">
				and open_rate = #{openRate}
			</if>
			<if test="revpar!=null and revpar!=''">
				and revpar = #{revpar}
			</if>
			<if test="goodsMoney!=null and goodsMoney!=''">
				and goods_money = #{goodsMoney}
			</if>
			<if test="foodsMoney!=null and foodsMoney!=''">
				and foods_money = #{foodsMoney}
			</if>
			<if test="businessDay!=null">
				and business_day = #{businessDay}
			</if>
			<if test="businessDayMin!=null">
				and business_day >= #{businessDayMin}
			</if>
			<if test="businessDayMax!=null">
				and #{businessDayMax}  >= business_day
			</if>
			<if test="auditYear!=null ">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null">
				and audit_year_month = #{auditYearMonth}
			</if>

		</where>
		<if test="groupType == 1">
			GROUP BY room_type_id
		</if>
	</select>

</mapper>
