<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightStepDao">
	<resultMap id="NightStepMap" type="com.pms.czabsnight.bean.NightStep">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="nightSettingId" jdbcType="INTEGER" column="night_setting_id"/>
		<result property="nightSettingName" jdbcType="VARCHAR" column="night_setting_name"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="state" jdbcType="INTEGER" column="state"/>
		<result property="beginTime" jdbcType="TIMESTAMP" column="begin_time"/>
		<result property="endTime" jdbcType="TIMESTAMP" column="end_time"/>
		<result property="finishType" jdbcType="VARCHAR" column="finish_type"/>
		<result property="createUserId" jdbcType="VARCHAR" column="create_user_id"/>
		<result property="createUserName" jdbcType="VARCHAR" column="create_user_name"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		night_setting_id,
		night_setting_name,
		hid,
		hotel_group_id,
		state,
		begin_time,
		end_time,
		finish_type,
		create_user_id,
		create_user_name,
		business_day
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightStepMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_step 
		WHERE 
			id = #{id} 
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_step WHERE
		id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightStep" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO night_step (
			night_setting_id,
			night_setting_name,
			hid,
			hotel_group_id,
			state,
			begin_time,
			end_time,
			finish_type,
			create_user_id,
			create_user_name,
			business_day
		) VALUES (
			#{nightSettingId,jdbcType=INTEGER},
			#{nightSettingName,jdbcType=VARCHAR},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{state,jdbcType=INTEGER},
			#{beginTime,jdbcType=TIMESTAMP},
			#{endTime,jdbcType=TIMESTAMP},
			#{finishType,jdbcType=INTEGER},
			#{createUserId,jdbcType=VARCHAR},
			#{createUserName,jdbcType=VARCHAR},
			#{businessDay,jdbcType=INTEGER}
		)
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightStepSearch" resultMap="NightStepMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_step
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="nightSettingId!=null and nightSettingId!=''">
				and night_setting_id = #{nightSettingId}
			</if>
			<if test="nightSettingName!=null and nightSettingName!=''">
				and night_setting_name = #{nightSettingName}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="state!=null ">
				and state = #{state}
			</if>
			<if test="beginTime!=null">
				and begin_time = #{beginTime}
			</if>
			<if test="endTime!=null">
				and end_time = #{endTime}
			</if>
			<if test="finishType!=null and finishType!=''">
				and finish_type = #{finishType}
			</if>
			<if test="createUserId!=null and createUserId!=''">
				and create_user_id = #{createUserId}
			</if>
			<if test="createUserName!=null and createUserName!=''">
				and create_user_name = #{createUserName}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
		</where>
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightStep">
		UPDATE night_step 
			<set>
				<if test="nightSettingId!=null and nightSettingId!=''">
				night_setting_id = #{nightSettingId,jdbcType=INTEGER},
				</if>
				<if test="nightSettingName!=null and nightSettingName!=''">
				night_setting_name = #{nightSettingName,jdbcType=VARCHAR},
				</if>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="state!=null ">
					state = #{state},
				</if>
				<if test="beginTime!=null">
				begin_time = #{beginTime,jdbcType=TIMESTAMP},
				</if>
				<if test="endTime!=null">
				end_time = #{endTime,jdbcType=TIMESTAMP},
				</if>
				<if test="finishType!=null and finishType!=''">
				finish_type = #{finishType},
				</if>
				<if test="createUserId!=null and createUserId!=''">
				create_user_id = #{createUserId,jdbcType=VARCHAR},
				</if>
				<if test="createUserName!=null and createUserName!=''">
				create_user_name = #{createUserName,jdbcType=VARCHAR},
				</if>
				<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER}
				</if>
			</set>
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</update>
	
</mapper>