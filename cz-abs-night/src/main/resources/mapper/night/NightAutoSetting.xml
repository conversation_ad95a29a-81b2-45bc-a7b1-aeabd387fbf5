<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAutoSettingDao">
    <resultMap id="NightAutoSettingMap" type="com.pms.czabsnight.bean.NightAutoSetting">
        <result property="id" jdbcType="INTEGER" column="id"/>
        <result property="hid" jdbcType="INTEGER" column="hid"/>
        <result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
        <result property="autoNight" jdbcType="INTEGER" column="auto_night"/>
        <result property="autoTime" jdbcType="VARCHAR" column="auto_time"/>
        <result property="antoNightCallTime" jdbcType="INTEGER" column="anto_night_call_time"/>
        <result property="notArrivedNoPayExecute" jdbcType="INTEGER" column="not_arrived_no_pay_execute"/>
        <result property="registPersonExecute" jdbcType="INTEGER" column="regist_person_execute"/>
        <result property="notArrivedPayExecute" jdbcType="INTEGER" column="not_arrived_pay_execute"/>
        <result property="notLeaveExecute" jdbcType="INTEGER" column="not_leave_execute"/>
        <result property="hourRoomExecute" jdbcType="INTEGER" column="hour_room_execute"/>
        <result property="ooRoomExecute" jdbcType="INTEGER" column="oo_room_execute"/>
        <result property="roomAddAccount" jdbcType="INTEGER" column="room_add_account"/>
    </resultMap>
    <sql id="BaseColumn">
        id
        ,
		hid,
		hotel_group_id,
		auto_night,
		auto_time,
		anto_night_call_time,
		not_arrived_no_pay_execute,
		regist_person_execute,
		not_arrived_pay_execute,
		not_leave_execute,
		hour_room_execute,
		oo_room_execute,
		room_add_account
    </sql>
    <select id="selectById" parameterType="java.lang.Integer" resultMap="NightAutoSettingMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM night_auto_setting
        WHERE
        id = #{id}
    </select>

    <!-- 按Id删除 -->
    <delete id="delete" parameterType="java.lang.Integer">
        DELETE
        FROM night_auto_setting
        WHERE id = #{id}
    </delete>
    <!-- 插入 -->
    <insert id="insert" parameterType="com.pms.czabsnight.bean.NightAutoSetting" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO night_auto_setting (id,
                                        hid,
                                        hotel_group_id,
                                        auto_night,
                                        auto_time,
                                        anto_night_call_time,
                                        not_arrived_no_pay_execute,
                                        regist_person_execute,
                                        not_arrived_pay_execute,
                                        not_leave_execute,
                                        hour_room_execute,
                                        oo_room_execute,
                                        room_add_account)
        VALUES (#{id,jdbcType=INTEGER},
                #{hid,jdbcType=INTEGER},
                #{hotelGroupId,jdbcType=INTEGER},
                #{autoNight,jdbcType=INTEGER},
                #{autoTime,jdbcType=VARCHAR},
                #{antoNightCallTime,jdbcType=INTEGER},
                #{notArrivedNoPayExecute,jdbcType=INTEGER},
                #{registPersonExecute,jdbcType=INTEGER},
                #{notArrivedPayExecute,jdbcType=INTEGER},
                #{notLeaveExecute,jdbcType=INTEGER},
                #{hourRoomExecute,jdbcType=INTEGER},
                #{ooRoomExecute,jdbcType=INTEGER},
                #{roomAddAccount,jdbcType=INTEGER})

    </insert>

    <select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAutoSettingSearch"
            resultMap="NightAutoSettingMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM night_auto_setting
        <where>
            <if test="id!=null">
                and id = #{id}
            </if>
            <if test="hid!=null">
                and hid = #{hid}
            </if>
            <if test="hotelGroupId!=null">
                and hotel_group_id = #{hotelGroupId}
            </if>
            <if test="autoNight!=null">
                and auto_night = #{autoNight}
            </if>
            <if test="autoTime!=null and autoTime!=''">
                and auto_time = #{autoTime}
            </if>
        </where>
    </select>

    <!-- 更新 -->
    <update id="update" parameterType="com.pms.czabsnight.bean.NightAutoSetting">
        UPDATE night_auto_setting
        <set>
            <if test="hid!=null">
                hid = #{hid,jdbcType=INTEGER},
            </if>
            <if test="hotelGroupId!=null">
                hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
            </if>
            <if test="autoNight!=null">
                auto_night = #{autoNight,jdbcType=INTEGER},
            </if>
            <if test="autoTime!=null and autoTime!=''">
                auto_time = #{autoTime,jdbcType=VARCHAR},
            </if>
            <if test="antoNightCallTime!=null">
                anto_night_call_time = #{antoNightCallTime,jdbcType=INTEGER},
            </if>
            <if test="notArrivedNoPayExecute!=null">
                not_arrived_no_pay_execute = #{notArrivedNoPayExecute,jdbcType=INTEGER},
            </if>
            <if test="registPersonExecute!=null">
                regist_person_execute = #{registPersonExecute,jdbcType=INTEGER},
            </if>
            <if test="notArrivedPayExecute!=null">
                not_arrived_pay_execute = #{notArrivedPayExecute,jdbcType=INTEGER},
            </if>
            <if test="notLeaveExecute!=null">
                not_leave_execute = #{notLeaveExecute,jdbcType=INTEGER},
            </if>
            <if test="hourRoomExecute!=null">
                hour_room_execute = #{hourRoomExecute,jdbcType=INTEGER},
            </if>
            <if test="ooRoomExecute!=null">
                oo_room_execute = #{ooRoomExecute,jdbcType=INTEGER},
            </if>
            <if test="roomAddAccount!=null">
                room_add_account = #{roomAddAccount,jdbcType=INTEGER}
            </if>
        </set>
        WHERE
        id = #{id,jdbcType=INTEGER}
    </update>

</mapper>