<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditDailyRegistRecordDao">
	<resultMap id="NightAuditDailyRegistRecordMap" type="com.pms.czabsnight.bean.NightAuditDailyRegistRecord">
		<result property="nightAuditDailyRegistRecordId" jdbcType="INTEGER" column="night_audit_daily_regist_record_id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="payClassId" jdbcType="VARCHAR" column="pay_class_id"/>
		<result property="payClassName" jdbcType="VARCHAR" column="pay_class_name"/>
		<result property="payCodeId" jdbcType="VARCHAR" column="pay_code_id"/>
		<result property="payCodeName" jdbcType="VARCHAR" column="pay_code_name"/>
		<result property="totalAmount" jdbcType="INTEGER" column="total_amount"/>
		<result property="totalMonthAmount" jdbcType="INTEGER" column="total_month_amount"/>
		<result property="totalYearAmount" jdbcType="INTEGER" column="total_year_amount"/>
		<result property="lastMonthAmount" jdbcType="INTEGER" column="last_month_amount"/>
		<result property="lastYearAmount" jdbcType="INTEGER" column="last_year_amount"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="auditYear" jdbcType="INTEGER" column="audit_year"/>
		<result property="auditYearMonth" jdbcType="INTEGER" column="audit_year_month"/>
	</resultMap>
	<sql id="BaseColumn">
		night_audit_daily_regist_record_id,
		hid,
		hotel_group_id,
		pay_class_id,
		pay_class_name,
		pay_code_id,
		pay_code_name,
		total_amount,
		total_month_amount,
		total_year_amount,
		last_month_amount,
		last_year_amount,
		business_day,
		audit_year,
		audit_year_month
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditDailyRegistRecordMap">
		SELECT
			<include refid="BaseColumn" />
		FROM night_audit_daily_regist_record
		WHERE
			night_audit_daily_regist_record_id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_daily_regist_record WHERE
		night_audit_daily_regist_record_id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditDailyRegistRecord" useGeneratedKeys="true" keyProperty="nightAuditDailyRegistRecordId">
		INSERT INTO night_audit_daily_regist_record (
			night_audit_daily_regist_record_id,
			hid,
			hotel_group_id,
			pay_class_id,
			pay_class_name,
			pay_code_id,
			pay_code_name,
			total_amount,
			total_month_amount,
			total_year_amount,
			last_month_amount,
			last_year_amount,
			business_day,
			audit_year,
			audit_year_month
		) VALUES (
			#{nightAuditDailyRegistRecordId,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{payClassId,jdbcType=VARCHAR},
			#{payClassName,jdbcType=VARCHAR},
			#{payCodeId,jdbcType=VARCHAR},
			#{payCodeName,jdbcType=VARCHAR},
			#{totalAmount,jdbcType=INTEGER},
			#{totalMonthAmount,jdbcType=INTEGER},
			#{totalYearAmount,jdbcType=INTEGER},
			#{lastMonthAmount,jdbcType=INTEGER},
			#{lastYearAmount,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{auditYear,jdbcType=INTEGER},
			#{auditYearMonth,jdbcType=INTEGER}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditDailyRegistRecordSearch" resultMap="NightAuditDailyRegistRecordMap">
		SELECT
			<include refid="BaseColumn" />
		FROM night_audit_daily_regist_record
		<where>
			<if test="nightAuditDailyRegistRecordId!=null and nightAuditDailyRegistRecordId!=''">
				and night_audit_daily_regist_record_id = #{nightAuditDailyRegistRecordId}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="payClassId!=null and payClassId!=''">
				and pay_class_id = #{payClassId}
			</if>
			<if test="payClassName!=null and payClassName!=''">
				and pay_class_name = #{payClassName}
			</if>
			<if test="payCodeId!=null and payCodeId!=''">
				and pay_code_id = #{payCodeId}
			</if>
			<if test="payCodeName!=null and payCodeName!=''">
				and pay_code_name = #{payCodeName}
			</if>
			<if test="totalAmount!=null and totalAmount!=''">
				and total_amount = #{totalAmount}
			</if>
			<if test="totalMonthAmount!=null and totalMonthAmount!=''">
				and total_month_amount = #{totalMonthAmount}
			</if>
			<if test="totalYearAmount!=null and totalYearAmount!=''">
				and total_year_amount = #{totalYearAmount}
			</if>
			<if test="lastMonthAmount!=null and lastMonthAmount!=''">
				and last_month_amount = #{lastMonthAmount}
			</if>
			<if test="lastYearAmount!=null and lastYearAmount!=''">
				and last_year_amount = #{lastYearAmount}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null and auditYear!=''">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null and auditYearMonth!=''">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
	</select>

	<select id="selectBySearchSummary" parameterType="com.pms.czabsnight.bean.search.NightAuditDailyRegistRecordSearch" resultMap="NightAuditDailyRegistRecordMap">
		SELECT
		night_audit_daily_regist_record_id,
		hid,
		hotel_group_id,
		pay_class_id,
		pay_class_name,
		pay_code_id,
		pay_code_name,
		sum(total_amount) as total_amount,
		total_month_amount,
		total_year_amount,
		last_month_amount,
		last_year_amount,
		business_day,
		audit_year,
		audit_year_month
		FROM night_audit_daily_regist_record
		<where>
			<if test="nightAuditDailyRegistRecordId!=null and nightAuditDailyRegistRecordId!=''">
				and night_audit_daily_regist_record_id = #{nightAuditDailyRegistRecordId}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="payClassId!=null and payClassId!=''">
				and pay_class_id = #{payClassId}
			</if>
			<if test="payClassName!=null and payClassName!=''">
				and pay_class_name = #{payClassName}
			</if>
			<if test="payCodeId!=null and payCodeId!=''">
				and pay_code_id = #{payCodeId}
			</if>
			<if test="payCodeName!=null and payCodeName!=''">
				and pay_code_name = #{payCodeName}
			</if>
			<if test="totalAmount!=null and totalAmount!=''">
				and total_amount = #{totalAmount}
			</if>
			<if test="totalMonthAmount!=null and totalMonthAmount!=''">
				and total_month_amount = #{totalMonthAmount}
			</if>
			<if test="totalYearAmount!=null and totalYearAmount!=''">
				and total_year_amount = #{totalYearAmount}
			</if>
			<if test="lastMonthAmount!=null and lastMonthAmount!=''">
				and last_month_amount = #{lastMonthAmount}
			</if>
			<if test="lastYearAmount!=null and lastYearAmount!=''">
				and last_year_amount = #{lastYearAmount}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="maxBusinessDay!=null ">
				and #{maxBusinessDay} >=  business_day
			</if>
			<if test="minBusinessDay!=null ">
				and  business_day >=  #{minBusinessDay}
			</if>
			<if test="auditYear!=null and auditYear!=''">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null and auditYearMonth!=''">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
		group by pay_code_id
	</select>

	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditDailyRegistRecord">
		UPDATE night_audit_daily_regist_record
			<set>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="payClassId!=null and payClassId!=''">
				pay_class_id = #{payClassId,jdbcType=INTEGER},
				</if>
				<if test="payClassName!=null and payClassName!=''">
				pay_class_name = #{payClassName,jdbcType=VARCHAR},
				</if>
				<if test="payCodeId!=null and payCodeId!=''">
				pay_code_id = #{payCodeId,jdbcType=VARCHAR},
				</if>
				<if test="payCodeName!=null and payCodeName!=''">
				pay_code_name = #{payCodeName,jdbcType=VARCHAR},
				</if>
				<if test="totalAmount!=null and totalAmount!=''">
				total_amount = #{totalAmount,jdbcType=VARCHAR},
				</if>
				<if test="totalMonthAmount!=null and totalMonthAmount!=''">
				total_month_amount = #{totalMonthAmount,jdbcType=INTEGER},
				</if>
				<if test="totalYearAmount!=null and totalYearAmount!=''">
				total_year_amount = #{totalYearAmount,jdbcType=INTEGER},
				</if>
				<if test="lastMonthAmount!=null and lastMonthAmount!=''">
				last_month_amount = #{lastMonthAmount,jdbcType=INTEGER},
				</if>
				<if test="lastYearAmount!=null and lastYearAmount!=''">
				last_year_amount = #{lastYearAmount,jdbcType=INTEGER},
				</if>
				<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER},
				</if>
				<if test="auditYear!=null and auditYear!=''">
				audit_year = #{auditYear,jdbcType=INTEGER},
				</if>
				<if test="auditYearMonth!=null and auditYearMonth!=''">
				audit_year_month = #{auditYearMonth,jdbcType=INTEGER}
				</if>
			</set>
		WHERE
			night_audit_daily_regist_record_id = #{nightAuditDailyRegistRecordId,jdbcType=INTEGER}
	</update>

</mapper>
