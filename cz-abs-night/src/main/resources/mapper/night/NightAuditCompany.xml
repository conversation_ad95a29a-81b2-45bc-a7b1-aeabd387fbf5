<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditCompanyDao">
	<resultMap id="NightAuditCompanyMap" type="com.pms.czabsnight.bean.NightAuditCompany">
		<result property="nightAuditCompanyId" jdbcType="INTEGER" column="night_audit_company_id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="companyId" jdbcType="INTEGER" column="company_id"/>
		<result property="companyName" jdbcType="VARCHAR" column="company_name"/>
		<result property="addAraccount" jdbcType="INTEGER" column="add_AR_account"/>
		<result property="decrAraccount" jdbcType="INTEGER" column="decr_AR_account"/>
		<result property="arLimit" jdbcType="INTEGER" column="ar_limit"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="auditYear" jdbcType="INTEGER" column="audit_year"/>
		<result property="auditYearMonth" jdbcType="INTEGER" column="audit_year_month"/>
	</resultMap>
	<sql id="BaseColumn">
		night_audit_company_id,
		hid,
		hotel_group_id,
		company_id,
		company_name,
		add_AR_account,
		decr_AR_account,
		ar_limit,
		business_day,
		audit_year,
		audit_year_month
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditCompanyMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_audit_company 
		WHERE 
			night_audit_company_id = #{id} 
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_company WHERE
		night_audit_company_id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditCompany" useGeneratedKeys="true" keyProperty="nightAuditCompanyId">
		INSERT INTO night_audit_company (
			night_audit_company_id,
			hid,
			hotel_group_id,
			company_id,
			company_name,
			add_AR_account,
			decr_AR_account,
			ar_limit,
			business_day,
			audit_year,
			audit_year_month
		) VALUES (
			#{nightAuditCompanyId,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{companyId,jdbcType=INTEGER},
			#{companyName,jdbcType=VARCHAR},
			#{addAraccount,jdbcType=INTEGER},
			#{decrAraccount,jdbcType=INTEGER},
			#{arLimit,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{auditYear,jdbcType=INTEGER},
			#{auditYearMonth,jdbcType=INTEGER}
		)
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditCompanySearch" resultMap="NightAuditCompanyMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_audit_company
		<where>
			<if test="nightAuditCompanyId!=null and nightAuditCompanyId!=''">
				and night_audit_company_id = #{nightAuditCompanyId}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="companyId!=null and companyId!=''">
				and company_id = #{companyId}
			</if>
			<if test="companyName!=null and companyName!=''">
				and company_name = #{companyName}
			</if>
			<if test="addAraccount!=null and addAraccount!=''">
				and add_AR_account = #{addAraccount}
			</if>
			<if test="decrAraccount!=null and decrAraccount!=''">
				and decr_AR_account = #{decrAraccount}
			</if>
			<if test="arLimit!=null and arLimit!=''">
				and ar_limit = #{arLimit}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null and auditYear!=''">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null and auditYearMonth!=''">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditCompany">
		UPDATE night_audit_company 
			<set>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="companyId!=null and companyId!=''">
				company_id = #{companyId,jdbcType=INTEGER},
				</if>
				<if test="companyName!=null and companyName!=''">
				company_name = #{companyName,jdbcType=VARCHAR},
				</if>
				<if test="addAraccount!=null and addAraccount!=''">
				add_AR_account = #{addAraccount,jdbcType=INTEGER},
				</if>
				<if test="decrAraccount!=null and decrAraccount!=''">
				decr_AR_account = #{decrAraccount,jdbcType=INTEGER},
				</if>
				<if test="arLimit!=null and arLimit!=''">
				ar_limit = #{arLimit,jdbcType=INTEGER},
				</if>
				<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER},
				</if>
				<if test="auditYear!=null and auditYear!=''">
				audit_year = #{auditYear,jdbcType=INTEGER},
				</if>
				<if test="auditYearMonth!=null and auditYearMonth!=''">
				audit_year_month = #{auditYearMonth,jdbcType=INTEGER}
				</if>
			</set>
		WHERE 
			night_audit_company_id = #{nightAuditCompanyId,jdbcType=INTEGER}
	</update>
	
</mapper>