<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditGoodsDao">
	<resultMap id="NightAuditGoodsMap" type="com.pms.czabsnight.bean.NightAuditGoods">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="goodsInfoId" jdbcType="INTEGER" column="goods_info_id"/>
		<result property="goodsInfoName" jdbcType="VARCHAR" column="goods_info_namgoods_info_name"/>
		<result property="goodsClassId" jdbcType="INTEGER" column="goods_class_id"/>
		<result property="goodsClassName" jdbcType="VARCHAR" column="goods_class_name"/>
		<result property="amount" jdbcType="INTEGER" column="amount"/>
		<result property="money" jdbcType="INTEGER" column="money"/>
		<result property="type" jdbcType="INTEGER" column="type"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="auditYear" jdbcType="INTEGER" column="audit_year"/>
		<result property="auditYearMonth" jdbcType="INTEGER" column="audit_year_month"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		goods_info_id,
		goods_info_name,
		goods_class_id,
		goods_class_name,
		amount,
		money,
		type,
		hid,
		hotel_group_id,
		business_day,
		audit_year,
		audit_year_month
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditGoodsMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_audit_goods 
		WHERE 
			id = #{id} 
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_goods WHERE
		id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditGoods" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO night_audit_goods (
			id,
			goods_info_id,
			goods_info_name,
			goods_class_id,
			goods_class_name,
			amount,
			money,
			type,
			hid,
			hotel_group_id,
			business_day,
			audit_year,
			audit_year_month
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{goodsInfoId,jdbcType=INTEGER},
			#{goodsInfoName,jdbcType=VARCHAR},
			#{goodsClassId,jdbcType=INTEGER},
			#{goodsClassName,jdbcType=VARCHAR},
			#{amount,jdbcType=INTEGER},
			#{money,jdbcType=INTEGER},
			#{type,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{auditYear,jdbcType=INTEGER},
			#{auditYearMonth,jdbcType=INTEGER}
		)
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditGoodsSearch" resultMap="NightAuditGoodsMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_audit_goods
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="goodsInfoId!=null and goodsInfoId!=''">
				and goods_info_id = #{goodsInfoId}
			</if>
			<if test="goodsInfoName!=null and goodsInfoName!=''">
				and goods_info_name = #{goodsInfoName}
			</if>
			<if test="goodsClassId!=null and goodsClassId!=''">
				and goods_class_id = #{goodsClassId}
			</if>
			<if test="goodsClassName!=null and goodsClassName!=''">
				and goods_class_name = #{goodsClassName}
			</if>
			<if test="amount!=null and amount!=''">
				and amount = #{amount}
			</if>
			<if test="money!=null and money!=''">
				and money = #{money}
			</if>
			<if test="type!=null and type!=''">
				and type = #{type}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null and auditYear!=''">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null and auditYearMonth!=''">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditGoods">
		UPDATE night_audit_goods 
			<set>
				<if test="goodsInfoId!=null and goodsInfoId!=''">
				goods_info_id = #{goodsInfoId,jdbcType=INTEGER},
				</if>
				<if test="goodsInfoName!=null and goodsInfoName!=''">
				goods_info_name = #{goodsInfoNamgoodsInfoName,jdbcType=VARCHAR},
				</if>
				<if test="goodsClassId!=null and goodsClassId!=''">
				goods_class_id = #{goodsClassId,jdbcType=INTEGER},
				</if>
				<if test="goodsClassName!=null and goodsClassName!=''">
				goods_class_name = #{goodsClassName,jdbcType=VARCHAR},
				</if>
				<if test="amount!=null and amount!=''">
				amount = #{amount,jdbcType=INTEGER},
				</if>
				<if test="money!=null and money!=''">
				money = #{money,jdbcType=INTEGER},
				</if>
				<if test="type!=null and type!=''">
				type = #{type,jdbcType=INTEGER},
				</if>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER},
				</if>
				<if test="auditYear!=null and auditYear!=''">
				audit_year = #{auditYear,jdbcType=INTEGER},
				</if>
				<if test="auditYearMonth!=null and auditYearMonth!=''">
				audit_year_month = #{auditYearMonth,jdbcType=INTEGER}
				</if>
			</set>
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</update>
	
</mapper>