<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditMemberBalanceDao">
	<resultMap id="NightAuditMemberBalanceMap" type="com.pms.czabsnight.bean.NightAuditMemberBalance">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="balance" jdbcType="VARCHAR" column="balance"/>
		<result property="largessBalance" jdbcType="VARCHAR" column="largess_balance"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		hotel_group_id,
		balance,
		largess_balance,
		business_day
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditMemberBalanceMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_audit_member_balance 
		WHERE 
			id = #{id} 
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_member_balance WHERE
		id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditMemberBalance" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO night_audit_member_balance (
			id,
			hid,
			hotel_group_id,
			balance,
			largess_balance,
			business_day
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{balance,jdbcType=VARCHAR},
			#{largessBalance,jdbcType=VARCHAR},
			#{businessDay,jdbcType=INTEGER}
		)
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditMemberBalanceSearch" resultMap="NightAuditMemberBalanceMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_audit_member_balance
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="balance!=null and balance!=''">
				and balance = #{balance}
			</if>
			<if test="largessBalance!=null and largessBalance!=''">
				and largess_balance = #{largessBalance}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
		</where>
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditMemberBalance">
		UPDATE night_audit_member_balance 
			<set>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="balance!=null and balance!=''">
				balance = #{balance,jdbcType=VARCHAR},
				</if>
				<if test="largessBalance!=null and largessBalance!=''">
				largess_balance = #{largessBalance,jdbcType=VARCHAR},
				</if>
				<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER}
				</if>
			</set>
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</update>
	
</mapper>