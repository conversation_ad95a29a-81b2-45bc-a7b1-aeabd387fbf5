<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditBalanceDao">
	<resultMap id="NightAuditBalanceMap" type="com.pms.czabsnight.bean.NightAuditBalance">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="type" jdbcType="INTEGER" column="type"/>
		<result property="money" jdbcType="INTEGER" column="money"/>
		<result property="vipMoney" jdbcType="INTEGER" column="vip_money"/>
		<result property="arMoney" jdbcType="INTEGER" column="ar_money"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="auditYear" jdbcType="INTEGER" column="audit_year"/>
		<result property="auditYearMonth" jdbcType="INTEGER" column="audit_year_month"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		type,
		money,
		vip_money,
		ar_money,
		hid,
		hotel_group_id,
		business_day,
		audit_year,
		audit_year_month
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditBalanceMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_audit_balance 
		WHERE 
			id = #{id} 
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_balance WHERE
		id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditBalance" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO night_audit_balance (
			id,
			type,
			money,
			vip_money,
			ar_money,
			hid,
			hotel_group_id,
			business_day,
			audit_year,
			audit_year_month
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{type,jdbcType=INTEGER},
			#{money,jdbcType=INTEGER},
			#{vipMoney,jdbcType=INTEGER},
			#{arMoney,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{auditYear,jdbcType=INTEGER},
			#{auditYearMonth,jdbcType=INTEGER}
		)
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditBalanceSearch" resultMap="NightAuditBalanceMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_audit_balance
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="type!=null and type!=''">
				and type = #{type}
			</if>
			<if test="money!=null and money!=''">
				and money = #{money}
			</if>
			<if test="vipMoney!=null and vipMoney!=''">
				and vip_money = #{vipMoney}
			</if>
			<if test="arMoney!=null and arMoney!=''">
				and ar_money = #{arMoney}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null and auditYear!=''">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null and auditYearMonth!=''">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditBalance">
		UPDATE night_audit_balance 
			<set>
				<if test="type!=null and type!=''">
				type = #{type,jdbcType=INTEGER},
				</if>
				<if test="money!=null and money!=''">
				money = #{money,jdbcType=INTEGER},
				</if>
				<if test="vipMoney!=null and vipMoney!=''">
				vip_money = #{vipMoney,jdbcType=INTEGER},
				</if>
				<if test="arMoney!=null and arMoney!=''">
				ar_money = #{arMoney,jdbcType=INTEGER},
				</if>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER},
				</if>
				<if test="auditYear!=null and auditYear!=''">
				audit_year = #{auditYear,jdbcType=INTEGER},
				</if>
				<if test="auditYearMonth!=null and auditYearMonth!=''">
				audit_year_month = #{auditYearMonth,jdbcType=INTEGER}
				</if>
			</set>
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</update>
	
</mapper>