<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditRoomStatusDao">
	<resultMap id="NightAuditRoomStatusMap" type="com.pms.czabsnight.bean.NightAuditRoomStatus">
		<result property="nightAuditRoomStatusId" jdbcType="INTEGER" column="night_audit_room_status_id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="roomId" jdbcType="INTEGER" column="room_id"/>
		<result property="roomNum" jdbcType="VARCHAR" column="room_num"/>
		<result property="registId" jdbcType="INTEGER" column="regist_id"/>
		<result property="guestNum" jdbcType="INTEGER" column="guest_num"/>
		<result property="guestName" jdbcType="VARCHAR" column="guest_name"/>
		<result property="startTime" jdbcType="TIMESTAMP" column="start_time"/>
		<result property="endTime" jdbcType="TIMESTAMP" column="end_time"/>
		<result property="registState" jdbcType="INTEGER" column="regist_state"/>
		<result property="roomState" jdbcType="INTEGER" column="room_state"/>
		<result property="roomTypeId" jdbcType="INTEGER" column="room_type_id"/>
		<result property="roomTypeName" jdbcType="VARCHAR" column="room_type_name"/>
		<result property="roomPrice" jdbcType="INTEGER" column="room_price"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="auditYear" jdbcType="INTEGER" column="audit_year"/>
		<result property="auditYearMonth" jdbcType="INTEGER" column="audit_year_month"/>
	</resultMap>
	<sql id="BaseColumn">
		night_audit_room_status_id,
		hid,
		hotel_group_id,
		room_id,
		room_num,
		regist_id,
		guest_num,
		guest_name,
		start_time,
		end_time,
		regist_state,
		room_state,
		room_type_id,
		room_type_name,
		room_price,
		business_day,
		audit_year,
		audit_year_month
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditRoomStatusMap">
		SELECT
			<include refid="BaseColumn" />
		FROM night_audit_room_status
		WHERE
			night_audit_room_status_id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_room_status WHERE
		night_audit_room_status_id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditRoomStatus" useGeneratedKeys="true" keyProperty="nightAuditRoomStatusId">
		INSERT INTO night_audit_room_status (
			night_audit_room_status_id,
			hid,
			hotel_group_id,
			room_id,
			room_num,
			regist_id,
			guest_num,
			guest_name,
			start_time,
			end_time,
			regist_state,
			room_state,
			room_type_id,
			room_type_name,
			room_price,
			business_day,
			audit_year,
			audit_year_month
		) VALUES (
			#{nightAuditRoomStatusId,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{roomId,jdbcType=INTEGER},
			#{roomNum,jdbcType=VARCHAR},
			#{registId,jdbcType=INTEGER},
			#{guestNum,jdbcType=INTEGER},
			#{guestName,jdbcType=VARCHAR},
			#{startTime,jdbcType=TIMESTAMP},
			#{endTime,jdbcType=TIMESTAMP},
			#{registState,jdbcType=INTEGER},
			#{roomState,jdbcType=INTEGER},
			#{roomTypeId,jdbcType=INTEGER},
			#{roomTypeName,jdbcType=VARCHAR},
			#{roomPrice,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{auditYear,jdbcType=INTEGER},
			#{auditYearMonth,jdbcType=INTEGER}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditRoomStatusSearch" resultMap="NightAuditRoomStatusMap">
		SELECT
			<include refid="BaseColumn" />
		FROM night_audit_room_status
		<where>
			<if test="nightAuditRoomStatusId!=null and nightAuditRoomStatusId!=''">
				and night_audit_room_status_id = #{nightAuditRoomStatusId}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="roomId!=null and roomId!=''">
				and room_id = #{roomId}
			</if>
			<if test="roomNum!=null and roomNum!=''">
				and room_num = #{roomNum}
			</if>
			<if test="registId!=null and registId!=''">
				and regist_id = #{registId}
			</if>
			<if test="guestNum!=null and guestNum!=''">
				and guest_num = #{guestNum}
			</if>
			<if test="guestName!=null and guestName!=''">
				and guest_name = #{guestName}
			</if>
			<if test="startTime!=null and startTime!=''">
				and start_time = #{startTime}
			</if>
			<if test="endTime!=null and endTime!=''">
				and end_time = #{endTime}
			</if>
			<if test="registState!=null and registState!=''">
				and regist_state = #{registState}
			</if>
			<if test="roomState!=null and roomState!=''">
				and room_state = #{roomState}
			</if>
			<if test="roomTypeId!=null and roomTypeId!=''">
				and room_type_id = #{roomTypeId}
			</if>
			<if test="roomTypeName!=null and roomTypeName!=''">
				and room_type_name = #{roomTypeName}
			</if>
			<if test="roomPrice!=null and roomPrice!=''">
				and room_price = #{roomPrice}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null and auditYear!=''">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null and auditYearMonth!=''">
				and audit_year_month = #{auditYearMonth}
			</if>
			<if test="maxBusinessDay!=null ">
				and #{maxBusinessDay} >=  business_day
			</if>
			<if test="minBusinessDay!=null ">
				and  business_day >=  #{minBusinessDay}
			</if>
		</where>
	</select>

	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditRoomStatus">
		UPDATE night_audit_room_status
			<set>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="roomId!=null and roomId!=''">
				room_id = #{roomId,jdbcType=INTEGER},
				</if>
				<if test="roomNum!=null and roomNum!=''">
				room_num = #{roomNum,jdbcType=VARCHAR},
				</if>
				<if test="registId!=null and registId!=''">
				regist_id = #{registId,jdbcType=INTEGER},
				</if>
				<if test="guestNum!=null and guestNum!=''">
				guest_num = #{guestNum,jdbcType=INTEGER},
				</if>
				<if test="guestName!=null and guestName!=''">
				guest_name = #{guestName,jdbcType=VARCHAR},
				</if>
				<if test="startTime!=null and startTime!=''">
				start_time = #{startTime,jdbcType=TIMESTAMP},
				</if>
				<if test="endTime!=null and endTime!=''">
				end_time = #{endTime,jdbcType=TIMESTAMP},
				</if>
				<if test="registState!=null and registState!=''">
				regist_state = #{registState,jdbcType=INTEGER},
				</if>
				<if test="roomState!=null and roomState!=''">
				room_state = #{roomState,jdbcType=INTEGER},
				</if>
				<if test="roomTypeId!=null and roomTypeId!=''">
				room_type_id = #{roomTypeId,jdbcType=INTEGER},
				</if>
				<if test="roomTypeName!=null and roomTypeName!=''">
				room_type_name = #{roomTypeName,jdbcType=VARCHAR},
				</if>
				<if test="roomPrice!=null and roomPrice!=''">
				room_price = #{roomPrice,jdbcType=INTEGER},
				</if>
				<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER},
				</if>
				<if test="auditYear!=null and auditYear!=''">
				audit_year = #{auditYear,jdbcType=INTEGER},
				</if>
				<if test="auditYearMonth!=null and auditYearMonth!=''">
				audit_year_month = #{auditYearMonth,jdbcType=INTEGER}
				</if>
			</set>
		WHERE
			night_audit_room_status_id = #{nightAuditRoomStatusId,jdbcType=INTEGER}
	</update>

</mapper>
