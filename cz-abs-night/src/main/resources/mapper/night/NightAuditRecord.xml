<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditRecordDao">
	<resultMap id="NightAuditRecordMap" type="com.pms.czabsnight.bean.NightAuditRecord">
		<result property="nightAuditRecordId" jdbcType="INTEGER" column="night_audit_record_id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="state" jdbcType="INTEGER" column="state"/>
		<result property="auditStart" jdbcType="TIMESTAMP" column="audit_start"/>
		<result property="auditEnd" jdbcType="TIMESTAMP" column="audit_end"/>
		<result property="classId" jdbcType="INTEGER" column="class_id"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
		<result property="createUserId" jdbcType="VARCHAR" column="create_user_id"/>
		<result property="createUserName" jdbcType="VARCHAR" column="create_user_name"/>
		<result property="message" jdbcType="VARCHAR" column="message"/>
	</resultMap>
	<sql id="BaseColumn">
		night_audit_record_id,
		hid,
		hotel_group_id,
		state,
		audit_start,
		audit_end,
		class_id,
		business_day,
		create_time,
		create_user_id,
		create_user_name,
		message
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditRecordMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_audit_record 
		WHERE 
			night_audit_record_id = #{id} 
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_record WHERE
		night_audit_record_id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditRecord" useGeneratedKeys="true" keyProperty="nightAuditRecordId">
		INSERT INTO night_audit_record (
			night_audit_record_id,
			hid,
			hotel_group_id,
			state,
			audit_start,
			audit_end,
			class_id,
			business_day,
			create_time,
			create_user_id,
			create_user_name,
			message
		) VALUES (
			#{nightAuditRecordId,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{state,jdbcType=INTEGER},
			#{auditStart,jdbcType=TIMESTAMP},
			#{auditEnd,jdbcType=TIMESTAMP},
			#{classId,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{createTime,jdbcType=TIMESTAMP},
			#{createUserId,jdbcType=VARCHAR},
			#{createUserName,jdbcType=VARCHAR},
			#{message,jdbcType=VARCHAR}
		)
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditRecordSearch" resultMap="NightAuditRecordMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM night_audit_record
		<where>
			<if test="nightAuditRecordId!=null and nightAuditRecordId!=''">
				and night_audit_record_id = #{nightAuditRecordId}
			</if>
			<if test="hid!=null ">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null ">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="state!=null ">
				and state = #{state}
			</if>
			<if test="auditStart!=null">
				and audit_start = #{auditStart}
			</if>
			<if test="auditEnd!=null">
				and audit_end = #{auditEnd}
			</if>
			<if test="classId!=null and classId!=''">
				and class_id = #{classId}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="createTime!=null ">
				and create_time = #{createTime}
			</if>
			<if test="createUserId!=null and createUserId!=''">
				and create_user_id = #{createUserId}
			</if>
			<if test="message!=null and message!=''">
				and message = #{message}
			</if>
			<if test="businessDayStart!=null and businessDayStart!=0">
				and business_day >= #{businessDayStart}
			</if>
			<if test="businessDayEnd!=null and businessDayEnd!=0">
				<![CDATA[
					  and business_day <= #{businessDayEnd}
				]]>
			</if>
		</where>
		ORDER BY night_audit_record_id desc
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditRecord">
		UPDATE night_audit_record 
			<set>
				<if test="hid!=null ">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="auditStart!=null">
				audit_start = #{auditStart,jdbcType=TIMESTAMP},
				</if>
				<if test="auditEnd!=null">
				audit_end = #{auditEnd,jdbcType=TIMESTAMP},
				</if>
				<if test="classId!=null and classId!=''">
				class_id = #{classId,jdbcType=INTEGER},
				</if>
				<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER},
				</if>
				<if test="state!=null ">
				state = #{state},
				</if>
				<if test="message!=null and message!=''">
				message = #{message,jdbcType=VARCHAR}
				</if>
			</set>
		WHERE 
			night_audit_record_id = #{nightAuditRecordId,jdbcType=INTEGER}
	</update>
	
</mapper>