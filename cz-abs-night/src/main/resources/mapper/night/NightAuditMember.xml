<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditMemberDao">
	<resultMap id="NightAuditMemberMap" type="com.pms.czabsnight.bean.NightAuditMember">
		<result property="nightAuditMemberId" jdbcType="INTEGER" column="night_audit_member_id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="cardTypeId" jdbcType="INTEGER" column="card_type_id"/>
		<result property="cardType" jdbcType="VARCHAR" column="card_type"/>
		<result property="cardLevelId" jdbcType="INTEGER" column="card_level_id"/>
		<result property="cardLevel" jdbcType="VARCHAR" column="card_level"/>
		<result property="type" jdbcType="INTEGER" column="type"/>
		<result property="addNewMember" jdbcType="INTEGER" column="add_new_member"/>
		<result property="recharge" jdbcType="INTEGER" column="recharge"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="auditYear" jdbcType="INTEGER" column="audit_year"/>
		<result property="auditYearMonth" jdbcType="INTEGER" column="audit_year_month"/>
	</resultMap>
	<sql id="BaseColumn">
		night_audit_member_id,
		hid,
		hotel_group_id,
		card_type_id,
		card_type,
		card_level_id,
		card_level,
		type,
		add_new_member,
		recharge,
		business_day,
		audit_year,
		audit_year_month
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditMemberMap">
		SELECT
			<include refid="BaseColumn" />
		FROM night_audit_member
		WHERE
			night_audit_member_id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_member WHERE
		night_audit_member_id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditMember" useGeneratedKeys="true" keyProperty="nightAuditMemberId">
		INSERT INTO night_audit_member (
			night_audit_member_id,
			hid,
			hotel_group_id,
			card_type_id,
			card_type,
			card_level_id,
			card_level,
			type,
			add_new_member,
			recharge,
			business_day,
			audit_year,
			audit_year_month
		) VALUES (
			#{nightAuditMemberId,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{cardTypeId,jdbcType=INTEGER},
			#{cardType,jdbcType=VARCHAR},
			#{cardLevelId,jdbcType=INTEGER},
			#{cardLevel,jdbcType=VARCHAR},
			#{type,jdbcType=INTEGER},
			#{addNewMember,jdbcType=INTEGER},
			#{recharge,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{auditYear,jdbcType=INTEGER},
			#{auditYearMonth,jdbcType=INTEGER}
		)
	</insert>

	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditMemberSearch" resultMap="NightAuditMemberMap">
		SELECT
		<include refid="BaseColumn" />
		FROM night_audit_member
		<where>
			<if test="nightAuditMemberId!=null and nightAuditMemberId!=''">
				and night_audit_member_id = #{nightAuditMemberId}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="cardTypeId!=null and cardTypeId!=''">
				and card_type_id = #{cardTypeId}
			</if>
			<if test="cardType!=null and cardType!=''">
				and card_type = #{cardType}
			</if>
			<if test="cardLevelId!=null and cardLevelId!=''">
				and card_level_id = #{cardLevelId}
			</if>
			<if test="cardLevel!=null and cardLevel!=''">
				and card_level = #{cardLevel}
			</if>
			<if test="type!=null and type!=''">
				and type = #{type}
			</if>
			<if test="addNewMember!=null and addNewMember!=''">
				and add_new_member = #{addNewMember}
			</if>
			<if test="recharge!=null and recharge!=''">
				and recharge = #{recharge}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null and auditYear!=''">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null and auditYearMonth!=''">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
	</select>

	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditMember">
		UPDATE night_audit_member
		<set>
			<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
			</if>
			<if test="cardTypeId!=null and cardTypeId!=''">
				card_type_id = #{cardTypeId,jdbcType=INTEGER},
			</if>
			<if test="cardType!=null and cardType!=''">
				card_type = #{cardType,jdbcType=VARCHAR},
			</if>
			<if test="cardLevelId!=null and cardLevelId!=''">
				card_level_id = #{cardLevelId,jdbcType=INTEGER},
			</if>
			<if test="cardLevel!=null and cardLevel!=''">
				card_level = #{cardLevel,jdbcType=VARCHAR},
			</if>
			<if test="type!=null and type!=''">
				type = #{type,jdbcType=INTEGER},
			</if>
			<if test="addNewMember!=null and addNewMember!=''">
				add_new_member = #{addNewMember,jdbcType=INTEGER},
			</if>
			<if test="recharge!=null and recharge!=''">
				recharge = #{recharge,jdbcType=INTEGER},
			</if>
			<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER},
			</if>
			<if test="auditYear!=null and auditYear!=''">
				audit_year = #{auditYear,jdbcType=INTEGER},
			</if>
			<if test="auditYearMonth!=null and auditYearMonth!=''">
				audit_year_month = #{auditYearMonth,jdbcType=INTEGER}
			</if>
		</set>
		WHERE
		night_audit_member_id = #{nightAuditMemberId,jdbcType=INTEGER}
	</update>


	<!--查询账务汇总-->
	<select id="selectBySearchSummary" resultType="com.pms.czabsnight.bean.NightAuditMember"
			parameterType="com.pms.czabsnight.bean.search.NightAuditMemberSearch">
		SELECT
		night_audit_member_id as nightAuditMemberId,
		hid,
		hotel_group_id as hotelGroupId,
		card_type_id as cardTypeId,
		card_type as cardType,
		card_level_id as cardLevelId,
		card_level as cardLevel,
		type,
		SUM(add_new_member) as addNewMember,
		SUM(recharge) as recharge ,
		business_day as businessDay,
		audit_year as auditYear,
		audit_year_month as auditYearMonth
		FROM
		night_audit_member
		WHERE
		hid = #{hid}
		<if test="businessDayMin!=null ">
			and business_day >= #{businessDayMin}
		</if>
		<if test="businessDayMax!=null ">
			and #{businessDayMax}>=business_day
		</if>
		GROUP BY type
	</select>


</mapper>
