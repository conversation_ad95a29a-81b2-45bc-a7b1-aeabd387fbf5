<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditPayDao">
	<resultMap id="NightAuditPayMap" type="com.pms.czabsnight.bean.NightAuditPay">
		<result property="nightAuditPayId" jdbcType="INTEGER" column="night_audit_pay_id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="payClassId" jdbcType="VARCHAR" column="pay_class_id"/>
		<result property="payClassName" jdbcType="VARCHAR" column="pay_class_name"/>
		<result property="payCodeId" jdbcType="VARCHAR" column="pay_code_id"/>
		<result property="payCodeName" jdbcType="VARCHAR" column="pay_code_name"/>
		<result property="totalAmount" jdbcType="INTEGER" column="total_amount"/>
		<result property="refundAmount" jdbcType="INTEGER" column="refund_amount"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="auditYear" jdbcType="INTEGER" column="audit_year"/>
		<result property="auditYearMonth" jdbcType="INTEGER" column="audit_year_month"/>
	</resultMap>
	<sql id="BaseColumn">
		night_audit_pay_id,
		hid,
		hotel_group_id,
		pay_class_id,
		pay_class_name,
		pay_code_id,
		pay_code_name,
		total_amount,
		refund_amount,
		type,
		business_day,
		audit_year,
		audit_year_month
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditPayMap">
		SELECT
			<include refid="BaseColumn" />
		FROM night_audit_pay
		WHERE
			night_audit_pay_id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_pay WHERE
		night_audit_pay_id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditPay" useGeneratedKeys="true" keyProperty="nightAuditPayId">
		INSERT INTO night_audit_pay (
			night_audit_pay_id,
			hid,
			hotel_group_id,
			pay_class_id,
			pay_class_name,
			pay_code_id,
			pay_code_name,
			total_amount,
			refund_amount,
			type,
			business_day,
			audit_year,
			audit_year_month
		) VALUES (
			#{nightAuditPayId,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{payClassId,jdbcType=VARCHAR},
			#{payClassName,jdbcType=VARCHAR},
			#{payCodeId,jdbcType=VARCHAR},
			#{payCodeName,jdbcType=VARCHAR},
			#{totalAmount,jdbcType=INTEGER},
			#{refundAmount,jdbcType=INTEGER},
			#{type,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{auditYear,jdbcType=INTEGER},
			#{auditYearMonth,jdbcType=INTEGER}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditPaySearch" resultMap="NightAuditPayMap">
		SELECT
			<include refid="BaseColumn" />
		FROM night_audit_pay
		<where>
			<if test="nightAuditPayId!=null and nightAuditPayId!=''">
				and night_audit_pay_id = #{nightAuditPayId}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="payClassId!=null and payClassId!=''">
				and pay_class_id = #{payClassId}
			</if>
			<if test="payClassName!=null and payClassName!=''">
				and pay_class_name = #{payClassName}
			</if>
			<if test="payCodeId!=null and payCodeId!=''">
				and pay_code_id = #{payCodeId}
			</if>
			<if test="payCodeName!=null and payCodeName!=''">
				and pay_code_name = #{payCodeName}
			</if>
			<if test="totalAmount!=null and totalAmount!=''">
				and total_amount = #{totalAmount}
			</if>
			<if test="refundAmount!=null and refundAmount!=''">
				and refund_amount = #{refundAmount}
			</if>
			<if test="type!=null">
				and type = #{type}
			</if>
			<if test="businessDay!=null ">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null ">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null ">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
	</select>

	<select id="selectBySearchSummary" parameterType="com.pms.czabsnight.bean.search.NightAuditPaySearch" resultMap="NightAuditPayMap">
		SELECT
		night_audit_pay_id,
		hid,
		hotel_group_id,
		pay_class_id,
		pay_class_name,
		pay_code_id,
		pay_code_name,
		SUM(total_amount) as total_amount,
		SUM(refund_amount) as refund_amount,
		type,
		business_day,
		audit_year,
		audit_year_month
		FROM night_audit_pay
		<where>
			<if test="nightAuditPayId!=null and nightAuditPayId!=''">
				and night_audit_pay_id = #{nightAuditPayId}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="payClassId!=null and payClassId!=''">
				and pay_class_id = #{payClassId}
			</if>
			<if test="payClassName!=null and payClassName!=''">
				and pay_class_name = #{payClassName}
			</if>
			<if test="payCodeId!=null and payCodeId!=''">
				and pay_code_id = #{payCodeId}
			</if>
			<if test="payCodeName!=null and payCodeName!=''">
				and pay_code_name = #{payCodeName}
			</if>
			<if test="totalAmount!=null and totalAmount!=''">
				and total_amount = #{totalAmount}
			</if>
			<if test="refundAmount!=null and refundAmount!=''">
				and refund_amount = #{refundAmount}
			</if>
			<if test="type!=null">
				and type = #{type}
			</if>
			<if test="businessDay!=null ">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null ">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null ">
				and audit_year_month = #{auditYearMonth}
			</if>
			<if test="businessDayMin!=null">
				and business_day >= #{businessDayMin}
			</if>
			<if test="businessDayMax!=null">
				and #{businessDayMax}  >= business_day
			</if>
		</where>
		GROUP BY type,pay_code_id
	</select>

	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditPay">
		UPDATE night_audit_pay
			<set>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="payClassId!=null and payClassId!=''">
				pay_class_id = #{payClassId,jdbcType=VARCHAR},
				</if>
				<if test="payClassName!=null and payClassName!=''">
				pay_class_name = #{payClassName,jdbcType=VARCHAR},
				</if>
				<if test="payCodeId!=null and payCodeId!=''">
				pay_code_id = #{payCodeId,jdbcType=VARCHAR},
				</if>
				<if test="payCodeName!=null and payCodeName!=''">
				pay_code_name = #{payCodeName,jdbcType=VARCHAR},
				</if>
				<if test="totalAmount!=null and totalAmount!=''">
				total_amount = #{totalAmount,jdbcType=INTEGER},
				</if>
				<if test="refundAmount!=null and refundAmount!=''">
				refund_amount = #{refundAmount,jdbcType=INTEGER},
				</if>
				<if test="type!=null">
				 type = #{type},
				</if>
				<if test="businessDay!=null ">
				business_day = #{businessDay,jdbcType=INTEGER},
				</if>
				<if test="auditYear!=null ">
				audit_year = #{auditYear,jdbcType=INTEGER},
				</if>
				<if test="auditYearMonth!=null ">
				audit_year_month = #{auditYearMonth,jdbcType=INTEGER}
				</if>
			</set>
		WHERE
			night_audit_pay_id = #{nightAuditPayId,jdbcType=INTEGER}
	</update>

</mapper>
