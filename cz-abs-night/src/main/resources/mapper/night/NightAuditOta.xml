<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NightAuditOtaDao">
	<resultMap id="NightAuditOtaMap" type="com.pms.czabsnight.bean.NightAuditOta">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="otaName" jdbcType="VARCHAR" column="ota_name"/>
		<result property="otaType" jdbcType="INTEGER" column="ota_type"/>
		<result property="checkinNum" jdbcType="INTEGER" column="checkin_num"/>
		<result property="checkinNumMonth" jdbcType="INTEGER" column="checkin_num_month"/>
		<result property="checkinNumYear" jdbcType="INTEGER" column="checkin_num_year"/>
		<result property="roomMoney" jdbcType="INTEGER" column="room_money"/>
		<result property="goodsMoney" jdbcType="INTEGER" column="goods_money"/>
		<result property="foodsMoney" jdbcType="INTEGER" column="foods_money"/>
		<result property="payMoney" jdbcType="INTEGER" column="pay_money"/>
		<result property="roomMoneyMonth" jdbcType="INTEGER" column="room_money_month"/>
		<result property="goodsMoneyMonth" jdbcType="INTEGER" column="goods_money_month"/>
		<result property="foodsMoneyMonth" jdbcType="INTEGER" column="foods_money_month"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="roomMoneyYear" jdbcType="INTEGER" column="room_money_year"/>
		<result property="goodsMoneyYear" jdbcType="INTEGER" column="goods_money_year"/>
		<result property="foodsMoneyYear" jdbcType="INTEGER" column="foods_money_year"/>
		<result property="auditYear" jdbcType="INTEGER" column="audit_year"/>
		<result property="auditYearMonth" jdbcType="INTEGER" column="audit_year_month"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		hotel_group_id,
		ota_name,
		ota_type,
		checkin_num,
		checkin_num_month,
		checkin_num_year,
		room_money,
		goods_money,
		foods_money,
		pay_money,
		room_money_month,
		goods_money_month,
		foods_money_month,
		business_day,
		room_money_year,
		goods_money_year,
		foods_money_year,
		audit_year,
		audit_year_month
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NightAuditOtaMap">
		SELECT
		<include refid="BaseColumn" />
		FROM night_audit_ota
		WHERE
		id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM night_audit_ota WHERE
		id = #{id}
	</delete>
	<!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NightAuditOta" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO night_audit_ota (
			id,
			hid,
			hotel_group_id,
			ota_name,
			ota_type,
			checkin_num,
			checkin_num_month,
			checkin_num_year,
			room_money,
			goods_money,
			foods_money,
			pay_money,
			room_money_month,
			goods_money_month,
			foods_money_month,
			business_day,
			room_money_year,
			goods_money_year,
			foods_money_year,
			audit_year,
			audit_year_month
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{otaName,jdbcType=VARCHAR},
			#{otaType,jdbcType=INTEGER},
			#{checkinNum,jdbcType=INTEGER},
			#{checkinNumMonth,jdbcType=INTEGER},
			#{checkinNumYear,jdbcType=INTEGER},
			#{roomMoney,jdbcType=INTEGER},
			#{goodsMoney,jdbcType=INTEGER},
			#{foodsMoney,jdbcType=INTEGER},
			#{payMoney,jdbcType=INTEGER},
			#{roomMoneyMonth,jdbcType=INTEGER},
			#{goodsMoneyMonth,jdbcType=INTEGER},
			#{foodsMoneyMonth,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{roomMoneyYear,jdbcType=INTEGER},
			#{goodsMoneyYear,jdbcType=INTEGER},
			#{foodsMoneyYear,jdbcType=INTEGER},
			#{auditYear,jdbcType=INTEGER},
			#{auditYearMonth,jdbcType=INTEGER}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NightAuditOtaSearch" resultMap="NightAuditOtaMap">
		SELECT
		<include refid="BaseColumn" />
		FROM night_audit_ota
		<where>
			<if test="id!=null">
				and id = #{id}
			</if>
			<if test="hid!=null">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="otaName!=null and otaName!=''">
				and ota_name = #{otaName}
			</if>
			<if test="otaType!=null">
				and ota_type = #{otaType}
			</if>
			<if test="checkinNum!=null">
				and checkin_num = #{checkinNum}
			</if>
			<if test="checkinNumMonth!=null">
				and checkin_num_month = #{checkinNumMonth}
			</if>
			<if test="checkinNumYear!=null">
				and checkin_num_year = #{checkinNumYear}
			</if>
			<if test="roomMoney!=null">
				and room_money = #{roomMoney}
			</if>
			<if test="goodsMoney!=null">
				and goods_money = #{goodsMoney}
			</if>
			<if test="foodsMoney!=null">
				and foods_money = #{foodsMoney}
			</if>
			<if test="payMoney!=null">
				and pay_money = #{payMoney}
			</if>
			<if test="roomMoneyMonth!=null">
				and room_money_month = #{roomMoneyMonth}
			</if>
			<if test="goodsMoneyMonth!=null">
				and goods_money_month = #{goodsMoneyMonth}
			</if>
			<if test="foodsMoneyMonth!=null">
				and foods_money_month = #{foodsMoneyMonth}
			</if>
			<if test="businessDay!=null">
				and business_day = #{businessDay}
			</if>
			<if test="roomMoneyYear!=null">
				and room_money_year = #{roomMoneyYear}
			</if>
			<if test="goodsMoneyYear!=null">
				and goods_money_year = #{goodsMoneyYear}
			</if>
			<if test="foodsMoneyYear!=null">
				and foods_money_year = #{foodsMoneyYear}
			</if>
			<if test="auditYear!=null">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
		order by business_day desc
	</select>

	<select id="selectBySearchSummary" parameterType="com.pms.czabsnight.bean.search.NightAuditOtaSearch" resultMap="NightAuditOtaMap">
		SELECT
		id,
		hid,
		hotel_group_id,
		ota_name,
		ota_type,
		SUM(checkin_num) as checkin_num,
		checkin_num_month,
		checkin_num_year,
		SUM(room_money) as room_money,
		SUM(goods_money) as goods_money,
		SUM(foods_money) as foods_money,
		SUM(pay_money) as pay_money,
		room_money_month,
		goods_money_month,
		foods_money_month,
		business_day,
		room_money_year,
		goods_money_year,
		foods_money_year,
		audit_year,
		audit_year_month
		FROM night_audit_ota
		<where>
			<if test="id!=null">
				and id = #{id}
			</if>
			<if test="businessDayMin!=null">
				and business_day >= #{businessDayMin}
			</if>
			<if test="businessDayMax!=null">
				and #{businessDayMax}  >= business_day
			</if>
			<if test="hid!=null">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="otaName!=null and otaName!=''">
				and ota_name = #{otaName}
			</if>
			<if test="otaType!=null">
				and ota_type = #{otaType}
			</if>
			<if test="checkinNum!=null">
				and checkin_num = #{checkinNum}
			</if>
			<if test="checkinNumMonth!=null">
				and checkin_num_month = #{checkinNumMonth}
			</if>
			<if test="checkinNumYear!=null">
				and checkin_num_year = #{checkinNumYear}
			</if>
			<if test="roomMoney!=null">
				and room_money = #{roomMoney}
			</if>
			<if test="goodsMoney!=null">
				and goods_money = #{goodsMoney}
			</if>
			<if test="foodsMoney!=null">
				and foods_money = #{foodsMoney}
			</if>
			<if test="payMoney!=null">
				and pay_money = #{payMoney}
			</if>
			<if test="roomMoneyMonth!=null">
				and room_money_month = #{roomMoneyMonth}
			</if>
			<if test="goodsMoneyMonth!=null">
				and goods_money_month = #{goodsMoneyMonth}
			</if>
			<if test="foodsMoneyMonth!=null">
				and foods_money_month = #{foodsMoneyMonth}
			</if>
			<if test="businessDay!=null">
				and business_day = #{businessDay}
			</if>
			<if test="roomMoneyYear!=null">
				and room_money_year = #{roomMoneyYear}
			</if>
			<if test="goodsMoneyYear!=null">
				and goods_money_year = #{goodsMoneyYear}
			</if>
			<if test="foodsMoneyYear!=null">
				and foods_money_year = #{foodsMoneyYear}
			</if>
			<if test="auditYear!=null">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>
		group by ota_type
	</select>

	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NightAuditOta">
		UPDATE night_audit_ota
		<set>
			<if test="hid!=null">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="hotelGroupId!=null">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
			</if>
			<if test="otaName!=null and otaName!=''">
				ota_name = #{otaName,jdbcType=VARCHAR},
			</if>
			<if test="otaType!=null">
				ota_type = #{otaType,jdbcType=INTEGER},
			</if>
			<if test="checkinNum!=null">
				checkin_num = #{checkinNum,jdbcType=INTEGER},
			</if>
			<if test="checkinNumMonth!=null">
				checkin_num_month = #{checkinNumMonth,jdbcType=INTEGER},
			</if>
			<if test="checkinNumYear!=null">
				checkin_num_year = #{checkinNumYear,jdbcType=INTEGER},
			</if>
			<if test="roomMoney!=null">
				room_money = #{roomMoney,jdbcType=INTEGER},
			</if>
			<if test="goodsMoney!=null">
				goods_money = #{goodsMoney,jdbcType=INTEGER},
			</if>
			<if test="foodsMoney!=null">
				foods_money = #{foodsMoney,jdbcType=INTEGER},
			</if>
			<if test="payMoney!=null">
				pay_money = #{payMoney,jdbcType=INTEGER},
			</if>
			<if test="roomMoneyMonth!=null">
				room_money_month = #{roomMoneyMonth,jdbcType=INTEGER},
			</if>
			<if test="goodsMoneyMonth!=null">
				goods_money_month = #{goodsMoneyMonth,jdbcType=INTEGER},
			</if>
			<if test="foodsMoneyMonth!=null">
				foods_money_month = #{foodsMoneyMonth,jdbcType=INTEGER},
			</if>
			<if test="businessDay!=null">
				business_day = #{businessDay,jdbcType=INTEGER},
			</if>
			<if test="roomMoneyYear!=null">
				room_money_year = #{roomMoneyYear,jdbcType=INTEGER},
			</if>
			<if test="goodsMoneyYear!=null">
				goods_money_year = #{goodsMoneyYear,jdbcType=INTEGER},
			</if>
			<if test="foodsMoneyYear!=null">
				foods_money_year = #{foodsMoneyYear,jdbcType=INTEGER},
			</if>
			<if test="auditYear!=null">
				audit_year = #{auditYear,jdbcType=INTEGER},
			</if>
			<if test="auditYearMonth!=null">
				audit_year_month = #{auditYearMonth,jdbcType=INTEGER}
			</if>
		</set>
		WHERE
		id = #{id,jdbcType=INTEGER}
	</update>

	<insert id="addNightAuditOtaList" parameterType="java.util.List" >
		<foreach collection="list" item="item" index="index" open="" close="" separator=";">
			INSERT INTO night_audit_ota (
			hid,
			hotel_group_id,
			ota_name,
			ota_type,
			checkin_num,
			checkin_num_month,
			checkin_num_year,
			room_money,
			goods_money,
			foods_money,
			pay_money,
			room_money_month,
			goods_money_month,
			foods_money_month,
			business_day,
			room_money_year,
			goods_money_year,
			foods_money_year,
			audit_year,
			audit_year_month
			) VALUES (
			#{item.hid,jdbcType=INTEGER},
			#{item.hotelGroupId,jdbcType=INTEGER},
			#{item.otaName,jdbcType=VARCHAR},
			#{item.otaType,jdbcType=INTEGER},
			#{item.checkinNum,jdbcType=INTEGER},
			#{item.checkinNumMonth,jdbcType=INTEGER},
			#{item.checkinNumYear,jdbcType=INTEGER},
			#{item.roomMoney,jdbcType=INTEGER},
			#{item.goodsMoney,jdbcType=INTEGER},
			#{item.foodsMoney,jdbcType=INTEGER},
			#{item.payMoney,jdbcType=INTEGER},
			#{item.roomMoneyMonth,jdbcType=INTEGER},
			#{item.goodsMoneyMonth,jdbcType=INTEGER},
			#{item.foodsMoneyMonth,jdbcType=INTEGER},
			#{item.businessDay,jdbcType=INTEGER},
			#{item.roomMoneyYear,jdbcType=INTEGER},
			#{item.goodsMoneyYear,jdbcType=INTEGER},
			#{item.foodsMoneyYear,jdbcType=INTEGER},
			#{item.auditYear,jdbcType=INTEGER},
			#{item.auditYearMonth,jdbcType=INTEGER}
			)
		</foreach>
	</insert>


</mapper>
