<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czabsnight.dao.NigthAuditArPaymentDao">
	<resultMap id="NigthAuditArPaymentMap" type="com.pms.czabsnight.bean.NigthAuditArPayment">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="money" jdbcType="INTEGER" column="money"/>
		<result property="payClassId" jdbcType="INTEGER" column="pay_class_id"/>
		<result property="payClassName" jdbcType="VARCHAR" column="pay_class_name"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="auditYear" jdbcType="INTEGER" column="audit_year"/>
		<result property="auditYearMonth" jdbcType="INTEGER" column="audit_year_month"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		hotel_group_id,
		money,
		pay_class_id,
		pay_class_name,
		business_day,
		audit_year,
		audit_year_month
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="NigthAuditArPaymentMap">
		SELECT
			<include refid="BaseColumn" />
		FROM nigth_audit_ar_payment
		WHERE
			id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM nigth_audit_ar_payment WHERE
		id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czabsnight.bean.NigthAuditArPayment" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO nigth_audit_ar_payment (
			id,
			hid,
			hotel_group_id,
			money,
			pay_class_id,
			pay_class_name,
			business_day,
			audit_year,
			audit_year_month
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{money,jdbcType=INTEGER},
			#{payClassId,jdbcType=INTEGER},
			#{payClassName,jdbcType=VARCHAR},
			#{businessDay,jdbcType=INTEGER},
			#{auditYear,jdbcType=INTEGER},
			#{auditYearMonth,jdbcType=INTEGER}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czabsnight.bean.search.NigthAuditArPaymentSearch" resultMap="NigthAuditArPaymentMap">
		SELECT
		<include refid="BaseColumn" />
		FROM nigth_audit_ar_payment
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="money!=null and money!=''">
				and money = #{money}
			</if>
			<if test="payClassId!=null and payClassId!=''">
				and pay_class_id = #{payClassId}
			</if>
			<if test="payClassName!=null and payClassName!=''">
				and pay_class_name = #{payClassName}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null and auditYear!=''">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null and auditYearMonth!=''">
				and audit_year_month = #{auditYearMonth}
			</if>
		</where>

	</select>

	<select id="selectBySearchSummary" parameterType="com.pms.czabsnight.bean.search.NigthAuditArPaymentSearch" resultMap="NigthAuditArPaymentMap">
		SELECT
		id,
		hid,
		hotel_group_id,
		SUM(money) as money,
		pay_class_id,
		pay_class_name,
		business_day,
		audit_year,
		audit_year_month
		FROM nigth_audit_ar_payment
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="money!=null and money!=''">
				and money = #{money}
			</if>
			<if test="payClassId!=null and payClassId!=''">
				and pay_class_id = #{payClassId}
			</if>
			<if test="payClassName!=null and payClassName!=''">
				and pay_class_name = #{payClassName}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="auditYear!=null and auditYear!=''">
				and audit_year = #{auditYear}
			</if>
			<if test="auditYearMonth!=null and auditYearMonth!=''">
				and audit_year_month = #{auditYearMonth}
			</if>
			<if test="businessDayMin!=null">
				and business_day >= #{businessDayMin}
			</if>
			<if test="businessDayMax!=null">
				and #{businessDayMax}  >= business_day
			</if>
		</where>
		group by pay_class_id
	</select>

	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czabsnight.bean.NigthAuditArPayment">
		UPDATE nigth_audit_ar_payment
			<set>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="money!=null and money!=''">
				money = #{money,jdbcType=INTEGER},
				</if>
				<if test="payClassId!=null and payClassId!=''">
				pay_class_id = #{payClassId,jdbcType=INTEGER},
				</if>
				<if test="payClassName!=null and payClassName!=''">
				pay_class_name = #{payClassName,jdbcType=VARCHAR},
				</if>
				<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER},
				</if>
				<if test="auditYear!=null and auditYear!=''">
				audit_year = #{auditYear,jdbcType=INTEGER},
				</if>
				<if test="auditYearMonth!=null and auditYearMonth!=''">
				audit_year_month = #{auditYearMonth,jdbcType=INTEGER}
				</if>
			</set>
		WHERE
			id = #{id,jdbcType=INTEGER}
	</update>

</mapper>
