# 数据库操作
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: 2222f27cfecd13ba718e9e7f1d7774f06f9aeb7b527a1575b83875683ae2414f0a3ddc2b4d0a3bf6b4086f762ef8bb61ba39d77f472a2a0f6d8a62ef0c76e3927dc9fe43ee8bb83532b6e400428422adf7dc2df747804c5fdef888e74dfca515c5597791d3eb88f8af1eef4628d36499
    username: 8336bf9f284f20c3
    password: d1f2341cd0a3bb9cfeb959b7d4642fcb
    #配置初始化大小/最小/最大
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 200
      minimum-idle: 20
      idle-timeout: 60000
      max-lifetime: 60000
      connection-test-query: SELECT 1
  redis:
    host: **************
    port: 6379
    password: 643e145d0e5c42ef073f4f39c975ff0efeb959b7d4642fcb
    database: 5
    timeout: 30000
  application:
    name: CZ-ABS-NIGHT
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true

quartzname: /myquartz-prod.properties

mybatis:
  mapper-locations: classpath*:mapper/*/*.xml,mapper/*.xml  #注意：一定要对应mapper映射xml文件的所在路径
  configuration:
    log-impl: com.pms.czpmsutils.MybatisLoggerImpl

eureka:
  client:
    service-url:
      defaultZone: ************************************/eureka
  instance:
    hostname: CZ-ABS-NIGHT
    prefer-ip-address: true
server:
  port: 8135
  tomcat:
    max-http-post-size: 10485760
logging:
  level:
    org.springframework.scheduling: INFO

pagehelper:
  auto-dialect: true
  supportMethodsArguments: true

#江苏商客订购集团的集团ID
hotel:
  group:
    id: 900021