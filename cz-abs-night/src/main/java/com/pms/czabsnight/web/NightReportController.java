package com.pms.czabsnight.web;

import com.pms.czabsnight.bean.search.NightAuditBalanceSearch;
import com.pms.czabsnight.bean.search.NightAuditRoomTypeSearch;
import com.pms.czabsnight.service.NightReportService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.RoomAccountDetails;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/nightbuss/")
public class NightReportController {

    @Autowired
    private NightReportService nightReportService;

    /**
     * 查询日营业报表
     * @param param
     * @return
     */
    @RequestMapping("searchDailyReport.do")
    @ResponseBody
    public ResponseData searchDailyReport(@RequestBody JSONObject param) {
        ResponseData searchDailyReport = nightReportService.searchDailyReport(param);
        return searchDailyReport;
    }

    /**
     * 查询日营业报表
     * @param param
     * @return
     */
    @RequestMapping("searchDailyReportTwo.do")
    @ResponseBody
    public ResponseData searchDailyReportTwo(@RequestBody JSONObject param) {
        ResponseData searchDailyReport = nightReportService.searchDailyReportTwo(param);
        return searchDailyReport;
    }

    /**
     * 查询日客源报表
     * @param param
     * @return
     */
    @RequestMapping("searchDailyResourceReport.do")
    @ResponseBody
    public ResponseData searchDailyResourceReport(@RequestBody JSONObject param) {
        ResponseData searchDailyReport = nightReportService.searchDailyResourceReport(param);
        return searchDailyReport;
    }

    /**
     * 查询日客源报表
     * @param param
     * @return
     */
    @RequestMapping("searchDailyRoomType.do")
    @ResponseBody
    public ResponseData searchDailyRoomType(@RequestBody JSONObject param) {
        ResponseData searchDailyRoomType = nightReportService.searchDailyRoomType(param);
        return searchDailyRoomType;
    }

    /**
     * 查询日客源报表
     * @param param
     * @return
     */
    @RequestMapping("roomAccountDetails.do")
    @ResponseBody
    public ResponseData roomAccountDetails(@RequestBody RoomAccountDetails param) {
        ResponseData searchDailyRoomType = nightReportService.roomAccountDetails(param);
        return searchDailyRoomType;
    }

    /**
     * 查询日客源报表
     * @param param
     * @return
     */
    @RequestMapping("searchNightRoomType.do")
    @ResponseBody
    public ResponseData searchNightRoomType(@RequestBody NightAuditRoomTypeSearch param) {
        ResponseData searchDailyRoomType = nightReportService.searchNightRoomType(param);
        return searchDailyRoomType;
    }

    /**
     * 查询日客源报表
     * @param param
     * @return
     */
    @RequestMapping("ssphr.do")
    @ResponseBody
    public ResponseData ssphr(@RequestBody NightAuditBalanceSearch param) {
        ResponseData searchDailyRoomType = nightReportService.ssphr(param);
        return searchDailyRoomType;
    }
}
