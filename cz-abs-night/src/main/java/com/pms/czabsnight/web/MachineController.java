package com.pms.czabsnight.web;

import com.pms.czabsnight.service.machine.MachineInterface;
import net.sf.json.JSONObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;


@RestController
@RequestMapping("/ElinesoftApi/api/")
public class MachineController {
    private static final Logger log = LogManager.getLogger(MachineController.class);
    @Autowired
    private MachineInterface machineInterface;

    @RequestMapping("Get_Hotel_BusinessDay.do")
    public JSONObject Get_Hotel_BusinessDay(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.getHotelBusinessDay(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("NoCardCheckin")
    public JSONObject NoCardCheckin(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.noCardCheckin(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Room_List.do")
    public JSONObject Get_Room_List(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.getRoomInfo(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Get_RoomType_Info_List.do")
    public JSONObject Get_RoomType_Info_List(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.getRoomTypeInfoList(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Room_Type_Price.do")
    public JSONObject Get_Room_Type_Price(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.getRoomTypePrice(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Get_RoomType_Info_Listasd.do")
    public JSONObject Get_RoomType_Info_Listasd(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.getRoomTypeInfoList(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Arriving_List.do")
    public JSONObject Get_Arriving_List(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.getArrivingList(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Get_Avail_Room_List.do")
    public JSONObject Get_Avail_Room_List(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.getAvailRoomList(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Lock_Room.do")
    public JSONObject Lock_Room(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.lockRoom(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Register_Check_In.do")
    public JSONObject Register_Check_In(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.registerCheckIn(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Update_GuestInfo.do")
    public JSONObject Update_GuestInfo(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.updateGuestInfo(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Add_Transaction.do")
    public JSONObject Add_Transaction(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.addTransaction(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Make_Guest_Card.do")
    public JSONObject Make_Guest_Card(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.makeGuestCard(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Msg_Continue.do")
    public JSONObject Get_Msg_Continue(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.getContinueLiveMsg(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Continued_Live.do")
    public JSONObject Continued_Live(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.continuedLive(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Register_Msg.do")
    public JSONObject Get_Register_Msg(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.getRegisterMsg(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Add_Register_Other.do")
    public JSONObject Add_Register_Other(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.addRegisterOther(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Assign_Room.do")
    public JSONObject Assign_Room(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.assignRoom(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Check_Room.do")
    public JSONObject Check_Room(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.checkRoom(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Check_Room_Result.do")
    public JSONObject Check_Room_Result(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.checkRoomResult(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Verify_Guest_Living.do")
    public JSONObject Verify_Guest_Living(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.getRegisterIsCheckIn(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Vip_Msg.do")
    public JSONObject Get_Vip_Msg(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.getVipMsg(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Vip_Pay.do")
    public JSONObject Vip_Pay(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.vipPay(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Verify_VC_Room.do")
    public JSONObject verifyVCRoom(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.verifyVCRoom(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Find_PriceCode_By_IdCode.do")
    public JSONObject findPriceCodeByIdCode(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.findPriceCodeByIdCode(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Auto_Checkout.do")
    public JSONObject autoCheckOut(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.checkOutRegister(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    /**
     * 统一结账
     *
     * @param request
     * @return
     */
    @RequestMapping("findRegistGourpRecord.do")
    public JSONObject findRegistGourpRecord(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.findRegistGourpRecord(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("addRegistGroupRecord.do")
    public JSONObject addRegistGroupRecord(@RequestBody JSONObject request) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            resultMap = machineInterface.addRegistGroupRecord(request);
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("webhooks")
    public JSONObject webhooks(HttpServletResponse response, @RequestBody JSONObject param) throws Exception {
        PrintWriter out = response.getWriter();
        out.print(param.getJSONObject("content").toString());
        out.flush();
        return param.getJSONObject("content");
    }

}
