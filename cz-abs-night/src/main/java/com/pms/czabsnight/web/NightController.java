package com.pms.czabsnight.web;

import com.pms.czabsnight.bean.search.NightAuditRecordSearch;
import com.pms.czabsnight.service.NightStepService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.ApplyHotelRequest;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/night/")
@Slf4j
public class NightController {

    @Autowired
    private NightStepService nightStepService;

    @RequestMapping("searchNightStep.do")
    @ResponseBody
    public ResponseData searchNightStep(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.searchNightStep(param);
        return responseData;
    }

    @RequestMapping("searchNightRecord.do")
    @ResponseBody
    public ResponseData searchNightRecord(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.searchNightRecord(param);
        return responseData;
    }

    @RequestMapping("createNightRecord.do")
    @ResponseBody
    public ResponseData createNightRecord(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.createNightRecord(param);
        return responseData;
    }

    @RequestMapping("skipNightStep.do")
    @ResponseBody
    public ResponseData skipNightStep(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.skipNightStep(param);
        return responseData;
    }

    @RequestMapping("keepRoomStatus.do")
    @ResponseBody
    public ResponseData keepRoomStatus(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.keepRoomStatus(param);
        return responseData;
    }

    @RequestMapping("noShowBook.do")
    @ResponseBody
    public ResponseData noShowBook(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.noShowBook(param);
        return responseData;
    }


    @RequestMapping("nightBalance.do")
    @ResponseBody
    public ResponseData nightBalance(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.nightBalance(param);
        return responseData;
    }

    @RequestMapping("repairRoomAddDate.do")
    @ResponseBody
    public ResponseData repairRoomAddDate(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.repairRoomAddDate(param);
        return responseData;
    }

    @RequestMapping("nightOnAccount.do")
    @ResponseBody
    public ResponseData nightOnAccount(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.nightOnAccount(param);
        return responseData;
    }

    @RequestMapping("nightHourOnAccount.do")
    @ResponseBody
    public ResponseData nightHourOnAccount(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.nightHourOnAccount(param);
        return responseData;
    }

    @RequestMapping("addNightAccount.do")
    @ResponseBody
    public ResponseData addNightAccount(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.addNightAccount(param);
        return responseData;
    }



    @RequestMapping("statisticsRoomAccountData.do")
    @ResponseBody
    public ResponseData statisticsRoomAccountData(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.statisticsRoomAccountData(param);
        return responseData;
    }

    @RequestMapping("statisticsGoodsData.do")
    @ResponseBody
    public ResponseData statisticsGoodsData(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.statisticsGoodsData(param);
        return responseData;
    }

    @RequestMapping("statisticsPayMsg.do")
    @ResponseBody
    public ResponseData statisticsPayMsg(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.statisticsPayMsg(param);
        return responseData;
    }

    @RequestMapping("statisticsRoomTypeData.do")
    @ResponseBody
    public ResponseData statisticsRoomTypeData(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.statisticsRoomTypeData(param);
        return responseData;
    }

    @RequestMapping("registerVipNum.do")
    @ResponseBody
    public ResponseData registerVipNum(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.registerVipNum(param);
        return responseData;
    }

    @RequestMapping("statisticsResouceData.do")
    @ResponseBody
    public ResponseData statisticsResouceData(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.statisticsResouceData(param);
        return responseData;
    }


    @RequestMapping("statisticsOTAData.do")
    @ResponseBody
    public ResponseData statisticsOTAData(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.statisticsOTAData(param);
        return responseData;
    }

    @RequestMapping("statisticsArData.do")
    @ResponseBody
    public ResponseData statisticsArData(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.statisticsArData(param);
        return responseData;
    }

    @RequestMapping("finishNightStep.do")
    @ResponseBody
    public ResponseData finishNightStep(@RequestBody JSONObject param) {
        ResponseData responseData = nightStepService.finishNightStep(param);
        return responseData;
    }

    @RequestMapping("searchNightAuditRecord.do")
    @ResponseBody
    public ResponseData searchNightAuditRecord(@RequestBody NightAuditRecordSearch nightAuditRecordSearch) {
        ResponseData responseData = nightStepService.searchNightAuditRecord(nightAuditRecordSearch);
        return responseData;
    }

    @RequestMapping("applyHotel")
    @ResponseBody
    public ResponseData applyHotel(@RequestBody ApplyHotelRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            nightStepService.applyHotel(request);
        } catch (Exception e) {
            log.error("新增酒店处理异常，异常信息为{}",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


}
