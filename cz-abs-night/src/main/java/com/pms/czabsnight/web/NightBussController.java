package com.pms.czabsnight.web;

import com.pms.czabsnight.bean.NightAuditSettingRequest;
import com.pms.czabsnight.service.NightBussService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.AddOrUpdateHotelBusinessDayRequest;
import com.pms.czpmsutils.request.BaseRequest;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/nightbuss/")
public class NightBussController {

    @Autowired
    private NightBussService nightBussService;


    /**
     * 查询所有noshow订单
     * @param param
     * @return
     */
    @RequestMapping("noShowBook.do")
    @ResponseBody
    public ResponseData noShowBook(@RequestBody JSONObject param) {
        ResponseData responseData = nightBussService.noShowBook(param);
        return responseData;
    }

    /**
     * 查询应到未到订单
     * @param param
     * @return
     */
    @RequestMapping("overtimeRegist.do")
    @ResponseBody
    public ResponseData overtimeRegist(@RequestBody JSONObject param) {
        ResponseData responseData = nightBussService.overtimeRegist(param);
        return responseData;
    }

    /**
     * 查询应到未到订单
     * @param param
     * @return
     */
    @RequestMapping("overtimeHourRoom.do")
    @ResponseBody
    public ResponseData overtimeHourRoom(@RequestBody JSONObject param) {
        ResponseData responseData = nightBussService.overtimeHourRoom(param);
        return responseData;
    }


    /**
     * 查询应到未到订单
     * @param param
     * @return
     */
    @RequestMapping("expireRepairRoom.do")
    @ResponseBody
    public ResponseData expireRepairRoom(@RequestBody JSONObject param) {
        ResponseData responseData = nightBussService.expireRepairRoom(param);
        return responseData;
    }


    /**
     * 查询在住订单
     * @param param
     * @return
     */
    @RequestMapping("checkinRoom.do")
    @ResponseBody
    public ResponseData checkinRoom(@RequestBody JSONObject param) {
        ResponseData responseData = nightBussService.checkinRoom(param);
        return responseData;
    }

    /**
     * 添加或修改自动夜审设置
     * @param param
     * @return
     */
    @RequestMapping("addOrUpdateNightSetting.do")
    @ResponseBody
    public ResponseData addOrUpdateNightSetting(@RequestBody NightAuditSettingRequest param) {
        ResponseData responseData = nightBussService.addOrUpdateNightSetting(param);
        return responseData;
    }

    /**
     * 查询自动夜审设置
     * @param param
     * @return
     */
    @RequestMapping("searchNightSetting.do")
    @ResponseBody
    public ResponseData searchNightSetting(@RequestBody JSONObject param) {
        ResponseData responseData = nightBussService.searchNightSetting(param);
        return responseData;
    }

    @RequestMapping("addOrUpdateHotelBusinessDay.do")
    @ResponseBody
    public ResponseData addOrUpdateHotelBusinessDay(@RequestBody AddOrUpdateHotelBusinessDayRequest addOrUpdateHotelBusinessDayRequest) {
        ResponseData responseData = nightBussService.addOrUpdateHotelBusinessDay(addOrUpdateHotelBusinessDayRequest);
        return responseData;
    }

    @RequestMapping("getHotelBusinessDay.do")
    @ResponseBody
    public ResponseData getHotelBusinessDay(@RequestBody BaseRequest baseRequest) {
        ResponseData responseData = nightBussService.getHotelBusinessDay(baseRequest);
        return responseData;
    }

}
