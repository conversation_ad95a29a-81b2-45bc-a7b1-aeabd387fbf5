package com.pms.czabsnight.web;

import com.github.pagehelper.Page;
import com.pms.czabsnight.service.machine.MachineInterface;
import com.pms.czaccount.service.alipay.AliPayService;
import com.pms.czaccount.service.wechat.WeChatPayService;
import com.pms.czhotelfoundation.bean.MuuidKey;
import com.pms.czhotelfoundation.bean.search.MuuidKeySearch;
import com.pms.czhotelfoundation.dao.setting.MuuidKeyDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.ER;
import net.sf.json.JSONObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@RestController
@RequestMapping("ElinesoftApi/api/")
public class MachineOtherController {
    private static final Logger log = LogManager.getLogger(MachineOtherController.class);
    @Autowired
    private MachineInterface machineInterface;

    @Autowired
    private MuuidKeyDao muuidKeyDao;

    @Autowired
    private WeChatPayService weChatPayService;

    @Autowired
    private AliPayService aliPayService;

    @Resource(name = "stringRedisTemplate")
    protected StringRedisTemplate stringRedisTemplate;

    @RequestMapping("Get_RoomType_Info_List")
    public JSONObject Get_RoomType_Info_List( HttpServletRequest request ) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getRoomTypeInfoList(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_RoomType_Info_Listasd")
    public JSONObject Get_RoomType_Info_Listasd( HttpServletRequest request ) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getRoomTypeInfoList(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Arriving_List")
    public JSONObject Get_Arriving_List( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getArrivingList(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Get_Avail_Room_List")
    public JSONObject Get_Avail_Room_List( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getAvailRoomList(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Lock_Room")
    public JSONObject Lock_Room( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.lockRoom(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Register_Check_In")
    public JSONObject Register_Check_In( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.registerCheckIn(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Update_GuestInfo")
    public JSONObject Update_GuestInfo( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.updateGuestInfo(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Add_Transaction")
    public JSONObject Add_Transaction( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.addTransaction(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Make_Guest_Card")
    public JSONObject Make_Guest_Card( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.makeGuestCard(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Msg_Continue")
    public JSONObject Get_Msg_Continue( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getContinueLiveMsg(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Continued_Live")
    public JSONObject Continued_Live( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.continuedLive(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Register_Msg")
    public JSONObject Get_Register_Msg( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getRegisterMsg(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Add_Register_Other")
    public JSONObject Add_Register_Other( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.addRegisterOther(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Assign_Room")
    public JSONObject Assign_Room( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.assignRoom(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Check_Room")
    public JSONObject Check_Room( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.checkRoom(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Check_Room_Result")
    public JSONObject Check_Room_Result( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.checkRoomResult(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Verify_Guest_Living")
    public JSONObject Verify_Guest_Living( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getRegisterIsCheckIn(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Vip_Msg")
    public JSONObject Get_Vip_Msg( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getVipMsg(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Vip_Pay")
    public JSONObject Vip_Pay( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.vipPay(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Verify_VC_Room")
    public JSONObject verifyVCRoom( HttpServletRequest request){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.verifyVCRoom(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Find_PriceCode_By_IdCode")
    public JSONObject findPriceCodeByIdCode( HttpServletRequest request){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.findPriceCodeByIdCode(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Auto_Checkout")
    public JSONObject autoCheckOut( HttpServletRequest request){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.checkOutRegister(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    /**
     * 统一结账
     * @param request
     * @return
     */
    @RequestMapping("findRegistGourpRecord")
    public JSONObject findRegistGourpRecord( HttpServletRequest request){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.findRegistGourpRecord(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("addRegistGroupRecord")
    public JSONObject addRegistGroupRecord( HttpServletRequest request){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.addRegistGroupRecord(getParam(HotelUtils.readReqeust(request)));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    /**
     * 获取微信二维码信息
     *
     * @param payMsg pub
     *               参数: money 单位元
     * @return
     */
    @RequestMapping("Pay_Qrcode")
    @ResponseBody
    public Map<String, Object> getWxQrcode(@RequestBody JSONObject payMsg) throws Exception{
        payMsg = getParam(payMsg);
        int payType = payMsg.getInt("payType");
        if (payType == 2) {
            return weChatPayService.getWeChatQrcode(payMsg);
        } else {
            return aliPayService.getAlipayQrCode(payMsg);
        }
    }

    /**
     * 获取支付结果
     *
     * @param payMsg 参数: money 单位元
     * @return
     */
    @RequestMapping("Pay_Result")
    @ResponseBody
    public Map<String, Object> getPayResult(@RequestBody JSONObject payMsg) throws Exception {
        //  JSONObject payMsg = JSONObject.fromObject(request.getAttribute(ER.PARAM_MAP));
        int payType = payMsg.getInt("payType");
        payMsg = getParam(payMsg);
        Map<String, Object> rt = new HashMap<>();
        if (payType == 2) {
            rt = weChatPayService.handleWeChatPayResult(payMsg);
        } else {
            rt = aliPayService.handleAliPayResult(payMsg);
        }

        //支付成功 将mainId放入Redis 半小时
        if (rt.getOrDefault(ER.RES, "").equals(ER.SUCC)) {
            String mainId = rt.getOrDefault("mainId", "").toString();
            String money = rt.getOrDefault("money", "").toString();
            mainId = String.format("payMainId:%s", mainId);
            stringRedisTemplate.opsForValue().set(mainId, money, 30L, TimeUnit.MINUTES);
        }

        return rt;
    }

    /**
     * 反扫
     *
     * @param payMsg 参数: money 单位元
     * @return
     */
    @RequestMapping("Micropay")
    @ResponseBody
    public Map<String, Object> micropay(@RequestBody JSONObject payMsg) throws Exception{
        // JSONObject payMsg = JSONObject.fromObject(request.getAttribute(ER.PARAM_MAP));
        payMsg = getParam(payMsg);
        int payType = payMsg.getInt("payType");
        if (payType == 2) {
            return weChatPayService.weChatMicropay(payMsg);
        } else {
            return aliPayService.alipayMicropay(payMsg);
        }
    }

    /**
     * 关闭交易
     *
     * @param payMsg 参数: money 单位元
     * @return
     */
    @RequestMapping("Pay_Close")
    @ResponseBody
    public Map<String, Object> payClose(@RequestBody JSONObject payMsg) throws Exception{
        //  JSONObject payMsg = JSONObject.fromObject(request.getAttribute(ER.PARAM_MAP));
        payMsg = getParam(payMsg);
        int payType = payMsg.getInt("payType");
        if (payType == 2) {
            return weChatPayService.weChatPayClose(payMsg);
        } else {
            return aliPayService.alipayClose(payMsg);
        }
    }

    JSONObject getParam(JSONObject param) throws Exception{

        String appKey = param.getString("appKey");
        MuuidKeySearch muuidKeySearch = new MuuidKeySearch();
        muuidKeySearch.setAppKey(appKey);
        Page<MuuidKey> muuidKeys = muuidKeyDao.selectBySearch(muuidKeySearch);
        if(muuidKeys.size()<1){
            throw new Exception("未查询到正确的key信息");
        }
        MuuidKey muuidKey = muuidKeys.get(0);
        param.put("appKey",muuidKey.getUuid());
        param.put(ER.SESSION_TOKEN,muuidKey.getUuid());
        return param;
    }

}
