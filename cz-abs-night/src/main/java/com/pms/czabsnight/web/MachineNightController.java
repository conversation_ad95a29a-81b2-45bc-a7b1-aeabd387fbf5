package com.pms.czabsnight.web;

import com.pms.czabsnight.service.machine.MachineInterface;
import com.pms.czpmsutils.HotelUtils;
import net.sf.json.JSONObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping("/hotel/nightbuss/")
public class MachineNightController {
    private static final Logger log = LogManager.getLogger(MachineNightController.class);
    @Autowired
    private MachineInterface machineInterface;

    @RequestMapping("Get_RoomType_Info_List.do")
    public JSONObject Get_RoomType_Info_List( HttpServletRequest request ) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getRoomTypeInfoList(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_RoomType_Info_Listasd.do")
    public JSONObject Get_RoomType_Info_Listasd( HttpServletRequest request ) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getRoomTypeInfoList(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Arriving_List.do")
    public JSONObject Get_Arriving_List( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getArrivingList(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Get_Avail_Room_List.do")
    public JSONObject Get_Avail_Room_List( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getAvailRoomList(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Lock_Room.do")
    public JSONObject Lock_Room( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.lockRoom(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Register_Check_In.do")
    public JSONObject Register_Check_In( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.registerCheckIn(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Update_GuestInfo.do")
    public JSONObject Update_GuestInfo( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.updateGuestInfo(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Add_Transaction.do")
    public JSONObject Add_Transaction( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.addTransaction(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Make_Guest_Card.do")
    public JSONObject Make_Guest_Card( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.makeGuestCard(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Msg_Continue.do")
    public JSONObject Get_Msg_Continue( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getContinueLiveMsg(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Continued_Live.do")
    public JSONObject Continued_Live( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.continuedLive(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Register_Msg.do")
    public JSONObject Get_Register_Msg( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getRegisterMsg(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Add_Register_Other.do")
    public JSONObject Add_Register_Other( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.addRegisterOther(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Assign_Room.do")
    public JSONObject Assign_Room( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.assignRoom(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Check_Room.do")
    public JSONObject Check_Room( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.checkRoom(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Check_Room_Result.do")
    public JSONObject Check_Room_Result( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.checkRoomResult(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Verify_Guest_Living.do")
    public JSONObject Verify_Guest_Living( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getRegisterIsCheckIn(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Get_Vip_Msg.do")
    public JSONObject Get_Vip_Msg( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.getVipMsg(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("Vip_Pay.do")
    public JSONObject Vip_Pay( HttpServletRequest request ){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.vipPay(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Verify_VC_Room.do")
    public JSONObject verifyVCRoom( HttpServletRequest request){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.verifyVCRoom(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
        }
        return resultMap;
    }

    @RequestMapping("Find_PriceCode_By_IdCode.do")
    public JSONObject findPriceCodeByIdCode( HttpServletRequest request){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.findPriceCodeByIdCode(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @RequestMapping("Auto_Checkout.do")
    public JSONObject autoCheckOut( HttpServletRequest request){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.checkOutRegister(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    /**
     * 统一结账
     * @param request
     * @return
     */
    @RequestMapping("findRegistGourpRecord.do")
    public JSONObject findRegistGourpRecord( HttpServletRequest request){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.findRegistGourpRecord(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    @RequestMapping("addRegistGroupRecord.do")
    public JSONObject addRegistGroupRecord( HttpServletRequest request){
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result","False");
        try {
            resultMap = machineInterface.addRegistGroupRecord(HotelUtils.readReqeust(request));
        }catch (Exception e){
            resultMap.put("Result","False");
            resultMap.put("Msg",e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }
}
