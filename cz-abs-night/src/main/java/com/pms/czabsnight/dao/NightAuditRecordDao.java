package com.pms.czabsnight.dao;


import com.github.pagehelper.Page;
import com.pms.czabsnight.bean.NightAuditRecord;
import com.pms.czabsnight.bean.search.NightAuditRecordSearch;

public interface NightAuditRecordDao {


    public Integer insert(NightAuditRecord nightAuditRecord) ;

    public Integer update(NightAuditRecord nightAuditRecord);

    public Integer delete(Integer nightAuditRecordId);

    public NightAuditRecord selectById(Integer nightAuditRecordId);

    public Page<NightAuditRecord> selectBySearch(NightAuditRecordSearch nightAuditRecordSearch);

}
