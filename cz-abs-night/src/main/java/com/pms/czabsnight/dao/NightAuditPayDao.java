package com.pms.czabsnight.dao;


import com.pms.czabsnight.bean.NightAuditPay;
import com.pms.czabsnight.bean.search.NightAuditPaySearch;

import java.util.List;

public interface NightAuditPayDao {

    public Integer insert(NightAuditPay nightAuditPay);

    public Integer update(NightAuditPay nightAuditPay);

    public Integer delete(Integer nightAuditPayId);

    public NightAuditPay selectById(Integer nightAuditPayId);

    public List<NightAuditPay> selectBySearch(NightAuditPaySearch nightAuditPaySearch);

    public List<NightAuditPay> selectBySearchSummary(NightAuditPaySearch nightAuditPaySearch);

}
