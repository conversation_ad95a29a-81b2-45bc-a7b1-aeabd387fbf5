package com.pms.czabsnight.dao;


import com.pms.czabsnight.bean.NightAuditRoomType;
import com.pms.czabsnight.bean.search.NightAuditRoomTypeSearch;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.search.AccountSearch;

import java.util.List;

public interface NightAuditRoomTypeDao {

    public Integer insert(NightAuditRoomType nightAuditRoomType) ;

    public Integer update(NightAuditRoomType nightAuditRoomType);

    public Integer delete(Integer nightAuditRoomTypeId) ;

    public NightAuditRoomType selectById(Integer nightAuditRoomTypeId);

    public List<NightAuditRoomType> selectBySearch(NightAuditRoomTypeSearch nightAuditRoomTypeSearch);

    public List<NightAuditRoomType> selectBySearchSummary(NightAuditRoomTypeSearch nightAuditRoomTypeSearch);

    public List<Account> searchRoomTypeRevenueSummary(AccountSearch account);

}
