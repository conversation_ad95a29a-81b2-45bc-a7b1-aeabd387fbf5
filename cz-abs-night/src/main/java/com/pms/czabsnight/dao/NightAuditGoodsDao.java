package com.pms.czabsnight.dao;


import com.pms.czabsnight.bean.NightAuditGoods;
import com.pms.czabsnight.bean.search.NightAuditGoodsSearch;

import java.util.List;

public interface NightAuditGoodsDao {

    public Integer insert(NightAuditGoods NightAuditGoods);

    public Integer update(NightAuditGoods NightAuditGoods);

    public Integer delete(Integer NightAuditGoodsId);

    public NightAuditGoods selectById(Integer NightAuditGoodsId);

    public List<NightAuditGoods> selectBySearch(NightAuditGoodsSearch NightAuditGoodsSearch);

}
