package com.pms.czabsnight.dao;

import com.github.pagehelper.Page;
import com.pms.czabsnight.bean.NightAuditBalance;
import com.pms.czabsnight.bean.search.NightAuditBalanceSearch;

public interface NightAuditBalanceDao{

	public Integer insert(NightAuditBalance nightAuditBalance);

	public Integer update(NightAuditBalance nightAuditBalance);

	public Integer delete(Integer id);

	public NightAuditBalance selectById(Integer id);

	public Page<NightAuditBalance> selectBySearch( NightAuditBalanceSearch nightAuditBalanceSearch);

}
