package com.pms.czabsnight.dao;


import com.pms.czabsnight.bean.NightAuditCompany;
import com.pms.czabsnight.bean.search.NightAuditCompanySearch;

import java.util.List;

public interface NightAuditCompanyDao {
    public Integer insert(NightAuditCompany nightAuditCompany);

    public Integer update(NightAuditCompany nightAuditCompany);

    public Integer delete(Integer nightAuditCompanyId);

    public NightAuditCompany selectById(Integer nightAuditCompanyId);

    public List<NightAuditCompany> selectBySearch(NightAuditCompanySearch nightAuditCompanySearch);
}
