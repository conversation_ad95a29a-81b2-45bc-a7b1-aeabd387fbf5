package com.pms.czabsnight.dao;

import com.github.pagehelper.Page;
import com.pms.czabsnight.bean.NigthAuditArPayment;
import com.pms.czabsnight.bean.search.NigthAuditArPaymentSearch;

public interface NigthAuditArPaymentDao{

	public Integer insert(NigthAuditArPayment nigthAuditArPayment);

	public Integer update(NigthAuditArPayment nigthAuditArPayment);

	public Integer delete(Integer id);

	public NigthAuditArPayment selectById(Integer id);

	public Page<NigthAuditArPayment> selectBySearch( NigthAuditArPaymentSearch nigthAuditArPaymentSearch);

	public Page<NigthAuditArPayment> selectBySearchSummary( NigthAuditArPaymentSearch nigthAuditArPaymentSearch);

}
