package com.pms.czabsnight.dao;


import com.github.pagehelper.Page;
import com.pms.czabsnight.bean.NightAuditOta;
import com.pms.czabsnight.bean.search.NightAuditOtaSearch;

import java.util.List;

public interface NightAuditOtaDao{

	public Integer insert(NightAuditOta nightAuditOta);

	public Integer update(NightAuditOta nightAuditOta);

	public Integer delete(Integer id);

	public NightAuditOta selectById(Integer id);

	public Page<NightAuditOta> selectBySearch(NightAuditOtaSearch nightAuditOtaSearch);

	public Page<NightAuditOta> selectBySearchSummary(NightAuditOtaSearch nightAuditOtaSearch);

	public Integer addNightAuditOtaList(List nightAuditOtas);

}
