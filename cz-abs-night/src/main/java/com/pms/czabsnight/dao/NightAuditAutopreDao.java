package com.pms.czabsnight.dao;


import com.pms.czabsnight.bean.NightAuditAutopre;
import com.pms.czabsnight.bean.search.NightAuditAutopreSearch;

import java.util.List;

public interface NightAuditAutopreDao {

    public Integer insert(NightAuditAutopre NightAuditAutopre);

    public Integer update(NightAuditAutopre NightAuditAutopre);

    public Integer delete(Integer NightAuditAutopreId);

    public NightAuditAutopre selectById(Integer NightAuditAutopreId);

    public List<NightAuditAutopre> selectBySearch(NightAuditAutopreSearch NightAuditAutopreSearch);

}
