package com.pms.czabsnight.dao;


import com.pms.czabsnight.bean.NightAuditRoomStatus;
import com.pms.czabsnight.bean.search.NightAuditRoomStatusSearch;

import java.util.List;

public interface NightAuditRoomStatusDao {

    public Integer insert(NightAuditRoomStatus nightAuditRoomStatus) ;

    public Integer update(NightAuditRoomStatus nightAuditRoomStatus);

    public Integer delete(Integer nightAuditRoomStatusId) ;

    public NightAuditRoomStatus selectById(Integer nightAuditRoomStatusId);

    public List<NightAuditRoomStatus> selectBySearch(NightAuditRoomStatusSearch nightAuditRoomStatusSearch);

}
