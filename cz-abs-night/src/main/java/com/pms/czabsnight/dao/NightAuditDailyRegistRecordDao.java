package com.pms.czabsnight.dao;


import com.pms.czabsnight.bean.NightAuditDailyRegistRecord;
import com.pms.czabsnight.bean.search.NightAuditDailyRegistRecordSearch;

import java.util.List;

public interface NightAuditDailyRegistRecordDao {

    public Integer insert(NightAuditDailyRegistRecord nightAuditDailyRegistRecord) ;

    public Integer update(NightAuditDailyRegistRecord nightAuditDailyRegistRecord);

    public Integer delete(Integer nightAuditDailyRegistRecordId) ;

    public NightAuditDailyRegistRecord selectById(Integer nightAuditDailyRegistRecordId);

    public List<NightAuditDailyRegistRecord> selectBySearch(NightAuditDailyRegistRecordSearch nightAuditDailyRegistRecordSearch);

    public List<NightAuditDailyRegistRecord> selectBySearchSummary(NightAuditDailyRegistRecordSearch nightAuditDailyRegistRecordSearch);


}
