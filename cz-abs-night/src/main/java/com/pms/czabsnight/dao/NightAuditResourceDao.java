package com.pms.czabsnight.dao;

import com.pms.czabsnight.bean.NightAuditResource;
import com.pms.czabsnight.bean.search.NightAuditResourceSearch;

import java.util.List;

public interface NightAuditResourceDao {

    public Integer insert(NightAuditResource nightAuditResource) ;

    public Integer update(NightAuditResource nightAuditResource);

    public Integer delete(Integer nightAuditResourceId);

    public NightAuditResource selectById(Integer nightAuditResourceId);

    public List<NightAuditResource> selectBySearchGroup(NightAuditResourceSearch nightAuditResourceSearch);

    public List<NightAuditResource> selectBySearch(NightAuditResourceSearch nightAuditResourceSearch);

}
