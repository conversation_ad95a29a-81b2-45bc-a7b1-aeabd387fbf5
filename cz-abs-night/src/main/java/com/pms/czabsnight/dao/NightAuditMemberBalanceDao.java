package com.pms.czabsnight.dao;

import com.github.pagehelper.Page;
import com.pms.czabsnight.bean.NightAuditMemberBalance;
import com.pms.czabsnight.bean.search.NightAuditMemberBalanceSearch;

public interface NightAuditMemberBalanceDao{

	public Integer insert(NightAuditMemberBalance nightAuditMemberBalance);

	public Integer update(NightAuditMemberBalance nightAuditMemberBalance);

	public Integer delete(Integer id);

	public NightAuditMemberBalance selectById(Integer id);

	public Page<NightAuditMemberBalance> selectBySearch( NightAuditMemberBalanceSearch nightAuditMemberBalanceSearch);

}
