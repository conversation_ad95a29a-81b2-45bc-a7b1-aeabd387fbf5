package com.pms.czabsnight.dao;


import com.pms.czabsnight.bean.NightAuditMember;
import com.pms.czabsnight.bean.search.NightAuditMemberSearch;

import java.util.List;

public interface NightAuditMemberDao {

    public Integer insert(NightAuditMember nightAuditMember);

    public Integer update(NightAuditMember nightAuditMember);

    public Integer delete(Integer nightAuditMemberId);

    public NightAuditMember selectById(Integer nightAuditMemberId);

    public List<NightAuditMember> selectBySearch(NightAuditMemberSearch nightAuditMemberSearch);

    public List<NightAuditMember> selectBySearchSummary(NightAuditMemberSearch nightAuditMemberSearch);
}
