package com.pms.czabsnight.config;

import com.pms.czpmsutils.RSAUtils;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
@ConfigurationProperties(prefix = "spring.datasource")
public class DataSourceConfig {

    private static final Logger log = LogManager.getLogger(DataSourceConfig.class);
    private String url;
    private String username;
    private String password;
    private HikariConfig hikari;

    @Bean
    public DataSource dataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl(decrypt(url));
        dataSource.setUsername(decrypt(username));
        dataSource.setPassword(decrypt(password));
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");

        // 设置 HikariCP 配置
        dataSource.setConnectionTimeout(hikari.getConnectionTimeout());
        dataSource.setMaximumPoolSize(hikari.getMaximumPoolSize());
        dataSource.setMinimumIdle(hikari.getMinimumIdle());
        dataSource.setIdleTimeout(hikari.getIdleTimeout());
        dataSource.setMaxLifetime(hikari.getMaxLifetime());
        dataSource.setConnectionTestQuery(hikari.getConnectionTestQuery());

        return dataSource;
    }

    private String decrypt(String encryptValue) {
        try {
            return RSAUtils.getStringDecrypt(encryptValue);
        } catch (Exception e){
            log.error("",e);
            throw new RuntimeException("数据库连接信息解密失败");
        }
    }

    // Getters and Setters

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public HikariConfig getHikari() {
        return hikari;
    }

    public void setHikari(HikariConfig hikari) {
        this.hikari = hikari;
    }
}
