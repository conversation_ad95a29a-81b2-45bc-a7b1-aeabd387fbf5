package com.pms.czabsnight.config;

import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;

@Component
public class HikariCPMonitor {

    private static final Logger logger = LoggerFactory.getLogger(HikariCPMonitor.class);

    @Autowired
    private DataSource dataSource;

    @Scheduled(fixedRate = 60000) // 每隔60秒执行一次
    public void monitorHikariCP() {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
            logger.info("HikariCP Pool Status:");
            logger.info("Active Connections: " + hikariDataSource.getHikariPoolMXBean().getActiveConnections());
            logger.info("Idle Connections: " + hikariDataSource.getHikariPoolMXBean().getIdleConnections());
            logger.info("Total Connections: " + hikariDataSource.getHikariPoolMXBean().getTotalConnections());
            logger.info("Threads Awaiting Connection: " + hikariDataSource.getHikariPoolMXBean().getThreadsAwaitingConnection());
        } else {
            logger.info("DataSource is not an instance of HikariDataSource");
        }
    }
}
