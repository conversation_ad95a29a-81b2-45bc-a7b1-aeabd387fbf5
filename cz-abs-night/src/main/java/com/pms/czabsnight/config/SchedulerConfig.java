package com.pms.czabsnight.config;

import com.pms.czpmsutils.RSAUtils;
import net.sf.json.JSONObject;
import org.quartz.Scheduler;
import org.quartz.ee.servlet.QuartzInitializerListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.PropertiesFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import java.util.Properties;


@Configuration
public class SchedulerConfig {

    private static final Logger log = LoggerFactory.getLogger(SchedulerConfig.class);
    @Value("${quartzname}")
    private String quartzname;

    @Bean(name="SchedulerFactory")
    public SchedulerFactoryBean schedulerFactoryBean() throws Exception {
        SchedulerFactoryBean factory = new SchedulerFactoryBean();
        factory.setQuartzProperties(quartzProperties());
        return factory;
    }

    @Bean
    public Properties quartzProperties() throws Exception {
        PropertiesFactoryBean propertiesFactoryBean = new PropertiesFactoryBean();
        propertiesFactoryBean.setLocation(new ClassPathResource(quartzname));
        //在quartz.properties中的属性被读取并注入后再初始化对象
        propertiesFactoryBean.afterPropertiesSet();
        String property = propertiesFactoryBean.getObject().getProperty("org.quartz.dataSource.qzDS.password");
        String pwd = RSAUtils.getStringDecrypt(property);
        propertiesFactoryBean.getObject().setProperty("org.quartz.dataSource.qzDS.password",pwd);

        // userName
        propertiesFactoryBean.getObject().setProperty("org.quartz.dataSource.qzDS.user",
                RSAUtils.getStringDecrypt(propertiesFactoryBean.getObject().getProperty("org.quartz.dataSource.qzDS.user")));

       propertiesFactoryBean.getObject().setProperty("org.quartz.dataSource.qzDS.URL",
                RSAUtils.getStringDecrypt(propertiesFactoryBean.getObject().getProperty("org.quartz.dataSource.qzDS.URL")));

        JSONObject jsonObject = JSONObject.fromObject(propertiesFactoryBean.getObject());
        return propertiesFactoryBean.getObject();
    }

    /**
     * quartz初始化监听器
     * 这个监听器可以监听到工程的启动，在工程停止再启动时可以让已有的定时任务继续进行。
     * @return
     */
    @Bean
    public QuartzInitializerListener executorListener() {
        return new QuartzInitializerListener();
    }

    /**
     *
     *通过SchedulerFactoryBean获取Scheduler的实例
     */
    @Bean(name="Scheduler")
    public Scheduler scheduler() throws Exception {
        return schedulerFactoryBean().getScheduler();
    }

}
