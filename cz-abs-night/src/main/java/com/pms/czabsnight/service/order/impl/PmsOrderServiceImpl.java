package com.pms.czabsnight.service.order.impl;

import com.github.pagehelper.Page;
import com.pms.czabsnight.bean.AddArAccount;
import com.pms.czabsnight.bean.CheckInRegist;
import com.pms.czabsnight.service.order.AccountMacService;
import com.pms.czabsnight.service.order.PmsOrderService;
import com.pms.czabsnight.service.order.transaction.PmsOrderTransactionService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountThirdPayRecode;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.price.RoomRateCode;
import com.pms.czhotelfoundation.bean.price.RoomRateCodeSpecific;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSpecificSearch;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.search.RoomAuxiliaryRelationSearch;
import com.pms.czhotelfoundation.dao.code.HotelBusinessDayDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeSpecificDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomCheckRecordDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czhotelfoundation.service.room.RoomService;
import com.pms.czhotelfoundation.service.room.transaction.RoomTransactionService;
import com.pms.czhotelfoundation.service.smart.AudioService;
import com.pms.czhotelfoundation.service.zimg.ZimgService;
import com.pms.czmembership.bean.company.HotelCompanyAccount;
import com.pms.czmembership.bean.company.HotelCompanyAccountInfo;
import com.pms.czmembership.bean.member.CardCheckinRecord;
import com.pms.czmembership.bean.member.CardInfo;
import com.pms.czmembership.bean.member.search.CardCheckinRecordSearch;
import com.pms.czmembership.dao.company.HotelCompanyAccountDao;
import com.pms.czmembership.dao.company.HotelCompanyAccountInfoDao;
import com.pms.czmembership.dao.company.HotelCompanyArRecodeDao;
import com.pms.czmembership.dao.company.HotelCompanyInfoDao;
import com.pms.czmembership.dao.member.CardCheckinRecordDao;
import com.pms.czmembership.dao.member.CardInfoDao;
import com.pms.czmembership.service.memeber.MemberService;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.constant.AddressParam;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.SYS_RESOURCE;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.hotelsetting.HOTEL_SETTING;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.regist.CHECK_IN_TYPE;
import com.pms.czpmsutils.constant.room.ROOM_AUXILIARY;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.*;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.search.*;
import com.pms.pmsorder.dao.*;
import com.pms.pmsorder.service.RegistService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Primary
@Slf4j
public class PmsOrderServiceImpl extends BaseService implements PmsOrderService {

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private ZimgService zimgService;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private RoomTypeDao roomTypeDao;


    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private RegistGroupDao registGroupDao;

    @Autowired
    private RoomTransactionService roomTransactionService;

    @Autowired
    private RoomService roomService;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private PmsOrderTransactionService pmsOrderTransactionService;

    @Autowired
    private RegistService registService;

    @Autowired
    private AccountMacService accountMacService;


    private BaseService baseService = this;

    private AudioService audioService;


    @Autowired
    private RoomRateCodeSpecificDao roomRateCodeSpecificDao;

    @Autowired
    private RoomRateCodeDao roomRateCodeDao;

    @Autowired
    private HotelBusinessDayDao hotelBusinessDayDao;

    @Autowired
    private RoomCheckRecordDao roomCheckRecordDao;

    @Autowired
    private CardInfoDao cardInfoDao;

    @Autowired
    private CardCheckinRecordDao cardCheckinRecordDao;
    @Autowired
    private MemberService memberService;

    @Autowired
    private HotelCompanyAccountDao hotelCompanyAccountDao;

    @Autowired
    private HotelCompanyInfoDao hotelCompanyInfoDao;

    @Autowired
    private HotelCompanyArRecodeDao hotelCompanyArRecodeDao;

    @Autowired
    private HotelCompanyAccountInfoDao hotelCompanyAccountInfoDao;

    @Autowired
    private PersonInfoDao personInfoDao;

    /**
     * 入住---团队、散客、联房
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData blendCheckIn(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);


            // 预订单参数
            String checkInParam = URLDecoder.decode(param.getString("checkInParam"), "utf-8");
            final JSONObject checkInJson = JSONObject.fromObject(checkInParam);

            JSONArray roomList = checkInJson.getJSONArray("roomList");

            // 房价码
            int rateId = checkInJson.getInt("rateId");
            RoomRateCode roomRateCode = roomRateCodeDao.selectById(rateId);
            String rateCode = "";
            if (roomRateCode == null) {
                rateCode = checkInJson.getString("rateCode");
            } else {
                rateCode = roomRateCode.getRateCodeName();
            }
            RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
            roomRateCodeSpecificSearch.setHid(user.getHid());
            roomRateCodeSpecificSearch.setRateId(rateId);
            List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);
            Map<Integer, RoomRateCodeSpecific> rateMap = roomRateCodeSpecifics.stream().collect(Collectors.toMap(RoomRateCodeSpecific::getRoomTypeId, a -> a, (k1, k2) -> k1));

            // 离店日期
            Date endDate = HotelUtils.parseStr2Date(checkInJson.getString("endTime") + " " + checkInJson.getString("keepTime") + ":00");
            //入住天数
            int dayCount = HotelUtils.getAllDayListBetweenDate(checkInJson.getString("startTime"), checkInJson.getString("endTime")).size();

            // 辅助房态集合
            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            StringBuilder guestNames = new StringBuilder();

            // 1.创建最初的regist

            Regist regist = new Regist();
            regist.setHid(user.getHid());
            regist.setHotelGroupId(user.getHotelGroupId());
            regist.setClassId(user.getClassId());
            regist.setCreateTime(new Date());
            regist.setCreateUserId(user.getUserId());
            regist.setCreateUserName(user.getUserName());
            regist.setBusinessDay(user.getBusinessDay());
            regist.setClassId(user.getClassId());
            regist.setRoomRateCodeId(rateId);
            regist.setRoomRateCodeName(rateCode);
            regist.setDayCount(dayCount);
            regist.setCheckinTime(new Date());
            regist.setMacRegist(1);
            regist.setCheckoutTime(endDate);


            // 判断是否是凌晨房
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setHid(user.getHid());
            hotelSettingByParamId.setParamId(HOTEL_SETTING.NIGHT_ROOM);

            Object minObj = this.findHotelSettingByParamId(hotelSettingByParamId);

            Boolean isNightRoom = false;

            if (minObj != null) {
                int hour = Integer.parseInt(minObj.toString());

                int hours = regist.getCheckinTime().getHours();

                if (hour > hours) {
                    Date newDate = HotelUtils.addDayGetNewDate(endDate, -1);
                    if (newDate.getTime() > new Date().getTime()) {
                        regist.setCheckoutTime(newDate);
                    }
                }

            }

            regist.setRemark("");

            regist.setRegistYear(user.getBusinessYear());
            regist.setRegistYearMonth(user.getBusinessMonth());

            if (checkInJson.get("remark") != null) {
                regist.setRemark(checkInJson.getString("remark"));
            }

            regist.setAutoCheckout(0);
            regist.setCheckinType(checkInJson.getInt("checkInType"));

            // 入住方式 0前台 1自助机
            regist.setCheckinMode(0);
            if (checkInJson.get("checkInModel") != null) {
                regist.setCheckinMode(checkInJson.getInt("checkInModel"));
            }

            int resourceId = checkInJson.getInt("resourceId");
            regist.setResourceId(resourceId);

            if (checkInJson.get("resourceName") != null) {
                regist.setResourceName(checkInJson.getString("resourceName"));
            }

            // 1.散客 2.会员
            if (resourceId == 2) {
                JSONObject memberInfo = checkInJson.getJSONObject("vipMsg");
                regist.setMemberId(memberInfo.getInt("cardId"));
                regist.setMemberCard(memberInfo.getString("cardNo"));

              /*  RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                roomAuxiliaryRelation.setHid(user.getHid());
                roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.VIP);
                roomAuxiliaryRelations.add(roomAuxiliaryRelation);*/

            }
            if (resourceId == 3 || resourceId == 4 || resourceId == 5) {
                JSONObject arMsg = checkInJson.getJSONObject("arMsg");
                regist.setCompanyId(arMsg.getInt("arId"));
                regist.setCompayName(arMsg.getString("arName"));
                if (checkInJson.get("arAntMsg") != null && !"".equals(checkInJson.getString("arAntMsg"))) {
                    regist.setCompanyAccountId(checkInJson.getJSONObject("arAntMsg").getInt("id"));
                }

            }

            Integer checkinType = regist.getCheckinType();
            if (checkinType == CHECK_IN_TYPE.CIT_HOURDAY) {

                RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                roomAuxiliaryRelation.setHid(user.getHid());
                roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.HOUR_ROOM);
                roomAuxiliaryRelations.add(roomAuxiliaryRelation);

            }


            if (true) {
                regist.setCheckinMode(1);
                RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                roomAuxiliaryRelation.setHid(user.getHid());
                roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.MACHINE);
                roomAuxiliaryRelations.add(roomAuxiliaryRelation);
            }

            regist.setState(0);
            // 2.检测不是团队团队
            int isGroup = 0;
            if (checkInJson.get("isGroup") != null) {
                isGroup = checkInJson.getInt("isGroup");
            }
            boolean isTeam = !(isGroup == 0 && roomList.size() == 1);
            RegistGroup registGroup = null;
            //没有设置团队，以及联房
            if (isTeam) {
                registGroup = new RegistGroup();
                registGroup.setRegistGroupId(0);
                registGroup.setHid(user.getHid());
                registGroup.setHotelGroupId(user.getHotelGroupId());
                registGroup.setClassId(user.getClassId());
                registGroup.setCreateTime(new Date());
                registGroup.setCreateUserId(user.getUserId());
                registGroup.setCreateUserName(user.getUserName());
                registGroup.setRoomRateCodeId(rateId);
                registGroup.setRoomRateCodeName(rateCode);
                registGroup.setGroupType(9);
                registGroup.setBusinessDay(user.getBusinessDay());
                registGroup.setMemberCard(regist.getMemberCard());
                registGroup.setMemberId(regist.getMemberId());
                registGroup.setCompanyId(regist.getCompanyId());
                registGroup.setCompayName(regist.getCompayName());
                registGroup.setSumRooms(roomList.size());
                if (isGroup == 1) {
                    Object groupType = checkInJson.get("groupType");
                    if (groupType != null) {
                        registGroup.setGroupType(Integer.parseInt(groupType.toString()));
                    }
                    registGroup.setGroupName(checkInJson.getString("groupName"));
                    registGroup.setState(1);
                    String g = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.GROUP, stringRedisTemplate);
                    registGroup.setSn(g);
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.TEAM);
                    roomAuxiliaryRelations.add(roomAuxiliaryRelation);
                } else if (roomList.size() > 1) { // roomList 房间数大于1 则联房
                    registGroup.setGroupType(9);
                    registGroup.setState(1);
                    String g = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.GROUP, stringRedisTemplate);
                    registGroup.setGroupName("联房" + g.substring(g.length() - 4, g.length()));
                    registGroup.setSn(g);
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.JOINT_HOUSING);
                    roomAuxiliaryRelations.add(roomAuxiliaryRelation);

                }
            }

            // 3.配置信息
            BookingOrderConfig bookingOrderConfig = new BookingOrderConfig();
            bookingOrderConfig.setPriceSecrecy(checkInJson.getBoolean("priceSecrecy") ? 1 : 0);
            bookingOrderConfig.setInfoSecrecy(checkInJson.getBoolean("infoSecrecy") ? 1 : 0);
            bookingOrderConfig.setAutoCheckin(checkInJson.getBoolean("autoCheckIn") ? 1 : 0);
            bookingOrderConfig.setNoDeposit(checkInJson.getBoolean("noDposit") ? 1 : 0);
            bookingOrderConfig.setNoPrice(checkInJson.getBoolean("noPrice") ? 1 : 0);
            bookingOrderConfig.setContinueRes(checkInJson.getBoolean("continueRes") ? 1 : 0);
            if (checkInJson.get("autoAr") != null) {
                bookingOrderConfig.setAutoAr(checkInJson.getBoolean("autoAr") ? 1 : 0);
            }

            // 叫早
            int morningCall = 0;
            if (null != checkInJson.get("morningCall") && !"".equals(checkInJson.getString("morningCall"))) {
                morningCall = checkInJson.getBoolean("morningCall") ? 1 : 0;
            }
            bookingOrderConfig.setMorningCall(morningCall);


            // 4.遍历房型
            // 所有入住人信息

            // 需要删除的辅助房态
            ArrayList<RoomAuxiliaryRelation> deleteRelations = new ArrayList<>();

            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setHid(user.getHid());


            int pNum = 0;

            Date date = new Date();

            StringBuilder roomBuilder = new StringBuilder();


            // 需要保存的登记单信息
            ArrayList<CheckInRegist> checkInRegists = new ArrayList<>();

            BookingOrderDailyPrice bodp = new BookingOrderDailyPrice();

            RegistPerson registPerson = new RegistPerson();

            for (int i = 0; i < roomList.size(); i++) {

                JSONObject rlo = roomList.getJSONObject(i);

                CheckInRegist checkInRegist = new CheckInRegist();

                // 1 添加登记单
                String regStr = JSONObject.fromObject(regist).toString();
                Regist roomRegist = (Regist) JSONObject.toBean(JSONObject.fromObject(regStr), Regist.class);
                roomRegist.setRoomNum(rlo.getString("roomNum"));
                roomRegist.setRoomNumId(rlo.getInt("roomInfoId"));

                /**
                 * 处理智能门锁编码的问题
                 */
                String lockNum = rlo.containsKey("lockNum") && rlo.get("lockNum") != null ? rlo.getString("lockNum") : "";
                roomRegist.setSessionToken(lockNum);

                roomBuilder.append(roomRegist.getRoomNum());
                roomBuilder.append(",");


                RoomInfo roomInfo = roomInfoDao.selectById(roomRegist.getRoomNumId());
                if (roomInfo.getRoomNumState() != 1) {
                    roomService.updateRoomListForCache(user);
                    throw new Exception(roomInfo.getRoomNum() + "房间状态不可用");
                }

                roomRegist.setRoomTypeId(rlo.getInt("roomTypeId"));
                roomRegist.setRoomTypeName(rlo.getString("roomTypeName"));
                roomRegist.setIsMainRoom(0);

                String no = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.REGIST, this.stringRedisTemplate);

                roomRegist.setSn(no);

                if (i == 0 && roomList.size() > 1) {
                    roomRegist.setIsMainRoom(1);
                } else if (i == 0 && isTeam) {
                    roomRegist.setIsMainRoom(1);
                }

                checkInRegist.setRegist(roomRegist);

                // 2 添加 配置
                checkInRegist.setBookingOrderConfig(bookingOrderConfig);

                // 3 添加价格
                JSONArray priceList = rlo.getJSONArray("priceList");

                // 房型添加的房价
                ArrayList<BookingOrderDailyPrice> rtPriceList = new ArrayList<>();

                RoomRateCodeSpecific roomRateCodeSpecific = rateMap.get(roomRegist.getRoomTypeId());
                if (roomRateCodeSpecific == null) {
                    roomRateCodeSpecific = new RoomRateCodeSpecific();
                }

                for (int pl = 0; pl < priceList.size(); pl++) {
                    JSONObject plObj = priceList.getJSONObject(pl);
                    bodp = new BookingOrderDailyPrice();
                    bodp.setBookingOrderRoomNumId(0);
                    bodp.setHid(user.getHid());
                    bodp.setHotelGroupId(user.getHotelGroupId());
                    bodp.setPrice(plObj.getInt("price"));
                    bodp.setDailyTime(Integer.parseInt(plObj.getString("date").replace("-", "")));
                    bodp.setRoomTypeId(roomRegist.getRoomTypeId());
                    bodp.setRoomNumId(0);
                    bodp.setBreakNum(roomRateCodeSpecific.getBreakfastNum());
                    bodp.setDailyState(1);
                    bodp.setIsStayover(0);
                    bodp.setCreateTime(date);
                    bodp.setCreateUserId(user.getUserId());
                    bodp.setCreateUserName(user.getUserName());
                    bodp.setUpdateTime(date);
                    bodp.setUpdateUserId(user.getUserId());
                    bodp.setUpdateUserName(user.getUserName());
                    rtPriceList.add(bodp);
                }

                checkInRegist.setBookingOrderDailyPrices(rtPriceList);

                // 4 添加入住人

                JSONArray guestList = rlo.getJSONArray("guestList");
                List<RegistPerson> registPeople = this.addCheckinGuest(guestList, user, roomRegist);


                for (RegistPerson rp : registPeople) {
                    guestNames.append(rp.getPersonName());
                    guestNames.append("  ");
                }

                checkInRegist.setRegistPeople(registPeople);

                pNum += registPeople.size();

                // 添加辅助房态

                checkInRegist.setRoomAuxiliaryRelations(roomAuxiliaryRelations);


                checkInRegists.add(checkInRegist);

                // 要删除的辅助房态 只删除预订中的辅助房态，且预订单号相同
                roomAuxiliaryRelationSearch.setRoomId(roomRegist.getRegistId());
                List<RoomAuxiliaryRelation> roomAuxiliaryRelations1 = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

                for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations1) {

                    if (roomAuxiliaryRelation.getRoomAuxiliaryId() > ROOM_AUXILIARY.BOOK_ARRIVALS) {
                        continue;
                    }

                    if (regist.getBookingOrderId() == null || roomAuxiliaryRelation.getBookingOrderId() == null) {
                        continue;
                    }

                    if (!regist.getBookingOrderId().equals(roomAuxiliaryRelation.getBookingOrderId())) {
                        continue;
                    }

                    deleteRelations.add(roomAuxiliaryRelation);

                }

            }


            if (isTeam) {
                registGroup.setSumPersonNum(pNum);
            }

            JSONArray accountList = checkInJson.getJSONArray("accountList");

            int sumMoney = 0;

            ArrayList<Account> accounts = new ArrayList<>();

            for (int i = 0; i < accountList.size(); i++) {

                JSONObject anc = accountList.getJSONObject(i);

                int money = anc.getInt("money");
                sumMoney += money;

                JSONObject selectedPayTypeInfo = anc.getJSONObject("selectedPayTypeInfo");

                String a = HotelUtils.getHIDUUID32("A", user.getHid());
                Account account = new Account();
                account.setAccountId(a);
                account.setHid(user.getHid());
                account.setCreateUserId(user.getUserId());
                account.setCreateUserName(user.getUserName());
                account.setCreateTime(new Date());
                account.setIsCancel(0);
                account.setAccountYear(user.getBusinessYear());
                account.setAccountYearMonth(user.getBusinessMonth());
                account.setBusinessDay(user.getBusinessDay());
                account.setClassId(user.getClassId());
                account.setPrice(money);
                account.setSettleAccountTime(new Date());

                account.setThirdRefundState(0);
                //类型 ：消费、付款
                int payType = anc.getInt("costType");
                account.setPayType(payType);

                if (anc.get("saleNum") != null) {
                    account.setSaleNum(anc.getInt("saleNum"));
                } else {
                    account.setSaleNum(1);
                }
                account.setUintPrice(money / account.getSaleNum());
                //费用码
                int costClassId = selectedPayTypeInfo.getInt("pId");
                String costClassName = selectedPayTypeInfo.getString("pName");
                String costCodeId = selectedPayTypeInfo.getString("costNum");
                String costCodeName = selectedPayTypeInfo.getString("name");

                account.setPayClassId(costClassId);
                account.setPayClassName(costClassName);
                account.setPayCodeId(costCodeId);
                account.setPayCodeName(costCodeName);

                Object accountType = anc.get("accountType");
                if (accountType != null) {
                    account.setAccountType(Integer.parseInt(accountType.toString()));
                }


                /**
                 * 根据不同的费用码判断进行不同的操作
                 *  微信/支付宝 获取 扫码账单
                 */
                switch (costCodeId) {
                    case "9320": //微信扫码支付

                        account.setThirdRefundState(-1);
                        if (anc.get("thirdRefundState") != null) {
                            account.setThirdRefundState(anc.getInt("thirdRefundState"));
                        }
                        String wxmainId = "";
                        if (anc.get("mainId") != null) {
                            wxmainId = anc.get("mainId").toString();
                        }
                        account.setThirdAccoutId(wxmainId);
                        break;

                    case "9300": //支付宝扫码支付
                        account.setThirdRefundState(-1);
                        if (anc.get("thirdRefundState") != null) {
                            account.setThirdRefundState(anc.getInt("thirdRefundState"));
                        }
                        String alimainId = "";
                        if (anc.get("mainId") != null) {
                            alimainId = anc.get("mainId").toString();
                        }
                        account.setThirdAccoutId(alimainId);
                        break;

                    case "9100": //银行卡预授权

                        AccountThirdPayRecode accountThirdPayRecode = new AccountThirdPayRecode();
                        String at = HotelUtils.getHIDUUID32("AT", user.getHid());
                        accountThirdPayRecode.setHid(user.getHid());
                        accountThirdPayRecode.setAccountId(account.getAccountId());
                        accountThirdPayRecode.setHotelGroupId(user.getHotelGroupId());
                        accountThirdPayRecode.setAccountThirdId(at);
                        //款台号
                        accountThirdPayRecode.setCounterId(HotelUtils.validaStr("counterId"));
                        //操作员号
                        accountThirdPayRecode.setOperatorId(HotelUtils.validaStr("operatorId"));
                        //交易编号
                        accountThirdPayRecode.setTransType(HotelUtils.validaStr("transType"));
                        //金额
                        accountThirdPayRecode.setAmount(account.getPrice());
                        //48域附加信息
                        accountThirdPayRecode.setMemo(HotelUtils.validaStr(anc.get("memo")));
                        //三个校验字符串
                        accountThirdPayRecode.setLrc(HotelUtils.validaStr(anc.get("lrc")));
                        //终端流水号
                        accountThirdPayRecode.setTrace(HotelUtils.validaStr(anc.get("trace")));
                        //银行id
                        accountThirdPayRecode.setBarkId(HotelUtils.validaStr(anc.get("barkId")));
                        //批次号
                        accountThirdPayRecode.setBatch(HotelUtils.validaStr(anc.get("batch")));
                        //交易日期 yyyyMMdd
                        accountThirdPayRecode.setTransDate(HotelUtils.validaStr(anc.get("transDate")));
                        //交易时间 hhmmss
                        accountThirdPayRecode.setTransTime(HotelUtils.validaStr(anc.get("transTime")));
                        //系统参考号
                        accountThirdPayRecode.setRef(HotelUtils.validaStr(anc.get("ref")));
                        //授权号
                        accountThirdPayRecode.setAuth(HotelUtils.validaStr(anc.get("auth")));
                        //商户号
                        accountThirdPayRecode.setMid(HotelUtils.validaStr(anc.get("mId")));
                        //终端号
                        accountThirdPayRecode.setTid(HotelUtils.validaStr(anc.get("tId")));
                        //有效期
                        accountThirdPayRecode.setEffectiveDays(HotelUtils.validaStr(anc.get("effectiveDays")));
                        //预授权
                        accountThirdPayRecode.setPayType(3);
                        //营业日
                        accountThirdPayRecode.setBusinessDay(user.getBusinessDay());
                        //日期
                        accountThirdPayRecode.setCreateTime(date);
                        accountThirdPayRecode.setClassId(user.getClassId());
                        accountThirdPayRecode.setCreateUserId(user.getUserId());
                        accountThirdPayRecode.setCreateUserName(user.getUserName());

                        account.setAccountThirdPayRecode(accountThirdPayRecode);
                        break;

                    case "9600": //会员储值卡

                 /*   int cardId = param.getInt("cardId");
                    CardInfo cardInfo = cardInfoDao.selectById(cardId);

                    if(cardInfo==null){
                        throw new Exception("未查到相关的会员信息");
                    }
                    Integer cardConsuptionRecordId = memberTransactionService.cardConsuptionRecord(user, cardInfo, account, account.getRoomNum() + "：会员支付");
                    account.setThirdAccoutId(cardConsuptionRecordId.toString());
                    account.setMemberId(cardId);*/

                        break;
                }
                //备注
                if (param.get("remark") != null) {
                    String remark = URLDecoder.decode(param.getString("remark"), "utf-8");
                    account.setRemark(remark);
                }

                //理由
                account.setReason(HotelUtils.validaStr(param.get("reason")));
                account.setRefundPrice(0);

                accounts.add(account);
            }


            final JSONArray jsonArray = pmsOrderTransactionService.blendCheckInTransaction(registGroup, checkInRegists, accounts, sumMoney, user, deleteRelations);

            final String s = roomBuilder.toString();

            final String names = guestNames.toString();

            // 更新房间缓存
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    try {
                        roomService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();

                        baseService.turnAlways(user);
                        HashMap<String, String> filedMap = new HashMap<>();
                        filedMap.put("roomList", s);
                        filedMap.put("guestName", names);

                        int pcId = 3;
                        if (checkInJson.get("macCheckIn") != null && checkInJson.getBoolean("macCheckIn")) {
                            pcId = 7;
                        }

                        HashMap<String, String> dataMap = new HashMap<>();
                        dataMap.put("registId", jsonArray.getJSONObject(0).get("registId") + "");

                        baseService.push(user.getHotelGroupId(), user.getHid(), 18, filedMap, dataMap, true, true);


                        HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
                        hotelSettingByParamId.setHid(user.getHid());
                        hotelSettingByParamId.setParamId(HOTEL_SETTING.AUTO_ADD_ROOMPRICE);
                        // 登记后多久产生房费
                        Object minObj = baseService.findHotelSettingByParamId(hotelSettingByParamId);
                        if (minObj == null) {
                            return;
                        }
                        int min = Integer.parseInt(minObj.toString());

                        if (min < 1) {
                            return;
                        }

                        String registIds = "";

                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject robj = jsonArray.getJSONObject(i);
                            registIds += robj.get("registId");
                            registIds += ",";
                        }
                        registIds = registIds.substring(0, registIds.length() - 1);
                        Map<String, Object> data = new HashMap<>();
                        data.put("registIds", registIds);
                        JSONObject addRoomPriceJobParam = JobName.getAddRoomPriceJobParam(min, data);
                        baseService.addCornJob(addRoomPriceJobParam);

                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("",e);
                    }

                }
            });


            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    for (int i = 0; i < checkInRegists.size(); i++) {
                        CheckInRegist checkInRegist = checkInRegists.get(i);
                        //如果是智能门锁则调用
                        SmartLockRequest smartLockRequest = new SmartLockRequest();
                        smartLockRequest.setSessionToken(sessionToken);
                        smartLockRequest.setCheckinTime(checkInRegist.getRegist().getCheckinTime());
                        smartLockRequest.setCheckoutTime(checkInRegist.getRegist().getCheckoutTime());
                        smartLockRequest.setRoomTypeId(checkInRegist.getRegist().getRoomTypeId());
                        smartLockRequest.setRoomTypeName(checkInRegist.getRegist().getRoomTypeName());
                        smartLockRequest.setRoomNumId(checkInRegist.getRegist().getRoomNumId());
                        smartLockRequest.setRoomNum(checkInRegist.getRegist().getRoomNum());
                        smartLockRequest.setHid(checkInRegist.getRegist().getHid());
                        smartLockRequest.setHotelGroupId(checkInRegist.getRegist().getHotelGroupId());
                        smartLockRequest.setBreakfastNum(checkInRegist.getRegist().getBreakfastNum());
                        smartLockRequest.setCheckinType(checkInRegist.getRegist().getCheckinType());
                        smartLockRequest.setLockNo(checkInRegist.getRegist().getSessionToken());
//                        List<RegistPerson> registPeople = checkInRegist.getRegistPeople();
                        List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                        RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                        registPersonSearch.setRegistId(checkInRegist.getRegist().getRegistId());
                        List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
                        for (int j = 0; j < registPeople.size(); j++) {
                            RegistPerson registPersonInfo = registPeople.get(j);
                            SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                            BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                            peoplesList.add(registPersonDetail);
                        }
                        smartLockRequest.setPeoples(peoplesList);
                        baseService.SmartLockCheckin(smartLockRequest);
                    }
                }
            });

            responseData.setData(jsonArray);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }
        return responseData;
    }


    /**
     * 预定转入住
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> bookingCheckIn(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            Date dates = new Date();
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            JSONObject bookParam = JSONObject.fromObject(URLDecoder.decode(param.getString("bookParam"), "utf-8"));

            // 查询所有客源类型
            // 需要保存的登记单信息
            ArrayList<CheckInRegist> checkInRegists = new ArrayList<>();

            // 需要修改的数据
            // 需要添加的房单集合
            HashMap<Integer, Regist> checkInRegistMap = new HashMap<>();

            // 需要修改的预定房型
            ArrayList<BookingOrderRoomNum> bookingOrderRoomNumList = new ArrayList<>();

            // 需要修改的房间信息
            ArrayList<RoomInfo> roomInfoList = new ArrayList<>();

            // 入住人信息 key:roomNumId  value:入住人集合
            HashMap<Integer, List<RegistPerson>> registPersonMap = new HashMap<>();

            // 登记的设置信息
            HashMap<Integer, BookingOrderConfig> bookingOrderConfigMap = new HashMap<>();

            // 要删除的辅助房态信息
            ArrayList<RoomAuxiliaryRelation> deleteRoomAuxiliaryRelationList = new ArrayList<>();

            // 要添加的辅助房态信息
            HashMap<Integer, List<RoomAuxiliaryRelation>> addRoomAuxiliaryRelation = new HashMap<>();

            // 1.获取预定单信息，查看当前预订单状态是否有效
            // 预订单id
            int bookingId = bookParam.getInt("bookingOrderId");

            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingId);

            if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())) {
                throw new Exception("未查到有效的预订单信息");
            }

            // 预订单设置信息
            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setBookingOrderId(bookingId);
            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);

            if (bookingOrderConfigs.size() < 1) {
                throw new Exception("未查到相应的预定设置信息");
            }

            BookingOrderConfig bookingOrderConfig = bookingOrderConfigs.get(0);
            bookingOrderConfig.setId(null);


            StringBuilder guestNames = new StringBuilder();

            // 2.获取预定排房的房间
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingId);
            List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            if (bookingOrderRoomNums.size() < 1) {
                throw new Exception("所选房间信息有误。预定编号:" + bookingOrder.getSn());
            }

            // 将预定房间集合转为map对象
            int checkNum = 0;
            Map<Integer, BookingOrderRoomNum> orderRoomNumMap = new HashMap<>();
            for (BookingOrderRoomNum bookingOrderRoomNum : bookingOrderRoomNums) {
                orderRoomNumMap.put(bookingOrderRoomNum.getId(), bookingOrderRoomNum);
                if (bookingOrderRoomNum.getIsCheckin() == 0) {
                    continue;
                }
                checkNum++;
            }

            // 获取预定房间信息
            BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
            bookingOrderRoomTypeSearch.setBookingOrderId(bookingId);
            List<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);

            Map<Integer, BookingOrderRoomType> bookingOrderRoomTypeMap = bookingOrderRoomTypes.stream().collect(Collectors.toMap(BookingOrderRoomType::getId, a -> a, (k1, k2) -> k1));

            //每日房价查询
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setBookingOrderId(bookingId);
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, List<BookingOrderDailyPrice>> dayPriceMap = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getRoomNumId));

            // 辅助房态查询条件
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setBookingOrderId(bookingId);

            // 3.获取要入住的房间
            JSONArray checkInRoomList = bookParam.getJSONArray("checkInRoomList");
            if (checkInRoomList.size() == 0) {
                new Exception("请传入入住房间");
            }

            // 判断预订单状态 部分入住还是全部入住
            // 如果已入住的房间加 当前要入住的房间数量 不等于 查询出的总房间入住数  则说明为部分入住
            if (checkNum + checkInRoomList.size() != bookingOrderRoomNums.size()) {
                bookingOrder.setOrderStatus(BOOK.STA_BFRZ);
            } else {
                bookingOrder.setOrderStatus(BOOK.STA_QBRZ);
            }

            // 4.查询团队信息
            RegistGroup registGroup = new RegistGroup();
            registGroup.setGroupType(9);
            Date date = new Date();

            // 如果预定房间为1 ，则不需要添加团队信息
            if (bookingOrderRoomNums.size() == 1) {
                registGroup.setTeamType(-1);
            } else {
                RegistGroupSearch registGroupSearch = new RegistGroupSearch();
                registGroupSearch.setBookingOrderId(bookingId);
                List<RegistGroup> registGroups = registGroupDao.selectBySearch(registGroupSearch);
                if (registGroups.size() > 0) {
                    registGroup = registGroups.get(0);

                    RegistSearch registSearch = new RegistSearch();
                    registSearch.setHid(user.getHid());
                    registSearch.setBookingOrderId(bookingId);
                    registSearch.setIsMainRoom(1);
                    List<Regist> regists = registDao.selectBySearch(registSearch);

                    if (regists.size() < 1) {
                        registGroup.setTeamType(-1);
                    } else {
                        registGroup.setTeamType(registGroup.getRegistGroupId());
                    }

                } else {
                    registGroup.setTeamType(-2);
                    String sn = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.GROUP, stringRedisTemplate);
                    registGroup.setSn(sn);
                    registGroup.setPayType(1);
                    registGroup.setGroupType(9);
                    registGroup.setGroupName(bookingOrder.getSn());
                    registGroup.setBookingOrderId(bookingOrder.getBookingOrderId());
                    registGroup.setSumRooms(bookingOrderRoomNums.size());
                    registGroup.setRemark("预订转入住创建");
                    registGroup.setState(1);
                    registGroup.setHid(user.getHid());
                    registGroup.setHotelGroupId(user.getHotelGroupId());
                    registGroup.setBusinessDay(user.getBusinessDay());
                    registGroup.setClassId(user.getClassId());
                    registGroup.setCreateTime(date);
                    registGroup.setCreateUserId(user.getUserId());
                    registGroup.setCreateUserName(user.getUserName());
                    registGroup.setCompanyId(bookingOrder.getCompanyId());
                    registGroup.setCompayName(bookingOrder.getCompanyName());
                    registGroup.setMemberId(bookingOrder.getCardId());
                    registGroup.setMemberCard(bookingOrder.getCardNo());
                }
            }

            Integer rateId = 0;
            String rateCode = "";

            StringBuilder roomBuilder = new StringBuilder();


            Boolean isMachineCheck = false;
            if (bookParam.get("macCheckIn") != null && bookParam.getBoolean("macCheckIn")) {
                isMachineCheck = true;
            }

            // 房价信息
            HashMap<String, RoomRateCodeSpecific> specificHashMap = new HashMap<>();
            //入住天数
            int dayCount = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(bookingOrder.getCheckinTime()).substring(0, 10), HotelUtils.parseDate2Str(bookingOrder.getCheckoutTime()).substring(0, 10)).size();

            for (int i = 0; i < checkInRoomList.size(); i++) {

                CheckInRegist checkInRegist = new CheckInRegist();

                JSONObject roomObj = checkInRoomList.getJSONObject(i);
                int id = roomObj.getInt("bookingRoomId");
                int roomNumId = roomObj.getInt("roomId");
                JSONArray guestList = roomObj.getJSONArray("guestList");

                BookingOrderRoomNum bookingOrderRoomNum = orderRoomNumMap.get(id);

                // 1.将预定房间信息改为 已入住装
                if (bookingOrderRoomNum == null || bookingOrderRoomNum.getIsCheckin() > 0 || bookingOrderRoomNum.getRoomNumId() < 1) {
                    throw new Exception("未查询到有效的分房信息，房间号:" + bookingOrderRoomNum.getRoomNum() + ",编号:" + bookingOrderRoomNum.getId());
                }

                bookingOrderRoomNum.setIsCheckin(1);
                bookingOrderRoomNumList.add(bookingOrderRoomNum);

                //  预定房型信息
                BookingOrderRoomType bookingOrderRoomType = bookingOrderRoomTypeMap.get(bookingOrderRoomNum.getBookingOrderRoomTypeId());

                // 2.将房间改为住净
                RoomInfo roomInfo = roomInfoDao.selectById(roomNumId);
                if (roomInfo == null || !roomInfo.getHid().equals(user.getHid())) {
                    throw new Exception("未查到相应的房间信息，房间号:" + roomInfo.getRoomNum() + ",编号:" + roomInfo.getRoomInfoId());
                }

                if (roomInfo.getRoomNumState() != 1) {
                    roomService.updateRoomListForCache(user);
                    throw new Exception(roomInfo.getRoomNum() + "房间状态不可用");
                }

                roomInfoList.add(roomInfo);

                // 3.添加设置信息
                bookingOrderConfigMap.put(roomNumId, bookingOrderConfig);

                // 4.添加登记信息
                Regist regist = new Regist();
                regist.setHid(user.getHid());
                regist.setHotelGroupId(user.getHotelGroupId());
                regist.setRoomNumId(roomNumId);
                regist.setRoomNum(roomInfo.getRoomNum());
                regist.setRoomTypeId(roomInfo.getRoomTypeId());
                regist.setRoomTypeName(roomInfo.getRoomTypeName());
                regist.setBookingOrderId(bookingId);
                regist.setMacRegist(1);
                regist.setCheckinMode(1);
                roomBuilder.append(regist.getRoomNum());
                roomBuilder.append(",");

                //生成流水号
                String sn = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.REGIST, stringRedisTemplate);
                regist.setSn(sn);

                //协议单位
                regist.setCompanyId(bookingOrder.getCompanyId());
                regist.setCompayName(bookingOrder.getCompanyName());
                regist.setCompanyAccountId(bookingOrder.getCompanyAccountId());

                //会员
                regist.setMemberCard(bookingOrder.getCardNo());
                regist.setMemberId(bookingOrder.getCardId());
                Boolean isVip = false;
                if (bookingOrder.getCardId() != null && bookingOrder.getCardId() > 0) {
                    isVip = true;
                }

                //房价码
                rateId = roomObj.getInt("rateId");
                rateCode = roomObj.containsKey("rateCode") ? roomObj.getString("rateCode") : "";
                regist.setRoomRateCodeId(rateId);
                regist.setRoomRateCodeName(rateCode);

                //入住时间
                regist.setCheckinTime(new Date());
                regist.setCheckoutTime(bookingOrder.getCheckoutTime());

                regist.setCheckoutBusinessDay(HotelUtils.parseDate2Int(bookingOrder.getCheckoutTime()));

                //备注
                regist.setRemark("预订转入住");

                //入住类型

                regist.setCheckinType(1);
                //钟点房
                if (bookingOrder.getOrderType() == 2) {
                    regist.setCheckinType(2);
                }

                //客源
                regist.setResourceId(bookingOrder.getResourceId());
                regist.setResourceName(SYS_RESOURCE.SYS_RESOURCE_MAP.get(bookingOrder.getResourceId()));

                //入住人数
                regist.setPersonCount(guestList.size());

                //是否客人自动结账
                regist.setAutoCheckout(0);


                //杂项
                regist.setBusinessDay(user.getBusinessDay());
                regist.setRegistYear(user.getBusinessYear());
                regist.setRegistYearMonth(user.getBusinessMonth());
                regist.setClassId(user.getClassId());
                regist.setCreateTime(new Date());
                regist.setCreateUserId(user.getUserId());
                regist.setUpdateTime(new Date());
                regist.setUpdateUserId(user.getUserId());
                regist.setIsCreateNightCost(0);
                regist.setRegistGroupId(0);
                regist.setTeamCodeId(0);
                regist.setIsMainRoom(1);
                //regist.setSessionToken(user.getSessionId());
                regist.setState(0);

                /**
                 * 处理智能门锁编码的问题
                 */
//                String lockNum = roomObj.containsKey("lockNum") && roomObj.get("lockNum")!=null ? roomObj.getString("lockNum") :"";
//                regist.setSessionToken(lockNum);
                regist.setSessionToken(roomInfo.getLockNum());

                // 查询房价信息
                if (bookingOrder.getOrderType() == 1) {
                    String sket = regist.getRoomTypeId() + "" + regist.getRoomRateCodeId();

                    RoomRateCodeSpecific roomRateCodeSpecific = specificHashMap.get(sket);
                    if (roomRateCodeSpecific == null) {

                        RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
                        roomRateCodeSpecificSearch.setHid(user.getHid());
                        roomRateCodeSpecificSearch.setRateId(regist.getRoomRateCodeId());
                        roomRateCodeSpecificSearch.setRoomTypeId(regist.getRoomTypeId());

                        List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);

                        if (roomRateCodeSpecifics.size() < 1) {
                            throw new Exception(regist.getRoomTypeName() + " 房价码： " + regist.getRoomRateCodeName() + "未保存");
                        }

                        roomRateCodeSpecific = roomRateCodeSpecifics.get(0);

                        specificHashMap.put(sket, roomRateCodeSpecific);

                    }

                    regist.setBreakfastNum(roomRateCodeSpecific.getBreakfastNum());
                } else {
                    regist.setBreakfastNum(0);
                }
                checkInRegistMap.put(roomNumId, regist);


                // 5.入住人的添加
                List<RegistPerson> registPeople = this.addCheckinGuest(guestList, user, regist, 0);
                registPersonMap.put(roomNumId, registPeople);

                for (RegistPerson rp : registPeople) {
                    guestNames.append(rp.getPersonName());
                    guestNames.append("  ");
                }

                // 6.需要删除的辅助房态
                roomAuxiliaryRelationSearch.setRoomId(roomNumId);
                List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);
                deleteRoomAuxiliaryRelationList.addAll(roomAuxiliaryRelations);

                // 7.需要添加的辅助房态
                ArrayList<RoomAuxiliaryRelation> rarl = new ArrayList<>();


                //信息保密
                if (bookingOrderConfig.getInfoSecrecy() == 1) {
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);

                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.SECRECY);
                    roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.SECRECY);

                    rarl.add(roomAuxiliaryRelation);
                }

                if (true) {
                    regist.setCheckinMode(1);
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);

                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.MACHINE);
                    roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.MACHINE);

                    rarl.add(roomAuxiliaryRelation);
                }

                //添加钟点房标识
                if (bookingOrder.getOrderType() == 2) {
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.HOUR_ROOM);
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);
                    rarl.add(roomAuxiliaryRelation);
                }


                //团队
                if (bookingOrderRoomNums.size() > 1) {
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);

                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.TEAM);
                    roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.TEAM);
                    if (registGroup.getGroupType() == 9) {
                        roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.JOINT_HOUSING);
                        roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.JOINT_HOUSING);
                    }

                    rarl.add(roomAuxiliaryRelation);
                }

                //2021-08-17 新增代码
                checkInRegist.setRegist(regist);
                checkInRegist.setBookingOrderConfig(bookingOrderConfig);
                checkInRegist.setBookingOrderDailyPrices(bookingOrderDailyPrices);
                checkInRegist.setRoomAuxiliaryRelations(roomAuxiliaryRelations);
                checkInRegist.setRegistPeople(registPeople);
                checkInRegists.add(checkInRegist);

                addRoomAuxiliaryRelation.put(roomNumId, rarl);

            }

            // 5.查询预定账务信息
            int sumPay = 0;
            int sumSale = 0;
            List<Account> accounts = new ArrayList<>();
            if (registGroup.getRegistGroupId() == null || registGroup.getRegistGroupId() < 1) {
                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setBookingId(bookingId);
                accounts = accountDao.selectBySearch(accountSearch);

                for (Account account : accounts) {

                    if (account.getPayType() == 1) {
                        sumSale += account.getPrice();
                    } else {
                        sumPay += account.getPrice();
                    }

                }

                registGroup.setSumPay(sumPay);
                registGroup.setSumSales(sumSale);
            }

            registGroup.setRoomRateCodeId(rateId);
            registGroup.setRoomRateCodeName(rateCode);
            // 6.调用预定入住事务接口
            final Map<Integer, Integer> registMap = pmsOrderTransactionService.bookingCheckInTransactionNew(bookingOrder, bookingOrderRoomNumList, checkInRegistMap, roomInfoList, registPersonMap,
                    bookingOrderConfigMap, deleteRoomAuxiliaryRelationList, addRoomAuxiliaryRelation, registGroup, accounts, user, dayPriceMap);

            final String s = roomBuilder.toString();

            resultMap.put("data", registMap);

            final String names = guestNames.toString();

           /*
            Integer k = 0;
            for (Integer i :integers){
                k = i;
                break;
            }
            final Regist regist = checkInRegistMap.get(k);*/

            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    try {
                        //更新房间缓存
                        List<RoomInfo> roomInfos = roomService.updateRoomListForCache(user);
                        roomService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        baseService.turnAlways(user);

                        HashMap<String, String> filedMap = new HashMap<>();
                        filedMap.put("roomList", s);
                        filedMap.put("guestName", names);

                        HashMap<String, String> regMap = new HashMap<>();
                        //regMap.put("registId",""+regist.getRegistId());

                        Set<Integer> integers1 = registMap.keySet();
                        int keys = 0;
                        for (Integer key : integers1) {
                            if (keys == 0) {
                                keys = key;
                                break;
                            }
                        }

                        HashMap<String, String> dataMap = new HashMap<>();
                        dataMap.put("registId", registMap.get("keys") + "");
                        int pcId = 3;
                        if (bookParam.get("macCheckIn") != null && bookParam.getBoolean("macCheckIn")) {
                            pcId = 18;
                        }
                        baseService.push(user.getHotelGroupId(), user.getHid(), 18, filedMap, dataMap, true, true);

                        HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
                        hotelSettingByParamId.setHid(user.getHid());
                        hotelSettingByParamId.setParamId(HOTEL_SETTING.AUTO_ADD_ROOMPRICE);
                        // 登记后多久产生房费
                        Object minObj = baseService.findHotelSettingByParamId(hotelSettingByParamId);
                        if (minObj == null) {
                            return;
                        }
                        int min = Integer.parseInt(minObj.toString());

                        if (min < 1) {
                            return;
                        }
                        Set<Integer> integers = registMap.keySet();

                        String registIds = "";

                        for (Integer key : integers) {
                            registIds += registMap.get(key);
                            registIds += ",";
                        }

                        registIds = registIds.substring(0, registIds.length() - 1);
                        Map<String, Object> data = new HashMap<>();
                        data.put("registIds", registIds);
                        JSONObject addRoomPriceJobParam = JobName.getAddRoomPriceJobParam(min, data);
                        baseService.addCornJob(addRoomPriceJobParam);



                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("",e);
                    }

                }
            });

            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    log.info("开始推送门锁信息");
                    for (int i = 0; i < checkInRegists.size(); i++) {
                        CheckInRegist checkInRegist = checkInRegists.get(i);
                        //如果是智能门锁则调用
                        SmartLockRequest smartLockRequest = new SmartLockRequest();
                        smartLockRequest.setSessionToken(sessionToken);
                        smartLockRequest.setCheckinTime(checkInRegist.getRegist().getCheckinTime());
                        smartLockRequest.setCheckoutTime(checkInRegist.getRegist().getCheckoutTime());
                        smartLockRequest.setRoomTypeId(checkInRegist.getRegist().getRoomTypeId());
                        smartLockRequest.setRoomTypeName(checkInRegist.getRegist().getRoomTypeName());
                        smartLockRequest.setRoomNumId(checkInRegist.getRegist().getRoomNumId());
                        smartLockRequest.setRoomNum(checkInRegist.getRegist().getRoomNum());
                        smartLockRequest.setHid(checkInRegist.getRegist().getHid());
                        smartLockRequest.setHotelGroupId(checkInRegist.getRegist().getHotelGroupId());
                        smartLockRequest.setBreakfastNum(checkInRegist.getRegist().getBreakfastNum());
                        smartLockRequest.setCheckinType(checkInRegist.getRegist().getCheckinType());
                        smartLockRequest.setLockNo(checkInRegist.getRegist().getSessionToken());
                        List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                        RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                        registPersonSearch.setRegistId(checkInRegist.getRegist().getRegistId());
                        List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
                        for (int j = 0; j < registPeople.size(); j++) {
                            RegistPerson registPersonInfo = registPeople.get(j);
                            SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                            BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                            peoplesList.add(registPersonDetail);
                        }
                        smartLockRequest.setPeoples(peoplesList);
                        baseService.SmartLockCheckin(smartLockRequest);
                    }
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    /**
     * 结账相关
     *
     * @param checkoutParam
     * @return
     */
    @Override
    public ResponseData checkOut(CheckoutParam checkoutParam) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = checkoutParam.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);


            //操作日志
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);

            StringBuilder roomBuilder = new StringBuilder();

            //获取结账类型
            //1.单房间结账
            //2.团队结账
            //3,入住人结账
            int type = checkoutParam.getType();
            final HashMap<Integer, Integer> registForRoomPrice = new HashMap<>();
            //登记单信息
            List<Regist> registList = new ArrayList<>();
            RegistGroup registGroup = new RegistGroup();
            if (type == 2) {

                int teamCodeId = checkoutParam.getTeamCodeId();

                registGroup = registGroupDao.selectById(teamCodeId);

                if (registGroup == null || !registGroup.getHid().equals(user.getHid())) {
                    throw new Exception("未查到当前团第信息");
                }

                if (registGroup.getState() != 1) {
                    throw new Exception("当前团队房状态不允许结账。编号:" + registGroup.getRegistGroupId());
                }

                RegistSearch registSearch = new RegistSearch();
                registSearch.setTeamCodeId(teamCodeId);

                registList = registDao.selectBySearch(registSearch);

                if (registList.size() < 1) {
                    throw new Exception("未查到团队下的登记信息");
                }

                roomBuilder.append("团队：");
                roomBuilder.append(registGroup.getGroupName());

            } else {

                int registId = checkoutParam.getRegistId();
                Regist regist = registDao.selectById(registId);
                if (regist == null || !regist.getHid().equals(user.getHid())) {
                    throw new Exception("未查到当前房间信息。编号:" + regist.getRegistId());
                }
                if (regist.getState() == 1 || regist.getState() == 4) {
                    throw new Exception("当前房间状态为 " + HotelUtils.stateMap.get(regist.getState()) + " ，不允许结账。编号:" + regist.getRegistId());
                }
                registList.add(regist);
                roomBuilder.append(regist.getRoomNum());
            }

            //  遍历登记单集合
            // 1.1获取登记单里的团队信息
            // 1.2获取登记单里的预定信息
            // 1.3获取登记单里的账务信息
            // 1.4获取登记单里的房间信息
            // 1.5获取登记单里的辅助房态
            // 1.6获取登记单里的佣金信息
            // 1.7获取登记单里的入住人信息

            //团队信息
            ArrayList<RegistGroup> registGroupList = new ArrayList<>();

            //预订单信息
            HashMap<Integer, BookingOrder> bookingOrderMap = new HashMap<>();
            HashMap<Integer, BookingOrderRoomNum> bookingOrderRoomNumMap = new HashMap<>();
            ArrayList<BookingOrder> bookingOrderList = new ArrayList<>();
            ArrayList<BookingOrderRoomNum> bookingOrderRoomNumList = new ArrayList<>();


            //账务信息
            ArrayList<Account> accountList = new ArrayList<>();
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setIsCancel(0);

            //房间信息
            HashMap<Integer, RoomInfo> roomInfoMap = new HashMap<>();

            //待结账的登记信息
            ArrayList<Regist> checkoutRegistList = new ArrayList<>();

            //辅助房态信息
            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelationList = new ArrayList<>();
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();

            // 如果当前房间是会员入住，则记录regist
            final ArrayList<Regist> cardCheckinRecords = new ArrayList<>();


            //提成信息
            //   ArrayList<SalesHotelCommissionDetails> salesHotelCommissionDetailsList = new ArrayList<>();

            // 待结账的入住人信息
            ArrayList<RegistPerson> checkOutPeople = new ArrayList<>();


            int sumPay = 0;
            int sumSale = 0;

            //总房费
            int sumRoomSale = 0;

            for (Regist regist : registList) {

                // 1已结 0未结 2挂账  4 预结 3撤销
                Integer state = regist.getState();
                if (state == 1 || state == 3) {
                    continue;
                }
                Date date = new Date();

                regist.setMacCheckout(1);

                //查询账务信息
                accountSearch.setRegistId(regist.getRegistId());
                List<Account> accounts = accountDao.selectBySearch(accountSearch);

                int pay = 0;
                int sale = 0;
                int roomSale = 0;

                for (Account account : accounts) {

                    Integer payType = account.getPayType();

                    if (payType == 1) {
                        sale += account.getPrice();

                        if (account.getAccountType() != null) {
                            if (account.getAccountType() == 1) {
                                roomSale += account.getPrice();
                            }
                        }


                    } else {
                        pay += account.getPrice();
                    }

                    //如果是已结，则不记录到修改列表
                    if (account.getRegistState() == 1) {
                        continue;
                    }

                    //如果是预授权，未完成不允许结账
                    if (account.getPayCodeId().equals("9100") && !account.getThirdRefundState().equals(2)) {
                        throw new Exception("预授权未完成，账单号:" + account.getAccountId());
                    }

                    account.setRegistState(1);
                    account.setUpdateUserName(user.getUserName());
                    account.setUpdateTime(date);
                    account.setUpdateUserId(user.getUserId());

                    accountList.add(account);

                }

                //记录总消费和总付款
                sumPay += pay;
                sumSale += sale;
                //如果当前房单信息是未结状态，则跳出循环
                if (regist.getState() == 1) {
                    continue;
                }
                registForRoomPrice.put(regist.getRegistId(), roomSale);
                //将入住单登记状态改为已经
                oprecord.setOccurTime(HotelUtils.currentTime());
                oprecord.setRoomNum(regist.getRoomNum());
                oprecord.setMainId(regist.getSn());
                oprecord.setBookingOrderId(regist.getBookingOrderId());
                oprecord.setRegistId(regist.getRegistId());
                oprecord.setSourceValue(regist.getState() + "");
                oprecord.setChangedValue("1");

                String descr = "将 " + regist.getRoomNum() + " 的状态从 " + HotelUtils.stateMap.get(regist.getState()) + " 改为 已结。";
                oprecord.setDescription(descr);
                oprecords.add(oprecord);

                //修改登记状态
                regist.setState(1);
                regist.setCheckoutBusinessDay(user.getBusinessDay());
                regist.setCheckoutClassId(user.getClassId());
                regist.setSettleAccountTime(date);
                regist.setCheckoutOperator(user.getUserId());
                regist.setUpdateUserId(user.getUserId());
                regist.setUpdateTime(date);

                //更新登记表中总消费和总付款
                regist.setSumPay(pay);
                regist.setSumSale(sale);

                checkoutRegistList.add(regist);

                Integer memberId = regist.getMemberId();

                if (memberId != null) {
                    cardCheckinRecords.add(regist);
                }


                //查询房间信息
                RoomInfo roomInfo = roomInfoDao.selectById(regist.getRoomNumId());
                roomInfoMap.put(regist.getRegistId(), roomInfo);

                //查询预订单信息
                if (regist.getBookingOrderId() != null && regist.getBookingOrderId() > 0) {

                    //如果预订单没有预定信息，则查询预定信息
                    if (bookingOrderMap.get(regist.getBookingOrderId()) == null) {

                        BookingOrder bookingOrder = bookingOrderDao.selectById(regist.getBookingOrderId());

                        bookingOrderMap.put(regist.getBookingOrderId(), bookingOrder);


                    }

                    BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                    bookingOrderRoomNumSearch.setRegistId(regist.getRegistId());
                    List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
                    if (bookingOrderRoomNums.size() > 0) {
                        BookingOrderRoomNum bookingOrderRoomNum = bookingOrderRoomNums.get(0);
                        bookingOrderRoomNum.setIsCheckout(1);
                        bookingOrderRoomNumMap.put(bookingOrderRoomNum.getId(), bookingOrderRoomNum);
                        bookingOrderRoomNumList.add(bookingOrderRoomNum);
                    }

                }

                //查询当前房间的辅助房态
                roomAuxiliaryRelationSearch.setRegistId(regist.getRegistId());
                List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

                String kes = "";
                for (RoomAuxiliaryRelation rarl : roomAuxiliaryRelations) {

                    kes += rarl.getRoomAuxiliaryId() + ",";

                }
                if (kes.length() > 1) {
                    kes = kes.substring(0, kes.length() - 1);
                }
                regist.setRoomAuxiliaryRecord(kes);

                roomAuxiliaryRelationList.addAll(roomAuxiliaryRelations);

                // 入住人
                RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                registPersonSearch.setRegistId(regist.getRegistId());
                List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);


                checkOutPeople.addAll(registPeople);
            }

            //取出要结账的房单集合
            if (checkoutRegistList.size() < 1) {
                throw new Exception("未查到要结账的房间信息");
            }

            //计算差额
            int diff = sumPay - sumSale;

            //当结账状态 不为 3.团队里单房间离店时，则验证消费和付款信息
            if (type != 3 && diff != 0) {
                JSONObject resultMap = new JSONObject();
                resultMap.put("errorType", 1);
                resultMap.put("diff", diff);
                responseData.setData(resultMap);
                throw new Exception("消费和付款不持平，请补足差价。");
            }

            //如果是团队则记录团队修改记录
            if (type > 1) {

                registGroup.setSumRooms(registList.size());
                registGroup.setSumSales(sumSale);
                registGroup.setSumPay(sumPay);
                registGroup.setState(type);

                registGroupList.add(registGroup);
            }

            /*2.查询预定信息，判断当前预定的状态*/
            Set<Integer> bookingKeys = bookingOrderMap.keySet();
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            for (Integer bk : bookingKeys) {

                //默认标示为全部已结
                Boolean isFinish = true;
                BookingOrder bookingOrder = bookingOrderMap.get(bk);
                bookingOrderRoomNumSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
                List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

                //遍历预定房间集合，判断是否有未结账的房价
                for (BookingOrderRoomNum born : bookingOrderRoomNums) {

                    if (bookingOrderRoomNumMap.get(born.getId()) != null) {
                        continue;
                    }

                    //如果有未结账单
                    //则不对预订单做操作
                    if (born.getIsCheckout() == 0) {
                        isFinish = false;
                        break;
                    }

                }

                //代表预订单全部完成
                if (isFinish) {
                    bookingOrder.setOrderStatus(BOOK.STA_RZWC);
                    bookingOrder.setUpdateTime(new Date());
                    bookingOrder.setUpdateUserId(user.getUserId());
                    bookingOrder.setUpdateUserName(user.getUserName());
                    bookingOrderList.add(bookingOrder);

                    //将入住单登记状态改为已经
                    oprecord.setOccurTime(HotelUtils.currentTime());
                    oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());

                    String descr = "将订单 " + bookingOrder.getSn() + " 的状态改为 入住完成。";
                    oprecord.setDescription(descr);
                    oprecords.add(oprecord);

                }

            }


            // 查询订单的在住人
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();

            PmsOrderServiceImpl pmsOrderService = this;


            //调用结账的业务方法
            pmsOrderTransactionService.checkOut(registList, registGroupList, bookingOrderList, new ArrayList<BookingOrderRoomType>(), bookingOrderRoomNumList, roomAuxiliaryRelationList,
                    user, accountList, roomInfoMap, new ArrayList<>(), checkOutPeople);


            final String s = roomBuilder.toString();
            final List<Regist> resList = registList;
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    try {


                        HashMap<String, String> fieledMap = new HashMap<>();

                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();

                        baseService.turnAlways(user);

                        fieledMap.put("roomList", s);

                        baseService.push(user.getHotelGroupId(), user.getHid(), 21, fieledMap, new HashMap<String, String>(), true, true);

                        roomService.updateRoomListForCache(user);

                        pmsOrderService.updateVipCheckInNum(cardCheckinRecords, user);

                        CardPointRequest cardPointRequest = new CardPointRequest();

                        for (Regist regist : resList) {
                            Integer memberId = regist.getMemberId();
                            if (memberId == null || memberId < 1) {
                                continue;
                            }

                            Integer integer = registForRoomPrice.get(regist.getRegistId());
                            if (integer == null || integer == 0) {
                                continue;
                            }
                            cardPointRequest = new CardPointRequest();
                            cardPointRequest.setSessionToken(user.getSessionId());
                            cardPointRequest.setCardId(regist.getMemberId());
                            cardPointRequest.setType(2);
                            cardPointRequest.setRegistId(regist.getRegistId());
                            cardPointRequest.setRoomInfoId(regist.getRoomNumId());
                            cardPointRequest.setRoomNo(regist.getRoomNum());
                            cardPointRequest.setPoint(integer);

                            memberService.memberTranslatePoint(cardPointRequest);
                        }

                    } catch (Exception e) {

                        log.error("-------------------结账线程处理报错-begin----------------");
                        e.printStackTrace();
                        log.error("-------------------结账线程处理报错-end----------------");
                    }

                }
            });

            this.addOprecords(oprecords);


        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }
        return responseData;
    }

    public List<RegistPerson> addCheckinGuest(JSONArray guestList, TbUserSession user, Regist regist) throws Exception {
        /**
         * 3.添加入住信息
         */
        ArrayList<RegistPerson> registPeople = new ArrayList<>();
        JSONObject imageData = new JSONObject();
        imageData.put(ER.SESSION_TOKEN, user.getSessionId());
        for (int i = 0; i < guestList.size(); i++) {

            JSONObject guest = guestList.getJSONObject(i);
            String code = guest.getString("idCode");

            RegistPerson person = new RegistPerson();
            person.setRegistState(0);
            person.setIdCode(guest.getString("idCode"));
            if (guest.get("birthday") != null && !"".equals(guest.getString("birthday")) && !"null".equals(guest.getString("birthday"))) {
                int birthday = Integer.parseInt(guest.getString("birthday").replace("-", ""));
                person.setBirthday(birthday);
                person.setBirthYear(Integer.parseInt(String.valueOf(birthday).substring(0, 4)));
            } else {
                String birthday = code.length() >= 15 ? code.substring(6, 14) : "";
                String year = code.length() >= 15 ? code.substring(6, 10) : "";
                if (!birthday.equals("")) {
                    person.setBirthday(Integer.parseInt(birthday));
                }
                if (!year.equals("")) {
                    person.setBirthYear(Integer.parseInt(year));
                }
            }
            person.setRegistId(regist.getRegistId());
            person.setIdType(1);
            person.setSex(0);

            if (guest.get("sex") != null) {
                person.setSex(guest.getInt("sex"));
            }
            person.setPersonName(guest.getString("personName"));
            person.setIsOther(i == 0 ? 0 : 1);
            person.setHid(user.getHid());
            person.setHotelGroupId(user.getHotelGroupId());
            person.setRoomNum(regist.getRoomNum());
            person.setRoomNumId(regist.getRoomNumId());
            person.setTeamCodeId(regist.getTeamCodeId());

            if (guest.get("address") != null) {
                person.setAddress(guest.getString("address"));
            }
            if (guest.get("Address") != null) {
                person.setAddress(guest.getString("Address"));
            }
            if (guest.get("phone") != null) {
                person.setPhone(guest.getString("phone"));
            }

            if (guest.get("nation") != null) {
                String nation = guest.getString("nation");
                if (nation.indexOf("族") < 1) {
                    nation += "族";
                }
                person.setNation(HotelUtils.nationMap.getInt(nation));
            }

            /**
             * 身份证照  新版本
             */
            if (guest.get("image") != null && !"".equals(guest.getString("image"))) {
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("image").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(code, "UTF-8") + ".jpeg");
                person.setIdImage(uploadObjectRsp.getFileName());
            }

            /**
             * 证件照,新版本
             */
            if (guest.get("cameraPicture") != null && !"".equals(guest.getString("cameraPicture"))) {
                String s = code + regist.getHid() + regist.getRoomNumId() + HotelUtils.currentTime();
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("cameraPicture").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                person.setCameraImage(uploadObjectRsp.getFileName());
            }

            /**
             * 证件照,老版本 Image
             */
            if (guest.get("photo") != null && !"".equals(guest.getString("photo"))) {
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("photo").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(code, "UTF-8") + ".jpeg");
                person.setIdImage(uploadObjectRsp.getFileName());
            }


            /**
             * 身份证照  老版本
             */
            if (guest.get("cameraPhoto") != null && !"".equals(guest.getString("cameraPhoto"))) {
                String s = code + regist.getHid() + regist.getRoomNumId() + HotelUtils.currentTime();
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("cameraPhoto").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                person.setCameraImage(uploadObjectRsp.getFileName());
            }


            /**
             * 证件照,老版本 Image
             */
            if (guest.get("Photo") != null && !"".equals(guest.getString("Photo"))) {
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("Photo").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(code, "UTF-8") + ".jpeg");
                person.setIdImage(uploadObjectRsp.getFileName());
            }


            /**
             * 身份证照  老版本
             */
            if (guest.get("CameraPhoto") != null && !"".equals(guest.getString("CameraPhoto"))) {
                String s = code + regist.getHid() + regist.getRoomNumId() + HotelUtils.currentTime();
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("CameraPhoto").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                person.setCameraImage(uploadObjectRsp.getFileName());
                log.info("person.getCameraImage:" + person.getCameraImage());
            }

            /**
             * 相似度   0~1 之间  Similarity，相似度
             */
            if (guest.get("semblance") != null && !"".equals(guest.getString("semblance"))) {
                person.setConfidence(guest.getString("semblance"));
            }

            /**
             * 相似度   0~1 之间  Similarity，相似度
             */
            if (guest.get("semblance") != null && !"".equals(guest.getString("semblance"))) {
                person.setConfidence(guest.getString("semblance"));
            }

            /**
             * 成功或失败  0-失败 1-成功
             */
            if (guest.get("faceResult") != null && !"".equals(guest.getString("faceResult"))) {
                try {
                    person.setFaceResult(guest.getInt("faceResult"));
                } catch (Exception ex) {
                    log.error("",ex);
                    person.setFaceResult(0);
                }
            }

            /**
             * 公安网上传状态
             */
            if (guest.get("guestStatus") != null && !"".equals(guest.getString("guestStatus"))) {
                try {
                    person.setGuestType(guest.getInt("guestStatus"));
                } catch (Exception ex) {
                    log.error("",ex);
                    person.setFaceResult(0);
                }
            }

            /**
             * 公安网流水号
             */
            if (guest.get("guestNo") != null && !"".equals(guest.getString("guestNo"))) {
                person.setGuestNo(guest.getString("guestNo"));
            }

            /**
             * 公安网唯一标识
             */
            if (guest.get("guestId") != null && !"".equals(guest.getString("guestId"))) {
                person.setGuestId(guest.getString("guestId"));
            }

            registPeople.add(person);

            /**
             * 这里创建子线程处理 客史的问题
             */
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        createPersonInfo(registPeople);
                    } catch (Exception e) {
                        log.error("",e);
                        e.printStackTrace();
                    }
                }
            });
        }
        return registPeople;
    }

    /**
     * 创建客历档案
     *
     * @param registPersonList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseData createPersonInfo(List<RegistPerson> registPersonList) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<PersonInfo> personInfoList = new ArrayList<>();
            for (int i = 0; i < registPersonList.size(); i++) {
                RegistPerson registPerson = registPersonList.get(i);
                if (null == registPerson.getPersonName() || registPerson.getPersonName().equals("")) {
                    continue;
                }
                if (null == registPerson.getIdCode() || registPerson.getIdCode().equals("")) {
                    continue;
                }
                //以证件号为唯一标准去查询客史档案
                String idCode = registPerson.getIdCode();
//                IdcardValidator iv = new IdcardValidator();
//                if (!iv.isValidatedAllIdcard(idCode)) {
//                    continue;
//                }
                PersonInfoSearch personInfoSearch = new PersonInfoSearch();
                personInfoSearch.setHid(registPerson.getHid());
                personInfoSearch.setIdCode(idCode);
                Page<PersonInfo> personInfos = personInfoDao.selectBySearch(personInfoSearch);
                if (null == personInfos || personInfos.size() < 1) {
                    PersonInfo personInfo = new PersonInfo();
                    personInfo.setHid(registPerson.getHid());
                    personInfo.setHotelGroupId(registPerson.getHotelGroupId());
                    personInfo.setPersonName(registPerson.getPersonName());
                    personInfo.setIdCode(registPerson.getIdCode());
                    personInfo.setSex(registPerson.getSex());
                    personInfo.setBirthday(registPerson.getBirthday());
                    personInfo.setNation(registPerson.getNation());
                    personInfo.setIdImage(registPerson.getIdImage());
                    personInfo.setAddress(registPerson.getAddress());
                    personInfo.setCameraImage(registPerson.getCameraImage());
                    personInfo.setPhone(registPerson.getPhone());
                    personInfoList.add(personInfo);
                }
            }
            for (int i = 0; i < personInfoList.size(); i++) {
                Integer insert = personInfoDao.insert(personInfoList.get(i));
                if (insert < 1) {
                    throw new Exception("创建客历档案失败");
                }
            }
        } catch (Exception e) {
            log.error("",e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 填充 RegistPerson 对象
     *
     * @param guestList
     * @param user
     * @param regist
     * @return
     * @throws Exception
     */
    public ArrayList<RegistPerson> addCheckinGuest(JSONArray guestList, TbUserSession user, Regist regist, Integer bookingOrderRoomNumId) throws Exception {
        /**
         * 3.添加入住信息
         */
        ArrayList<RegistPerson> registPeople = new ArrayList<>();
        JSONObject imageData = new JSONObject();
        imageData.put(ER.SESSION_TOKEN, user.getSessionId());
        for (int i = 0; i < guestList.size(); i++) {
            JSONObject guest = guestList.getJSONObject(i);

            if (guest.get("personName") == null || guest.getString("personName").equals("") || guest.getString("personName").equals("null")) {
                continue;
            }
            String code = guest.getString("Code");
            RegistPerson person = new RegistPerson();
            AddressParam addressParam = new AddressParam(code);
            person.setCity(addressParam.getCity());
            person.setArea(addressParam.getArea());
            person.setProvice(addressParam.getProvice());
            person.setRegistState(0);
            person.setIdCode(guest.getString("Code"));
            if (guest.get("birthday") != null && !"".equals(guest.getString("birthday")) && !"null".equals(guest.getString("birthday"))) {
                int birthday = Integer.parseInt(guest.getString("birthday").replace("-", ""));
                person.setBirthday(birthday);
                person.setBirthYear(Integer.parseInt(String.valueOf(birthday).substring(0, 4)));
            } else {
                String birthday = code.length() >= 15 ? code.substring(6, 14) : "";
                String year = code.length() >= 15 ? code.substring(6, 10) : "";
                if (!birthday.equals("")) {
                    person.setBirthday(Integer.parseInt(birthday));
                }
                if (!year.equals("")) {
                    person.setBirthYear(Integer.parseInt(year));
                }
            }
            person.setRegistId(regist.getRegistId());
            person.setIdType(1);
            person.setSex(0);

            if (guest.get("sex") != null && !guest.getString("sex").equals("")) {
                person.setSex(guest.getInt("sex"));
            }

            person.setRegistPersonId(0);

            if (guest.get("registPersonId") != null && !guest.getString("registPersonId").equals("")) {
                int registPersonId = guest.getInt("registPersonId");
                if (registPersonId > 0) {
                    person.setRegistPersonId(guest.getInt("registPersonId"));
                }
            }
            person.setPersonName(guest.getString("personName"));
            person.setIsOther(i == 0 ? 0 : 1);
            person.setHid(user.getHid());
            person.setHotelGroupId(user.getHotelGroupId());
            person.setRoomNum(regist.getRoomNum());
            person.setRoomNumId(regist.getRoomNumId());
            person.setTeamCodeId(regist.getTeamCodeId());
            person.setBookingOrderId(regist.getBookingOrderId());
            if (bookingOrderRoomNumId > 0) {
                person.setBookingOrderRoomNumId(bookingOrderRoomNumId);
            }
            person.setUpdateUserId(user.getUserId());
            person.setUpdateUserName(user.getUserName());
            person.setClassId(user.getClassId());
            person.setHotelGroupId(user.getHotelGroupId());

            if (guest.get("address") != null) {
                person.setAddress(guest.getString("address"));
            }
            if (guest.get("Address") != null) {
                person.setAddress(guest.getString("Address"));
            }
            if (guest.get("nation") != null && !"".equals(guest.getString("nation")) && !"null".equals(guest.getString("nation"))) {
                String nation = guest.getString("nation");
                if (nation.indexOf("族") < 1) {
                    nation += "族";
                }
                person.setNation(HotelUtils.nationMap.getInt(nation));
            }

            if (guest.get("phone") != null && !guest.getString("phone").equals("") && !guest.getString("phone").equals("null")) {
                person.setPhone(guest.getString("phone"));
            }

            /**
             * 身份证照  新版本
             */
            if (guest.get("image") != null && !"".equals(guest.getString("image"))) {
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("image").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(code, "UTF-8") + ".jpeg");
                person.setIdImage(uploadObjectRsp.getFileName());
            }

            /**
             * 证件照,新版本
             */
            if (guest.get("cameraPicture") != null && !"".equals(guest.getString("cameraPicture"))) {
                String s = code + regist.getHid() + regist.getRoomNumId() + HotelUtils.currentTime();
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("cameraPicture").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                person.setCameraImage(uploadObjectRsp.getFileName());
            }

            /**
             * 证件照,老版本 Image
             */
            if (guest.get("photo") != null && !"".equals(guest.getString("photo"))) {
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("photo").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(code, "UTF-8") + ".jpeg");
                person.setIdImage(uploadObjectRsp.getFileName());
            }


            /**
             * 身份证照  老版本
             */
            if (guest.get("cameraPhoto") != null && !"".equals(guest.getString("cameraPhoto"))) {
                String s = code + regist.getHid() + regist.getRoomNumId() + HotelUtils.currentTime();
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("cameraPhoto").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                person.setCameraImage(uploadObjectRsp.getFileName());
            }


            /**
             * 证件照,老版本 Image
             */
            if (guest.get("Photo") != null && !"".equals(guest.getString("Photo"))) {
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("Photo").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(code, "UTF-8") + ".jpeg");
                person.setIdImage(uploadObjectRsp.getFileName());
            }


            /**
             * 身份证照  老版本
             */
            if (guest.get("CameraPhoto") != null && !"".equals(guest.getString("CameraPhoto"))) {
                String s = code + regist.getHid() + regist.getRoomNumId() + HotelUtils.currentTime();
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("CameraPhoto").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                person.setCameraImage(uploadObjectRsp.getFileName());
            }

            /**
             * 相似度   0~1 之间  Similarity，相似度
             */
            if (guest.get("semblance") != null && !"".equals(guest.getString("semblance"))) {
                person.setConfidence(guest.getString("semblance"));
            }

            /**
             * 成功或失败  0-失败 1-成功
             */
            if (guest.get("faceResult") != null && !"".equals(guest.getString("faceResult"))) {
                try {
                    person.setFaceResult(guest.getInt("faceResult"));
                } catch (Exception ex) {
                    log.error("",ex);
                    person.setFaceResult(0);
                }
            }

            /**
             * 公安网上传状态
             */
            if (guest.get("guestStatus") != null && !"".equals(guest.getString("guestStatus"))) {
                try {
                    person.setGuestType(guest.getInt("guestStatus"));
                } catch (Exception ex) {
                    log.error("",ex);
                    person.setFaceResult(0);
                }
            }

            /**
             * 公安网流水号
             */
            if (guest.get("guestNo") != null && !"".equals(guest.getString("guestNo")) && !"null".equals(guest.getString("guestNo"))) {
                person.setGuestNo(guest.getString("guestNo"));
            }

            /**
             * 公安网唯一标识
             */
            if (guest.get("guestId") != null && !"".equals(guest.getString("guestId"))) {
                person.setGuestId(guest.getString("guestId"));
            }

            registPeople.add(person);

            /**
             * 这里创建子线程处理 客史的问题
             */
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        createPersonInfo(registPeople);
                    } catch (Exception e) {
                        log.error("",e);
                        e.printStackTrace();
                    }
                }
            });
        }

        return registPeople;
    }

    /**
     * 更新会员入住天数
     */
    public void updateVipCheckInNum(final ArrayList<Regist> regists, final TbUserSession user) {

        HashMap<Integer, CardInfo> cardInfoHashMap = new HashMap<>();

        ArrayList<CardCheckinRecord> cardCheckinRecords = new ArrayList<>();

        CardCheckinRecordSearch cardCheckinRecordSearch = new CardCheckinRecordSearch();
        cardCheckinRecordSearch.setHid(user.getHid());

        for (Regist regist : regists) {

            Integer memberId = regist.getMemberId();
            // 查询会员信息
            CardInfo cardInfo = cardInfoHashMap.get(memberId);
            if (cardInfo == null) {
                CardInfo cardInfo1 = cardInfoDao.selectById(memberId);
                if (cardInfo1 == null) {
                    continue;
                }
                cardInfo = cardInfo1;
                cardInfoHashMap.put(memberId, cardInfo1);
            }

            CardCheckinRecord cardCheckinRecord = new CardCheckinRecord();

            // 结账班次不等于0 ，则说明已经结过账反结
            if (regist.getCheckoutClassId() != 0) {
                cardCheckinRecordSearch.setRegistId(regist.getRegistId());
                Page<CardCheckinRecord> cardCheckinRecords1 = cardCheckinRecordDao.selectBySearch(cardCheckinRecordSearch);
                if (cardCheckinRecords1.size() > 0) {
                    cardCheckinRecord = cardCheckinRecords1.get(0);
                }
            } else {
                cardCheckinRecord.setHid(user.getHid());
                cardCheckinRecord.setHotelGroupId(user.getHotelGroupId());
                cardCheckinRecord.setRegistId(regist.getRegistId());
                cardCheckinRecord.setBookingId(regist.getBookingOrderId());
            }
            cardCheckinRecord.setRoomInfoId(regist.getRoomNumId());
            cardCheckinRecord.setRoomNo(regist.getRoomNum());
            cardCheckinRecord.setRoomTypeId(regist.getRoomTypeId());
            cardCheckinRecord.setRoomTypeName(regist.getRoomTypeName());
            cardCheckinRecord.setCardGroupId(cardInfo.getCardGroupId());
            cardCheckinRecord.setCardId(cardInfo.getId());
            cardCheckinRecord.setCardGroupLevelId(cardInfo.getCardGroupLevelId());
            cardCheckinRecord.setCardGroupTypeId(cardInfo.getCardGroupTypeId());
            cardCheckinRecord.setCardLevelId(cardInfo.getCardLevelId());
            cardCheckinRecord.setCardTypeId(cardInfo.getCardTypeId());

            cardCheckinRecord.setCheckInTime(regist.getCheckinTime());
            cardCheckinRecord.setCheckOutTime(regist.getSettleAccountTime());

            List betweenDates = HotelUtils.getBetweenDates(regist.getCheckinTime(), regist.getSettleAccountTime());
            cardCheckinRecord.setDayNum(betweenDates.size());

            cardCheckinRecords.add(cardCheckinRecord);
        }

        for (CardCheckinRecord cardCheckinRecord : cardCheckinRecords) {

            if (cardCheckinRecord.getId() != null) {
                cardCheckinRecordDao.update(cardCheckinRecord);
            } else {
                cardCheckinRecordDao.insert(cardCheckinRecord);
            }

        }

    }

    /**
     * 续住
     *
     * @param overStayRequest
     * @return
     */
    @Override
    public ResponseData overStayNew(OverStayRequest overStayRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = overStayRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Regist regist = registDao.selectById(overStayRequest.getRegistId());

            if (regist == null || !regist.getHid().equals(user.getHid()) || regist.getState() != 0) {
                throw new Exception("当前订单信息有误，未查到订单信息。");
            }

            //优化更新regist表字段业务
            Regist registInfo = Regist.CreateRegist(regist.getRegistId());

            Date checkoutTime = regist.getCheckoutTime();


            ArrayList<Oprecord> oprecords = new ArrayList<>();

            Oprecord oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setRoomNum(regist.getRoomNum());

            final HashMap<String, String> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put("room", regist.getRoomNum());
            stringStringHashMap.put("oldDate", HotelUtils.parseDate2Str(regist.getCheckoutTime()));


            // 查询当前订单配置信息
            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setRegistId(regist.getRegistId());
            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);

            BookingOrderConfig bookingOrderConfig = bookingOrderConfigs.get(0);

            // 查询房价方案，获取早餐信息
            RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
            roomRateCodeSpecificSearch.setHid(user.getHid());
            roomRateCodeSpecificSearch.setRateId(overStayRequest.getRateId());
            roomRateCodeSpecificSearch.setRoomTypeId(regist.getRoomTypeId());
            List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);

            int breakNum = 0;

            if (roomRateCodeSpecifics != null && roomRateCodeSpecifics.size() > 0) {
                RoomRateCodeSpecific roomRateCodeSpecific = roomRateCodeSpecifics.get(0);
                breakNum = roomRateCodeSpecific.getBreakfastNum();
            }

            regist.setRoomRateCodeId(overStayRequest.getRateId());
            regist.setRoomRateCodeName(overStayRequest.getRateCode());

            //新对象赋值
            registInfo.setRoomRateCodeId(overStayRequest.getRateId());
            registInfo.setRoomRateCodeName(overStayRequest.getRateCode());

            // 添加的房价信息
            ArrayList<BookingOrderDailyPrice> addPriceList = new ArrayList<>();

            AddArAccount addArAccount = null;

            //查询之前的入住人  2021-10-19 新增

            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(regist.getRegistId());
            List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);

            // 查询原有房价
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setRegistId(regist.getRegistId());

            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, BookingOrderDailyPrice> priceMaps = bookingOrderDailyPrices.stream().collect(Collectors.toMap(BookingOrderDailyPrice::getDailyState, a -> a, (k1, k2) -> k1));

            List<OverStayRequest.price> priceList = overStayRequest.getPriceList();

            Date date = new Date();

            StringBuilder dayPiceStr = new StringBuilder();

            Integer sumDayPrice = 0;

            for (OverStayRequest.price pc : priceList) {

                Integer daily = Integer.parseInt(pc.getDate().substring(0, 10).replace("-", ""));

                BookingOrderDailyPrice bookingOrderDailyPrice = priceMaps.get(daily);

                // 已添加的日期则过滤不添加
                if (bookingOrderDailyPrice != null && bookingOrderDailyPrice.getId() > 0) {
                    continue;
                }

                BookingOrderDailyPrice bodps = new BookingOrderDailyPrice();
                bodps.setId(null);
                bodps.setBookingOrderId(regist.getBookingOrderId());
                bodps.setHid(user.getHid());
                bodps.setHotelGroupId(user.getHotelGroupId());
                bodps.setPrice(pc.getPrice());
                bodps.setDailyTime(daily);
                bodps.setRoomTypeId(regist.getRoomTypeId());
                bodps.setRoomNumId(0);
                bodps.setDailyState(1);
                bodps.setIsStayover(0);
                bodps.setCreateTime(date);
                bodps.setCreateUserId(user.getUserId());
                bodps.setCreateUserName(user.getUserName());
                bodps.setUpdateTime(date);
                bodps.setUpdateUserId(user.getUserId());
                bodps.setUpdateUserName(user.getUserName());
                bodps.setRegistId(regist.getRegistId());
                bodps.setRoomNumId(regist.getRoomNumId());
                bodps.setBreakNum(breakNum);
                bodps.setBreakAddNum(0);
                bodps.setBreakUseNum(0);
                addPriceList.add(bodps);

                dayPiceStr.append(pc.getPrice());
                sumDayPrice += pc.getPrice();

            }

            // 修改预订单时间
            Date newDates = HotelUtils.parseStr2Date(overStayRequest.getNewDate());

            regist.setCheckoutTime(newDates);
            registInfo.setCheckoutTime(newDates);
            if (regist.getDayCount() != null) {
                regist.setDayCount(regist.getDayCount() + overStayRequest.getDayCount());
                registInfo.setDayCount(regist.getDayCount() + overStayRequest.getDayCount());
            }

            stringStringHashMap.put("newDate", HotelUtils.parseDate2Str(regist.getCheckoutTime()));

            oprecord.setDescription("续住：" + overStayRequest.getDayCount() + "天。新的离店时间:" + HotelUtils.parseDate2Str(regist.getCheckoutTime()));
            oprecords.add(oprecord);

            // 判断当前订单信息 ,如果是OTA订单并且可以结账转AR，则说明是预付订单。
            Integer resourceId = regist.getResourceId();

            Integer autoAr = bookingOrderConfig.getAutoAr();

            HotelCompanyAccount hotelCompanyAccount = null;

            HotelCompanyAccountInfo hotelCompanyAccountInfo = null;


            int newDateInt = HotelUtils.parseDate2Int(regist.getCheckoutTime());

            ArrayList<BookingOrderDailyPrice> deletePrices = new ArrayList<>();

            Integer deleteMoney = 0;

            for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {
                Integer dailyState = bodp.getDailyState();

                if (dailyState == 0) {
                    continue;
                }

                if (bodp.getDailyTime() >= newDateInt) {

                    deletePrices.add(bodp);
                    deleteMoney += bodp.getPrice();
                }
            }


            // 判断是否是协议
      /*      if (resourceId == 5 && autoAr == 1) {

                bookingOrderConfig.setAutoAr(0);

                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setIsCancel(0);
                accountSearch.setRegistId(regist.getRegistId());

                List<Account> accounts = accountDao.selectBySearch(accountSearch);

                Integer sumArAccount = 0;

                for (Account account : accounts) {

                    String payCodeId = account.getPayCodeId();

                    if (!payCodeId.equals("9800")) {
                        continue;
                    }

                    String thirdAccoutId = account.getAccountId();

                    HotelCompanyArRecodeSearch hotelCompanyArRecodeSearch = new HotelCompanyArRecodeSearch();
                    hotelCompanyArRecodeSearch.setHid(user.getHid());
                    hotelCompanyArRecodeSearch.setTransactionId(thirdAccoutId);
                    Page<HotelCompanyArRecode> hotelCompanyArRecodes = hotelCompanyArRecodeDao.selectBySearch(hotelCompanyArRecodeSearch);

                    if (hotelCompanyArRecodes == null || hotelCompanyArRecodes.size() < 1) {
                        continue;
                    }

                    HotelCompanyArRecode hotelCompanyArRecode = hotelCompanyArRecodes.get(0);

                    boolean equals = regist.getCompanyAccountId().equals(hotelCompanyArRecode.getCompanyAccountId());

                    if (equals) {
                        sumArAccount += account.getPrice();
                    }

                }

                Integer sumPrice = 0;
                // 查询所有的价格
                for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {
                    sumPrice += bodp.getPrice();
                }

                sumPrice -= deleteMoney;

                int diff = sumPrice - sumArAccount;

                // 把原有的应挂账金额添加过去

          *//*      if (diff > 0) {

                    String a = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
                    Account account = new Account();
                    account.setAccountId(a);
                    account.setHid(user.getHid());
                    account.setCreateUserId(user.getUserId());
                    account.setCreateUserName(user.getUserName());
                    account.setCreateTime(new Date());
                    account.setIsCancel(0);
                    account.setAccountYear(user.getBusinessYear());
                    account.setAccountYearMonth(user.getBusinessMonth());
                    account.setBusinessDay(user.getBusinessDay());
                    account.setClassId(user.getClassId());
                    account.setAccountType(1);
                    account.setSettleAccountTime(new Date());
                    account.setThirdRefundState(0);
                    account.setRoomNum(regist.getRoomNum());
                    account.setRoomInfoId(regist.getRoomNumId());
                    account.setRoomTypeId(regist.getRoomTypeId());
                    account.setRegistState(0);
                    account.setRegistId(regist.getRegistId());
                    account.setRemark("续住自动挂房费");
                    account.setBookingId(regist.getBookingOrderId());
                    //类型 ：消费、付款 "9800", "AR帐", 9, "AR帐"
                    account.setPayType(2);
                    account.setSaleNum(1);
                    account.setPayClassId(9);
                    account.setPayClassName("AR帐");
                    //全天房费
                    account.setPayCodeId("9800");
                    account.setPayCodeName("AR帐");

                    //用户信息
                    account.setCreateTime(date);
                    account.setCreateUserId(user.getUserId());
                    account.setCreateUserName(user.getUserName());
                    account.setUpdateTime(date);
                    account.setUpdateUserId(user.getUserId());
                    account.setUpdateUserName(user.getUserName());

                    account.setClassId(user.getClassId());
                    account.setUpdateCalssId(user.getClassId());

                    // 营业日期
                    account.setBusinessDay(user.getBusinessDay());


                    //设置账务关联人id为0
                    //

                    account.setRegistPersonId(0);
                    account.setRegistPersonName("");

                    if (null != registPersonList && registPersonList.size() > 0){
                        account.setRegistPersonId(registPersonList.get(0).getRegistPersonId());
                        account.setRegistPersonName((registPersonList.get(0).getPersonName()));
                    }
                    account.setPrice(diff);
                    account.setUintPrice(diff);

                    hotelCompanyAccount = hotelCompanyAccountDao.selectById(regist.getCompanyAccountId());
                    Integer hotelCompanyId = hotelCompanyAccount.getHotelCompanyId();
                    if (null != hotelCompanyId){
                        HotelCompanyInfo hotelCompanyInfo = hotelCompanyInfoDao.selectById(hotelCompanyId);
                        account.setCompanyName(hotelCompanyInfo.getCompanyName());
                        account.setCompanyId(hotelCompanyInfo.getId());
                    }


                    HotelCompanyArRecode hotelCompanyArRecode = new HotelCompanyArRecode();

                    hotelCompanyArRecode.setCompanyId(hotelCompanyAccount.getHotelCompanyId());
                    hotelCompanyArRecode.setGroupCompanyId(hotelCompanyAccount.getGroupCompanyId());
                    hotelCompanyArRecode.setCompanyAccountId(hotelCompanyAccount.getId());



                    account.setCompanyId(hotelCompanyAccount.getHotelCompanyId());
                    account.setCompanyName(hotelCompanyAccount.getName());

                    // 房间id
                    hotelCompanyArRecode.setRoomInfoId(regist.getRoomNumId());
                    // 房号
                    hotelCompanyArRecode.setRoomNo(regist.getRoomNum());
                    hotelCompanyArRecode.setMoney(diff);
                    // 房间id
                    hotelCompanyArRecode.setRegistId(regist.getRegistId());

                    // 操作人id
                    //  hotelCompanyArRecode.setOperatorId(Integer.parseInt(user.getUserId()));
                    hotelCompanyArRecode.setOperatorName(user.getUserName());
                    // 备注
                    hotelCompanyArRecode.setOperatorName(user.getUserName() + "：续住自动挂房费 ");

                    hotelCompanyArRecode.setSettleId(regist.getRegistId());
                    hotelCompanyArRecode.setOperatTime(date);
                    hotelCompanyArRecode.setBusinessShiftId(user.getClassId());
                    hotelCompanyArRecode.setBusinessDay(user.getBusinessDay());
                    hotelCompanyArRecode.setCreateUserId(user.getUserId());
                    hotelCompanyArRecode.setCreateUserName(user.getUserName());
                    hotelCompanyArRecode.setCreateTime(date);
                    hotelCompanyArRecode.setUpdateUserId(user.getUserId());
                    hotelCompanyArRecode.setUpdateUserName(user.getUserName());
                    hotelCompanyArRecode.setCreateTime(date);
                    hotelCompanyArRecode.setHid(user.getHid());
                    hotelCompanyArRecode.setHotelGroupId(user.getHotelGroupId());
                    hotelCompanyArRecode.setPayState(0);

                    addArAccount = new AddArAccount();
                    addArAccount.setAccount(account);
                    addArAccount.setHotelCompanyArRecode(hotelCompanyArRecode);


                    hotelCompanyAccountInfo = hotelCompanyAccountInfoDao.selectById(hotelCompanyAccount.getId());
                    hotelCompanyAccountInfo.setId(hotelCompanyAccount.getId());
                    hotelCompanyAccountInfo.setNoOffWriteMoney(hotelCompanyAccountInfo.getNoOffWriteMoney() + diff);
                    hotelCompanyAccountInfo.setMaxLimit(hotelCompanyAccountInfo.getMaxLimit() - diff);

                    oprecord = new Oprecord(user);
                    oprecord.setOccurTime(HotelUtils.currentTime());
                    oprecord.setRegistId(regist.getRegistId());
                    oprecord.setRoomNum(regist.getRoomNum());

                    oprecord.setDescription("续住时将原有订单房费：" + diff / 100.0 + "自动转到" + regist.getCompayName());

                    oprecords.add(oprecord);


                    regist.setSumPay(regist.getSumPay() + diff);

                    registInfo.setSumPay(regist.getSumPay() + diff);

                }*//*


            }*/
            regist.setMacContinue(1);

            registInfo.setMacContinue(1);

            RegistStayover rs = new RegistStayover();
            rs.setRegistId(regist.getRegistId());
            rs.setBookingOrderId(regist.getBookingOrderId());
            rs.setDayCount(overStayRequest.getDayCount());
            rs.setRoomNum(regist.getRoomNum());
            rs.setRoomTypeId(regist.getRoomTypeId());
            rs.setRoomTypeName(regist.getRoomTypeName());
            rs.setStartTime(checkoutTime);
            rs.setEndTime(regist.getCheckoutTime());
            rs.setNprices(dayPiceStr.toString());
            rs.setTotalPrice(sumDayPrice);
            rs.setDescRemark("自助机续住");
            rs.setState(1);

            rs.setHid(user.getHid());
            rs.setHotelGroupId(user.getHotelGroupId());
            rs.setClassId(user.getClassId());
            rs.setBusinessDay(user.getBusinessDay());
            rs.setBusinessDay(user.getBusinessMonth());
            rs.setBusinessYear(user.getBusinessYear());
            rs.setCreateUserId(user.getUserId());
            rs.setCreateUserName(user.getUserName());
            rs.setCreateTime(new Date());
            rs.setUpdateUserId(user.getUserId());
            rs.setUpdateUserName(user.getUserName());
            rs.setUpdateTime(new Date());

            // 班次6 说明自助机开房
            if (regist.getClassId() == 6) {
                rs.setTypeState(1);
            } else {
                rs.setTypeState(2);
            }

            pmsOrderTransactionService.overStayTransactionTwo(registInfo, addPriceList, deletePrices, addArAccount, hotelCompanyAccountInfo, bookingOrderConfig, rs);

            this.addOprecords(oprecords);

            final String roomNum = regist.getRoomNum();
            final Integer registId = regist.getRegistId();
            final String startTime = HotelUtils.parseDate2Str(regist.getCheckinTime());
            final String endTime = HotelUtils.parseDate2Str(regist.getCheckoutTime());

            // 更新房间缓存
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        roomService.updateRoomListForCache(user);
                        baseService.turnAlways(user);
                        HashMap<String, String> filedMap = new HashMap<>();
                        filedMap.put("roomList", roomNum);
                        filedMap.put("beginTime", startTime);
                        filedMap.put("endTime", endTime);

                        HashMap<String, String> dataMap = new HashMap<>();
                        dataMap.put("registId", registId + "");
                        baseService.push(user.getHotelGroupId(), user.getHid(), 22, filedMap, dataMap, true, true);

                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

            //门锁推送
            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    log.info("开始推送门锁信息");

                    //如果是智能门锁则调用
                    SmartLockRequest smartLockRequest = new SmartLockRequest();
                    smartLockRequest.setSessionToken(sessionToken);
                    smartLockRequest.setCheckinTime(regist.getCheckinTime());
                    smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                    smartLockRequest.setRoomTypeId(regist.getRoomTypeId());
                    smartLockRequest.setRoomTypeName(regist.getRoomTypeName());
                    smartLockRequest.setRoomNumId(regist.getRoomNumId());
                    smartLockRequest.setRoomNum(regist.getRoomNum());
                    smartLockRequest.setHid(regist.getHid());
                    smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                    smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                    smartLockRequest.setCheckinType(regist.getCheckinType());
                    smartLockRequest.setLockNo(regist.getSessionToken());

                    List<RegistPerson> registPeople = registPersonList;
                    List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                    for (int j = 0; j < registPeople.size(); j++) {
                        RegistPerson registPersonInfo = registPeople.get(j);
                        SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                        BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                        peoplesList.add(registPersonDetail);
                    }
                    smartLockRequest.setPeoples(peoplesList);
                    baseService.SmartLockStayOver(smartLockRequest);
                }
            });

        } catch (Exception e) {
            log.error("",e);
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData hourRoomPushMessage(Regist regist) {
        ResponseData responseData = ResponseData.newSuccessData();
        /**
         * 首先发送系统提醒
         */
        Map<String, String> fieledMap = new HashMap<>();
        fieledMap.put("roomNo", regist.getRoomNum());
        this.push(regist.getHotelGroupId(), regist.getHid(), 43, fieledMap, new HashMap<String, String>(), true, true);
//        /**
//         * 判断酒店是否有智能音箱，如果有的话则推送
//         */
        SendMessageToAudioRequest sendMessageToAudioRequest = new SendMessageToAudioRequest();
        sendMessageToAudioRequest.setRoomNo(regist.getRoomNum());
        sendMessageToAudioRequest.setRoomInfoId(regist.getRoomNumId());
        sendMessageToAudioRequest.setTitle("退房提醒");
        sendMessageToAudioRequest.setMessageTxt("您的房间15分钟后超时，如需续时，请及时处理！");
        sendMessageToAudioRequest.setMessageType("1");
        audioService.sendMessageToAudio(sendMessageToAudioRequest);
        return responseData;
    }

}
