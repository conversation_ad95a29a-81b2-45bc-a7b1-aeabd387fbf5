package com.pms.czabsnight.service;

import com.pms.czabsnight.bean.search.NightAuditRecordSearch;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.ApplyHotelRequest;
import net.sf.json.JSONObject;

public interface NightStepService {

    public ResponseData searchNightAuditRecord(NightAuditRecordSearch nightAuditRecordSearch);

    /**
     * 查询酒店的夜审步骤
     * @return
     */
    public ResponseData searchNightStep(JSONObject param);

    /**
     * 查询夜审状态
     * @param param
     * @return
     */
    public ResponseData searchNightRecord(JSONObject param);


    /**
     * 跳过夜审步骤
     * @param param
     * @return
     */
    public ResponseData skipNightStep(JSONObject param);

    /**
     * 创建夜审状态
     * @param param
     * @return
     */
    public ResponseData createNightRecord(JSONObject param);

    /**
     * 保留当晚房态
     * @param param
     * @return
     */
    public ResponseData keepRoomStatus(JSONObject param);

    /**
     * 取消当晚全部noShow房间
     * @param param
     * @return
     */
    public ResponseData noShowBook(JSONObject param);


    /**
     * 维修房延期处理
     * @param param
     * @return
     */
    public ResponseData repairRoomAddDate(JSONObject param);

    /**
     * 夜审挂账处理
     * @param param
     * @return
     */
    public ResponseData nightOnAccount(JSONObject param);


    /**
     * 夜审钟点房挂账处理
     * @param param
     * @return
     */
    public ResponseData nightHourOnAccount(JSONObject param);

    /**
     * 夜审房租及入账
     * @return
     */
    public ResponseData addNightAccount(JSONObject param);


    /**
     * 统计当前房型数据
     * @param param
     * @return
     */
    public ResponseData statisticsRoomAccountData(JSONObject param);


    /**
     * 统计当前房间售卖详情
     * @param param
     * @return
     */
    public ResponseData statisticsGoodsData(JSONObject param);


    /**
     * 统计当前支付信息
     * @param param
     * @return
     */
    public ResponseData statisticsPayMsg(JSONObject param);

    public ResponseData nightBalance(JSONObject param);


    /**
     * 统计房型数据
     * @param param
     * @return
     */
    public ResponseData statisticsRoomTypeData(JSONObject param);

    /**
     * 统计客源数据
     * @param param
     * @return
     */
    public ResponseData statisticsResouceData(JSONObject param);

    /**
     * 统计OTA数据
     * @param param
     * @return
     */
    public ResponseData statisticsOTAData(JSONObject param);

    /**
     * 会员注册数
     * @param param
     * @return
     */
    public ResponseData registerVipNum(JSONObject param);

    /**
     * 协议单位统计
     * @param param
     * @return
     */
    public ResponseData statisticsArData(JSONObject param);

    /**
     * 协议回款信息
     * @param param
     * @return
     */
    public ResponseData arPayRec (JSONObject param );


    /**
     * 夜审完成时
     * @param param
     * @return
     */
    public ResponseData finishNightStep(JSONObject param);

    /**
     * 创建酒店时自动创建 night_audit_record 和 night_auto_setting
     * @param request
     */
    void applyHotel(ApplyHotelRequest request);
}
