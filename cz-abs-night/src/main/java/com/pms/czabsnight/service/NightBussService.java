package com.pms.czabsnight.service;

import com.pms.czabsnight.bean.NightAuditSettingRequest;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.AddOrUpdateHotelBusinessDayRequest;
import com.pms.czpmsutils.request.BaseRequest;
import net.sf.json.JSONObject;

/**
 * 关于相关的业务查询方法
 */
public interface NightBussService {

    /**
     * 查询应到未到订单
     *
     * @param param
     * @return
     */
    public ResponseData noShowBook(JSONObject param);

    /**
     * 查询应离未离的订单
     *
     * @param param
     * @return
     */
    public ResponseData overtimeRegist(JSONObject param);


    /**
     * 查询夜审未离钟点房
     *
     * @param param
     * @return
     */
    public ResponseData overtimeHourRoom(JSONObject param);

    /**
     * 查询到期的维修房
     *
     * @param param
     * @return
     */
    public ResponseData expireRepairRoom(JSONObject param);

    /**
     * 查询当前未结的房间
     *
     * @param param
     * @return
     */
    public ResponseData checkinRoom(JSONObject param);

    /**
     * 查询自动夜审
     *
     * @param param
     * @return
     */
    public ResponseData searchNightSetting(JSONObject param);

    /**
     * 添加或修改自动夜审设置
     *
     * @param param
     * @return
     */
    public ResponseData addOrUpdateNightSetting(NightAuditSettingRequest param);

    /**
     * 修改营业日期
     *
     * @param addOrUpdateHotelBusinessDayRequest
     * @return
     */
    public ResponseData addOrUpdateHotelBusinessDay(AddOrUpdateHotelBusinessDayRequest addOrUpdateHotelBusinessDayRequest);

    public ResponseData getHotelBusinessDay(BaseRequest baseRequest);
}
