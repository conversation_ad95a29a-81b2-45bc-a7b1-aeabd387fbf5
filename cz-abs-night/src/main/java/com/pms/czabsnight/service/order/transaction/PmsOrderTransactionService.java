package com.pms.czabsnight.service.order.transaction;

import com.pms.czabsnight.bean.AddArAccount;
import com.pms.czabsnight.bean.CheckInRegist;
import com.pms.czaccount.bean.account.Account;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czmembership.bean.company.HotelCompanyAccountInfo;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.*;
import net.sf.json.JSONArray;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface PmsOrderTransactionService {
    /**
     *
     * @param registGroup       集团信息
     * @param checkInRegists     入住人集合
     * @param accounts           账务集合
     * @param sumMoney           总支付金额
     * @param deleteRelations   需要删除的辅助房态
     * @return
     */
    public JSONArray blendCheckInTransaction(RegistGroup registGroup, List<CheckInRegist> checkInRegists, List<Account> accounts, int sumMoney, TbUserSession user, ArrayList<RoomAuxiliaryRelation> deleteRelations) throws Exception;


    public Map<Integer, Integer> bookingCheckInTransactionNew(BookingOrder bookingOrder, List<BookingOrderRoomNum> bookingOrderRoomNumList, Map<Integer, Regist> checkInRegistMap, List<RoomInfo> roomInfoList, Map<Integer, List<RegistPerson>> registPersonMap,
                                                              Map<Integer, BookingOrderConfig> bookingOrderConfigMap, List<RoomAuxiliaryRelation> deleteRoomAuxiliaryRelationList, Map<Integer, List<RoomAuxiliaryRelation>> addRoomAuxiliaryRelation,
                                                              RegistGroup registGroup, List<Account> accounts, TbUserSession user, Map<Integer, List<BookingOrderDailyPrice>> dayPriceMap) throws Exception;

    /**
     * 结账
     *
     * @param registList
     * @param registGroups
     * @param bookingOrderList
     * @param bookingOrderRoomTypeList
     * @param bookingOrderRoomNums
     * @param roomAuxiliaryRelations
     * @param user
     * @param accountList
     * @param roomInfoMap
     * @throws Exception
     */
    public default void checkOut(List<Regist> registList, List<RegistGroup> registGroups, List<BookingOrder> bookingOrderList,
                                 List<BookingOrderRoomType> bookingOrderRoomTypeList, List<BookingOrderRoomNum> bookingOrderRoomNums,
                                 List<RoomAuxiliaryRelation> roomAuxiliaryRelations, TbUserSession user, List<Account> accountList, Map<Integer, RoomInfo> roomInfoMap,
                                 ArrayList<Object> salesHotelCommissionDetailsList,List<RegistPerson> registPeople) throws Exception {

    }

    /**
     * 续住参数
     * @param regist
     * @param bookingOrderDailyPrices
     * @param deletePrices
     * @param addArAccount
     * @param hotelCompanyAccountInfo
     * @param bookingOrderConfig
     * @throws Exception
     */
    public void overStayTransactionTwo(Regist regist, List<BookingOrderDailyPrice> bookingOrderDailyPrices , List<BookingOrderDailyPrice> deletePrices , AddArAccount addArAccount , HotelCompanyAccountInfo hotelCompanyAccountInfo , BookingOrderConfig bookingOrderConfig,RegistStayover rs) throws Exception;


}
