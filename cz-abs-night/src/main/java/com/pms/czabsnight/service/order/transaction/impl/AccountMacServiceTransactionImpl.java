package com.pms.czabsnight.service.order.transaction.impl;

import com.pms.czabsnight.service.order.transaction.AccountMacTransactionService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czpmsutils.OrderNumUtils;
import com.pms.czpmsutils.constant.regist.CHECK_IN_TYPE;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.dao.BookingOrderDailyPriceDao;
import com.pms.pmsorder.dao.RegistDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@Service
@Primary
public class AccountMacServiceTransactionImpl extends BaseService implements AccountMacTransactionService {

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void autoAddRoomPrice(BookingOrderDailyPrice bookingOrderDailyPrice, Regist regist, RegistPerson registPerson, TbUserSession user) throws Exception {

        String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
        // 1.修改价格信息改为已结
        bookingOrderDailyPrice.setDailyState(0);
        bookingOrderDailyPriceDao.editBookingOrderDailyPrice(bookingOrderDailyPrice);

        // 2.添加账务信息
        Account account = new Account();
        account.setAccountId(accountId);
        account.setHid(user.getHid());
        account.setHotelGroupId(user.getHotelGroupId());
        account.setCreateUserId(user.getUserId());
        account.setCreateUserName(user.getUserName());
        account.setCreateTime(new Date());
        account.setIsCancel(0);
        account.setAccountYear(user.getBusinessYear());
        account.setAccountYearMonth(user.getBusinessMonth());
        account.setBusinessDay(user.getBusinessDay());
        account.setClassId(user.getClassId());
        account.setRegistId(regist.getRegistId());
        account.setBookingId(regist.getBookingOrderId());
        account.setTeamCodeId(regist.getTeamCodeId());
        account.setPrice(bookingOrderDailyPrice.getPrice());
        account.setPayType(1);

        account.setPayClassId(10);
        account.setPayClassName("客房");
        account.setPayCodeId("0002");
        account.setPayCodeName("全天房费");
        if (regist.getCheckinType() == CHECK_IN_TYPE.CIT_HOURDAY) {
            account.setPayCodeId("0007");
            account.setPayCodeName("钟点房费");
        }

        account.setRoomInfoId(regist.getRoomNumId());
        account.setRoomTypeId(regist.getRoomTypeId());
        account.setRoomNum(regist.getRoomNum());
        account.setIsSale(1);

        account.setUintPrice(bookingOrderDailyPrice.getPrice());
        account.setSaleNum(1);
        account.setRegistState(0);
        account.setRemark("开房自动产生房费");
        account.setSettleAccountTime(new Date());
        account.setRefundPrice(0);
        account.setThirdRefundState(0);
        account.setAccountType(1);

        account.setThirdAccoutId(bookingOrderDailyPrice.getId()+"");

        account.setRegistPersonId(registPerson.getRegistPersonId());
        account.setRegistPersonName(registPerson.getPersonName());

        Integer integer = accountDao.saveAccount(account);

        if (integer < 1) {
            throw new Exception("自动添加房费信息失败");
        }

        regist.setSumSale(regist.getSumSale() + bookingOrderDailyPrice.getPrice());

        Integer update = registDao.update(regist);

        if (update < 1) {
            throw new Exception("自动添加房费信息失败");
        }

    }
}
