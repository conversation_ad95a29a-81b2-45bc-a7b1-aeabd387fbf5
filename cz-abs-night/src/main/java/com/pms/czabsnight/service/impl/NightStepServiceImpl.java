package com.pms.czabsnight.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.Page;
import com.pms.czabsnight.bean.*;
import com.pms.czabsnight.bean.search.*;
import com.pms.czabsnight.dao.*;
import com.pms.czabsnight.service.NightStepService;
import com.pms.czabsnight.service.transaction.NightTransactionService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountThirdSummary;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.bean.account.search.AccountThirdSummarySearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czaccount.dao.account.AccountThirdPayRecodeDao;
import com.pms.czhotelfoundation.bean.code.HotelBusinessDay;
import com.pms.czhotelfoundation.bean.code.HotelCostCode;
import com.pms.czhotelfoundation.bean.code.search.HotelBusinessDaySearch;
import com.pms.czhotelfoundation.bean.code.search.HotelCostCodeSearch;
import com.pms.czhotelfoundation.bean.docking.PlatDockingSetting;
import com.pms.czhotelfoundation.bean.room.*;
import com.pms.czhotelfoundation.bean.room.search.*;
import com.pms.czhotelfoundation.bean.room.search.RoomAuxiliaryRelationSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomRepairRecordHistorySearch;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeSearch;
import com.pms.czhotelfoundation.bean.search.FounTbUserSessionSearch;
import com.pms.czhotelfoundation.bean.setting.HotelSetting;
import com.pms.czhotelfoundation.dao.code.HotelBusinessDayDao;
import com.pms.czhotelfoundation.dao.code.HotelCostCodeDao;
import com.pms.czhotelfoundation.dao.room.*;
import com.pms.czhotelfoundation.dao.docking.PlatDockingSettingDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomRepairRecordHistoryDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czhotelfoundation.dao.setting.HotelSettingDao;
import com.pms.czhotelfoundation.dao.user.FounTbUserSessionDao;
import com.pms.czhotelfoundation.service.docking.PlatDockingSettingService;
import com.pms.czmembership.bean.company.HotelCompanyInfo;
import com.pms.czmembership.bean.company.HotelCompanyPayRecord;
import com.pms.czmembership.bean.company.search.HotelCompanyInfoSearch;
import com.pms.czmembership.bean.company.search.HotelCompanyPayRecordSearch;
import com.pms.czmembership.bean.member.*;
import com.pms.czmembership.bean.member.search.CardGroupInfoSearch;
import com.pms.czmembership.bean.member.search.CardLevelSearch;
import com.pms.czmembership.bean.member.search.CardOperationRecordSearch;
import com.pms.czmembership.bean.member.search.CardRechargeSearch;
import com.pms.czmembership.dao.company.HotelCompanyInfoDao;
import com.pms.czmembership.dao.company.HotelCompanyPayRecordDao;
import com.pms.czmembership.dao.member.CardGroupInfoDao;
import com.pms.czmembership.dao.member.CardLevelDao;
import com.pms.czmembership.dao.member.CardOperationRecordDao;
import com.pms.czmembership.dao.member.CardRechargeDao;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.ET;
import com.pms.czpmsutils.constant.HC;
import com.pms.czpmsutils.constant.HclassUtits;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.night.N_STEP;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.push.PUSH_CONFIG_ID;
import com.pms.czpmsutils.constant.room.ROOM_AUXILIARY;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.room.RoomUtils;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.enums.PlatTypeEnum;
import com.pms.czpmsutils.request.AccountSummary;
import com.pms.czpmsutils.request.ApplyHotelRequest;
import com.pms.czpmsutils.request.ThirdAuthReq;
import com.pms.czpmsutils.request.UpdateHotelUserRequest;
import com.pms.czpmsutils.request.search.AccountSummarySearch;
import com.pms.czpmsutils.threadpool.ThreadPool;
import com.pms.czpmsutils.threadpool.ThreadPoolFactory;
import com.pms.czpmsutils.threadpool.ThreadTypeEnum;
import com.pms.pmsorder.bean.NightRoomAveragePrice;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.request.RegistPageRequest;
import com.pms.pmsorder.bean.search.*;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.dao.*;
import com.pms.pmsorder.dao.RegistDao;
import com.pms.pmswarehouse.bean.GoodsSummaryData;
import com.pms.pmswarehouse.bean.search.GoodsStockDetailSearch;
import com.pms.pmswarehouse.dao.GoodsStockDetailDao;
import net.sf.json.JSONObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.client.RestTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Primary
public class NightStepServiceImpl extends BaseService implements NightStepService {

    private static final Logger log = LogManager.getLogger(NightStepServiceImpl.class);
    // region 引入外部到层
    @Autowired
    private HotelSettingDao hotelSettingDao;

    @Autowired
    private NightAuditRecordDao auditRecordDao;

    @Autowired
    private NightStepDao nightStepDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private NightAuditRoomStatusDao nightAuditRoomStatusDao;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RoomRepairRecordHistoryDao roomRepairRecordHistoryDao;


    @Autowired
    private NightTransactionService nightTransactionService;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private HotelCostCodeDao hotelCostCodeDao;

    @Autowired
    private NightAuditDailyRegistRecordDao auditDailyRegistRecordDao;

    @Autowired
    private GoodsStockDetailDao goodsStockDetailDao;

    @Autowired
    private NightAuditGoodsDao nightAuditGoodsDao;

    @Autowired
    private AccountThirdPayRecodeDao accountThirdPayRecodeDao;

    private BaseService baseService = this;

    @Autowired
    private RoomTypeDao roomTypeDao;

    @Autowired
    private CardOperationRecordDao cardOperationRecordDao;

    @Autowired
    private CardLevelDao cardLevelDao;

    @Autowired
    private CardRechargeDao cardRechargeDao;

    @Autowired
    private NightAuditRecordDao nightAuditRecordDao;

    @Autowired
    private HotelBusinessDayDao hotelBusinessDayDao;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private HotelCompanyInfoDao hotelCompanyInfoDao;

    @Autowired
    private NightAuditOtaDao nightAuditOtaDao;

    @Autowired
    private NightAuditResourceDao nightAuditResourceDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Resource
    private NightAutoSettingDao nightAutoSettingDao;

    @Autowired
    private HotelCompanyPayRecordDao hotelCompanyPayRecordDao;

    @Autowired
    private NigthAuditArPaymentDao auditArPaymentDao;

    @Autowired
    private NightAuditBalanceDao nightAuditBalanceDao;

    @Autowired
    private NightAuditMemberBalanceDao nightAuditMemberBalanceDao;

    @Autowired
    private CardGroupInfoDao cardGroupInfoDao;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private RestTemplate restTemplate;

//    @Autowired
//    private HotelRoomRateDao hotelRoomRateDao;

//    private final static String futureSevenBookRate = "/hotel/main/futureSevenBookRate";
//    private final static String preSevenBookRate = "/hotel/main/preSevenBookRate";
//    private final static String currentOccupancyRate = "/hotel/main/currentOccupancyRate";
//    private final static String yesterdayOccupancyRate = "/hotel/main/yesterdayOccupancyRate";

    @Autowired
    private PlatDockingSettingService platDockingSettingService;

    @Autowired
    private FounTbUserSessionDao tbUserSessionDao;


    @Value("${hotel.group.id}")
    private String hotelGroupId;

    // endregion

    /**
     * 保留二位小数
     */
    final static String df = "%.2f";


    @Override
    public ResponseData searchNightAuditRecord(NightAuditRecordSearch nightAuditRecordSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /**
             * 1.获取登录信息
             */
            String sessionToken = nightAuditRecordSearch.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            nightAuditRecordSearch.setHid(user.getHid());
            Page<NightAuditRecord> nightAuditRecords = nightAuditRecordDao.selectBySearch(nightAuditRecordSearch);
            responseData.setData(nightAuditRecords);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }
        return responseData;
    }

    /**
     * 查询夜审步骤
     * <p>
     * 步骤为 酒店夜审设置中 paramId 340-355
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData searchNightStep(JSONObject param) {

        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            // 2. 查询所有的夜审步骤
            HotelSetting hotelSettingSearch = new HotelSetting();
            hotelSettingSearch.setHid(user.getHid());
            hotelSettingSearch.setParentId(1);

            List<HotelSetting> allSetting = hotelSettingDao.getAllSetting(hotelSettingSearch);

            Stream<HotelSetting> sorted = allSetting.stream().sorted(Comparator.comparing(HotelSetting::getParamId));

            responseData.setData(sorted);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * 查询夜审状态
     * <p>
     * 验证当前酒店夜审是否完成，未完成则所有都跳到夜审界面，并查询当前夜审进行到哪一步
     * 已完成或者未夜审，则不进行处理
     * <p>
     * nightState 夜审装  0.进行中  1.已完成 2.代表未开始
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData searchNightRecord(JSONObject param) {

        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            Map<Object, Object> jsonObject = new HashMap<>();

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            NightAuditRecordSearch nightAuditRecordSearch = new NightAuditRecordSearch();
            nightAuditRecordSearch.setHid(user.getHid());
            nightAuditRecordSearch.setBusinessDay(user.getBusinessDay());

            List<NightAuditRecord> nightAuditRecords = auditRecordDao.selectBySearch(nightAuditRecordSearch);

            // 如果没有夜审记录，则说明未开始夜审
            if (nightAuditRecords == null || nightAuditRecords.size() < 1) {
                jsonObject.put("nightState", 2);
                responseData.setData(jsonObject);
                return responseData;
            }

            NightAuditRecord nightAuditRecord = nightAuditRecords.get(0);
            Integer state = nightAuditRecord.getState();
            jsonObject.put("nightState", 2);
            if (state == 1) {
                responseData.setData(jsonObject);
                return responseData;
            }

            // 查询夜审进行步骤
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            Map<Integer, NightStep> collect = nightSteps.stream().collect(Collectors.toMap(NightStep::getNightSettingId, a -> a, (k1, k2) -> k1));

            // 目前未完成的步骤，既进行到哪一步
            NightStep nightStep1 = nightSteps.stream().filter(nightStep -> nightStep.getState() == 0).findAny().orElse(null);
            jsonObject.put("nightState", 1);
            jsonObject.put("nightData", collect);
            jsonObject.put("noFinish", nightStep1);
            jsonObject.put("nightAuditRecord", nightAuditRecord);

            responseData.setData(jsonObject);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * 强制跳过夜审步骤
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData skipNightStep(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);
            // 2. 根据步骤id 查询夜审步骤详情
            int nightStepId = param.getInt("nightStepId");

            NightStep nightStep = nightStepDao.selectById(nightStepId);

            if (nightStep == null) {
                throw new Exception("未查到相应的夜审记录");
            }

            if (nightStep.getState() == 1) {
                throw new Exception("当前步骤已完成，请进行下一步。");
            }

            nightStep.setEndTime(new Date());
            nightStep.setState(1);
            nightStep.setFinishType(2);

            Integer update = nightStepDao.update(nightStep);

            if (update < 1) {
                throw new Exception("强制跳过失败，请尝试正常执行。");
            }
            Integer nightSettingId = nightStep.getNightSettingId();
            nightSettingId++;
            createNightStep(nightSettingId, user, N_STEP.NIGHT_STEP_NAME.get(nightStepId));

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }
        return responseData;
    }

    /**
     * 创建夜审状态
     * <p>
     * 创建夜审记录 创建夜审步骤第一步的信息
     * <p>
     * 如果已创建 则直接返回夜审相关的信息
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public ResponseData createNightRecord(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            Map<Object, Object> jsonObject = new HashMap<>();

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            NightAuditRecordSearch nightAuditRecordSearch = new NightAuditRecordSearch();
            nightAuditRecordSearch.setHid(user.getHid());
            nightAuditRecordSearch.setBusinessDay(user.getBusinessDay());

            List<NightAuditRecord> nightAuditRecords = auditRecordDao.selectBySearch(nightAuditRecordSearch);

            if (nightAuditRecords.size() > 0) {
                return this.searchNightRecord(param);
            }

            NightAuditRecord nightAuditRecord = new NightAuditRecord();
            nightAuditRecord.setHid(user.getHid());
            nightAuditRecord.setHotelGroupId(user.getHotelGroupId());
            nightAuditRecord.setState(0);
            nightAuditRecord.setAuditStart(new Date());
            nightAuditRecord.setClassId(user.getClassId());
            nightAuditRecord.setBusinessDay(user.getBusinessDay());
            nightAuditRecord.setCreateTime(new Date());
            nightAuditRecord.setCreateUserId(user.getUserId());
            nightAuditRecord.setCreateUserName(user.getUserName());
            nightAuditRecord.setMessage(user.getSessionId());

            Integer insert = auditRecordDao.insert(nightAuditRecord);

            if (insert < 1) {
                throw new Exception("创建夜审记录失败请重试。");
            }

            NightStep nextNightStep = createNightStep(N_STEP.NS_KEEP_ROOM_STATUS, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_KEEP_ROOM_STATUS));

            HashMap<Object, Object> objectObjectHashMap = new HashMap<>();

            Map<Integer, NightStep> nightStepMap = new HashMap<>();

            nightStepMap.put(nextNightStep.getNightSettingId(), nextNightStep);
            objectObjectHashMap.put("nightData", nightStepMap);
            objectObjectHashMap.put("noFinish", nextNightStep);
            objectObjectHashMap.put("nightAuditRecord", nightAuditRecord);

            responseData.setData(objectObjectHashMap);

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * 创建夜审步骤
     *
     * @param settingId   夜审步骤对应的settingId
     * @param user        登录信息
     * @param settingName 夜审步骤对应的名称
     * @return
     * @throws Exception
     */
    public NightStep createNightStep(Integer settingId, TbUserSession user, String settingName) throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        NightStepSearch nightStepSearch = new NightStepSearch();
        nightStepSearch.setHid(user.getHid());
        nightStepSearch.setBusinessDay(user.getBusinessDay());
        nightStepSearch.setNightSettingId(settingId);

        List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);


        if (nightSteps.size() > 0) {
            return nightSteps.get(0);
        }

        NightStep nightStep = new NightStep();
        nightStep.setNightSettingId(settingId);
        nightStep.setNightSettingName(settingName);
        nightStep.setHid(user.getHid());
        nightStep.setHotelGroupId(user.getHotelGroupId());
        nightStep.setState(0);
        nightStep.setBeginTime(new Date());
        nightStep.setCreateUserId(user.getUserId());
        nightStep.setCreateUserName(user.getUserName());
        nightStep.setBusinessDay(user.getBusinessDay());
        Integer insert = nightStepDao.insert(nightStep);

        if (insert < 1) {
            throw new Exception(settingName + " 任务开始失败。");
        }

        // 4.成功后推送数据
        HotelUtils.cachedThreadPool.execute(new Runnable() {
            @Override
            public void run() {

                try {

                    HashMap<String, String> stringStringHashMap = new HashMap<>();
                    stringStringHashMap.put("nightName", settingName);
                    baseService.push(user.getHotelGroupId(), user.getHid(), 4, stringStringHashMap, new HashMap<String, String>(), true, true);

                } catch (Exception e) {
                    log.error("",e);
                }

            }
        });

        return nightStep;
    }

    /**
     * 保留当晚夜审房态
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData keepRoomStatus(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_KEEP_ROOM_STATUS);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            // 如果没查询出夜审记录 ，则重新创建记录
            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_KEEP_ROOM_STATUS, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_KEEP_ROOM_STATUS));
            }

            // 执行的时候查询当晚夜审前的记录房态
            NightAuditRoomStatusSearch nghtAuditRoomStatusSearch = new NightAuditRoomStatusSearch();
            nghtAuditRoomStatusSearch.setHid(user.getHid());
            nghtAuditRoomStatusSearch.setBusinessDay(user.getBusinessDay());

            // 需要删除的夜审订单
            List<NightAuditRoomStatus> deleteNightAuditRoomStatuses = nightAuditRoomStatusDao.selectBySearch(nghtAuditRoomStatusSearch);

            // 查询当前房态，并对房态进行保存，只保存非净房
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);

            // 查询所有的在住信息 已roomId进行分组
            RegistSearch registSearch = new RegistSearch();
            registSearch.setState(0);
            registSearch.setHid(user.getHid());

            List<Regist> regists = registDao.selectBySearch(registSearch);
            Map<Integer, Regist> registMap = regists.stream().collect(Collectors.toMap(Regist::getRoomNumId, a -> a, (k1, k2) -> k1));

            // 查询在住人信息，已registId进行分组
            HashMap<String, Object> personParam = new HashMap<>();
            personParam.put("hid", user.getHid());
            personParam.put("registState", 0);

            List<RegistPerson> registPeople = registPersonDao.searchCheckinPeople(personParam);
            Map<Integer, List<RegistPerson>> peopleMap = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));

            // 查询所有的修为停用记录
            RoomRepairRecordHistorySearch roomRepairRecordSearch = new RoomRepairRecordHistorySearch();
            roomRepairRecordSearch.setHid(user.getHid());
            roomRepairRecordSearch.setState(0);
            List<RoomRepairRecordHistory> roomRepairRecordHistories = roomRepairRecordHistoryDao.selectBySearch(roomRepairRecordSearch);

            Map<Integer, RoomRepairRecordHistory> recordHistoryMap = roomRepairRecordHistories.stream().collect(Collectors.toMap(RoomRepairRecordHistory::getRoomId, a -> a, (k1, k2) -> k2));


            // 添加的夜审房间
            ArrayList<NightAuditRoomStatus> addNightRoomStatus = new ArrayList<>();
            NightAuditRoomStatus nars = new NightAuditRoomStatus();

            // 改为住脏状态
            ArrayList<RoomInfo> roomInfosZz = new ArrayList<>();
            ArrayList<RoomRepairRecordHistory> roomRepairRecordHistoriesZz = new ArrayList<>();


            for (RoomInfo roomInfo : roomInfos) {

                Integer state = roomInfo.getRoomNumState();

                nars = new NightAuditRoomStatus();

                nars.setHid(user.getHid());
                nars.setHotelGroupId(user.getHotelGroupId());
                nars.setRoomId(roomInfo.getRoomInfoId());
                nars.setRoomNum(roomInfo.getRoomNum());
                nars.setRoomTypeId(roomInfo.getRoomTypeId());
                nars.setRoomTypeName(roomInfo.getRoomTypeName());
                nars.setBusinessDay(user.getBusinessDay());
                nars.setAuditYear(user.getBusinessYear());
                nars.setAuditYearMonth(user.getBusinessMonth());
                nars.setRoomState(state);

                // 停用
                if (roomInfo.getState() == 0) {
                    continue;
                }

                // 净房时跳出
                if (state == 1) {
                    continue;
                }

                if (state == 2) {
                    nars.setRoomState(5);
                }

                // 住净、住脏
                switch (state) {
                    case 3:
                    case 4:
                        Regist regist = registMap.get(roomInfo.getRoomInfoId());

                        if (regist == null) {
                            regist = new Regist();
                        }

                        if (regist.getBusinessDay().equals(user.getBusinessDay())) {
                            nars.setRegistState(1);
                        } else {
                            nars.setRegistState(2);
                        }

                        // 添加入住信息
                        nars.setRegistId(regist.getRegistId());
                        nars.setStartTime(regist.getCheckinTime());
                        nars.setEndTime(regist.getCheckoutTime());

                        // 添加入住人数
                        List<RegistPerson> people = peopleMap.get(regist.getRegistId());
                        if (people == null) {
                            people = new ArrayList<>();
                        }

                        String name = "";
                        for (RegistPerson person : people) {
                            name += person.getPersonName() + "、";
                        }
                        nars.setRoomState(1);
                        nars.setGuestName(name);
                        nars.setGuestNum(people.size());

                        if (state == 3) {
                            roomInfo.setRoomNumState(ROOM_STATUS.OD);
                            roomInfosZz.add(roomInfo);
                            RoomRepairRecordHistory roomRepairRecord = new RoomRepairRecordHistory();

                            roomRepairRecord.setRoomId(roomInfo.getRoomInfoId());
                            roomRepairRecord.setRoomNum(roomInfo.getRoomNum());
                            roomRepairRecord.setHid(roomInfo.getHid());
                            roomRepairRecord.setRegistId(regist.getRegistId());
                            roomRepairRecord.setHotelGroupId(roomInfo.getHotelGroupId());
                            roomRepairRecord.setClassId(user.getClassId());
                            roomRepairRecord.setCreateUserId(user.getUserId());
                            roomRepairRecord.setCreateUserName(user.getUserName());
                            roomRepairRecord.setCreateTime(new Date());
                            roomRepairRecord.setUpdateUserId(user.getUserId());
                            roomRepairRecord.setUpdateUserName(user.getUserName());
                            roomRepairRecord.setRoomTypeId(roomInfo.getRoomTypeId());
                            roomRepairRecord.setUpdateTime(new Date());
                            roomRepairRecord.setType(RoomUtils.getRoomRepairRecordType(ROOM_STATUS.OCC, ROOM_STATUS.OD));
                            roomRepairRecord.setState(RoomUtils.getRoomRepairRecordState(ROOM_STATUS.OCC, ROOM_STATUS.OD));
                            roomRepairRecord.setBusinessDay(user.getBusinessDay());
                            roomRepairRecord.setDes(roomInfo.getRoomNum() + "夜审后改为住脏。");

                            roomRepairRecordHistoriesZz.add(roomRepairRecord);
                        }

                        // 免费自用
                        Integer checkinType = regist.getCheckinType();
                        if (checkinType == 4 || checkinType == 5) {
                            nars.setRoomState(2);
                            addNightRoomStatus.add(nars);
                            continue;
                        }

                        break;
                    case 5:
                        RoomRepairRecordHistory roomRepairRecordHistory = recordHistoryMap.get(roomInfo.getRoomInfoId());
                        if (roomRepairRecordHistory == null) {
                            roomRepairRecordHistory = new RoomRepairRecordHistory();
                        }
                        nars.setRoomState(4);
                        nars.setEndTime(roomRepairRecordHistory.getEndtime());
                        nars.setStartTime(roomRepairRecordHistory.getBegintime());
                        nars.setGuestName(roomRepairRecordHistory.getDes());

                        break;
                }

                addNightRoomStatus.add(nars);

            }

            nightTransactionService.nightKeepRoomStatusTransaction(deleteNightAuditRoomStatuses, addNightRoomStatus, roomInfosZz, roomRepairRecordHistoriesZz);

            // 更改当前夜审状态
            nightStep.setState(1);
            nightStep.setEndTime(new Date());

            Integer update = nightStepDao.update(nightStep);

            // 创建新的夜审状态
            createNightStep(N_STEP.NS_NOSHOW, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_NOSHOW));


        } catch (Exception e) {

            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * 应到未到订单处理
     * <p>
     * 统一改为noshow状态
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData noShowBook(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            user.setClassId(HclassUtits.NIGHT_AUDIT);

            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_NOSHOW);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_NOSHOW, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_NOSHOW));
            }

            BookingOrderSearch bookingOrderSearch = new BookingOrderSearch();
            bookingOrderSearch.setHid(user.getHid());
            bookingOrderSearch.setCheckInEndTime(new Date());
            bookingOrderSearch.setOrderStatus(BOOK.STA_YX);
            List<BookingOrder> bookingOrders = bookingOrderDao.selectBySearch(bookingOrderSearch);
            bookingOrders = getNeedNoshowBook(bookingOrders);

            Map<Integer, BookingOrder> bookMap = bookingOrders.stream().collect(Collectors.toMap(BookingOrder::getBookingOrderId, a -> a, (k1, k2) -> k2));

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setHid(user.getHid());
            bookingOrderRoomNumSearch.setIsCheckin(0);
            bookingOrderRoomNumSearch.setOrderState(BOOK.STA_YX);

            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            Date date = new Date();

            Calendar instance = Calendar.getInstance();

            int hour = instance.get(Calendar.HOUR);
            if (hour >= 20) {
                date = HotelUtils.addDayGetNewDate(date, 1);
            }

            Integer nowInteDate = HotelUtils.parseDate2Int(date);

            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            String roomIds = "";

            Boolean roomIdsKey = false;

            ArrayList<BookingOrderRoomNum> updBookRooms = new ArrayList<>();

            ArrayList<BookingOrderRoomType> updRoomType = new ArrayList<>();

            for (BookingOrderRoomNum bkor : bookingOrderRoomNums) {

                roomIdsKey = true;

                roomIds += bkor.getRoomNumId() + ",";

                // 如果是取消的订单，则同时取消子订单
                BookingOrder bookingOrder = bookMap.get(bkor.getBookingOrderId());
                if(bookingOrder!=null){

                    BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
                    bookingOrderRoomTypeSearch.setHid(user.getHid());
                    bookingOrderRoomTypeSearch.setBookingOrderId(bkor.getBookingOrderId());
                    bookingOrderRoomTypeSearch.setOrderState(BOOK.STA_YX);
                    Page<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);
                    updRoomType.addAll(bookingOrderRoomTypes);

                    bkor.setOrderState(BOOK.STA_YQX);
                    bkor.setUpdateCalssId(user.getClassId());
                    bkor.setUpdateUserId(user.getUserId());
                    bkor.setUpdateTime(new Date());
                    bkor.setUpdateUserName(user.getUserName());
                    updBookRooms.add(bkor);
                }

            }

            if (roomIdsKey) {
                roomIds = roomIds.substring(0, roomIds.length() - 1);

                RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
                roomAuxiliaryRelationSearch.setHid(user.getHid());
                roomAuxiliaryRelationSearch.setRoomIds(roomIds);
                roomAuxiliaryRelationSearch.setRoomAuxiliaryId(ROOM_AUXILIARY.BOOK_CREATE);

                List<RoomAuxiliaryRelation> roomAuxiliaryRelations1 = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

                for (RoomAuxiliaryRelation ralr : roomAuxiliaryRelations1) {

                    ralr.setRoomAuxiliaryId(ROOM_AUXILIARY.BOOK_ARRIVALS);

                    roomAuxiliaryRelations.add(ralr);
                }

            }

            nightTransactionService.cancelOrderService(bookingOrders, user, roomAuxiliaryRelations,updBookRooms,updRoomType);

            // 如果没查询出夜审记录 ，则重新创建记录
            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            Integer update = nightStepDao.update(nightStep);

            // 创建新的夜审状态
            createNightStep(N_STEP.NS_CHECKOUT_OVERTIME, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_CHECKOUT_OVERTIME));


        } catch (Exception e) {

            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    private List<BookingOrder> getNeedNoshowBook(List<BookingOrder> bookingOrders) throws ParseException {
        List<BookingOrder> rt = new ArrayList<>(bookingOrders.size());
        for (BookingOrder bookingOrder : bookingOrders) {
            Date checkinTime = new Date();
            checkinTime.setTime(bookingOrder.getCheckinTime().getTime());

            String keepTime = bookingOrder.getKeepTime();
            String[] split = keepTime.split(":");
            if (split.length == 2) {
                int h = Integer.parseInt(split[0]);

                //次日
                if (h < 12 || h == 24) {
                    checkinTime.setTime(checkinTime.getTime() + 24 * 3600 * 1000);
                }
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                String checkinTimeText = dateFormat.format(checkinTime);

                if (h == 24) {
                    keepTime = "00:00";
                }

                keepTime = checkinTimeText.split(" ")[0] + " " + keepTime;
                bookingOrder.setKeepTime(keepTime);

                Date keepTimeDate = dateFormat.parse(keepTime);

                dateFormat = new SimpleDateFormat("MM-dd HH:mm");
                if (keepTimeDate.getTime() < System.currentTimeMillis()) {
                    rt.add(bookingOrder);
                    bookingOrder.setKeepTime(dateFormat.format(keepTimeDate));
                }
            }
        }
        return rt;
    }

    /**
     * 维修房到期处理
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData repairRoomAddDate(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            user.setClassId(HclassUtits.NIGHT_AUDIT);

            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_OUT_OF_REPAIR);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_OUT_OF_REPAIR, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_OUT_OF_REPAIR));
            }

            // 查询当前到期的维修房
            RoomRepairRecordHistorySearch roomRepairRecordHistorySearch = new RoomRepairRecordHistorySearch();
            roomRepairRecordHistorySearch.setHid(user.getHid());
            roomRepairRecordHistorySearch.setNewRoomState(ROOM_STATUS.OOO);
            roomRepairRecordHistorySearch.setMaxEndTime(new Date());
            roomRepairRecordHistorySearch.setState(0);
            List<RoomRepairRecordHistory> roomRepairRecordHistories = roomRepairRecordHistoryDao.selectBySearch(roomRepairRecordHistorySearch);

            ArrayList<Oprecord> oprecords = new ArrayList<>();

            Oprecord oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());

            Date date = new Date();

            int hours = date.getHours();

            if (hours > 12) {
                date = HotelUtils.addDayGetNewDate(date, 1);
            }

            for (RoomRepairRecordHistory rrrh : roomRepairRecordHistories) {

                rrrh.setEndtime(date);

                Integer update = roomRepairRecordHistoryDao.update(rrrh);

                oprecord = new Oprecord(user);
                oprecord.setRoomNum(rrrh.getRoomNum());
                oprecord.setBcodeO(rrrh.getRoomId() + "");
                oprecord.setBcodeT(rrrh.getRepairCheckRoomRecordId() + "");
                oprecord.setDescription("夜审时维修房到期延迟一天。");
                oprecords.add(oprecord);

            }

            addOprecords(oprecords);

            // 如果没查询出夜审记录 ，则重新创建记录
            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            Integer update = nightStepDao.update(nightStep);

            // 创建新的夜审状态
            createNightStep(N_STEP.NS_ADD_ACOUNT, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_ADD_ACOUNT));


        } catch (Exception e) {

            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * 夜审时应到未到全部挂账处理
     * <p>
     * 查询所有的应离未离订单，改为挂账
     * <p>
     * 删除所有的辅助房态
     * <p>
     * 将在住人改为挂账状态
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData nightOnAccount(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {


            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            user.setClassId(HclassUtits.NIGHT_AUDIT);

            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_CHECKOUT_OVERTIME);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_CHECKOUT_OVERTIME, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_CHECKOUT_OVERTIME));
            }

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setCheckinType(HC.DAY_ROOM);
            registSearch.setState(ET.STATUS_NOT_END);
            registSearch.setCheckoutTimeMax(new Date());

            List<Regist> regists = registDao.selectBySearch(registSearch);

            ArrayList<Regist> upaRegists = new ArrayList<>();

            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            ArrayList<RegistPerson> registPeople = new ArrayList<>();

            /**
             * 添加操作日志
             */
            ArrayList<Oprecord> oprecords = new ArrayList<>();


            Date date = new Date();

            Integer businessDay = user.getBusinessDay();

            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            Date parse = formatter.parse(businessDay + "");

            Calendar calendar = new GregorianCalendar();
            calendar.setTime(parse);
            calendar.add(calendar.DATE, 1);//把日期往后增加一天.整数往后推,负数往前移动

            String format = formatter.format(calendar.getTime());

            final Integer newBus = Integer.parseInt(format);

            /**
             * 所有未结房间进行挂账
             */
            for (Regist regist : regists) {

                Oprecord oprecord = new Oprecord(user);
                oprecord.setSourceValue(regist.getState() + "");
                regist.setUpdateCalssId(user.getClassId());
                regist.setUpdateTime(date);
                regist.setUpdateUserId(user.getUserId());
                regist.setSettleAccountTime(new Date());
                regist.setUpdateUserName(user.getUserName());
                regist.setCheckoutBusinessDay(newBus);
                regist.setState(ET.STATUS_LOSSES);
                upaRegists.add(regist);


                oprecord.setMainId(regist.getSn());
                oprecord.setRegistId(regist.getRegistId());
                oprecord.setOccurTime(HotelUtils.currentTime());
                oprecord.setRoomNum(regist.getRoomNum());
                oprecord.setDescription(regist.getRoomNum() + ":退房超时，夜审时改为挂账状态。");
                oprecord.setChangedValue(regist.getState() + "");
                oprecords.add(oprecord);

                // 辅助房态
                RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
                roomAuxiliaryRelationSearch.setHid(user.getHid());
                roomAuxiliaryRelationSearch.setRegistId(regist.getRegistId());
                List<RoomAuxiliaryRelation> roomAuxiliaryRelations1 = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

                roomAuxiliaryRelations.addAll(roomAuxiliaryRelations1);

                // 入住人信息
                RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                registPersonSearch.setHid(user.getHid());
                registPersonSearch.setRegistState(ET.STATUS_NOT_END);
                registPersonSearch.setRegistId(regist.getRegistId());

                List<RegistPerson> registPeople1 = registPersonDao.selectBySearch(registPersonSearch);

                registPeople.addAll(registPeople1);

            }

            nightTransactionService.nightOnAccountTransaction(upaRegists, roomAuxiliaryRelations, registPeople);

            // 更新可用房间
            this.turnAlways(user);

            this.addOprecords(oprecords);


            // 如果没查询出夜审记录 ，则重新创建记录
            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            Integer update = nightStepDao.update(nightStep);

            // 创建新的夜审状态
            createNightStep(N_STEP.NS_HOUR_ROOM, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_HOUR_ROOM));


        } catch (Exception e) {

            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    @Override
    public ResponseData nightHourOnAccount(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {


            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            user.setClassId(HclassUtits.NIGHT_AUDIT);

            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_HOUR_ROOM);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_HOUR_ROOM, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_HOUR_ROOM));
            }

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setCheckinType(HC.HOUR_ROOM);
            registSearch.setState(ET.STATUS_NOT_END);

            List<Regist> regists = registDao.selectBySearch(registSearch);

            ArrayList<Regist> upaRegists = new ArrayList<>();

            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            ArrayList<RegistPerson> registPeople = new ArrayList<>();

            /**
             * 添加操作日志
             */
            ArrayList<Oprecord> oprecords = new ArrayList<>();


            Date date = new Date();


            Integer businessDay = user.getBusinessDay();

            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            Date parse = formatter.parse(businessDay + "");

            Calendar calendar = new GregorianCalendar();
            calendar.setTime(parse);
            calendar.add(calendar.DATE, 1);//把日期往后增加一天.整数往后推,负数往前移动

            String format = formatter.format(calendar.getTime());

            final Integer newBus = Integer.parseInt(format);
            /**
             * 所有未结房间进行挂账
             */
            for (Regist regist : regists) {

                Oprecord oprecord = new Oprecord(user);
                oprecord.setSourceValue(regist.getState() + "");
                regist.setUpdateCalssId(user.getClassId());
                regist.setUpdateTime(date);
                regist.setUpdateUserId(user.getUserId());
                regist.setUpdateUserName(user.getUserName());
                regist.setState(ET.STATUS_LOSSES);
                regist.setCheckoutBusinessDay(newBus);
                upaRegists.add(regist);


                oprecord.setMainId(regist.getSn());
                oprecord.setRegistId(regist.getRegistId());
                oprecord.setOccurTime(HotelUtils.currentTime());
                oprecord.setRoomNum(regist.getRoomNum());
                oprecord.setDescription(regist.getRoomNum() + ":退房超时，夜审时改为挂账状态。");
                oprecord.setChangedValue(regist.getState() + "");
                oprecords.add(oprecord);

                // 辅助房态
                RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
                roomAuxiliaryRelationSearch.setHid(user.getHid());
                roomAuxiliaryRelationSearch.setRegistId(regist.getRegistId());
                List<RoomAuxiliaryRelation> roomAuxiliaryRelations1 = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

                roomAuxiliaryRelations.addAll(roomAuxiliaryRelations1);

                // 入住人信息
                RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                registPersonSearch.setHid(user.getHid());
                registPersonSearch.setRegistState(ET.STATUS_NOT_END);
                registPersonSearch.setRegistId(regist.getRegistId());

                List<RegistPerson> registPeople1 = registPersonDao.selectBySearch(registPersonSearch);

                registPeople.addAll(registPeople1);

            }

            //   nightTransactionService.nightOnAccountTransaction(upaRegists, roomAuxiliaryRelations, registPeople);

            // 更新可用房间
            //   this.turnAlways(user);

            //   this.addOprecords(oprecords);


            // 如果没查询出夜审记录 ，则重新创建记录
            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            Integer update = nightStepDao.update(nightStep);

            // 创建新的夜审状态
            createNightStep(N_STEP.NS_OUT_OF_REPAIR, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_OUT_OF_REPAIR));


        } catch (Exception e) {

            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * 夜审入账处理
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData addNightAccount(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);
            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_ADD_ACOUNT);

            String key = user.getHid() + "addNightAccount" + user.getBusinessDay();

            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();

            Object addNightAccount = userCahe.get("addNightAccount", key);
/*
            if (addNightAccount == null) {
                System.out.println("=====================未夜审++++" + key);
                userCahe.put("addNightAccount", key, "1");
            } else {
                int i = Integer.parseInt(addNightAccount.toString());
                if (i == 1) {
                    System.out.println("=====================已经夜审++++" + key);
                    return responseData;
                } else {
                    System.out.println("=====================未夜审1111++++" + key);
                    userCahe.put("addNightAccount", key, "1");
                }

            }*/

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_ADD_ACOUNT, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_ADD_ACOUNT));
            }

            // 进行账务处理
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setDailyTime(user.getBusinessDay());
            List<Map<String, Object>> maps = bookingOrderDailyPriceDao.selectNightPrice(bookingOrderDailyPriceSearch);

            ArrayList<Regist> regists = new ArrayList<>();

            // 添加账务的集合 、需要修改的房价表集合
            ArrayList<Account> accounts = new ArrayList<>();

            ArrayList<BookingOrderDailyPrice> bookingOrderDailyPrices = new ArrayList<>();

            HashMap<Integer, Boolean> regMap = new HashMap<>();


            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setState(0);
            List<Regist> registsZks = registDao.selectBySearch(registSearch);

            Boolean redCz = false;

            String ids = "";

            for (Map<String, Object> m : maps) {
                redCz = true;
                ids += m.get("regist_id").toString() + ",";
            }

            for (Regist regist : registsZks) {
                redCz = true;
                ids += regist.getRegistId().toString() + ",";
            }

            Map<Integer, List<RegistPerson>> registPeopleMap = new HashMap<>();

            Map<Integer, BookingOrderConfig> bookingOrderConfigsMap = new HashMap<>();

            // 查询入住人信息，查询订单配置信息
            if (redCz) {
                ids = ids.substring(0, ids.length() - 1);

                BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
                bookingOrderConfigSearch.setRegistIds(ids);
                List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);

                bookingOrderConfigsMap = bookingOrderConfigs.stream().collect(Collectors.toMap(BookingOrderConfig::getRegistId, a -> a, (k1, k2) -> k2));

                RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                registPersonSearch.setHid(user.getHid());
                registPersonSearch.setRegistIds(ids);
                registPersonSearch.setRegistState(0);

                List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);

                registPeopleMap = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));

            }

            for (Map<String, Object> m : maps) {

                int registId = Integer.parseInt(m.get("regist_id").toString());
                if (regMap.get(registId) != null && regMap.get(registId)) {
                    continue;
                }

                Account account = new Account();

                account.setHid(user.getHid());
                account.setHotelGroupId(user.getHotelGroupId());
                account.setCreateUserId(user.getUserId());
                account.setCreateUserName(user.getUserName());
                account.setCreateTime(new Date());
                account.setIsCancel(0);
                account.setAccountYear(user.getBusinessYear());
                account.setAccountYearMonth(user.getBusinessMonth());
                account.setBusinessDay(user.getBusinessDay());
                account.setClassId(user.getClassId());
                if (m.get("price") == null) {
                    continue;
                }
                account.setPrice(Integer.parseInt(m.get("price").toString()));
                account.setSettleAccountTime(new Date());
                account.setRegistState(0);
                //消费-付款
                account.setPayType(1);
                account.setSaleNum(1);
                account.setUintPrice(account.getPrice());
                account.setPayClassId(10);
                account.setPayClassName("客房");
                account.setPayCodeId("0001");
                account.setPayCodeName("夜审房费");
                account.setAccountType(1);
                account.setRemark("夜审产生房费");
                Object id = m.get("id");
                if (id != null) {
                    account.setThirdAccoutId(id.toString());
                }
                account.setRefundPrice(0);
                account.setRegistPersonId(0);
                account.setRegistId(registId);
                if (m.get("booking_order_id") != null && !"".equals(m.get("booking_order_id").toString())) {
                    account.setBookingId(Integer.parseInt(m.get("booking_order_id").toString()));
                }
                account.setRoomInfoId(Integer.parseInt(m.get("room_num_id").toString()));
                account.setRoomNum(m.get("room_num").toString());
                account.setRoomTypeId(Integer.parseInt(m.get("room_type_id").toString()));
                if (account.getPrice() == null) {
                    account.setPrice(0);
                }
                account.setGroupAccount(0);


                BookingOrderConfig bookingOrderConfig = bookingOrderConfigsMap.get(registId);
                List<RegistPerson> registPeople = registPeopleMap.get(registId);
                if (registPeople == null) {
                    registPeople = new ArrayList<>();
                }

                account.setBegRegistId(account.getRegistId());
                account.setGroupAccount(0);

                if (registPeople.size() > 0) {
                    RegistPerson registPerson = registPeople.get(0);
                    account.setRegistPersonId(registPerson.getRegistPersonId());
                    account.setRegistPersonName(registPerson.getPersonName());
                    account.setBegRegistPersonId(registPerson.getRegistPersonId());
                }

                // 平摊房费
                if (bookingOrderConfig != null && bookingOrderConfig.getAvePrice() != null && bookingOrderConfig.getAvePrice() == 1) {

                    // 均摊房费，当登记单大于1时生效
                    if (registPeople.size() > 1) {
                        int price = 0;
                        for (int rek = 0; rek < registPeople.size(); rek++) {
                            RegistPerson registPerson = registPeople.get(rek);
                            Account peoAccount = new Account();
                            BeanUtils.copyProperties(account, peoAccount);
                            peoAccount.setRegistPersonId(registPerson.getRegistPersonId());
                            peoAccount.setRegistPersonName(registPerson.getPersonName());
                            String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);

                            // 最后一位时
                            if (rek == registPeople.size() - 1) {
                                int i = account.getPrice() - price;
                                peoAccount.setPrice(i);
                                peoAccount.setUintPrice(i);
                            } else {
                                price += account.getPrice() / registPeople.size();
                                peoAccount.setPrice(account.getPrice() / registPeople.size());
                                peoAccount.setUintPrice(peoAccount.getPrice());
                            }


                            peoAccount.setAccountId(accountId);
                            accounts.add(peoAccount);

                        }
                    } else {
                        String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
                        account.setAccountId(accountId);
                        accounts.add(account);
                    }

                } else {
                    String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
                    account.setAccountId(accountId);
                    accounts.add(account);
                }

                // 每日房价
                BookingOrderDailyPrice bookingOrderDailyPrice = new BookingOrderDailyPrice();
                bookingOrderDailyPrice.setId(Integer.parseInt(m.get("id").toString()));
                bookingOrderDailyPrice.setDailyState(0);

                bookingOrderDailyPrices.add(bookingOrderDailyPrice);

                // 登记表
                Regist regist = registDao.selectById(account.getRegistId());
                regist.setSumSale(regist.getSumSale() + account.getPrice());
                regists.add(regist);

                regMap.put(registId, true);
            }

            // 查询当前营业日期，查询有没有生成夜审房费，已生成房费的不计算在内
            bookingOrderDailyPriceSearch.setDailyState(0);
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices3 = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            for (BookingOrderDailyPrice bkop : bookingOrderDailyPrices3) {
                regMap.put(bkop.getRegistId(), true);
            }

            ArrayList<BookingOrderDailyPrice> bookingOrderDailyPrices2 = new ArrayList<>();

            for (Regist regist : registsZks) {

                Integer registId = regist.getRegistId();

                if (regMap.get(registId) != null && regMap.get(registId)) {
                    continue;
                }

                BookingOrderDailyPriceSearch bodSearch = new BookingOrderDailyPriceSearch();
                bodSearch.setHid(user.getHid());
                bodSearch.setRegistId(registId);

                Page<BookingOrderDailyPrice> bookingOrderDailyPrices1 = bookingOrderDailyPriceDao.selectBySearch(bodSearch);

                if (bookingOrderDailyPrices1.size() < 1) {
                    continue;
                }

                BookingOrderDailyPrice bookingOrderDailyPrice = bookingOrderDailyPrices1.get(0);

                Account account = new Account();
                account.setHid(user.getHid());
                account.setHotelGroupId(user.getHotelGroupId());
                account.setCreateUserId(user.getUserId());
                account.setCreateUserName(user.getUserName());
                account.setCreateTime(new Date());
                account.setIsCancel(0);
                account.setAccountYear(user.getBusinessYear());
                account.setAccountYearMonth(user.getBusinessMonth());
                account.setBusinessDay(user.getBusinessDay());
                account.setClassId(user.getClassId());
                account.setPrice(bookingOrderDailyPrice.getPrice());
                account.setSettleAccountTime(new Date());
                account.setRegistState(0);
                //消费-付款
                account.setPayType(1);
                account.setSaleNum(1);
                account.setUintPrice(account.getPrice());
                account.setPayClassId(10);
                account.setPayClassName("客房");
                account.setPayCodeId("0001");
                account.setPayCodeName("夜审房费");
                account.setAccountType(1);
                account.setRemark("夜审产生房费");
                account.setRefundPrice(0);
                account.setRegistPersonId(0);
                account.setRegistId(registId);
                account.setBookingId(regist.getBookingOrderId());
                account.setRoomTypeId(regist.getRoomTypeId());
                account.setRoomNum(regist.getRoomNum());
                account.setRoomInfoId(regist.getRoomNumId());

//                RegistPersonSearch registPersonSearch = new RegistPersonSearch();
//                registPersonSearch.setRegistId(account.getRegistId());
//                registPersonSearch.setRegistState(0);
//                registPersonSearch.setIsOther(0);
//                List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
//                if(registPeople!=null&&registPeople.size()>0){
//                    RegistPerson registPerson = registPeople.get(0);
//                    account.setRegistPersonName(registPerson.getPersonName());
//                    account.setRegistPersonId(registPerson.getRegistPersonId());
//                }

                BookingOrderConfig bookingOrderConfig = bookingOrderConfigsMap.get(registId);
                List<RegistPerson> registPeople = registPeopleMap.get(registId);
                if (registPeople == null) {
                    registPeople = new ArrayList<>();
                }

                if (registPeople.size() > 0) {
                    RegistPerson registPerson = registPeople.get(0);
                    account.setRegistPersonId(registPerson.getRegistPersonId());
                    account.setRegistPersonName(registPerson.getPersonName());
                }
                account.setGroupAccount(0);
                // 平摊房费
                if (bookingOrderConfig != null && null != bookingOrderConfig.getAvePrice() && 1 == bookingOrderConfig.getAvePrice()) {

                    // 均摊房费，当登记单大于1时生效
                    if (registPeople.size() > 1) {
                        int price = 0;
                        for (int rek = 0; rek < registPeople.size(); rek++) {
                            RegistPerson registPerson = registPeople.get(rek);
                            Account peoAccount = new Account();
                            BeanUtils.copyProperties(account, peoAccount);
                            peoAccount.setRegistPersonId(registPerson.getRegistPersonId());
                            peoAccount.setRegistPersonName(registPerson.getPersonName());
                            String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);

                            // 最后一位时
                            if (rek == registPeople.size() - 1) {
                                int i = account.getPrice() - price;
                                peoAccount.setPrice(i);
                                peoAccount.setUintPrice(i);
                            } else {
                                price += account.getPrice() / registPeople.size();
                                peoAccount.setPrice(account.getPrice() / registPeople.size());
                                peoAccount.setUintPrice(peoAccount.getPrice());
                            }


                            peoAccount.setAccountId(accountId);
                            accounts.add(peoAccount);

                        }
                    } else {
                        String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
                        account.setAccountId(accountId);
                        accounts.add(account);
                    }

                } else {
                    String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
                    account.setAccountId(accountId);
                    accounts.add(account);
                }

                // 每日房价
                BookingOrderDailyPrice bodpadd = new BookingOrderDailyPrice();
                BeanUtils.copyProperties(bookingOrderDailyPrice, bodpadd);
                bodpadd.setId(null);
                bodpadd.setDailyTime(user.getBusinessDay());
                bodpadd.setDailyState(0);
                bookingOrderDailyPrices2.add(bodpadd);
                Regist registInfo = Regist.CreateRegist(registId);
                // 登记表
                if (regist.getSumSale() == null) {
                    regist.setSumSale(0);
                    registInfo.setSumSale(0);
                }
                if (account.getPrice() == null) {
                    account.setPrice(0);
                }
                // regist.setSumSale(regist.getSumSale() + account.getPrice());
                registInfo.setSumSale(regist.getSumSale() + account.getPrice());
                regists.add(registInfo);
            }

            nightTransactionService.nightAddAcountHandleTransaction(accounts, bookingOrderDailyPrices, user, regists, bookingOrderDailyPrices2);

            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            Integer update = nightStepDao.update(nightStep);

            // 创建新的夜审状态
            createNightStep(N_STEP.NS_ROOM_REVENUE, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_ROOM_REVENUE));


        } catch (Exception e) {

            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }


    /**
     * 房费营收统计
     * <p>
     * 统计当天房费的收入情况,只统计房费
     *
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData statisticsRoomAccountData(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);

            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_ROOM_REVENUE);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_ROOM_REVENUE, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_ROOM_REVENUE));
            }

            // 1.查询账务信息
            AccountSummarySearch accountSummary = new AccountSummarySearch();
            accountSummary.setHid(user.getHid());
            // accountSummary.setAccountType(1);
            accountSummary.setPayType(1);
            accountSummary.setBusinessDay(user.getBusinessDay());
            accountSummary.setGroupType(1);
            // 当日汇总
            Map<String, AccountSummary> accountSummariesDay = accountDao.accountSummary(accountSummary).stream().collect(Collectors.toMap(AccountSummary::getPayCodeId, a -> a, (k1, k2) -> k2));

            accountSummary.setBusinessDay(null);
            accountSummary.setAccountYearMonth(user.getBusinessMonth());
            // 月汇总
            Map<String, AccountSummary> accountSummariesMonth = accountDao.accountSummary(accountSummary).stream().collect(Collectors.toMap(AccountSummary::getPayCodeId, a -> a, (k1, k2) -> k2));
            ;

            accountSummary.setAccountYearMonth(null);
            accountSummary.setAccountYear(null);

            // 年汇总
            Map<String, AccountSummary> accountSummariesYear = accountDao.accountSummary(accountSummary).stream().collect(Collectors.toMap(AccountSummary::getPayCodeId, a -> a, (k1, k2) -> k2));
            ;

            // 2.查询时房费的费用码
            HotelCostCodeSearch hotelCostCodeSearch = new HotelCostCodeSearch();
            hotelCostCodeSearch.setParentId(10);
            hotelCostCodeSearch.setCostType(1);
            List<HotelCostCode> hotelCostCodes = hotelCostCodeDao.selectBySearch(hotelCostCodeSearch);

            // 3.针对付款码信息进行入账
            for (HotelCostCode costCode : hotelCostCodes) {

                NightAuditDailyRegistRecord nightAuditDailyRegistRecord = new NightAuditDailyRegistRecord();
                nightAuditDailyRegistRecord.setHid(user.getHid());
                nightAuditDailyRegistRecord.setHotelGroupId(user.getHotelGroupId());

                AccountSummary accountSummaryDay = accountSummariesDay.get(costCode.getCostNameEn());
                AccountSummary accountSummaryMonth = accountSummariesMonth.get(costCode.getCostNameEn());
                AccountSummary accountSummaryYear = accountSummariesYear.get(costCode.getCostNameEn());

                nightAuditDailyRegistRecord.setPayClassId("0001");
                nightAuditDailyRegistRecord.setPayClassName("房费");

                nightAuditDailyRegistRecord.setPayCodeId(costCode.getCostNameEn());
                nightAuditDailyRegistRecord.setPayCodeName(costCode.getCostName());

                nightAuditDailyRegistRecord.setBusinessDay(user.getBusinessDay());
                nightAuditDailyRegistRecord.setAuditYearMonth(user.getBusinessMonth());
                nightAuditDailyRegistRecord.setAuditYear(user.getBusinessYear());

                if (accountSummaryDay != null) {
                    nightAuditDailyRegistRecord.setTotalAmount(accountSummaryDay.getSumMoney());
                } else {
                    nightAuditDailyRegistRecord.setTotalAmount(0);
                }

                if (accountSummaryMonth != null) {
                    nightAuditDailyRegistRecord.setTotalMonthAmount(accountSummaryMonth.getSumMoney());
                } else {
                    nightAuditDailyRegistRecord.setTotalMonthAmount(0);
                }

                if (accountSummaryYear != null) {
                    nightAuditDailyRegistRecord.setTotalYearAmount(accountSummaryYear.getSumMoney());
                } else {
                    nightAuditDailyRegistRecord.setTotalYearAmount(0);
                }

                auditDailyRegistRecordDao.insert(nightAuditDailyRegistRecord);

            }

            // 如果没查询出夜审记录 ，则重新创建记录
            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            Integer update = nightStepDao.update(nightStep);
            // 创建新的夜审状态
            createNightStep(N_STEP.NS_RESALE, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_RESALE));


        } catch (Exception e) {

            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }


    /**
     * 统计当天的小商品售卖情况
     * <p>
     * 包含
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData statisticsGoodsData(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);

            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_RESALE);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_RESALE, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_RESALE));
            }


            // 1.查询 进货、销售明细

            GoodsStockDetailSearch goodsStockDetailSearch = new GoodsStockDetailSearch();
            goodsStockDetailSearch.setHid(user.getHid());
            goodsStockDetailSearch.setBusinessDay(user.getBusinessDay());
            goodsStockDetailSearch.setType(1);  // 销售
            List<GoodsSummaryData> goodsSummaryDatas = goodsStockDetailDao.goodsSummary(goodsStockDetailSearch);


            NightAuditGoods nightAuditGoods = new NightAuditGoods();

            for (GoodsSummaryData goodsSummaryData : goodsSummaryDatas) {

                nightAuditGoods = new NightAuditGoods();
                nightAuditGoods.setId(null);
                nightAuditGoods.setHid(user.getHid());
                nightAuditGoods.setHotelGroupId(user.getHotelGroupId());
                nightAuditGoods.setAuditYear(user.getBusinessYear());
                nightAuditGoods.setAuditYearMonth(user.getBusinessMonth());
                nightAuditGoods.setBusinessDay(user.getBusinessDay());

                nightAuditGoods.setAmount(goodsSummaryData.getSumAmount());
                nightAuditGoods.setMoney(nightAuditGoods.getMoney());

                nightAuditGoods.setGoodsClassId(nightAuditGoods.getGoodsClassId());
                nightAuditGoods.setGoodsClassName(nightAuditGoods.getGoodsClassName());
                nightAuditGoods.setGoodsInfoId(nightAuditGoods.getGoodsInfoId());
                nightAuditGoods.setGoodsInfoName(nightAuditGoods.getGoodsClassName());
                nightAuditGoods.setType(1);

                nightAuditGoodsDao.insert(nightAuditGoods);
            }

            goodsStockDetailSearch.setType(0);
            List<GoodsSummaryData> goodsSummaryDatasIn = goodsStockDetailDao.goodsSummary(goodsStockDetailSearch);


            for (GoodsSummaryData goodsSummaryData : goodsSummaryDatasIn) {

                nightAuditGoods = new NightAuditGoods();
                nightAuditGoods.setId(null);
                nightAuditGoods.setHid(user.getHid());
                nightAuditGoods.setHotelGroupId(user.getHotelGroupId());
                nightAuditGoods.setAuditYear(user.getBusinessYear());
                nightAuditGoods.setAuditYearMonth(user.getBusinessMonth());
                nightAuditGoods.setBusinessDay(user.getBusinessDay());

                nightAuditGoods.setAmount(goodsSummaryData.getSumAmount());
                nightAuditGoods.setMoney(nightAuditGoods.getMoney());

                nightAuditGoods.setGoodsClassId(nightAuditGoods.getGoodsClassId());
                nightAuditGoods.setGoodsClassName(nightAuditGoods.getGoodsClassName());
                nightAuditGoods.setGoodsInfoId(nightAuditGoods.getGoodsInfoId());
                nightAuditGoods.setGoodsInfoName(nightAuditGoods.getGoodsClassName());
                nightAuditGoods.setType(0);

                nightAuditGoodsDao.insert(nightAuditGoods);
            }
            // 如果没查询出夜审记录 ，则重新创建记录
            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            Integer update = nightStepDao.update(nightStep);
            // 创建新的夜审状态
            createNightStep(N_STEP.NS_CAPITAL_FLOW, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_CAPITAL_FLOW));


        } catch (Exception e) {


            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * 生成资金流水
     * <p>
     * 1. 先查询付款大类和小类的资金明细
     * <p>
     * 2. 查询第三方支付表查看 预授权和会员冻结的消费记录
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData statisticsPayMsg(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);

            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_CAPITAL_FLOW);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_CAPITAL_FLOW, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_CAPITAL_FLOW));
            }

            this.nightBalance(param);

            /**
             * 1.查询 付款的明细，除预授权
             */

            // 查询费用码

            AccountSummarySearch accountSummarySearch = new AccountSummarySearch();
            accountSummarySearch.setHid(user.getHid());
            accountSummarySearch.setBusinessDay(user.getBusinessDay());
            accountSummarySearch.setNoPre(1);
            accountSummarySearch.setPayType(2);
            accountSummarySearch.setGroupType(1);

            // 资金流水明细 根据小类分组
            List<AccountSummary> accountSummaries = accountDao.accountSummary(accountSummarySearch);

            // 根据小类大类分组
            accountSummarySearch.setGroupType(2);
            List<AccountSummary> accountSummariesBig = accountDao.accountSummary(accountSummarySearch);

            // 需要添加的支付记录
            ArrayList<NightAuditPay> nightAuditPays = new ArrayList<>();

            NightAuditPay nightAuditPay = new NightAuditPay();

            // 大类组装
            for (AccountSummary accountSummary : accountSummariesBig) {
                nightAuditPay = new NightAuditPay();

                nightAuditPay.setPayClassId(accountSummary.getPayClassId());
                nightAuditPay.setPayClassName(accountSummary.getPayClassName());
                nightAuditPay.setType(1);
                nightAuditPay.setTotalAmount(accountSummary.getSumMoney());
                nightAuditPay.setHid(user.getHid());
                nightAuditPay.setHotelGroupId(user.getHotelGroupId());
                nightAuditPay.setBusinessDay(user.getBusinessDay());
                nightAuditPay.setAuditYearMonth(user.getBusinessMonth());
                nightAuditPay.setAuditYear(user.getBusinessYear());
                nightAuditPays.add(nightAuditPay);
            }

            // 小类组装
            for (AccountSummary accountSummary : accountSummaries) {
                nightAuditPay = new NightAuditPay();

                nightAuditPay.setPayClassId(accountSummary.getPayClassId());
                nightAuditPay.setPayClassName(accountSummary.getPayClassName());
                nightAuditPay.setType(2);
                nightAuditPay.setPayCodeId(accountSummary.getPayCodeId());
                nightAuditPay.setPayCodeName(accountSummary.getPayCodeName());
                nightAuditPay.setTotalAmount(accountSummary.getSumMoney());
                nightAuditPay.setHid(user.getHid());
                nightAuditPay.setHotelGroupId(user.getHotelGroupId());
                nightAuditPay.setBusinessDay(user.getBusinessDay());
                nightAuditPay.setAuditYearMonth(user.getBusinessMonth());
                nightAuditPay.setAuditYear(user.getBusinessYear());
                nightAuditPays.add(nightAuditPay);
            }

            /**
             * 2.查询预授权明细
             */
            AccountThirdSummarySearch accountThirdSummarySearch = new AccountThirdSummarySearch();
            accountThirdSummarySearch.setHid(user.getHid());
            accountThirdSummarySearch.setBusinessDay(user.getBusinessDay());
            accountThirdSummarySearch.setThirdState(1);
            List<AccountThirdSummary> accountThirdSummaries = accountThirdPayRecodeDao.accountThirdSummary(accountThirdSummarySearch);

            // 当天支付
            Map<Integer, AccountThirdSummary> dayPay = accountThirdSummaries.stream().collect(Collectors.toMap(AccountThirdSummary::getPayType, a -> a, (k1, k2) -> k1));
            accountThirdSummarySearch = new AccountThirdSummarySearch();
            accountThirdSummarySearch.setHid(user.getHid());
            accountThirdSummarySearch.setThirdState(1);
            accountThirdSummarySearch.setThirdRefundState(2);
            accountThirdSummarySearch.setFinishBusinessDay(user.getBusinessDay());
            // 当日完成
            List<AccountThirdSummary> accountThirdSummaries1 = accountThirdPayRecodeDao.accountThirdSummary(accountThirdSummarySearch);
            Map<Integer, AccountThirdSummary> dayFinish = accountThirdSummaries1.stream().collect(Collectors.toMap(AccountThirdSummary::getPayType, a -> a, (k1, k2) -> k1));

            // 银行卡
            ArrayList<NightAuditAutopre> nightAuditAutopres = new ArrayList<>();

            NightAuditAutopre bankcard = new NightAuditAutopre();
            bankcard.setHid(user.getHid());
            bankcard.setHotelGroupId(user.getHotelGroupId());
            bankcard.setBusinessDay(user.getBusinessDay());
            bankcard.setAuditYearMonth(user.getBusinessMonth());
            bankcard.setAuditYear(user.getBusinessYear());
            bankcard.setType(1);

            // 今日新增金额
            AccountThirdSummary bank = dayPay.get(3);
            if (bank == null) {
                bankcard.setMoney(0);
            } else {
                bankcard.setMoney(bank.getSumMoney());
            }

            // 今日完成金额
            AccountThirdSummary bankFinish = dayFinish.get(3);
            if (bankFinish == null) {
                bankcard.setFinishMoney(0);
            } else {
                bankcard.setFinishMoney(bankFinish.getRefund());
            }

            nightAuditAutopres.add(bankcard);

            // 会员冻结
            NightAuditAutopre vip = new NightAuditAutopre();
            vip.setHid(user.getHid());
            vip.setHotelGroupId(user.getHotelGroupId());
            vip.setBusinessDay(user.getBusinessDay());
            vip.setAuditYearMonth(user.getBusinessMonth());
            vip.setAuditYear(user.getBusinessYear());
            vip.setType(2);

            AccountThirdSummary vipMoney = dayPay.get(5);
            if (vipMoney == null) {
                vip.setMoney(0);
            } else {
                vip.setMoney(vipMoney.getSumMoney());
            }

            AccountThirdSummary vipFinish = dayFinish.get(5);
            if (vipFinish == null) {
                vip.setFinishMoney(0);
            } else {
                vip.setFinishMoney(vipFinish.getRefund());
            }

            nightAuditAutopres.add(vip);

            nightTransactionService.nightCapitalFlow(nightAuditPays, nightAuditAutopres);
            // 如果没查询出夜审记录 ，则重新创建记录
            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            Integer update = nightStepDao.update(nightStep);
            // 创建新的夜审状态
            createNightStep(N_STEP.NS_ROOM_TYPE, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_ROOM_TYPE));


        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    @Override
    public ResponseData nightBalance(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);
            Date date = HotelUtils.parseInt2Date(user.getBusinessDay());

            Date date1 = HotelUtils.addDayGetNewDate(date, -1);

            Integer lastBuss = HotelUtils.parseDate2Int(date1);

            NightAuditBalanceSearch nightAuditBalanceSearch = new NightAuditBalanceSearch();
            nightAuditBalanceSearch.setHid(user.getHid());
            nightAuditBalanceSearch.setBusinessDay(lastBuss);

            Page<NightAuditBalance> nightAuditBalances = nightAuditBalanceDao.selectBySearch(nightAuditBalanceSearch);

            // 则说明夜审前没数据，增加数据
            if (nightAuditBalances == null || nightAuditBalances.size() < 1) {

                nightAuditBalances = new Page<NightAuditBalance>();

                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setHid(user.getHid());
                accountSearch.setRegistState(0);
                accountSearch.setIsCancel(0);
                accountSearch.setBusinessDayEnd(lastBuss);

                Integer money = 0;

                List<Account> accounts = accountDao.selectBySearch(accountSearch);

                for (Account account : accounts) {
                    Integer accountType = account.getPayType();
                    if (accountType == 1) {
                        money += account.getPrice();
                    } else {

                        //如果是预授权，则不统计
                        if ((account.getPayCodeId().equals("9100") || account.getPayCodeId().equals("9620")) && !account.getThirdRefundState().equals(2)) {
                            continue;
                        }

                        money -= account.getPrice();
                    }
                }

                NightAuditBalance nightAuditBalance = new NightAuditBalance();
                nightAuditBalance.setHid(user.getHid());
                nightAuditBalance.setHotelGroupId(user.getHotelGroupId());
                nightAuditBalance.setAuditYearMonth(user.getBusinessMonth());
                nightAuditBalance.setAuditYear(user.getBusinessYear());
                nightAuditBalance.setBusinessDay(lastBuss);
                nightAuditBalance.setArMoney(0);
                nightAuditBalance.setVipMoney(0);
                nightAuditBalance.setMoney(money);
                nightAuditBalanceDao.insert(nightAuditBalance);

                nightAuditBalances.add(nightAuditBalance);
            }

            NightAuditBalance nightAuditBalance1 = nightAuditBalances.get(0);

            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setIsCancel(0);
            accountSearch.setBusinessDay(user.getBusinessDay());

            Integer money = 0;

            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            for (Account account : accounts) {
                Integer accountType = account.getPayType();

                if (accountType == 1) {
                    money += account.getPrice();
                } else {
                    //如果是预授权，则不统计
                    if ((account.getPayCodeId().equals("9100") || account.getPayCodeId().equals("9620")) && !account.getThirdRefundState().equals(2)) {
                        continue;
                    }
                    money -= account.getPrice();
                }
            }
            NightAuditBalance nightAuditBalance = new NightAuditBalance();
            nightAuditBalance.setHid(user.getHid());
            nightAuditBalance.setHotelGroupId(user.getHotelGroupId());
            nightAuditBalance.setAuditYearMonth(user.getBusinessMonth());
            nightAuditBalance.setAuditYear(user.getBusinessYear());
            nightAuditBalance.setBusinessDay(user.getBusinessDay());
            nightAuditBalance.setArMoney(0);
            nightAuditBalance.setVipMoney(0);
            nightAuditBalance.setMoney(money + nightAuditBalance1.getMoney());
            nightAuditBalanceDao.insert(nightAuditBalance);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * 统计房型营收数据
     * <p>
     * 1. 房型的总开房数
     * <p>
     * 2. 房型的总消费
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData statisticsRoomTypeData(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);
            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_ROOM_TYPE);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_ROOM_TYPE, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_ROOM_TYPE));
            }

            // 1.查询所有房型、房间
            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setHid(user.getHid());
            roomTypeSearch.setState(1);
            List<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);

            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            List<RoomInfoNum> roomNumByRoomType = roomInfoDao.findRoomNumByRoomType(roomInfoSearch);
            Map<Integer, RoomInfoNum> roomNumMap = roomNumByRoomType.stream().collect(Collectors.toMap(RoomInfoNum::getRoomTypeId, a -> a, (k1, k2) -> k1));


            // 查询当日的每日房价信息
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setDailyTime(user.getBusinessDay());
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            List<NightRoomAveragePrice> nightRoomAveragePrices = bookingOrderDailyPriceDao.selectAveragePrice(bookingOrderDailyPriceSearch);

            Map<Integer, NightRoomAveragePrice> nightRoomAveragePriceMap = nightRoomAveragePrices.stream().collect(Collectors.toMap(NightRoomAveragePrice::getRoomTypeId, a -> a, (k1, k2) -> k1));


            // 2.查询当前房间的除净房、脏房之外的房态
            NightAuditRoomStatusSearch nightAuditRoomStatusSearch = new NightAuditRoomStatusSearch();
            nightAuditRoomStatusSearch.setHid(user.getHid());
            nightAuditRoomStatusSearch.setBusinessDay(user.getBusinessDay());
            List<NightAuditRoomStatus> nightAuditRoomStatuses = nightAuditRoomStatusDao.selectBySearch(nightAuditRoomStatusSearch);

            Map<Integer, List<NightAuditRoomStatus>> nightAuditRoomMap = nightAuditRoomStatuses.stream().collect(Collectors.groupingBy(NightAuditRoomStatus::getRoomTypeId));

            // 查询当天在住且当天退房的信息
            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setCheckoutBusinessDay(user.getBusinessDay());
            registSearch.setState(1);
            registSearch.setBusinessDay(user.getBusinessDay());
            List<Regist> regists = registDao.selectBySearch(registSearch);
            Map<Integer, List<Regist>> dayRegistMap = regists.stream().collect(Collectors.groupingBy(Regist::getRoomTypeId));


            // 3.查询当日房间消费
            AccountSummarySearch accountSummarySearch = new AccountSummarySearch();
            accountSummarySearch.setHid(user.getHid());
            accountSummarySearch.setPayType(1);
            accountSummarySearch.setBusinessDay(user.getBusinessDay());
            accountSummarySearch.setGroupType(3);

            // 房费
            accountSummarySearch.setAccountType(1);
            Map<Integer, AccountSummary> accountRoomSummaries = accountDao.accountSummary(accountSummarySearch).stream().collect(Collectors.toMap(AccountSummary::getRoomTypeId, a -> a, (k1, k2) -> k1));

            // 商品费
            accountSummarySearch.setAccountType(2);
            Map<Integer, AccountSummary> accountGoodsSummaries = accountDao.accountSummary(accountSummarySearch).stream().collect(Collectors.toMap(AccountSummary::getRoomTypeId, a -> a, (k1, k2) -> k1));

            // 餐费
            accountSummarySearch.setAccountType(3);
            Map<Integer, AccountSummary> accountFoodsSummaries = accountDao.accountSummary(accountSummarySearch).stream().collect(Collectors.toMap(AccountSummary::getRoomTypeId, a -> a, (k1, k2) -> k1));

            // 4.组装夜审数据，开房和账务数据
            ArrayList<NightAuditRoomType> nightAuditRoomTypes = new ArrayList<>();

            NightAuditRoomType nightAuditRoomType = new NightAuditRoomType();

            // 遍历房型数据、添加房型数据
            for (RoomType rt : roomTypes) {

                nightAuditRoomType = new NightAuditRoomType();
                nightAuditRoomType.setHid(user.getHid());
                nightAuditRoomType.setHotelGroupId(user.getHotelGroupId());
                nightAuditRoomType.setBusinessDay(user.getBusinessDay());
                nightAuditRoomType.setAuditYearMonth(user.getBusinessMonth());
                nightAuditRoomType.setAuditYear(user.getBusinessYear());
                nightAuditRoomType.setRoomTypeId(rt.getRoomTypeId());
                nightAuditRoomType.setRoomTypeName(rt.getRoomTypeName());

                // 总房间、开房、维修数等
                RoomInfoNum roomInfoNum = roomNumMap.get(rt.getRoomTypeId());
                if (roomInfoNum == null) {
                    roomInfoNum = new RoomInfoNum();
                }
                // 总房数
                nightAuditRoomType.setTotalRoomCount(roomInfoNum.getSumCount());

                // 房间状态集合,如果当前房型下没有特殊房态，说明入住维修数都是0 房间状态 1.入住 2.自用 3.停用 4.维修
                List<NightAuditRoomStatus> rtRoomStatus = nightAuditRoomMap.get(rt.getRoomTypeId());

                if (rtRoomStatus == null || rtRoomStatus.size() < 1) {

                    nightAuditRoomType.setOpenRoomCount(0);
                    nightAuditRoomType.setRepairRoomCount(0);
                    nightAuditRoomType.setNoserviceRoomCount(0);
                    nightAuditRoomType.setSelfRoomCount(0);

                } else {

                    Map<Integer, List<NightAuditRoomStatus>> collect = rtRoomStatus.stream().collect(Collectors.groupingBy(NightAuditRoomStatus::getRoomState));

                    // 状态1 在住
                    List<NightAuditRoomStatus> nightAuditRoomStatuses1 = collect.get(1);
                    if (nightAuditRoomStatuses1 == null) {
                        nightAuditRoomType.setOpenRoomCount(0);
                    } else {
                        nightAuditRoomType.setOpenRoomCount(nightAuditRoomStatuses1.size());
                    }

                    // 状态2 自用
                    List<NightAuditRoomStatus> nightAuditRoomStatuses2 = collect.get(2);
                    if (nightAuditRoomStatuses2 == null) {
                        nightAuditRoomType.setSelfRoomCount(0);
                    } else {
                        nightAuditRoomType.setSelfRoomCount(nightAuditRoomStatuses2.size());
                    }


                    List<Regist> regists1 = dayRegistMap.get(rt.getRoomTypeId());
                    if (regists1 != null) {
                        for (Regist rs : regists1) {
                            if (rs.getCheckinType() == 4 || rs.getCheckinType() == 5) {
                                nightAuditRoomType.setSelfRoomCount(nightAuditRoomType.getSelfRoomCount() + 1);
                                continue;
                            }
                            nightAuditRoomType.setOpenRoomCount(nightAuditRoomType.getOpenRoomCount() + 1);
                        }
                    }

                    // 状态4 维修房
                    List<NightAuditRoomStatus> nightAuditRoomStatuses4 = collect.get(4);
                    if (nightAuditRoomStatuses4 == null) {
                        nightAuditRoomType.setRepairRoomCount(0);
                    } else {
                        nightAuditRoomType.setRepairRoomCount(nightAuditRoomStatuses4.size());
                    }

                    // 状态3 停用
                    List<NightAuditRoomStatus> nightAuditRoomStatuses3 = collect.get(3);
                    if (nightAuditRoomStatuses3 == null) {
                        nightAuditRoomType.setNoserviceRoomCount(0);
                    } else {
                        // nightAuditRoomType.setNoserviceRoomCount(nightAuditRoomStatuses3.size());
                        nightAuditRoomType.setTotalRoomCount(nightAuditRoomType.getTotalRoomCount() - nightAuditRoomStatuses3.size());
                    }


                }

                // 开房率 开房数/(房间总数-维修房-自用-停用)
                Integer openRoomCount = nightAuditRoomType.getOpenRoomCount();
                if (openRoomCount == 0 || nightAuditRoomType.getTotalRoomCount() == 0) {
                    nightAuditRoomType.setOpenRate(0.0);
                } else {
                    Double orc = nightAuditRoomType.getOpenRoomCount() * 100.0;
                    // 可用数
                    int canNum = nightAuditRoomType.getTotalRoomCount() - nightAuditRoomType.getRepairRoomCount() - nightAuditRoomType.getNoserviceRoomCount();
                    // 开房率
                    if (canNum == 0) {
                        nightAuditRoomType.setOpenRate(0.0);
                    } else {
                        double v = new Double(orc / canNum).doubleValue();
                        nightAuditRoomType.setOpenRate(v);
                    }


                }

                // 平均房价
                NightRoomAveragePrice nightRoomAveragePrice = nightRoomAveragePriceMap.get(rt.getRoomTypeId());
                if (nightRoomAveragePrice == null || nightRoomAveragePrice.getPrice() == 0 || nightRoomAveragePrice.getSumCount() == 0) {
                    nightAuditRoomType.setAveragePrice(0);
                } else {
                    nightAuditRoomType.setAveragePrice(nightRoomAveragePrice.getPrice() / nightRoomAveragePrice.getSumCount());
                }

                // 费用  房费、商品、食品
                AccountSummary roomSummary = accountRoomSummaries.get(rt.getRoomTypeId());
                nightAuditRoomType.setTotalAmount(roomSummary == null ? 0 : roomSummary.getSumMoney());
                AccountSummary goodSummary = accountGoodsSummaries.get(rt.getRoomTypeId());
                nightAuditRoomType.setGoodsMoney(goodSummary == null ? 0 : goodSummary.getSumMoney());
                AccountSummary foodSummary = accountFoodsSummaries.get(rt.getRoomTypeId());
                nightAuditRoomType.setFoodsMoney(foodSummary == null ? 0 : foodSummary.getSumMoney());

                nightAuditRoomTypes.add(nightAuditRoomType);

            }

            nightTransactionService.nightAuditRoomType(nightAuditRoomTypes);

            responseData.setData(nightAuditRoomTypes);

            // 如果没查询出夜审记录 ，则重新创建记录
            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            Integer update = nightStepDao.update(nightStep);
            // 创建新的夜审状态
            createNightStep(N_STEP.NS_RESOURCE, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_RESOURCE));


        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * a
     * 统计客源开房数
     * <p>
     * 1、客源开发数
     * <p>
     * 2、客源总收入占比
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData statisticsResouceData(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);
            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_RESOURCE);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_RESOURCE, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_RESOURCE));
            }


            // 查询账务信息
            AccountSummarySearch accountSummarySearch = new AccountSummarySearch();
            accountSummarySearch.setPayType(1);
            accountSummarySearch.setHid(user.getHid());
            accountSummarySearch.setBusinessDay(user.getBusinessDay());
            accountSummarySearch.setGroupType(4);

            // 房费
            accountSummarySearch.setAccountType(1);
            Map<Integer, AccountSummary> accountSummaryMap = accountDao.accountSummary(accountSummarySearch).stream().collect(Collectors.toMap(AccountSummary::getRegistId, a -> a, (k1, k2) -> k1));

            String ids = "-1,";
            HashMap<Integer, Boolean> regMap = new HashMap<>();


            Set<Integer> regKeys = accountSummaryMap.keySet();
            for (Integer reg : regKeys) {
                regMap.put(reg, true);
                ids += reg + ",";
            }

            // 商品费
            accountSummarySearch.setAccountType(2);
            Map<Integer, AccountSummary> accountSummaryGoodsMap = accountDao.accountSummary(accountSummarySearch).stream().collect(Collectors.toMap(AccountSummary::getRegistId, a -> a, (k1, k2) -> k1));
            regKeys = accountSummaryGoodsMap.keySet();
            for (Integer reg : regKeys) {
                if (regMap.get(reg) != null && regMap.get(reg)) {
                    continue;
                }
                regMap.put(reg, true);
                ids += reg + ",";
            }
            // 餐费
            accountSummarySearch.setAccountType(3);
            Map<Integer, AccountSummary> accountSummaryFoodsMap = accountDao.accountSummary(accountSummarySearch).stream().collect(Collectors.toMap(AccountSummary::getRegistId, a -> a, (k1, k2) -> k1));
            regKeys = accountSummaryFoodsMap.keySet();
            for (Integer reg : regKeys) {
                if (regMap.get(reg) != null && regMap.get(reg)) {
                    continue;
                }
                regMap.put(reg, true);
                ids += reg + ",";
            }

            // 查询当天开，当天退的客人信息
            // 查询当天在住且当天退房的信息
            RegistSearch  registSearchCheckOut = new RegistSearch();
            registSearchCheckOut.setHid(user.getHid());
            registSearchCheckOut.setCheckoutBusinessDay(user.getBusinessDay());
            registSearchCheckOut.setState(1);
            registSearchCheckOut.setBusinessDay(user.getBusinessDay());
            List<Regist> registsNew = registDao.selectBySearch(registSearchCheckOut);
            for (Regist reg : registsNew) {
                Integer checkinType = reg.getCheckinType();
                // 自用、免费房不算
                if(checkinType==4||checkinType==5){
                    continue;
                }
                if (regMap.get(reg.getRegistId()) != null && regMap.get(reg.getRegistId())) {
                    continue;
                }
                regMap.put(reg.getRegistId(), true);
                ids += reg.getRegistId() + ",";
            }

            ids = ids.substring(0, ids.length() - 1);
            // 查询在住客人信息
            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setRegistIds(ids);
            List<Regist> regists = registDao.selectBySearch(registSearch);



            // 需要组装的数据
            HashMap<Integer, NightAuditResource> pxHashMap = new HashMap<>();

            // 查询当月当年数据 月

            Map<Integer, NightAuditResource> nightAuditResourceMonthMap = new HashMap<>();
            Map<Integer, NightAuditResource> nightAuditResourceYearMap = new HashMap<>();

            NightAuditResourceSearch nightAuditResourceSearch = new NightAuditResourceSearch();
            nightAuditResourceSearch.setHid(user.getHid());
            nightAuditResourceSearch.setAuditYearMonth(user.getBusinessMonth());

            List<NightAuditResource> nightAuditResourceMonth = nightAuditResourceDao.selectBySearchGroup(nightAuditResourceSearch);
            if (nightAuditResourceMonth != null && nightAuditResourceMonth.size() > 0) {
                nightAuditResourceMonthMap = nightAuditResourceMonth.stream().collect(Collectors.toMap(NightAuditResource::getResourceId, a -> a, (k1, k2) -> k1));
            }

            // 年
            nightAuditResourceSearch.setAuditYearMonth(null);
            nightAuditResourceSearch.setAuditYear(user.getBusinessYear());
            List<NightAuditResource> nightAuditResourceYear = nightAuditResourceDao.selectBySearchGroup(nightAuditResourceSearch);

            if (nightAuditResourceYear != null && nightAuditResourceYear.size() > 0) {
                nightAuditResourceYearMap = nightAuditResourceYear.stream().collect(Collectors.toMap(NightAuditResource::getResourceId, a -> a, (k1, k2) -> k1));
            }

            // 2.查询当前房间的除净房、脏房之外的房态
            RegistSearch registNumsSearch = new RegistSearch();
            registNumsSearch.setHid(user.getHid());
            registNumsSearch.setState(0);
            List<Regist> checkInRegist = registDao.selectBySearch(registNumsSearch);
            // 查询当天在住且当天退房的信息
            registNumsSearch = new RegistSearch();
            registNumsSearch.setHid(user.getHid());
            registNumsSearch.setCheckoutBusinessDay(user.getBusinessDay());
            registNumsSearch.setState(1);
            registNumsSearch.setBusinessDay(user.getBusinessDay());
            List<Regist> registLeave = registDao.selectBySearch(registNumsSearch);
            registLeave.addAll(checkInRegist);

            ArrayList<Regist> registCheck = new ArrayList<>();
            for(Regist regist:registLeave){
                if(regist.getCheckinType()!=4&&regist.getCheckinType()!=5){
                    registCheck.add(regist);
                }
            }

            Map<Integer, List<Regist>> dayRegistMap = registCheck.stream().filter(map -> map.getResourceId() != null).collect(Collectors.groupingBy(Regist::getResourceId));

            for (int i = 1; i < 6; i++) {
                NightAuditResource nightResourcePx = new NightAuditResource();
                nightResourcePx.setResourceId(i);
                nightResourcePx.setHid(user.getHid());
                nightResourcePx.setHotelGroupId(user.getHotelGroupId());
                nightResourcePx.setBusinessDay(user.getBusinessDay());
                nightResourcePx.setAuditYear(user.getBusinessYear());
                nightResourcePx.setAuditYearMonth(user.getBusinessMonth());
                nightResourcePx.setMoney(0);
                nightResourcePx.setFoodsMoney(0);
                nightResourcePx.setGoodsMoney(0);
                nightResourcePx.setMoneyMonth(0L);
                nightResourcePx.setFoodsMoneyMonth(0L);
                nightResourcePx.setGoodsMoneyMonth(0L);
                nightResourcePx.setMoneyYear(0L);
                nightResourcePx.setFoodsMoneyYear(0L);
                nightResourcePx.setGoodsMoneyYear(0L);
                nightResourcePx.setCheckinNum(0);
                List<Regist> regists1 = dayRegistMap.get(i);
                if (regists1 != null) {
                    nightResourcePx.setCheckinNum(regists1.size());
                }
                nightResourcePx.setCheckinNumMonth(0);
                nightResourcePx.setCheckinNumYear(0);
                pxHashMap.put(i, nightResourcePx);
            }

            // 遍历当天在住数据，并进行组装
            for (Regist regist : regists) {
                Integer resourceId = regist.getResourceId();
                Integer registId = regist.getRegistId();
                // 入住数
                NightAuditResource nightResourcePx = pxHashMap.get(resourceId);


                // 总房费
                AccountSummary accountSummary = accountSummaryMap.get(registId);
                if (accountSummary != null) {
                    Integer money = nightResourcePx.getMoney();
                    int smoney = money + accountSummary.getSumMoney();
                    nightResourcePx.setMoney(smoney);
                }

                // 总商品费
                AccountSummary accountGoodsSummary = accountSummaryGoodsMap.get(registId);
                if (accountGoodsSummary != null) {
                    Integer money = nightResourcePx.getGoodsMoney();
                    int smoney = money + accountGoodsSummary.getSumMoney();
                    nightResourcePx.setGoodsMoney(smoney);
                }

                // 总餐费
                AccountSummary accountFoodsSummary = accountSummaryFoodsMap.get(registId);
                if (accountFoodsSummary != null) {
                    Integer money = nightResourcePx.getFoodsMoney();
                    int smoney = money + accountFoodsSummary.getSumMoney();
                    nightResourcePx.setFoodsMoney(smoney);
                }

                pxHashMap.put(resourceId, nightResourcePx);

            }

            // 查询当前有效的预订信息

            ArrayList<BookingOrder> bookingOrders = new ArrayList<>();

            BookingOrderSearch bookingOrderSearch = new BookingOrderSearch();
            bookingOrderSearch.setHid(user.getHid());
            bookingOrderSearch.setOrderStatus(1);

            List<BookingOrder> bookingOrders1 = bookingOrderDao.selectBySearch(bookingOrderSearch);

            Map<Integer, List<Account>> accountMap = new HashMap<>();

            if (bookingOrders1 != null && bookingOrders1.size() > 0) {
                String bookingIds = "";
                for (BookingOrder bo : bookingOrders1) {
                    bookingIds += bo.getBookingOrderId();
                    bookingIds += ",";
                }

                bookingIds = bookingIds.substring(0, bookingIds.length() - 1);
                // 查询在住账务信息
                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setIsCancel(0);
                accountSearch.setHid(user.getHid());
                accountSearch.setBusinessDay(user.getBusinessDay());
                accountSearch.setBookingIds(bookingIds);
                List<Account> accounts = accountDao.selectBySearch(accountSearch);
                accountMap = accounts.stream().collect(Collectors.groupingBy(Account::getBookingId));

                for (BookingOrder bo : bookingOrders1) {

                    Integer resourceId = bo.getResourceId();

                    List<Account> accounts1 = accountMap.get(bo.getBookingOrderId());

                    if (accounts1 == null || accounts1.size() < 1) {
                        continue;
                    }

                    NightAuditResource nightAuditResource = pxHashMap.get(resourceId);

                    Integer roomMoney = 0;
                    Integer goodMoney = 0;
                    Integer foodMoney = 0;


                    for (Account account : accounts1) {

                        if (account.getPayType() != 1) {
                            continue;
                        }

                        switch (account.getAccountType()) {
                            case 1:
                                roomMoney += account.getPrice();
                                break;
                            case 2:
                                goodMoney += account.getPrice();
                                break;
                            case 3:
                                foodMoney += account.getPrice();
                                break;
                        }

                    }

                    nightAuditResource.setMoney(nightAuditResource.getMoney() + roomMoney);
                    nightAuditResource.setGoodsMoney(nightAuditResource.getGoodsMoney() + goodMoney);
                    nightAuditResource.setFoodsMoney(nightAuditResource.getFoodsMoney() + foodMoney);

                    pxHashMap.put(resourceId, nightAuditResource);

                }

            }


            ArrayList<NightAuditResource> nightAuditResources = new ArrayList<>();

            Set<Integer> integers = pxHashMap.keySet();

            for (Integer key : integers) {

                NightAuditResource nightAuditResource = pxHashMap.get(key);

                NightAuditResource monthData = nightAuditResourceMonthMap.get(key);
                if (monthData == null) {
                    monthData = new NightAuditResource();
                    monthData.setMoney(0);
                    monthData.setFoodsMoney(0);
                    monthData.setGoodsMoney(0);
                    monthData.setMoneyMonth(0L);
                    monthData.setFoodsMoneyMonth(0L);
                    monthData.setGoodsMoneyMonth(0L);
                    monthData.setMoneyYear(0L);
                    monthData.setFoodsMoneyYear(0L);
                    monthData.setGoodsMoneyYear(0L);
                    monthData.setCheckinNum(0);
                    monthData.setCheckinNumMonth(0);
                    monthData.setCheckinNumYear(0);
                }
                NightAuditResource yearData = nightAuditResourceYearMap.get(key);
                if (yearData == null) {
                    yearData = new NightAuditResource();
                    yearData.setMoney(0);
                    yearData.setFoodsMoney(0);
                    yearData.setGoodsMoney(0);
                    yearData.setMoneyMonth(0L);
                    yearData.setFoodsMoneyMonth(0L);
                    yearData.setGoodsMoneyMonth(0L);
                    yearData.setMoneyYear(0L);
                    yearData.setFoodsMoneyYear(0L);
                    yearData.setGoodsMoneyYear(0L);
                    yearData.setCheckinNum(0);
                    yearData.setCheckinNumMonth(0);
                    yearData.setCheckinNumYear(0);
                }

                nightAuditResource.setCheckinNumMonth(nightAuditResource.getCheckinNum() + monthData.getCheckinNum());
                nightAuditResource.setCheckinNumYear(nightAuditResource.getCheckinNum() + yearData.getCheckinNum());

                nightAuditResource.setFoodsMoneyMonth(nightAuditResource.getFoodsMoney().longValue() + monthData.getFoodsMoney().longValue());
                nightAuditResource.setFoodsMoneyYear(nightAuditResource.getFoodsMoney().longValue() + yearData.getFoodsMoney());
                Long i = nightAuditResource.getMoney().longValue() + yearData.getMoney();
                nightAuditResource.setMoneyMonth(nightAuditResource.getMoney().longValue() + monthData.getMoney());
                nightAuditResource.setMoneyYear(i);

                nightAuditResource.setGoodsMoneyMonth(nightAuditResource.getGoodsMoney().longValue() + monthData.getGoodsMoney());
                nightAuditResource.setGoodsMoneyYear(nightAuditResource.getGoodsMoney().longValue() + yearData.getGoodsMoney());

                nightAuditResources.add(nightAuditResource);
            }

            nightTransactionService.nightAuditResource(nightAuditResources);

            responseData.setData(nightAuditResources);
            // 如果没查询出夜审记录 ，则重新创建记录
            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            Integer update = nightStepDao.update(nightStep);
            // 创建新的夜审状态
            createNightStep(N_STEP.NS_ADD_MEMBER, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_ADD_MEMBER));


        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }


    @Override
    public ResponseData statisticsOTAData(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);

            HotelCompanyInfoSearch hotelCompanyInfoSearch = new HotelCompanyInfoSearch();
            hotelCompanyInfoSearch.setHid(user.getHid());
            hotelCompanyInfoSearch.setCompanyType(3);

            List<HotelCompanyInfo> hotelCompanyInfos = hotelCompanyInfoDao.selectBySearch(hotelCompanyInfoSearch);

            Map<Integer, HotelCompanyInfo> hotelCompanyInfoMap = hotelCompanyInfos.stream().collect(Collectors.toMap(HotelCompanyInfo::getOtaType, a -> a, (k1, k2) -> k1));

            // 在住信息
            ArrayList<Regist> checkInRegits = new ArrayList<>();

            // 查询当前在住信息
            RegistSearch registSearch = new RegistSearch();
            registSearch.setState(0);
            registSearch.setHid(user.getHid());
            registSearch.setResourceId(5);
            List<Regist> regists = registDao.selectBySearch(registSearch);
            if (regists != null) {
                checkInRegits.addAll(regists);
            }

            registSearch.setState(1);
            registSearch.setBusinessDay(user.getBusinessDay());
            registSearch.setCheckoutBusinessDay(user.getBusinessDay());

            List<Regist> regists1 = registDao.selectBySearch(registSearch);
            if (regists1 != null) {
                checkInRegits.addAll(regists1);
            }

            // 查询符合条件在住信息的账务信息
            String registIds = "0";

            for (Regist regist : checkInRegits) {
                registIds += ",";
                registIds += regist.getRegistId();
            }

            // 查询当前有效的预订信息

            ArrayList<BookingOrder> bookingOrders = new ArrayList<>();

            BookingOrderSearch bookingOrderSearch = new BookingOrderSearch();
            bookingOrderSearch.setHid(user.getHid());
            bookingOrderSearch.setOrderStatus(1);

            List<BookingOrder> bookingOrders1 = bookingOrderDao.selectBySearch(bookingOrderSearch);

            if (bookingOrders1 != null) {
                bookingOrders.addAll(bookingOrders1);
            }

            // 查询在住账务信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setIsCancel(0);
            accountSearch.setHid(user.getHid());
            accountSearch.setBusinessDay(user.getBusinessDay());
            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            Map<Integer, List<Account>> accountMap = accounts.stream().collect(Collectors.groupingBy(Account::getRegistId));

            Set<Integer> accRegistIds = accountMap.keySet();

            String rsgKids = "0";
            for (Integer re : accRegistIds) {
                rsgKids += ",";
                rsgKids += re;
            }
            RegistSearch registSearch1 = new RegistSearch();
            registSearch1.setRegistIds(rsgKids);
            List<Regist> regists3 = registDao.selectBySearch(registSearch1);


            // 查询预订账务信息

            String bookingIds = "0";

            for (BookingOrder bo : bookingOrders) {
                bookingIds += ",";
                bookingIds += bo.getBookingOrderId();
            }

            accountSearch.setRegistIds(null);
            accountSearch.setBookingIds(bookingIds);

            List<Account> bookAccounts = accountDao.selectBySearch(accountSearch);

            Map<Integer, List<Account>> bookAccountMap = bookAccounts.stream().collect(Collectors.groupingBy(Account::getBookingId));

            // 查询上一次夜审数据
            NightAuditOtaSearch nightAuditOtaSearch = new NightAuditOtaSearch();
            nightAuditOtaSearch.setHid(user.getHid());
            nightAuditOtaSearch.setPageNum(0);
            nightAuditOtaSearch.setPageSize(1);

            Page<NightAuditOta> nightAuditOtas1 = nightAuditOtaDao.selectBySearch(nightAuditOtaSearch);
            ArrayList<NightAuditOta> oldAuditData = new ArrayList<>();

            if (nightAuditOtas1 != null && nightAuditOtas1.size() > 0) {
                NightAuditOta nightAuditOta1 = nightAuditOtas1.get(0);

                nightAuditOtaSearch.setPageNum(null);
                nightAuditOtaSearch.setPageSize(null);
                nightAuditOtaSearch.setBusinessDay(nightAuditOta1.getBusinessDay());

                oldAuditData = nightAuditOtaDao.selectBySearch(nightAuditOtaSearch);

            }

            Map<Integer, NightAuditOta> oldAuditDataMap = oldAuditData.stream().collect(Collectors.toMap(NightAuditOta::getOtaType, a -> a, (k1, k2) -> k1));

            // 根据协议单位区分 客源信息
            // 2.携程预付 3.携程到付 4.携程闪住 5.美团预付 6.美团到付 7.美团溜溜住 8.艺龙预付 9.艺龙到付 10.去哪儿预付
            // 11.去哪儿到付 12.飞猪预付 13.飞猪到付 14.阿里信用住
            Map<Integer, List<Regist>> companyRegistMap = checkInRegits.stream().collect(Collectors.groupingBy(Regist::getCompanyId));
            Map<Integer, List<BookingOrder>> companyBookMap = bookingOrders.stream().collect(Collectors.groupingBy(BookingOrder::getCompanyId));
            Map<Integer, List<Regist>> allAccountRegist = regists3.stream().collect(Collectors.groupingBy(org -> Optional.ofNullable(org.getCompanyId()).orElse(-1)));

            ArrayList<NightAuditOta> nightAuditOtas = new ArrayList<>();

            for (int i = 2; i < 20; i++) {

                NightAuditOta nightAuditOta = new NightAuditOta();
                nightAuditOta.setBusinessDay(user.getBusinessDay());
                nightAuditOta.setAuditYear(user.getBusinessYear());
                nightAuditOta.setAuditYearMonth(user.getBusinessMonth());

                nightAuditOta.setHid(user.getHid());
                nightAuditOta.setHotelGroupId(user.getHotelGroupId());

                nightAuditOta.setOtaType(i);
                nightAuditOta.setOtaName(OTA_TYPE.getName(i));

                HotelCompanyInfo hotelCompanyInfo = hotelCompanyInfoMap.get(i);
                if (hotelCompanyInfo == null) {
                    nightAuditOtas.add(nightAuditOta);
                    continue;
                }

                NightAuditOta nauOtaData = oldAuditDataMap.get(i);
                if (nauOtaData == null) {
                    nauOtaData = new NightAuditOta();
                }

                // 根据协议区分的 入住 登记信息
                List<Regist> regists2 = companyRegistMap.get(hotelCompanyInfo.getId());
                if (regists2 == null) {
                    regists2 = new ArrayList<>();
                }

                List<BookingOrder> bookingOrders2 = companyBookMap.get(hotelCompanyInfo.getId());

                List<Regist> accountRegistIds = allAccountRegist.get(hotelCompanyInfo.getId());

                if (accountRegistIds == null && bookingOrders2 == null) {
                    nightAuditOta.setCheckinNum(0);
                    nightAuditOta.setFoodsMoney(0);
                    nightAuditOta.setRoomMoney(0);
                    nightAuditOta.setGoodsMoney(0);
                    nightAuditOta.setPayMoney(0);

                    nightAuditOta.setCheckinNumMonth(nauOtaData.getCheckinNumMonth());
                    nightAuditOta.setCheckinNumYear(nauOtaData.getCheckinNumYear());

                    nightAuditOta.setFoodsMoneyMonth(nauOtaData.getFoodsMoneyMonth());
                    nightAuditOta.setFoodsMoneyYear(nauOtaData.getFoodsMoneyYear());

                    nightAuditOta.setGoodsMoneyMonth(nauOtaData.getFoodsMoneyMonth());
                    nightAuditOta.setGoodsMoneyYear(nauOtaData.getGoodsMoneyYear());

                    nightAuditOta.setRoomMoneyMonth(nauOtaData.getRoomMoneyMonth());
                    nightAuditOta.setRoomMoneyYear(nauOtaData.getRoomMoneyYear());

                    nightAuditOtas.add(nightAuditOta);
                    continue;
                }

                Integer roomMoney = 0;
                Integer foodMoney = 0;
                Integer goodMoney = 0;
                Integer price = 0;

                nightAuditOta.setCheckinNum(regists2.size());

                nightAuditOta.setCheckinNumMonth(nauOtaData.getCheckinNumMonth() + nightAuditOta.getCheckinNum());
                nightAuditOta.setCheckinNumYear(nauOtaData.getCheckinNumYear() + nightAuditOta.getCheckinNum());

                if (accountRegistIds == null) {
                    accountRegistIds = new ArrayList<>();
                }

                ArrayList<Account> childAccounts = new ArrayList<>();

                for (Regist re : accountRegistIds) {

                    if (re.getResourceId() != 5) {
                        continue;
                    }

                    List<Account> accounts1 = accountMap.get(re.getRegistId());

                    if (accounts1 == null || accounts1.size() < 1) {
                        continue;
                    }

                    childAccounts.addAll(accounts1);

                }

                if (bookingOrders2 == null) {
                    bookingOrders2 = new ArrayList<>();
                }

                for (BookingOrder bo : bookingOrders2) {

                    List<Account> accounts1 = bookAccountMap.get(bo.getBookingOrderId());

                    if (accounts1 == null || accounts1.size() < 1) {
                        continue;
                    }

                    childAccounts.addAll(accounts1);

                }

                for (Account act : childAccounts) {

                    Integer payType = act.getPayType();
                    Integer childPrice = act.getPrice();

                    // 消费
                    if (payType == 1) {

                        switch (act.getAccountType()) {
                            case 1:
                                roomMoney += childPrice;
                                break;
                            case 2:
                                goodMoney += childPrice;
                                break;
                            case 3:
                                foodMoney += childPrice;
                                break;
                        }

                        continue;

                    }

                    String payCodeId = act.getPayCodeId();

                    // 预授权不计算
                    if ("9100".equals(payCodeId) || "9620".equals(payCodeId)) {
                        continue;
                    }

                    price += childPrice;

                }


                nightAuditOta.setFoodsMoney(foodMoney);
                nightAuditOta.setRoomMoney(roomMoney);
                nightAuditOta.setGoodsMoney(goodMoney);
                nightAuditOta.setPayMoney(price);


                nightAuditOta.setFoodsMoneyMonth(nauOtaData.getFoodsMoneyMonth() + foodMoney);
                nightAuditOta.setFoodsMoneyYear(nauOtaData.getFoodsMoneyYear() + foodMoney);

                nightAuditOta.setGoodsMoneyMonth(nauOtaData.getFoodsMoneyMonth() + goodMoney);
                nightAuditOta.setGoodsMoneyYear(nauOtaData.getGoodsMoneyYear() + goodMoney);

                nightAuditOta.setRoomMoneyMonth(nauOtaData.getRoomMoneyMonth() + roomMoney);
                nightAuditOta.setRoomMoneyYear(nauOtaData.getRoomMoneyYear() + roomMoney);

                nightAuditOtas.add(nightAuditOta);
            }


            nightTransactionService.nightOtaData(nightAuditOtas);

            responseData.setData(nightAuditOtas);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    /**
     * 会员注册数统计
     * <p>
     * 统计当前会员的总消费 和 注册信息
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData registerVipNum(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);

            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_ADD_MEMBER);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_ADD_MEMBER, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_ADD_MEMBER));
            }

            ArrayList<NightAuditMember> nightAuditMembers = new ArrayList<>();


            this.memberBalance(user);
            // 1.查询当前酒店的注册会员数

            // 会员注册数据 根据不同的级别来
            HashMap<Integer, NightAuditMember> memberRegisterMap = new HashMap<>();

            CardOperationRecordSearch cardOperationRecordSearch = new CardOperationRecordSearch();
            cardOperationRecordSearch.setHid(user.getHid());
            cardOperationRecordSearch.setBusinessDay(user.getBusinessDay());
            cardOperationRecordSearch.setType(1);

            List<CardOperationRecord> cardOperationRecords = cardOperationRecordDao.selectBySearch(cardOperationRecordSearch);

            for (CardOperationRecord cardOperationRecord : cardOperationRecords) {

                CardInfo c = (CardInfo) JSONObject.toBean(JSONObject.fromObject(cardOperationRecord.getAfterData()), CardInfo.class);

                Integer cardLevelId = c.getCardLevelId();

                NightAuditMember nightAuditMember = memberRegisterMap.get(cardLevelId);
                if (nightAuditMember == null) {
                    nightAuditMember = new NightAuditMember();
                    nightAuditMember.setCardLevel(c.getCardLevel());
                    nightAuditMember.setCardLevelId(cardLevelId);
                    nightAuditMember.setCardType(c.getCardType());
                    nightAuditMember.setCardTypeId(c.getCardTypeId());
                    nightAuditMember.setType(1);
                    nightAuditMember.setHid(user.getHid());
                    nightAuditMember.setHotelGroupId(user.getHotelGroupId());
                    nightAuditMember.setBusinessDay(user.getBusinessDay());
                    nightAuditMember.setAuditYear(user.getBusinessYear());
                    nightAuditMember.setAuditYearMonth(user.getBusinessMonth());
                    nightAuditMember.setRecharge(0);
                    nightAuditMember.setAddNewMember(0);
                }

                // 注册数
                int reNum = nightAuditMember.getAddNewMember() + 1;
                nightAuditMember.setAddNewMember(reNum);

                // 注册金额
                int recharge = nightAuditMember.getRecharge() + c.getCardMoney();
                nightAuditMember.setRecharge(recharge);
                memberRegisterMap.put(cardLevelId, nightAuditMember);

            }


            // 2.查看不同级别的注册金额
            CardLevelSearch cardLevelSearch = new CardLevelSearch();
            cardLevelSearch.setHid(user.getHid());
            Map<Integer, CardLevel> cardLevelMap = cardLevelDao.selectBySearch(cardLevelSearch).stream().collect(Collectors.toMap(CardLevel::getId, a -> a, (k1, k2) -> k1));

            CardRechargeSearch cardRechargeSearch = new CardRechargeSearch();
            cardRechargeSearch.setHid(user.getHid());
            cardRechargeSearch.setNoType(2);
            cardRechargeSearch.setBusinessDay(user.getBusinessDay());
            List<CardRechargeData> cardRechargeData = cardRechargeDao.cardRechargeData(cardRechargeSearch);

            for (CardRechargeData cardRecs : cardRechargeData) {

                CardLevel cardLevel = cardLevelMap.get(cardRecs.getCardLevelId());

                NightAuditMember nightAuditMember = new NightAuditMember();
                nightAuditMember.setCardLevel(cardLevel.getCode());
                nightAuditMember.setCardLevelId(cardLevel.getId());
                nightAuditMember.setCardType(cardLevel.getCardType());
                nightAuditMember.setCardTypeId(cardLevel.getCardTypeId());
                nightAuditMember.setRecharge(cardRecs.getPayMoney());
                nightAuditMember.setType(2);
                nightAuditMember.setHid(user.getHid());
                nightAuditMember.setHotelGroupId(user.getHotelGroupId());
                nightAuditMember.setBusinessDay(user.getBusinessDay());
                nightAuditMember.setAuditYear(user.getBusinessYear());
                nightAuditMember.setAuditYearMonth(user.getBusinessMonth());
                nightAuditMember.setRecharge(cardRecs.getPayMoney());
                nightAuditMember.setAddNewMember(0);

                nightAuditMembers.add(nightAuditMember);

            }

            Set<Integer> mkeys = memberRegisterMap.keySet();

            for (Integer key : mkeys) {

                nightAuditMembers.add(memberRegisterMap.get(key));

            }


            nightTransactionService.nightAuditMember(nightAuditMembers);
            // 如果没查询出夜审记录 ，则重新创建记录
            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            Integer update = nightStepDao.update(nightStep);
            // 创建新的夜审状态
            createNightStep(N_STEP.NS_AR, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_AR));


        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    public ResponseData memberBalance(TbUserSession user) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            CardGroupInfoSearch cardGroupInfoSearch = new CardGroupInfoSearch();
            cardGroupInfoSearch.setHotelGroupId(user.getHotelGroupId());
            cardGroupInfoSearch.setSta("I");

            List<CardGroupInfo> cardGroupInfos = cardGroupInfoDao.selectBySearchSummary(cardGroupInfoSearch);
            if (cardGroupInfos == null || cardGroupInfos.size() < 1) {
                return responseData;
            }
            CardGroupInfo cardGroupInfo = cardGroupInfos.get(0);

            NightAuditMemberBalance nightAuditMemberBalance = new NightAuditMemberBalance();
            nightAuditMemberBalance.setHid(user.getHid());
            nightAuditMemberBalance.setHotelGroupId(user.getHotelGroupId());
            nightAuditMemberBalance.setBusinessDay(user.getBusinessDay());
            nightAuditMemberBalance.setBalance(cardGroupInfo.getBalance() + "");
            nightAuditMemberBalance.setLargessBalance(cardGroupInfo.getLargessBalance() + "");

            nightAuditMemberBalanceDao.insert(nightAuditMemberBalance);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }
        return responseData;
    }


    /**
     * 统计夜审协议单位数据
     * <p>
     * 1. 当前协议单位开房数
     * <p>
     * 2. 新增挂账总额
     * <p>
     * 3. 已结算挂账总额
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData statisticsArData(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);

            // 查询当前夜审步骤，没有则重新创建
            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_AR);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_AR, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_AR));
            }

            // 如果没查询出夜审记录 ，则重新创建记录
            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            Integer update = nightStepDao.update(nightStep);
            // 创建新的夜审状态
            createNightStep(N_STEP.NS_AR, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_AR));
            this.arPayRec(param);
            this.statisticsOTAData(param);

            // 如果没查询出夜审记录 ，则重新创建记录
            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            nightStepDao.update(nightStep);
            HashMap<String, String> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put("nightName", nightStep.getNightSettingName());

            createNightStep(N_STEP.NS_UPDATE_BUSINESS_DAY, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_UPDATE_BUSINESS_DAY));

            baseService.push(user.getHotelGroupId(), user.getHid(), 4, stringStringHashMap, new HashMap<String, String>(), true, true);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    @Override
    public ResponseData arPayRec(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);

            HotelCompanyPayRecordSearch hotelCompanyPayRecordSearch = new HotelCompanyPayRecordSearch();
            hotelCompanyPayRecordSearch.setBusinessDay(user.getBusinessDay());
            hotelCompanyPayRecordSearch.setHid(user.getHid());

            Page<HotelCompanyPayRecord> hotelCompanyPayRecords = hotelCompanyPayRecordDao.selectBySearch(hotelCompanyPayRecordSearch);

            Map<Integer, List<HotelCompanyPayRecord>> collect = hotelCompanyPayRecords.stream().collect(Collectors.groupingBy(HotelCompanyPayRecord::getPayClassId));

            Set<Integer> integers = collect.keySet();

            for (Integer key : integers) {

                Integer sumMoney = 0;
                List<HotelCompanyPayRecord> hotelCompanyPayRecords1 = collect.get(key);

                HotelCompanyPayRecord hotelCompanyPayRecord = hotelCompanyPayRecords1.get(0);

                NigthAuditArPayment nigthAuditArPayment = new NigthAuditArPayment();
                nigthAuditArPayment.setHid(user.getHid());
                nigthAuditArPayment.setHotelGroupId(user.getHotelGroupId());
                nigthAuditArPayment.setAuditYearMonth(user.getBusinessMonth());
                nigthAuditArPayment.setAuditYear(user.getBusinessYear());
                nigthAuditArPayment.setBusinessDay(user.getBusinessDay());
                nigthAuditArPayment.setPayClassId(hotelCompanyPayRecord.getPayClassId());
                nigthAuditArPayment.setPayClassName(hotelCompanyPayRecord.getPayClassName());

                for (HotelCompanyPayRecord hcpr : hotelCompanyPayRecords1) {
                    sumMoney += hcpr.getMoney();
                }

                nigthAuditArPayment.setMoney(sumMoney);

                auditArPaymentDao.insert(nigthAuditArPayment);

            }


        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    @Override
    public ResponseData finishNightStep(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            user.setClassId(HclassUtits.NIGHT_AUDIT);

            NightStepSearch nightStepSearch = new NightStepSearch();
            nightStepSearch.setHid(user.getHid());
            nightStepSearch.setBusinessDay(user.getBusinessDay());
            nightStepSearch.setNightSettingId(N_STEP.NS_UPDATE_BUSINESS_DAY);

            List<NightStep> nightSteps = nightStepDao.selectBySearch(nightStepSearch);

            NightStep nightStep = new NightStep();
            if (nightSteps.size() > 0) {
                nightStep = nightSteps.get(0);
            } else {
                nightStep = createNightStep(N_STEP.NS_UPDATE_BUSINESS_DAY, user, N_STEP.NIGHT_STEP_NAME.get(N_STEP.NS_UPDATE_BUSINESS_DAY));
            }

            NightAuditRecordSearch nightAuditRecordSearch = new NightAuditRecordSearch();
            nightAuditRecordSearch.setHid(user.getHid());
            nightAuditRecordSearch.setBusinessDay(user.getBusinessDay());
            List<NightAuditRecord> nightAuditRecords = auditRecordDao.selectBySearch(nightAuditRecordSearch);
            if (nightAuditRecords.size() < 1) {
                throw new Exception("未查询到夜审执行信息。");
            }

            NightAuditRecord nightAuditRecord = nightAuditRecords.get(0);
            nightAuditRecord.setState(1);
            nightAuditRecord.setAuditEnd(new Date());

            Integer update = nightAuditRecordDao.update(nightAuditRecord);

            if (update < 1) {
                throw new Exception("修改夜审状态失败");
            }

            // 将订单流水信息清空
            HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();

            ArrayList<String> keyList = OrderNumUtils.keyList;

            for (String key : keyList) {
                String s = key + user.getHid();
                userCahe.put(OrderNumUtils.orderdbkey, s, "0");
                stringRedisTemplate.delete(key);
            }


            // 查询当前营业日期
            HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
            hotelBusinessDaySearch.setHid(user.getHid());
            HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);

            Integer businessDay = user.getBusinessDay();
            if (hotelBusinessDay != null) {
                businessDay = hotelBusinessDay.getBusinessDay();
            }

            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            Date parse = formatter.parse(businessDay + "");

            Calendar calendar = new GregorianCalendar();
            calendar.setTime(parse);
            calendar.add(calendar.DATE, 1);//把日期往后增加一天.整数往后推,负数往前移动

            String format = formatter.format(calendar.getTime());

            final Integer newBus = Integer.parseInt(format);


            HotelBusinessDay hotelBusinessDay1 = new HotelBusinessDay();
            hotelBusinessDay1.setHid(user.getHid());
            hotelBusinessDay1.setBusinessDay(newBus);
            hotelBusinessDayDao.insert(hotelBusinessDay1);

            nightStep.setState(1);
            nightStep.setEndTime(new Date());
            nightStepDao.update(nightStep);

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        HashMap<String, String> stringStringHashMap = new HashMap<>();
                        baseService.push(user.getHotelGroupId(), user.getHid(), PUSH_CONFIG_ID.NIGHT_OVER, stringStringHashMap, new HashMap<String, String>(), true, true);
                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        baseService.turnAlways(user);
                        UpdateHotelUserRequest updateHotelUserRequest = new UpdateHotelUserRequest();
                        updateHotelUserRequest.setHid(user.getHid());
                        updateHotelUserRequest.setBusinessDay(newBus);
                        baseService.updateHotelAllSession(updateHotelUserRequest);
                    } catch (Exception e) {
                        e.printStackTrace();
                        UpdateHotelUserRequest updateHotelUserRequest = new UpdateHotelUserRequest();
                        updateHotelUserRequest.setHid(user.getHid());
                        updateHotelUserRequest.setBusinessDay(newBus);
                        baseService.updateHotelAllSession(updateHotelUserRequest);
                    }
                }
            });

//            // 夜审之后，触发统计前一个营业日的订出率，入住率
//            ThreadPool bookingThreadPool = ThreadPoolFactory.getInstance()
//                    .getThreadPool(ThreadTypeEnum.EXECUTE_BOOKING_TASK);
//            bookingThreadPool.monitor(ThreadTypeEnum.EXECUTE_BOOKING_TASK.getMessage());
//            bookingThreadPool.execute(() -> {
//                JSONObject jsonObject = new JSONObject();
//                double futureSevenBook = getDoubleDataFromOrder(user.getHid(),futureSevenBookRate);
//                double preSevenBook = getDoubleDataFromOrder(user.getHid(),preSevenBookRate);
//                double result = (futureSevenBook - preSevenBook/preSevenBook);
//                RoomRateSearch roomRateSearch = new RoomRateSearch();
//                roomRateSearch.setBusinessDay(user.getBusinessDay()+"");
//                roomRateSearch.setHid(user.getHid());
//                roomRateSearch.setHotelGroupId(user.getHotelGroupId());
//                HotelRoomRate hotelRoomRate = hotelRoomRateDao.selectBySearch(roomRateSearch);
//                if(null == hotelRoomRate){
//                    hotelRoomRate = new HotelRoomRate();
//                    hotelRoomRate.setHid(user.getHid());
//                    hotelRoomRate.setHotelGroupId(user.getHotelGroupId());
//                    hotelRoomRate.setBookRateWeekOnWeek(result+"");
//                    hotelRoomRate.setBusinessDay(user.getBusinessDay()+"");
//                    hotelRoomRate.setCreateTime(new Date());
//                    hotelRoomRateDao.addOne(hotelRoomRate);
//                }else{
//                    hotelRoomRate.setBookRateWeekOnWeek(result+"");
//                    hotelRoomRateDao.updateOne(hotelRoomRate);
//                }
//            });
//
//            ThreadPool occupancyThreadPool = ThreadPoolFactory.getInstance()
//                    .getThreadPool(ThreadTypeEnum.EXECUTE_OCCUPANCY_TASK);
//            occupancyThreadPool.monitor(ThreadTypeEnum.EXECUTE_OCCUPANCY_TASK.getMessage());
//            occupancyThreadPool.execute(() -> {
//                double currentOccupancy = getDoubleDataFromOrder(user.getHid(),currentOccupancyRate);
//                double yesterdayOccupancy = getDoubleDataFromOrder(user.getHid(),yesterdayOccupancyRate);
//                double result = (currentOccupancy - yesterdayOccupancy/yesterdayOccupancy);
//                RoomRateSearch roomRateSearch = new RoomRateSearch();
//                roomRateSearch.setBusinessDay(user.getBusinessDay()+"");
//                roomRateSearch.setHid(user.getHid());
//                roomRateSearch.setHotelGroupId(user.getHotelGroupId());
//                HotelRoomRate hotelRoomRate = hotelRoomRateDao.selectBySearch(roomRateSearch);
//                if(null == hotelRoomRate){
//                    hotelRoomRate = new HotelRoomRate();
//                    hotelRoomRate.setHid(user.getHid());
//                    hotelRoomRate.setHotelGroupId(user.getHotelGroupId());
//                    hotelRoomRate.setOccupancyRateDayOnDay(result+"");
//                    hotelRoomRate.setBusinessDay(user.getBusinessDay()+"");
//                    hotelRoomRate.setCreateTime(new Date());
//                    hotelRoomRateDao.addOne(hotelRoomRate);
//                }else{
//                    hotelRoomRate.setOccupancyRateDayOnDay(result+"");
//                    hotelRoomRateDao.updateOne(hotelRoomRate);
//                }
//            });
            Integer finalBusinessDay = businessDay;
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        log.info("用户id:{}",user.getHotelGroupId());
                        log.info("配置id:{}",hotelGroupId);
                        PlatDockingSetting platDockingSetting = platDockingSettingService.selectByPlatAndHotel(user.getHid(), user.getHotelGroupId(), PlatTypeEnum.JS_SK_PLAT.getTypeStr());
                        String appSecret = platDockingSetting.getAppSecret();
                        String md5Hex = DigestUtils.md5DigestAsHex(appSecret.getBytes());
                        String key = md5Hex.substring(4, 20);

                        TbUserSession adminTbUser = getAdminTbUser(user.getHid());

                        if(Objects.equals(user.getHotelGroupId(), Integer.valueOf(hotelGroupId))){


                            log.info("调用商客经营数据推送开始");
                            List<OperateDataInfo> operateDataInfos = new ArrayList<>();
                            OperateDataInfo operateDataInfo = new OperateDataInfo();
                            String encryptPhone = AesCbcEncryptUtils.aesEncrypt(Objects.isNull(adminTbUser) ? "" : adminTbUser.getPhone(), key, AesCbcEncryptUtils.DEFAULT_IV);
                            operateDataInfo.setCustContactPhone(encryptPhone);
                            //营业额
                            Integer revenue = revenue(user, finalBusinessDay);
                            //客房入住率
                            String roomOccupancyRate = currentOccupancyRate(user);



                            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

                            LocalDate date = LocalDate.parse(String.valueOf(finalBusinessDay), inputFormatter);

                            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

                            String output = date.format(outputFormatter);

                            String startBusinessDay = output +" 00:00:00";
                            String endBusinessDay = output +" 23:59:59";
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
                            Date startParseDate = sdf.parse(startBusinessDay);
                            Date endParseDate = sdf.parse(endBusinessDay);
                            RegistPageRequest newRegistPageRequest = new RegistPageRequest();
                            newRegistPageRequest.setHid(user.getHid());
                            newRegistPageRequest.setGroupType(0);
                            newRegistPageRequest.setPageNum(1);
                            newRegistPageRequest.setPageSize(10000);
                            newRegistPageRequest.setOrderStatus(Collections.singletonList(0));
                            newRegistPageRequest.setCheckinTime(Arrays.asList(startParseDate.getTime()/1000, endParseDate.getTime()/1000));
                            //入住订单数
                            Page<Map<String, Object>> andRersonNamePage = registDao.selectRegistAndRersonNamePage(newRegistPageRequest);
                            List<BusinessData> businessDatas = new ArrayList<>();
                            BusinessData turnover = new BusinessData();
                            turnover.setKey("turnover");
                            turnover.setValue(String.valueOf(revenue));
                            businessDatas.add(turnover);
                            BusinessData occupancyRate = new BusinessData();
                            occupancyRate.setKey("roomOccupancyRate");
                            occupancyRate.setValue(roomOccupancyRate);
                            businessDatas.add(occupancyRate);
                            BusinessData checkInOrders = new BusinessData();
                            checkInOrders.setKey("checkInOrders");
                            checkInOrders.setValue(String.valueOf(andRersonNamePage.getTotal()));
                            businessDatas.add(checkInOrders);
                            operateDataInfo.setBusinessDatas(businessDatas);
                            OperationData operationData = new OperationData();

                            FounTbUserSessionSearch founTbUserSessionSearch = new FounTbUserSessionSearch();
                            founTbUserSessionSearch.setHid(user.getHid());
                            founTbUserSessionSearch.setHotelGroupId(user.getHotelGroupId());
                            founTbUserSessionSearch.setStartTime(startParseDate);
                            founTbUserSessionSearch.setEndTime(endParseDate);
                            List<TbUserSession> tbUserSessions = tbUserSessionDao.selectBySearchFounTbUserSession(founTbUserSessionSearch);
                            operationData.setMerchantLogins(CollectionUtils.isEmpty(tbUserSessions) ? 0 : tbUserSessions.size());
                            operationData.setUserLogins(0);
                            operateDataInfo.setOperationData(operationData);
                            operateDataInfos.add(operateDataInfo);
                            ThirdAuthReq thirdAuthReq = new ThirdAuthReq();
                            thirdAuthReq.setAuthType(PlatTypeEnum.JS_SK_PLAT);
                            JSONObject authParam = new JSONObject();
                            authParam.put("path", Pms.SYNCOPERATEDATA);
                            authParam.put("iv",AesCbcEncryptUtils.DEFAULT_IV);
                            thirdAuthReq.setAuthParam(authParam);
                            JSONObject reqParam = new JSONObject();
                            String appId ="";
                            try{
                                if(platDockingSetting!= null ){
                                    String otherConf = platDockingSetting.getOtherConf();
                                    if(StringUtils.isNotBlank(otherConf)){
                                        JSONObject otherConfJson = JSONObject.fromObject(otherConf);
                                        appId = otherConfJson.getString("pmsAppId");
                                    }
                                }
                                reqParam.put("appId",appId);
                            }catch (Exception e){
                                log.error("江苏商客appId获取失败:{}",e);
                            }
                            reqParam.put("operateDataInfo",operateDataInfos);
                            thirdAuthReq.setReqParam(reqParam);
                            baseService.pmsSouth(convertEntityToJson(thirdAuthReq));
                        }

                    }catch (Exception e){
                        log.error("商客经营数据发送失败:{}",e);
                    }
                }
            });



        } catch (Exception e) {
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("夜审任务执行异常:{}",e.getMessage(), e);
        }

        return responseData;
    }

    private double getDoubleDataFromOrder(Integer hid,String url) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("hid", hid);
        HttpEntity<HashMap<String, Object>> httpEntity = new HttpEntity<>(paramMap, headers);
        ResponseData rt = restTemplate.postForEntity("http://" + GURL.HOTEL + url, httpEntity, ResponseData.class).getBody();
        if (rt == null || !new Integer(1).equals(rt.getCode())) {
            return 0.0;
        }
        return (double) rt.getData();
    }


    public JSONObject convertEntityToJson(Object entity) throws Exception {
        JSONObject jsonObject = new JSONObject();
        Field[] declaredFields = entity.getClass().getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            jsonObject.put(declaredField.getName(), declaredField.get(entity));
        }
        return jsonObject;
    }

    /**
     * 今日收款
     * @param user
     * @param businessDay
     * @return
     */
    private Integer revenue(TbUserSession user,Integer businessDay){
        AccountSearch accountSearch = new AccountSearch();
        accountSearch.setHid(user.getHid());
        accountSearch.setBusinessDay(businessDay);
        accountSearch.setIsCancel(0);
        List<Account> accounts = accountDao.selectBySearch(accountSearch);
        Integer sumPayMoney = 0 ;
        if(!CollectionUtils.isEmpty(accounts)){
            for(Account account:accounts){
                if(account.getPrice() >= 0){
                    sumPayMoney += account.getPrice();
                }
            }
        }
        return sumPayMoney;
    }



    /**
     * 计算实时入住率和未来7天订出率
     * @param user
     * @return
     */
    private String currentOccupancyRate(TbUserSession user){
        String roomOccupancyRate = null;
        try {
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            roomInfoSearch.setState(1);
            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
            // 查询在住信息
            RegistSearch registSearch1 = new RegistSearch();
            registSearch1.setHid(user.getHid());
            registSearch1.setState(0);
            List<Regist> regists = registDao.selectBySearch(registSearch1);
            // 自用
            int zyNum = 0;
            for (Regist regist : regists) {
                Integer checkinType = regist.getCheckinType();
                if (checkinType == 5 || checkinType == 4) {
                    zyNum++;
                }
            }
            // 维修
            int wxNum = 0;
            // 按照房态分组
            Map<Integer, List<RoomInfo>> roomStateMap = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomNumState));
            // 维修数据
            List<RoomInfo> wxList = roomStateMap.get(ROOM_STATUS.OOO);
            if (wxList != null) {
                wxNum = wxList.size();
            }
            int bcs = roomInfos.size() - wxNum - zyNum;
            // 当前入住率 =（实时在住客房数－自用房）/（总客房数-维修-自用）×100%
            double v = (1.0 * regists.size() - zyNum) / bcs;
            if (Double.isNaN(v)){
                v = 0.0;
            }
            // 当前入住率
            roomOccupancyRate = String.format(df,v * 100) + "%";
        } catch (Exception e) {
            log.error("计算实时入住率异常",e);
        }
        return roomOccupancyRate;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyHotel(ApplyHotelRequest request) {
        NightAutoSetting nightAutoSetting = new NightAutoSetting();
        nightAutoSetting.setHotelGroupId(request.getHotelGroupId());
        nightAutoSetting.setHid(request.getHotelId());
        nightAutoSetting.setAutoNight(0);
        nightAutoSetting.setAutoTime("00:00");
        nightAutoSettingDao.insert(nightAutoSetting);
        NightAuditRecord nightAuditRecord = new NightAuditRecord();
        nightAuditRecord.setHid(request.getHotelId());
        nightAuditRecord.setHotelGroupId(request.getHotelGroupId());
        nightAuditRecord.setState(1);
        nightAuditRecord.setAuditStart(HotelUtils.addDayGetNewDate(new Date(), -1));
        nightAuditRecord.setAuditEnd(HotelUtils.addDayGetNewDate(new Date(), -1));
        nightAuditRecord.setClassId(5);
        nightAuditRecord.setBusinessDay(HotelUtils.getBusinessDayNumInt(HotelUtils.addDayGetNewDate(new Date(), -1).getTime()));
        nightAuditRecord.setCreateTime(new Date());
        nightAuditRecord.setCreateUserId("autonight" + request.getHotelId());
        nightAuditRecord.setMessage("创建酒店自动添加夜审记录");
        nightAuditRecordDao.insert(nightAuditRecord);
        String key = "audit:" + nightAuditRecord.getHid() + ":" + nightAuditRecord.getBusinessDay();
        stringRedisTemplate.opsForValue().set(key, new SimpleDateFormat("yyyy-MM-dd HH:mm").format(new Date()), 3, TimeUnit.DAYS);

    }
}
