package com.pms.czabsnight.service.order;

import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.CheckoutParam;
import com.pms.czpmsutils.request.OverStayRequest;
import com.pms.pmsorder.bean.Regist;
import net.sf.json.JSONObject;

import java.util.Map;

public interface PmsOrderService {


    /**
     * 散客入住
     * @param param
     * @return
     */
    public ResponseData blendCheckIn(JSONObject param);


    /**
     * 预定入住
     * @param param
     * @return
     */
    public Map<String,Object> bookingCheckIn(JSONObject param);

    /**
     * 结账
     * @param checkoutParam
     * @return
     */
    public ResponseData checkOut(CheckoutParam checkoutParam);

    /**
     * 续住参数
     * @param overStayRequest
     * @return
     */
    public ResponseData overStayNew(OverStayRequest overStayRequest);

    public ResponseData hourRoomPushMessage(Regist regist);

}
