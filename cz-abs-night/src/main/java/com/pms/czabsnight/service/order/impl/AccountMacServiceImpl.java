package com.pms.czabsnight.service.order.impl;

import com.pms.czabsnight.service.order.AccountMacService;
import com.pms.czabsnight.service.order.transaction.AccountMacTransactionService;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.search.BookingOrderDailyPriceSearch;
import com.pms.pmsorder.bean.search.RegistPersonSearch;
import com.pms.pmsorder.dao.BookingOrderDailyPriceDao;
import com.pms.pmsorder.dao.RegistDao;
import com.pms.pmsorder.dao.RegistPersonDao;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Primary
public class AccountMacServiceImpl extends BaseService implements AccountMacService {

    private static final Logger log = LogManager.getLogger(AccountMacServiceImpl.class);
    @Autowired
    private AccountDao accountDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private AccountMacTransactionService accountMacTransactionService ;

    @Override
    public ResponseData autoAddRoomPrice(TbUserSession user, Integer registId, Integer min) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            // 登记单id
            Regist regist = registDao.selectById(registId);
            if(regist==null||regist.getState()!=0){
                return responseData;
            }

            // 分钟

            if(min==0){

                return responseData;
            }

            // 营业日
            Integer businessDay = user.getBusinessDay();

            long time = new Date().getTime();

            long diff = time - regist.getCheckinTime().getTime() ;

            if(diff < min){
                return responseData;
            }

            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(registId);
            registPersonSearch.setRegistState(0);

            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
            RegistPerson registPerson = registPeople.get(0);

            // 查询当天的房价是否产生
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setRegistId(registId);
            bookingOrderDailyPriceSearch.setDailyTime(businessDay);
            bookingOrderDailyPriceSearch.setDailyState(1);

            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            if(bookingOrderDailyPrices==null||bookingOrderDailyPrices.size()<1){
                return responseData;
            }

            BookingOrderDailyPrice bookingOrderDailyPrice = bookingOrderDailyPrices.get(0);

            accountMacTransactionService.autoAddRoomPrice(bookingOrderDailyPrice,regist,registPerson,user);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }
        return responseData;
    }


}
