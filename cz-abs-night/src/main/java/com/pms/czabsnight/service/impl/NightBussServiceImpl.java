package com.pms.czabsnight.service.impl;

import com.github.pagehelper.Page;
import com.pms.czabsnight.bean.NightAuditRecord;
import com.pms.czabsnight.bean.NightAuditSettingRequest;
import com.pms.czabsnight.bean.NightAutoSetting;
import com.pms.czabsnight.bean.search.NightAuditRecordSearch;
import com.pms.czabsnight.bean.search.NightAutoSettingSearch;
import com.pms.czabsnight.dao.NightAuditRecordDao;
import com.pms.czabsnight.dao.NightAutoSettingDao;
import com.pms.czabsnight.service.NightBussService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.code.HotelBusinessDay;
import com.pms.czhotelfoundation.bean.code.search.HotelBusinessDaySearch;
import com.pms.czhotelfoundation.bean.room.RoomRepairRecordHistory;
import com.pms.czhotelfoundation.bean.room.search.RoomRepairRecordHistorySearch;
import com.pms.czhotelfoundation.dao.code.HotelBusinessDayDao;
import com.pms.czhotelfoundation.dao.room.RoomRepairRecordHistoryDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.JobName;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.ET;
import com.pms.czpmsutils.constant.HC;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.push.PUSH_CONFIG_ID;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.AddOrUpdateHotelBusinessDayRequest;
import com.pms.czpmsutils.request.BaseRequest;
import com.pms.czpmsutils.request.UpdateHotelUserRequest;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.search.BookingOrderDailyPriceSearch;
import com.pms.pmsorder.bean.search.BookingOrderSearch;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.dao.BookingOrderDailyPriceDao;
import com.pms.pmsorder.dao.BookingOrderDao;
import com.pms.pmsorder.dao.RegistDao;
import net.sf.json.JSONObject;
import org.quartz.Scheduler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Primary
public class NightBussServiceImpl extends BaseService implements NightBussService {


    private static final Logger log = LoggerFactory.getLogger(NightBussServiceImpl.class);

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private NightAuditRecordDao nightAuditRecordDao;

    private BaseService baseService = this;

    @Autowired
    private HotelBusinessDayDao hotelBusinessDayDao;

    @Autowired
    private RoomRepairRecordHistoryDao roomRepairRecordHistoryDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private NightAutoSettingDao nightAutoSettingDao;

    @Autowired
    @Qualifier("Scheduler")
    private Scheduler scheduler;

    // 任务名称 类名
    String jobClassName = "com.pms.czabsnight.jobs.AutoNightJob";

    @Override
    public ResponseData noShowBook(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            Map<Object, Object> jsonObject = new HashMap<>();
            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            BookingOrderSearch bookingOrderSearch = new BookingOrderSearch();
            bookingOrderSearch.setHid(user.getHid());
            bookingOrderSearch.setCheckInEndTime(new Date());
            bookingOrderSearch.setOrderStatus(BOOK.STA_YX);
            List<BookingOrder> bookingOrders = bookingOrderDao.selectBySearch(bookingOrderSearch);

            //获取需要NoShow处理的订单
            ArrayList<Object> rt = getNeedNoshowBook(bookingOrders);

            responseData.setData(rt);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    private ArrayList<Object> getNeedNoshowBook(List<BookingOrder> bookingOrders) throws ParseException {
        ArrayList<Object> rt = new ArrayList<>(bookingOrders.size());
        for (BookingOrder bookingOrder : bookingOrders) {
            Date checkinTime = new Date();
            checkinTime.setTime(bookingOrder.getCheckinTime().getTime());

            String keepTime = bookingOrder.getKeepTime();
            String[] split = keepTime.split(":");
            if (split.length == 2) {
                int h = Integer.parseInt(split[0]);

                //次日
                if (h < 12 || h == 24) {
                    checkinTime.setTime(checkinTime.getTime() + 24 * 3600 * 1000);
                }
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                String checkinTimeText = dateFormat.format(checkinTime);

                if (h == 24) {
                    keepTime = "00:00";
                }

                keepTime = checkinTimeText.split(" ")[0] + " " + keepTime;
                bookingOrder.setKeepTime(keepTime);

                Date keepTimeDate = dateFormat.parse(keepTime);

                dateFormat = new SimpleDateFormat("MM-dd HH:mm");
                if (keepTimeDate.getTime() < System.currentTimeMillis()) {
                    rt.add(bookingOrder);
                    bookingOrder.setKeepTime(dateFormat.format(keepTimeDate));
                }
            }
        }
        return rt;
    }


    /**
     * 查询应离未离订单
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData overtimeRegist(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            Map<Object, Object> jsonObject = new HashMap<>();
            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setState(ET.STATUS_NOT_END);
            registSearch.setCheckoutTimeMax(new Date());

            List<Regist> regists = registDao.selectBySearch(registSearch);

            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setDailyState(user.getBusinessDay());

            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, BookingOrderDailyPrice> collect = bookingOrderDailyPrices.stream().collect(Collectors.toMap(BookingOrderDailyPrice::getRegistId, a -> a, (k1, k2) -> k1));

            ArrayList<Regist> regists1 = new ArrayList<>();

            for (Regist regist : regists) {

                BookingOrderDailyPrice bookingOrderDailyPrice = collect.get(regist.getRegistId());

                if (bookingOrderDailyPrice == null) {
                    regists1.add(regist);
                    continue;
                }

                if (1 == bookingOrderDailyPrice.getDailyState()) {
                    regists1.add(regist);
                }


            }

            responseData.setData(regists1);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * 查询应离未离钟点房
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData overtimeHourRoom(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            Map<Object, Object> jsonObject = new HashMap<>();
            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setState(ET.STATUS_NOT_END);
            registSearch.setCheckinType(HC.HOUR_ROOM);
            List<Regist> regists = registDao.selectBySearch(registSearch);
            responseData.setData(regists);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * 查询到期维修房
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData expireRepairRoom(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            Map<Object, Object> jsonObject = new HashMap<>();
            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            RoomRepairRecordHistorySearch roomRepairRecordHistorySearch = new RoomRepairRecordHistorySearch();
            roomRepairRecordHistorySearch.setHid(user.getHid());
            roomRepairRecordHistorySearch.setMaxEndTime(new Date());
            roomRepairRecordHistorySearch.setNewRoomState(ROOM_STATUS.OOO);
            roomRepairRecordHistorySearch.setState(0);
            List<RoomRepairRecordHistory> roomRepairRecordHistories = roomRepairRecordHistoryDao.selectBySearch(roomRepairRecordHistorySearch);

            responseData.setData(roomRepairRecordHistories);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * 查询在住房间
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData checkinRoom(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            Map<Object, Object> jsonObject = new HashMap<>();
            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setDailyTime(user.getBusinessDay());
            List<Map<String, Object>> maps = bookingOrderDailyPriceDao.selectNightPrice(bookingOrderDailyPriceSearch);
            if (maps.size() > 0) {
                String key = user.getHid() + "addNightAccount" + user.getBusinessDay();

                final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();

                Object addNightAccount = userCahe.get("addNightAccount", key);
                userCahe.put("addNightAccount", key, "0");
            }
            responseData.setData(maps);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    @Override
    public ResponseData searchNightSetting(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            NightAutoSettingSearch nightAutoSettingSearch = new NightAutoSettingSearch();
            nightAutoSettingSearch.setHid(user.getHid());
            Page<NightAutoSetting> nightAutoSettings = nightAutoSettingDao.selectBySearch(nightAutoSettingSearch);

            NightAutoSetting nightAutoSetting = new NightAutoSetting();
            nightAutoSetting.setId(-1);

            if (nightAutoSettings != null) {
                nightAutoSetting = nightAutoSettings.get(0);
            }

            responseData.setData(nightAutoSetting);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    @Override
    public ResponseData addOrUpdateNightSetting(NightAuditSettingRequest param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Integer res = -1;
            NightAutoSetting nightAutoSetting = new NightAutoSetting();

            if (param.getAutoNight().equals(1)) {
                //判断设置的夜审时间跟上次夜审时间的间隔
                String autoTime = param.getAutoTime();
                //查询上次夜审时间与现在时间是否超过了14小时
                NightAuditRecordSearch nightAuditRecordSearch = new NightAuditRecordSearch();
                nightAuditRecordSearch.setHid(user.getHid());
                nightAuditRecordSearch.setPageNum(1);
                nightAuditRecordSearch.setPageSize(1);
                Page<NightAuditRecord> nightAuditRecords = nightAuditRecordDao.selectBySearch(nightAuditRecordSearch);
                if (nightAuditRecords != null && nightAuditRecords.size() > 0) {
                    NightAuditRecord nightAuditRecord = nightAuditRecords.get(0);
                    Date auditEnd = nightAuditRecord.getAuditEnd();
                    if (auditEnd == null) {
                        nightAuditRecord.getAuditStart();
                    }
                    //现在时间的 时分
                    Date nowTime = new Date();
                    SimpleDateFormat sf = new SimpleDateFormat("HH");
                    int hoursNow = Integer.parseInt(sf.format(nowTime));
                    int minutesNow = nowTime.getMinutes();



                    Date businessTime = HotelUtils.parseStr2Date(HotelUtils.currentDate(nowTime) + " " + autoTime + ":00");

                    //夜审时间的 时分
                    int hours = Integer.parseInt(sf.format(businessTime));
                    int minutes = businessTime.getMinutes();

                    int now = hoursNow * 60 + minutesNow;
                    int auto = hours * 60 + minutes;
                    //现在时间的小时大于 夜审时间的时间 则夜审时间是次日
                    businessTime = HotelUtils.parseStr2Date(HotelUtils.currentDate(nowTime) + " " + autoTime + ":00");
                    if (now > auto) {
                        businessTime = HotelUtils.parseStr2Date(HotelUtils.currentDate(HotelUtils.addDayGetNewDate(nowTime, 1)) + " " + autoTime + ":00");
                    }
                    log.info(HotelUtils.parseDate2Str(businessTime));
                    long l = businessTime.getTime() - auditEnd.getTime();
                    // 两次夜审间隔小于14小时不自动夜审
                    if (l < 50400000) {
                        throw new Exception("离上次夜审时间小于14个小时,不能修改营业日");
                    }
                }
            }


            nightAutoSetting = new NightAutoSetting();
            HotelUtils.classCopy(param, nightAutoSetting);

            // 等于1 说明是自动夜审
            if( 1==nightAutoSetting.getAutoNight()){
                // 先删除原有表达式

                // 夜审时间，改为 corn 表达式
                String[] split = nightAutoSetting.getAutoTime().split(":");
                String corn = "0 " + Integer.parseInt(split[1]) + " " + Integer.parseInt(split[0]) + " * * ? ";
                log.info("添加 自动夜审 corn 表达式为    {}",corn);
                // 添加对应的定时任务
                HashMap<String, Object> jobData = new HashMap<>();
                jobData.put("hid",nightAutoSetting.getHid());
                JobName.nightAutoSettingJob(corn,jobData);
            }

            // 如果是添加 先添加对应的定时任务
            if (null == param.getId() || param.getId() < 0) {

                nightAutoSetting.setHid(user.getHid());
                nightAutoSetting.setHotelGroupId(user.getHotelGroupId());
                res = nightAutoSettingDao.insert(nightAutoSetting);
            } else {
                res = nightAutoSettingDao.update(nightAutoSetting);
            }
            if (res < 1) {
                throw new Exception("操作失败");
            }
            responseData.setData(nightAutoSetting);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }
        return responseData;
    }

    @Override
    public ResponseData addOrUpdateHotelBusinessDay(AddOrUpdateHotelBusinessDayRequest addOrUpdateHotelBusinessDayRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = addOrUpdateHotelBusinessDayRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Oprecord oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setOperator(user.getUserName());
            int dateNum = Integer.parseInt(HotelUtils.currentDate().replace("-", ""));
            //营业日期可以超过当前日期一天
            if (addOrUpdateHotelBusinessDayRequest.getBusinessDay() - dateNum > 1) {
                throw new Exception("营业日期不能超过当前日期");
            }
            /**
             * 首先查询，当前营业日内有没有账务的产生
             */
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setBusinessDay(user.getBusinessDay());
            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            if (accounts != null && accounts.size() > 1) {
                throw new Exception("当前营业日内已经产生账务信息,不允许修改");
            }
            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setBusinessDay(user.getBusinessDay());
            List<Regist> regists = registDao.selectBySearch(registSearch);
            if (regists != null && regists.size() > 1) {
                throw new Exception("当前营业日内已经产生登记信息,不允许修改");
            }
            BookingOrderSearch bookingOrderSearch = new BookingOrderSearch();
            bookingOrderSearch.setHid(user.getHid());
            bookingOrderSearch.setBusinessDay(user.getBusinessDay());
            List<BookingOrder> bookingOrders = bookingOrderDao.selectBySearch(bookingOrderSearch);
            if (bookingOrders != null && bookingOrders.size() > 1) {
                throw new Exception("当前营业日内已经产生预订信息,不允许修改");
            }
            int updateValue = 0;
            /**
             * id 为空说明是添加
             *    不为空说明是修改
             */
            HotelBusinessDay hotelBusinessDay = new HotelBusinessDay();
            Integer newBus = Integer.parseInt(HotelUtils.currentDate().replace("-", ""));
            if (addOrUpdateHotelBusinessDayRequest.getHotelBusinessDayId() == null) {
                hotelBusinessDay.setHid(user.getHid());
                hotelBusinessDay.setBusinessDay(newBus);
                updateValue = hotelBusinessDayDao.insert(hotelBusinessDay);
            } else {
                hotelBusinessDay.setHotelBusinessDayId(addOrUpdateHotelBusinessDayRequest.getHotelBusinessDayId());
                hotelBusinessDay.setBusinessDay(addOrUpdateHotelBusinessDayRequest.getBusinessDay());
                if (addOrUpdateHotelBusinessDayRequest.getBusinessDay() == null) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                    Date parse = sdf.parse(user.getBusinessDay().toString());
                    Date date = HotelUtils.addDayGetNewDate(parse, 1);
                    String businessDay = sdf.format(date);
                    newBus = Integer.parseInt(businessDay);
                    hotelBusinessDay.setBusinessDay(newBus);
                }
                updateValue = hotelBusinessDayDao.update(hotelBusinessDay);
            }


            if (updateValue < 1) {
                throw new Exception("修改失败，请稍后重试");
            }
            oprecord.setDescription("修改酒店营业日期:" + hotelBusinessDay.getBusinessDay());
            oprecord.setHid(user.getHid());
            oprecord.setBusinessShiftName(user.getClassId().toString());
            oprecord.setBusinessShiftId(user.getClassId());
            oprecord.setBusinessDay(user.getBusinessDay());
            this.addOprecords(oprecord);

            //更新自助机小程序等所有的营业日
            final Integer businessDay = hotelBusinessDay.getBusinessDay();
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        HashMap<String, String> stringStringHashMap = new HashMap<>();
                        baseService.push(user.getHotelGroupId(), user.getHid(), PUSH_CONFIG_ID.NIGHT_OVER, stringStringHashMap, new HashMap<String, String>(), true, true);
                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        baseService.turnAlways(user);
                        UpdateHotelUserRequest updateHotelUserRequest = new UpdateHotelUserRequest();
                        updateHotelUserRequest.setHid(user.getHid());
                        updateHotelUserRequest.setBusinessDay(businessDay);
                        baseService.updateHotelAllSession(updateHotelUserRequest);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("",e);
                        UpdateHotelUserRequest updateHotelUserRequest = new UpdateHotelUserRequest();
                        updateHotelUserRequest.setHid(user.getHid());
                        updateHotelUserRequest.setBusinessDay(businessDay);
                        baseService.updateHotelAllSession(updateHotelUserRequest);
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }
        return responseData;
    }

    @Override
    public ResponseData getHotelBusinessDay(BaseRequest baseRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = baseRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
            hotelBusinessDaySearch.setHid(user.getHid());
            hotelBusinessDaySearch.setBusinessDay(user.getBusinessDay());
            HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);
            responseData.setData(hotelBusinessDay);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }
        return responseData;
    }


}
