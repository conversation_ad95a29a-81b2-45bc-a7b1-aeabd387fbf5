package com.pms.czabsnight.service.machine.impl;

import com.github.pagehelper.Page;
import com.pms.czabsnight.bean.AddArAccount;
import com.pms.czabsnight.service.machine.MachineInterface;
import com.pms.czabsnight.service.order.PmsOrderService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountThirdPayRecode;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.bean.account.search.AccountThirdPayRecodeSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czaccount.dao.account.AccountThirdPayRecodeDao;
import com.pms.czaccount.service.account.AccountCanService;
import com.pms.czhotelfoundation.bean.hotel.HotelHourRoomType;
import com.pms.czhotelfoundation.bean.hotel.HourRoomTypeInfo;
import com.pms.czhotelfoundation.bean.hotel.search.HotelHourRoomTypeSearch;
import com.pms.czhotelfoundation.bean.hotel.search.HourRoomTypeInfoSearch;
import com.pms.czhotelfoundation.bean.hotel.search.ParamSearch;
import com.pms.czhotelfoundation.bean.price.RoomDayPrice;
import com.pms.czhotelfoundation.bean.price.RoomRateCode;
import com.pms.czhotelfoundation.bean.price.RoomRateCodeSpecific;
import com.pms.czhotelfoundation.bean.price.search.RoomDayPriceSearch;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSearch;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSpecificSearch;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomType;
import com.pms.czhotelfoundation.bean.room.RoomTypeImage;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeImageSearch;
import com.pms.czhotelfoundation.dao.hotel.HotelHourRoomTypeDao;
import com.pms.czhotelfoundation.dao.hotel.HourRoomTypeInfoDao;
import com.pms.czhotelfoundation.dao.hotel.ParamDao;
import com.pms.czhotelfoundation.dao.price.RoomDayPriceDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeSpecificDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomRepairRecordDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeImageDao;
import com.pms.czhotelfoundation.service.room.RoomService;
import com.pms.czmembership.bean.company.HotelCompanyAccount;
import com.pms.czmembership.bean.company.HotelCompanyAccountInfo;
import com.pms.czmembership.bean.company.HotelCompanyArRecode;
import com.pms.czmembership.bean.company.HotelCompanyInfo;
import com.pms.czmembership.bean.member.CardInfo;
import com.pms.czmembership.dao.company.HotelCompanyAccountDao;
import com.pms.czmembership.dao.company.HotelCompanyAccountInfoDao;
import com.pms.czmembership.dao.company.HotelCompanyArRecodeDao;
import com.pms.czmembership.dao.company.HotelCompanyInfoDao;
import com.pms.czmembership.service.memeber.MemberService;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.hotelsetting.HOTEL_SETTING;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.*;
import com.pms.czpmsutils.view.HotelRoomTypeView;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.request.BookingOrderPageRequest;
import com.pms.pmsorder.bean.search.*;
import com.pms.pmsorder.dao.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Primary
@Slf4j
public class MachineInterfaceImpl extends BaseService implements MachineInterface {

    @Autowired
    RegistPersonHealthCodeDao registPersonHealthCodeDao;

    @Autowired
    RoomInfoDao roomInfoDao;

    @Autowired
    RegistDao registDao;

    @Autowired
    RegistPersonDao registPersonDao;

    @Autowired
    BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    RoomRepairRecordDao roomRepairRecordDao;

    @Autowired
    AccountDao accountDao;

    @Autowired
    RoomTypeDao roomTypeDao;

    @Autowired
    ParamDao paramDao;

    @Autowired
    RoomDayPriceDao roomDayPriceDao;

    @Autowired
    private RoomService roomService;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private HotelHourRoomTypeDao hotelHourRoomTypeDao;

    @Autowired
    private HourRoomTypeInfoDao hourRoomTypeInfoDao;


    @Autowired
    private PmsOrderService pmsOrderService;

    @Autowired
    private RoomRateCodeSpecificDao roomRateCodeSpecificDao;

    @Autowired
    private HotelCompanyAccountDao hotelCompanyAccountDao;

    @Autowired
    private HotelCompanyAccountInfoDao hotelCompanyAccountInfoDao;

    @Autowired
    private AccountCanService accountCanService;

    @Autowired
    private MemberService memberService;

    @Autowired
    private HotelCompanyArRecodeDao hotelCompanyArRecodeDao;

    @Autowired
    private HotelCompanyInfoDao hotelCompanyInfoDao;

    @Autowired
    private RegistTeamDao registTeamDao;

    private BaseService baseService = this;

    @Autowired
    private RoomTypeImageDao roomTypeImageDao;

    @Autowired
    private PersonInfoDao personInfoDao;

    @Autowired
    private AccountThirdPayRecodeDao accountThirdPayRecodeDao;

    @Autowired
    private RoomRateCodeDao roomRateCodeDao;

    @Override
    public JSONObject getRoomNoLockCode(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject getRoomInfo(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            roomInfoSearch.setState(1);
            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
            JSONArray roomList = new JSONArray();
            for (int i = 0; i < roomInfos.size(); i++) {
                JSONObject roomInfo = new JSONObject();
                roomInfo.put("RoomNo", roomInfos.get(i).getRoomNum());
                roomInfo.put("RoomInfoId", roomInfos.get(i).getRoomInfoId());
                roomInfo.put("RoomTypeId", roomInfos.get(i).getRoomTypeId());
                roomInfo.put("RoomTypeName", roomInfos.get(i).getRoomTypeName());
                roomInfo.put("BuildingId", roomInfos.get(i).getBuildingId());
                roomInfo.put("BuildingName", roomInfos.get(i).getBuildingName());
                roomInfo.put("FloorId", roomInfos.get(i).getFloorId());
                roomInfo.put("FloorName", roomInfos.get(i).getFloorName());
                roomInfo.put("LockNo", roomInfos.get(i).getLockNum());
                roomInfo.put("RoomState", roomInfos.get(i).getRoomNumState());
                roomList.add(roomInfo);
            }
            resultMap.put("RoomList", roomList);
            resultMap.put("Result", "True");
            resultMap.put("Msg", "查询成功");
        } catch (Exception e) {
            log.error("",e);
            e.printStackTrace();
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
        }

        return resultMap;
    }

    @Override
    public JSONObject getHotelBusinessDay(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);
            Integer businessDay = user.getBusinessDay();
            String day = HotelUtils.businessDay2Str(businessDay);
            resultMap.put("Result", "True");
            resultMap.put("Data", day);
        } catch (Exception e) {
            log.error("",e);
            e.printStackTrace();
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
        }
        return resultMap;
    }

    @Override
    public JSONObject getBuildFloorRoom(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject getRoomTypeInfoList(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);

            if (!map.containsKey("BeginTime") || map.get("BeginTime").toString().equals("")) {
                map.put("BeginTime", HotelUtils.currentTime().substring(0, 10));
            }
            if (!map.containsKey("EndTime") || map.get("EndTime").toString().equals("")) {
                resultMap.put("Msg", "退房时间不能为空");
                return resultMap;
            }
            if (!map.containsKey("PriceCode") || map.get("PriceCode").toString().equals("")) {
                resultMap.put("Msg", "房价码不能空");
                return resultMap;
            }
            if (!map.containsKey("CheckInType") || map.get("CheckInType").toString().equals("")) {
                resultMap.put("Msg", "入住类型不能空");
                return resultMap;
            }

            RoomTypeImageSearch roomTypeImageSearch = new RoomTypeImageSearch();
            roomTypeImageSearch.setHid(user.getHid());
            List<RoomTypeImage> roomTypeImages = roomTypeImageDao.selectBySearch(roomTypeImageSearch);
            Map<Integer, List<RoomTypeImage>> imgMap = roomTypeImages.stream().collect(Collectors.groupingBy(RoomTypeImage::getRoomTypeId));

            String checkInType = map.get("CheckInType").toString();
            //首先查询酒店所有房型信息
            ParamSearch paramSearch = new ParamSearch();
            paramSearch.setHid(user.getHid());
            List<HotelRoomTypeView> hotelRoomTypeList = paramDao.getHotelRoomTypeList(paramSearch);

            JSONObject roomTypeMap = new JSONObject();
            List<HotelRoomTypeView> hotelRoomTypeViews = new ArrayList<>();
            for (int i = 0; i < hotelRoomTypeList.size(); i++) {
                if (hotelRoomTypeList.get(i).getState() == 1) {
                    hotelRoomTypeViews.add(hotelRoomTypeList.get(i));
                    roomTypeMap.put(hotelRoomTypeList.get(i).getRoomTypeId(), hotelRoomTypeList.get(i));
                }
            }

            if (checkInType.equals("7")) {
                checkInType = "1";
                map.put("BeginTime", HotelUtils.parseDate2Str(HotelUtils.addDayGetNewDate(HotelUtils.parseStr2Date(map.get("BeginTime").toString()), -1)));
            }

            //日租房
            if (checkInType.equals("1")) {
                String startTime = map.get("BeginTime").toString();
                String endTime = map.get("EndTime").toString();


                //查询酒店的价格信息
                RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
                roomDayPriceSearch.setHid(user.getHid());
                roomDayPriceSearch.setRoomRateId(Integer.parseInt(map.get("PriceCode").toString()));
                roomDayPriceSearch.setDayTimeMin(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(startTime.toString())));
                roomDayPriceSearch.setDayTimeMax(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(endTime.toString())));
                List<RoomDayPrice> roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);

                Map<String, List<RoomDayPrice>> roomTypePriceMap = new HashMap<>();
                for (RoomDayPrice roomDayPrice : roomDayPrices) {
                    Integer roomTypeId = roomDayPrice.getRoomTypeId();
                    if (roomTypePriceMap.containsKey(roomTypeId.toString())) {
                        roomTypePriceMap.get(roomTypeId.toString()).add(roomDayPrice);
                    } else {
                        List<RoomDayPrice> roomDayPriceList = new ArrayList<>();
                        roomDayPriceList.add(roomDayPrice);
                        roomTypePriceMap.put(roomTypeId.toString(), roomDayPriceList);
                    }
                }
                log.info(roomTypePriceMap.toString());
                Map<Integer, RoomDayPrice> dayPriceMap = roomDayPrices.stream().collect(Collectors.toMap(RoomDayPrice::getRoomTypeId, a -> a, (k1, k2) -> k2));
                // 3.获取两个日期差
                List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(HotelUtils.parseStr2Date(startTime.toString())).substring(0, 10), HotelUtils.parseDate2Str(HotelUtils.parseStr2Date(endTime.toString())).substring(0, 10));
                //查询酒店房型的可售信息
                AvailableRoom availableRoom = new AvailableRoom();
                availableRoom.setStartTime(startTime);
                availableRoom.setEndTime(endTime);
                availableRoom.setType(2);
                availableRoom.setSessionToken(tokenId);
                Map<String, Object> canUserRoomType = this.findavailableRoom(availableRoom);
                log.info(canUserRoomType.toString());
                JSONObject canUseRoomMap = JSONObject.fromObject(canUserRoomType.get("canUseRoomMap"));
                JSONObject canUseRoomNum = JSONObject.fromObject(canUserRoomType.get("canUseRoomNum"));
                JSONObject trrb = JSONObject.fromObject(canUserRoomType.get("trrb"));
                if (!canUserRoomType.get("Result").toString().equals("Success")) {
                    throw new Exception("查询酒店房型信息失败");
                }
                JSONArray roomTypeList = new JSONArray();
                for (int i = 0; i < hotelRoomTypeViews.size(); i++) {
                    JSONObject roomType = new JSONObject();
                    roomType.put("RoomTypeID", hotelRoomTypeViews.get(i).getRoomTypeId());
                    roomType.put("RoomType", hotelRoomTypeViews.get(i).getRoomTypeName());
                    int roomNum;
                    if (!canUseRoomNum.containsKey(hotelRoomTypeViews.get(i).getRoomTypeId().toString())) {
                        roomNum = 0;
                    } else {
                        roomNum = canUseRoomNum.getInt(hotelRoomTypeViews.get(i).getRoomTypeId().toString());
                    }
                    roomType.put("RoomNum", roomNum);
                    roomType.put("LeaveTime", "12:00");
                    roomType.put("BreakFast", "0");
                    roomType.put("Deposit", hotelRoomTypeViews.get(i).getDeposit());
                    roomType.put("RoomTypeDesc", hotelRoomTypeViews.get(i).getRoomTypeNameEn());
                    roomType.put("Demo", hotelRoomTypeViews.get(i).getDes());
                    roomType.put("Hours", 4);
                    roomType.put("HalfDayPrice", "0");
                    roomType.put("HourPrice", 0);
                    ArrayList<String> strings = new ArrayList<>();
                    List<RoomTypeImage> rtis = imgMap.get(hotelRoomTypeViews.get(i).getRoomTypeId());
                    if (rtis != null) {
                        for (RoomTypeImage r : rtis) {
                            if (r.getUrl() == null || r.getUrl().length() < 2) {
                                continue;
                            }
                            strings.add(r.getUrl());
                        }
                    }
                    roomType.put("imgs", strings);

                    JSONObject priceMsg = new JSONObject();
                    JSONArray priceList = new JSONArray();
                    double SumPrice = 0.00;
                    for (String date : allDayListBetweenDate) {
                        if (roomTypePriceMap.containsKey(hotelRoomTypeViews.get(i).getRoomTypeId().toString())) {
                            List<RoomDayPrice> roomDayPriceList = roomTypePriceMap.get(hotelRoomTypeViews.get(i).getRoomTypeId().toString());
                            for (int j = 0; j < roomDayPriceList.size(); j++) {
                                if (date.replace("-", "").equals(roomDayPriceList.get(j).getDayTime().toString())) {
                                    JSONObject priceInfo = new JSONObject();
                                    priceInfo.put("Date", date + " 00:00:00");
                                    priceInfo.put("DayPrice", roomDayPriceList.get(j).getPrice() / 100.0);
                                    SumPrice += roomDayPriceList.get(j).getPrice() / 100.0;
                                    priceList.add(priceInfo);
                                    continue;
                                }
                            }
                        }
                        //每日房价中不包含房型房价信息
                        else {
                            JSONObject priceInfo = new JSONObject();
                            priceInfo.put("Date", date + " 00:00:00");
                            double v = hotelRoomTypeViews.get(i).getPrice() / 100.0;
                            priceInfo.put("DayPrice", v);
                            SumPrice += v;
                            priceList.add(priceInfo);
                        }
                    }
                    priceMsg.put("PriceCode", map.get("PriceCode").toString());
                    priceMsg.put("SumPrice", SumPrice);
                    priceMsg.put("PriceList", priceList);
                    roomType.put("PriceMsg", priceMsg);
                    roomTypeList.add(roomType);
                }
                resultMap.put("RoomTypeList", roomTypeList);
                resultMap.put("Result", "True");
                resultMap.put("Msg", "查询房型信息成功");
                log.info(resultMap.toString());
            }
            //钟点房
            else if (checkInType.equals("2")) {
                RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
                roomInfoSearch.setRoomNumState(1);
                roomInfoSearch.setHid(user.getHid());
                List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
                Map<Integer, List<RoomInfo>> roomNumMap = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomTypeId));
                //查询当前酒店开钟点房的房型列表
                HotelHourRoomTypeSearch hotelHourRoomTypeSearch = new HotelHourRoomTypeSearch();
                hotelHourRoomTypeSearch.setHid(user.getHid());
                //    hotelHourRoomTypeSearch.setHourRoomInfoId(Integer.parseInt(map.get("PriceCode").toString()));
                Integer HourCode = map.containsKey("HourCode") ? Integer.parseInt(map.get("HourCode").toString()) : 4;
                hotelHourRoomTypeSearch.setHourRoomCode(HourCode);
                List<HotelHourRoomType> hotelHourRoomTypes = hotelHourRoomTypeDao.selectBySearch(hotelHourRoomTypeSearch);
                Map<Integer, List<HotelHourRoomType>> hotelHourRoomTypeMap = hotelHourRoomTypes.stream().collect(Collectors.groupingBy(HotelHourRoomType::getRoomTypeId));
                HourRoomTypeInfoSearch hourRoomTypeInfoSearch = new HourRoomTypeInfoSearch();
                hourRoomTypeInfoSearch.setHid(user.getHid());
                hourRoomTypeInfoSearch.setHourRoomId(hotelHourRoomTypes.get(0).getHourRoomInfoId());
                Page<HourRoomTypeInfo> hourRoomTypeInfos = hourRoomTypeInfoDao.selectBySearch(hourRoomTypeInfoSearch);
                JSONArray roomTypeList = new JSONArray();
                for (int i = 0; i < hourRoomTypeInfos.size(); i++) {
                    JSONObject roomType = new JSONObject();
                    roomType.put("RoomTypeID", hotelHourRoomTypeMap.get(hourRoomTypeInfos.get(i).getRoomTypeId()).get(0).getRoomTypeId());
                    log.info(String.valueOf(roomTypeMap.getJSONObject(hourRoomTypeInfos.get(i).getRoomTypeId().toString())));
                    roomType.put("RoomType", roomTypeMap.getJSONObject(hourRoomTypeInfos.get(i).getRoomTypeId().toString()).getString("roomTypeName"));
                    roomType.put("RoomNum", roomNumMap.containsKey(hourRoomTypeInfos.get(i).getRoomTypeId()) ? roomNumMap.get(hourRoomTypeInfos.get(i).getRoomTypeId()).size() : 0);
                    roomType.put("LeaveTime", "12:00");
                    roomType.put("BreakFast", "0");
                    roomType.put("Deposit", 0);
                    roomType.put("RoomTypeDesc", "");
                    roomType.put("Demo", "");
                    ArrayList<String> strings = new ArrayList<>();
                    List<RoomTypeImage> rtis = imgMap.get(hourRoomTypeInfos.get(i).getRoomTypeId());
                    if (rtis != null) {
                        for (RoomTypeImage r : rtis) {
                            if (r.getUrl() == null || r.getUrl().length() < 2) {
                                continue;
                            }
                            strings.add(r.getUrl());
                        }
                    }
                    roomType.put("imgs", strings);
                    roomType.put("Hours", hotelHourRoomTypeMap.get(hourRoomTypeInfos.get(i).getRoomTypeId()).get(0).getHourRoomCode());
                    roomType.put("HalfDayPrice", "0");
                    roomType.put("HourPrice", 0);
                    JSONObject priceMsg = new JSONObject();
                    JSONArray priceList = new JSONArray();
                    JSONObject priceInfo = new JSONObject();
                    priceInfo.put("Data", map.get("BeginTime").toString());
                    priceInfo.put("DayPrice", hotelHourRoomTypeMap.get(hourRoomTypeInfos.get(i).getRoomTypeId()).get(0).getPrice() / 100.0);
                    priceList.add(priceInfo);
                    priceMsg.put("PriceList", priceList);
                    priceMsg.put("PriceCode", hotelHourRoomTypeMap.get(hourRoomTypeInfos.get(i).getRoomTypeId()).get(0).getHourRoomCode());
                    priceMsg.put("SumPrice", hotelHourRoomTypeMap.get(hourRoomTypeInfos.get(i).getRoomTypeId()).get(0).getPrice() / 100.0);
                    priceMsg.put("PriceList", priceList);
                    roomType.put("PriceMsg", priceMsg);

                    roomTypeList.add(roomType);
                }
                resultMap.put("RoomTypeList", roomTypeList);
                resultMap.put("Result", "True");
                resultMap.put("Msg", "查询房型信息成功");
                log.info(resultMap.toString());
            }
        } catch (Exception e) {
            log.error("",e);
            e.printStackTrace();
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
        }
        return resultMap;
    }

    @Override
    public JSONObject getRoomTypePrice(JSONObject postData) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = postData.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);

            if (!postData.containsKey("BeginTime") || postData.get("BeginTime").toString().equals("")) {
                postData.put("BeginTime", HotelUtils.currentTime().substring(0, 10));
            }
            if (!postData.containsKey("EndTime") || postData.get("EndTime").toString().equals("")) {
                resultMap.put("Msg", "退房时间不能为空");
                return resultMap;
            }
            if (!postData.containsKey("PriceCode") || postData.get("PriceCode").toString().equals("")) {
                resultMap.put("Msg", "房价码不能空");
                return resultMap;
            }
            if (!postData.containsKey("CheckInType") || postData.get("CheckInType").toString().equals("")) {
                resultMap.put("Msg", "入住类型不能空");
                return resultMap;
            }

            RoomTypeImageSearch roomTypeImageSearch = new RoomTypeImageSearch();
            roomTypeImageSearch.setHid(user.getHid());
            List<RoomTypeImage> roomTypeImages = roomTypeImageDao.selectBySearch(roomTypeImageSearch);
            Map<Integer, List<RoomTypeImage>> imgMap = roomTypeImages.stream().collect(Collectors.groupingBy(RoomTypeImage::getRoomTypeId));

            //首先查询酒店所有房型信息
            ParamSearch paramSearch = new ParamSearch();
            paramSearch.setHid(user.getHid());
            List<HotelRoomTypeView> hotelRoomTypeList = paramDao.getHotelRoomTypeList(paramSearch);

            JSONObject roomTypeMap = new JSONObject();
            List<HotelRoomTypeView> hotelRoomTypeViews = new ArrayList<>();
            for (int i = 0; i < hotelRoomTypeList.size(); i++) {
                if (hotelRoomTypeList.get(i).getState() == 1) {
                    hotelRoomTypeViews.add(hotelRoomTypeList.get(i));
                    roomTypeMap.put(hotelRoomTypeList.get(i).getRoomTypeId(), hotelRoomTypeList.get(i));
                }
            }
            String startTime = postData.get("BeginTime").toString();
            String endTime = postData.get("EndTime").toString();

            RoomRateCodeSearch roomRateCodeSearch = new RoomRateCodeSearch();
            roomRateCodeSearch.setHid(user.getHid());
            roomRateCodeSearch.setRateCode(postData.get("PriceCode").toString());
            Page<RoomRateCode> roomRateCodes = roomRateCodeDao.selectBySearch(roomRateCodeSearch);
            if (null == roomRateCodes || roomRateCodes.size() != 1) {
                throw new Exception("房价码不明确");
            }

            //查询酒店的价格信息
            RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
            roomDayPriceSearch.setHid(user.getHid());
            roomDayPriceSearch.setRoomRateId(roomRateCodes.get(0).getRateId());
            roomDayPriceSearch.setDayTimeMin(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(startTime.toString())));
            roomDayPriceSearch.setDayTimeMax(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(endTime.toString())));
            List<RoomDayPrice> roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);

            Map<String, List<RoomDayPrice>> roomTypePriceMap = new HashMap<>();
            for (RoomDayPrice roomDayPrice : roomDayPrices) {
                Integer roomTypeId = roomDayPrice.getRoomTypeId();
                if (roomTypePriceMap.containsKey(roomTypeId.toString())) {
                    roomTypePriceMap.get(roomTypeId.toString()).add(roomDayPrice);
                } else {
                    List<RoomDayPrice> roomDayPriceList = new ArrayList<>();
                    roomDayPriceList.add(roomDayPrice);
                    roomTypePriceMap.put(roomTypeId.toString(), roomDayPriceList);
                }
            }

            Map<Integer, RoomDayPrice> dayPriceMap = roomDayPrices.stream().collect(Collectors.toMap(RoomDayPrice::getRoomTypeId, a -> a, (k1, k2) -> k2));
            // 3.获取两个日期差
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(HotelUtils.parseStr2Date(startTime.toString())).substring(0, 10), HotelUtils.parseDate2Str(HotelUtils.parseStr2Date(endTime.toString())).substring(0, 10));
            //查询酒店房型的可售信息
            AvailableRoom availableRoom = new AvailableRoom();
            availableRoom.setStartTime(startTime);
            availableRoom.setEndTime(endTime);
            availableRoom.setType(1);
            availableRoom.setSessionToken(tokenId);
            Map<String, Object> canUserRoomType = this.findavailableRoom(availableRoom);
            log.info(canUserRoomType.toString());
            JSONObject canUseRoomMap = JSONObject.fromObject(canUserRoomType.get("canUseRoomMap"));
            JSONObject trrb = JSONObject.fromObject(canUserRoomType.get("trrb"));
            if (!canUserRoomType.get("Result").toString().equals("Success")) {
                throw new Exception("查询酒店房型信息失败");
            }
            JSONArray roomTypeList = new JSONArray();
            for (int i = 0; i < hotelRoomTypeViews.size(); i++) {
                JSONObject roomType = new JSONObject();
                roomType.put("RoomTypeID", hotelRoomTypeViews.get(i).getRoomTypeId());
                roomType.put("RoomType", hotelRoomTypeViews.get(i).getRoomTypeName());
                int roomNum;
                if (!canUseRoomMap.containsKey(hotelRoomTypeViews.get(i).getRoomTypeId().toString())) {
                    roomNum = 0;
                } else {
                    roomNum = canUseRoomMap.getJSONArray(hotelRoomTypeViews.get(i).getRoomTypeId().toString()).size() - trrb.getInt(hotelRoomTypeViews.get(i).getRoomTypeId().toString());
                }
                roomType.put("RoomNum", roomNum);
                roomType.put("LeaveTime", "12:00");
                roomType.put("BreakFast", "0");
                roomType.put("Deposit", hotelRoomTypeViews.get(i).getDeposit());
                roomType.put("RoomTypeDesc", hotelRoomTypeViews.get(i).getRoomTypeNameEn());
                roomType.put("Demo", hotelRoomTypeViews.get(i).getDes());
                roomType.put("Hours", 4);
                roomType.put("HalfDayPrice", "0");
                roomType.put("HourPrice", 0);
                ArrayList<String> strings = new ArrayList<>();
                List<RoomTypeImage> rtis = imgMap.get(hotelRoomTypeViews.get(i).getRoomTypeId());
                if (rtis != null) {
                    for (RoomTypeImage r : rtis) {
                        if (r.getUrl() == null || r.getUrl().length() < 2) {
                            continue;
                        }
                        strings.add(r.getUrl());
                    }
                }
                roomType.put("imgs", strings);

                JSONObject priceMsg = new JSONObject();
                JSONArray priceList = new JSONArray();
                double SumPrice = 0.00;
                for (String date : allDayListBetweenDate) {
                    if (roomTypePriceMap.containsKey(hotelRoomTypeViews.get(i).getRoomTypeId().toString())) {
                        List<RoomDayPrice> roomDayPriceList = roomTypePriceMap.get(hotelRoomTypeViews.get(i).getRoomTypeId().toString());
                        for (int j = 0; j < roomDayPriceList.size(); j++) {
                            if (date.replace("-", "").equals(roomDayPriceList.get(j).getDayTime().toString())) {
                                JSONObject priceInfo = new JSONObject();
                                priceInfo.put("Date", date + " 00:00:00");
                                priceInfo.put("DayPrice", roomDayPriceList.get(j).getPrice() / 100.0);
                                SumPrice += roomDayPriceList.get(j).getPrice() / 100.0;
                                priceList.add(priceInfo);
                                continue;
                            }
                        }
                    }
                    //每日房价中不包含房型房价信息
                    else {
                        JSONObject priceInfo = new JSONObject();
                        priceInfo.put("Date", date + " 00:00:00");
                        double v = hotelRoomTypeViews.get(i).getPrice() / 100.0;
                        priceInfo.put("DayPrice", v);
                        SumPrice += v;
                        priceList.add(priceInfo);
                    }
                }
                priceMsg.put("PriceCode", postData.get("PriceCode").toString());
                priceMsg.put("SumPrice", SumPrice);
                priceMsg.put("PriceList", priceList);
                roomType.put("PriceMsg", priceMsg);
                roomTypeList.add(roomType);
            }
            resultMap.put("RoomTypeList", roomTypeList);
            resultMap.put("Result", "True");
            resultMap.put("Msg", "查询房型信息成功");
            log.info(resultMap.toString());
        } catch (Exception e) {
            log.error("",e);
            e.printStackTrace();
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
        }
        return resultMap;
    }

    @Override
    public JSONObject getAvailRoomList(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);

            if (!map.containsKey("BeginTime") || map.get("BeginTime").toString().equals("")) {
                map.put("BeginTime", HotelUtils.currentTime().substring(0, 10));
            }
            if (!map.containsKey("EndTime") || map.get("EndTime").toString().equals("")) {
                throw new Exception("退房时间不能为空");
            }
            if (!map.containsKey("RoomTypeID") || map.get("RoomTypeID").toString().equals("")) {
                throw new Exception("房型ID不能为空");
            }
            if (!map.containsKey("CheckInType") || map.get("CheckInType").toString().equals("")) {
                map.put("CheckInType", "1");
            }
            String checkInType = map.get("CheckInType").toString();
            JSONArray roomInfoList = new JSONArray();
            if (checkInType.equals("1")) {
                String startTime = map.get("BeginTime").toString();
                String endTime = map.get("EndTime").toString();
                //查询酒店房型的可售信息
                AvailableRoom availableRoom = new AvailableRoom();
                availableRoom.setStartTime(startTime);
                availableRoom.setEndTime(endTime);
                availableRoom.setType(1);
                availableRoom.setSessionToken(tokenId);
                Map<String, Object> canUserRoomType = this.findavailableRoom(availableRoom);
                log.info(canUserRoomType.toString());
                JSONObject canUseRoomMap = JSONObject.fromObject(canUserRoomType.get("canUseRoomMap"));
                JSONObject trrb = JSONObject.fromObject(canUserRoomType.get("trrb"));
                if (!canUserRoomType.get("Result").toString().equals("Success")) {
                    throw new Exception("查询酒店可用房间信息失败");
                }
                String roomTypeID = map.get("RoomTypeID").toString();
                JSONArray roomList = JSONArray.fromObject(canUseRoomMap.get(roomTypeID));
                for (int i = 0; i < roomList.size(); i++) {
                    JSONObject roomInfo = roomList.getJSONObject(i);
                    JSONObject room = new JSONObject();
                    room.put("RoomTypeID", roomInfo.get("roomTypeId").toString());
                    room.put("RoomNo", roomInfo.get("roomNum").toString());
                    room.put("Building", roomInfo.get("buildingName") != null && !roomInfo.get("buildingName").toString().equals("null") ? roomInfo.get("buildingName").toString() : "");
                    room.put("Floor", roomInfo.get("floorName") != null && !roomInfo.get("floorName").toString().equals("null") ? roomInfo.get("floorName").toString() : "");
                    room.put("BedNum", "");
                    room.put("ComputerAble", "");
                    room.put("Spec", "");
                    room.put("LockCode", (roomInfo.get("lockNum") != null && !roomInfo.getString("lockNum").equals("null")) ? roomInfo.getString("lockNum") : "");
                    room.put("GuestNo", roomInfo.get("guestNum") != null && !roomInfo.get("guestNum").toString().equals("null") ? roomInfo.get("guestNum").toString() : "");
                    room.put("Memo", "");
                    roomInfoList.add(room);
                }
            } else if (checkInType.equals("2")) {
                RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
                roomInfoSearch.setHid(user.getHid());
                roomInfoSearch.setRoomNumState(1);
                roomInfoSearch.setState(1);
                roomInfoSearch.setRoomTypeId(Integer.parseInt(map.get("RoomTypeID").toString()));
                List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
                for (int i = 0; i < roomInfos.size(); i++) {
                    RoomInfo roomInfo = roomInfos.get(i);
                    JSONObject room = new JSONObject();
                    room.put("RoomTypeID", roomInfo.getRoomTypeId());
                    room.put("RoomNo", roomInfo.getRoomNum());
                    room.put("Building", roomInfo.getBuildingName());
                    room.put("Floor", roomInfo.getFloorName());
                    room.put("BedNum", "");
                    room.put("ComputerAble", "");
                    room.put("Spec", "");
                    room.put("LockCode", roomInfo.getLockNum());
                    room.put("GuestNo", roomInfo.getGuestNum());
                    room.put("Memo", "");
                    roomInfoList.add(room);
                }
            }
            resultMap.put("Result", "True");
            resultMap.put("Msg", "查询酒店房间成功");
            resultMap.put("RoomList", roomInfoList);
            log.info(resultMap.toString());
        } catch (Exception e) {
            log.error("",e);
            e.printStackTrace();
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
        }
        return resultMap;
    }

    @Override
    public JSONObject lockRoom(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
//            String tokenId = map.get("appKey").toString();
//            //获取tokenId
//            final TbUserSession user = this.getTbUserSession(tokenId);
//            if (!map.containsKey("RoomNo") || map.get("RoomNo").toString().equals("")) {
//                throw new Exception("RoomNo房间号不能为空。");
//            }
//            if (!map.containsKey("LockType") || map.get("LockType").toString().equals("")) {
//                throw new Exception("LockType类型不能为空");
//            }
//            int lockType = Integer.parseInt(map.get("LockType").toString());
//            String roomNo = map.get("RoomNo").toString();
//            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
//            roomInfoSearch.setHid(user.getHid());
//            roomInfoSearch.setRoomNum(roomNo);
//            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
//            if (roomInfos == null || roomInfos.size()!= 1){
//                throw new Exception("房间信息异常");
//            }
//            JSONObject postData = new JSONObject();
//            if (lockType == 1){
//                postData.put("newRoomNumState",6);
//
//            }else {
//                postData.put("newRoomNumState",0);
//            }
//            postData.put("roomInfos",roomInfos);
//            postData.put("sessionToken",tokenId);
//            Map<String, Object> result = roomService.updateRoomsState(postData);
//            if (!result.get("Result").toString().equals("Success")){
//                throw new Exception(lockType == 1?"房间锁定失败":"房间解锁失败");
//            }
//            resultMap.put("Result","True");
//            resultMap.put("Msg",lockType == 1?"房间锁定成功":"房间解锁成功");
        } catch (Exception e) {
            log.error("",e);
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
        }
        resultMap.put("Result", "True");
        return resultMap;
    }

    @Override
    public JSONObject registerCheckIn(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);

            if (!map.containsKey("BeginTime") || map.get("BeginTime").toString().equals("")) {
                map.put("BeginTime", HotelUtils.currentTime());
            }
            if (!map.containsKey("EndTime") || map.get("EndTime").toString().equals("")) {
                throw new Exception("退房时间不能为空");
            }
            if (!map.containsKey("CheckInType") || map.get("CheckInType").toString().equals("")) {
                map.put("CheckInType", "1");
            }
            if (!map.containsKey("RoomNo") || map.get("RoomNo").toString().equals("")) {
                throw new Exception("要入住的房间不能空");
            }

            if (map.get("GuestList") == null) {
                throw new Exception("入住人不能为空！");
            }

            String checkInType = map.get("CheckInType").toString();

            if (checkInType.equals("7")) {
                checkInType = "1";
                String startTime = map.get("BeginTime").toString();
                String endTime = map.get("EndTime").toString();
                if (startTime.substring(0, 10).equals(endTime.substring(0, 10))) {
                    map.put("BeginTime", HotelUtils.parseDate2Str(HotelUtils.addDayGetNewDate(HotelUtils.parseStr2Date(map.get("BeginTime").toString()), -1)));
                }
            }

            //预订转入住
            if (map.get("OrderID") != null && !"".equals(map.get("OrderID").toString())) {
                Integer orderID = Integer.parseInt(map.get("OrderID").toString());

                if (map.get("OrderRoomID") == null || "".equals(map.get("OrderRoomID").toString())) {
                    throw new Exception("小订单编号不能空！");
                }
                int orderRoomID = Integer.parseInt(map.get("OrderRoomID").toString());

                BookingOrderRoomNum bookingOrderRoomNum = bookingOrderRoomNumDao.selectById(orderRoomID);

                if (bookingOrderRoomNum == null) {
                    throw new Exception("小订单编号不能空！");
                }


                if (!bookingOrderRoomNum.getRoomNum().equals(map.get("RoomNo").toString())) {
                    throw new Exception("排房信息有变化，无法办理入住！");
                }

                //查询预订房型信息，主要是获取预订的房价码
                BookingOrder bookingOrder = bookingOrderDao.selectById(orderID);
                JSONArray roomList = new JSONArray();
                JSONArray guestList = new JSONArray();
                JSONArray guestInfoList = JSONArray.fromObject(map.get("GuestList").toString());
                for (int i = 0; i < guestInfoList.size(); i++) {
                    JSONObject guestInfo = new JSONObject();
                    JSONObject guest = guestInfoList.getJSONObject(i);
                    guestInfo.put("idType", 1);
                    guestInfo.put("Code", guest.getString("Code"));
                    guestInfo.put("idCode", guest.getString("Code"));
                    guestInfo.put("personName", guest.getString("Name"));
                    guestInfo.put("sex", guest.getString("Sex").equals("男") ? 0 : 1);
                    guestInfo.put("phone", map.get("Phone").toString());
                    guestInfo.put("guestNo", guest.get("GuestNo"));
                    guestInfo.put("Address", guest.get("Address"));
                    guestInfo.put("address", guest.get("Address"));
                    guestInfo.put("image", guest.get("image"));
                    guestInfo.put("cameraPicture", guest.get("cameraPicture"));
                    guestInfo.put("photo", guest.get("photo"));
                    guestInfo.put("cameraPhoto", guest.get("cameraPhoto"));
                    guestInfo.put("Photo", guest.get("Photo"));
                    guestInfo.put("CameraPhoto", guest.get("CameraPhoto"));
                    guestList.add(guestInfo);
                }
                JSONObject roomInfo = new JSONObject();
                roomInfo.put("bookingRoomId", bookingOrderRoomNum.getId());
                roomInfo.put("roomId", bookingOrderRoomNum.getRoomNumId());
                roomInfo.put("roomNum", bookingOrderRoomNum.getRoomNum());
                roomInfo.put("rateCode", bookingOrder.getRateCodeName());
                roomInfo.put("rateId", bookingOrder.getRateCodeId());

                roomInfo.put("guestList", guestList);
                roomList.add(roomInfo);
                JSONObject requestData = new JSONObject();
                requestData.put("bookingOrderId", orderID);
                requestData.put("macCheckIn", true);
                requestData.put("checkInRoomList", roomList);
                JSONObject postData = new JSONObject();
                postData.put("bookParam", requestData);
                postData.put("sessionToken", tokenId);
                Map<String, Object> res = pmsOrderService.bookingCheckIn(postData);
                if (!res.get("Result").toString().equals(ER.SUCC)) {
                    throw new Exception(res.get(ER.MSG).toString());
                }
                log.info("预订转入住返回:" + res.toString());
                HashMap<Integer, Integer> data = (HashMap<Integer, Integer>) (res.get("data"));
                resultMap.put("Result", "True");
                resultMap.put("Msg", "入住成功");
                resultMap.put("RegistID", data.get(bookingOrderRoomNum.getRoomNumId()));
                resultMap.put("MainID", data.get(bookingOrderRoomNum.getRoomNumId()));
                resultMap.put("EndTime", map.get("EndTime").toString());
                resultMap.put("RoomNo", map.get("RoomNo").toString());
            }
            //散客步入
            else {
                if (!map.containsKey("PriceCode") || map.get("PriceCode").toString().equals("")) {
                    throw new Exception("房价码不能为空");
                }
                //查询房间信息
                String roomNo = map.get("RoomNo").toString();
                RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
                roomInfoSearch.setHid(user.getHid());
                roomInfoSearch.setRoomNum(roomNo);
                roomInfoSearch.setState(1);
                List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
                if (roomInfos == null || roomInfos.size() != 1) {
                    throw new Exception("要入住的房间不能空");
                }
                RoomInfo roomInfo = roomInfos.get(0);
                if (roomInfo.getRoomNumState() != 1) {
                    if (roomInfo.getRoomNumState() == 6) {
                        Map<String, Object> lockMap = new HashMap<>();
                        lockMap.put("RoomNo", map.get("RoomNo").toString());
                        lockMap.put("LockType", 2);
                        lockMap.put("appKey", map.get("appKey").toString());
                        this.lockRoom(lockMap);
                    } else {
                        throw new Exception("当前房间无法办理入住");
                    }
                }

                List<RoomDayPrice> roomDayPrices = new ArrayList<RoomDayPrice>();
                if (checkInType.equals("1")) {
                    //查询当前房间的房价
                    //查询酒店的价格信息
                    String startTime = map.get("BeginTime").toString();
                    String endTime = map.get("EndTime").toString();
                    //如果日期一致，则开始日期往前推迟一天
                    if (startTime.substring(0, 10).equals(endTime.substring(0, 10))) {
                        startTime = HotelUtils.parseDate2Str(HotelUtils.addDayGetNewDate(HotelUtils.parseStr2Date(startTime), -1));
                    }
                    RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
                    roomDayPriceSearch.setHid(user.getHid());
                    roomDayPriceSearch.setRoomRateId(Integer.parseInt(map.get("PriceCode").toString()));
                    roomDayPriceSearch.setRoomTypeId(Integer.parseInt(map.get("RoomTypeID").toString()));
                    roomDayPriceSearch.setDayTimeMin(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(startTime.toString())));
                    roomDayPriceSearch.setDayTimeMax(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(endTime.toString())));
                    roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);
                    //这里业务要做修改，如果没有保存每日房价则取房型价格
                    if (roomDayPrices == null || roomDayPrices.size() < 1) {
                        List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(startTime, endTime);
                        Calendar cal = Calendar.getInstance();
                        RoomType roomTypeID = roomTypeDao.selectById(Integer.parseInt(map.get("RoomTypeID").toString()));
                        for (String day : allDayListBetweenDate) {
                            RoomDayPrice roomDayPrice = new RoomDayPrice();
                            roomDayPrice.setHid(user.getHid());
                            roomDayPrice.setHotelGroupId(user.getHotelGroupId());
                            roomDayPrice.setRoomTypeId(Integer.parseInt(map.get("RoomTypeID").toString()));
                            roomDayPrice.setRoomRateId(Integer.parseInt(map.get("PriceCode").toString()));
                            roomDayPrice.setDayTime(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(day + " 00:00:00")));
                            roomDayPrice.setPrice(roomTypeID.getPrice());
                            cal.setTime(HotelUtils.parseStr2Date(day + " " + "00:00:00"));
                            roomDayPrices.add(roomDayPrice);
                        }
                    }
                } else if (checkInType.equals("2")) {
                    HotelHourRoomTypeSearch hotelHourRoomTypeSearch = new HotelHourRoomTypeSearch();
                    hotelHourRoomTypeSearch.setRoomTypeId(Integer.parseInt(map.get("RoomTypeID").toString()));
                    hotelHourRoomTypeSearch.setHid(user.getHid());
                    Integer HourCode = map.containsKey("HourCode") ? Integer.parseInt(map.get("HourCode").toString()) : 4;
                    hotelHourRoomTypeSearch.setHourRoomCode(HourCode);
                    List<HotelHourRoomType> hotelHourRoomTypes = hotelHourRoomTypeDao.selectBySearch(hotelHourRoomTypeSearch);
                    if (hotelHourRoomTypes.size() != 1) {
                        throw new Exception("未设置钟点房价");
                    }
                    map.put("PriceCode", hotelHourRoomTypes.get(0).getHourRoomInfoId());
                    RoomDayPrice roomDayPrice = new RoomDayPrice();
                    roomDayPrice.setPrice(hotelHourRoomTypes.get(0).getPrice());
                    roomDayPrice.setDayTime(Integer.parseInt(map.get("BeginTime").toString().substring(0, 10).replace("-", "")));
                    roomDayPrices.add(roomDayPrice);
                }

                JSONObject postData = new JSONObject();
                postData.put("infoSecrecy", false);
                postData.put("checkInType", map.get("CheckInType").toString());
                postData.put("rateId", map.get("PriceCode").toString());
                //后面再去查询下
                postData.put("rateCode", map.get("PriceCode").toString());
                postData.put("startTime", map.get("BeginTime").toString());
                postData.put("endTime", map.get("EndTime").toString());
                postData.put("keepTime", "18:00");
                postData.put("macCheckIn", true);
                postData.put("macCompRoom", false);
                postData.put("macDepositFree", false);
                postData.put("morningCall", false);
                postData.put("morningCallTime", "08:00");
                postData.put("continueRes", true);
                postData.put("noPrice", false);
                postData.put("noDposit", false);
                postData.put("autoAr", false);
                postData.put("phone", map.get("Phone") != null ? map.get("Phone").toString() : "");
                postData.put("autoCheckIn", true);
                postData.put("priceSecrecy", false);
                postData.put("roomTypeId", map.get("RoomTypeID").toString());
                //房间ID
                postData.put("roomInfoId", "");
                postData.put("remark", map.get("remarks") != null ? map.get("remarks").toString() : "");
                postData.put("resourceId", 1);
                postData.put("isGroup", 0);
                postData.put("groupName", "");
                postData.put("accountList", new JSONArray());

                JSONArray roomList = new JSONArray();
                JSONArray guestList = new JSONArray();
                JSONArray guestInfoList = JSONArray.fromObject(map.get("GuestList").toString());
                for (int i = 0; i < guestInfoList.size(); i++) {
                    JSONObject guestInfo = new JSONObject();
                    JSONObject guest = guestInfoList.getJSONObject(i);
                    guestInfo.put("idType", 1);
                    guestInfo.put("idCode", guest.getString("Code"));
                    guestInfo.put("personName", guest.getString("Name"));
                    guestInfo.put("sex", guest.getString("Sex").equals("男") ? 0 : 1);
                    guestInfo.put("phone", map.containsKey("Phone") && map.get("Phone") != null ? map.get("Phone").toString() : "");
                    guestInfo.put("guestNo", guest.get("GuestNo"));
                    guestInfo.put("image", guest.get("image"));
                    guestInfo.put("cameraPicture", guest.get("cameraPicture"));
                    guestInfo.put("photo", guest.get("photo"));
                    guestInfo.put("cameraPhoto", guest.get("cameraPhoto"));
                    guestInfo.put("Photo", guest.get("Photo"));
                    guestInfo.put("CameraPhoto", guest.get("CameraPhoto"));
                    guestInfo.put("Address", guest.get("Address"));
                    guestInfo.put("address", guest.get("Address"));
                    guestList.add(guestInfo);
                }


                JSONArray priceList = new JSONArray();
                for (int i = 0; i < roomDayPrices.size(); i++) {
                    RoomDayPrice roomDayPrice = roomDayPrices.get(i);
                    JSONObject priceInfo = new JSONObject();
                    String date = roomDayPrice.getDayTime().toString();
                    priceInfo.put("date", date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8));
                    priceInfo.put("price", roomDayPrice.getPrice());

                    priceList.add(priceInfo);
                }
                if (roomDayPrices.size() < 1) {

                    String startTime = map.get("BeginTime").toString();
                    String endTime = map.get("EndTime").toString();

                    List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(startTime, endTime);

                    RoomType roomTypeID = roomTypeDao.selectById(Integer.parseInt(map.get("RoomTypeID").toString()));

                    if (roomTypeID != null) {
                        for (String s : allDayListBetweenDate) {
                            JSONObject priceInfo = new JSONObject();
                            priceInfo.put("date", s);
                            priceInfo.put("price", roomTypeID.getPrice());
                            priceList.add(priceInfo);
                        }
                    }

                }
                JSONObject roomInfoData = JSONObject.fromObject(roomInfo);
                roomInfoData.put("guestList", guestList);
                roomInfoData.put("priceList", priceList);
                roomList.add(roomInfoData);
                postData.put("roomList", roomList);
                log.info(postData.toString());
                JSONObject requestData = new JSONObject();
                requestData.put("checkInParam", URLEncoder.encode(postData.toString()));
                requestData.put("sessionToken", tokenId);
                ResponseData responseData1 = pmsOrderService.blendCheckIn(requestData);
                if (responseData1.getCode() < 1) {
                    resultMap.put("Result", "False");
                    resultMap.put("Msg", responseData1.getMsg());
                    return resultMap;
                }

                Object data = responseData1.getData();

                Integer registId = 0;

                if (data instanceof JSONArray) {
                    JSONArray jsonArray = JSONArray.fromObject(data);
                    JSONObject jsonObject = jsonArray.getJSONObject(0);
                    registId = jsonObject.getInt("registId");
                } else if (data instanceof JSONObject) {
                    registId = JSONObject.fromObject(data).getInt("registId");
                }


                resultMap.put("Result", "True");
                resultMap.put("Msg", "入住成功");
                resultMap.put("RegistID", registId);
                resultMap.put("MainID", registId);
                resultMap.put("EndTime", map.get("EndTime").toString());
                resultMap.put("RoomNo", map.get("RoomNo").toString());
            }

        } catch (Exception e) {
            log.error("",e);
            e.printStackTrace();
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
        }
        log.info(resultMap.toString());
        return resultMap;
    }

    @Override
    public JSONObject addTransaction(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            if (!map.containsKey("RoomNo") || map.get("RoomNo").toString().equals("")) {
                throw new Exception("房间编号不能为空");
            }
            if (!map.containsKey("RegistID") || map.get("RegistID").toString().equals("")) {
                throw new Exception("主单编号不能为空");
            }
            if (!map.containsKey("Money") || map.get("Money").toString().equals("")) {
                throw new Exception("金额不能为空");
            }
            if (!map.containsKey("PayType") || map.get("PayType").toString().equals("")) {
                throw new Exception("支付方式不能为空");
            }
            if (!map.containsKey("TradeNo") || map.get("TradeNo").toString().equals("")) {
                throw new Exception("第三方支付流水号不能空");
            }


            int registID = Integer.parseInt(map.get("RegistID").toString());

            String payType = map.get("PayType").toString();

            Regist regist = registDao.selectById(registID);

            if (regist == null) {
                throw new Exception("入账失败");
            }


            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(registID);
            registPersonSearch.setRegistState(0);
            List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);

            if (registPersonList == null) {
                throw new Exception("入账失败");
            }

            AddAccountParam postData = new AddAccountParam();
            postData.setSessionToken(tokenId);
            postData.setAccountType(1);
            postData.setPayType(2);
            postData.setRegistId(registID);
            Double money = (Double.parseDouble(map.get("Money").toString()) * 100);
            postData.setPrice(money.intValue());
            postData.setRoomInfoId(regist.getRoomNumId());
            postData.setRoomNum(regist.getRoomNum());
            postData.setRegistState(regist.getState());
            postData.setSaleNum(1);
            postData.setRemark("自助机支付");

            Object remark = map.get("Remark");
            if (remark != null && !"".equals(remark.toString())) {
                postData.setRemark(remark.toString());
            }

            postData.setThirdAccoutId(map.get("TradeNo").toString());
            postData.setRoomTypeId(regist.getRoomTypeId());
            postData.setGroupAccount(0);
            for (int i = 0; i < registPersonList.size(); i++) {
                RegistPerson registPerson = registPersonList.get(i);
                if (registPerson.getIsOther() == 0) {
                    postData.setRegistPersonId(registPerson.getRegistPersonId());
                    postData.setRegistPersonName(registPerson.getPersonName());
                    break;
                }
            }


//            JSONObject postData = new JSONObject();
//            postData.put("sessionToken" ,tokenId);
//            postData.put("accountType",1);
//            postData.put("payType" , 2);
//            postData.put("registId" , registID);
//            postData.put("price" ,money.intValue() );
//            postData.put("roomTypeId" , regist.getRoomTypeId());
//            postData.put("roomNum" , regist.getRoomNum());
//            postData.put("roomInfoId" ,regist.getRoomNumId());
//            postData.put("registState" ,regist.getState());
//            postData.put("saleNum" , 1);
//            postData.put("remark" , "自助机支付");

            //支付宝支付
            if (payType.equals("8")) {
                postData.setPayClassId(11);
                postData.setPayClassName("支付宝");
                postData.setPayCodeId("9300");
                postData.setPayCodeName("支付宝扫码支付");
            } else if (payType.equals("13")) {
                postData.setPayClassId(12);
                postData.setPayClassName("微信支付");
                postData.setPayCodeId("9320");
                postData.setPayCodeName("微信扫码支付");
            } else if (payType.equals("15")) {
                JSONObject memeberPayInfo = new JSONObject();
                memeberPayInfo.put("cardId", map.get("CardID").toString());
                memeberPayInfo.put("money", (Double.parseDouble(map.get("Money").toString()) * 100));
                memeberPayInfo.put("registId", regist.getRegistId());
                memeberPayInfo.put("type", 2);
                memeberPayInfo.put("sessionToken", map.get("appKey").toString());
                ResponseData responseData = memberService.memberConsumption(memeberPayInfo);
                if (responseData.getCode() != 1) {
                    throw new Exception("会员入账失败");
                }
                postData.setPayClassId(5);
                postData.setPayClassName("会员储值");
                postData.setPayCodeId("9620");
                postData.setPayCodeName("会员储值卡冻结");
                postData.setThirdAccoutId(responseData.getData().toString());

                log.info(postData.toString());
            } else if (payType.equals("16")) {
                //国内卡预授权
                postData.setPayClassId(3);
                postData.setPayClassName("国内卡");
                postData.setPayCodeId("9100");
                postData.setPayCodeName("国内卡-预授权");
                AddAccountParam.BankPayInfo bankPayInfo = new AddAccountParam.BankPayInfo();
                bankPayInfo.setAuth("111");
                bankPayInfo.setBarkId("62");
                bankPayInfo.setEffectiveDays("30");
                postData.setBankPayInfo(bankPayInfo);
            }


            Map<String, Object> res = this.addAccountFunc(postData);
            if (Integer.parseInt(res.get("code").toString()) != 1) {
                resultMap.put("Result", "False");
                resultMap.put("Msg", "入账失败");
            }
            resultMap.put("Result", "True");
            resultMap.put("Msg", "入账成功");
        } catch (Exception e) {
            log.error("",e);
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
        }
        return resultMap;
    }

    @Override
    public JSONObject makeGuestCard(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            JSONObject data = JSONObject.fromObject(map);


        } catch (Exception e) {
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject cancelRoom(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {

        } catch (Exception e) {
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject addRegisterOther(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            JSONObject data = JSONObject.fromObject(map);
            if (!data.containsKey("GuestList") || data.get("GuestList").toString().equals("")) {
                throw new Exception("随行客信息不能为空");
            }
            if (!data.containsKey("RegistID") || data.getInt("RegistID") < 1) {
                throw new Exception("房单编号不能为空");
            }
            int registID = data.getInt("RegistID");
            Regist regist = registDao.selectById(registID);
            if (null == regist || regist.getState() != 0) {
                throw new Exception("房单状态异常");
            }
            String sessionToken = map.get("appKey").toString();
            TbUserSession user = this.getTbUserSession(sessionToken);
            JSONArray guestList = data.getJSONArray("GuestList");
            List<RegistPerson> registPersonList = new ArrayList<>();
            //业务类型  1-入住 2-退房  3-修改入住人  4-新增入住人  5-续住 6-删除入住人
            Integer bussType = 4;
            for (int i = 0; i < guestList.size(); i++) {
                JSONObject guest = guestList.getJSONObject(i);
                RegistPerson guestInfo = new RegistPerson();
                guestInfo.setPersonName(guest.containsKey("Name") ? guest.getString("Name") : "");
                guestInfo.setIdCode(guest.containsKey("Code") ? guest.getString("Code") : "");
                guestInfo.setSex(guest.containsKey("Sex") ? guest.getString("Sex").indexOf("男") == -1 ? 1 : 0 : 0);
                guestInfo.setAddress(guest.containsKey("Address") ? guest.getString("Address") : "");
                if (guest.get("Birthday") != null && !"".equals(guest.getString("Birthday")) && !"null".equals(guest.getString("Birthday"))) {
                    int birthday = Integer.parseInt(guest.getString("Birthday").replace("-", ""));
                    guestInfo.setBirthday(birthday);
                    guestInfo.setBirthYear(Integer.parseInt(String.valueOf(birthday).substring(0, 4)));
                } else {
                    IdcardValidator iv = new IdcardValidator();
                    boolean validatedAllIdcard = iv.isValidatedAllIdcard(guestInfo.getIdCode());
                    if (validatedAllIdcard) {
                        String birthday = guestInfo.getIdCode().length() >= 15 ? guestInfo.getIdCode().substring(6, 14) : "";
                        String year = guestInfo.getIdCode().length() >= 15 ? guestInfo.getIdCode().substring(6, 10) : "";
                        if (!birthday.equals("")) {
                            guestInfo.setBirthday(Integer.parseInt(birthday));
                        }
                        if (!year.equals("")) {
                            guestInfo.setBirthYear(Integer.parseInt(year));
                        }
                    }
                }
                if (guest.get("Nation") != null && !"".equals(guest.getString("Nation")) && !"null".equals(guest.getString("Nation"))) {
                    String nation = guest.getString("Nation");
                    if (nation.indexOf("族") < 1) {
                        nation += "族";
                    }
                    guestInfo.setNation(HotelUtils.nationMap.getInt(nation));
                }
                guestInfo.setIdType(1);
                guestInfo.setPersonName(guest.getString("Name"));
                guestInfo.setIsOther(1);

                guestInfo.setHid(user.getHid());
                guestInfo.setUpdateUserId(user.getUserId());
                guestInfo.setUpdateUserName(user.getUserName());
                guestInfo.setClassId(user.getClassId());

                guestInfo.setHotelGroupId(regist.getHotelGroupId());
                guestInfo.setPhone(guest.containsKey("Phone") ? guest.getString("Phone") : "");
                guestInfo.setRegistState(0);
                guestInfo.setRegistId(regist.getRegistId());
                guestInfo.setRoomNum(regist.getRoomNum());
                guestInfo.setRoomNumId(regist.getRoomNumId());
                guestInfo.setTeamCodeId(regist.getTeamCodeId());
                guestInfo.setBookingOrderId(regist.getBookingOrderId());

                /**
                 * 证件照,老版本 Image
                 */
                if (guest.get("Photo") != null && !"".equals(guest.getString("Photo"))) {
                    String photo = guest.getString("Photo").replace(' ', '+');
                    CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(photo, 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(guestInfo.getIdCode(), "UTF-8") + ".jpeg");
                    guestInfo.setIdImage(uploadObjectRsp.getFileName());
                }
                /**
                 * 身份证照  老版本
                 */
                if (guest.get("CameraPhoto") != null && !"".equals(guest.getString("CameraPhoto"))) {
                    String s = guestInfo.getIdCode() + regist.getHid() + regist.getRoomNumId() + HotelUtils.currentTime();
                    CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("CameraPhoto").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                    guestInfo.setCameraImage(uploadObjectRsp.getFileName());
                }

                Integer insert = registPersonDao.insert(guestInfo);
                if (insert < 1) {
                    throw new Exception("新增宾客信息失败");
                }

                /**
                 * 这里创建子线程处理 客史的问题
                 */
                registPersonList = new ArrayList<>();
                registPersonList.add(guestInfo);

                final List<RegistPerson> registPeople = registPersonList;
                HotelUtils.cachedThreadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            createPersonInfo(registPeople);
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error("",e);
                        }
                    }
                });


            }
            resultMap.put("Result", "True");
            final Regist registInfo = regist;
            final List<RegistPerson> registPeople = registPersonList;
            final Integer bussTypeOne = bussType;
            //推送智能門鎖的數據
            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        //如果是智能门锁则调用
                        SmartLockRequest smartLockRequest = new SmartLockRequest();
                        smartLockRequest.setBussType(bussTypeOne);
                        smartLockRequest.setSessionToken(sessionToken);
                        smartLockRequest.setCheckinTime(registInfo.getCheckinTime());
                        smartLockRequest.setCheckoutTime(registInfo.getCheckoutTime());
                        smartLockRequest.setRoomTypeId(registInfo.getRoomTypeId());
                        smartLockRequest.setRoomTypeName(registInfo.getRoomTypeName());
                        smartLockRequest.setRoomNumId(registInfo.getRoomNumId());
                        smartLockRequest.setRoomNum(registInfo.getRoomNum());
                        smartLockRequest.setHid(registInfo.getHid());
                        smartLockRequest.setHotelGroupId(registInfo.getHotelGroupId());
                        smartLockRequest.setBreakfastNum(registInfo.getBreakfastNum());
                        smartLockRequest.setCheckinType(registInfo.getCheckinType());
                        smartLockRequest.setLockNo(registInfo.getSessionToken());
                        List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                        for (int j = 0; j < registPeople.size(); j++) {
                            RegistPerson registPersonInfo = registPeople.get(j);
                            SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                            BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                            peoplesList.add(registPersonDetail);
                        }
                        smartLockRequest.setPeoples(peoplesList);
                        baseService.SmartLockUpdateGuest(smartLockRequest);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("",e);
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }


    /**
     * 创建客历档案
     *
     * @param registPersonList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseData createPersonInfo(List<RegistPerson> registPersonList) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<PersonInfo> personInfoList = new ArrayList<>();
            for (int i = 0; i < registPersonList.size(); i++) {
                RegistPerson registPerson = registPersonList.get(i);
                if (null == registPerson.getPersonName() || registPerson.getPersonName().equals("")) {
                    continue;
                }
                if (null == registPerson.getIdCode() || registPerson.getIdCode().equals("")) {
                    continue;
                }
                //以证件号为唯一标准去查询客史档案
                String idCode = registPerson.getIdCode();
//                IdcardValidator iv = new IdcardValidator();
//                if (!iv.isValidatedAllIdcard(idCode)) {
//                    continue;
//                }
                PersonInfoSearch personInfoSearch = new PersonInfoSearch();
                personInfoSearch.setHid(registPerson.getHid());
                personInfoSearch.setIdCode(idCode);
                Page<PersonInfo> personInfos = personInfoDao.selectBySearch(personInfoSearch);
                if (null == personInfos || personInfos.size() < 1) {
                    PersonInfo personInfo = new PersonInfo();
                    personInfo.setHid(registPerson.getHid());
                    personInfo.setHotelGroupId(registPerson.getHotelGroupId());
                    personInfo.setPersonName(registPerson.getPersonName());
                    personInfo.setIdCode(registPerson.getIdCode());
                    personInfo.setSex(registPerson.getSex());
                    personInfo.setBirthday(registPerson.getBirthday());
                    personInfo.setNation(registPerson.getNation());
                    personInfo.setIdImage(registPerson.getIdImage());
                    personInfo.setAddress(registPerson.getAddress());
                    personInfo.setCameraImage(registPerson.getCameraImage());
                    personInfo.setPhone(registPerson.getPhone());
                    personInfoList.add(personInfo);
                }
            }
            for (int i = 0; i < personInfoList.size(); i++) {
                Integer insert = personInfoDao.insert(personInfoList.get(i));
                if (insert < 1) {
                    throw new Exception("创建客历档案失败");
                }
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }
        return responseData;
    }

    @Override
    public JSONObject getArrivingList(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);


            if (!map.containsKey("Value") || map.get("Value").toString().equals("")) {
                throw new Exception("查询值不能为空");
            }
            if (!map.containsKey("Type") || map.get("Type").toString().equals("")) {
                map.put("Type", 3);
            }
            String search = map.get("Value").toString();

            BookingOrderPageRequest bookingOrderPageRequest = new BookingOrderPageRequest();
            bookingOrderPageRequest.setSessionToken(tokenId);
            bookingOrderPageRequest.setSearch(search);
            bookingOrderPageRequest.setHid(user.getHid());

            //只查询有效和部分入住的
            ArrayList<Integer> status = new ArrayList<>();
            status.add(1);
            status.add(3);
            if (null != map.get("GetAllOrder") && map.get("GetAllOrder").toString().equals("true")) {
                status.add(4);
            }
            bookingOrderPageRequest.setOrderStatus(status);
            ArrayList<Long> checkinTime = new ArrayList<>();
            String date = HotelUtils.currentDate();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date parse1 = simpleDateFormat.parse(date);
            long time = parse1.getTime();
            checkinTime.add(time - 86400000 - 1000);
            checkinTime.add(time + 86400000 - 1000);
            bookingOrderPageRequest.setCheckinTime(checkinTime);

            if (bookingOrderPageRequest.getCheckinTime() != null) {
                ArrayList<Long> longs = new ArrayList<>();
                bookingOrderPageRequest.getCheckinTime().forEach(t -> {
                    if (t != null) {
                        longs.add(t / 1000);
                    }
                });
                bookingOrderPageRequest.setCheckinTime(longs);
            }
            Page<BookingOrder> bookingOrders = bookingOrderDao.selectPageByRequest(bookingOrderPageRequest);

            BookingOrder bookingOrder = new BookingOrder();
            bookingOrder.setBookingOrderId(0);
            if (bookingOrders == null || bookingOrders.size() < 1) {
                bookingOrder = bookingOrderDao.selectById(Integer.parseInt(search));
            } else {
                bookingOrder = bookingOrders.get(0);
            }
            Object idCode = map.get("IDCode");
            if (idCode != null && !idCode.equals("") && idCode.toString().length() > 10) {
                Boolean aBoolean = this.roomCodeBook(user, idCode.toString(), resultMap);
                if (aBoolean) {
                    return resultMap;
                }
            }
            if (bookingOrder.getBookingOrderId() == 0) {
                throw new Exception("未查询到订单");
            }
            // 判断
            JSONArray orderList = new JSONArray();
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setHid(user.getHid());
            bookingOrderRoomNumSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
            if (bookingOrderRoomNums == null || bookingOrderRoomNums.size() < 1) {
                throw new Exception("未查询到订单信息");
            }
            //查询预订的价格信息
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
            ParamSearch paramSearch = new ParamSearch();
            paramSearch.setHid(user.getHid());
            List<HotelRoomTypeView> hotelRoomTypeList = paramDao.getHotelRoomTypeList(paramSearch);
            Map<Integer, HotelRoomTypeView> hotelRoomTypeMap = hotelRoomTypeList.stream().collect(Collectors.toMap(HotelRoomTypeView::getRoomTypeId, a -> a, (k1, k2) -> k1));
            /**
             * 分组，处理预订为排房的价格信息
             */
            Map<Integer, List<BookingOrderDailyPrice>> collect = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getBookingOrderRoomNumId));

            Map<Integer, List<BookingOrderDailyPrice>> collect1 = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(c -> c.getBookingOrderRoomNumId() == null ? -1 : c.getBookingOrderRoomNumId()));

            Double orderMoney = 0.00;
            for (int i = 0; i < bookingOrderRoomNums.size(); i++) {
                bookingOrderRoomNums.get(i).setBookingOrderDailyPrices((ArrayList<BookingOrderDailyPrice>) collect.get(bookingOrderRoomNums.get(i).getId()));

                for (int j = 0; j < bookingOrderRoomNums.get(i).getBookingOrderDailyPrices().size(); j++) {
                    orderMoney += bookingOrderRoomNums.get(i).getBookingOrderDailyPrices().get(j).getPrice() / 100.0;
                }
            }


            JSONArray roomList = new JSONArray();
            for (int i = 0; i < bookingOrderRoomNums.size(); i++) {
                BookingOrderRoomNum bookingOrderRoomNum = bookingOrderRoomNums.get(i);
                JSONObject roomInfo = new JSONObject();
                roomInfo.put("OrderID", bookingOrder.getBookingOrderId());
                roomInfo.put("BookMan", bookingOrder.getBookingName());
                //获取房型信息
                roomInfo.put("RoomType", hotelRoomTypeMap.get(bookingOrderRoomNum.getRoomTypeId()).getRoomTypeName());
                roomInfo.put("Reserved", bookingOrderRoomNum.getId());
                roomInfo.put("RoomTypeID", bookingOrderRoomNum.getRoomTypeId());
                roomInfo.put("BeginTime", HotelUtils.stampToDate(bookingOrder.getCheckinTime().getTime()));
                roomInfo.put("EndTime", HotelUtils.stampToDate(bookingOrder.getCheckoutTime().getTime()));
                String roomNo = bookingOrderRoomNum.getRoomNum() != null && !bookingOrderRoomNum.getRoomNum().equals("0") && !bookingOrderRoomNum.getRoomNum().equals("") ? bookingOrderRoomNum.getRoomNum() : "";
                Integer roomNumId = bookingOrderRoomNum.getRoomNumId();
                if (roomNumId == null || roomNumId < 10) {
                    roomNo = "";
                }
                roomInfo.put("RoomNo", roomNo);
                roomInfo.put("Building", "");
                roomInfo.put("Floor", "");
                roomInfo.put("GuestNo", "");
                roomInfo.put("BreakFast", "");
                roomInfo.put("OrderMoney", orderMoney);
                roomInfo.put("IsPay", false);
                roomInfo.put("PayType", "");
                roomInfo.put("PayMoney", 0);
                roomInfo.put("RoomPriceID", bookingOrderRoomNum.getRateCodeId());
                roomInfo.put("BookNum", 0);
                roomInfo.put("IsCheckIn", bookingOrderRoomNum.getIsCheckin() == 1);
                roomInfo.put("isCheckout", bookingOrderRoomNum.getIsCheckout() == 1);
                roomInfo.put("AbleCheckIn", bookingOrderRoomNum.getIsCheckin() == 0);
                String orderState = "R";
                if (bookingOrderRoomNum.getIsCheckin() == 0) {
                    orderState = "R";
                } else if (bookingOrderRoomNum.getIsCheckin() == 1 && bookingOrderRoomNum.getIsCheckout() == 0) {
                    orderState = "I";
                } else if (bookingOrderRoomNum.getIsCheckin() == 1 && bookingOrderRoomNum.getIsCheckout() == 1) {
                    orderState = "O";
                }
                roomInfo.put("orderState", orderState);
                roomInfo.put("RegistID", bookingOrderRoomNum.getRegistId());
                roomInfo.put("GuestList", "");
                JSONArray PriceList = new JSONArray();
                List<BookingOrderDailyPrice> bookingOrderDailyPrices1 = collect1.get(bookingOrderRoomNum.getId());
                for (int j = 0; j < bookingOrderDailyPrices1.size(); j++) {
                    JSONObject info = new JSONObject();
                    info.put("date", HotelUtils.businessDay2Str(bookingOrderDailyPrices1.get(j).getDailyTime()));
                    info.put("price", bookingOrderDailyPrices1.get(j).getPrice() / 100.0);
                    PriceList.add(info);
                }

                roomInfo.put("PriceList", PriceList);
                roomList.add(roomInfo);
            }

            JSONObject orderInfo = new JSONObject();
            orderInfo.put("OrderId", bookingOrder.getBookingOrderId());
            orderInfo.put("BookMan", bookingOrder.getBookingName());
            orderInfo.put("Phone", bookingOrder.getBookingPhone());
            orderInfo.put("BeginTime", HotelUtils.stampToDate(bookingOrder.getCheckinTime().getTime()));
            orderInfo.put("EndTime", HotelUtils.stampToDate(bookingOrder.getCheckoutTime().getTime()));
            orderInfo.put("VipNo", "");
            orderInfo.put("VipLevel", "");
            orderInfo.put("IsPay", false);
            orderInfo.put("OrderType", "");
            // 1-什么都不需要付款  2-付房费不付押金 3-免押金不免房费
            if (1 == bookingOrder.getNoPrice() && 1 == bookingOrder.getNoDeposit()) {
                orderInfo.put("IsPay", true);
                orderInfo.put("OrderType", 1);
            } else if (1 == bookingOrder.getNoPrice() && 1 != bookingOrder.getNoDeposit()) {
                orderInfo.put("OrderType", 2);
            } else if (1 != bookingOrder.getNoPrice() && 1 == bookingOrder.getNoDeposit()) {
                orderInfo.put("OrderType", 3);
            }
            orderInfo.put("Corp", "");
            orderInfo.put("Secrecy", 1 == bookingOrder.getPriceSecrecy() ? true : false);
            orderInfo.put("ChannelName", "");
            orderInfo.put("OrderMoney", orderMoney);
            orderInfo.put("PayType", "");
            orderInfo.put("PayMoney", 0);
            orderInfo.put("Reserved", "");
            orderInfo.put("Remark", bookingOrder.getRemark());
            orderInfo.put("RoomList", roomList);
            orderList.add(orderInfo);
            resultMap.put("Result", "True");
            resultMap.put("Msg", "查询预订单成功");
            resultMap.put("OrderList", orderList);
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        log.info(resultMap.toString());
        return resultMap;
    }

    public Boolean roomCodeBook(TbUserSession user, String idCode, JSONObject resultMap) {
        Boolean as = false;
        try {
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setIsCheckin(0);
            bookingOrderRoomNumSearch.setOrderState(1);
            bookingOrderRoomNumSearch.setHid(user.getHid());
            bookingOrderRoomNumSearch.setRoomCode(idCode);
            ArrayList<Long> checkinTime = new ArrayList<>();
            String date = HotelUtils.currentDate();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date parse1 = simpleDateFormat.parse(date);
            long time = parse1.getTime() / 1000;
            checkinTime.add(time - 86400000 - 1000);
            checkinTime.add(time + 86400000 + 1000);
            bookingOrderRoomNumSearch.setCheckinTime(checkinTime);
            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            if (bookingOrderRoomNums.size() < 1) {

                bookingOrderRoomNumSearch.setRoomCode(null);
                bookingOrderRoomNumSearch.setBookingIdCode(idCode);
                bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearchIdCode(bookingOrderRoomNumSearch);

                if (bookingOrderRoomNums.size() < 1) {
                    return false;
                }

            }
            BookingOrderRoomNum bookingOrderRoomNum = bookingOrderRoomNums.get(0);
            Integer bookingOrderId = bookingOrderRoomNum.getBookingOrderId();
            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderId);
            BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
            bookingOrderRoomTypeSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            List<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);

            Map<Integer, BookingOrderRoomType> bookingOrderRoomTypeMap = bookingOrderRoomTypes.stream().collect(Collectors.toMap(BookingOrderRoomType::getRoomTypeId, a -> a, (k1, k2) -> k1));


            ParamSearch paramSearch = new ParamSearch();
            paramSearch.setHid(user.getHid());
            List<HotelRoomTypeView> hotelRoomTypeList = paramDao.getHotelRoomTypeList(paramSearch);
            Map<Integer, HotelRoomTypeView> hotelRoomTypeMap = hotelRoomTypeList.stream().collect(Collectors.toMap(HotelRoomTypeView::getRoomTypeId, a -> a, (k1, k2) -> k1));


            Integer orderMoney = 0;
            JSONArray roomList = new JSONArray();

            for (BookingOrderRoomNum born : bookingOrderRoomNums) {
                if (!born.getBookingOrderId().equals(bookingOrderId)) {
                    continue;
                }
                JSONObject roomInfo = new JSONObject();
                roomInfo.put("OrderID", born.getBookingOrderId());
                roomInfo.put("BookMan", bookingOrder.getBookingName());

                BookingOrderDailyPriceSearch bdpr = new BookingOrderDailyPriceSearch();
                bdpr.setHid(user.getHid());
                bdpr.setBookingOrderRoomNumId(born.getBookingOrderId());
                Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bdpr);

                int sum = bookingOrderDailyPrices.stream().mapToInt(BookingOrderDailyPrice::getPrice).sum();
                orderMoney += sum;

                //获取房型信息
                roomInfo.put("RoomType", hotelRoomTypeMap.get(bookingOrderRoomNum.getRoomTypeId()).getRoomTypeName());
                roomInfo.put("Reserved", born.getId());
                roomInfo.put("RoomTypeID", born.getRoomTypeId());
                roomInfo.put("BeginTime", HotelUtils.stampToDate(bookingOrder.getCheckinTime().getTime()));
                roomInfo.put("EndTime", HotelUtils.stampToDate(bookingOrder.getCheckoutTime().getTime()));
                String roomNo = born.getRoomNum() != null && !born.getRoomNum().equals("0") && !born.getRoomNum().equals("") ? born.getRoomNum() : "";
                Integer roomNumId = born.getRoomNumId();
                if (roomNumId == null || roomNumId < 10) {
                    roomNo = "";
                }
                roomInfo.put("RoomNo", roomNo);
                roomInfo.put("Building", "");
                roomInfo.put("Floor", "");
                roomInfo.put("GuestNo", "");
                roomInfo.put("BreakFast", "");
                roomInfo.put("OrderMoney", sum);
                roomInfo.put("IsPay", false);
                roomInfo.put("PayType", "");
                roomInfo.put("PayMoney", 0);
                roomInfo.put("RoomPriceID", bookingOrderRoomTypeMap.get(bookingOrderRoomNum.getRoomTypeId()).getPriceCodeId());
                roomInfo.put("BookNum", 0);
                roomInfo.put("IsCheckIn", bookingOrderRoomNum.getIsCheckin() == 1);
                roomInfo.put("AbleCheckIn", bookingOrderRoomNum.getIsCheckin() == 0);
                roomInfo.put("GuestList", "");
                roomList.add(roomInfo);
                as = true;
            }


            JSONObject orderInfo = new JSONObject();
            orderInfo.put("OrderId", bookingOrder.getBookingOrderId());
            orderInfo.put("BookMan", bookingOrder.getBookingName());
            orderInfo.put("Phone", bookingOrder.getBookingPhone());
            orderInfo.put("BeginTime", HotelUtils.stampToDate(bookingOrder.getCheckinTime().getTime()));
            orderInfo.put("EndTime", HotelUtils.stampToDate(bookingOrder.getCheckoutTime().getTime()));
            orderInfo.put("VipNo", "");
            orderInfo.put("VipLevel", "");
            orderInfo.put("IsPay", false);
            orderInfo.put("OrderType", "");
            // 1-什么都不需要付款  2-付房费不付押金
            if (1 == bookingOrder.getNoPrice() && 1 == bookingOrder.getNoDeposit()) {
                orderInfo.put("IsPay", true);
                orderInfo.put("OrderType", 1);
            } else if (1 == bookingOrder.getNoPrice() && 1 != bookingOrder.getNoDeposit()) {
                orderInfo.put("OrderType", 2);
            }
            orderInfo.put("Corp", "");
            orderInfo.put("Secrecy", 1 == bookingOrder.getPriceSecrecy() ? true : false);
            orderInfo.put("ChannelName", "");
            orderInfo.put("OrderMoney", orderMoney);
            orderInfo.put("PayType", "");
            orderInfo.put("PayMoney", 0);
            orderInfo.put("Reserved", "");
            orderInfo.put("RoomList", roomList);
            JSONArray orderList = new JSONArray();
            orderList.add(orderInfo);
            resultMap.put("Result", "True");
            resultMap.put("Msg", "查询预订单成功");
            resultMap.put("OrderList", orderList);


        } catch (Exception e) {
            e.printStackTrace();
            as = false;
            log.error("",e);
        }
        return as;
    }


    @Override
    public JSONObject assignRoom(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);
            if (!map.containsKey("OrderID") || map.get("OrderID").toString().equals("")) {
                throw new Exception("预订单号不能为空");
            }

            if (!map.containsKey("Reserved") || map.get("Reserved").toString().equals("")) {
                throw new Exception("小单号不能空");
            }

            if (!map.containsKey("RoomNo") || map.get("RoomNo").toString().equals("")) {
                throw new Exception("房间号不能空");
            }
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            roomInfoSearch.setRoomNum(map.get("RoomNo").toString());
            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
            if (roomInfos == null || roomInfos.size() != 1) {
                throw new Exception("房间状态异常");
            }
            BookingOrderRoomNum bookingOrderRoomNum = bookingOrderRoomNumDao.selectById(Integer.parseInt(map.get("Reserved").toString()));
            bookingOrderRoomNum.setRoomNum(roomInfos.get(0).getRoomNum());
            bookingOrderRoomNum.setRoomNumId(roomInfos.get(0).getRoomInfoId());
            bookingOrderRoomNum.setRoomTypeId(roomInfos.get(0).getRoomTypeId());
            JSONObject postData = new JSONObject();
            postData.put("bookingOrderId", Integer.parseInt(map.get("OrderID").toString()));
            postData.put("type", 1);
            JSONArray roomList = new JSONArray();
            roomList.add(JSONObject.fromObject(bookingOrderRoomNum));
            postData.put("roomList", roomList);
            postData.put("sessionToken", tokenId);
            Map<String, Object> res = this.addBookOrderRoomFunc(postData);
            if (!res.get("Result").toString().equals("Success")) {
                resultMap.put("Result", "False");
                resultMap.put("Msg", res.get("Msg").toString());
                return resultMap;
            }
            resultMap.put("Result", "True");
            resultMap.put("Msg", "预订单排房成功");
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject continuedLive(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);

            if (!map.containsKey("RegistID") || map.get("RegistID").toString().equals("")) {
                throw new Exception("主单编号不能空");
            }
            int registID = Integer.parseInt(map.get("RegistID").toString());
            Regist regist = registDao.selectById(registID);
            if (regist.getState() != 0) {
                throw new Exception("订单状态异常不允许续住");
            }
            JSONObject postData = new JSONObject();
            postData.put("registId", regist.getRegistId());
            postData.put("sessionToken", tokenId);
            JSONObject stayOverParam = new JSONObject();
            int dayNum = Integer.parseInt(map.get("DayNum").toString());
            stayOverParam.put("newDate", HotelUtils.parseDate2Str(HotelUtils.addDayGetNewDate(HotelUtils.parseStr2Date(HotelUtils.stampToDate(regist.getCheckoutTime().getTime())), dayNum)));
            JSONObject priceCodeMsg = new JSONObject();
            priceCodeMsg.put("rateCode", regist.getRoomRateCodeName());
            priceCodeMsg.put("rateId", regist.getRoomRateCodeId());
            stayOverParam.put("days", dayNum);
            stayOverParam.put("isRoomTypeUp", false);
            stayOverParam.put("isOldPrice", false);
            stayOverParam.put("priceCodeMsg", priceCodeMsg);
            //查询续住可用房间  --默认查询7天
            String startTime = HotelUtils.stampToDate(regist.getCheckoutTime().getTime());
            String endTime = HotelUtils.stampToDate(HotelUtils.addDayGetNewDate(regist.getCheckoutTime(), dayNum).getTime());
            //查询酒店的价格信息
            RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
            roomDayPriceSearch.setHid(user.getHid());
            roomDayPriceSearch.setRoomRateId(regist.getRoomRateCodeId());
            roomDayPriceSearch.setRoomTypeId(regist.getRoomTypeId());
            roomDayPriceSearch.setDayTimeMin(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(startTime.toString())));
            roomDayPriceSearch.setDayTimeMax(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(endTime.toString())));
            List<RoomDayPrice> roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);
            Map<String, List<RoomDayPrice>> roomTypePriceMap = new HashMap<>();
            JSONArray priceList = new JSONArray();

            ArrayList<OverStayRequest.price> prices = new ArrayList<>();

            for (RoomDayPrice roomDayPrice : roomDayPrices) {
                priceList.add(JSONObject.fromObject(roomDayPrice));

                OverStayRequest.price price = new OverStayRequest.price();

                price.setPrice(roomDayPrice.getPrice());
                price.setDate(HotelUtils.parseDate2Str(HotelUtils.parseInt2Date(roomDayPrice.getDayTime())));

                prices.add(price);

            }
            stayOverParam.put("priceList", priceList);
            postData.put("stayOverParam", stayOverParam);

            // 调用新的续住方法
            OverStayRequest overStayRequest = new OverStayRequest();

            overStayRequest.setRegistId(registID);
            overStayRequest.setNewDate(stayOverParam.getString("newDate"));
            overStayRequest.setDayCount(dayNum);
            overStayRequest.setRateId(regist.getRoomRateCodeId());
            overStayRequest.setRateCode(regist.getRoomRateCodeName());
            overStayRequest.setPriceList(prices);
            overStayRequest.setSessionToken(tokenId);
            overStayRequest.setDayCount(dayNum);
            ResponseData responseData = pmsOrderService.overStayNew(overStayRequest);

            if (responseData.getCode() < 0) {
                throw new Exception(responseData.getMsg());
            }
            resultMap.put("Result", "True");

        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject getRegisterMsg(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);

            if (!map.containsKey("Type") || map.get("Type").toString().equals("")) {
                map.put("Type", 3);
            }
            if (!map.containsKey("Value") || map.get("Value").toString().equals("")) {
                throw new Exception("查询值不能空");
            }
            String value = map.get("Value").toString();
            String type = map.get("Type").toString();
            Regist regist = null;


            /**
             * 通过房间号查询在住信息
             */
            if (type.equals("3")) {
                RegistSearch registSearch = new RegistSearch();
                //只查询在住
                registSearch.setState(0);
                registSearch.setHid(user.getHid());
                registSearch.setRoomNum(value);
                List<Regist> regists = registDao.selectBySearch(registSearch);
                if (regists == null || regists.size() != 1) {
                    throw new Exception("未查询到在住信息");
                }
                regist = regists.get(0);
            }
            //通过证件号查询在住信息
            else if (type.equals("2")) {
                RegistPersonSearch registPerson = new RegistPersonSearch();
                registPerson.setHid(user.getHid());
                registPerson.setIdCode(map.get("Value").toString());
                registPerson.setRegistState(0);

                List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPerson);

                if (registPeople == null) {
                    throw new Exception("未查询到在住信息");
                }

                for (RegistPerson rp : registPeople) {
                    regist = registDao.selectById(rp.getRegistId());
                    if (regist == null) {
                        continue;
                    }
                    if (regist.getState() == 0 && regist.getHid().equals(user.getHid())) {
                        break;
                    }
                }
                if (regist == null) {
                    throw new Exception("未查询到在住信息");
                }
            } else if (type.equals("5")) {
                regist = registDao.selectById(Integer.parseInt(value));
                if (regist == null) {
                    throw new Exception("未查询到在住信息");
                }
            }

            // 1.配置信息
            BookingOrderConfig bookingOrderConfig = new BookingOrderConfig();
            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setRegistId(regist.getRegistId());
            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);
            if (bookingOrderConfigs != null && bookingOrderConfigs.size() > 0) {
                bookingOrderConfig = bookingOrderConfigs.get(0);
            }

            Integer integer = HotelUtils.parseDate2Int(regist.getCheckinTime());
            // 登记单离店时间
            Integer checkoutInt = HotelUtils.parseDate2Int(regist.getCheckoutTime());
            Date checkoutTimeOld = regist.getCheckoutTime();

            // 当前时间 into
            Integer nowDateInt = HotelUtils.parseDate2Int(new Date());

            // 如果退房时间不是当天，则把regist表中离店日期，改为当天 + 石粉
            if (!checkoutInt.equals(nowDateInt) && !integer.equals(nowDateInt)) {
                String newDate = HotelUtils.parseDate2Str(new Date()).substring(0, 10);
                String oldCheckoutTime = HotelUtils.parseDate2Str(regist.getCheckoutTime());
                String[] s = oldCheckoutTime.split(" ");

                String newCheckOutDate = newDate + " " + s[1];
                regist.setCheckoutTime(HotelUtils.parseStr2Date(newCheckOutDate));
            }
            /**
             * 查询入住人信息
             */
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(regist.getRegistId());
            registPersonSearch.setRegistState(0);
            if (regist.getState() == 1) {
                registPersonSearch.setRegistState(1);
            }
            List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
            JSONArray guestInfoList = new JSONArray();
            for (int i = 0; i < registPersonList.size(); i++) {
                JSONObject guestInfo = new JSONObject();
                RegistPerson registPerson = registPersonList.get(i);
                guestInfo.put("Name", registPerson.getPersonName());
                guestInfo.put("Code", registPerson.getIdCode());
                guestInfo.put("Nation", registPerson.getNation());
                guestInfo.put("Sex", registPerson.getSex().equals(0) ? "男" : "女");
                guestInfo.put("Address", registPerson.getAddress());
                guestInfo.put("Birthday", registPerson.getBirthday());
                guestInfo.put("Phone", "null".equals(registPerson.getPhone()) ? "" : registPerson.getPhone());
                String guestNo = registPerson.getGuestNo();
                if (guestNo == null || guestNo.equals("") || guestNo.equals("null")) {
                    guestNo = "";
                }
                guestInfo.put("GuestNo", guestNo);
                guestInfoList.add(guestInfo);
            }

            //查询帐务信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setRegistId(regist.getRegistId());
            accountSearch.setRegistState(0);
            if (regist.getState() == 1) {
                accountSearch.setRegistState(1);
            }
            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            double cashMoney = 0.00;
            double costMoney = 0.00;
            JSONArray cashList = new JSONArray();
            JSONArray costList = new JSONArray();
            Integer roomMoney = 0;

            Integer arMoney = 0;

            for (int i = 0; i < accounts.size(); i++) {
                Account account = accounts.get(i);
                double v = Double.parseDouble(account.getPrice().toString()) / 100.0;
                //消费
                if (account.getPayType() == 1 && account.getIsCancel() != 1) {
                    JSONObject costData = new JSONObject();
                    costData.put("Money", v);
                    costData.put("OccurTime", account.getCreateTime());
                    costData.put("CostCode", account.getPayCodeName());
                    costData.put("remark", account.getRemark());
                    costData.put("codeId", account.getPayCodeId());
                    costList.add(costData);
                    costMoney += v;

                    if (1 == account.getAccountType()) {
                        roomMoney += account.getPrice();
                    }

                }
                //付款
                else if (account.getPayType() == 2 && account.getIsCancel() != 1) {
                    cashMoney += v;
                    JSONObject cashData = new JSONObject();
                    cashData.put("Money", v);
                    cashData.put("Type", "1");
                    if (account.getPayCodeId().equals("9100") && account.getThirdRefundState() == 0) {
                        cashData.put("Type", "2");
                    }
                    cashData.put("OccurTime", account.getCreateTime());
                    cashData.put("PayMode", account.getPayCodeName());
                    cashData.put("remark", account.getRemark());
                    cashData.put("codeId", account.getPayCodeId());
                    cashList.add(cashData);
                    if (account.getPayCodeId().equals("9800")) {
                        arMoney += account.getPrice();
                    }
                }
            }


            resultMap.put("PriceCode", regist.getRoomRateCodeId());
            resultMap.put("RoomType", regist.getRoomTypeName());
            resultMap.put("RegistID", regist.getRegistId());
            resultMap.put("MainID", regist.getRegistId());
            if (null != regist.getBookingOrderId()) {
                resultMap.put("OrderID", regist.getBookingOrderId());
            }
            resultMap.put("BeginTime", HotelUtils.stampToDate(regist.getCheckinTime().getTime()));
            resultMap.put("EndTime", HotelUtils.stampToDate(checkoutTimeOld.getTime()));
            resultMap.put("CheckInType", regist.getCheckinType());
            resultMap.put("isVip", regist.getMemberId() == null ? false : true);
            resultMap.put("RoomNo", regist.getRoomNum());
            resultMap.put("RoomTypeID", regist.getRoomTypeId());
            resultMap.put("CanCheckOut", true);
            resultMap.put("BookingType", "");
            resultMap.put("GuestList", guestInfoList);
            resultMap.put("GuestName", registPersonList.size() > 0 ? registPersonList.get(0).getPersonName() : "");
            resultMap.put("IDType", "身份证");
            resultMap.put("IDCode", registPersonList.size() > 0 ? registPersonList.get(0).getIdCode() : "");
            resultMap.put("Gender", registPersonList.size() > 0 ? registPersonList.get(0).getSex().equals(0) ? "男" : "女" : "");
            resultMap.put("PayType", "");
            resultMap.put("RegistState", regist.getState());

            resultMap.put("FeeAdd", "0");

            /**
             * 验证房价信息，是否需要加收房费
             */
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setRegistId(regist.getRegistId());
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
            JSONArray priceList = new JSONArray();
            for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
                JSONObject priceInfo = new JSONObject();
                priceInfo.put("date", HotelUtils.businessDay2Str(bookingOrderDailyPrices.get(i).getDailyTime()));
                priceInfo.put("price", bookingOrderDailyPrices.get(i).getPrice() / 100.0);
                priceInfo.put("dailyState", bookingOrderDailyPrices.get(i).getDailyState());
                priceList.add(priceInfo);
            }

            Map<Integer, BookingOrderDailyPrice> dailyPriceMap = bookingOrderDailyPrices.stream().collect(Collectors.toMap(BookingOrderDailyPrice::getDailyTime, a -> a, (k1, k2) -> k2));

            Integer AddMoney = 0;

            Date date1 = new Date();

            Integer dayInt = HotelUtils.parseDate2Int(date1);

            Integer businessDay = user.getBusinessDay();

            Integer checkInBussday = regist.getBusinessDay();

            // 判断是否是凌晨房
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setHid(user.getHid());
            hotelSettingByParamId.setParamId(HOTEL_SETTING.NIGHT_ROOM);

            Object minObj = this.findHotelSettingByParamId(hotelSettingByParamId);

            Boolean isNightRoom = false;

            if (minObj != null) {
                int hour = Integer.parseInt(minObj.toString());

                int hours = regist.getCheckinTime().getHours();

                if (hour > hours) {
                    isNightRoom = true;
                }

            }

            // 如果营业日等于当前时间
            if (!isNightRoom && (dayInt.equals(businessDay) || dayInt > businessDay) && !checkInBussday.equals(businessDay)) {
                dayInt = HotelUtils.parseDate2Int(HotelUtils.addDayGetNewDate(date1, -1));
            }

            // 如果是凌晨房 ，且开房日期不等于当前日期，则取昨天价格
            if (isNightRoom && !checkInBussday.equals(businessDay)) {
                dayInt = HotelUtils.parseDate2Int(HotelUtils.addDayGetNewDate(date1, -1));
            }

            //
            if (checkInBussday.equals(businessDay)) {
                dayInt = businessDay;
            }

            BookingOrderDailyPrice bookingOrderDailyPrice = dailyPriceMap.get(dayInt);
            if (bookingOrderDailyPrice == null) {
                bookingOrderDailyPrice = bookingOrderDailyPrices.get(bookingOrderDailyPrices.size() - 1);

                JSONObject costData = new JSONObject();
                costData.put("Money", bookingOrderDailyPrice.getPrice() / 100.0);
                costData.put("OccurTime", HotelUtils.currentDate());
                costData.put("CostCode", "全天房费");
                costList.add(costData);
                costMoney += bookingOrderDailyPrice.getPrice() / 100.0;
                roomMoney += bookingOrderDailyPrice.getPrice();
            } else if (bookingOrderDailyPrice.getDailyState() == 1 && regist.getState() == 0) {

                JSONObject costData = new JSONObject();
                costData.put("Money", bookingOrderDailyPrice.getPrice() / 100.0);
                costData.put("OccurTime", HotelUtils.currentDate());
                costData.put("CostCode", "全天房费");
                costList.add(costData);
                costMoney += bookingOrderDailyPrice.getPrice() / 100.0;
                roomMoney += bookingOrderDailyPrice.getPrice();
            }

            // 判断离店时间是否是和入住日期一致,则盘是否要加收房费,或者入住时间和离店时间一致都需要判断是否加收房费
            Integer checkInTime = HotelUtils.parseDate2Int(regist.getCheckinTime());
            Integer checkOutTime = HotelUtils.parseDate2Int(regist.getCheckoutTime());
            Date date = new Date();
            if (date.getTime() > regist.getCheckoutTime().getTime()) {
                RoomRateCodeSpecific rrcs = new RoomRateCodeSpecific();
                rrcs.setAddAllDay("18:00");
                rrcs.setAddHalfDay("16:00");
                rrcs.setOvertimeAddMoney(50);

                RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
                roomRateCodeSpecificSearch.setHid(user.getHid());
                roomRateCodeSpecificSearch.setHid(regist.getRoomRateCodeId());
                roomRateCodeSpecificSearch.setRoomTypeId(regist.getRoomTypeId());

                List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);

                if (roomRateCodeSpecifics != null && roomRateCodeSpecifics.size() > 0) {
                    rrcs = roomRateCodeSpecifics.get(0);
                }

                String dateStr = HotelUtils.currentDate();

                Long nowTime = date.getTime();

                Long allDayTime = HotelUtils.parseStr2Date(dateStr + " " + rrcs.getAddAllDay() + ":00").getTime();
                Long halfDayTime = HotelUtils.parseStr2Date(dateStr + " " + rrcs.getAddHalfDay() + ":00").getTime();


                if (nowTime > allDayTime) {
                    AddMoney += bookingOrderDailyPrice.getPrice();
                } else if (nowTime > halfDayTime) {
                    AddMoney += (bookingOrderDailyPrice.getPrice() / 2);
                } else {

                    Long checkouTimeLong = regist.getCheckouTimeLong();

                    Long diff = nowTime - checkouTimeLong;
                    int diffInt = diff.intValue() / 1000;

                    int overTime = diffInt / 3600;

                    int i = overTime % 3600;
                    if (i > 180) {

                        overTime += 1;

                    }

                    int i1 = overTime * rrcs.getOvertimeAddMoney();

                    AddMoney += i1;

                }

            }

            // 可挂账
            if (bookingOrderConfig.getAutoAr() == 1) {

                // 如果 是预订转入住
                Integer bookingOrderId = regist.getBookingOrderId();
                int i = 0;
                if (bookingOrderId != null && bookingOrderId > 0) {

                    Integer sumDayPrice = 0;

                    for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {

                        Integer bookingOrderRoomNumId = bodp.getBookingOrderRoomNumId();

                        // 非预订价格
                        if (bookingOrderRoomNumId == null || bookingOrderRoomNumId < 10 || bodp.getDailyState() == 1) {
                            continue;
                        }
                        sumDayPrice += bodp.getPrice();

                    }
                    i = sumDayPrice - arMoney;
                } else {
                    // 判断当前还可以挂账多少。  房费减 - 当前挂账金额  = 可挂账金额
                    i = roomMoney - arMoney;
                }

                if (i < 0) {
                    i = 0;
                }

                JSONObject cashData = new JSONObject();
                cashData.put("Money", i / 100.0);
                cashData.put("OccurTime", HotelUtils.parseDate2Str(new Date()));
                cashData.put("PayMode", "可挂账金额");
                cashList.add(cashData);

                cashMoney += i / 100.0;
            }

            JSONObject addCostMsg = new JSONObject();
            addCostMsg.put("IsNeed", false);
            addCostMsg.put("AddMoney", AddMoney);
            if (AddMoney > 0) {
                addCostMsg.put("IsNeed", true);
                java.text.DecimalFormat df = new java.text.DecimalFormat("#.##");
                addCostMsg.put("AddMoney", df.format(AddMoney / 100.0));
                resultMap.put("FeeAdd", df.format(AddMoney / 100.0));
            }
            resultMap.put("PayMoney", cashMoney);
            resultMap.put("CostMoney", costMoney);
            resultMap.put("CostList", costList);
            resultMap.put("CashList", cashList);
            resultMap.put("AddCostMsg", addCostMsg);
            resultMap.put("PriceList", priceList);
            resultMap.put("Result", "True");
            resultMap.put("Msg", "查询入住信息成功");
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        log.info(resultMap.toString());
        return resultMap;
    }

    @Override
    public JSONObject checkRoom(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);

            Object registId = map.get("RegistId");
            if (registId == null || registId.toString().equals("")) {
                resultMap.put("Result", "True");
                resultMap.put("Msg", "请求查房成功");
                return resultMap;
            }
            Regist regist = registDao.selectById(Integer.parseInt(registId.toString()));
            if (regist == null) {
                throw new Exception("请求查房失败");
            }
            RoomCheckRecordRequest roomCheckRecordRequest = new RoomCheckRecordRequest();
            roomCheckRecordRequest.setRegistId(regist.getRegistId());
            roomCheckRecordRequest.setSessionToken(tokenId);
            roomCheckRecordRequest.setRoomInfoId(regist.getRoomNumId());
            roomCheckRecordRequest.setRoomTypeId(regist.getRoomTypeId());
            roomCheckRecordRequest.setRoomNum(regist.getRoomNum());
            ResponseData responseData = roomService.addOrUpdateRoomCheckRecord(roomCheckRecordRequest);
            if (responseData.getCode() != 1) {
                throw new Exception("请求查房失败");
            }
            resultMap.put("Result", "True");
            resultMap.put("Msg", "请求查房成功");
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject checkRoomResult(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject getTransactionMsg(Map<String, Object> map) {
        return null;
    }


    public JSONObject checkout(TbUserSession user, Integer registId) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "True");
        try {
            Regist regist = registDao.selectById(Integer.parseInt(registId.toString()));
            if (regist == null || regist.getState() != 0) {
                throw new Exception("当前订单状态不允许结账");
            }
            // 验证联房或团队是否只有一间未结房，如果剩余多间，则不退
            Integer teamCodeId = regist.getTeamCodeId();
            if (teamCodeId != null && teamCodeId > 0) {
                int regTeNum = 0;
                RegistSearch registSearch = new RegistSearch();
                registSearch.setHid(user.getHid());
                registSearch.setTeamCodeId(teamCodeId);
                List<Regist> registTeams = registDao.selectBySearch(registSearch);
                if (registTeams != null && registTeams.size() > 0) {
                    for (Regist rg : registTeams) {
                        Integer state = rg.getState();
                        if (state == 0 || state == 2) {
                            regTeNum++;
                        }
                    }
                }

                if (regTeNum > 1) {
                    throw new Exception("当前团队或联房有多间房间未退，请前台处理");
                }

            }

            // 1.配置信息
            BookingOrderConfig bookingOrderConfig = new BookingOrderConfig();
            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setRegistId(regist.getRegistId());
            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);
            if (bookingOrderConfigs != null && bookingOrderConfigs.size() > 0) {
                bookingOrderConfig = bookingOrderConfigs.get(0);
            }

            // 在住人
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(regist.getRegistId());
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
            String peopleName = "";
            for (RegistPerson registPerson : registPeople) {

                peopleName += registPerson.getPersonName() + " ";

            }


            // 查询协议单位账户
            HotelCompanyAccount hotelCompanyAccount = hotelCompanyAccountDao.selectById(regist.getCompanyAccountId());

            // 房价码信息

            // 2.查询账务信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setRegistId(regist.getRegistId());
            accountSearch.setRegistState(0);
            accountSearch.setIsCancel(0);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            // 总付款
            Integer sumPay = 0;
            // 总消费
            Integer sumSale = 0;

            // 总房费信息
            Integer sumRoomSale = 0;

            // 可用退款金额
            Integer canRefundMoney = 0;

            // 9320-微信支付 9300-支付宝支付 9100-国内卡预授权 9620-会员储值卡冻结 9800-协议挂账
            // 可以退款的账务信息 微信支付宝
            ArrayList<Account> refundList = new ArrayList<>();

            // 预授权
            ArrayList<Account> ysqList = new ArrayList<>();

            // 会员预授权
            ArrayList<Account> vipysqList = new ArrayList<>();

            // 挂账信息
            ArrayList<Account> arList = new ArrayList<>();
            Integer arMoney = 0;

            // 账务信息
            for (Account account : accounts) {
                Integer payType = account.getPayType();

                // 消费
                if (payType == 1) {

                    sumSale += account.getPrice();

                    //  房费
                    if (1 == account.getAccountType()) {

                        sumRoomSale += account.getPrice();

                    }

                    continue;

                }

                // 付款
                sumPay += account.getPrice();

                String payCodeId = account.getPayCodeId();

                if ("9800".equals(payCodeId)) {
                    arMoney += account.getPrice();
                    arList.add(account);
                }

                Integer thirdRefundState = account.getThirdRefundState();
                if (thirdRefundState == null || 0 != thirdRefundState) {
                    continue;
                }
                canRefundMoney += account.getPrice();
                switch (payCodeId) {
                    case "9320":
                        refundList.add(account);
                        break;
                    case "9300":
                        refundList.add(account);
                        break;
                    case "9100":
                        ysqList.add(account);
                        break;
                    case "9620":
                        vipysqList.add(account);
                        break;
                }

            }

            // region验证是否需要加收房费
            /**
             * 验证房价信息，是否需要加收房费
             */
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setRegistId(regist.getRegistId());
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, BookingOrderDailyPrice> dailyPriceMap = bookingOrderDailyPrices.stream().collect(Collectors.toMap(BookingOrderDailyPrice::getDailyTime, a -> a, (k1, k2) -> k2));

            // 需要添加的账务信息
            ArrayList<Account> addAccounts = new ArrayList<>();

            Integer AddMoney = 0;
            Date date1 = new Date();
            Integer dayInt = HotelUtils.parseDate2Int(date1);

            Integer businessDay = user.getBusinessDay();
            Integer checkInBussday = regist.getBusinessDay();

            Boolean isNightRoom = false;

            // 判断是否是凌晨房
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setHid(user.getHid());
            hotelSettingByParamId.setParamId(HOTEL_SETTING.NIGHT_ROOM);

            Object minObj = 6;

            if (minObj != null) {
                int hour = Integer.parseInt(minObj.toString());

                int hours = regist.getCheckinTime().getHours();

                if (hour > hours) {
                    isNightRoom = true;
                }

            }

            // 如果营业日等于当前时间
            if ((dayInt.equals(businessDay) || dayInt > businessDay) && !checkInBussday.equals(businessDay)) {
                dayInt = HotelUtils.parseDate2Int(HotelUtils.addDayGetNewDate(date1, -1));
            }

            // 如果是凌晨房 ，且开房日期不等于当前日期，则取昨天价格
            if (isNightRoom && !checkInBussday.equals(businessDay)) {
                dayInt = HotelUtils.parseDate2Int(HotelUtils.addDayGetNewDate(date1, -1));
            }

            //
            if (checkInBussday.equals(businessDay)) {
                dayInt = businessDay;
            }

            BookingOrderDailyPrice bookingOrderDailyPrice = dailyPriceMap.get(dayInt);
            if (bookingOrderDailyPrice == null) {
                bookingOrderDailyPrice = bookingOrderDailyPrices.get(bookingOrderDailyPrices.size() - 1);
                bookingOrderDailyPrice.getPrice();
                Account account = regAccounts(user, regist, registPeople.get(0), bookingOrderDailyPrice.getPrice(), "0002", "全天房费", 10, "客房", 1);
                account.setRemark("自动产生当天房费");
                if (regist.getCheckinType() == 2) {
                    account = regAccounts(user, regist, registPeople.get(0), bookingOrderDailyPrice.getPrice(), "0007", "钟点房费", 10, "客房", 1);
                    account.setRemark("自动产生钟点房费");
                }
                Integer integer = accountDao.saveAccount(account);
                AddMoney += bookingOrderDailyPrice.getPrice();

                if (integer < 1) {
                    throw new Exception("添加加收全天房费失败");
                }
                bookingOrderDailyPrice.setId(null);
                bookingOrderDailyPrice.setDailyTime(dayInt);
                bookingOrderDailyPrice.setDailyState(0);


                Integer integer1 = bookingOrderDailyPriceDao.saveBookingOrderDailyPrice(bookingOrderDailyPrice);
                // 添加当天房价失败
                if (integer1 < 1) {
                    accountDao.deleteAccount(account.getAccountId());
                    throw new Exception("添加加收全天房费失败");
                }


            } else if (bookingOrderDailyPrice.getDailyState() == 1) {
                bookingOrderDailyPrice.getPrice();
                Account account = regAccounts(user, regist, registPeople.get(0), bookingOrderDailyPrice.getPrice(), "0002", "全天房费", 10, "客房", 1);
                account.setRemark("自动产生当天房费");
                if (regist.getCheckinType() == 2) {
                    account = regAccounts(user, regist, registPeople.get(0), bookingOrderDailyPrice.getPrice(), "0007", "钟点房费", 10, "客房", 1);
                    account.setRemark("自动产生钟点房费");
                }
                AddMoney += bookingOrderDailyPrice.getPrice();
                bookingOrderDailyPrice.setDailyState(0);
                Integer integer = accountDao.saveAccount(account);
                if (integer < 1) {
                    throw new Exception("添加加收全天房费失败");
                }

                Integer integer1 = bookingOrderDailyPriceDao.editBookingOrderDailyPrice(bookingOrderDailyPrice);
                // 添加当天房价失败
                if (integer1 < 1) {
                    accountDao.deleteAccount(account.getAccountId());
                    throw new Exception("添加加收全天房费失败");
                }
            }

            // 判断离店时间是否是和入住日期一致,则盘是否要加收房费,或者入住时间和离店时间一致都需要判断是否加收房费
            Date date = new Date();

            Integer checkInTime = HotelUtils.parseDate2Int(regist.getCheckinTime());

            Integer checkOutTime = HotelUtils.parseDate2Int(regist.getCheckoutTime());

            Integer nowDate = HotelUtils.parseDate2Int(date);

            Integer hid = user.getHid();

         /*   if(!hid.equals(2126)&&!hid.equals(2082)){
                if(nowDate<checkOutTime){
                    throw new Exception("未到退房时间，请误结账");
                }
            }*/
            if (date.getTime() > regist.getCheckoutTime().getTime()) {
                Integer checkinType = regist.getCheckinType();
               /* if (checkinType == 2) {
                    throw new Exception("钟点房超时不允许结账");
                }*/

                if (!dayInt.equals(checkInTime) || checkInTime.equals(checkOutTime)) {

                    RoomRateCodeSpecific rrcs = new RoomRateCodeSpecific();
                    rrcs.setAddAllDay("18:00");
                    rrcs.setAddHalfDay("16:00");
                    rrcs.setOvertimeAddMoney(0);

                    RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
                    roomRateCodeSpecificSearch.setHid(user.getHid());
                    roomRateCodeSpecificSearch.setRateId(regist.getRoomRateCodeId());
                    roomRateCodeSpecificSearch.setRoomTypeId(regist.getRoomTypeId());

                    List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);

                    if (roomRateCodeSpecifics != null && roomRateCodeSpecifics.size() > 0) {
                        rrcs = roomRateCodeSpecifics.get(0);
                    }

                    String dateStr = HotelUtils.currentDate();

                    Long nowTime = date.getTime();

                    Long allDayTime = HotelUtils.parseStr2Date(dateStr + " " + rrcs.getAddAllDay() + ":00").getTime();
                    Long halfDayTime = HotelUtils.parseStr2Date(dateStr + " " + rrcs.getAddHalfDay() + ":00").getTime();

                    Account account = regAccounts(user, regist, registPeople.get(0), AddMoney, "0004", "延退房费", 10, "客房", 1);


                    if (nowTime > allDayTime) {
                        AddMoney += bookingOrderDailyPrice.getPrice();
                        account.setRemark("超时退房，退房时间：" + HotelUtils.currentTime() + "，加收全天房费");
                    } else if (nowTime > halfDayTime) {
                        account.setRemark("超时退房，退房时间：" + HotelUtils.currentTime() + "，加收半天房费");
                        AddMoney += (bookingOrderDailyPrice.getPrice() / 2);
                    } else {

                        Long checkouTimeLong = regist.getCheckouTimeLong();

                        Long diff = nowTime - checkouTimeLong;
                        int diffInt = diff.intValue() / 1000;

                        int overTime = diffInt / 3600;

                        int i = overTime % 3600;
                        if (i > 180) {

                            overTime += 1;

                        }

                        int i1 = overTime * rrcs.getOvertimeAddMoney();

                        AddMoney += i1;

                        account.setRemark("超时退房，退房时间：" + HotelUtils.currentTime() + "，加收" + i1 / 100.0 + "小时房费");

                    }
                    account.setPrice(AddMoney);
                    account.setUintPrice(AddMoney);
                    if (AddMoney > 0) {
                        Integer integer = accountDao.saveAccount(account);
                        if (integer < 1) {
                            throw new Exception("添加加收房费失败");
                        }
                    } else {
                        AddMoney = 0;
                    }
                }
            }


            sumSale += AddMoney;
            sumRoomSale += AddMoney;


            // endregion


            // 计算是否可挂账,1可挂账
            Integer autoAr = bookingOrderConfig.getAutoAr();

            // 可以挂账金额
            Integer canArMoney = 0;

            // 可挂账
            if (autoAr == 1 && regist.getCompanyAccountId() != null) {

                // 如果 是预订转入住
                Integer bookingOrderId = regist.getBookingOrderId();

                if (bookingOrderId != null && bookingOrderId > 0) {

                    Integer sumDayPrice = 0;

                    for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {

                        Integer bookingOrderRoomNumId = bodp.getBookingOrderRoomNumId();

                        // 非预订价格
                        if (bookingOrderRoomNumId == null || bookingOrderRoomNumId < 10 || bodp.getDailyState() == 1) {
                            continue;
                        }

                        sumDayPrice += bodp.getPrice();

                    }
                    canArMoney = sumDayPrice - arMoney;
                } else {
                    // 判断当前还可以挂账多少。  房费减 - 当前挂账金额  = 可挂账金额
                    canArMoney = sumRoomSale - arMoney;
                }

                if (canArMoney < 0) {
                    canArMoney = 0;
                }

            }

            sumPay += canArMoney;

            // 房费差额 如果付款小于消费 ，则提示报错
            int roomDiff = sumPay - sumSale;
            if (roomDiff + canArMoney < 0) {
                throw new Exception("可用余额不足，请去前台处理");
            }

            // 退款金额 小于  可用退款金额+可挂账金额 则不可退款
            if (roomDiff > canRefundMoney) {
                throw new Exception("可用退款金额不足，请去前台处理。");
            }

            // region  进行退款操作
            ArrayList<Account> updateAccounts = new ArrayList<>();

            Oprecord oprecord = new Oprecord(user);
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setRoomNum(regist.getRoomNum());
            oprecord.setBookingOrderId(regist.getBookingOrderId());
            oprecord.setOccurTime(HotelUtils.currentTime());

            // 1. 先把可挂房费 挂账
            ArrayList<AddArAccount> addArAccounts = new ArrayList<>();

            if (canArMoney > 0) {

                // 添加挂账信息
                Account arAccount = regAccounts(user, regist, registPeople.get(0), canArMoney, "9800", "AR帐", 9, "AR帐", 2);


                arAccount.setGroupAccount(0);
                arAccount.setRegistState(1);
                arAccount.setCompanyId(hotelCompanyAccount.getHotelCompanyId());

                try {
                    HotelCompanyInfo hotelCompanyInfo = hotelCompanyInfoDao.selectById(hotelCompanyAccount.getHotelCompanyId());
                    arAccount.setCompanyName(hotelCompanyInfo.getCompanyName());
                } catch (Exception esok) {
                    log.error("",esok);

                }

                HotelCompanyArRecode hotelCompanyArRecode = new HotelCompanyArRecode();

                hotelCompanyArRecode.setCompanyId(hotelCompanyAccount.getHotelCompanyId());
                hotelCompanyArRecode.setGroupCompanyId(hotelCompanyAccount.getGroupCompanyId());
                hotelCompanyArRecode.setCompanyAccountId(hotelCompanyAccount.getId());
                // 房间id
                hotelCompanyArRecode.setRoomInfoId(regist.getRoomNumId());
                // 房号
                hotelCompanyArRecode.setRoomNo(regist.getRoomNum());
                hotelCompanyArRecode.setMoney(canArMoney);
                // 房间id
                hotelCompanyArRecode.setRegistId(regist.getRegistId());

                // 操作人id
                //  hotelCompanyArRecode.setOperatorId(Integer.parseInt(user.getUserId()));
                hotelCompanyArRecode.setOperatorName(user.getUserName());
                // 备注
                hotelCompanyArRecode.setRemark(user.getUserName() + "：结账自动挂房费 ");

                hotelCompanyArRecode.setSettleId(regist.getRegistId());
                hotelCompanyArRecode.setOperatTime(date);
                hotelCompanyArRecode.setBusinessShiftId(user.getClassId());
                hotelCompanyArRecode.setBusinessDay(user.getBusinessDay());
                hotelCompanyArRecode.setCreateUserId(user.getUserId());
                hotelCompanyArRecode.setCreateUserName(user.getUserName());
                hotelCompanyArRecode.setCreateTime(date);
                hotelCompanyArRecode.setUpdateUserId(user.getUserId());
                hotelCompanyArRecode.setUpdateUserName(user.getUserName());
                hotelCompanyArRecode.setCreateTime(date);
                hotelCompanyArRecode.setHid(user.getHid());
                hotelCompanyArRecode.setHotelGroupId(user.getHotelGroupId());
                hotelCompanyArRecode.setPayState(0);
                hotelCompanyArRecode.setPersonName(peopleName);

                AddArAccount addArAccount = new AddArAccount();
                addArAccount.setAccount(arAccount);
                addArAccount.setHotelCompanyArRecode(hotelCompanyArRecode);
                arAccount.setRoomInfoId(regist.getRoomNumId());
                arAccount.setRoomTypeId(regist.getRoomTypeId());
                arAccount.setGroupAccount(0);
                Integer integer = accountDao.saveAccount(arAccount);

                if (integer < 1) {
                    throw new Exception("添加挂账信息失败");
                }

                hotelCompanyArRecode.setTransactionId(arAccount.getAccountId());

                Integer insert = hotelCompanyArRecodeDao.insert(hotelCompanyArRecode);
                if (insert < 1) {
                    accountDao.deleteAccount(arAccount.getAccountId());
                    throw new Exception("添加挂账信息失败");
                }


                HotelCompanyAccountInfo hotelCompanyAccountInfo = hotelCompanyAccountInfoDao.selectById(hotelCompanyAccount.getId());
                hotelCompanyAccountInfo.setId(hotelCompanyAccount.getId());
                hotelCompanyAccountInfo.setNoOffWriteMoney(hotelCompanyAccountInfo.getNoOffWriteMoney() + canArMoney);
                hotelCompanyAccountInfo.setMaxLimit(hotelCompanyAccountInfo.getMaxLimit() - canArMoney);
                Integer update = hotelCompanyAccountInfoDao.update(hotelCompanyAccountInfo);

                if (update < 1) {
                    throw new Exception("修改账户信息失败");
                }

                oprecord.setDescription("自助结账自动把:" + canArMoney / 100.0 + " 元房费转AR账");
                this.addOprecords(oprecord);

            }


            // 进行退款操作
            // 退款顺序  微信、支付宝、预授权、会员冻结
            // 批量修改账务信息集合

            // 2.会员冻结完成
            for (Account account : vipysqList) {

                Integer price = account.getPrice();

                Integer refundMoney = 0;

                if (price >= roomDiff) {
                    refundMoney = roomDiff;
                    roomDiff = 0;
                } else {

                    roomDiff -= price;
                    refundMoney = price;

                }

                JSONObject jsonObject = new JSONObject();
                jsonObject.put(ER.SESSION_TOKEN, user.getSessionId());
                jsonObject.put("money", price - refundMoney);
                jsonObject.put("accountId", account.getThirdAccoutId());

                ResponseData responseData = memberService.finishMemberFreeze(jsonObject);
                if (responseData.getResult().equals(ER.ERR)) {
                    throw new Exception(responseData.getMsg());
                }
                Account upaAccount = new Account();
                upaAccount.setPrice(price - refundMoney);
                upaAccount.setThirdRefundState(2);
                upaAccount.setRefundPrice(price - refundMoney);
                upaAccount.setAccountId(account.getAccountId());
                upaAccount.setRegistId(regist.getRegistId());
                upaAccount.setPayCodeId("9600");
                upaAccount.setBusinessDay(user.getBusinessDay());
//                upaAccount.setPayCodeId("会员储值卡");
                upaAccount.setRegistPersonId(account.getRegistPersonId());

                accountDao.editAccount(upaAccount);


            }

            // 3.预授权完成
            for (Account account : ysqList) {
                Integer price = account.getPrice();
                Integer refundMoney = 0;
                if (price >= roomDiff) {
                    refundMoney = roomDiff;
                    roomDiff = 0;
                } else {
                    roomDiff -= price;
                    refundMoney = price;
                }
                account.setThirdRefundState(2);
                account.setPrice(price - refundMoney);
                account.setRefundPrice(price - refundMoney);
                account.setBusinessDay(user.getBusinessDay());
                AccountThirdPayRecodeSearch accountThirdPayRecodeSearch = new AccountThirdPayRecodeSearch();
                accountThirdPayRecodeSearch.setHid(user.getHid());
                accountThirdPayRecodeSearch.setAccountId(account.getAccountId());
                List<AccountThirdPayRecode> accountThirdPayRecodes = accountThirdPayRecodeDao.selectBySearch(accountThirdPayRecodeSearch);
                if (accountThirdPayRecodes == null || accountThirdPayRecodes.size() != 1) {
                    throw new Exception("账单异常");
                }
                AccountThirdPayRecode accountThirdPayRecode = accountThirdPayRecodes.get(0);
                accountThirdPayRecode.setFinishBusinessDay(user.getBusinessDay());
                accountThirdPayRecode.setRefund(price - refundMoney);
                accountThirdPayRecode.setThirdRefundState(2);
                accountThirdPayRecode.setUpdateTime(new Date());
                accountThirdPayRecode.setUpdateCalssId(user.getClassId());
                accountThirdPayRecode.setUpdateUserName(user.getUserName());
                accountThirdPayRecode.setUpdateUserId(user.getUserId());

                //取消预授权
                if ((price - refundMoney) == 0) {
                    accountThirdPayRecode.setThirdState(2);
                    account.setRegistState(3);
                } else {
                    accountThirdPayRecode.setThirdState(1);
                }
                Integer update = 0;

                update = accountThirdPayRecodeDao.update(accountThirdPayRecode);
                if (update < 1) {
                    throw new Exception("更新账务信息失败");
                }

                update = accountDao.editAccount(account);
                if (update < 1) {
                    throw new Exception("更新账务信息失败");
                }

            }

            // 4.微信、支付宝退款
            for (Account account : refundList) {

                Integer price = account.getPrice();

                if (roomDiff == 0) {
                    continue;
                }

                Integer refundMoney = 0;

                if (price >= roomDiff) {
                    refundMoney = roomDiff;
                    roomDiff = 0;
                } else {
                    roomDiff -= price;
                    refundMoney = price;

                }

                JSONObject jsonObject = new JSONObject();
                jsonObject.put(ER.SESSION_TOKEN, user.getSessionId());
                jsonObject.put("refundMoney", refundMoney);
                jsonObject.put("accountId", account.getAccountId());

                ResponseData responseData = accountCanService.refundMoneyJiaTuiKuan(jsonObject);

                if (responseData.getResult().equals(ER.ERR)) {
                    throw new Exception(responseData.getMsg());
                }

                account.setThirdRefundState(1);
                account.setRefundPrice(refundMoney);

            }


            // 进行结账
            CheckoutParam checkoutParam = new CheckoutParam();
            checkoutParam.setType(1);
            checkoutParam.setRegistId(regist.getRegistId());
            checkoutParam.setSessionToken(user.getSessionId());
            ResponseData responseData = pmsOrderService.checkOut(checkoutParam);

            if (responseData.getResult().equals(ER.ERR)) {
                throw new Exception(responseData.getMsg());
            }

            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    SmartLockRequest smartLockRequest = new SmartLockRequest();
                    smartLockRequest.setSessionToken(user.getSessionId());
                    smartLockRequest.setRegistId(regist.getRegistId());
                    smartLockRequest.setCheckinTime(regist.getCheckinTime());
                    smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                    smartLockRequest.setRoomTypeId(regist.getRoomTypeId());
                    smartLockRequest.setRoomTypeName(regist.getRoomTypeName());
                    smartLockRequest.setRoomNumId(regist.getRoomNumId());
                    smartLockRequest.setRoomNum(regist.getRoomNum());
                    smartLockRequest.setHid(regist.getHid());
                    smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                    smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                    smartLockRequest.setCheckinType(regist.getCheckinType());
                    smartLockRequest.setBussType(2);
                    List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                    for (int j = 0; j < registPeople.size(); j++) {
                        RegistPerson registPersonInfo = registPeople.get(j);
                        SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                        BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                        peoplesList.add(registPersonDetail);
                    }
                    smartLockRequest.setPeoples(peoplesList);
                    baseService.SmartLockCheckout(smartLockRequest);
                }
            });


        } catch (Exception e) {
            log.error("",e);
        }
        return resultMap;
    }
    @Override
    public JSONObject checkOutRegister(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "True");
        try {
            String tokenId = map.containsKey("sessionToken") ? map.get("sessionToken").toString() : map.get("appKey").toString();
//            if (StringUtil.isEmpty(tokenId)){
//                tokenId = map.get("appKey").toString();
//            }
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);

            Object registId = map.get("registId");
            if (registId == null) {
                throw new Exception("请传入正确的查询参数");
            }
            //如果是第三方PMS调用不直接调用退款
            if (map.containsKey("type") && map.get("type").toString().equals("2")) {
                return this.checkout(user, Integer.parseInt(registId.toString()));
            }

            // 营业日时间戳
            long bussinessDayTime = HotelUtils.parseInt2Date(user.getBusinessDay()).getTime();

            // 当前时间戳
            long nowTimeCheck = new Date().getTime();

            Long diffCheck = (nowTimeCheck - bussinessDayTime) / 60 / 60 / 24 / 1000;
            if (diffCheck.intValue() > 3) {
                throw new Exception("营业日具体当前日期相差3天以上，无法结账");
            }


            Regist regist = registDao.selectById(Integer.parseInt(registId.toString()));

            String smainId = "machineCheckout:" + regist.getHid() + "-" + regist.getRegistId();

            Boolean ok1 = stringRedisTemplate.opsForValue().setIfAbsent(smainId, "", 1, TimeUnit.MINUTES);
            //设置失败, 说明之前已经有数据了, 直接返回
            if (Boolean.FALSE.equals(ok1)) {
                resultMap.put("Result", "False");
                resultMap.put("Msg", ER.EXECUTE_SUCCESS);
                return resultMap;
            }


            // 入住时间
            Integer integer2 = HotelUtils.parseDate2Int(regist.getCheckinTime());

            // 登记单离店时间
            Integer checkoutInt = HotelUtils.parseDate2Int(regist.getCheckoutTime());

            // 当前时间 into
            Integer nowDateInt = HotelUtils.parseDate2Int(new Date());

            // 如果退房时间不是当天，并且不是入住时间，则把regist表中离店日期，改为当天 + 时分
            if (!checkoutInt.equals(nowDateInt) && !integer2.equals(nowDateInt)) {
                String newDate = HotelUtils.parseDate2Str(new Date()).substring(0, 10);
                String oldCheckoutTime = HotelUtils.parseDate2Str(regist.getCheckoutTime());
                String[] s = oldCheckoutTime.split(" ");

                String newCheckOutDate = newDate + " " + s[1];
                regist.setCheckoutTime(HotelUtils.parseStr2Date(newCheckOutDate));
            }


//            String newDate = HotelUtils.parseDate2Str(new Date()).substring(0, 10);
//            String oldCheckoutTime = HotelUtils.parseDate2Str(regist.getCheckoutTime());
//            String[] s = oldCheckoutTime.split(" ");
//
//            String newCheckOutDate = newDate + " " + s[1];
//            regist.setCheckoutTime(HotelUtils.parseStr2Date(newCheckOutDate));

            if (regist == null || regist.getState() != 0) {
                throw new Exception("当前订单状态不允许结账");
            }

            // 验证联房或团队是否只有一间未结房，如果剩余多间，则不退
            Integer teamCodeId = regist.getTeamCodeId();
            if (teamCodeId != null && teamCodeId > 0) {

                int regTeNum = 0;

                RegistSearch registSearch = new RegistSearch();
                registSearch.setHid(user.getHid());
                registSearch.setTeamCodeId(teamCodeId);

                List<Regist> registTeams = registDao.selectBySearch(registSearch);

                if (registTeams != null && registTeams.size() > 0) {

                    for (Regist rg : registTeams) {

                        Integer state = rg.getState();

                        if (state == 0 || state == 2) {

                            regTeNum++;

                        }

                    }

                }
                //判断如果是主账房并且有成员房没有退
                if (regTeNum > 1) {
                    throw new Exception("当前团队或联房有多间房间未退，请前台处理");
                }

            }


            // 1.配置信息
            BookingOrderConfig bookingOrderConfig = new BookingOrderConfig();
            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setRegistId(regist.getRegistId());
            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);
            if (bookingOrderConfigs != null && bookingOrderConfigs.size() > 0) {
                bookingOrderConfig = bookingOrderConfigs.get(0);
            }

            // 在住人
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(regist.getRegistId());
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
            String peopleName = "";
            for (RegistPerson registPerson : registPeople) {

                peopleName += registPerson.getPersonName() + " ";

            }

            // 查询协议单位账户
            HotelCompanyAccount hotelCompanyAccount = hotelCompanyAccountDao.selectById(regist.getCompanyAccountId());

            // 房价码信息

            // 2.查询账务信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setRegistId(regist.getRegistId());
            accountSearch.setRegistState(0);
            accountSearch.setIsCancel(0);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            // 总付款
            Integer sumPay = 0;
            // 总消费
            Integer sumSale = 0;

            // 总房费信息
            Integer sumRoomSale = 0;

            // 可用退款金额
            Integer canRefundMoney = 0;

            // 9320-微信支付 9300-支付宝支付 9100-国内卡预授权 9620-会员储值卡冻结 9800-协议挂账
            // 可以退款的账务信息 微信支付宝
            ArrayList<Account> refundList = new ArrayList<>();

            // 预授权
            ArrayList<Account> ysqList = new ArrayList<>();

            // 会员预授权
            ArrayList<Account> vipysqList = new ArrayList<>();

            // 挂账信息
            ArrayList<Account> arList = new ArrayList<>();
            Integer arMoney = 0;

            // 账务信息
            for (Account account : accounts) {
                Integer payType = account.getPayType();

                // 消费
                if (payType == 1) {

                    sumSale += account.getPrice();

                    //  房费
                    if (1 == account.getAccountType()) {

                        sumRoomSale += account.getPrice();

                    }

                    continue;

                }

                // 付款
                sumPay += account.getPrice();

                String payCodeId = account.getPayCodeId();

                if ("9800".equals(payCodeId)) {
                    arMoney += account.getPrice();
                    arList.add(account);
                }

                Integer thirdRefundState = account.getThirdRefundState();
                if (thirdRefundState == null || 0 != thirdRefundState) {
                    continue;
                }
                canRefundMoney += account.getPrice();
                switch (payCodeId) {
                    case "9320":
                        refundList.add(account);
                        break;
                    case "9300":
                        refundList.add(account);
                        break;
                    case "9100":
                        ysqList.add(account);
                        break;
                    case "9620":
                        vipysqList.add(account);
                        break;
                }

            }

            // region验证是否需要加收房费
            /**
             * 验证房价信息，是否需要加收房费
             */
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setRegistId(regist.getRegistId());
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, BookingOrderDailyPrice> dailyPriceMap = bookingOrderDailyPrices.stream().collect(Collectors.toMap(BookingOrderDailyPrice::getDailyTime, a -> a, (k1, k2) -> k2));

            // 需要添加的账务信息
            ArrayList<Account> addAccounts = new ArrayList<>();

            Integer AddMoney = 0;
            Date date1 = new Date();
            Integer dayInt = HotelUtils.parseDate2Int(date1);

            Integer businessDay = user.getBusinessDay();
            Integer checkInBussday = regist.getBusinessDay();

            Boolean isNightRoom = false;

            // 判断是否是凌晨房
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setHid(user.getHid());
            hotelSettingByParamId.setParamId(HOTEL_SETTING.NIGHT_ROOM);

            Object minObj = 6;

            if (minObj != null) {
                int hour = Integer.parseInt(minObj.toString());

                int hours = regist.getCheckinTime().getHours();

                if (hour > hours) {
                    isNightRoom = true;
                }

            }

            // 如果营业日等于当前时间
            if ((dayInt.equals(businessDay) || dayInt > businessDay) && !checkInBussday.equals(businessDay)) {
                dayInt = HotelUtils.parseDate2Int(HotelUtils.addDayGetNewDate(date1, -1));
            }

            // 如果是凌晨房 ，且开房日期不等于当前日期，则取昨天价格
            if (isNightRoom && !checkInBussday.equals(businessDay)) {
                dayInt = HotelUtils.parseDate2Int(HotelUtils.addDayGetNewDate(date1, -1));
            }

            //
            if (checkInBussday.equals(businessDay)) {
                dayInt = businessDay;
            }

            BookingOrderDailyPrice bookingOrderDailyPrice = dailyPriceMap.get(dayInt);
            if (bookingOrderDailyPrice == null) {
                bookingOrderDailyPrice = bookingOrderDailyPrices.get(bookingOrderDailyPrices.size() - 1);
                bookingOrderDailyPrice.getPrice();
                Account account = regAccounts(user, regist, registPeople.get(0), bookingOrderDailyPrice.getPrice(), "0002", "全天房费", 10, "客房", 1);
                account.setRemark("自动产生当天房费");
                if (regist.getCheckinType() == 2) {
                    account = regAccounts(user, regist, registPeople.get(0), bookingOrderDailyPrice.getPrice(), "0007", "钟点房费", 10, "客房", 1);
                    account.setRemark("自动产生钟点房费");
                }
                Integer integer = accountDao.saveAccount(account);
                AddMoney += bookingOrderDailyPrice.getPrice();

                if (integer < 1) {
                    throw new Exception("添加加收全天房费失败");
                }
                bookingOrderDailyPrice.setId(null);
                bookingOrderDailyPrice.setDailyTime(dayInt);
                bookingOrderDailyPrice.setDailyState(0);


                Integer integer1 = bookingOrderDailyPriceDao.saveBookingOrderDailyPrice(bookingOrderDailyPrice);
                // 添加当天房价失败
                if (integer1 < 1) {
                    accountDao.deleteAccount(account.getAccountId());
                    throw new Exception("添加加收全天房费失败");
                }


            } else if (bookingOrderDailyPrice.getDailyState() == 1) {
                bookingOrderDailyPrice.getPrice();
                Account account = regAccounts(user, regist, registPeople.get(0), bookingOrderDailyPrice.getPrice(), "0002", "全天房费", 10, "客房", 1);
                account.setRemark("自动产生当天房费");
                if (regist.getCheckinType() == 2) {
                    account = regAccounts(user, regist, registPeople.get(0), bookingOrderDailyPrice.getPrice(), "0007", "钟点房费", 10, "客房", 1);
                    account.setRemark("自动产生钟点房费");
                }
                AddMoney += bookingOrderDailyPrice.getPrice();
                bookingOrderDailyPrice.setDailyState(0);
                Integer integer = accountDao.saveAccount(account);
                if (integer < 1) {
                    throw new Exception("添加加收全天房费失败");
                }

                Integer integer1 = bookingOrderDailyPriceDao.editBookingOrderDailyPrice(bookingOrderDailyPrice);
                // 添加当天房价失败
                if (integer1 < 1) {
                    accountDao.deleteAccount(account.getAccountId());
                    throw new Exception("添加加收全天房费失败");
                }
            }

            // 判断离店时间是否是和入住日期一致,则盘是否要加收房费,或者入住时间和离店时间一致都需要判断是否加收房费
            Date date = new Date();

            Integer checkInTime = HotelUtils.parseDate2Int(regist.getCheckinTime());

            Integer checkOutTime = HotelUtils.parseDate2Int(regist.getCheckoutTime());

            Integer nowDate = HotelUtils.parseDate2Int(date);

            Integer hid = user.getHid();

         /*   if(!hid.equals(2126)&&!hid.equals(2082)){
                if(nowDate<checkOutTime){
                    throw new Exception("未到退房时间，请误结账");
                }
            }*/
            if (date.getTime() > regist.getCheckoutTime().getTime()) {
                Integer checkinType = regist.getCheckinType();
               /* if (checkinType == 2) {
                    throw new Exception("钟点房超时不允许结账");
                }*/
                RoomRateCodeSpecific rrcs = new RoomRateCodeSpecific();
                rrcs.setAddAllDay("18:00");
                rrcs.setAddHalfDay("16:00");
                rrcs.setOvertimeAddMoney(0);

                RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
                roomRateCodeSpecificSearch.setHid(user.getHid());
                roomRateCodeSpecificSearch.setRateId(regist.getRoomRateCodeId());
                roomRateCodeSpecificSearch.setRoomTypeId(regist.getRoomTypeId());

                List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);

                if (roomRateCodeSpecifics != null && roomRateCodeSpecifics.size() > 0) {
                    rrcs = roomRateCodeSpecifics.get(0);
                }

                String dateStr = HotelUtils.currentDate();

                Long nowTime = date.getTime();

                Long allDayTime = HotelUtils.parseStr2Date(dateStr + " " + rrcs.getAddAllDay() + ":00").getTime();
                Long halfDayTime = HotelUtils.parseStr2Date(dateStr + " " + rrcs.getAddHalfDay() + ":00").getTime();

                Account account = regAccounts(user, regist, registPeople.get(0), AddMoney, "0004", "延退房费", 10, "客房", 1);


                if (nowTime > allDayTime) {
                    AddMoney += bookingOrderDailyPrice.getPrice();
                    account.setRemark("超时退房，退房时间：" + HotelUtils.currentTime() + "，加收全天房费");
                } else if (nowTime > halfDayTime) {
                    account.setRemark("超时退房，退房时间：" + HotelUtils.currentTime() + "，加收半天房费");
                    AddMoney += (bookingOrderDailyPrice.getPrice() / 2);
                } else {

                    Long checkouTimeLong = regist.getCheckouTimeLong();

                    Long diff = nowTime - checkouTimeLong;
                    int diffInt = diff.intValue() / 1000;

                    int overTime = diffInt / 3600;

                    int i = overTime % 3600;
                    if (i > 180) {

                        overTime += 1;

                    }

                    int i1 = overTime * rrcs.getOvertimeAddMoney();

                    AddMoney += i1;

                    account.setRemark("超时退房，退房时间：" + HotelUtils.currentTime() + "，加收" + i1 / 100.0 + "小时房费");

                }
                account.setPrice(AddMoney);
                account.setUintPrice(AddMoney);
                if (AddMoney > 0) {
                    Integer integer = accountDao.saveAccount(account);
                    if (integer < 1) {
                        throw new Exception("添加加收房费失败");
                    }
                } else {
                    AddMoney = 0;
                }
            }


            sumSale += AddMoney;
            sumRoomSale += AddMoney;


            // endregion


            // 计算是否可挂账,1可挂账
            Integer autoAr = bookingOrderConfig.getAutoAr();

            // 可以挂账金额
            Integer canArMoney = 0;

            // 可挂账
            if (autoAr == 1 && regist.getCompanyAccountId() != null) {

                // 如果 是预订转入住
                Integer bookingOrderId = regist.getBookingOrderId();

                if (bookingOrderId != null && bookingOrderId > 0) {

                    Integer sumDayPrice = 0;

                    for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {

                        Integer bookingOrderRoomNumId = bodp.getBookingOrderRoomNumId();

                        // 非预订价格
                        if (bookingOrderRoomNumId == null || bookingOrderRoomNumId < 10 || bodp.getDailyState() == 1) {
                            continue;
                        }

                        sumDayPrice += bodp.getPrice();

                    }
                    canArMoney = sumDayPrice - arMoney;
                } else {
                    // 判断当前还可以挂账多少。  房费减 - 当前挂账金额  = 可挂账金额
                    canArMoney = sumRoomSale - arMoney;
                }

                if (canArMoney < 0) {
                    canArMoney = 0;
                }

            }

            sumPay += canArMoney;

            // 房费差额 如果付款小于消费 ，则提示报错
            int roomDiff = sumPay - sumSale;
            if (roomDiff + canArMoney < 0) {
                throw new Exception("可用余额不足，请去前台处理");
            }

            // 退款金额 小于  可用退款金额+可挂账金额 则不可退款
            if (roomDiff > canRefundMoney) {
                throw new Exception("可用退款金额不足，请去前台处理。");
            }

            // region  进行退款操作
            ArrayList<Account> updateAccounts = new ArrayList<>();

            Oprecord oprecord = new Oprecord(user);
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setRoomNum(regist.getRoomNum());
            oprecord.setBookingOrderId(regist.getBookingOrderId());
            oprecord.setOccurTime(HotelUtils.currentTime());

            // 1. 先把可挂房费 挂账
            ArrayList<AddArAccount> addArAccounts = new ArrayList<>();

            if (canArMoney > 0) {

                // 添加挂账信息
                Account arAccount = regAccounts(user, regist, registPeople.get(0), canArMoney, "9800", "AR帐", 9, "AR帐", 2);


                arAccount.setGroupAccount(0);
                arAccount.setRegistState(1);
                arAccount.setCompanyId(hotelCompanyAccount.getHotelCompanyId());

                try {
                    HotelCompanyInfo hotelCompanyInfo = hotelCompanyInfoDao.selectById(hotelCompanyAccount.getHotelCompanyId());
                    arAccount.setCompanyName(hotelCompanyInfo.getCompanyName());
                } catch (Exception esok) {
                    log.error("",esok);
                }

                HotelCompanyArRecode hotelCompanyArRecode = new HotelCompanyArRecode();

                hotelCompanyArRecode.setCompanyId(hotelCompanyAccount.getHotelCompanyId());
                hotelCompanyArRecode.setGroupCompanyId(hotelCompanyAccount.getGroupCompanyId());
                hotelCompanyArRecode.setCompanyAccountId(hotelCompanyAccount.getId());
                // 房间id
                hotelCompanyArRecode.setRoomInfoId(regist.getRoomNumId());
                // 房号
                hotelCompanyArRecode.setRoomNo(regist.getRoomNum());
                hotelCompanyArRecode.setMoney(canArMoney);
                // 房间id
                hotelCompanyArRecode.setRegistId(regist.getRegistId());

                // 操作人id
                //  hotelCompanyArRecode.setOperatorId(Integer.parseInt(user.getUserId()));
                hotelCompanyArRecode.setOperatorName(user.getUserName());
                // 备注
                hotelCompanyArRecode.setRemark(user.getUserName() + "：结账自动挂房费 ");

                hotelCompanyArRecode.setSettleId(regist.getRegistId());
                hotelCompanyArRecode.setOperatTime(date);
                hotelCompanyArRecode.setBusinessShiftId(user.getClassId());
                hotelCompanyArRecode.setBusinessDay(user.getBusinessDay());
                hotelCompanyArRecode.setCreateUserId(user.getUserId());
                hotelCompanyArRecode.setCreateUserName(user.getUserName());
                hotelCompanyArRecode.setCreateTime(date);
                hotelCompanyArRecode.setUpdateUserId(user.getUserId());
                hotelCompanyArRecode.setUpdateUserName(user.getUserName());
                hotelCompanyArRecode.setCreateTime(date);
                hotelCompanyArRecode.setHid(user.getHid());
                hotelCompanyArRecode.setHotelGroupId(user.getHotelGroupId());
                hotelCompanyArRecode.setPayState(0);
                hotelCompanyArRecode.setPersonName(peopleName);


                // 先添加AR
                AddArAccount addArAccount = new AddArAccount();
                addArAccount.setAccount(arAccount);
                addArAccount.setHotelCompanyArRecode(hotelCompanyArRecode);
                hotelCompanyArRecode.setTransactionId(arAccount.getAccountId());

                Integer insert = hotelCompanyArRecodeDao.insert(hotelCompanyArRecode);
                if (insert < 1) {
                    accountDao.deleteAccount(arAccount.getAccountId());
                    throw new Exception("添加挂账信息失败");
                }

                arAccount.setRoomInfoId(regist.getRoomNumId());
                arAccount.setRoomTypeId(regist.getRoomTypeId());
                arAccount.setGroupAccount(0);
                arAccount.setThirdAccoutId(hotelCompanyArRecode.getId() + "");
                Integer integer = accountDao.saveAccount(arAccount);

                if (integer < 1) {
                    throw new Exception("添加挂账信息失败");
                }

                HotelCompanyAccountInfo hotelCompanyAccountInfo = hotelCompanyAccountInfoDao.selectById(hotelCompanyAccount.getId());
                hotelCompanyAccountInfo.setId(hotelCompanyAccount.getId());
                hotelCompanyAccountInfo.setNoOffWriteMoney(hotelCompanyAccountInfo.getNoOffWriteMoney() + canArMoney);
                hotelCompanyAccountInfo.setMaxLimit(hotelCompanyAccountInfo.getMaxLimit() - canArMoney);
                Integer update = hotelCompanyAccountInfoDao.update(hotelCompanyAccountInfo);

                if (update < 1) {
                    throw new Exception("修改账户信息失败");
                }

                oprecord.setDescription("自助结账自动把:" + canArMoney / 100.0 + " 元房费转AR账");
                this.addOprecords(oprecord);

            }


            // 进行退款操作
            // 退款顺序  微信、支付宝、预授权、会员冻结
            // 批量修改账务信息集合

            // 2.会员冻结完成
            for (Account account : vipysqList) {

                Integer price = account.getPrice();

                Integer refundMoney = 0;

                if (price >= roomDiff) {
                    refundMoney = roomDiff;
                    roomDiff = 0;
                } else {

                    roomDiff -= price;
                    refundMoney = price;

                }

                JSONObject jsonObject = new JSONObject();
                jsonObject.put(ER.SESSION_TOKEN, user.getSessionId());
                jsonObject.put("money", price - refundMoney);
                jsonObject.put("accountId", account.getThirdAccoutId());

                ResponseData responseData = memberService.finishMemberFreeze(jsonObject);
                if (responseData.getResult().equals(ER.ERR)) {
                    throw new Exception(responseData.getMsg());
                }
                Account upaAccount = new Account();
                upaAccount.setPrice(price - refundMoney);
                upaAccount.setThirdRefundState(2);
                upaAccount.setRefundPrice(price - refundMoney);
                upaAccount.setAccountId(account.getAccountId());
                upaAccount.setRegistId(regist.getRegistId());
                upaAccount.setPayCodeId("9600");
                upaAccount.setBusinessDay(user.getBusinessDay());
//                upaAccount.setPayCodeId("会员储值卡");
                upaAccount.setRegistPersonId(account.getRegistPersonId());

                accountDao.editAccount(upaAccount);


            }

            // 3.预授权完成


            // 4.微信、支付宝退款
            for (Account account : refundList) {

                Integer price = account.getPrice();

                if (roomDiff == 0) {
                    continue;
                }

                Integer refundMoney = 0;

                if (price >= roomDiff) {
                    refundMoney = roomDiff;
                    roomDiff = 0;
                } else {
                    roomDiff -= price;
                    refundMoney = price;

                }

                JSONObject jsonObject = new JSONObject();
                jsonObject.put(ER.SESSION_TOKEN, user.getSessionId());
                jsonObject.put("refundMoney", refundMoney);
                jsonObject.put("accountId", account.getAccountId());

                ResponseData responseData = accountCanService.refundMoneyForOtherPms(jsonObject);

                if (responseData.getResult().equals(ER.ERR)) {
                    throw new Exception(responseData.getMsg());
                }

                account.setThirdRefundState(1);
                account.setRefundPrice(refundMoney);

            }


            // 进行结账
            CheckoutParam checkoutParam = new CheckoutParam();
            checkoutParam.setType(1);
            checkoutParam.setRegistId(regist.getRegistId());
            checkoutParam.setSessionToken(user.getSessionId());
            ResponseData responseData = pmsOrderService.checkOut(checkoutParam);

            // 结账返回重新认证
            if (responseData.getCode() < 1) {
                throw new Exception(responseData.getMsg());
            }

            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    SmartLockRequest smartLockRequest = new SmartLockRequest();
                    smartLockRequest.setSessionToken(tokenId);
                    smartLockRequest.setRegistId(regist.getRegistId());
                    smartLockRequest.setCheckinTime(regist.getCheckinTime());
                    smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                    smartLockRequest.setRoomTypeId(regist.getRoomTypeId());
                    smartLockRequest.setRoomTypeName(regist.getRoomTypeName());
                    smartLockRequest.setRoomNumId(regist.getRoomNumId());
                    smartLockRequest.setRoomNum(regist.getRoomNum());
                    smartLockRequest.setHid(regist.getHid());
                    smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                    smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                    smartLockRequest.setCheckinType(regist.getCheckinType());
                    smartLockRequest.setBussType(2);
                    List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                    for (int j = 0; j < registPeople.size(); j++) {
                        RegistPerson registPersonInfo = registPeople.get(j);
                        SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                        BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                        peoplesList.add(registPersonDetail);
                    }
                    smartLockRequest.setPeoples(peoplesList);
                    baseService.SmartLockCheckout(smartLockRequest);
                }
            });


        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;

    }

    /**
     * @param user         用户信息
     * @param regist       登记信息
     * @param registPerson 登记人
     * @param money        金额
     * @param payCodeId    付款码小类
     * @param payCode
     * @return
     */
    private Account regAccounts(TbUserSession user, Regist regist, RegistPerson registPerson, Integer money, String payCodeId, String payCode, Integer payClassId, String payClass, Integer payType) {

        Date date = new Date();

        String a = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
        Account account = new Account();
        account.setAccountId(a);
        account.setHid(user.getHid());
        account.setCreateUserId(user.getUserId());
        account.setCreateUserName(user.getUserName());
        account.setCreateTime(new Date());
        account.setIsCancel(0);
        account.setAccountYear(user.getBusinessYear());
        account.setAccountYearMonth(user.getBusinessMonth());
        account.setBusinessDay(user.getBusinessDay());
        account.setClassId(user.getClassId());
        account.setAccountType(1);
        account.setSettleAccountTime(new Date());
        account.setThirdRefundState(0);
        account.setRoomNum(regist.getRoomNum());
        account.setRoomInfoId(regist.getRoomNumId());
        account.setRoomTypeId(regist.getRoomTypeId());
        account.setRegistState(0);
        account.setRegistId(regist.getRegistId());
        account.setBookingId(regist.getBookingOrderId());
        //类型 ：消费、付款
        account.setPayType(payType);
        account.setSaleNum(1);
        account.setPayClassId(payClassId);
        account.setPayClassName(payClass);
        //全天房费
        account.setPayCodeId(payCodeId);
        account.setPayCodeName(payCode);

        //用户信息
        account.setCreateTime(date);
        account.setCreateUserId(user.getUserId());
        account.setCreateUserName(user.getUserName());
        account.setUpdateTime(date);
        account.setUpdateUserId(user.getUserId());
        account.setUpdateUserName(user.getUserName());

        account.setClassId(user.getClassId());
        account.setUpdateCalssId(user.getClassId());

        // 营业日期
        account.setBusinessDay(user.getBusinessDay());


        //设置账务关联人id为0
        account.setRegistPersonId(registPerson.getRegistPersonId());
        account.setPrice(money);
        account.setUintPrice(money);
        return account;
    }

    @Override
    public JSONObject getVipMsg(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "True");

        try {

        } catch (Exception e) {
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject addVipMoney(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject getRegisterIsCheckIn(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "True");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);
            if (!map.containsKey("Value") || map.get("Value").toString().equals("")) {
                throw new Exception("查询值不能空");
            }
            String idCode = map.get("Value").toString();
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistState(0);
            registPersonSearch.setHid(user.getHid());
            registPersonSearch.setIdCode(idCode);
            List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
            if (registPersonList != null && registPersonList.size() > 0) {
                for (int i = 0; i < registPersonList.size(); i++) {
                    RegistPerson registPerson = registPersonList.get(i);
                    Integer registId = registPerson.getRegistId();
                    Regist regist = registDao.selectById(registId);
                    if (regist.getState() == 0) {
                        throw new Exception("证件号为:" + idCode + " 的客人在住中");
                    }
                }
            }
            resultMap.put("Result", "False");
            resultMap.put("Msg", "当前客人未在住");
        } catch (Exception e) {
            resultMap.put("Result", "True");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject vipPay(Map<String, Object> map) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "True");
        try {

        } catch (Exception e) {
            resultMap.put("Result", "True");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject findPriceCodeByIdCode(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);

            JSONObject request = JSONObject.fromObject(map);
            Object type = map.get("Type");
            if (null != type && map.get("Type").toString().equals("2")) {
                String code = map.get("Value").toString();
                CardInfoSearchRequest cardInfoSearchRequest = new CardInfoSearchRequest();
                cardInfoSearchRequest.setHid(user.getHid());
                cardInfoSearchRequest.setSessionToken(tokenId);
                cardInfoSearchRequest.setSta("I");
                cardInfoSearchRequest.setSearch(code);
                Page<CardInfo> member = memberService.findMember(cardInfoSearchRequest);
                if (member != null && member.size() == 1) {
                    resultMap.put("PriceMsg", "");
                    resultMap.put("Code", "");
                    resultMap.put("LeaveTime", member.get(0).getCheckOutTime());
                    resultMap.put("PriceCode", member.get(0).getPriceCodeId());
                    resultMap.put("VipNo", member.get(0).getId());
                    resultMap.put("VipLevel", member.get(0).getCardLevel());
                }
                resultMap.put("Result", "True");
                return resultMap;
            }

            if (!request.containsKey("GuestList") || request.getJSONArray("GuestList") == null || request.getJSONArray("GuestList").size() < 1) {
                throw new Exception("入住人信息不明确");
            }

            JSONArray guestList = request.getJSONArray("GuestList");

            for (int i = 0; i < guestList.size(); i++) {
                JSONObject guestInfo = guestList.getJSONObject(i);
                String code = guestInfo.getString("Code");
                CardInfoSearchRequest cardInfoSearchRequest = new CardInfoSearchRequest();
                cardInfoSearchRequest.setHid(user.getHid());
                cardInfoSearchRequest.setSessionToken(tokenId);
                cardInfoSearchRequest.setSta("I");
                cardInfoSearchRequest.setSearch(code);
                Page<CardInfo> member = memberService.findMember(cardInfoSearchRequest);
                if (member != null && member.size() == 1) {
                    resultMap.put("PriceMsg", "");
                    resultMap.put("Code", "");
                    resultMap.put("LeaveTime", member.get(i).getCheckOutTime());
                    resultMap.put("PriceCode", member.get(i).getPriceCodeId());
                    resultMap.put("VipNo", member.get(i).getId());
                    resultMap.put("VipLevel", member.get(i).getCardLevel());
                    resultMap.put("Money", (member.get(i).getBalance() + member.get(i).getLargessBalance()) / 100.0);

                    //查询酒店的价格信息
                    RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
                    roomDayPriceSearch.setHid(user.getHid());
                    roomDayPriceSearch.setRoomTypeId(Integer.parseInt(map.get("RoomTypeID").toString()));
                    roomDayPriceSearch.setRoomRateId(member.get(i).getPriceCodeId());
                    roomDayPriceSearch.setDayTimeMin(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(map.get("BeginTime").toString())));
                    roomDayPriceSearch.setDayTimeMax(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(map.get("EndTime").toString())));
                    List<RoomDayPrice> roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);
                    log.info(String.valueOf(roomDayPrices.size()));
                    JSONArray dayPriceList = new JSONArray();
                    double sumPrice = 0.0;
                    for (int j = 0; j < roomDayPrices.size(); j++) {
                        RoomDayPrice roomDayPrice = roomDayPrices.get(j);
                        if (roomDayPrice.getRoomTypeId().equals(roomDayPriceSearch.getRoomTypeId())) {
                            log.info(roomDayPrice.getPrice().toString());
                            JSONObject dayPrice = new JSONObject();
                            dayPrice.put("Date", roomDayPrice.getDayTime());
                            dayPrice.put("DayPrice", roomDayPrice.getPrice() / 100.0);
                            sumPrice += (roomDayPrice.getPrice() / 100.0);
                            dayPriceList.add(dayPrice);
                        }
                    }
                    JSONObject PriceMsg = new JSONObject();
                    PriceMsg.put("PriceList", dayPriceList);
                    PriceMsg.put("SumPrice", sumPrice);
                    resultMap.put("PriceMsg", PriceMsg);
                    resultMap.put("Result", "True");
                    break;
                }
            }


        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject getContinueLiveMsg(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);

            if (!map.containsKey("Type") || map.get("Type").toString().equals("")) {
                map.put("Type", 3);
            }
            if (!map.containsKey("Value") || map.get("Value").toString().equals("")) {
                throw new Exception("查询值不能空");
            }

            String type = map.get("Type").toString();

            Regist regist = new Regist();
            /**
             * 通过房间号查询在住信息
             */
            if (type.equals("3")) {
                RegistSearch registSearch = new RegistSearch();
                //只查询在住
                registSearch.setState(0);
                registSearch.setHid(user.getHid());
                registSearch.setRoomNum(map.get("Value").toString());
                List<Regist> regists = registDao.selectBySearch(registSearch);
                if (regists == null || regists.size() != 1) {
                    throw new Exception("未查询到在住信息");
                }
                regist = regists.get(0);
            }
            //通过证件号查询在住信息
            else if (type.equals("2")) {

                RegistPersonSearch registPerson = new RegistPersonSearch();
                registPerson.setHid(user.getHid());
                registPerson.setIdCode(map.get("Value").toString());
                registPerson.setRegistState(0);

                List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPerson);

                if (registPeople == null) {
                    throw new Exception("未查询到在住信息");
                }


                for (RegistPerson rp : registPeople) {
                    regist = registDao.selectById(rp.getRegistId());
                    if (regist == null) {
                        continue;
                    }
                    if (regist.getState() == 0 && regist.getHid().equals(user.getHid())) {
                        break;
                    }
                }
                if (regist == null) {
                    throw new Exception("未查询到在住信息");
                }
            } else if (type.equals("5")) {
                regist = registDao.selectById(Integer.parseInt(map.get("Value").toString()));
                if (regist == null) {
                    throw new Exception("未查询到在住信息");
                }
            }

            /**
             * 查询入住人信息
             */
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(regist.getRegistId());
            if (regist.getState() == 1) {
                registPersonSearch.setRegistState(1);
            }
            registPersonSearch.setRegistState(0);
            List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
            JSONArray guestInfoList = new JSONArray();
            for (int i = 0; i < registPersonList.size(); i++) {
                RegistPerson registPerson = registPersonList.get(i);
                JSONObject guestInfo = new JSONObject();
                guestInfo.put("Name", registPerson.getPersonName());
                guestInfo.put("Code", registPerson.getIdCode());
                guestInfo.put("Nation", registPerson.getNation());
                guestInfo.put("Sex", registPerson.getSex().equals(0) ? "男" : "女");
                guestInfo.put("Address", registPerson.getAddress());
                guestInfo.put("Birthday", registPerson.getBirthday());
                guestInfo.put("Phone", "null".equals(registPerson.getPhone()) ? "" : registPerson.getPhone());
                String guestNo = registPerson.getGuestNo();
                if (guestNo == null || guestNo.equals("") || guestNo.equals("null")) {
                    guestNo = "";
                }
                guestInfo.put("GuestNo", guestNo);
                guestInfoList.add(guestInfo);
            }

            //查询帐务信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setRegistId(regist.getRegistId());
            if (regist.getState() == 1) {
                accountSearch.setRegistState(1);
            }
            accountSearch.setRegistState(0);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            double cashMoney = 0.00;
            double costMoney = 0.00;
            JSONArray cashList = new JSONArray();
            JSONArray costList = new JSONArray();
            for (int i = 0; i < accounts.size(); i++) {
                Account account = accounts.get(i);
                //消费
                if (account.getPayType() == 1 && account.getIsCancel() != 1) {
                    costMoney += Double.parseDouble(account.getPrice().toString()) / 100.0;
                    JSONObject costData = new JSONObject();
                    costData.put("Amount", 1);
                    costData.put("Money", Double.parseDouble(account.getPrice().toString()) / 100.0);
                    costData.put("OccurTime", account.getCreateTime());
                    costData.put("CostCode", account.getPayCodeName());
                    costList.add(costData);
                }
                //付款
                else if (account.getPayType() == 2 && account.getIsCancel() != 1) {
                    cashMoney += Double.parseDouble(account.getPrice().toString()) / 100.0;
                    JSONObject cashData = new JSONObject();
                    cashData.put("Amount", 1);
                    cashData.put("Money", Double.parseDouble(account.getPrice().toString()) / 100.0);
                    cashData.put("Type", "1");
                    if (account.getPayCodeId().equals("9100") && account.getThirdRefundState() == 0) {
                        cashData.put("Type", "2");
                    }
                    cashData.put("OccurTime", account.getCreateTime());
                    cashData.put("CostCode", account.getPayCodeName());
                    cashList.add(cashData);
                }
            }
            //查询续住可用房间  --默认查询7天
            String startTime = HotelUtils.stampToDate(regist.getCheckoutTime().getTime());
            String endTime = HotelUtils.stampToDate(HotelUtils.addDayGetNewDate(regist.getCheckoutTime(), 3).getTime());
            //查询酒店的价格信息
            RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
            roomDayPriceSearch.setHid(user.getHid());
            roomDayPriceSearch.setRoomRateId(regist.getRoomRateCodeId());
            roomDayPriceSearch.setRoomTypeId(regist.getRoomTypeId());
            roomDayPriceSearch.setDayTimeMin(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(startTime.toString())));
            roomDayPriceSearch.setDayTimeMax(HotelUtils.parseDate2Int(HotelUtils.parseStr2Date(endTime.toString())));
            List<RoomDayPrice> roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);
            if (null == roomDayPrices || roomDayPrices.size() < 1) {
                throw new Exception("房价暂未设置无法续住");
            }

            Map<String, List<RoomDayPrice>> roomTypePriceMap = new HashMap<>();
            for (RoomDayPrice roomDayPrice : roomDayPrices) {
                Integer roomTypeId = regist.getRoomTypeId();
                if (roomTypePriceMap.containsKey(roomTypeId.toString())) {
                    roomTypePriceMap.get(roomTypeId.toString()).add(roomDayPrice);
                } else {
                    List<RoomDayPrice> roomDayPriceList = new ArrayList<>();
                    roomDayPriceList.add(roomDayPrice);
                    roomTypePriceMap.put(roomTypeId.toString(), roomDayPriceList);
                }
            }
            Map<Integer, RoomDayPrice> dayPriceMap = roomDayPrices.stream().collect(Collectors.toMap(RoomDayPrice::getRoomTypeId, a -> a, (k1, k2) -> k2));
            // 3.获取两个日期差
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(HotelUtils.parseStr2Date(startTime.toString())).substring(0, 10), HotelUtils.parseDate2Str(HotelUtils.parseStr2Date(endTime.toString())).substring(0, 10));
            //查询酒店房型的可售信息
            AvailableRoom availableRoom = new AvailableRoom();
            availableRoom.setStartTime(startTime);
            availableRoom.setEndTime(endTime);
            availableRoom.setType(2);
            availableRoom.setSessionToken(tokenId);
            Map<String, Object> canUserRoomType = this.findavailableRoom(availableRoom);
            JSONObject canUseRoomMap = JSONObject.fromObject(canUserRoomType.get("canUseRoomMap"));
            JSONObject canUseRoomNum = JSONObject.fromObject(canUserRoomType.get("canUseRoomNum"));
            JSONObject trrb = JSONObject.fromObject(canUserRoomType.get("trrb"));
            if (!canUserRoomType.get("Result").toString().equals("Success")) {
                throw new Exception("查询酒店房型信息失败");
            }
//            int roomNum = canUseRoomMap.getJSONArray(regist.getRoomTypeId().toString()).size() - trrb.getInt(regist.getRoomTypeId().toString());
            int roomNum = canUseRoomNum.getInt(regist.getRoomTypeId().toString());
            JSONObject priceMsg = new JSONObject();
            JSONArray priceList = new JSONArray();
            JSONArray ContinuedInfoList = new JSONArray();
            JSONObject ContinuedInfo = new JSONObject();
            for (String date : allDayListBetweenDate) {
                if (roomTypePriceMap.containsKey(regist.getRoomTypeId().toString())) {
                    List<RoomDayPrice> roomDayPriceList = roomTypePriceMap.get(regist.getRoomTypeId().toString());
                    for (int j = 0; j < roomDayPriceList.size(); j++) {
                        if (date.replace("-", "").equals(roomDayPriceList.get(j).getDayTime().toString())) {
                            JSONObject priceInfo = new JSONObject();
                            priceInfo.put("Date", date);
                            priceInfo.put("DayPrice", roomDayPriceList.get(j).getPrice() / 100.0);
                            priceList.add(priceInfo);

                            ContinuedInfo.put("Date", date);
                            ContinuedInfo.put("IsContinued", roomNum > 0);
                            ContinuedInfoList.add(ContinuedInfo);
                            continue;
                        }
                    }
                }
                //每日房价中不包含房型房价信息
                else {
                    JSONObject priceInfo = new JSONObject();
                    priceInfo.put("Date", date);
                    priceInfo.put("DayPrice", 0);
                    priceList.add(priceInfo);

                    ContinuedInfo.put("Date", date);
                    ContinuedInfo.put("IsContinued", false);
                    ContinuedInfoList.add(ContinuedInfo);
                }
            }

            resultMap.put("PriceCode", regist.getRoomRateCodeId());
            resultMap.put("RoomType", regist.getRoomTypeName());
            resultMap.put("RegistID", regist.getRegistId());
            resultMap.put("MainID", regist.getRegistId());
            resultMap.put("BeginTime", HotelUtils.stampToDate(regist.getCheckinTime().getTime()));
            resultMap.put("EndTime", HotelUtils.stampToDate(regist.getCheckoutTime().getTime()));
            resultMap.put("CheckInType", regist.getCheckinType());
            resultMap.put("isVip", regist.getMemberId() == null ? false : true);
            resultMap.put("RoomNo", regist.getRoomNum());
            resultMap.put("RoomTypeID", regist.getRoomTypeId());
            resultMap.put("CanCheckOut", true);
            resultMap.put("BookingType", "");
            resultMap.put("GuestList", guestInfoList);
            resultMap.put("GuestName", registPersonList.size() > 0 ? registPersonList.get(0).getPersonName() : "");
            resultMap.put("IDType", "身份证");
            resultMap.put("IDCode", registPersonList.size() > 0 ? registPersonList.get(0).getIdCode() : "");
            resultMap.put("Gender", registPersonList.size() > 0 ? registPersonList.get(0).getSex().equals(0) ? "男" : "女" : "");
            resultMap.put("PayType", "");
            resultMap.put("PayMoney", cashMoney);
            resultMap.put("CostMoney", costMoney);
            resultMap.put("CostList", costList);
            resultMap.put("CashList", cashList);
            resultMap.put("FeeAdd", "0");
            JSONObject addCostMsg = new JSONObject();
            addCostMsg.put("IsNeed", false);
            addCostMsg.put("AddMoney", 0);
            resultMap.put("AddCostMsg", addCostMsg);
            resultMap.put("ContinuedInfo", ContinuedInfoList);
            priceMsg.put("PriceList", priceList);
            resultMap.put("PriceMsg", priceMsg);
            resultMap.put("Result", "True");
            resultMap.put("Msg", "查询续住信息成功");
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject verifyVCRoom(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);
            if (!map.containsKey("Value") || map.get("Value").toString().equals("")) {
                throw new Exception("查询值不能空");
            }
            String roomNo = map.get("Value").toString();
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            roomInfoSearch.setRoomNum(roomNo);
            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
            if (roomInfos == null || roomInfos.size() != 1) {
                throw new Exception("房间信息异常");
            }
            RoomInfo roomInfo = roomInfos.get(0);
            if (roomInfo.getRoomNumState() != 1) {
                throw new Exception("当前房间不是净房");
            }
            resultMap.put("Result", "True");
            resultMap.put("Msg", "净房");

        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        log.info(resultMap.toString());
        return resultMap;
    }

    @Override
    public JSONObject verifyGuestLiving(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "True");
        try {
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);
            if (!map.containsKey("Value") || map.get("Value").toString().equals("")) {
                throw new Exception("查询值不能空");
            }
            String idCode = map.get("Value").toString();
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistState(0);
            registPersonSearch.setHid(user.getHid());
            registPersonSearch.setIdCode(idCode);
            List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
            if (registPersonList != null && registPersonList.size() > 0) {
                throw new Exception("证件号为:" + idCode + " 的客人在住中");
            }
            resultMap.put("Result", "False");
            resultMap.put("Msg", "当前客人未在住");
        } catch (Exception e) {
            resultMap.put("Result", "True");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject login(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject getSessionToken(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject getPhoneNumber(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject valPhoneNumber(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject createMember(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject getMemberInfo(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject getMemberCardType(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject memberPay(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject getMemberPayResult(Map<String, Object> map) {
        return null;
    }

    @Override
    public JSONObject findRegistGourpRecord(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("sessionToken").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);
            if (!map.containsKey("orderId") || map.get("orderId").toString().equals("")) {
                throw new Exception("订单号不能空");
            }
            RegistTeamSearch registTeamSearch = new RegistTeamSearch();
            registTeamSearch.setSn(map.get("orderId").toString());
            Page<RegistTeam> registTeams = registTeamDao.selectBySearch(registTeamSearch);
            if (registTeams == null || registTeams.size() != 1) {
                throw new Exception("未查询到当前团队订单之前的支付信息");
            }
            resultMap.put("Result", "True");
            resultMap.put("Data", registTeams.get(0));
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject addRegistGroupRecord(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = map.get("sessionToken").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);
            if (!map.containsKey("orderId") || map.get("orderId").toString().equals("")) {
                throw new Exception("订单号不能空");
            }
            if (!map.containsKey("payType") || map.get("payType").toString().equals("")) {
                throw new Exception("支付类型不能空");
            }
            if (!map.containsKey("payMoney") || map.get("payMoney").toString().equals("")) {
                throw new Exception("支付金额不能空");
            }

            RegistTeam registTeam = new RegistTeam();
            registTeam.setSn(map.get("orderId").toString());
            registTeam.setPayType(Integer.parseInt(map.get("payType").toString()));
            registTeam.setSumPay(Integer.parseInt(map.get("payMoney").toString()));
            registTeam.setHid(user.getHid());
            registTeam.setCreateUserId(user.getUserId());
            registTeam.setCreateUserName(user.getUserName());
            registTeam.setCreateTime(new Date());
            registTeam.setHotelGroupId(user.getHotelGroupId());
            registTeam.setTeamType(Integer.parseInt(map.get("teamType").toString()));

            Integer insert = registTeamDao.insert(registTeam);

            if (insert < 1) {
                throw new Exception("添加支付信息失败");
            }
            resultMap.put("Result", "True");
            resultMap.put("Msg", "添加团队支付信息成功");

        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject updateGuestInfo(Map<String, Object> map) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            JSONObject request = JSONObject.fromObject(map);
            String tokenId = map.get("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);
            if (map.get("RegistID") == null || map.get("RegistID").toString().equals("") || map.get("RegistID").toString().equals("null")) {
                throw new Exception("登记单账号不能空");
            }
            int registID = Integer.parseInt(map.get("RegistID").toString());
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(registID);
            List<RegistPerson> registPeoples = registPersonDao.selectBySearch(registPersonSearch);
            if (registPeoples == null || registPeoples.size() < 1) {
                throw new Exception("未查询到宾客信息");
            }
            if (!request.containsKey("GuestList") || request.getJSONArray("GuestList") == null || request.getJSONArray("GuestList").size() < 1) {
                throw new Exception("入住人信息不明确");
            }
            JSONArray guestList = request.getJSONArray("GuestList");
            ArrayList<RegistPersonHealthCode> registPersonHealthCodeList = new ArrayList<>();
            for (int i = 0; i < guestList.size(); i++) {
                JSONObject guestInfo = guestList.getJSONObject(i);
                for (int j = 0; j < registPeoples.size(); j++) {
                    RegistPerson registPerson = registPeoples.get(j);
                    String code = guestInfo.getString("Code");
                    if (code.equals(registPerson.getIdCode())) {
                        RegistPersonHealthCode registPersonHealthCode = new RegistPersonHealthCode();
                        registPersonHealthCode.setRegistPersonId(registPerson.getRegistPersonId());
                        Integer healthCode = 1;
                        String healthCodeStr = "绿码";
                        if (guestInfo.containsKey("HealthCode") && !guestInfo.getString("HealthCode").equals("") && !guestInfo.getString("HealthCode").equals("null")) {
                            String healthCode1 = guestInfo.getString("HealthCode");

                            if (healthCode1.equals("00")) {
                                healthCode = 1;
                                healthCodeStr = "绿码";
                            } else if (healthCode1.equals("01")) {
                                healthCode = 2;
                                healthCodeStr = "黄码";
                            } else if (healthCode1.equals("02")) {
                                healthCode = 3;
                                healthCodeStr = "红码";
                            }
                        }
                        registPersonHealthCode.setCode(healthCode);
                        registPersonHealthCode.setCodeStr(healthCodeStr);
                        registPersonHealthCode.setIdCode(registPerson.getIdCode());
                        registPersonHealthCode.setPersonName(registPerson.getPersonName());
                        registPersonHealthCode.setHid(user.getHid());
                        registPersonHealthCode.setTemperature("36.5");
                        if (guestInfo.containsKey("GoAddress") && !guestInfo.getString("GoAddress").equals("") && !guestInfo.getString("GoAddress").equals("null")) {
                            registPersonHealthCode.setGoAddress(guestInfo.getString("GoAddress"));
                        }
                        if (guestInfo.containsKey("Temperature") && !guestInfo.getString("Temperature").equals("") && !guestInfo.getString("HealthCode").equals("null")) {
                            registPersonHealthCode.setGoAddress(guestInfo.getString("Temperature"));
                        }
                        if (guestInfo.containsKey("Nuclein") && !guestInfo.getString("Nuclein").equals("") && !guestInfo.getString("Nuclein").equals("null")) {
                            registPersonHealthCode.setGoAddress(guestInfo.getString("Nuclein"));
                        }
                        if (guestInfo.containsKey("Vaccinum") && !guestInfo.getString("Vaccinum").equals("") && !guestInfo.getString("Vaccinum").equals("null")) {
                            registPersonHealthCode.setGoAddress(guestInfo.getString("Vaccinum"));
                        }
                        registPersonHealthCodeList.add(registPersonHealthCode);
                        continue;
                    }
                }
            }
            for (int i = 0; i < registPersonHealthCodeList.size(); i++) {
                RegistPersonHealthCode registPersonHealthCode = registPersonHealthCodeList.get(i);
                Integer insert = registPersonHealthCodeDao.insert(registPersonHealthCode);
                if (insert < 1) {
                    throw new Exception("插入健康码数据失败");
                }
            }
            resultMap.put("Result", "True");
            resultMap.put("Msg", "更新宾客健康码信息成功");
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject noCardCheckin(JSONObject postData) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            if (!postData.containsKey("uuid") || postData.getString("uuid").equals("")) {
                throw new Exception("uuid不能空");
            }
            if (!postData.containsKey("name") || postData.getString("name").equals("")) {
                throw new Exception("name不能空");
            }
            if (!postData.containsKey("code") || postData.getString("code").equals("")) {
                throw new Exception("name不能空");
            }
            resultMap.put("Result", "True");
            resultMap.put("Msg", "发起成功");
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }

    @Override
    public JSONObject getMemberInfo(JSONObject postData) {
        JSONObject resultMap = new JSONObject();
        resultMap.put("Result", "False");
        try {
            String tokenId = postData.getString("appKey").toString();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);
            if (!postData.containsKey("value") || postData.getString("value").equals("")) {
                throw new Exception("查询值不能空");
            }

            resultMap.put("Result", "True");
            resultMap.put("Msg", "发起成功");
        } catch (Exception e) {
            resultMap.put("Result", "False");
            resultMap.put("Msg", e.getMessage());
            log.error("",e);
        }
        return resultMap;
    }
}
