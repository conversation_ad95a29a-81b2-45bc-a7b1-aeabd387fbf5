package com.pms.czabsnight.service.transaction;



import com.pms.czabsnight.bean.*;
import com.pms.czaccount.bean.account.Account;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomRepairRecordHistory;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.*;

import java.util.ArrayList;
import java.util.List;

public interface NightTransactionService {

    public void nightKeepRoomStatusTransaction(List<NightAuditRoomStatus> deleteNightAuditRoomStatuses, ArrayList<NightAuditRoomStatus> addNightRoomStatus
    ,List<RoomInfo> updateRoomifos,List<RoomRepairRecordHistory> roomRepairRecordHistories
    ) throws Exception ;

    // noshow 预订单批量取消
    public void cancelOrderService(List<BookingOrder> bookingOrders, TbUserSession user,List<RoomAuxiliaryRelation> roomAuxiliaryRelations,List<BookingOrderRoomNum> updBookRooms,List<BookingOrderRoomType> updRoomType)  throws Exception;


    public void nightAddAcountHandleTransaction(List<Account> accounts, List<BookingOrderDailyPrice> bookingOrderDailyPrices, TbUserSession user, ArrayList<Regist> regists,List<BookingOrderDailyPrice> addBookingOrderDailyPrices) throws Exception;

    // 添加房型数据汇总
    public void nightAddNightRoomTypeHandleTransaction(List<NightAuditRoomType> nightAuditRoomTypeList) throws Exception;

    public void nightAddNightPayHandleTransaction(List<NightAuditPay> nightAuditPays)  throws Exception;

    public void nightOnAccountTransaction(ArrayList<Regist> upaRegists,ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations, ArrayList<RegistPerson> registPeople) throws Exception;

    // 日资金流水统计
    public void nightCapitalFlow( ArrayList<NightAuditPay> nightAuditPays, ArrayList<NightAuditAutopre> nightAuditAutopres )  throws Exception;


    // 统计房型数据
    public void nightAuditRoomType(List<NightAuditRoomType> nightAuditRoomTypes)  throws Exception;

    // 统计客源数据
    public void nightAuditResource(List<NightAuditResource> nightAuditResources)  throws Exception;


    // 统计会员数据
    public void nightAuditMember(List<NightAuditMember> nightAuditMembers)  throws Exception;

    // 统计客源类型
    public void nightOtaData(List<NightAuditOta> nightAuditOtas)  throws Exception;
}
