package com.pms.czabsnight.service;

import com.pms.czabsnight.bean.search.NightAuditBalanceSearch;
import com.pms.czabsnight.bean.search.NightAuditRoomTypeSearch;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.RoomAccountDetails;
import net.sf.json.JSONObject;

public interface NightReportService {

    public ResponseData searchDailyReport(JSONObject param);

    public ResponseData searchDailyReportTwo(JSONObject param);

    public ResponseData searchDailyResourceReport(JSONObject param);

    public ResponseData searchDailyRoomType(JSONObject param);

    public ResponseData roomAccountDetails(RoomAccountDetails roomAccountDetails);

    public ResponseData ssphr(NightAuditBalanceSearch nightAuditBalanceSearch);

    // 推送方法图更新
    public void  pushRoomState(TbUserSession userSession);

    public ResponseData searchNightRoomType(NightAuditRoomTypeSearch nightAuditRoomTypeSearch);
}
