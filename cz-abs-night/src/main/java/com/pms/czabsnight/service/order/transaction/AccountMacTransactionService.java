package com.pms.czabsnight.service.order.transaction;

import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;

public interface AccountMacTransactionService {
    public void autoAddRoomPrice(BookingOrderDailyPrice bookingOrderDailyPrice, Regist regist, <PERSON>t<PERSON><PERSON> registPerson, TbUserSession user) throws Exception;
}
