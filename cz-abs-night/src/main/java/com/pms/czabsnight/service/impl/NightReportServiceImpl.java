package com.pms.czabsnight.service.impl;

import com.github.pagehelper.Page;
import com.pms.czabsnight.bean.*;
import com.pms.czabsnight.bean.search.*;
import com.pms.czabsnight.dao.*;
import com.pms.czabsnight.service.NightReportService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountBalance;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.code.HotelCostCode;
import com.pms.czhotelfoundation.bean.code.search.HotelCostCodeSearch;
import com.pms.czhotelfoundation.dao.code.HotelCostCodeDao;
import com.pms.czmembership.bean.member.CardRecharge;
import com.pms.czmembership.bean.member.search.CardRechargeSearch;
import com.pms.czmembership.dao.member.CardRechargeDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.AccountSummary;
import com.pms.czpmsutils.request.RoomAccountDetails;
import com.pms.czpmsutils.request.search.AccountSummarySearch;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.search.BookingOrderSearch;
import com.pms.pmsorder.bean.search.RegistPersonSearch;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.dao.BookingOrderDao;
import com.pms.pmsorder.dao.RegistDao;
import com.pms.pmsorder.dao.RegistPersonDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Primary
public class NightReportServiceImpl extends BaseService implements NightReportService {


    private static final Logger log = LogManager.getLogger(NightReportServiceImpl.class);
    @Autowired
    private NightAuditRoomTypeDao nightAuditRoomTypeDao;

    @Autowired
    private NightAuditPayDao nightAuditPayDao;

    @Autowired
    private NightAuditAutopreDao nightAuditAutopreDao;

    @Autowired
    private NightAuditResourceDao nightAuditResourceDao;

    @Autowired
    private NightAuditMemberDao nightAuditMemberDao;

    @Autowired
    private NightAuditDailyRegistRecordDao nightAuditDailyRegistRecordDao;

    @Autowired
    private NightAuditRoomStatusDao nightAuditRoomStatusDao;

    @Autowired
    private NightAuditOtaDao nightAuditOtaDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private NightAuditBalanceDao nightAuditBalanceDao;

    @Autowired
    private NigthAuditArPaymentDao nigthAuditArPaymentDao;

    @Autowired
    private CardRechargeDao cardRechargeDao;

    @Autowired
    private HotelCostCodeDao hotelCostCodeDao;

    @Autowired
    private NightAuditMemberBalanceDao nightAuditMemberBalanceDao;


    /**
     * 查询营业日报表
     * @param param
     * @return
     */
    @Override
    public ResponseData searchDailyReport(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            // 获取营业日
            int businessDay = param.getInt("businessDay");
            Object endBusinessDay = param.get("endBusinessDay");
            // 开始营业日不为空，则查询多天
            if(null!=endBusinessDay  ){
                return  this.searchDailyReportMoreDay(user,businessDay,Integer.parseInt(endBusinessDay.toString()));
            }

            // 房型数据
            NightAuditRoomTypeSearch nightAuditRoomTypeSearch = new NightAuditRoomTypeSearch();
            nightAuditRoomTypeSearch.setHid(user.getHid());
            nightAuditRoomTypeSearch.setBusinessDay(businessDay);
            List<NightAuditRoomType> nightAuditRoomTypes = nightAuditRoomTypeDao.selectBySearch(nightAuditRoomTypeSearch);

            HashMap<String,Object> resData = new HashMap<String,Object>();
            resData.put("nightRoomType",nightAuditRoomTypes);

            // 支付数据
            NightAuditPaySearch nightAuditPaySearch = new NightAuditPaySearch();
            nightAuditPaySearch.setHid(user.getHid());
            nightAuditPaySearch.setBusinessDay(businessDay);
            nightAuditPaySearch.setType(1);
            List<NightAuditPay> nightAuditPays = nightAuditPayDao.selectBySearch(nightAuditPaySearch);

            Map<String, NightAuditPay> collect = nightAuditPays.stream().collect(Collectors.toMap(NightAuditPay::getPayClassId, a -> a, (k1, k2) -> k1));

            resData.put("nightAuditPays",collect);

            // 房费数据
            NightAuditDailyRegistRecordSearch nightAuditDailyRegistRecordSearch = new NightAuditDailyRegistRecordSearch();
            nightAuditDailyRegistRecordSearch.setHid(user.getHid());
            nightAuditDailyRegistRecordSearch.setBusinessDay(businessDay);
            List<NightAuditDailyRegistRecord> nightAuditDailyRegistRecords = nightAuditDailyRegistRecordDao.selectBySearch(nightAuditDailyRegistRecordSearch);

            resData.put("nightAuditDailyRegistRecords",nightAuditDailyRegistRecords);

            String bstr = businessDay+"";

            int month =  Integer.parseInt(bstr.substring(4,6));

            // 每年的第一个营业日期
            int starDay = Integer.parseInt(bstr.substring(0,4)+ "0101");
            nightAuditDailyRegistRecordSearch = new NightAuditDailyRegistRecordSearch();
            nightAuditDailyRegistRecordSearch.setHid(user.getHid());
            nightAuditDailyRegistRecordSearch.setAuditYearMonth(month);
            nightAuditDailyRegistRecordSearch.setMaxBusinessDay(businessDay);
            nightAuditDailyRegistRecordSearch.setMinBusinessDay(starDay);
            List<NightAuditDailyRegistRecord> nightAuditDailyRegistRecordsMonth = nightAuditDailyRegistRecordDao.selectBySearchSummary(nightAuditDailyRegistRecordSearch);

            Map<String, NightAuditDailyRegistRecord> collect1 = nightAuditDailyRegistRecordsMonth.stream().collect(Collectors.toMap(NightAuditDailyRegistRecord::getPayCodeId, a -> a, (k1, k2) -> k1));

            int sumMonth = nightAuditDailyRegistRecordsMonth.stream().mapToInt(NightAuditDailyRegistRecord::getTotalAmount).sum();

            resData.put("nightAuditDailyRegistRecordsMonth",collect1);
            resData.put("nightAuditDailyRegistRecordsMonthSum",sumMonth);


            nightAuditDailyRegistRecordSearch = new NightAuditDailyRegistRecordSearch();
            nightAuditDailyRegistRecordSearch.setHid(user.getHid());

            int year = Integer.parseInt(bstr.substring(0, 4));
            nightAuditDailyRegistRecordSearch.setAuditYear(year);
            nightAuditDailyRegistRecordSearch.setMaxBusinessDay(businessDay);
            nightAuditDailyRegistRecordSearch.setMinBusinessDay(starDay);
            List<NightAuditDailyRegistRecord> nightAuditDailyRegistRecordsYear = nightAuditDailyRegistRecordDao.selectBySearchSummary(nightAuditDailyRegistRecordSearch);

            Map<String, NightAuditDailyRegistRecord> collect2 = nightAuditDailyRegistRecordsYear.stream().collect(Collectors.toMap(NightAuditDailyRegistRecord::getPayCodeId, a -> a, (k1, k2) -> k1));

            int sumYear = nightAuditDailyRegistRecordsYear.stream().mapToInt(NightAuditDailyRegistRecord::getTotalAmount).sum();

            resData.put("nightAuditDailyRegistRecordsYear",collect2);
            resData.put("nightAuditDailyRegistRecordsYearSum",sumYear);


            // 客源数据
            NightAuditResourceSearch nightAuditResourceSearch = new NightAuditResourceSearch();
            nightAuditResourceSearch.setHid(user.getHid());
            nightAuditResourceSearch.setBusinessDay(businessDay);
            List<NightAuditResource> nightAuditResources = nightAuditResourceDao.selectBySearch(nightAuditResourceSearch);
            resData.put("nightAuditResources",nightAuditResources);

            // 夜审会员统计
            NightAuditMemberSearch nightAuditMemberSearch = new NightAuditMemberSearch();
            nightAuditMemberSearch.setBusinessDay(businessDay);
            nightAuditMemberSearch.setHid(user.getHid());

            List<NightAuditMember> nightAuditMembers = nightAuditMemberDao.selectBySearch(nightAuditMemberSearch);
            resData.put("nightAuditMembers",nightAuditMembers);

            // 夜审预授权统计
            NightAuditAutopreSearch nightAuditAutopreSearch = new NightAuditAutopreSearch();
            nightAuditAutopreSearch.setBusinessDay(businessDay);
            nightAuditAutopreSearch.setHid(user.getHid());
            List<NightAuditAutopre> nightAuditAutopres = nightAuditAutopreDao.selectBySearch(nightAuditAutopreSearch);
            resData.put("nightAuditAutopres",nightAuditAutopres);

            // 夜审开房数统计
            NightAuditRoomStatusSearch nightAuditRoomStatusSearch = new NightAuditRoomStatusSearch();
            nightAuditRoomStatusSearch.setBusinessDay(businessDay);
            nightAuditRoomStatusSearch.setHid(user.getHid());
            nightAuditRoomStatusSearch.setRoomState(1);
            List<NightAuditRoomStatus> nightAuditRoomStatuses = nightAuditRoomStatusDao.selectBySearch(nightAuditRoomStatusSearch);
            resData.put("nightAuditRoomStatuses",nightAuditRoomStatuses);

            NightAuditMemberBalanceSearch nightAuditMemberBalanceSearch = new NightAuditMemberBalanceSearch();
            nightAuditMemberBalanceSearch.setBusinessDay(businessDay);
            nightAuditMemberBalanceSearch.setHid(user.getHid());
            Page<NightAuditMemberBalance> nightAuditMemberBalances = nightAuditMemberBalanceDao.selectBySearch(nightAuditMemberBalanceSearch);

            resData.put("nightAuditMemberBalances",nightAuditMemberBalances);

            responseData.setData(resData);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    public ResponseData searchDailyReportMoreDay(TbUserSession user,Integer startBusinessDay,Integer businessDay) throws Exception{
        ResponseData responseData = new ResponseData(ER.SUCC);
        // 房型数据
        NightAuditRoomTypeSearch nightAuditRoomTypeSearch = new NightAuditRoomTypeSearch();
        nightAuditRoomTypeSearch.setHid(user.getHid());
        nightAuditRoomTypeSearch.setBusinessDayMax(businessDay);
        nightAuditRoomTypeSearch.setBusinessDayMin(startBusinessDay);
        nightAuditRoomTypeSearch.setGroupType(1);
        List<NightAuditRoomType> nightAuditRoomTypes = nightAuditRoomTypeDao.selectBySearchSummary(nightAuditRoomTypeSearch);

        HashMap<String,Object> resData = new HashMap<String,Object>();
        resData.put("nightRoomType",nightAuditRoomTypes);

        // 支付数据
        NightAuditPaySearch nightAuditPaySearch = new NightAuditPaySearch();
        nightAuditPaySearch.setHid(user.getHid());
        nightAuditPaySearch.setBusinessDay(businessDay);
        nightAuditPaySearch.setType(1);
        nightAuditPaySearch.setBusinessDayMax(businessDay);
        nightAuditPaySearch.setBusinessDayMin(startBusinessDay);
        List<NightAuditPay> nightAuditPays = nightAuditPayDao.selectBySearchSummary(nightAuditPaySearch);

        Map<String, NightAuditPay> collect = nightAuditPays.stream().collect(Collectors.toMap(NightAuditPay::getPayClassId, a -> a, (k1, k2) -> k1));

        resData.put("nightAuditPays",collect);

        // 房费数据
        NightAuditDailyRegistRecordSearch nightAuditDailyRegistRecordSearch = new NightAuditDailyRegistRecordSearch();
        nightAuditDailyRegistRecordSearch.setHid(user.getHid());
        nightAuditDailyRegistRecordSearch.setMaxBusinessDay(businessDay);
        nightAuditDailyRegistRecordSearch.setMinBusinessDay(startBusinessDay);
        List<NightAuditDailyRegistRecord> nightAuditDailyRegistRecords = nightAuditDailyRegistRecordDao.selectBySearchSummary(nightAuditDailyRegistRecordSearch);

        resData.put("nightAuditDailyRegistRecords",nightAuditDailyRegistRecords);

        // 客源数据
        NightAuditResourceSearch nightAuditResourceSearch = new NightAuditResourceSearch();
        nightAuditResourceSearch.setHid(user.getHid());
        nightAuditResourceSearch.setBusinessDayMax(businessDay);
        nightAuditResourceSearch.setBusinessDayMin(startBusinessDay);
        List<NightAuditResource> nightAuditResources = nightAuditResourceDao.selectBySearchGroup(nightAuditResourceSearch);
        resData.put("nightAuditResources",nightAuditResources);

        // 夜审会员统计
        NightAuditMemberSearch nightAuditMemberSearch = new NightAuditMemberSearch();
        nightAuditMemberSearch.setHid(user.getHid());
        nightAuditMemberSearch.setBusinessDayMin(startBusinessDay);
        nightAuditMemberSearch.setBusinessDayMax(businessDay);

        List<NightAuditMember> nightAuditMembers = nightAuditMemberDao.selectBySearchSummary(nightAuditMemberSearch);
        resData.put("nightAuditMembers",nightAuditMembers);

        // 夜审预授权统计
        NightAuditAutopreSearch nightAuditAutopreSearch = new NightAuditAutopreSearch();
        nightAuditAutopreSearch.setBusinessDay(businessDay);
        nightAuditAutopreSearch.setHid(user.getHid());
        List<NightAuditAutopre> nightAuditAutopres = nightAuditAutopreDao.selectBySearch(nightAuditAutopreSearch);
        resData.put("nightAuditAutopres",nightAuditAutopres);

        // 夜审开房数统计
        NightAuditRoomStatusSearch nightAuditRoomStatusSearch = new NightAuditRoomStatusSearch();
        nightAuditRoomStatusSearch.setHid(user.getHid());
        nightAuditRoomStatusSearch.setRoomState(1);
        nightAuditRoomStatusSearch.setMaxBusinessDay(businessDay);
        nightAuditRoomStatusSearch.setMinBusinessDay(startBusinessDay);
        List<NightAuditRoomStatus> nightAuditRoomStatuses = nightAuditRoomStatusDao.selectBySearch(nightAuditRoomStatusSearch);
        resData.put("nightAuditRoomStatuses",nightAuditRoomStatuses);

        NightAuditMemberBalanceSearch nightAuditMemberBalanceSearch = new NightAuditMemberBalanceSearch();
        nightAuditMemberBalanceSearch.setBusinessDay(businessDay);
        nightAuditMemberBalanceSearch.setHid(user.getHid());
        Page<NightAuditMemberBalance> nightAuditMemberBalances = nightAuditMemberBalanceDao.selectBySearch(nightAuditMemberBalanceSearch);

        resData.put("nightAuditMemberBalances",nightAuditMemberBalances);

        responseData.setData(resData);
        return responseData;
    }

    /**
     * 第二种格式的营业日报表
     * @param param
     * @return
     */
    @Override
    public ResponseData searchDailyReportTwo(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            // 获取营业日
            int businessDay = param.getInt("businessDay");

            String bstr = businessDay+"";
            String yearStr = bstr.substring(0, 4);
            String dayStr = bstr.substring(4, 8);
            // 每年的第一个营业日期
            int starDay = Integer.parseInt(yearStr+ "0101");

            // 每月的第一个营业日期
            int starMonthDay = Integer.parseInt(bstr.substring(0,6)+ "01");

            // 查询当日
            AccountSummarySearch accountSummarySearch = new AccountSummarySearch();
            accountSummarySearch.setHid(user.getHid());
            accountSummarySearch.setPayType(1);
            accountSummarySearch.setGroupType(1);
            accountSummarySearch.setBusinessDay(businessDay);

            // 当日
            List<AccountSummary> accountSummaries = accountDao.accountSummary(accountSummarySearch);
            if(accountSummaries.size()<1){
                throw new Exception("未查询到夜审记录");
            }

            // 查询会员储值
            // 查询会员储值
            CardRechargeSearch nightAuditMemberSearch = new CardRechargeSearch();
            nightAuditMemberSearch.setSta("I");
            nightAuditMemberSearch.setHid(user.getHid());
            nightAuditMemberSearch.setBusinessDay(businessDay);
            List<CardRecharge> nightAuditMembers = cardRechargeDao.selectBySearchTypeSummary(nightAuditMemberSearch);

            accountSummarySearch = new AccountSummarySearch();
            accountSummarySearch.setHid(user.getHid());
            accountSummarySearch.setPayType(1);
            accountSummarySearch.setGroupType(1);
            accountSummarySearch.setBusinessDayMin(starMonthDay);
            accountSummarySearch.setBusinessDayMax(businessDay);

            // 当月
            List<AccountSummary> accountSummariesMonth = accountDao.accountSummary(accountSummarySearch);
            // 查询会员储值
            nightAuditMemberSearch.setBusinessDay(null);
            nightAuditMemberSearch = new CardRechargeSearch();
            nightAuditMemberSearch.setSta("I");
            nightAuditMemberSearch.setHid(user.getHid());
            nightAuditMemberSearch.setBusinessDayMin(starMonthDay);
            nightAuditMemberSearch.setBusinessDayMax(businessDay);
            List<CardRecharge> nightAuditMembersMonth = cardRechargeDao.selectBySearchTypeSummary(nightAuditMemberSearch);

            accountSummarySearch = new AccountSummarySearch();
            accountSummarySearch.setHid(user.getHid());
            accountSummarySearch.setPayType(1);
            accountSummarySearch.setGroupType(1);
            accountSummarySearch.setBusinessDayMin(starDay);
            accountSummarySearch.setBusinessDayMax(businessDay);


            // 当年
            List<AccountSummary> accountSummariesYear = accountDao.accountSummary(accountSummarySearch);
            // 查询会员储值
            nightAuditMemberSearch = new CardRechargeSearch();
            nightAuditMemberSearch.setSta("I");
            nightAuditMemberSearch.setHid(user.getHid());
            nightAuditMemberSearch.setBusinessDayMin(starDay);
            nightAuditMemberSearch.setBusinessDayMax(businessDay);
            List<CardRecharge> nightAuditMembersYear = cardRechargeDao.selectBySearchTypeSummary(nightAuditMemberSearch);


            // 查询去年今日
            Integer lastYear = Integer.parseInt(yearStr)-1;
            String lastYearStar = lastYear + "";
            Integer lastBusDay = Integer.parseInt(lastYear + dayStr);
            String lastBusDayStr = lastBusDay + "";

            // 每年的第一个营业日期
            int lastStarDay = Integer.parseInt(lastYearStar+ "0101");

            // 每月的第一个营业日期
            int lastStarMonthDay = Integer.parseInt(lastBusDayStr.substring(0,6)+ "01");



            accountSummarySearch = new AccountSummarySearch();
            accountSummarySearch.setHid(user.getHid());
            accountSummarySearch.setPayType(1);
            accountSummarySearch.setGroupType(1);
            accountSummarySearch.setBusinessDayMin(lastStarMonthDay);
            accountSummarySearch.setBusinessDayMax(lastBusDay);

            // 当月
            List<AccountSummary> lastaccountSummariesMonth = accountDao.accountSummary(accountSummarySearch);

            // 查询会员储值
            nightAuditMemberSearch = new CardRechargeSearch();
            nightAuditMemberSearch.setSta("I");
            nightAuditMemberSearch.setHid(user.getHid());
            nightAuditMemberSearch.setBusinessDayMin(lastStarMonthDay);
            nightAuditMemberSearch.setBusinessDayMax(lastBusDay);
            List<CardRecharge> nightAuditMembersMonthlast = cardRechargeDao.selectBySearchTypeSummary(nightAuditMemberSearch);

            accountSummarySearch = new AccountSummarySearch();
            accountSummarySearch.setHid(user.getHid());
            accountSummarySearch.setPayType(1);
            accountSummarySearch.setGroupType(1);
            accountSummarySearch.setBusinessDayMin(lastStarDay);
            accountSummarySearch.setBusinessDayMax(lastBusDay);

            // 当年
            List<AccountSummary> lastaccountSummariesYear = accountDao.accountSummary(accountSummarySearch);

            nightAuditMemberSearch = new CardRechargeSearch();
            nightAuditMemberSearch.setSta("I");
            nightAuditMemberSearch.setHid(user.getHid());
            nightAuditMemberSearch.setBusinessDayMin(lastStarDay);
            nightAuditMemberSearch.setBusinessDayMax(lastBusDay);
            List<CardRecharge> nightAuditMembersYearlast = cardRechargeDao.selectBySearchTypeSummary(nightAuditMemberSearch);


            // 返回值
            JSONObject jsonObject = new JSONObject();

            DailyReportView dayView = piGetDailyReportView(accountSummaries, nightAuditMembers);
            DailyReportView monthView = piGetDailyReportView(accountSummariesMonth, nightAuditMembersMonth);
            DailyReportView yearView = piGetDailyReportView(accountSummariesYear, nightAuditMembersYear);
            DailyReportView beforMonthView = piGetDailyReportView(lastaccountSummariesMonth, nightAuditMembersMonthlast);
            DailyReportView beforDayView = piGetDailyReportView(lastaccountSummariesYear, nightAuditMembersYearlast);

            piGetDailyReportViewTb(monthView,beforMonthView);
            piGetDailyReportViewTb(yearView,beforDayView);

            // 查询入住数
            NightAuditRoomTypeSearch nightAuditRoomTypeSearch = new NightAuditRoomTypeSearch();
            nightAuditRoomTypeSearch.setHid(user.getHid());
            nightAuditRoomTypeSearch.setBusinessDay(businessDay);
            List<NightAuditRoomType> nightAuditRoomTypes = nightAuditRoomTypeDao.selectBySearchSummary(nightAuditRoomTypeSearch);
            DailyReportRoomView dailyReportRoomView = new DailyReportRoomView(nightAuditRoomTypes.get(0));

            // 当月
            nightAuditRoomTypeSearch = new NightAuditRoomTypeSearch();
            nightAuditRoomTypeSearch.setHid(user.getHid());
            nightAuditRoomTypeSearch.setBusinessDayMin(starMonthDay);
            nightAuditRoomTypeSearch.setBusinessDayMax(businessDay);
            List<NightAuditRoomType> nightAuditRoomTypesMonth = nightAuditRoomTypeDao.selectBySearchSummary(nightAuditRoomTypeSearch);
            DailyReportRoomView dailyReportRoomViewMonth = new DailyReportRoomView(nightAuditRoomTypesMonth.get(0));

            // 当年
            nightAuditRoomTypeSearch = new NightAuditRoomTypeSearch();
            nightAuditRoomTypeSearch.setHid(user.getHid());
            nightAuditRoomTypeSearch.setBusinessDayMin(starDay);
            nightAuditRoomTypeSearch.setBusinessDayMax(businessDay);
            List<NightAuditRoomType> nightAuditRoomTypesYear= nightAuditRoomTypeDao.selectBySearchSummary(nightAuditRoomTypeSearch);
            DailyReportRoomView dailyReportRoomViewYear = new DailyReportRoomView(nightAuditRoomTypesYear.get(0));

            // 去年当月
            nightAuditRoomTypeSearch = new NightAuditRoomTypeSearch();
            nightAuditRoomTypeSearch.setHid(user.getHid());
            nightAuditRoomTypeSearch.setBusinessDayMin(lastStarMonthDay);
            nightAuditRoomTypeSearch.setBusinessDayMax(lastBusDay);
            List<NightAuditRoomType> nightAuditRoomTypesMonthLast = nightAuditRoomTypeDao.selectBySearchSummary(nightAuditRoomTypeSearch);
            DailyReportRoomView dailyReportRoomViewMonthLast = new DailyReportRoomView(nightAuditRoomTypesMonthLast.get(0));


            // 去年当年
            nightAuditRoomTypeSearch = new NightAuditRoomTypeSearch();
            nightAuditRoomTypeSearch.setHid(user.getHid());
            nightAuditRoomTypeSearch.setBusinessDayMin(lastStarDay);
            nightAuditRoomTypeSearch.setBusinessDayMax(lastBusDay);
            List<NightAuditRoomType> nightAuditRoomTypesYearLast = nightAuditRoomTypeDao.selectBySearchSummary(nightAuditRoomTypeSearch);
            DailyReportRoomView dailyReportRoomViewYearLast = new DailyReportRoomView(nightAuditRoomTypesYearLast.get(0));

            piGetDailyReportViewTb(dailyReportRoomViewMonth,dailyReportRoomViewMonthLast);
            piGetDailyReportViewTb(dailyReportRoomViewYear,dailyReportRoomViewYearLast);


            // 查询客源信息
            NightAuditResourceSearch nightAuditResourceSearch = new NightAuditResourceSearch();
            nightAuditResourceSearch.setHid(user.getHid());
            nightAuditResourceSearch.setBusinessDay(businessDay);
            List<NightAuditResource> nightAuditResources = nightAuditResourceDao.selectBySearch(nightAuditResourceSearch);

            // 当月
            nightAuditResourceSearch = new NightAuditResourceSearch();
            nightAuditResourceSearch.setHid(user.getHid());
            nightAuditResourceSearch.setBusinessDayMin(starMonthDay);
            nightAuditResourceSearch.setBusinessDayMax(businessDay);
            List<NightAuditResource> monthAr = nightAuditResourceDao.selectBySearchGroup(nightAuditResourceSearch);
            Map<Integer, NightAuditResource> monthArMap = monthAr.stream().collect(Collectors.toMap(NightAuditResource::getResourceId, a -> a, (k1, k2) -> k1));

            // 当年
            nightAuditResourceSearch = new NightAuditResourceSearch();
            nightAuditResourceSearch.setHid(user.getHid());
            nightAuditResourceSearch.setBusinessDayMin(starDay);
            nightAuditRoomTypeSearch.setBusinessDayMax(businessDay);
            List<NightAuditResource> yearAr = nightAuditResourceDao.selectBySearchGroup(nightAuditResourceSearch);
            Map<Integer, NightAuditResource> yearArMap = yearAr.stream().collect(Collectors.toMap(NightAuditResource::getResourceId, a -> a, (k1, k2) -> k1));

            // 去年当月
            nightAuditResourceSearch = new NightAuditResourceSearch();
            nightAuditResourceSearch.setHid(user.getHid());
            nightAuditResourceSearch.setBusinessDayMin(lastStarMonthDay);
            nightAuditResourceSearch.setBusinessDayMax(lastBusDay);
            List<NightAuditResource> monthArLast = nightAuditResourceDao.selectBySearchGroup(nightAuditResourceSearch);
            Map<Integer, NightAuditResource> monthArLastMap = monthArLast.stream().collect(Collectors.toMap(NightAuditResource::getResourceId, a -> a, (k1, k2) -> k1));

            // 去年当年
            nightAuditResourceSearch = new NightAuditResourceSearch();
            nightAuditResourceSearch.setHid(user.getHid());
            nightAuditResourceSearch.setBusinessDayMin(lastStarDay);
            nightAuditResourceSearch.setBusinessDayMax(lastBusDay);
            List<NightAuditResource> yearArLast = nightAuditResourceDao.selectBySearchGroup(nightAuditResourceSearch);
            Map<Integer, NightAuditResource> yearArLastMap = yearArLast.stream().collect(Collectors.toMap(NightAuditResource::getResourceId, a -> a, (k1, k2) -> k1));

            HashMap<String, JSONObject> integerNightAuditResourceHashMap = new HashMap<>();
            for(NightAuditResource nr:nightAuditResources){

                JSONObject jo = new JSONObject();
                jo.put("checkInNum",nr.getCheckinNum());
                jo.put("money",nr.getSumMoney());

                NightAuditResource mon = monthArMap.get(nr.getResourceId());
                jo.put("checkInNumMon",mon.getCheckinNum());
                jo.put("moneyMon",mon.getSumMoney());

                NightAuditResource year = yearArMap.get(nr.getResourceId());
                jo.put("checkInNumYear",year.getCheckinNum());
                jo.put("moneyYear",year.getSumMoney());

                NightAuditResource nightAuditResource = monthArLastMap.get(nr.getResourceId());

                // 上月汇总
                if(nightAuditResource == null){

                    nightAuditResource = new NightAuditResource();
                    nightAuditResource.setCheckinNum(0);
                    nightAuditResource.setFoodsMoney(0);
                    nightAuditResource.setMoney(0);
                    nightAuditResource.setGoodsMoney(0);

                }


                // 月对比
                Integer checkinNumMon = nightAuditResource.getCheckinNum();

                if(checkinNumMon==0){
                    jo.put("checkInNumMonTb","0.0");
                }else if(mon.getCheckinNum()==0){
                    jo.put("checkInNumMonTb","0.0");
                }else {
                    Double i = (mon.getCheckinNum() - checkinNumMon)*100.0 /checkinNumMon;
                    jo.put("checkInNumMonTb",i);
                }

                jo.put("checkInNumMonLast",checkinNumMon);

                Integer sumMoney = nightAuditResource.getSumMoney();

                if(sumMoney==0){
                    jo.put("moneyMonTb","0.0");
                }else if(mon.getSumMoney()==0){
                    jo.put("moneyMonTb","0.0");
                }else {
                    Double i = (mon.getSumMoney() - sumMoney)*100.0 /sumMoney;
                    jo.put("moneyMonTb",i);
                }

                jo.put("moneyMonLast",sumMoney);

                // 年对比
                NightAuditResource yearData = yearArLastMap.get(nr.getResourceId());

                if(yearData == null){

                    yearData = new NightAuditResource();
                    yearData.setCheckinNum(0);
                    yearData.setFoodsMoney(0);
                    yearData.setMoney(0);
                    yearData.setGoodsMoney(0);

                }


                // 年对比
                Integer checkinNumYear = yearData.getCheckinNum();

                if(checkinNumYear==0){
                    jo.put("checkInNumYearTb","0.0");
                }else if(year.getCheckinNum()==0){
                    jo.put("checkInNumYearTb","0.0");
                }else {
                    Double i = (year.getCheckinNum() - checkinNumYear)*100.0 /checkinNumYear;
                    jo.put("checkInNumYearTb",i);
                }

                jo.put("checkInNumYearLast",checkinNumMon);

                Integer sumMoneyYear = nightAuditResource.getSumMoney();

                if(sumMoneyYear==0){
                    jo.put("moneyYearTb","0.0");
                }else if(year.getSumMoney()==0){
                    jo.put("moneyYearTb","0.0");
                }else {
                    Double i = (year.getSumMoney() - sumMoneyYear)*100.0 /sumMoneyYear;
                    jo.put("moneyYearTb",i);
                }

                jo.put("moneyYearLast",sumMoneyYear);

                integerNightAuditResourceHashMap.put(nr.getResourceId()+"",jo);
            }


            jsonObject.put("dayView",dayView);
            jsonObject.put("monthView",monthView);
            jsonObject.put("yearView",yearView);
            jsonObject.put("beforMonthView",beforMonthView);
            jsonObject.put("beforYearView",beforDayView);

            jsonObject.put("dayRoomView",dailyReportRoomView);
            jsonObject.put("monthRoomView",dailyReportRoomViewMonth);
            jsonObject.put("yearRoomView",dailyReportRoomViewYear);
            jsonObject.put("beforMonthRoomView",dailyReportRoomViewMonthLast);
            jsonObject.put("beforYearRoomView",dailyReportRoomViewYearLast);
            jsonObject.put("resourceMap",integerNightAuditResourceHashMap);

            responseData.setData(jsonObject);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    public DailyReportView piGetDailyReportView( List<AccountSummary> accountSummaries , List<CardRecharge> nightAuditMembers){
        DailyReportView dailyReportView = new DailyReportView();

        for(AccountSummary accountSummary :accountSummaries){

            String payCodeId = accountSummary.getPayCodeId();
            Integer sumMoney = accountSummary.getSumMoney();

            dailyReportView.setSumMoney(sumMoney +dailyReportView.getSumMoney());

            // 会议费
            if(payCodeId.equals("0020")||payCodeId.equals("0021")){
                dailyReportView.setMeetMoney(dailyReportView.getMeetMoney()+sumMoney);
                continue;
            }

            // 餐费
            if(payCodeId.equals("0022")||payCodeId.equals("3500")||payCodeId.equals("3599")||payCodeId.equals("3600")){
                dailyReportView.setFoodMoney(dailyReportView.getFoodMoney()+sumMoney);
                continue;
            }
            // 商品费
            if(payCodeId.equals("3100")||payCodeId.equals("3199")){
                dailyReportView.setGoodMoney(dailyReportView.getGoodMoney()+sumMoney);
                continue;
            }
            // 赔偿费
            if(payCodeId.equals("3300")||payCodeId.equals("3399")){
                dailyReportView.setCompensateMoney(dailyReportView.getCompensateMoney()+sumMoney);
                continue;
            }

            // 赔偿费
            if(payCodeId.equals("0508")){
                dailyReportView.setVipMoney(dailyReportView.getVipMoney()+sumMoney);
                continue;
            }

            // 房费
            dailyReportView.setRoomMoney(dailyReportView.getRoomMoney()+sumMoney);

        }

        for(CardRecharge nm:nightAuditMembers){
            Integer type = nm.getType();
            Integer recharge = nm.getAddBalance();
            if(recharge==null){
                recharge = 0;
            }
            dailyReportView.setSumMoney(recharge +dailyReportView.getSumMoney());
            if(type==2){
                dailyReportView.setVipMoney(dailyReportView.getVipMoney()+recharge);
            }else {
                dailyReportView.setVipRechange(dailyReportView.getVipRechange()+recharge);
            }
        }

        int i = dailyReportView.getMeetMoney() + dailyReportView.getVipMoney() + dailyReportView.getVipRechange() + dailyReportView.getCompensateMoney();
        dailyReportView.setOtherMoney(i);

        return dailyReportView;
    }


    public DailyReportView piGetDailyReportViewTb(DailyReportView piGetDailyReportView,DailyReportView piGetDailyReportView1){

        Integer roomMoney = piGetDailyReportView1.getRoomMoney();
        if(roomMoney==0){
            piGetDailyReportView.setRoomTb(0.0);
        }else if(piGetDailyReportView.getRoomMoney()==0){
            piGetDailyReportView.setRoomTb(0.0);
        }else {
            Double i = (piGetDailyReportView.getRoomMoney() - roomMoney)*100.0 /roomMoney;
            piGetDailyReportView.setRoomTb(i);
        }

        Integer foodMoney = piGetDailyReportView1.getFoodMoney();
        if(foodMoney==0){
            piGetDailyReportView.setFoodTb(0.0);
        }else if(piGetDailyReportView.getFoodMoney()==0){
            piGetDailyReportView.setFoodTb(0.0);
        }else {
            Double i = (piGetDailyReportView.getFoodMoney() - foodMoney)*100.0/ foodMoney;
            piGetDailyReportView.setFoodTb(i);
        }

        Integer goodMoney = piGetDailyReportView1.getGoodMoney();
        if(goodMoney==0){
            piGetDailyReportView.setGoodTb(0.0);
        }else if(piGetDailyReportView.getGoodMoney()==0){
            piGetDailyReportView.setGoodTb(0.0);
        }else {
            Double i = (piGetDailyReportView.getGoodMoney() - goodMoney)*100.0/ goodMoney;
            piGetDailyReportView.setGoodTb(i);
        }


        Integer otherMoney = piGetDailyReportView1.getOtherMoney();
        if(otherMoney==0){
            piGetDailyReportView.setOtherTb(0.0);
        }else if(piGetDailyReportView.getOtherMoney()==0){
            piGetDailyReportView.setOtherTb(0.0);
        }else {
            Double i = (piGetDailyReportView.getOtherMoney() - otherMoney)*100.0/ otherMoney;
            piGetDailyReportView.setOtherTb(i);
        }

        Integer compensateMoney = piGetDailyReportView1.getCompensateMoney();
        if(compensateMoney==0){
            piGetDailyReportView.setCompensateTb(0.0);
        }else if(piGetDailyReportView.getCompensateMoney()==0){
            piGetDailyReportView.setCompensateTb(0.0);
        }else {
            Double i = (piGetDailyReportView.getCompensateMoney() - compensateMoney)*100.0/ compensateMoney;
            piGetDailyReportView.setCompensateTb(i);
        }

        Integer getMeetMoney = piGetDailyReportView1.getMeetMoney();
        if(getMeetMoney==0){
            piGetDailyReportView.setMeetTb(0.0);
        }else if(piGetDailyReportView.getMeetMoney()==0){
            piGetDailyReportView.setMeetTb(0.0);
        }else {
            Double i = (piGetDailyReportView.getCompensateMoney() - getMeetMoney)*100.0/ getMeetMoney;
            piGetDailyReportView.setMeetTb(i);
        }

        Integer getVipMoney = piGetDailyReportView1.getVipMoney();
        if(getVipMoney==0){
            piGetDailyReportView.setVipMoneyTb(0.0);
        }else if(piGetDailyReportView.getVipMoney()==0){
            piGetDailyReportView.setVipMoneyTb(0.0);
        }else {
            Double i = (piGetDailyReportView.getVipMoney() - getVipMoney)*100.0/ getVipMoney;
            piGetDailyReportView.setVipMoneyTb(i);
        }

        Integer vipRechange = piGetDailyReportView1.getVipRechange();
        if(vipRechange==0){
            piGetDailyReportView.setVipRechangeTb(0.0);
        }else if(piGetDailyReportView.getVipRechange()==0){
            piGetDailyReportView.setVipRechangeTb(0.0);
        }else {
            Double i = (piGetDailyReportView.getVipRechange() - vipRechange)*100.0/ vipRechange;
            piGetDailyReportView.setVipRechangeTb(i);
        }

        Integer sumMoney = piGetDailyReportView1.getSumMoney();
        if(sumMoney==0){
            piGetDailyReportView.setSumTb(0.0);
        }else if(piGetDailyReportView.getSumMoney()==0){
            piGetDailyReportView.setSumTb(0.0);
        }else {
            Double i = (piGetDailyReportView.getSumMoney() - sumMoney)*100.0/ sumMoney;
            piGetDailyReportView.setSumTb(i);
        }

        return piGetDailyReportView;
    }

    public DailyReportRoomView piGetDailyReportViewTb(DailyReportRoomView dailyReportRoomView,DailyReportRoomView dailyReportRoomView1){

        Integer canUseRoom = dailyReportRoomView1.getCanUseRoom();

        if(canUseRoom==0){
            dailyReportRoomView.setCanUseRoomTb(0.0);
        }else if(dailyReportRoomView.getCanUseRoom()==0){
            dailyReportRoomView.setCanUseRoomTb(0.0);
        }else {
            Double i = (dailyReportRoomView.getCanUseRoom() - canUseRoom)*100.0 /canUseRoom;
            dailyReportRoomView.setCanUseRoomTb(i);
        }

        Integer repairRoomCount = dailyReportRoomView1.getRepairRoomCount();

        if(repairRoomCount==0){
            dailyReportRoomView.setRepairRoomCountTb(0.0);
        }else if(dailyReportRoomView.getRepairRoomCount()==0){
            dailyReportRoomView.setCanUseRoomTb(0.0);
        }else {
            Double i = (dailyReportRoomView.getRepairRoomCount() - repairRoomCount)*100.0 /repairRoomCount;
            dailyReportRoomView.setRepairRoomCountTb(i);
        }

        Integer openRoomCount = dailyReportRoomView1.getOpenRoomCount();

        if(openRoomCount==0){
            dailyReportRoomView.setOpenRoomCountTb(0.0);
        }else if(dailyReportRoomView.getOpenRoomCount()==0){
            dailyReportRoomView.setOpenRoomCountTb(0.0);
        }else {
            Double i = (dailyReportRoomView.getOpenRoomCount() - openRoomCount)*100.0 /openRoomCount;
            dailyReportRoomView.setOpenRoomCountTb(i);
        }

        Double openRate = dailyReportRoomView1.getOpenRate();

        if(openRate==0){
            dailyReportRoomView.setOpenRateTb(0.0);
        }else if(dailyReportRoomView.getOpenRate()==0){
            dailyReportRoomView.setOpenRateTb(0.0);
        }else {
            Double i = (dailyReportRoomView.getOpenRate() - openRate)*100.0 /openRate;
            dailyReportRoomView.setOpenRateTb(i);
        }

        Integer averagePrice = dailyReportRoomView1.getAveragePrice();

        if(averagePrice==0){
            dailyReportRoomView.setAveragePriceTb(0.0);
        }else if(dailyReportRoomView.getAveragePrice()==0){
            dailyReportRoomView.setAveragePriceTb(0.0);
        }else {
            Double i = (dailyReportRoomView.getAveragePrice() - averagePrice)*100.0 /averagePrice;
            dailyReportRoomView.setAveragePriceTb(i);
        }


        return dailyReportRoomView;
    }
    /**
     * 客源类型报表
     * @param param
     * @return
     */
    @Override
    public ResponseData searchDailyResourceReport(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            // 获取营业日
            int businessDay = param.getInt("businessDay");
            if(null!=param.get("endBusinessDay")){
                return searchDailyResourceReportMoreDay(user,businessDay,param.getInt("endBusinessDay"));
            }



            Date date = HotelUtils.parseInt2Date(businessDay);

            Integer monthDate = HotelUtils.parseDate2Int(HotelUtils.subMonth(date, -1));

            Integer yearDate = HotelUtils.parseDate2Int(HotelUtils.subMonth(date, -12));


            HashMap<String, Object> stringObjectHashMap = new HashMap<>();

            JSONArray resourceList = new JSONArray();

            // 统计客源数据
            NightAuditResourceSearch nightAuditResourceSearch = new NightAuditResourceSearch();
            nightAuditResourceSearch.setHid(user.getHid());
            nightAuditResourceSearch.setBusinessDay(businessDay);
            List<NightAuditResource> nightAuditResources = nightAuditResourceDao.selectBySearch(nightAuditResourceSearch);

            // 月
            nightAuditResourceSearch.setBusinessDay(monthDate);
            List<NightAuditResource> nightAuditResourcesMonth = nightAuditResourceDao.selectBySearch(nightAuditResourceSearch);
            if(nightAuditResourcesMonth==null){
                nightAuditResourcesMonth = new ArrayList<>();
            }
            Map<Integer, NightAuditResource> resMonthMap = nightAuditResourcesMonth.stream().collect(Collectors.toMap(NightAuditResource::getResourceId, a -> a, (k1, k2) -> k1));

            //年
            nightAuditResourceSearch.setBusinessDay(yearDate);
            List<NightAuditResource> nightAuditResourcesYear = nightAuditResourceDao.selectBySearch(nightAuditResourceSearch);
            if(nightAuditResourcesYear==null){
                nightAuditResourcesYear =new ArrayList<>();
            }
            Map<Integer, NightAuditResource> resYearMap = nightAuditResourcesYear.stream().collect(Collectors.toMap(NightAuditResource::getResourceId, a -> a, (k1, k2) -> k1));

            for(NightAuditResource niar:nightAuditResources){

                JSONObject jsonObject = JSONObject.fromObject(niar);

                NightAuditResource mon = resMonthMap.get(niar.getResourceId());
                if(mon==null){
                    mon = new NightAuditResource();
                }

                jsonObject.put("monMoney",mon.getMoney());
                jsonObject.put("monFoodsMoney",mon.getFoodsMoney());
                jsonObject.put("monGoodsMoney",mon.getGoodsMoney());
                jsonObject.put("monCheckinNum",mon.getCheckinNum());

                jsonObject.put("monSumMoney",mon.getMoneyMonth());
                jsonObject.put("monSumFoodsMoney",mon.getFoodsMoneyMonth());
                jsonObject.put("monSumGoodsMoney",mon.getGoodsMoneyMonth());

                NightAuditResource yea = resYearMap.get(niar.getResourceId());
                if(yea==null){
                    yea = new NightAuditResource();
                }

                jsonObject.put("yeaMoney",yea.getMoney());
                jsonObject.put("yeaFoodsMoney",yea.getFoodsMoney());
                jsonObject.put("yeaGoodsMoney",yea.getGoodsMoney());
                jsonObject.put("yeaCheckinNum",yea.getCheckinNum());
                resourceList.add(jsonObject);

            }

            stringObjectHashMap.put("resourceList",resourceList);

            // 计算OTA数据
            NightAuditOtaSearch nightAuditOtaSearch = new NightAuditOtaSearch();
            nightAuditOtaSearch.setHid(user.getHid());
            nightAuditOtaSearch.setBusinessDay(businessDay);
            Page<NightAuditOta> nightAuditOtas = nightAuditOtaDao.selectBySearch(nightAuditOtaSearch);

            // 月
            nightAuditOtaSearch.setBusinessDay(monthDate);
            List<NightAuditOta> nightAuditOtasMonth = nightAuditOtaDao.selectBySearch(nightAuditOtaSearch);
            if(nightAuditOtasMonth==null){
                nightAuditOtasMonth = new ArrayList<>();
            }
            Map<Integer, NightAuditOta> otaMonth = nightAuditOtasMonth.stream().collect(Collectors.toMap(NightAuditOta::getOtaType, a -> a, (k1, k2) -> k1));

            // 年
            nightAuditOtaSearch.setBusinessDay(yearDate);
            List<NightAuditOta> nightAuditOtasYear = nightAuditOtaDao.selectBySearch(nightAuditOtaSearch);
            if(nightAuditOtasYear==null){
                nightAuditOtasYear = new ArrayList<>();
            }

            Map<Integer, NightAuditOta> otaYear = nightAuditOtasYear.stream().collect(Collectors.toMap(NightAuditOta::getOtaType, a -> a, (k1, k2) -> k1));

            JSONArray nightAuditOtasList = new JSONArray();

            for(NightAuditOta nao:nightAuditOtas){

                JSONObject jsonObject = JSONObject.fromObject(nao);

                NightAuditOta otMon = otaMonth.get(nao.getOtaType());
                if(otMon==null){
                    otMon = new NightAuditOta();
                }
                jsonObject.put("monCheckinNum",otMon.getCheckinNum());
                jsonObject.put("monRoomMoney",otMon.getRoomMoney());
                jsonObject.put("monFoodsMoney",otMon.getFoodsMoney());
                jsonObject.put("monGoodsMoney",otMon.getGoodsMoney());

                jsonObject.put("monSumRoomMoney",otMon.getRoomMoneyMonth());
                jsonObject.put("monSumFoodsMoney",otMon.getFoodsMoneyMonth());
                jsonObject.put("monSumGoodsMoney",otMon.getGoodsMoneyMonth());

                NightAuditOta otYea = otaYear.get(nao.getOtaType());
                if(otYea==null){
                    otYea = new NightAuditOta();
                }
                jsonObject.put("yeaCheckinNum",otYea.getCheckinNum());
                jsonObject.put("yeaRoomMoney",otYea.getRoomMoney());
                jsonObject.put("yeaFoodsMoney",otYea.getFoodsMoney());
                jsonObject.put("yeaGoodsMoney",otYea.getGoodsMoney());

                nightAuditOtasList.add(jsonObject);

            }

            stringObjectHashMap.put("nightAuditOtas",nightAuditOtasList);

            responseData.setData(stringObjectHashMap);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    // 多天
    public ResponseData searchDailyResourceReportMoreDay( TbUserSession user,Integer businessDay, Integer lastBuss) throws Exception{

        ResponseData responseData = new ResponseData(ER.SUCC);
        Date date = HotelUtils.parseInt2Date(businessDay);

        Integer monthDate = HotelUtils.parseDate2Int(HotelUtils.subMonth(date, -1));

        Integer yearDate = HotelUtils.parseDate2Int(HotelUtils.subMonth(date, -12));

        HashMap<String, Object> stringObjectHashMap = new HashMap<>();

        JSONArray resourceList = new JSONArray();

        // 统计客源数据
        NightAuditResourceSearch nightAuditResourceSearch = new NightAuditResourceSearch();
        nightAuditResourceSearch.setHid(user.getHid());
        nightAuditResourceSearch.setBusinessDayMin(businessDay);
        nightAuditResourceSearch.setBusinessDayMax(lastBuss);
        List<NightAuditResource> nightAuditResources = nightAuditResourceDao.selectBySearchGroup(nightAuditResourceSearch);

        // 月
        nightAuditResourceSearch.setBusinessDay(monthDate);
        List<NightAuditResource> nightAuditResourcesMonth = nightAuditResourceDao.selectBySearch(nightAuditResourceSearch);
        if(nightAuditResourcesMonth==null){
            nightAuditResourcesMonth = new ArrayList<>();
        }
        Map<Integer, NightAuditResource> resMonthMap = nightAuditResourcesMonth.stream().collect(Collectors.toMap(NightAuditResource::getResourceId, a -> a, (k1, k2) -> k1));

        //年
        nightAuditResourceSearch.setBusinessDay(yearDate);
        List<NightAuditResource> nightAuditResourcesYear = nightAuditResourceDao.selectBySearch(nightAuditResourceSearch);
        if(nightAuditResourcesYear==null){
            nightAuditResourcesYear =new ArrayList<>();
        }
        Map<Integer, NightAuditResource> resYearMap = nightAuditResourcesYear.stream().collect(Collectors.toMap(NightAuditResource::getResourceId, a -> a, (k1, k2) -> k1));

        for(NightAuditResource niar:nightAuditResources){

            JSONObject jsonObject = JSONObject.fromObject(niar);

            NightAuditResource mon = resMonthMap.get(niar.getResourceId());
            if(mon==null){
                mon = new NightAuditResource();
            }

            jsonObject.put("monMoney",mon.getMoney());
            jsonObject.put("monFoodsMoney",mon.getFoodsMoney());
            jsonObject.put("monGoodsMoney",mon.getGoodsMoney());
            jsonObject.put("monCheckinNum",mon.getCheckinNum());

            jsonObject.put("monSumMoney",mon.getMoneyMonth());
            jsonObject.put("monSumFoodsMoney",mon.getFoodsMoneyMonth());
            jsonObject.put("monSumGoodsMoney",mon.getGoodsMoneyMonth());

            NightAuditResource yea = resYearMap.get(niar.getResourceId());
            if(yea==null){
                yea = new NightAuditResource();
            }

            jsonObject.put("yeaMoney",yea.getMoney());
            jsonObject.put("yeaFoodsMoney",yea.getFoodsMoney());
            jsonObject.put("yeaGoodsMoney",yea.getGoodsMoney());
            jsonObject.put("yeaCheckinNum",yea.getCheckinNum());
            resourceList.add(jsonObject);

        }

        stringObjectHashMap.put("resourceList",resourceList);

        // 计算OTA数据
        NightAuditOtaSearch nightAuditOtaSearch = new NightAuditOtaSearch();
        nightAuditOtaSearch.setHid(user.getHid());
        nightAuditOtaSearch.setBusinessDayMin(businessDay);
        nightAuditOtaSearch.setBusinessDayMax(lastBuss);
        Page<NightAuditOta> nightAuditOtas = nightAuditOtaDao.selectBySearchSummary(nightAuditOtaSearch);

        // 月
        nightAuditOtaSearch.setBusinessDay(monthDate);
        List<NightAuditOta> nightAuditOtasMonth = nightAuditOtaDao.selectBySearch(nightAuditOtaSearch);
        if(nightAuditOtasMonth==null){
            nightAuditOtasMonth = new ArrayList<>();
        }
        Map<Integer, NightAuditOta> otaMonth = nightAuditOtasMonth.stream().collect(Collectors.toMap(NightAuditOta::getOtaType, a -> a, (k1, k2) -> k1));

        // 年
        nightAuditOtaSearch.setBusinessDay(yearDate);
        List<NightAuditOta> nightAuditOtasYear = nightAuditOtaDao.selectBySearch(nightAuditOtaSearch);
        if(nightAuditOtasYear==null){
            nightAuditOtasYear = new ArrayList<>();
        }

        Map<Integer, NightAuditOta> otaYear = nightAuditOtasYear.stream().collect(Collectors.toMap(NightAuditOta::getOtaType, a -> a, (k1, k2) -> k1));

        JSONArray nightAuditOtasList = new JSONArray();

        for(NightAuditOta nao:nightAuditOtas){

            JSONObject jsonObject = JSONObject.fromObject(nao);

            NightAuditOta otMon = otaMonth.get(nao.getOtaType());
            if(otMon==null){
                otMon = new NightAuditOta();
            }
            jsonObject.put("monCheckinNum",otMon.getCheckinNum());
            jsonObject.put("monRoomMoney",otMon.getRoomMoney());
            jsonObject.put("monFoodsMoney",otMon.getFoodsMoney());
            jsonObject.put("monGoodsMoney",otMon.getGoodsMoney());

            jsonObject.put("monSumRoomMoney",otMon.getRoomMoneyMonth());
            jsonObject.put("monSumFoodsMoney",otMon.getFoodsMoneyMonth());
            jsonObject.put("monSumGoodsMoney",otMon.getGoodsMoneyMonth());

            NightAuditOta otYea = otaYear.get(nao.getOtaType());
            if(otYea==null){
                otYea = new NightAuditOta();
            }
            jsonObject.put("yeaCheckinNum",otYea.getCheckinNum());
            jsonObject.put("yeaRoomMoney",otYea.getRoomMoney());
            jsonObject.put("yeaFoodsMoney",otYea.getFoodsMoney());
            jsonObject.put("yeaGoodsMoney",otYea.getGoodsMoney());

            nightAuditOtasList.add(jsonObject);

        }

        stringObjectHashMap.put("nightAuditOtas",nightAuditOtasList);

        responseData.setData(stringObjectHashMap);

        return responseData;
    }

    @Override
    public ResponseData searchDailyRoomType(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);



            Date businessDayMin = HotelUtils.parseInt2Date(param.getInt("businessDayMin"));
            Date businessDayMax = HotelUtils.parseInt2Date(param.getInt("businessDayMax"));

            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDateTwo(HotelUtils.parseDate2Str(businessDayMin), HotelUtils.parseDate2Str(businessDayMax));

            // 房型数据
            NightAuditRoomTypeSearch nightAuditRoomTypeSearch = new NightAuditRoomTypeSearch();
            nightAuditRoomTypeSearch.setHid(user.getHid());
            nightAuditRoomTypeSearch.setBusinessDayMax(param.getInt("businessDayMax"));
            nightAuditRoomTypeSearch.setBusinessDayMin(param.getInt("businessDayMin"));
            List<NightAuditRoomType> nightAuditRoomTypes = nightAuditRoomTypeDao.selectBySearch(nightAuditRoomTypeSearch);


            Map<String, Object> collect = nightAuditRoomTypes.stream().collect(Collectors.toMap(NightAuditRoomType::getDateKey, a -> a, (k1, k2) -> k1)) ;

            // 房型汇总
            Map<Integer, List<NightAuditRoomType>> rtMap = nightAuditRoomTypes.stream().collect(Collectors.groupingBy(NightAuditRoomType::getRoomTypeId));

            Set<Integer> rtKeys = rtMap.keySet();

            JSONObject roomCountMap = new JSONObject();

            for(Integer key : rtKeys){

                List<NightAuditRoomType> nightAuditRoomTypes1 = rtMap.get(key);

                int sum = nightAuditRoomTypes1.stream().mapToInt(NightAuditRoomType::getOpenRoomCount).sum();

                roomCountMap.put(key+"",sum);
            }


            // 日期汇总
            int sumCount = 0;

            JSONObject jsonObject = new JSONObject();


            Map<Integer, List<NightAuditRoomType>> dayMap = nightAuditRoomTypes.stream().collect(Collectors.groupingBy(NightAuditRoomType::getBusinessDay));

            Set<Integer> dayKeys = dayMap.keySet();

            for(Integer key :dayKeys){

                List<NightAuditRoomType> nightAuditRoomTypes1 = dayMap.get(key);

                int sum = nightAuditRoomTypes1.stream().mapToInt(NightAuditRoomType::getOpenRoomCount).sum();

                sumCount+=sum;

                jsonObject.put(HotelUtils.parseDate2Str(HotelUtils.parseInt2Date(key)).substring(0, 10).substring(0, 10),sum);

            }


            HashMap<String, Object> stringObjectHashMap = new HashMap<>();
            stringObjectHashMap.put("dateList",allDayListBetweenDate);
            stringObjectHashMap.put("roomList",collect);
            stringObjectHashMap.put("roomCountMap",roomCountMap);
            stringObjectHashMap.put("dayCountMap",jsonObject);
            stringObjectHashMap.put("sumCount",sumCount);

            responseData.setData(stringObjectHashMap);


        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    @Override
    public ResponseData roomAccountDetails(RoomAccountDetails roomAccountDetails) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            final TbUserSession user = this.getTbUserSession(roomAccountDetails);

            // 查询当前夜审记录的在住信息
            NightAuditRoomStatusSearch nightAuditRoomStatusSearch = new NightAuditRoomStatusSearch();
            nightAuditRoomStatusSearch.setHid(user.getHid());
            nightAuditRoomStatusSearch.setBusinessDay(user.getBusinessDay());

            List<NightAuditRoomStatus> nightAuditRoomStatuses = nightAuditRoomStatusDao.selectBySearch(nightAuditRoomStatusSearch);

            HashMap<Integer, Boolean> registIdShows = new HashMap<>();

            StringBuilder sb = new StringBuilder();

            HashMap<Integer, RegistAccountRep> bookAccountMap = new HashMap<>();
            HashMap<Integer, RegistAccountRep> registAccountMap = new HashMap<>();

            Boolean registBoo = false;
            Boolean bookBoo = false;

            for(NightAuditRoomStatus nightAuditRoomStatus:nightAuditRoomStatuses){

                registBoo = true;

                Integer registId = nightAuditRoomStatus.getRegistId();

                if(registId==null){
                    continue;
                }

                if(registIdShows.get(registId)!=null&&registIdShows.get(registId)){
                    continue;
                }

                sb.append(registId);
                sb.append(",");

                registIdShows.put(registId,true);

                RegistAccountRep registAccountRep = registAccountMap.get(registId);

                if(registAccountRep==null){
                    registAccountRep = new RegistAccountRep();
                }
                registAccountRep.setRegistId(registId);
                registAccountRep.setPeopleName(nightAuditRoomStatus.getGuestName());
                registAccountRep.setType(1);
                registAccountRep.setRoomNo(nightAuditRoomStatus.getRoomNum());

                registAccountMap.put(registId,registAccountRep);
            }

            // 查找账务信息

            HashMap<Integer, Boolean> bookingOderShows = new HashMap<>();
            StringBuilder sbBookIds = new StringBuilder();


            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setBusinessDay(roomAccountDetails.getBusinessDay());
            accountSearch.setIsCancel(0);

            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            //  收款退款
            Integer sumPayMoney = 0 ;
            Integer sumRefundMoney = 0 ;

            // 房费 餐费 商品费
            Integer sumRoomPrice = 0 ;
            Integer sumFoodPrice = 0 ;
            Integer sumGoodPrice = 0 ;

            for(Account account:accounts){

                Integer registId = account.getRegistId();

                Integer bookingId = account.getBookingId();
                RegistAccountRep registAccountRep ;

                if(registId==null&&bookingId==null){
                    continue;
                }
                // 预订单信息
                if(registId==null){

                    bookBoo = true;

                    if(bookingOderShows.get(bookingId)==null){
                        bookingOderShows.put(bookingId,true);
                        sbBookIds.append(bookingId);
                        sbBookIds.append(",");
                    }

                    registAccountRep = bookAccountMap.get(bookingId);
                    if(registAccountRep==null){
                        registAccountRep = new RegistAccountRep();
                        registAccountRep.setBookingId(bookingId);
                    }
                    registAccountRep.setType(2);

                }else {

                    registBoo = true;

                    registAccountRep = registAccountMap.get(registId);

                    if(registIdShows.get(registId)==null){
                        sb.append(registId);
                        sb.append(",");

                        registIdShows.put(registId,true);

                    }

                    if(registAccountRep==null){
                        registAccountRep = new RegistAccountRep();
                        registAccountRep.setRegistId(registId);
                        registAccountRep.setType(1);
                    }
                }

                Integer payType = account.getPayType();

                Integer price = account.getPrice();


                if(payType==1){

                    switch (account.getAccountType()){
                        case 1:
                            sumRoomPrice+=price;
                            registAccountRep.setSumRoom(registAccountRep.getSumRoom()+price);
                            break;
                        case 2:
                            sumGoodPrice+=price;
                            registAccountRep.setSumGoods(registAccountRep.getSumGoods()+price);
                            break;
                        case 3:
                            sumFoodPrice+=price;
                            registAccountRep.setSumFood(registAccountRep.getSumFood()+price);
                            break;
                    }

                } else {

                    if(price>=0){
                        sumPayMoney+=price;
                        registAccountRep.setSumPay(registAccountRep.getSumPay()+price);

                    }else {
                        sumRefundMoney+=price;
                        registAccountRep.setSumRefund(registAccountRep.getSumRefund()+price);
                    }

                }

                if(registAccountRep.getType()==1){
                    registAccountMap.put(registId,registAccountRep);
                }else {
                    bookAccountMap.put(bookingId,registAccountRep);
                }

            }

            List<Regist> regists = new ArrayList<>();

            List<RegistPerson> registPeople = new ArrayList<>();

            if(registBoo){
                String s = sb.toString();
                RegistSearch registSearch = new RegistSearch();
                registSearch.setHid(user.getHid());
                registSearch.setRegistIds(s.substring(0,s.length()-1));
                regists = registDao.selectBySearch(registSearch);

                RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                registPersonSearch.setHid(user.getHid());
                registPersonSearch.setRegInKeys(registSearch.getRegistIds());

                registPeople = registPersonDao.selectBySearch(registPersonSearch);

            }

            Map<Integer, List<RegistPerson>> personList = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));

            ArrayList<RegistAccountRep> registAccountReps = new ArrayList<>();

            HashMap<Integer, String> resourceMap = new HashMap<>();
            resourceMap.put(1,"散客");
            resourceMap.put(2,"会员");
            resourceMap.put(3,"OＴA");
            resourceMap.put(4,"协议单位");
            resourceMap.put(5,"旅行社");

            for(Regist regist:regists){

                Integer registId = regist.getRegistId();

                Integer resourceId = regist.getResourceId();

                List<RegistPerson> registPeople1 = personList.get(registId);

                String name = "";

                if(registPeople1!=null){
                    for(RegistPerson rp:registPeople1){
                        name+=rp.getPersonName()+" ";
                    }
                }

                RegistAccountRep registAccountRep = registAccountMap.get(registId);
                if(registAccountRep==null){
                    registAccountRep = new RegistAccountRep();
                    registAccountRep.setRoomNo(regist.getRoomNum());
                    registAccountRep.setRegistId(registId);
                    registAccountRep.setType(1);

                }
                registAccountRep.setState(regist.getState());
                registAccountRep.setRoomNo(regist.getRoomNum());
                registAccountRep.setResourceId(resourceId);
                registAccountRep.setPeopleName(name);
                if(resourceId==5){

                    registAccountRep.setResourceName(regist.getCompayName());


                }else {

                    registAccountRep.setResourceName(resourceMap.get(resourceId));

                }

                registAccountReps.add(registAccountRep);
            }

            // 预订信息
            List<BookingOrder> bookingOrders = new ArrayList<>();
            if(bookBoo){

                String s = sbBookIds.toString();

                BookingOrderSearch bookingOrderSearch = new BookingOrderSearch();
                bookingOrderSearch.setHid(user.getHid());
                bookingOrderSearch.setBookingOrderIds(s.substring(0,s.length()-1));

                bookingOrders = bookingOrderDao.selectBySearch(bookingOrderSearch);

            }

            for(BookingOrder bookingOrder:bookingOrders){
                Integer resourceId = bookingOrder.getResourceId();
                Integer bookingOrderId = bookingOrder.getBookingOrderId();

                RegistAccountRep registAccountRep = bookAccountMap.get(bookingOrderId);
                if(registAccountRep==null){
                    registAccountRep = new RegistAccountRep();
                    registAccountRep.setBookingId(bookingOrderId);
                    registAccountRep.setType(2);
                    registAccountRep.setPeopleName(bookingOrder.getBookingName());

                }

                registAccountRep.setRoomNo("预订单");

                if(resourceId==5){

                    registAccountRep.setResourceName(bookingOrder.getCompanyName());


                }else {

                    registAccountRep.setResourceName(resourceMap.get(resourceId));

                }

                registAccountReps.add(registAccountRep);
            }


            HashMap<String, Object> res = new HashMap<>();
            res.put("list",registAccountReps);
            res.put("sumPayMoney",sumPayMoney);
            res.put("sumRefundMoney",sumRefundMoney);
            res.put("sumFoodPrice",sumFoodPrice);
            res.put("sumGoodPrice",sumGoodPrice);
            res.put("sumRoomPrice",sumRoomPrice);


            responseData.setData(res);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    /**
     * 试算平衡表哦
     * @param nightAuditBalanceSearch
     * @return
     */
    @Override
    public ResponseData ssphr(NightAuditBalanceSearch nightAuditBalanceSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            final TbUserSession user = this.getTbUserSession(nightAuditBalanceSearch);
            Integer businessDay = nightAuditBalanceSearch.getBusinessDay();
            if(null!=nightAuditBalanceSearch.getEndBusinessDay()){
               return ssphrMoreDay(user,businessDay,nightAuditBalanceSearch.getEndBusinessDay());
            }

            Date date = HotelUtils.parseInt2Date(businessDay);

            Date date1 = HotelUtils.addDayGetNewDate(date, -1);

            Integer lastBuss = HotelUtils.parseDate2Int(date1);

            JSONObject res = new JSONObject();

            // 余额记录
            NightAuditBalanceSearch nbs = new NightAuditBalanceSearch();
            nbs.setHid(user.getHid());
            nbs.setBusinessDay(lastBuss);

            Page<NightAuditBalance> nightAuditBalances = nightAuditBalanceDao.selectBySearch(nbs);
            if(nightAuditBalances==null||nightAuditBalances.size()<0){
                throw new Exception("未查询到相应的夜审记录");
            }

            NightAuditBalance nightAuditBalance1 = new NightAuditBalance();
            res.put("lastData",nightAuditBalances.get(0));


            nbs.setBusinessDay(businessDay);
            Page<NightAuditBalance> nownightAuditBalances = nightAuditBalanceDao.selectBySearch(nbs);
            res.put("nowData",nownightAuditBalances.get(0));
            if(nownightAuditBalances==null||nownightAuditBalances.size()<0){
                throw new Exception("未查询到相应的夜审记录");
            }

            // 查询协议回款信息
            NigthAuditArPaymentSearch nigthAuditArPaymentSearch = new NigthAuditArPaymentSearch();
            nigthAuditArPaymentSearch.setHid(user.getHid());
            nigthAuditArPaymentSearch.setBusinessDay(businessDay);
            Page<NigthAuditArPayment> nigthAuditArPayments = nigthAuditArPaymentDao.selectBySearch(nigthAuditArPaymentSearch);

            Map<Integer, NigthAuditArPayment> nigthAuditArPaymentMap = nigthAuditArPayments.stream().collect(Collectors.toMap(NigthAuditArPayment::getPayClassId, a -> a, (k1, k2) -> k1));

            // 会员数据
            CardRechargeSearch cardRechargeSearch = new CardRechargeSearch();
            cardRechargeSearch.setHid(user.getHid());
            cardRechargeSearch.setBusinessDay(businessDay);
            cardRechargeSearch.setSta("I");

            Page<CardRecharge> cardRecharges = cardRechargeDao.selectBySearch(cardRechargeSearch);

            Map<Integer, List<CardRecharge>> cardRechargesMap = cardRecharges.stream().collect(Collectors.groupingBy(CardRecharge::getPayClassId));


            // 支付数据
            NightAuditPaySearch nightAuditPaySearch = new NightAuditPaySearch();
            nightAuditPaySearch.setHid(user.getHid());
            nightAuditPaySearch.setBusinessDay(businessDay);
            nightAuditPaySearch.setType(1);
            List<NightAuditPay> nightAuditPays = nightAuditPayDao.selectBySearch(nightAuditPaySearch);
            Map<String, NightAuditPay> nightAuditPaysMap = nightAuditPays.stream().collect(Collectors.toMap(NightAuditPay::getPayClassId, a -> a, (k1, k2) -> k1));


            HotelCostCodeSearch hotelCostCodeSearch = new HotelCostCodeSearch();
            hotelCostCodeSearch.setParentId(0);
            hotelCostCodeSearch.setCostType(2);
            List<HotelCostCode> hotelCostCodes = hotelCostCodeDao.selectBySearch(hotelCostCodeSearch);

            JSONArray payList = new JSONArray();

            Integer arSumMoney  = 0;

            Integer vipSumMoney = 0;

            Integer paySumMoney = 0;

            for(HotelCostCode hcode:hotelCostCodes){

                Integer parentId = hcode.getParentId();
                if(parentId>0){
                    continue;
                }

                Integer sumMoney = 0;

                NightAuditPay nap = nightAuditPaysMap.get(hcode.getCostId().toString());
                if(nap==null){
                    nap = new NightAuditPay();
                    nap.setTotalAmount(0);
                    nap.setPayClassId(hcode.getCostId().toString());
                    nap.setPayClassName(hcode.getCostName());
                }

                Integer totalAmount = nap.getTotalAmount();
                sumMoney+= totalAmount;
                paySumMoney+= totalAmount;

                Integer arMoney = 0 ;
                NigthAuditArPayment nigthAuditArPayment = nigthAuditArPaymentMap.get(hcode.getCostId());

                if(nigthAuditArPayment!=null){
                    arMoney=nigthAuditArPayment.getMoney();
                }
                sumMoney+=arMoney;

                Integer vipMoney = 0;
                List<CardRecharge> rechargeList = cardRechargesMap.get(hcode.getCostId());
                if(rechargeList!=null){
                    for(CardRecharge cr:rechargeList){
                        vipMoney+=cr.getPayMoney();
                    }
                }
                sumMoney+=vipMoney;

                if(vipMoney==0&&arMoney==0&&totalAmount==0){
                    continue;
                }

                // 查询历史数据
                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setHid(user.getHid());
                accountSearch.setBusinessDay(businessDay);
                AccountBalance accountBalance = accountDao.accountBalance(accountSearch);

                NightAuditBalance nightAuditBalance = nightAuditBalances.get(0);
                nightAuditBalance.setMoney(accountBalance.getDiff());
                res.put("lastData",nightAuditBalance);

                JSONObject payNode = new JSONObject();
                payNode.put("payClassId",nap.getPayClassId());
                payNode.put("payClassName",nap.getPayClassName());
                payNode.put("money",totalAmount);
                payNode.put("arMoney",arMoney);
                payNode.put("vipMoney",vipMoney);
                payNode.put("sumMoney",sumMoney);
                payList.add(payNode);

                arSumMoney+=arMoney;
                vipSumMoney+=vipMoney;


            }

            res.put("payList",payList);
            res.put("arSumMoney",arSumMoney);
            res.put("vipSumMoney",vipSumMoney);
            res.put("paySumMoney",paySumMoney);


            // 房费数据
            NightAuditDailyRegistRecordSearch nightAuditDailyRegistRecordSearch = new NightAuditDailyRegistRecordSearch();
            nightAuditDailyRegistRecordSearch.setHid(user.getHid());
            nightAuditDailyRegistRecordSearch.setBusinessDay(businessDay);
            List<NightAuditDailyRegistRecord> nightAuditDailyRegistRecords = nightAuditDailyRegistRecordDao.selectBySearch(nightAuditDailyRegistRecordSearch);

            JSONArray saleList = new JSONArray();

            Integer saleSumMoney = 0;

            for(NightAuditDailyRegistRecord ndr:nightAuditDailyRegistRecords){

                if(ndr.getTotalAmount()==0){
                    continue;
                }

                JSONObject payNode = new JSONObject();
                payNode.put("payClassId",ndr.getPayCodeId());
                payNode.put("payClassName",ndr.getPayCodeName());
                payNode.put("money",ndr.getTotalAmount());
                saleList.add(payNode);

                saleSumMoney+=ndr.getTotalAmount();

            }

            res.put("saleSumMoney",saleSumMoney);
            res.put("saleList",saleList);

            responseData.setData(res);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    public ResponseData ssphrMoreDay(TbUserSession user ,Integer businessDay, Integer lastBuss) throws Exception{
        ResponseData responseData = new ResponseData(ER.SUCC);


        JSONObject res = new JSONObject();

        // 余额记录
        NightAuditBalanceSearch nbs = new NightAuditBalanceSearch();
        nbs.setHid(user.getHid());
        nbs.setBusinessDay(lastBuss);

        Page<NightAuditBalance> nightAuditBalances = nightAuditBalanceDao.selectBySearch(nbs);

        NightAuditBalance nightAuditBalance1 = new NightAuditBalance();
        res.put("lastData",nightAuditBalances.size()>0?nightAuditBalances.get(0):nightAuditBalance1);


        nbs.setBusinessDay(businessDay);
        Page<NightAuditBalance> nownightAuditBalances = nightAuditBalanceDao.selectBySearch(nbs);
        res.put("nowData",nownightAuditBalances.size()>0?nownightAuditBalances.get(0):nightAuditBalance1);


        // 查询协议回款信息
        NigthAuditArPaymentSearch nigthAuditArPaymentSearch = new NigthAuditArPaymentSearch();
        nigthAuditArPaymentSearch.setHid(user.getHid());
        nigthAuditArPaymentSearch.setBusinessDayMin(businessDay);
        nigthAuditArPaymentSearch.setBusinessDayMax(lastBuss);
        Page<NigthAuditArPayment> nigthAuditArPayments = nigthAuditArPaymentDao.selectBySearchSummary(nigthAuditArPaymentSearch);

        Map<Integer, NigthAuditArPayment> nigthAuditArPaymentMap = nigthAuditArPayments.stream().collect(Collectors.toMap(NigthAuditArPayment::getPayClassId, a -> a, (k1, k2) -> k1));

        // 会员数据
        CardRechargeSearch cardRechargeSearch = new CardRechargeSearch();
        cardRechargeSearch.setHid(user.getHid());
        cardRechargeSearch.setSta("I");
        cardRechargeSearch.setBusinessDayMin(businessDay);
        cardRechargeSearch.setBusinessDayMax(lastBuss);
        Page<CardRecharge> cardRecharges = cardRechargeDao.selectBySearch(cardRechargeSearch);

        Map<Integer, List<CardRecharge>> cardRechargesMap = cardRecharges.stream().collect(Collectors.groupingBy(CardRecharge::getPayClassId));

        // 支付数据
        NightAuditPaySearch nightAuditPaySearch = new NightAuditPaySearch();
        nightAuditPaySearch.setHid(user.getHid());
        nightAuditPaySearch.setType(2);
        nightAuditPaySearch.setBusinessDayMin(businessDay);
        nightAuditPaySearch.setBusinessDayMax(lastBuss);
        List<NightAuditPay> nightAuditPays = nightAuditPayDao.selectBySearchSummary(nightAuditPaySearch);
        Map<String, NightAuditPay> nightAuditPaysMap = nightAuditPays.stream().collect(Collectors.toMap(NightAuditPay::getPayClassId, a -> a, (k1, k2) -> k1));

        HotelCostCodeSearch hotelCostCodeSearch = new HotelCostCodeSearch();
        hotelCostCodeSearch.setParentId(0);
        hotelCostCodeSearch.setCostType(2);
        List<HotelCostCode> hotelCostCodes = hotelCostCodeDao.selectBySearch(hotelCostCodeSearch);

        JSONArray payList = new JSONArray();

        Integer arSumMoney  = 0;

        Integer vipSumMoney = 0;

        Integer paySumMoney = 0;

        for(HotelCostCode hcode:hotelCostCodes){

            Integer parentId = hcode.getParentId();
            if(parentId>0){
                continue;
            }

            Integer sumMoney = 0;

            NightAuditPay nap = nightAuditPaysMap.get(hcode.getCostId().toString());
            if(nap==null){
                nap = new NightAuditPay();
                nap.setTotalAmount(0);
                nap.setPayClassId(hcode.getCostId().toString());
                nap.setPayClassName(hcode.getCostName());
            }

            Integer totalAmount = nap.getTotalAmount();
            sumMoney+= totalAmount;
            paySumMoney+= totalAmount;

            Integer arMoney = 0 ;
            NigthAuditArPayment nigthAuditArPayment = nigthAuditArPaymentMap.get(hcode.getCostId());

            if(nigthAuditArPayment!=null){
                arMoney=nigthAuditArPayment.getMoney();
            }
            sumMoney+=arMoney;

            Integer vipMoney = 0;
            List<CardRecharge> rechargeList = cardRechargesMap.get(hcode.getCostId());
            if(rechargeList!=null){
                for(CardRecharge cr:rechargeList){
                    vipMoney+=cr.getPayMoney();
                }
            }
            sumMoney+=vipMoney;

            if(vipMoney==0&&arMoney==0&&totalAmount==0){
                continue;
            }


            JSONObject payNode = new JSONObject();
            payNode.put("payClassId",nap.getPayClassId());
            payNode.put("payClassName",nap.getPayClassName());
            payNode.put("money",totalAmount);
            payNode.put("arMoney",arMoney);
            payNode.put("vipMoney",vipMoney);
            payNode.put("sumMoney",sumMoney);
            payList.add(payNode);

            arSumMoney+=arMoney;
            vipSumMoney+=vipMoney;


        }

        res.put("payList",payList);
        res.put("arSumMoney",arSumMoney);
        res.put("vipSumMoney",vipSumMoney);
        res.put("paySumMoney",paySumMoney);


        // 房费数据
        NightAuditDailyRegistRecordSearch nightAuditDailyRegistRecordSearch = new NightAuditDailyRegistRecordSearch();
        nightAuditDailyRegistRecordSearch.setHid(user.getHid());
        nightAuditDailyRegistRecordSearch.setMinBusinessDay(businessDay);
        nightAuditDailyRegistRecordSearch.setMaxBusinessDay(lastBuss);
        List<NightAuditDailyRegistRecord> nightAuditDailyRegistRecords = nightAuditDailyRegistRecordDao.selectBySearchSummary(nightAuditDailyRegistRecordSearch);

        JSONArray saleList = new JSONArray();

        Integer saleSumMoney = 0;

        for(NightAuditDailyRegistRecord ndr:nightAuditDailyRegistRecords){

            if(ndr.getTotalAmount()==0){
                continue;
            }

            JSONObject payNode = new JSONObject();
            payNode.put("payClassId",ndr.getPayCodeId());
            payNode.put("payClassName",ndr.getPayCodeName());
            payNode.put("money",ndr.getTotalAmount());
            saleList.add(payNode);

            saleSumMoney+=ndr.getTotalAmount();

        }

        res.put("saleSumMoney",saleSumMoney);
        res.put("saleList",saleList);

        responseData.setData(res);
        return responseData;
    }



    @Override
    public ResponseData searchNightRoomType(NightAuditRoomTypeSearch nightAuditRoomTypeSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            final TbUserSession user = this.getTbUserSession(nightAuditRoomTypeSearch);

            List<NightAuditRoomType> nightAuditRoomTypes = nightAuditRoomTypeDao.selectBySearch(nightAuditRoomTypeSearch);

            Map<String, NightAuditRoomType> collect = nightAuditRoomTypes.stream().collect(Collectors.toMap(NightAuditRoomType::getDateKey, a -> a, (k1, k2) -> k1));

            Date date = HotelUtils.parseInt2Date(nightAuditRoomTypeSearch.getBusinessDayMin());
            Date date1 = HotelUtils.parseInt2Date(nightAuditRoomTypeSearch.getBusinessDayMax());

            nightAuditRoomTypeSearch.setGroupType(1);
            List<NightAuditRoomType> nightAuditRoomTypesMonth = nightAuditRoomTypeDao.selectBySearchSummary(nightAuditRoomTypeSearch);

            for(NightAuditRoomType rt:nightAuditRoomTypesMonth){
                collect.put("sum"+rt.getRoomTypeId(),rt);
            }

            List<String> allDayListBetweenDateTwo = HotelUtils.getAllDayListBetweenDateTwo(HotelUtils.parseDate2Str(date), HotelUtils.parseDate2Str(date1));

            responseData.setData(collect);
            responseData.setData1(allDayListBetweenDateTwo);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            log.error("",e);
        }

        return responseData;
    }

    @Override
    public void pushRoomState(TbUserSession user) {
        this.push(user.getHotelGroupId(), user.getHid(), 7, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
    }
}
