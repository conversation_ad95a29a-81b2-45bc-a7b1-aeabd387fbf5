package com.pms.czabsnight.service.order.transaction.impl;

import com.pms.czabsnight.bean.AddArAccount;
import com.pms.czabsnight.bean.CheckInRegist;
import com.pms.czabsnight.service.order.transaction.PmsOrderTransactionService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountThirdPayRecode;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czaccount.dao.account.AccountThirdPayRecodeDao;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomRepairRecordHistory;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomCheckRecordDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomRepairRecordHistoryDao;
import com.pms.czhotelfoundation.service.room.transaction.RoomTransactionService;
import com.pms.czhotelfoundation.service.zimg.ZimgService;
import com.pms.czmembership.bean.company.HotelCompanyAccountInfo;
import com.pms.czmembership.bean.company.HotelCompanyArRecode;
import com.pms.czmembership.dao.company.HotelCompanyAccountInfoDao;
import com.pms.czmembership.dao.company.HotelCompanyArRecodeDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.StringUtil;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.room.RoomUtils;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.dao.*;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CountDownLatch;

@Service
@Primary
public class PmsOrderTransactionServiceImpl extends BaseService implements PmsOrderTransactionService {

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private RegistGroupDao registGroupDao;

    @Autowired
    private RoomTransactionService roomTransactionService;

    @Autowired
    private ZimgService zimgService;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private AccountThirdPayRecodeDao accountThirdPayRecodeDao;

    @Autowired
    private RoomRepairRecordHistoryDao roomRepairRecordDao;

    @Autowired
    private RoomCheckRecordDao roomCheckRecordDao;

    @Autowired
    private HotelCompanyArRecodeDao hotelCompanyArRecodeDao;

    @Autowired
    private HotelCompanyAccountInfoDao hotelCompanyAccountInfoDao;

    @Autowired
    private RegistStayoverDao registStayoverDao;

    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public JSONArray blendCheckInTransaction(RegistGroup registGroup, List<CheckInRegist> checkInRegists, List<Account> accounts, int sumMoney, TbUserSession user, ArrayList<RoomAuxiliaryRelation> deleteRelations) throws Exception {

        JSONArray res = new JSONArray();

        ArrayList<Oprecord> oprecords = new ArrayList<>();

        Oprecord oprecord = new Oprecord(user);

        // 1.添加集团

        if (registGroup != null) {
            registGroup.setSumPay(sumMoney);
            Integer insert = registGroupDao.insert(registGroup);
            if (insert < 1) {
                throw new Exception("保存团队或联房信息失败");
            }
        }
        // 2.添加入住信息
        Regist mainRegist = new Regist();

        // 添加入住人
        ArrayList<RegistPerson> allRegistPeople = new ArrayList<>();
        ArrayList<RoomAuxiliaryRelation> allRoomAuxiliary = new ArrayList<>();
        ArrayList<BookingOrderDailyPrice> allAddPriceList = new ArrayList<>();
        ArrayList<BookingOrderConfig> allBookingOrderConfigs = new ArrayList<>();
        ArrayList<RoomInfo> roomInfos = new ArrayList<>();
        ArrayList<RoomRepairRecordHistory> roomRepairRecordHistories = new ArrayList<>();

        HashMap<Integer, RegistPerson> registPeopleMap = new HashMap<>();

        RoomAuxiliaryRelation addRelation = new RoomAuxiliaryRelation();

        for (CheckInRegist checkInRegist : checkInRegists) {

            // 1.主单
            Regist regist = checkInRegist.getRegist();
            regist.setRegistId(null);
            regist.setTeamCodeId(registGroup != null ? registGroup.getRegistGroupId() : 0);
            regist.setTeamCodeName(registGroup != null ? registGroup.getGroupName() : "");
            regist.setCheckoutBusinessDay((HotelUtils.parseDate2Int(regist.getCheckoutTime())));

            if (regist.getIsMainRoom() == 1 || checkInRegists.size() == 1) {
                regist.setSumPay(sumMoney);
            }

            Integer insert1 = registDao.insert(regist);

            if (insert1 < 1) {
                throw new Exception("房间:" + regist.getRoomNum() + "  入住失败");
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("roomNo", regist.getRoomNum());
            jsonObject.put("registId", regist.getRegistId());

            res.add(jsonObject);

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription(HotelUtils.currentTime() + "    房间" + regist.getRoomNum() + " 办理入住。");
            oprecords.add(oprecord);

            if (mainRegist.getRegistId() == null) {
                if (regist.getIsMainRoom() == 1) {
                    mainRegist = regist;
                } else {
                    mainRegist = regist;
                }
            }
            // 2.配置信息
            BookingOrderConfig bookingOrderConfig = checkInRegist.getBookingOrderConfig();
            bookingOrderConfig.setBookingOrderId(regist.getBookingOrderId());
            bookingOrderConfig.setRegistId(regist.getRegistId());

            allBookingOrderConfigs.add(bookingOrderConfig);

         /*   Integer saveBookingOrderConfig = bookingOrderConfigDao.saveBookingOrderConfig(bookingOrderConfig);

            if (saveBookingOrderConfig < 1) {
                throw new Exception("房间:" + regist.getRoomNum() + "  添加配置失败");
            }*/

            // 3.辅助房态
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = checkInRegist.getRoomAuxiliaryRelations();

            for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {

                addRelation = new RoomAuxiliaryRelation();
                addRelation.setRelationId(null);
                addRelation.setHid(user.getHid());
                addRelation.setHotelGroupId(user.getHotelGroupId());
                addRelation.setBookingOrderId(regist.getBookingOrderId());
                addRelation.setRegistId(regist.getRegistId());
                addRelation.setRoomId(regist.getRoomNumId());
                addRelation.setRoomNum(regist.getRoomNum());
                addRelation.setRoomAuxiliaryId(roomAuxiliaryRelation.getRoomAuxiliaryId());
                addRelation.setSort(roomAuxiliaryRelation.getSort());

                allRoomAuxiliary.add(addRelation);
                //  roomAuxiliaryRelationDao.saveRoomAuxiliaryRelation(roomAuxiliaryRelation);

            }


            // 4.价格
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = checkInRegist.getBookingOrderDailyPrices();
            for (BookingOrderDailyPrice bookingOrderDailyPrice : bookingOrderDailyPrices) {

                bookingOrderDailyPrice.setRoomNumId(regist.getRoomNumId());
                bookingOrderDailyPrice.setRoomTypeId(regist.getRoomTypeId());
                bookingOrderDailyPrice.setRegistId(regist.getRegistId());
                bookingOrderDailyPrice.setBookingOrderId(regist.getBookingOrderId());

                allAddPriceList.add(bookingOrderDailyPrice);
                /*Integer integer = bookingOrderDailyPriceDao.saveBookingOrderDailyPrice(bookingOrderDailyPrice);

                if (integer < 1) {
                    throw new Exception("房间:" + regist.getRoomNum() + "  添加 " + bookingOrderDailyPrice.getDailyTime() + " 房价失败");
                }*/

            }

            // 5.入住人
            List<RegistPerson> registPeople = checkInRegist.getRegistPeople();
            for (RegistPerson registPerson : registPeople) {
                registPerson.setRegistPersonId(null);
                registPerson.setHid(user.getHid());
                registPerson.setHotelGroupId(user.getHotelGroupId());
                registPerson.setRegistId(regist.getRegistId());
                registPerson.setRegistState(0);

                allRegistPeople.add(registPerson);

              /*  Integer insert2 = registPersonDao.insert(registPerson);
                if (insert2 < 1) {
                    throw new Exception("房间:" + regist.getRoomNum() + "  添加入住人： " + registPerson.getPersonName() + " 失败");
                }
*/
                Integer integer = registPeopleMap.get(regist.getRegistId()) != null ? registPeopleMap.get(regist.getRegistId()).getRegistId() : null;
                if (integer == null) {
                    registPeopleMap.put(regist.getRegistId(), registPerson);
                }

                oprecord = new Oprecord(user);
                oprecord.setOccurTime(HotelUtils.currentTime());
                oprecord.setDescription("房间:" + regist.getRoomNum() + "  添加入住人： " + registPerson.getPersonName());
                oprecords.add(oprecord);

            }

            // 6 修改房间状态
            RoomInfo roomInfo = new RoomInfo();
            roomInfo.setRoomInfoId(regist.getRoomNumId());
            roomInfo.setRoomNumState(ROOM_STATUS.OCC);
            roomInfos.add(roomInfo);

            RoomRepairRecordHistory roomRepairRecord = new RoomRepairRecordHistory();

            roomRepairRecord.setRoomId(roomInfo.getRoomInfoId());
            roomRepairRecord.setRoomNum(roomInfo.getRoomNum());
            roomRepairRecord.setHid(roomInfo.getHid());
            roomRepairRecord.setHotelGroupId(roomInfo.getHotelGroupId());
            roomRepairRecord.setClassId(user.getClassId());
            roomRepairRecord.setCreateUserId(user.getUserId());
            roomRepairRecord.setCreateUserName(user.getUserName());
            roomRepairRecord.setCreateTime(new Date());
            roomRepairRecord.setUpdateUserId(user.getUserId());
            roomRepairRecord.setUpdateUserName(user.getUserName());
            roomRepairRecord.setRoomTypeId(roomInfo.getRoomTypeId());
            roomRepairRecord.setUpdateTime(new Date());
            roomRepairRecord.setType(RoomUtils.getRoomRepairRecordType(roomInfo.getRoomNumState(), ROOM_STATUS.OCC));
            roomRepairRecord.setState(RoomUtils.getRoomRepairRecordState(roomInfo.getRoomNumState(), ROOM_STATUS.OCC));
            roomRepairRecord.setBusinessDay(user.getBusinessDay());
            roomRepairRecord.setDes(roomInfo.getRoomNum() + "入住。");

            roomRepairRecordHistories.add(roomRepairRecord);

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription("房间:" + regist.getRoomNum() + " 改为在住。");
            oprecords.add(oprecord);

        }

        // 添加账务
        for (Account account : accounts) {
            account.setRoomInfoId(mainRegist.getRoomNumId());
            account.setRoomNum(mainRegist.getRoomNum());
            account.setRoomTypeId(mainRegist.getRoomTypeId());
            account.setRegistId(mainRegist.getRegistId());
            account.setRegistState(mainRegist.getState());
            account.setTeamCodeId(registGroup != null ? registGroup.getRegistGroupId() : 0);
            account.setRoomTypeId(mainRegist.getRoomTypeId());
            account.setBookingId(mainRegist.getBookingOrderId());
            if (registPeopleMap.get(mainRegist.getRegistId()) != null) {
                account.setRegistPersonId(registPeopleMap.get(mainRegist.getRegistId()).getRegistPersonId());
                account.setRegistPersonName(registPeopleMap.get(mainRegist.getRegistId()).getPersonName());
            }

            AccountThirdPayRecode accountThirdPayRecode = account.getAccountThirdPayRecode();
            if (accountThirdPayRecode != null) {
                Integer insert1 = accountThirdPayRecodeDao.insert(accountThirdPayRecode);
                account.setThirdAccoutId(accountThirdPayRecode.getAccountThirdId());
            }

            Integer integer = accountDao.saveAccount(account);
            if (integer < 1) {
                throw new Exception("房间:" + mainRegist.getRoomNum() + " 入账 --" + account.getGoodName() + " " + account.getPrice() / 100.0 + " 元 失败");
            }

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription("房间:" + mainRegist.getRoomNum() + " 入账 --" + account.getGoodName() + " " + account.getPrice() / 100.0 + " 元");
            oprecords.add(oprecord);

        }

        // 4.删除辅助房态
        if (deleteRelations.size() > 0) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(deleteRelations);
        }
        if (allRoomAuxiliary.size() > 0) {
            roomAuxiliaryRelationDao.addRoomAuxiliaryRelationList(allRoomAuxiliary);
        }

        // 修改房价
        bookingOrderDailyPriceDao.addPriceList(allAddPriceList);

        // 批量添加入住人
        registPersonDao.addPeople(allRegistPeople);

        // 批量添加房间配置 bookingordercofig
        bookingOrderConfigDao.addBookingOrderConfigList(allBookingOrderConfigs);


        // 修改房间信息
        roomInfoDao.updateRoomList(roomInfos);

        // 添加房间修改记录
        roomRepairRecordDao.addRoomRepairRecordHistoryList(roomRepairRecordHistories);


        addOprecords(oprecords);

        return res;
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public Map<Integer, Integer> bookingCheckInTransactionNew(BookingOrder bookingOrder, List<BookingOrderRoomNum> bookingOrderRoomNumList, Map<Integer, Regist> checkInRegistMap,
                                                              List<RoomInfo> roomInfoList, Map<Integer, List<RegistPerson>> registPersonMap, Map<Integer, BookingOrderConfig> bookingOrderConfigMap,
                                                              List<RoomAuxiliaryRelation> deleteRoomAuxiliaryRelationList, Map<Integer, List<RoomAuxiliaryRelation>> addRoomAuxiliaryRelation, RegistGroup registGroup,
                                                              List<Account> accounts, TbUserSession user, Map<Integer, List<BookingOrderDailyPrice>> dayPriceMap) throws Exception {
        HashMap<Integer, Integer> registMap = new HashMap<>();

        final ArrayList<Oprecord> oprecords = new ArrayList<>();
        Oprecord oprecord = new Oprecord(user);

        // 1.判断是否是团队，指定主账房
        //   第一个登记单为主账房
        //   registGroup === id : -2 需要注册团队信息 ，-1 不需要添加 团队信息  ，>0 说明已创建过团队信息
        Regist mainRegist = new Regist();
        boolean isMain = false;
        boolean needAccount = false;  //如果当前预定为第一次入住，则需把账务转入到主账房

        if (registGroup.getTeamType() == -1) {
            isMain = true;
            needAccount = true;
        }

        if (registGroup.getTeamType() == -2) {
            registGroup.setRegistGroupId(null);
            Integer insert = registGroupDao.insert(registGroup);

            if (insert < 1) {
                throw new Exception("创建订单团队信息失败。");
            }

            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setBcodeO(registGroup.getRegistGroupId() + "");

            oprecord.setDescription("创建预定团队记录。预订单号:" + bookingOrder.getSn());

            oprecords.add(oprecord);

            isMain = true;
            needAccount = true;

        }

        // 修改预订单状态
        bookingOrderDao.editBookingOrder(bookingOrder);

        // 2.遍历预定房型表，进行入住操作
        String currentTime = HotelUtils.currentTime();

        final CountDownLatch cdOrder = new CountDownLatch(1);
        final CountDownLatch cdAnswer = new CountDownLatch(3);


        // 添加入住人
        ArrayList<RegistPerson> allRegistPeople = new ArrayList<>();
        ArrayList<BookingOrderRoomNum> allUpaOrderRoom = new ArrayList<>();
        ArrayList<RoomAuxiliaryRelation> allRoomAuxiliary = new ArrayList<>();
        ArrayList<BookingOrderDailyPrice> allUpaPriceList = new ArrayList<>();
        ArrayList<BookingOrderConfig> allBookingOrders = new ArrayList<>();
        ArrayList<RoomInfo> roomInfos = new ArrayList<>();
        ArrayList<RoomRepairRecordHistory> roomRepairRecordHistories = new ArrayList<>();


        for (BookingOrderRoomNum roomNum : bookingOrderRoomNumList) {
            Date date = new Date();
            // 2.1 入住，保存登记记录
            Regist regist = checkInRegistMap.get(roomNum.getRoomNumId());
            if (needAccount) {
                regist.setSumPay(registGroup.getSumPay());
                regist.setSumSale(registGroup.getSumSales());
                needAccount = false;
            }
            if (registGroup.getRegistGroupId() != null && registGroup.getRegistGroupId() > 0) {
                regist.setSessionToken(user.getSessionId());
                regist.setRegistGroupId(registGroup.getRegistGroupId());
                regist.setTeamCodeId(registGroup.getRegistGroupId());
                regist.setTeamCodeName(registGroup.getGroupName());
                regist.setIsMainRoom(isMain ? 1 : 0);
            }

            Integer registInsert = registDao.insert(regist);
            if (registInsert < 1) {
                throw new Exception("添加入住信息失败。排房编号:" + roomNum.getId());
            }
            registMap.put(regist.getRoomNumId(), regist.getRegistId());

            // 如果为第一间房，则表位主账房
            if (isMain) {
                mainRegist = regist;
                isMain = false;
            }

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(currentTime);
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setBcodeO(registGroup.getRegistGroupId() + "");
            oprecord.setDescription("将 " + roomNum.getRoomNum() + " 的排房信息转为入住信息。");

            oprecords.add(oprecord);

            // 2.2 添加入住人
            List<RegistPerson> registPeople = registPersonMap.get(roomNum.getRoomNumId());
            for (RegistPerson registPerson : registPeople) {
                registPerson.setRegistId(regist.getRegistId());
                registPerson.setRegistPersonId(null);
                registPerson.setTeamCodeId(regist.getTeamCodeId());
                registPerson.setRegistState(0);

                allRegistPeople.add(registPerson);

                oprecord = new Oprecord(user);
                oprecord.setOccurTime(currentTime);
                oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
                oprecord.setRegistId(regist.getRegistId());
                oprecord.setBcodeO(registGroup.getRegistGroupId() + "");
                oprecord.setDescription(roomNum.getRoomNum() + " 房间添加入住人 : " + registPerson.getPersonName());

                oprecords.add(oprecord);

            }
            if (registPeople.size() < 1) {
                RegistPerson registPerson = new RegistPerson();
                registPerson.setRegistPersonId(null);
                registPerson.setHid(user.getHid());
                registPerson.setHotelGroupId(user.getHotelGroupId());
                registPerson.setRegistId(regist.getRegistId());
                registPerson.setClassId(user.getClassId());
                registPerson.setRegistState(0);
                registPerson.setPersonName("");
                registPerson.setRoomNum(regist.getRoomNum());
                registPerson.setRoomNumId(regist.getRoomNumId());
                registPerson.setRegistState(0);
                registPerson.setTeamCodeId(registGroup.getRegistGroupId());
                registPerson.setBookingOrderRoomNumId(roomNum.getId());
                registPerson.setIsOther(0);
                registPerson.setStartTime(new Date());

                allRegistPeople.add(registPerson);

            }

            // 2.3 将预定排房表改成已入住的状态
            roomNum.setIsCheckin(1);
            roomNum.setUpdateTime(date);
            roomNum.setUpdateUserId(user.getUserId());
            roomNum.setRegistId(regist.getRegistId());

            allUpaOrderRoom.add(roomNum);

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(currentTime);
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setBcodeO(registGroup.getRegistGroupId() + "");
            oprecord.setDescription("将 " + roomNum.getRoomNum() + " 的排房信息转为入住完成。");

            oprecords.add(oprecord);

            // 2.4 添加辅助房态
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = addRoomAuxiliaryRelation.get(roomNum.getRoomNumId());

            for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {
                roomAuxiliaryRelation.setRelationId(null);
                roomAuxiliaryRelation.setRegistId(regist.getRegistId());
                allRoomAuxiliary.add(roomAuxiliaryRelation);
            }

            // 2.5 添加配置信息
            BookingOrderConfig bookingOrderConfig = bookingOrderConfigMap.get(roomNum.getRoomNumId());
            bookingOrderConfig.setId(null);
            bookingOrderConfig.setBookingOrderId(null);
            bookingOrderConfig.setRegistId(regist.getRegistId());

            // 批量修改
            allBookingOrders.add(bookingOrderConfig);

            // 2.6 添加房价信息
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = dayPriceMap.get(roomNum.getRoomNumId());
            for (BookingOrderDailyPrice bookingOrderDailyPrice : bookingOrderDailyPrices) {
                bookingOrderDailyPrice.setRegistId(regist.getRegistId());
                bookingOrderDailyPrice.setBreakNum(regist.getBreakfastNum());

                allUpaPriceList.add(bookingOrderDailyPrice);

            }

        }

        String accountDescr = "";
        for (Account account : accounts) {

            // 已添加账务不作处理
            Integer registId = account.getRegistId();
            if (registId != null && registId > 0) {
                continue;
            }
            account.setRegistPersonName("");
            account.setRegistPersonId(0);
            account.setRegistId(mainRegist.getRegistId());
            account.setRoomInfoId(mainRegist.getRoomNumId());
            account.setRoomNum(mainRegist.getRoomNum());
            account.setRoomTypeId(mainRegist.getRoomTypeId());
            account.setUpdateTime(new Date());
            account.setUpdateUserId(user.getUserId());
            account.setUpdateUserName(user.getUserName());

            Integer integer = accountDao.editAccount(account);

            if (integer < 1) {
                throw new Exception("将预订账务:" + account.getAccountId() + " 转入房间:" + mainRegist.getRoomNum() + " 失败。");
            }

            accountDescr += account.getAccountId() + ",";
        }

        if (!StringUtil.isEmpty(accountDescr.toString())) {
            oprecord = new Oprecord(user);
            oprecord.setOccurTime(currentTime);
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setRegistId(mainRegist.getRegistId());
            oprecord.setBcodeO(registGroup.getRegistGroupId() + "");
            oprecord.setDescription("将编号为  " + accountDescr + " 的预订单的账务转入到 : " + mainRegist.getRoomNum() + " 房间中");
        }

        oprecords.add(oprecord);

        // 4.删除辅助房态
        if (deleteRoomAuxiliaryRelationList.size() > 0) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(deleteRoomAuxiliaryRelationList);
        }

        if (allRoomAuxiliary.size() > 0) {
            roomAuxiliaryRelationDao.addRoomAuxiliaryRelationList(allRoomAuxiliary);
        }

        // 修改房价
        bookingOrderDailyPriceDao.updatePriceList(allUpaPriceList);

        // 批量修改预订信息
        HashMap<String, List> stringListHashMap = new HashMap<>();
        stringListHashMap.put("list", allUpaOrderRoom);
        bookingOrderRoomNumDao.updateBookingOrderRoomNumList(stringListHashMap);

        // 批量添加入住人
        registPersonDao.addPeople(allRegistPeople);

        // 批量添加房间配置 bookingordercofig
        bookingOrderConfigDao.addBookingOrderConfigList(allBookingOrders);


        // 5.更改房间记录
        for (RoomInfo roomInfo : roomInfoList) {

            roomInfo.setRoomNumState(ROOM_STATUS.OCC);

            roomInfos.add(roomInfo);

            RoomRepairRecordHistory roomRepairRecord = new RoomRepairRecordHistory();

            roomRepairRecord.setRoomId(roomInfo.getRoomInfoId());
            roomRepairRecord.setRoomNum(roomInfo.getRoomNum());
            roomRepairRecord.setHid(roomInfo.getHid());
            roomRepairRecord.setHotelGroupId(roomInfo.getHotelGroupId());
            roomRepairRecord.setClassId(user.getClassId());
            roomRepairRecord.setRoomTypeId(roomInfo.getRoomTypeId());
            roomRepairRecord.setCreateUserId(user.getUserId());
            roomRepairRecord.setCreateUserName(user.getUserName());
            roomRepairRecord.setCreateTime(new Date());
            roomRepairRecord.setUpdateUserId(user.getUserId());
            roomRepairRecord.setUpdateUserName(user.getUserName());
            roomRepairRecord.setUpdateTime(new Date());
            roomRepairRecord.setType(RoomUtils.getRoomRepairRecordType(roomInfo.getRoomNumState(), ROOM_STATUS.OCC));
            roomRepairRecord.setState(RoomUtils.getRoomRepairRecordState(roomInfo.getRoomNumState(), ROOM_STATUS.OCC));
            roomRepairRecord.setBusinessDay(user.getBusinessDay());
            roomRepairRecord.setDes(roomInfo.getRoomNum() + "入住。");

            roomRepairRecordHistories.add(roomRepairRecord);

        }

        // 修改房间信息
        roomInfoDao.updateRoomList(roomInfos);

        // 添加房间修改记录
        roomRepairRecordDao.addRoomRepairRecordHistoryList(roomRepairRecordHistories);


        this.addOprecords(oprecords);

        return registMap;
    }

    /**
     * 结账
     * roomAuxiliaryRelations 为删除
     * 其他的全部修改。
     *
     * @param registList               登记集合
     * @param registGroups             团队主表
     * @param bookingOrderList         预订单集合
     * @param bookingOrderRoomTypeList 预定房型集合
     * @param bookingOrderRoomNums     预定房间
     * @param roomAuxiliaryRelations   辅助房态集合
     * @param user
     * @param accountList              账务集合 m-,lnp90k8bciouvy67
     * @param registPeople             入住人集合
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void checkOut(List<Regist> registList, List<RegistGroup> registGroups, List<BookingOrder> bookingOrderList, List<BookingOrderRoomType> bookingOrderRoomTypeList,
                         List<BookingOrderRoomNum> bookingOrderRoomNums, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, TbUserSession user, List<Account> accountList, Map<Integer, RoomInfo> roomInfoMap,
                         ArrayList<Object> salesHotelCommissionDetailsList, List<RegistPerson> registPeople) throws Exception {


        //1.将登记信息改为已结
       /* for (Regist regist:registList){
            Integer update = registDao.update(regist);
        }*/
        registDao.updateRegistList(registList);

        //2.将团队信息改成已结
        for (RegistGroup registGroup : registGroups) {

            Integer update = registGroupDao.update(registGroup);
            String descr = "结账";

            if (registGroup.getState() == 3) {
                descr = "部分结账";
            }

            if (update < 1) {
                throw new Exception("将 " + registGroup.getGroupName() + " 改为 " + descr + " 失败");
            }
        }

        //3.修改预订单
        for (BookingOrder bookingOrder : bookingOrderList) {

            Integer integer = bookingOrderDao.editBookingOrder(bookingOrder);

            if (integer < 1) {
                throw new Exception("修改预订单失败,订单号:" + bookingOrder.getSn() + "。编号:" + bookingOrder.getBookingOrderId());
            }

        }

        //4.预定房型
        for (BookingOrderRoomType bookingOrderRoomType : bookingOrderRoomTypeList) {

            Integer integer = bookingOrderRoomTypeDao.editBookingOrderRoomType(bookingOrderRoomType);

            if (integer < 1) {
                throw new Exception("修改预定房型失败，编号:" + bookingOrderRoomType.getId());
            }

        }

        //5.预定房间
        HashMap<String, List> stringListHashMap = new HashMap<>();
        if (bookingOrderRoomNums.size() > 0) {
            stringListHashMap.put("list", bookingOrderRoomNums);
            bookingOrderRoomNumDao.updateBookingOrderRoomNumList(stringListHashMap);
        }
      /*  for(BookingOrderRoomNum bookingOrderRoomNum:bookingOrderRoomNums){

            Integer integer = bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNum);

            if(integer<1){
                throw new Exception("修改预定房间失败,编号："+bookingOrderRoomNum.getId());
            }

        }*/

        //6.辅助房态
        if (roomAuxiliaryRelations.size() > 0) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(roomAuxiliaryRelations);
        }
        /*for(RoomAuxiliaryRelation roomAuxiliaryRelation:roomAuxiliaryRelations){

            Integer integer = roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());

            if(integer<1){
                throw new Exception("删除辅助房态失败。编号:"+roomAuxiliaryRelation.getRelationId());
            }

        }*/

        //7.账务信息
        if (accountList.size() > 0) {
            accountDao.editAccountList(accountList);
        }
     /*   for(Account account:accountList){

            Integer integer = accountDao.editAccount(account);

            if(integer<1){
                throw new Exception("修改账务信息失败。编号:"+account.getAccountId());
            }

        }
*/
        //8.修改房间信息
        Set<Integer> roomKeys = roomInfoMap.keySet();

        ArrayList<RoomInfo> roomInfos = new ArrayList<>();

        ArrayList<RoomRepairRecordHistory> roomRepairRecordHistories = new ArrayList<>();

        ArrayList<Oprecord> oprecords = new ArrayList<>();

        Oprecord oprecord = new Oprecord(user);

        JSONObject param = new JSONObject();
        for (Integer key : roomKeys) {
            RoomInfo roomInfo = roomInfoMap.get(key);
            roomInfo.setRoomNumState(ROOM_STATUS.VD);
            // 6 修改房间状态
            roomInfos.add(roomInfo);

            RoomRepairRecordHistory roomRepairRecord = new RoomRepairRecordHistory();

            roomRepairRecord.setRoomId(roomInfo.getRoomInfoId());
            roomRepairRecord.setRoomNum(roomInfo.getRoomNum());
            roomRepairRecord.setHid(roomInfo.getHid());
            roomRepairRecord.setHotelGroupId(roomInfo.getHotelGroupId());
            roomRepairRecord.setClassId(user.getClassId());
            roomRepairRecord.setCreateUserId(user.getUserId());
            roomRepairRecord.setCreateUserName(user.getUserName());
            roomRepairRecord.setCreateTime(new Date());
            roomRepairRecord.setUpdateUserId(user.getUserId());
            roomRepairRecord.setUpdateUserName(user.getUserName());
            roomRepairRecord.setUpdateTime(new Date());
            roomRepairRecord.setType(RoomUtils.getRoomRepairRecordType(roomInfo.getRoomNumState(), ROOM_STATUS.VD));
            roomRepairRecord.setState(RoomUtils.getRoomRepairRecordState(roomInfo.getRoomNumState(), ROOM_STATUS.VD));
            roomRepairRecord.setBusinessDay(user.getBusinessDay());
            roomRepairRecord.setDes(roomInfo.getRoomNum() + "入住。");

            roomRepairRecordHistories.add(roomRepairRecord);

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription("房间:" + roomInfo.getRoomNum() + " 进行结账，房态改为脏房。");
            oprecords.add(oprecord);
        }

        // 修改房间信息
        roomInfoDao.updateRoomList(roomInfos);

        // 添加房间修改记录
        roomRepairRecordDao.addRoomRepairRecordHistoryList(roomRepairRecordHistories);
        //9.修改提成信息
        /*for(SalesHotelCommissionDetails salesHotelCommissionDetails:salesHotelCommissionDetailsList){

            Integer integer = salesHotelCommissionDetailsDao.editSalesHotelCommissionDetails(salesHotelCommissionDetails);

            if(integer<1){
                throw new Exception("修改提成信息失败，流水号:"+salesHotelCommissionDetails.getSn()+", 编号:"+salesHotelCommissionDetails.getId());
            }

        }*/

        // 10 入住人更改

        ArrayList<RegistPerson> registPeople1 = new ArrayList<>();

        for (RegistPerson registPerson : registPeople) {

            Integer registState = registPerson.getRegistState();
            if (registState == 1) {
                continue;
            }

            registPerson.setRegistState(1);
            registPerson.setAuthenticateTime(new Date());
            registPeople1.add(registPerson);
        }

        registPersonDao.updatePeople(registPeople1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void overStayTransactionTwo(Regist regist, List<BookingOrderDailyPrice> bookingOrderDailyPrices, List<BookingOrderDailyPrice> deletePrices, AddArAccount addArAccount, HotelCompanyAccountInfo hotelCompanyAccountInfo, BookingOrderConfig bookingOrderConfig, RegistStayover rs) throws Exception {

        Integer update = registDao.update(regist);

        if (update < 1) {
            throw new Exception("修改登记单失败");
        }

        Integer integer = bookingOrderConfigDao.editBookingOrderConfig(bookingOrderConfig);

        if (integer < 1) {
            throw new Exception("修改配置信息失败");
        }

        if (bookingOrderDailyPrices.size() > 0) {
            bookingOrderDailyPriceDao.addPriceList(bookingOrderDailyPrices);
        }

        if (deletePrices.size() > 0) {
            bookingOrderDailyPriceDao.deletePriceList(deletePrices);
        }


        if (addArAccount != null) {

            Account account = addArAccount.getAccount();

            HotelCompanyArRecode hotelCompanyArRecode = addArAccount.getHotelCompanyArRecode();

            Integer integer1 = accountDao.saveAccount(account);

            if (integer1 < 1) {
                throw new Exception("添加挂账信息失败");
            }

            hotelCompanyArRecode.setTransactionId(account.getAccountId());
            Integer insert = hotelCompanyArRecodeDao.insert(hotelCompanyArRecode);


            if (insert < 1) {
                throw new Exception("添加挂账信息失败-1");
            }

            Integer update1 = hotelCompanyAccountInfoDao.update(hotelCompanyAccountInfo);

            if (update1 < 1) {
                throw new Exception("修改账户信息失败");
            }

        }


        registStayoverDao.insert(rs);


    }

}
