package com.pms.czabsnight.service.machine;

import net.sf.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

public interface MachineInterface {
    /**
     * 获取PMS系统中房间和门锁的对应关系，没有的话需手动输入。
     * @param map
     * @return
     */
    public JSONObject getRoomNoLockCode(Map<String, Object> map);

    public JSONObject getRoomInfo(Map<String ,Object> map);

    public JSONObject getHotelBusinessDay(Map<String ,Object> map);

    /**
     * 获取PMS系统中楼栋和楼层的数据
     * @param map
     * @return
     */
    public JSONObject getBuildFloorRoom(Map<String, Object> map);

    /**
     * 查询酒店房型列表，查询两个时间段内酒店房型的详细信息及可用数量
     * @param map
     * @return
     */
    public JSONObject getRoomTypeInfoList(Map<String, Object> map);


    public JSONObject getRoomTypePrice(JSONObject postData);

    /**
     * 查询当前房型下可用的房间列表，可以根据楼层楼栋进行筛选
     * @param map
     * @return
     */
    public JSONObject getAvailRoomList(Map<String, Object> map);


    /**
     * 房间锁定或解锁
     * @param map
     * @return
     */
    public JSONObject lockRoom(Map<String, Object> map);

    /**
     * 贵宾入住
     * @param map
     * @return
     */
    public JSONObject registerCheckIn(Map<String, Object> map);

    /**
     * 添加支付信息.客人读取身份证并提交入住信息之后，会跳到支付界面，付款成功后，自助机会把相应的付款信息提交到PMS系统
     * @param map
     * @return
     */
    public JSONObject addTransaction(Map<String, Object> map);

    /**
     * 制作房卡后，在PMS中添加制作房卡的记录。只用来记录做卡的信息
     * @param map
     * @return
     */
    public JSONObject makeGuestCard(Map<String, Object> map);

    /**
     * 撤销登记状态，如果用户取消未支付取消入住，则把房间返回成可用状态。
     * @param map
     * @return
     */
    public JSONObject cancelRoom(Map<String, Object> map);


    /**
     * 添加随客信息
     * @param map
     * @return
     */
    public JSONObject addRegisterOther(Map<String, Object> map);


    /**
     * 查询PMS预订信息，用来查看当前用户未入住的预订订单。本接口只处理单个房间的散客订单.
     * @param map
     * @return
     */
    public JSONObject getArrivingList(Map<String, Object> map);


    /**
     * 预订补排房，仅针对有效但尚未排房的订单，进行入住前的补排房操作。
     * @param map
     * @return
     */
    public JSONObject assignRoom(Map<String, Object> map);


    /**
     * 客人续住
     * @param map
     * @return
     */
    public JSONObject continuedLive(Map<String, Object> map);


    /**
     * 查询在住人的账务信息,计算出加收金额或者退款金额。等客人点击确认后把信息转发到结账接口。退款顺序一般先微信支付宝，最后才是预授权，
     * 注（自助机实际业务是传门锁的物理编号过去，PMS后台根据门锁的物理编号反查询房间号，然后通过房间号去查询登记信息）
     * @param map
     * @return
     */
    public JSONObject getRegisterMsg(Map<String, Object> map);

    /**
     * 发送查房请求
     * @param map
     * @return
     */
    public JSONObject checkRoom(Map<String, Object> map);

    /**
     * 获取查房结果
     * @param map
     * @return
     */
    public JSONObject checkRoomResult(Map<String, Object> map);


    /**
     * 查询结账单明细
     * @param map
     * @return
     */
    public JSONObject getTransactionMsg(Map<String, Object> map);

    /**
     * 结账
     * @param map
     * @return
     */
    public JSONObject checkOutRegister(Map<String, Object> map);


    /**
     * 获取酒店会员信息
     * @param map
     * @return
     */
    public JSONObject getVipMsg(Map<String, Object> map);

    /**
     * 会员充值
     * @param map
     * @return
     */
    public JSONObject addVipMoney(Map<String, Object> map);


    /**
     * 验证客人是否在住
     * @param map
     * @return
     */
    public JSONObject getRegisterIsCheckIn(Map<String, Object> map);

    /**
     * 会员支付，通过会员支付消费
     * @param map
     * @return
     */
    public JSONObject vipPay(Map<String, Object> map) throws NoSuchAlgorithmException, UnsupportedEncodingException;


    /**
     * 根据身份证号查询房价码
     * @param map
     * @return
     */
    public JSONObject findPriceCodeByIdCode(Map<String, Object> map);


    /**
     * 获取续住信息
     * @param map
     * @return
     */
    public JSONObject getContinueLiveMsg(Map<String, Object> map);


    /**
     * 验证房间是否为净房
     * @param map
     * @return
     */
    public JSONObject verifyVCRoom(Map<String, Object> map);


    public JSONObject verifyGuestLiving(Map<String ,Object> map);

    /***
     * 登陆
     * @param map
     * @return
     */
    public JSONObject login(Map<String,Object> map);


    public JSONObject getSessionToken(Map<String,Object> map);

    public JSONObject getPhoneNumber(Map<String,Object> map);

    public JSONObject valPhoneNumber(Map<String,Object> map);

    public JSONObject createMember(Map<String,Object> map);

    public JSONObject getMemberInfo(Map<String,Object> map);

    public JSONObject getMemberCardType(Map<String,Object> map);

    public JSONObject memberPay(Map<String , Object> map);

    public JSONObject getMemberPayResult(Map<String ,Object> map);

    /**
     * 新增  查询之前团队信息
     * @param map
     * @return
     */
    public JSONObject findRegistGourpRecord(Map<String, Object> map);

    /**
     * 新增团队支付信息
     * @param map
     * @return
     */
    public JSONObject addRegistGroupRecord(Map<String, Object> map);

    public JSONObject updateGuestInfo(Map<String,Object>  map);

    /**
     * 发起无证
     * @param postData
     * @return
     */
    public JSONObject noCardCheckin(JSONObject postData);

    public JSONObject getMemberInfo(JSONObject postData);
}
