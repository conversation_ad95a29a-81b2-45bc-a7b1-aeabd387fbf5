package com.pms.czabsnight.service.transaction.impl;


import com.pms.czabsnight.bean.*;
import com.pms.czabsnight.dao.*;
import com.pms.czabsnight.service.transaction.NightTransactionService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomRepairRecordHistory;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomRepairRecordHistoryDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.ET;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.dao.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service
@Primary
@Slf4j
public class NightTransactionServiceImpl extends BaseService implements NightTransactionService {

    // region 引用
    @Autowired
    private NightAuditRoomStatusDao roomStatusDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private NightAuditRoomTypeDao nightAuditRoomTypeDao;

    @Autowired
    private NightAuditPayDao nightAuditPayDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;


    @Autowired
    private NightAuditAutopreDao nightAuditAutopreDao;

    @Autowired
    private NightAuditResourceDao nightAuditResourceDao;

    @Autowired
    private NightAuditMemberDao nightAuditMemberDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private NightAuditOtaDao nightAuditOtaDao;

    @Autowired
    private RoomRepairRecordHistoryDao roomRepairRecordHistoryDao;

    // endregion

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void nightKeepRoomStatusTransaction(List<NightAuditRoomStatus> deleteNightAuditRoomStatuses, ArrayList<NightAuditRoomStatus> addNightRoomStatus,
                                               List<RoomInfo> updateRoomifos, List<RoomRepairRecordHistory> roomRepairRecordHistories) throws Exception {


        // 删除房间信息
        for (NightAuditRoomStatus nrs : deleteNightAuditRoomStatuses) {
            roomStatusDao.delete(nrs.getNightAuditRoomStatusId());
        }

        // 添加新的信息
        for (NightAuditRoomStatus nrs : addNightRoomStatus) {
            roomStatusDao.insert(nrs);
        }

        // 将住净改为住脏
        if (updateRoomifos.size() > 0) {
            roomInfoDao.updateRoomList(updateRoomifos);
        }
        if (roomRepairRecordHistories.size() > 0) {
            roomRepairRecordHistoryDao.addRoomRepairRecordHistoryList(roomRepairRecordHistories);
        }

    }

    /**
     * 取消订单
     *
     * @param bookingOrders
     * @param user
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public void cancelOrderService(List<BookingOrder> bookingOrders, TbUserSession user, List<RoomAuxiliaryRelation> roomAuxiliaryRelations,
                                   List<BookingOrderRoomNum> updBookRooms,List<BookingOrderRoomType> updRoomType) throws Exception {

        ArrayList<Oprecord> oprecords = new ArrayList<>();

        for (BookingOrder bookingOrder : bookingOrders) {
            Oprecord oprecord = new Oprecord(user);
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setSourceValue(bookingOrder.getOrderStatus() + "");
            oprecord.setChangedValue(BOOK.STA_YQX + "");
            oprecord.setMainId(bookingOrder.getSn());
            bookingOrder.setOrderStatus(BOOK.STA_YQX);



            //1.取消预订主单
            Integer boState = bookingOrderDao.editBookingOrder(bookingOrder);
            if (boState < 1) {
                throw new Exception("取消预订失败。bid:" + bookingOrder.getBookingOrderId());
            }

            // 修改预订房型
            BookingOrderRoomType bookingOrderRoomType = new BookingOrderRoomType();
            bookingOrderRoomType.setBookingOrderId(bookingOrder.getBookingOrderId());
            bookingOrderRoomType.setOrderState(BOOK.STA_YQX);
            bookingOrderRoomType.setUpdateCalssId(user.getClassId());
            bookingOrderRoomType.setUpdateUserId(user.getUserId());
            bookingOrderRoomType.setUpdateTime(new Date());
            bookingOrderRoomType.setUpdateUserName(user.getUserName());
         /*   Integer brtState = bookingOrderRoomTypeDao.updateForNight(bookingOrderRoomType);
            if (brtState < 1) {
                throw new Exception("取消预定房型失败。bid:" + bookingOrderRoomType.getBookingOrderId());
            }*/

            // 修改预订房间

            oprecord.setDescription("夜审中，将订单:" + bookingOrder.getSn() + " 改为取消状态");
            oprecords.add(oprecord);

         /*   BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
            bookingOrderRoomNum.setBookingOrderId(bookingOrder.getBookingOrderId());
            bookingOrderRoomNum.setOrderState(BOOK.STA_YQX);
            bookingOrderRoomNum.setUpdateUserId(user.getUserId());
            bookingOrderRoomNum.setUpdateTime(new Date());
            bookingOrderRoomNum.setRoomNumId(0);
            bookingOrderRoomNum.setRoomNum("");
            Integer integer = bookingOrderRoomNumDao.updateForNight(bookingOrderRoomNum);*/


        }

       for (BookingOrderRoomType bookingOrderRoomType:updRoomType){
           bookingOrderRoomType.setOrderState(BOOK.STA_YQX);
           bookingOrderRoomType.setUpdateCalssId(user.getClassId());
           bookingOrderRoomType.setUpdateUserId(user.getUserId());
           bookingOrderRoomType.setUpdateTime(new Date());
           bookingOrderRoomType.setUpdateUserName(user.getUserName());
           bookingOrderRoomTypeDao.editBookingOrderRoomType(bookingOrderRoomType);
       }

        if(updBookRooms.size()>0){
            HashMap<String, List> stringListHashMap = new HashMap<>();
            stringListHashMap.put("list", updBookRooms);
            bookingOrderRoomNumDao.updateBookingOrderRoomNumList(stringListHashMap);
        }

        if (roomAuxiliaryRelations.size() > 0) {
            roomAuxiliaryRelationDao.editRoomAuxiliaryRelationList(roomAuxiliaryRelations);
        }

        this.addOprecords(oprecords);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void nightAddAcountHandleTransaction(List<Account> accounts, List<BookingOrderDailyPrice> bookingOrderDailyPrices, TbUserSession user,
                                                ArrayList<Regist> regists, List<BookingOrderDailyPrice> addBookingOrderDailyPrices) throws Exception {

        if (bookingOrderDailyPrices.size() > 0) {
            bookingOrderDailyPriceDao.updatePriceList(bookingOrderDailyPrices);
        }

        ArrayList<Oprecord> oprecords = new ArrayList<>();
        Oprecord oprecord = new Oprecord();
        for (Account account : accounts) {

            Integer integer = accountDao.saveAccount(account);

            if (integer < 0) {
                throw new Exception(account.getRoomNum() + " : 生成夜审房费失败。");
            }

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription(account.getRoomNum() + " : 生成夜审房费。");
            oprecords.add(oprecord);

        }

        // 修改登记单的价格
        if (regists.size() > 0) {
            registDao.updateRegistList(regists);
        }


        if (addBookingOrderDailyPrices.size() > 0) {
            bookingOrderDailyPriceDao.addPriceList(addBookingOrderDailyPrices);
        }

        addOprecords(oprecords);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void nightAddNightRoomTypeHandleTransaction(List<NightAuditRoomType> nightAuditRoomTypeList) throws Exception {

        for (NightAuditRoomType nightAuditRoomType : nightAuditRoomTypeList) {

            nightAuditRoomTypeDao.insert(nightAuditRoomType);

        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void nightAddNightPayHandleTransaction(List<NightAuditPay> nightAuditPays) throws Exception {

        for (NightAuditPay nightAuditPay : nightAuditPays) {

            nightAuditPayDao.insert(nightAuditPay);

        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void nightOnAccountTransaction(ArrayList<Regist> upaRegists, ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations, ArrayList<RegistPerson> registPeople) throws Exception {

        // 修改预订单状态
        for (Regist regist : upaRegists) {

            regist.setState(ET.STATUS_LOSSES);

            Regist registInfo = Regist.CreateRegist(regist.getRegistId());
            registInfo.setState(ET.STATUS_LOSSES);

            Integer update = registDao.update(registInfo);

            if (update < 1) {

                throw new Exception(regist.getRoomNum() + " 改为挂账失败");

            }

            RoomInfo roomInfo = new RoomInfo();
            roomInfo.setRoomInfoId(regist.getRoomNumId());
            roomInfo.setRoomNumState(ET.ROOM_STATUS_VD);

            roomInfoDao.editRoomInfo(roomInfo);
        }

        // 删除辅助房态
        for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());
        }

        for (RegistPerson registPerson : registPeople) {

            registPerson.setRegistState(ET.STATUS_LOSSES);

            registPersonDao.update(registPerson);

        }

    }

    /**
     * 日自己流水明细
     *
     * @param nightAuditPays
     * @param nightAuditAutopres
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void nightCapitalFlow(ArrayList<NightAuditPay> nightAuditPays, ArrayList<NightAuditAutopre> nightAuditAutopres) throws Exception {

        for (NightAuditPay nightAuditPay : nightAuditPays) {
            nightAuditPayDao.insert(nightAuditPay);
        }

        for (NightAuditAutopre nightAuditAutopre : nightAuditAutopres) {
            nightAuditAutopreDao.insert(nightAuditAutopre);
        }

    }

    /**
     * 日自己流水明细
     *
     * @param nightAuditRoomTypes
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void nightAuditRoomType(List<NightAuditRoomType> nightAuditRoomTypes) throws Exception {
        int i = 0;
        for (NightAuditRoomType nightAuditRoomType : nightAuditRoomTypes) {
            log.info("i============" + i);
            nightAuditRoomTypeDao.insert(nightAuditRoomType);
            i++;
        }

    }

    /**
     * 客源类型统计
     *
     * @param nightAuditResources
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void nightAuditResource(List<NightAuditResource> nightAuditResources) throws Exception {

        for (NightAuditResource nightAuditResource : nightAuditResources) {

            nightAuditResourceDao.insert(nightAuditResource);

        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void nightAuditMember(List<NightAuditMember> nightAuditMembers) throws Exception {
        for (NightAuditMember nightAuditMember : nightAuditMembers) {
            nightAuditMemberDao.insert(nightAuditMember);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void nightOtaData(List<NightAuditOta> nightAuditOtas) throws Exception {
        nightAuditOtaDao.addNightAuditOtaList(nightAuditOtas);
    }
}
