package com.pms.czabsnight.jobs;


import com.github.pagehelper.Page;
import com.pms.czabsnight.bean.NightAuditRecord;
import com.pms.czabsnight.bean.NightAutoSetting;
import com.pms.czabsnight.bean.search.NightAuditRecordSearch;
import com.pms.czabsnight.bean.search.NightAutoSettingSearch;
import com.pms.czabsnight.dao.NightAuditRecordDao;
import com.pms.czabsnight.dao.NightAutoSettingDao;
import com.pms.czabsnight.listener.SpringUtil;
import com.pms.czabsnight.service.impl.NightStepServiceImpl;
import com.pms.czhotelfoundation.bean.hotel.HotelBaseInfo;
import com.pms.czhotelfoundation.bean.hotel.search.HotelBaseInfoSearch;
import com.pms.czhotelfoundation.dao.hotel.HotelBaseInfoDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 定时查询夜审设置
 */
@Service
@Slf4j
public class AutoNightJob extends BaseService implements ApplicationRunner, Job {

    private static final Logger logger = LoggerFactory.getLogger(AutoNightJob.class);


    //防止重复 超时设置为3天
    public final static ScheduledExecutorService auditNightExecPool = new ScheduledThreadPoolExecutor(16,
            new BasicThreadFactory.Builder().namingPattern("AutoNightJob-%d").daemon(true).build());


    public static SimpleDateFormat getSdf() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm");
    }

    private static Map<Integer, HotelBaseInfo> hotelBaseInfoMap = new ConcurrentHashMap<>();
    private static AtomicInteger runTimes = new AtomicInteger(0);


    /**
     * 获取酒店最后一次夜审记录
     *
     * @param hid
     * @return
     */
    private static NightAuditRecord getLastNightAuditRecord(Integer hid) {
        NightAuditRecordDao nightAuditRecordDao = SpringUtil.getBean(NightAuditRecordDao.class);
        NightAuditRecordSearch nightAuditRecordSearch = new NightAuditRecordSearch();
        nightAuditRecordSearch.setHid(hid);
        nightAuditRecordSearch.setPageNum(1);
        nightAuditRecordSearch.setPageSize(1);
        Page<NightAuditRecord> nightAuditRecords = nightAuditRecordDao.selectBySearch(nightAuditRecordSearch);
        if (nightAuditRecords == null || nightAuditRecords.size() == 0)
            return null;
        else
            return nightAuditRecords.get(0);
    }


    //转换函数，可以封装成公用方法
    public static String timeDiffStr(Date date1, Date date2) {
        long ms = Math.abs(date1.getTime() - date2.getTime());
        int ss = 1000;
        int mi = ss * 60;
        Integer hh = mi * 60;
        Integer dd = hh * 24;

        Long day = ms / dd;
        Long hour = (ms - day * dd) / hh;
        Long minute = (ms - day * dd - hour * hh) / mi;
        Long second = (ms - day * dd - hour * hh - minute * mi) / ss;
        Long milliSecond = ms - day * dd - hour * hh - minute * mi - second * ss;

        StringBuilder sb = new StringBuilder();
        if (day > 0) {
            sb.append(day).append("天");
        }
        if (hour > 0) {
            sb.append(hour).append("小时");
        }
        if (minute > 0) {
            sb.append(minute).append("分");
        }
        if (second > 0) {
            sb.append(second).append("秒");
        }
        if (milliSecond > 0) {
            sb.append(milliSecond).append("毫秒");
        }
        return sb.toString();
    }

    public final long minute = 1000 * 60;
    public final long hour = minute * 60;

    //3分钟提醒
        /*
        String notifyBefore3Min = getSdf().format(new Date(autoAuditTime.getTime() - 3 * 60 * 1000));
        String invokerName = notifyBefore3Min + "_notifyBefore3Min_" + hid;
        AutoNightJob.GetExeCacheList(notifyBefore3Min).putIfAbsent(invokerName, new JobInvokerOnceAbstract(invokerName) {
            @Override
            public void invokerImpl() {
                HashMap<String, String> filedMap = new HashMap<>();
                filedMap.put("min", "3");
                NightStepServiceImpl nightStepService = SpringUtil.getBean(NightStepServiceImpl.class);
                HashMap<String, String> regMap = new HashMap<>();
                nightStepService.push(hid, hid, 19, filedMap, regMap, true, true);
            }
        });
         */
    //1分钟提醒
        /*
        String notifyBefore1Min = getSdf().format(new Date(autoAuditTime.getTime() - 60 * 1000));
        invokerName = notifyBefore1Min + "_notifyBefore1Min_" + hid;
        HashMap<String, JobInvoker> hashMap = AutoNightJob.GetExeCacheList(notifyBefore1Min);
        hashMap.putIfAbsent(invokerName, new JobInvokerOnceAbstract(invokerName) {
            @Override
            public void invokerImpl() {
                HashMap<String, String> filedMap = new HashMap<>();
                filedMap.put("min", "1");
                NightStepServiceImpl nightStepService = SpringUtil.getBean(NightStepServiceImpl.class);
                HashMap<String, String> regMap = new HashMap<>();
                nightStepService.push(hid, hid, 19, filedMap, regMap, true, true);
            }
        });
         */


    private void doNightAudi(NightAutoSetting nightAutoSetting) {
        Date now = new Date();
        int hid = nightAutoSetting.getHid();
        String hotelStr = getHotelStr(hid);

        Date autoAuditTime = HotelUtils.parseStr2Date(HotelUtils.currentDate() + " " + nightAutoSetting.getAutoTime() + ":00");
        if (autoAuditTime == null) {
            logger.error("酒店:{}, 自动夜审配置出错,自动夜审配置时间:{}", hotelStr, nightAutoSetting.getAutoTime());
            return;
        }

        //如果还没有到夜审时间, 跳过,不执行夜审
        // todo 对于 23:50 的夜审时间也30次机会夜审
        if (now.getTime() < autoAuditTime.getTime()) {
            logger.info("还没有到夜审时间跳过夜审,酒店:{},自动夜审配置时间:{}", hotelStr, nightAutoSetting.getAutoTime());
            return;
        }

        // 超过夜审时间30分钟跳过
        if (now.getTime() - autoAuditTime.getTime() > 30 * minute) {
            logger.info("超过夜审时间30分钟,酒店:{},自动夜审配置时间:{}", hotelStr, nightAutoSetting.getAutoTime());
            return;
        }

        NightAuditRecord lastNightAuditRecord = getLastNightAuditRecord(hid);
        if (lastNightAuditRecord != null) {
            Date lastAuditEnd = lastNightAuditRecord.getAuditStart();
            // 两次夜审间隔小于14小时不自动夜审
            if (now.getTime() - lastAuditEnd.getTime() < 14 * hour) {
                logger.warn("两次夜审间隔小于14小时不自动夜审,酒店:{},上次夜审时间:{},夜审间隔:{}", hotelStr, getSdf().format(lastAuditEnd), timeDiffStr(now, lastAuditEnd));
                return;
            }
        }

        String finalHotelStr = hotelStr;
        auditNightExecPool.execute(() -> {
            //执行
            logger.info("添加到夜审执行队列, 酒店:{}", finalHotelStr);
            executeNight(hid);
        });
    }

    private String getHotelStr(Integer hid) {
        HotelBaseInfo hotelBaseInfo = hotelBaseInfoMap.get(hid);
        String hotelStr = "[hid:" + hid;
        if (hotelBaseInfo != null) {
            hotelStr += ", hotelName:" + hotelBaseInfo.getHotelName();
        }
        hotelStr += "]";
        return hotelStr;
    }

    public void run(ApplicationArguments args) throws Exception {
        new Thread(() -> {
            while (true) {
                int times = runTimes.incrementAndGet();
                //60 分钟查询一次酒店信息
                if ((times - 1) % 60 == 0) {
                    HotelBaseInfoDao baseInfoDao = SpringUtil.getBean(HotelBaseInfoDao.class);
                    Page<HotelBaseInfo> hotelBaseInfos = baseInfoDao.selectBySearch(new HotelBaseInfoSearch());
                    for (HotelBaseInfo info : hotelBaseInfos) {
                        hotelBaseInfoMap.put(info.getHid(), info);
                    }
                }

                // 查询所有的自动夜审的酒店
                NightAutoSettingDao nightAutoSettingDao = SpringUtil.getBean(NightAutoSettingDao.class);
                NightAutoSettingSearch nightAutoSettingSearch = new NightAutoSettingSearch();
                nightAutoSettingSearch.setAutoNight(1);
                Page<NightAutoSetting> nightAutoSettings = nightAutoSettingDao.selectBySearch(nightAutoSettingSearch);

                for (NightAutoSetting nightAutoSetting : nightAutoSettings) {
                    int hid = nightAutoSetting.getHid();
                    String hotelStr = getHotelStr(hid);

                    //加try catch 防止循环中出现异常导致循环后续不执行
                    try {
                        log.info("夜审执行中....");
                        doNightAudi(nightAutoSetting);
                    } catch (Exception e) {
                        e.printStackTrace();
                        logger.error("执行夜审错误, 酒店:{}", hotelStr);
                    }
                }


                // 一分钟扫表一次
                try {
                    Thread.sleep(minute);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                    log.error("",e);
                }
            }

        }).start();
    }

    @Override
    public void execute(JobExecutionContext jobContext) throws JobExecutionException {
        logger.info("execute");
    }

    /**
     * 夜审方法
     *
     * @param hid
     */
    private void executeNight(Integer hid) {
        String hotelStr = getHotelStr(hid);
        JSONObject param = new JSONObject();
        String session = "autonight" + hid;
        param.put(ER.SESSION_TOKEN, session);
        NightStepServiceImpl nightStepService = SpringUtil.getBean(NightStepServiceImpl.class);
        String sessionToken = param.getString(ER.SESSION_TOKEN);


        try {
            Date auditStart = new Date();
            final TbUserSession user = nightStepService.getTbUserSession(sessionToken);
            String key = "audit:" + user.getHid() + ":" + user.getBusinessDay();
            String nowStr = getSdf().format(new Date());
            if (Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(key, nowStr, 3, TimeUnit.DAYS))) {
                logger.info("开始自动夜审, 酒店:{},businessDay:{}, 夜审开始时间:{},", hotelStr, user.getBusinessDay(), nowStr);
            } else {
                logger.info("已自动夜审过,跳过,酒店:{},businessDay:{},夜审时间:{},", hotelStr, user.getBusinessDay(), stringRedisTemplate.opsForValue().get(key));
                return;
            }

            Date start = new Date();
            // 创建夜审状态
            ResponseData nightRecordRT = nightStepService.createNightRecord(param);
            logger.info("自动夜审:createNightRecord完成,耗时:{},结果:{} 酒店:{},businessDay:{},夜审开始时间:{},",
                    timeDiffStr(new Date(), start), nightRecordRT.Result(), hotelStr, user.getBusinessDay(), nowStr);

            start = new Date();
            // 保留当晚房态
            ResponseData keepRoomStatusRT = nightStepService.keepRoomStatus(param);
            logger.info("自动夜审:keepRoomStatus完成,耗时:{},结果:{} 酒店:{},businessDay:{},夜审开始时间:{},",
                    timeDiffStr(new Date(), start), keepRoomStatusRT.Result(), hotelStr, user.getBusinessDay(), nowStr);

            start = new Date();
            // 取消当晚全部noShow房间
            ResponseData noShowBookRT = nightStepService.noShowBook(param);
            logger.info("自动夜审:noShowBook完成,耗时:{},结果:{} 酒店:{},businessDay:{},夜审开始时间:{},",
                    timeDiffStr(new Date(), start), noShowBookRT.Result(), hotelStr, user.getBusinessDay(), nowStr);
            // 维修房延期处理
            nightStepService.repairRoomAddDate(param);
            // 夜审挂账处理
            nightStepService.nightOnAccount(param);
            // 夜审钟点房挂账处理
            nightStepService.nightHourOnAccount(param);
            // 夜审房租及入账
            nightStepService.addNightAccount(param);
            // 统计当前房型数据
            nightStepService.statisticsRoomAccountData(param);
            // 统计当前房间售卖详情
            nightStepService.statisticsGoodsData(param);
            // 统计当前支付信息
            nightStepService.statisticsPayMsg(param);
            // 统计房型数据
            nightStepService.statisticsRoomTypeData(param);
            // 统计客源数据
            nightStepService.statisticsResouceData(param);
            // 会员注册数
            nightStepService.registerVipNum(param);
            // 协议单位统计
            nightStepService.statisticsArData(param);
            // 夜审完成时
            nightStepService.finishNightStep(param);
            logger.info("自动夜审完成,酒店:{},耗时:{},businessDay:{}, 夜审开始时间:{},",
                    hotelStr, timeDiffStr(auditStart, new Date()), user.getBusinessDay(), nowStr);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("",e);
        }
    }


}
