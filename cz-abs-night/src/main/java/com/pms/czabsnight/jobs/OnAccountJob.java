package com.pms.czabsnight.jobs;

import com.pms.czabsnight.service.BaseJob;
import com.pms.czpmsutils.HttpRequest;
import com.pms.czpmsutils.constant.user.BaseService;
import net.sf.json.JSONObject;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OnAccountJob extends BaseService implements BaseJob {

    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        log.info("进入挂账执行接口任务----[{}]",JSONObject.fromObject(mergedJobDataMap));
        int registId = mergedJobDataMap.getInt("registId");
        JSONObject postData = new JSONObject();
        postData.put("registId", registId);
        postData.put("sessionToken", mergedJobDataMap.getString("sessionToken"));
        Object reason = mergedJobDataMap.get("reason");
        if(reason==null){
            reason = "定时挂账业务处理";
        }
        postData.put("reason",reason);
        try {
            String url = "https://czpms.cn/hotel/checkout/onAccount.do";
            postData.put("registId", registId);
            postData.put("sessionToken", mergedJobDataMap.getString("sessionToken"));
            postData.put("reason", reason);
            String result = HttpRequest.sendPostPms(url, postData ,mergedJobDataMap.getString("sessionToken"));
            log.info("进入挂账执行返回----[{}]",result);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("",e);
        }
    }
}
