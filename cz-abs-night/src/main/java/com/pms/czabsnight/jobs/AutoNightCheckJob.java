package com.pms.czabsnight.jobs;


import com.github.pagehelper.Page;
import com.pms.czabsnight.bean.NightAuditRecord;
import com.pms.czabsnight.bean.NightAutoSetting;
import com.pms.czabsnight.bean.search.NightAuditRecordSearch;
import com.pms.czabsnight.bean.search.NightAutoSettingSearch;
import com.pms.czabsnight.dao.NightAuditRecordDao;
import com.pms.czabsnight.dao.NightAutoSettingDao;
import com.pms.czabsnight.listener.SpringUtil;
import com.pms.czabsnight.service.BaseJob;
import com.pms.czabsnight.service.impl.NightStepServiceImpl;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.user.BaseService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 检测 没有夜审的酒店 重新夜审
 */
@Service
@Slf4j
public class AutoNightCheckJob extends BaseService implements BaseJob {

    private static final Logger LOGGER = LoggerFactory.getLogger(AutoNightCheckJob.class);


    public static SimpleDateFormat getSdf() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm");
    }

    @Override
    public void execute(JobExecutionContext jobContext) throws JobExecutionException {
        NightAutoSettingDao nightAutoSettingDao = SpringUtil.getBean(NightAutoSettingDao.class);
        NightAuditRecordDao nightAuditRecordDao = SpringUtil.getBean(NightAuditRecordDao.class);
        NightAutoSettingSearch nightAutoSettingSearch = new NightAutoSettingSearch();
        nightAutoSettingSearch.setAutoNight(1);
        Page<NightAutoSetting> nightAutoSettings = nightAutoSettingDao.selectBySearch(nightAutoSettingSearch);
        Date date = new Date();

        for (NightAutoSetting nightAutoSetting : nightAutoSettings) {
            Integer hid = nightAutoSetting.getHid();
            NightAuditRecordSearch nightAuditRecordSearch = new NightAuditRecordSearch();
            nightAuditRecordSearch.setHid(hid);
            nightAuditRecordSearch.setPageNum(1);
            nightAuditRecordSearch.setPageSize(1);
            Page<NightAuditRecord> nightAuditRecords = nightAuditRecordDao.selectBySearch(nightAuditRecordSearch);
            if (nightAuditRecords != null && nightAuditRecords.size() > 0) {
                NightAuditRecord nightAuditRecord = nightAuditRecords.get(0);

                Date auditEnd = nightAuditRecord.getAuditEnd();

                if (auditEnd == null) {
                    nightAuditRecord.getAuditStart();
                }

                long l = date.getTime() - auditEnd.getTime();

                log.info("夜审间隔----" + hid + ":----------------------------" + l);

                // 两次夜审间隔小于14小时不自动夜审
                if (l < 50400000) {
                    continue;
                }

                String autoTime = nightAutoSetting.getAutoTime();

                // 夜审时间
                Date nightDate = HotelUtils.parseStr2Date(HotelUtils.currentDate() + " " + autoTime + ":00");

                String[] split = autoTime.split(":");
                int hour = Integer.parseInt(split[0]);

                long diffs = date.getTime() - nightDate.getTime()  ;
                // 如果夜审时间大于20点，则验证的时候间隔加一天
                if(hour>20){
                    diffs+=86400000;
                }

                log.info("超时未夜审检测++++++++++++++++++++++++++++++"+diffs);

                if(diffs>613456){

                    JSONObject param = new JSONObject();
                    String session = "autonight" + hid;
                    param.put(ER.SESSION_TOKEN, session);

                   // executeNight(param);

                }

            }
        }
    }


    /**
     * 夜审方法
     *
     * @param param
     */
    private void executeNight(JSONObject param) {
        NightStepServiceImpl nightStepService = SpringUtil.getBean(NightStepServiceImpl.class);
        // 创建夜审状态
        nightStepService.createNightRecord(param);
        // 保留当晚房态
        nightStepService.keepRoomStatus(param);
        // 取消当晚全部noShow房间
        nightStepService.noShowBook(param);
        // 维修房延期处理
        nightStepService.repairRoomAddDate(param);
        // 夜审挂账处理
        nightStepService.nightOnAccount(param);
        // 夜审钟点房挂账处理
        nightStepService.nightHourOnAccount(param);
        //      * 夜审房租及入账
        nightStepService.addNightAccount(param);
        // 统计当前房型数据
        nightStepService.statisticsRoomAccountData(param);
        // 统计当前房间售卖详情
        nightStepService.statisticsGoodsData(param);
        // 统计当前支付信息
        nightStepService.statisticsPayMsg(param);
        // 统计房型数据
        nightStepService.statisticsRoomTypeData(param);
        // 统计客源数据
        nightStepService.statisticsResouceData(param);
        // 会员注册数
        nightStepService.registerVipNum(param);
        // 协议单位统计
        nightStepService.statisticsArData(param);
        // 夜审完成时
        nightStepService.finishNightStep(param);
    }
}
