package com.pms.czabsnight.jobs;

import com.pms.czabsnight.listener.SpringUtil;
import com.pms.czabsnight.service.BaseJob;
import com.pms.czabsnight.service.order.PmsOrderService;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.dao.RegistDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class HourRoomExpReminderJob extends BaseService implements BaseJob {
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        log.info("进入钟点房结束提醒定时任务:");
        log.info(mergedJobDataMap.toString());
        RegistDao registDao = SpringUtil.getBean(RegistDao.class);
        PmsOrderService pmsOrderService = SpringUtil.getBean(PmsOrderService.class);
        int registId = mergedJobDataMap.getInt("registId");
        Regist regist = registDao.selectById(registId);
        /**
         * 如果状态不是未结，则不处理
         */
        if (null == regist || regist.getState() != 0) {
            return;
        }
        pmsOrderService.hourRoomPushMessage(regist);
    }
}
