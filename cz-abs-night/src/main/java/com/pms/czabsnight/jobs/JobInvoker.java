package com.pms.czabsnight.jobs;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;

public interface JobInvoker {
    boolean hasInvoker();

    void invoker();
}

abstract class JobInvokerOnceAbstract implements JobInvoker {
    private static final Logger LOGGER = LoggerFactory.getLogger(JobInvokerOnceAbstract.class);

    /**
     * 线程池
     * 最大等带条数 256
     */
    public static ScheduledExecutorService autoNightCachedThreadPool = new ScheduledThreadPoolExecutor(256,
            new BasicThreadFactory.Builder().namingPattern("example-schedule-pool-%d").daemon(true).build());
    private boolean hasInvoker = false;
    private String name;

    public JobInvokerOnceAbstract(String name) {
        this.name = name;
    }

    @Override
    public boolean hasInvoker() {
        return hasInvoker;
    }

    @Override
    public void invoker() {
        synchronized (this) {
            if (hasInvoker()) {
                return;
            }
            hasInvoker = true;
        }
        LOGGER.info("execute invoker: {}", name);
        autoNightCachedThreadPool.execute(this::invokerImpl);
    }
    
    protected abstract void invokerImpl();
}
