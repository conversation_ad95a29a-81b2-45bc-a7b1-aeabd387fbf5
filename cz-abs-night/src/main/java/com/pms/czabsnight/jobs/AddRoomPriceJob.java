package com.pms.czabsnight.jobs;

import com.pms.czabsnight.listener.SpringUtil;
import com.pms.czabsnight.service.BaseJob;
import com.pms.czabsnight.service.impl.NightReportServiceImpl;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.regist.CHECK_IN_TYPE;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.search.BookingOrderDailyPriceSearch;
import com.pms.pmsorder.bean.search.RegistPersonSearch;
import com.pms.pmsorder.dao.BookingOrderDailyPriceDao;
import com.pms.pmsorder.dao.RegistDao;
import com.pms.pmsorder.dao.RegistPersonDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;


/**
 * 开房自动产生房费的任务
 */
@Service
@Slf4j
public class AddRoomPriceJob  extends BaseService  implements BaseJob {





    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {

        RegistPersonDao registPersonDao = SpringUtil.getBean(RegistPersonDao.class);
        BookingOrderDailyPriceDao bookingOrderDailyPriceDao = SpringUtil.getBean(BookingOrderDailyPriceDao.class);
        RegistDao registDao = SpringUtil.getBean(RegistDao.class);
        AccountDao accountDao = SpringUtil.getBean(AccountDao.class);

        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();

        log.info("进入加收房费定时任务");

        log.info(mergedJobDataMap.toString());

        String registIds = mergedJobDataMap.getString("registIds");
        String[] split = registIds.split(",");

        TbUserSession user = new TbUserSession();

        for(int i = 0 ;i<split.length;i++){

            // 查询登记单信息
            int registId = Integer.parseInt(split[i]);

            Regist regist = registDao.selectById(registId);

            if(regist==null||regist.getState()!=0){
                return;
            }

            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(registId);
            registPersonSearch.setRegistState(0);

            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
            RegistPerson registPerson = registPeople.get(0);

            // 查询当天的房价是否产生
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(registPerson.getHid());
            bookingOrderDailyPriceSearch.setRegistId(registId);
            bookingOrderDailyPriceSearch.setDailyTime(regist.getBusinessDay());
            bookingOrderDailyPriceSearch.setDailyState(1);

            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            if (bookingOrderDailyPrices == null || bookingOrderDailyPrices.size() < 1) {
                return;
            }

            BookingOrderDailyPrice bookingOrderDailyPrice = bookingOrderDailyPrices.get(0);

            user.setHotelGroupId(user.getHotelGroupId());
            user.setHid(regist.getHid());
            user.setUserId(regist.getCreateUserId());
            user.setBusinessDay(regist.getBusinessDay());

            String accountId = UUID.randomUUID().toString();
            // 1.修改价格信息改为已结
            bookingOrderDailyPrice.setDailyState(0);
            bookingOrderDailyPriceDao.editBookingOrderDailyPrice(bookingOrderDailyPrice);
            // 2.添加账务信息
            Account account = new Account();
            account.setAccountId(accountId);
            account.setHid(regist.getHid());
            account.setHotelGroupId(regist.getHotelGroupId());
            account.setCreateUserId(regist.getCreateUserId());
            account.setCreateUserName(regist.getCreateUserName());
            account.setCreateTime(new Date());
            account.setIsCancel(0);
            account.setAccountYear(regist.getRegistYear());
            account.setAccountYearMonth(regist.getRegistYearMonth());
            account.setBusinessDay(regist.getBusinessDay());
            account.setClassId(regist.getClassId());
            account.setRegistId(regist.getRegistId());
            account.setBookingId(regist.getBookingOrderId());
            account.setTeamCodeId(regist.getTeamCodeId());
            account.setPrice(bookingOrderDailyPrice.getPrice());
            account.setPayType(1);

            account.setPayClassId(10);
            account.setPayClassName("客房");
            account.setPayCodeId("0002");
            account.setPayCodeName("全天房费");
            if (regist.getCheckinType() == CHECK_IN_TYPE.CIT_HOURDAY) {
                account.setPayCodeId("0007");
                account.setPayCodeName("钟点房费");
            }

            account.setRoomInfoId(regist.getRoomNumId());
            account.setRoomTypeId(regist.getRoomTypeId());
            account.setRoomNum(regist.getRoomNum());
            account.setIsSale(1);

            account.setUintPrice(bookingOrderDailyPrice.getPrice());
            account.setSaleNum(1);
            account.setRegistState(0);
            account.setRemark("开房自动产生房费");
            account.setSettleAccountTime(new Date());
            account.setRefundPrice(0);
            account.setThirdRefundState(0);
            account.setAccountType(1);

            account.setThirdAccoutId(bookingOrderDailyPrice.getId() + "");

            account.setRegistPersonId(registPerson.getRegistPersonId());
            account.setRegistPersonName(registPerson.getPersonName());
            account.setGroupAccount(0);
            account.setBegRegistId(registId);
            account.setBegRegistPersonId(registPerson.getRegistPersonId());

            Integer integer = 0;
            try {
                integer = accountDao.saveAccount(account);
            } catch (Exception e) {
                String uuid = HotelUtils.getUUID();
                account.setAccountId(uuid);
                integer = accountDao.saveAccount(account);
                log.error("",e);
            }

        }
        NightReportServiceImpl nightReportService = SpringUtil.getBean(NightReportServiceImpl.class);

        nightReportService.pushRoomState(user);
    }


}
