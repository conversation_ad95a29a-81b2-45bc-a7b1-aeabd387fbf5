package com.pms.czabsnight.jobs;

import com.pms.czabsnight.service.BaseJob;
import com.pms.czpmsutils.HttpRequest;
import com.pms.czpmsutils.constant.user.BaseService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AddCancleBookingOrderJob extends BaseService implements BaseJob {
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap mergedJobDataMap = context.getMergedJobDataMap();
        log.info("进入取消预订单定时任务");
        log.info(mergedJobDataMap.toString());
        int bokingOrderId = mergedJobDataMap.getInt("bokingOrderId");
        JSONObject postData = new JSONObject();
        postData.put("bookingOrderId", bokingOrderId);
        postData.put("sessionToken", mergedJobDataMap.getString("sessionToken"));
        postData.put("reason", mergedJobDataMap.getString("reason"));
        log.info(postData.toString());
        try {
            String url = "https://czpms.cn/hotel/absorder/cancelBook.do";
            postData.put("bookingOrderId", bokingOrderId);
            postData.put("sessionToken", mergedJobDataMap.getString("sessionToken"));
            postData.put("reason", mergedJobDataMap.getString("reason"));
            String result = HttpRequest.sendPostPms(url, postData ,mergedJobDataMap.getString("sessionToken"));
            log.info(result);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
