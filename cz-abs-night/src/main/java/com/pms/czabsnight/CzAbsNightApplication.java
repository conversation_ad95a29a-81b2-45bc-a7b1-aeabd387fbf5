package com.pms.czabsnight;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication
@MapperScan({"com.pms.*","com.pms.*.*","com.pms.*.*.*"})
@ComponentScan({"com.pms.*","com.pms.*.*","com.pms.*.*.*"})
@EnableDiscoveryClient
@Configuration
public class CzAbsNightApplication {

	public static void main(String[] args) {
		SpringApplication.run(CzAbsNightApplication.class, args);
	}

	@LoadBalanced
	@Bean
	RestTemplate restTemplate() {
		return new RestTemplate();
	}

}
