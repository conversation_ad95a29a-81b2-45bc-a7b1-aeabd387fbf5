package com.pms.czabsnight.bean.search;

import com.pms.czpmsutils.request.PageBaseRequest;
import org.apache.ibatis.type.Alias;

@<PERSON>as("NightAuditRecordSearch")
public class NightAuditRecordSearch extends PageBaseRequest {
	private Integer nightAuditRecordId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer state;
	private java.util.Date auditStart;
	private java.util.Date auditEnd;
	private Integer classId;
	private Integer businessDay;
	private java.util.Date createTime;
	private String createUserId;
	private String message;

	public Long getBusinessDayStart() {
		return businessDayStart;
	}

	public void setBusinessDayStart(Long businessDayStart) {
		this.businessDayStart = businessDayStart;
	}

	public Long getBusinessDayEnd() {
		return businessDayEnd;
	}

	public void setBusinessDayEnd(Long businessDayEnd) {
		this.businessDayEnd = businessDayEnd;
	}

	private Long businessDayStart;
	private Long businessDayEnd;

	public void setNightAuditRecordId(Integer value) {
		this.nightAuditRecordId = value;
	}
	
	public Integer getNightAuditRecordId() {
		return this.nightAuditRecordId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}

	public void setAuditStart(java.util.Date value) {
		this.auditStart = value;
	}
	
	public java.util.Date getAuditStart() {
		return this.auditStart;
	}

	public void setAuditEnd(java.util.Date value) {
		this.auditEnd = value;
	}
	
	public java.util.Date getAuditEnd() {
		return this.auditEnd;
	}
	public void setClassId(Integer value) {
		this.classId = value;
	}
	
	public Integer getClassId() {
		return this.classId;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setMessage(String value) {
		this.message = value;
	}
	
	public String getMessage() {
		return this.message;
	}

	public Integer getHotelGroupId() {
		return hotelGroupId;
	}

	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}
}

