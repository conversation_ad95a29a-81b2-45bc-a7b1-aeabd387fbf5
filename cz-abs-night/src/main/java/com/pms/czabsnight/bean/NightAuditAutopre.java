package com.pms.czabsnight.bean;

import java.io.Serializable;

public class NightAuditAutopre implements Serializable{
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer money  ;
	private Integer finishMoney  ;
	private Integer type  ;
	private Integer businessDay  ;
	private Integer auditYear  ;
	private Integer auditYearMonth  ;

	public NightAuditAutopre(){
	}

	public NightAuditAutopre(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setMoney(Integer money) {
		this.money = money;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setFinishMoney(Integer finishMoney) {
		this.finishMoney = finishMoney;
	}
	
	public Integer getFinishMoney() {
		return this.finishMoney;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	
	public Integer getType() {
		return this.type;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer auditYear) {
		this.auditYear = auditYear;
	}
	
	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer auditYearMonth) {
		this.auditYearMonth = auditYearMonth;
	}
	
	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

}

