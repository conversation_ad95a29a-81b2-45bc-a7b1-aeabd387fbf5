package com.pms.czabsnight.bean;

import com.pms.czpmsutils.request.BaseRequest;

public class NightAuditSettingRequest  extends BaseRequest {
    private Integer id;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public Integer getHotelGroupId() {
        return hotelGroupId;
    }

    public void setHotelGroupId(Integer hotelGroupId) {
        this.hotelGroupId = hotelGroupId;
    }

    public Integer getAutoNight() {
        return autoNight;
    }

    public void setAutoNight(Integer autoNight) {
        this.autoNight = autoNight;
    }

    public String getAutoTime() {
        return autoTime;
    }

    public void setAutoTime(String autoTime) {
        this.autoTime = autoTime;
    }

    public Integer getAntoNightCallTime() {
        return antoNightCallTime;
    }

    public void setAntoNightCallTime(Integer antoNightCallTime) {
        this.antoNightCallTime = antoNightCallTime;
    }

    public Integer getNotArrivedNoPayExecute() {
        return notArrivedNoPayExecute;
    }

    public void setNotArrivedNoPayExecute(Integer notArrivedNoPayExecute) {
        this.notArrivedNoPayExecute = notArrivedNoPayExecute;
    }

    public Integer getRegistPersonExecute() {
        return registPersonExecute;
    }

    public void setRegistPersonExecute(Integer registPersonExecute) {
        this.registPersonExecute = registPersonExecute;
    }

    public Integer getNotArrivedPayExecute() {
        return notArrivedPayExecute;
    }

    public void setNotArrivedPayExecute(Integer notArrivedPayExecute) {
        this.notArrivedPayExecute = notArrivedPayExecute;
    }

    public Integer getNotLeaveExecute() {
        return notLeaveExecute;
    }

    public void setNotLeaveExecute(Integer notLeaveExecute) {
        this.notLeaveExecute = notLeaveExecute;
    }

    public Integer getHourRoomExecute() {
        return hourRoomExecute;
    }

    public void setHourRoomExecute(Integer hourRoomExecute) {
        this.hourRoomExecute = hourRoomExecute;
    }

    public Integer getOoRoomExecute() {
        return ooRoomExecute;
    }

    public void setOoRoomExecute(Integer ooRoomExecute) {
        this.ooRoomExecute = ooRoomExecute;
    }

    public Integer getRoomAddAccount() {
        return roomAddAccount;
    }

    public void setRoomAddAccount(Integer roomAddAccount) {
        this.roomAddAccount = roomAddAccount;
    }

    //
    private Integer hotelGroupId;
    //0.非自动夜审 1.是自动夜审
    private Integer autoNight;
    //夜审时间 HH:mm
    private String autoTime;
    //夜审提醒时间 夜审前 1-10分钟
    private Integer antoNightCallTime;
    //应到未到现付单处理 1-NOSHOW  2-取消
    private Integer notArrivedNoPayExecute;
    //住客非会员业务处理 1-注册会员 2-不注册
    private Integer registPersonExecute;
    //应到未到预付单处理 1-房间自动取消 2-空入住
    private Integer notArrivedPayExecute;
    //应离未离入住单处理 1-挂账 2-续住 （保留房态信息）
    private Integer notLeaveExecute;
    //钟点房处理 1-挂账 2-转日租，并冲掉钟点房费，录入日租房费
    private Integer hourRoomExecute;
    //酒店到期维修房处理 1-完成维修  2-自动延期一天
    private Integer ooRoomExecute;
    //在住房间入账 1-入账  2- ？？？
    private Integer roomAddAccount;
}
