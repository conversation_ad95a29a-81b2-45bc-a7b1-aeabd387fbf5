package com.pms.czabsnight.bean.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class NigthAuditArPaymentSearch extends PageBaseRequest{
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer money;
	private Integer payClassId;
	private String payClassName;
	private Integer businessDay;
	private Integer auditYear;
	private Integer auditYearMonth;

	private Integer businessDayMin;
	private Integer businessDayMax;

	public void setId(Integer value) {
		this.id = value;
	}

	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setMoney(Integer value) {
		this.money = value;
	}

	public Integer getMoney() {
		return this.money;
	}
	public void setPayClassId(Integer value) {
		this.payClassId = value;
	}

	public Integer getPayClassId() {
		return this.payClassId;
	}
	public void setPayClassName(String value) {
		this.payClassName = value;
	}

	public String getPayClassName() {
		return this.payClassName;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer value) {
		this.auditYear = value;
	}

	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer value) {
		this.auditYearMonth = value;
	}

	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

	public Integer getBusinessDayMin() {
		return businessDayMin;
	}

	public void setBusinessDayMin(Integer businessDayMin) {
		this.businessDayMin = businessDayMin;
	}

	public Integer getBusinessDayMax() {
		return businessDayMax;
	}

	public void setBusinessDayMax(Integer businessDayMax) {
		this.businessDayMax = businessDayMax;
	}
}

