package com.pms.czabsnight.bean;

import java.io.Serializable;

/**
 * 
 */
public class NightAutoSetting implements Serializable{
	//
	private Integer id;
	//
	private Integer hid;
	//
	private Integer hotelGroupId;
	//0.非自动夜审 1.是自动夜审
	private Integer autoNight;
	//夜审时间 HH:mm
	private String autoTime;
	//夜审提醒时间 夜审前 1-10分钟
	private Integer antoNightCallTime;
	//应到未到现付单处理 1-NOSHOW  2-取消
	private Integer notArrivedNoPayExecute;
	//住客非会员业务处理 1-注册会员 2-不注册
	private Integer registPersonExecute;
	//应到未到预付单处理 1-房间自动取消 2-空入住
	private Integer notArrivedPayExecute;
	//应离未离入住单处理 1-挂账 2-续住 （保留房态信息）
	private Integer notLeaveExecute;
	//钟点房处理 1-挂账 2-转日租，并冲掉钟点房费，录入日租房费
	private Integer hourRoomExecute;
	//酒店到期维修房处理 1-完成维修  2-自动延期一天
	private Integer ooRoomExecute;
	//在住房间入账 1-入账  2- ？？？
	private Integer roomAddAccount;


	public NightAutoSetting(){
	}

	public NightAutoSetting(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setAutoNight(Integer autoNight) {
		this.autoNight = autoNight;
	}
	
	public Integer getAutoNight() {
		return this.autoNight;
	}
	public void setAutoTime(String autoTime) {
		this.autoTime = autoTime;
	}
	
	public String getAutoTime() {
		return this.autoTime;
	}
	public void setAntoNightCallTime(Integer antoNightCallTime) {
		this.antoNightCallTime = antoNightCallTime;
	}

	public Integer getAntoNightCallTime() {
		return this.antoNightCallTime;
	}
	public void setNotArrivedNoPayExecute(Integer notArrivedNoPayExecute) {
		this.notArrivedNoPayExecute = notArrivedNoPayExecute;
	}

	public Integer getNotArrivedNoPayExecute() {
		return this.notArrivedNoPayExecute;
	}
	public void setRegistPersonExecute(Integer registPersonExecute) {
		this.registPersonExecute = registPersonExecute;
	}

	public Integer getRegistPersonExecute() {
		return this.registPersonExecute;
	}
	public void setNotArrivedPayExecute(Integer notArrivedPayExecute) {
		this.notArrivedPayExecute = notArrivedPayExecute;
	}

	public Integer getNotArrivedPayExecute() {
		return this.notArrivedPayExecute;
	}
	public void setNotLeaveExecute(Integer notLeaveExecute) {
		this.notLeaveExecute = notLeaveExecute;
	}

	public Integer getNotLeaveExecute() {
		return this.notLeaveExecute;
	}
	public void setHourRoomExecute(Integer hourRoomExecute) {
		this.hourRoomExecute = hourRoomExecute;
	}

	public Integer getHourRoomExecute() {
		return this.hourRoomExecute;
	}
	public void setOoRoomExecute(Integer ooRoomExecute) {
		this.ooRoomExecute = ooRoomExecute;
	}

	public Integer getOoRoomExecute() {
		return this.ooRoomExecute;
	}
	public void setRoomAddAccount(Integer roomAddAccount) {
		this.roomAddAccount = roomAddAccount;
	}

	public Integer getRoomAddAccount() {
		return this.roomAddAccount;
	}
}

