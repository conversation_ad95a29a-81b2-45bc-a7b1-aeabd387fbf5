package com.pms.czabsnight.bean;

import java.io.Serializable;

public class NightAuditPay implements Serializable{
	private Integer nightAuditPayId  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private String payClassId  ;
	private String payClassName  ;
	private String payCodeId  ;
	private String payCodeName  ;
	private Integer totalAmount  ;
	private Integer refundAmount  ;
	private Integer type;
	private Integer businessDay  ;
	private Integer auditYear  ;
	private Integer auditYearMonth  ;

	public NightAuditPay(){
	}

	public NightAuditPay(Integer nightAuditPayId){
		this.nightAuditPayId = nightAuditPayId;
	}

	public void setNightAuditPayId(Integer nightAuditPayId) {
		this.nightAuditPayId = nightAuditPayId;
	}
	
	public Integer getNightAuditPayId() {
		return this.nightAuditPayId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setPayClassId(String payClassId) {
		this.payClassId = payClassId;
	}
	
	public String getPayClassId() {
		return this.payClassId;
	}
	public void setPayClassName(String payClassName) {
		this.payClassName = payClassName;
	}
	
	public String getPayClassName() {
		return this.payClassName;
	}
	public void setPayCodeId(String payCodeId) {
		this.payCodeId = payCodeId;
	}
	
	public String getPayCodeId() {
		return this.payCodeId;
	}
	public void setPayCodeName(String payCodeName) {
		this.payCodeName = payCodeName;
	}
	
	public String getPayCodeName() {
		return this.payCodeName;
	}
	public void setTotalAmount(Integer totalAmount) {
		this.totalAmount = totalAmount;
	}
	
	public Integer getTotalAmount() {
		return this.totalAmount;
	}
	public void setRefundAmount(Integer refundAmount) {
		this.refundAmount = refundAmount;
	}
	
	public Integer getRefundAmount() {
		return this.refundAmount;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer auditYear) {
		this.auditYear = auditYear;
	}
	
	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer auditYearMonth) {
		this.auditYearMonth = auditYearMonth;
	}
	
	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
}

