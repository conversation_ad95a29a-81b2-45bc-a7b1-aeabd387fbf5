package com.pms.czabsnight.bean.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("NightAuditPaySearch")
public class NightAuditPaySearch extends BaseSearch{
	private Integer nightAuditPayId;
	private Integer hid;
	private Integer hotelGroupId;
	private String payClassId;
	private String payClassName;
	private String payCodeId;
	private String payCodeName;
	private Integer totalAmount;
	private Integer refundAmount;
	private Integer type;
	private Integer businessDay;
	private Integer auditYear;
	private Integer auditYearMonth;
	private Integer businessDayMin;
	private Integer businessDayMax;

	public Integer getBusinessDayMin() {
		return businessDayMin;
	}

	public void setBusinessDayMin(Integer businessDayMin) {
		this.businessDayMin = businessDayMin;
	}

	public Integer getBusinessDayMax() {
		return businessDayMax;
	}

	public void setBusinessDayMax(Integer businessDayMax) {
		this.businessDayMax = businessDayMax;
	}

	public void setNightAuditPayId(Integer value) {
		this.nightAuditPayId = value;
	}

	public Integer getNightAuditPayId() {
		return this.nightAuditPayId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setPayClassId(String value) {
		this.payClassId = value;
	}

	public String getPayClassId() {
		return this.payClassId;
	}
	public void setPayClassName(String value) {
		this.payClassName = value;
	}

	public String getPayClassName() {
		return this.payClassName;
	}
	public void setPayCodeId(String value) {
		this.payCodeId = value;
	}

	public String getPayCodeId() {
		return this.payCodeId;
	}
	public void setPayCodeName(String value) {
		this.payCodeName = value;
	}

	public String getPayCodeName() {
		return this.payCodeName;
	}
	public void setTotalAmount(Integer value) {
		this.totalAmount = value;
	}

	public Integer getTotalAmount() {
		return this.totalAmount;
	}
	public void setRefundAmount(Integer value) {
		this.refundAmount = value;
	}

	public Integer getRefundAmount() {
		return this.refundAmount;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer value) {
		this.auditYear = value;
	}

	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer value) {
		this.auditYearMonth = value;
	}

	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}
}

