package com.pms.czabsnight.bean;

import com.pms.czpmsutils.HotelUtils;

import java.io.Serializable;
import java.util.Date;

public class NightStep implements Serializable{
	private Integer id  ;
	private Integer nightSettingId  ;
	private String nightSettingName  ;
	private Integer hid  ;
	private Integer state;
	private Integer hotelGroupId  ;
	private Date beginTime  ;
	private Date endTime  ;
	private String createUserId  ;
	private String createUserName  ;
	private Integer businessDay  ;
	private Integer finishType;

	public NightStep(){
	}

	public NightStep(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setNightSettingId(Integer nightSettingId) {
		this.nightSettingId = nightSettingId;
	}
	
	public Integer getNightSettingId() {
		return this.nightSettingId;
	}
	public void setNightSettingName(String nightSettingName) {
		this.nightSettingName = nightSettingName;
	}
	
	public String getNightSettingName() {
		return this.nightSettingName;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}
	
	public String getBeginTime() {
		return HotelUtils.parseDate2Str(this.beginTime);
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	
	public String getEndTime() {
		if(this.endTime==null){
			return null;
		}
		return HotelUtils.parseDate2Str(this.endTime);
	}
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getFinishType() {
		return finishType;
	}

	public void setFinishType(Integer finishType) {
		this.finishType = finishType;
	}
}

