package com.pms.czabsnight.bean;

import java.io.Serializable;

/**
 *
 */
public class NightAuditOta implements Serializable{
	//
	private Integer id;
	//
	private Integer hid;
	//
	private Integer hotelGroupId;
	//名称
	private String otaName;
	//ota类型
	private Integer otaType;
	//入住次数
	private Integer checkinNum = 0;
	//入住次数
	private Integer checkinNumMonth = 0;
	//入住次数
	private Integer checkinNumYear = 0;
	//房费
	private Integer roomMoney = 0;
	//商品费
	private Integer goodsMoney = 0;
	//餐费
	private Integer foodsMoney = 0;
	//支付金额
	private Integer payMoney = 0;
	//房费
	private Integer roomMoneyMonth = 0;
	//商品费
	private Integer goodsMoneyMonth = 0;
	//餐费
	private Integer foodsMoneyMonth = 0;
	//营业日期  yyyyMMdd
	private Integer businessDay;
	//房费
	private Integer roomMoneyYear = 0;
	//商品费
	private Integer goodsMoneyYear = 0;
	//餐费
	private Integer foodsMoneyYear = 0;
	//订单冗余年 2017
	private Integer auditYear;
	//订单冗余年 月
	private Integer auditYearMonth;

	public NightAuditOta(){
	}

	public NightAuditOta(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setOtaName(String otaName) {
		this.otaName = otaName;
	}

	public String getOtaName() {
		return this.otaName;
	}
	public void setOtaType(Integer otaType) {
		this.otaType = otaType;
	}

	public Integer getOtaType() {
		return this.otaType;
	}
	public void setCheckinNum(Integer checkinNum) {
		this.checkinNum = checkinNum;
	}

	public Integer getCheckinNum() {
		return this.checkinNum;
	}
	public void setCheckinNumMonth(Integer checkinNumMonth) {
		this.checkinNumMonth = checkinNumMonth;
	}

	public Integer getCheckinNumMonth() {
		return this.checkinNumMonth;
	}
	public void setCheckinNumYear(Integer checkinNumYear) {
		this.checkinNumYear = checkinNumYear;
	}

	public Integer getCheckinNumYear() {
		return this.checkinNumYear;
	}
	public void setRoomMoney(Integer roomMoney) {
		this.roomMoney = roomMoney;
	}

	public Integer getRoomMoney() {
		return this.roomMoney;
	}
	public void setGoodsMoney(Integer goodsMoney) {
		this.goodsMoney = goodsMoney;
	}

	public Integer getGoodsMoney() {
		return this.goodsMoney;
	}
	public void setFoodsMoney(Integer foodsMoney) {
		this.foodsMoney = foodsMoney;
	}

	public Integer getFoodsMoney() {
		return this.foodsMoney;
	}
	public void setPayMoney(Integer payMoney) {
		this.payMoney = payMoney;
	}

	public Integer getPayMoney() {
		return this.payMoney;
	}
	public void setRoomMoneyMonth(Integer roomMoneyMonth) {
		this.roomMoneyMonth = roomMoneyMonth;
	}

	public Integer getRoomMoneyMonth() {
		return this.roomMoneyMonth;
	}
	public void setGoodsMoneyMonth(Integer goodsMoneyMonth) {
		this.goodsMoneyMonth = goodsMoneyMonth;
	}

	public Integer getGoodsMoneyMonth() {
		return this.goodsMoneyMonth;
	}
	public void setFoodsMoneyMonth(Integer foodsMoneyMonth) {
		this.foodsMoneyMonth = foodsMoneyMonth;
	}

	public Integer getFoodsMoneyMonth() {
		return this.foodsMoneyMonth;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setRoomMoneyYear(Integer roomMoneyYear) {
		this.roomMoneyYear = roomMoneyYear;
	}

	public Integer getRoomMoneyYear() {
		return this.roomMoneyYear;
	}
	public void setGoodsMoneyYear(Integer goodsMoneyYear) {
		this.goodsMoneyYear = goodsMoneyYear;
	}

	public Integer getGoodsMoneyYear() {
		return this.goodsMoneyYear;
	}
	public void setFoodsMoneyYear(Integer foodsMoneyYear) {
		this.foodsMoneyYear = foodsMoneyYear;
	}

	public Integer getFoodsMoneyYear() {
		return this.foodsMoneyYear;
	}
	public void setAuditYear(Integer auditYear) {
		this.auditYear = auditYear;
	}

	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer auditYearMonth) {
		this.auditYearMonth = auditYearMonth;
	}

	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

}

