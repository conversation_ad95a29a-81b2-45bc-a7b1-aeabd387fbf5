package com.pms.czabsnight.bean;

import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.pmsorder.bean.BookingOrderConfig;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;

import java.util.List;

/**
 * 入住的参数集合
 */
public class CheckInRegist {

    // 入住登记单
    private Regist regist;

    // 入住人
    private List<RegistPerson> registPeople;

    // 房价
    private List<BookingOrderDailyPrice> bookingOrderDailyPrices;

    // 配置
    private BookingOrderConfig bookingOrderConfig;

    // 辅助房态
    private List<RoomAuxiliaryRelation> roomAuxiliaryRelations;


    public Regist getRegist() {
        return regist;
    }

    public void setRegist(Regist regist) {
        this.regist = regist;
    }

    public List<RegistPerson> getRegistPeople() {
        return registPeople;
    }

    public void setRegistPeople(List<RegistPerson> registPeople) {
        this.registPeople = registPeople;
    }

    public List<BookingOrderDailyPrice> getBookingOrderDailyPrices() {
        return bookingOrderDailyPrices;
    }

    public void setBookingOrderDailyPrices(List<BookingOrderDailyPrice> bookingOrderDailyPrices) {
        this.bookingOrderDailyPrices = bookingOrderDailyPrices;
    }

    public BookingOrderConfig getBookingOrderConfig() {
        return bookingOrderConfig;
    }

    public void setBookingOrderConfig(BookingOrderConfig bookingOrderConfig) {
        this.bookingOrderConfig = bookingOrderConfig;
    }

    public List<RoomAuxiliaryRelation> getRoomAuxiliaryRelations() {
        return roomAuxiliaryRelations;
    }

    public void setRoomAuxiliaryRelations(List<RoomAuxiliaryRelation> roomAuxiliaryRelations) {
        this.roomAuxiliaryRelations = roomAuxiliaryRelations;
    }
}
