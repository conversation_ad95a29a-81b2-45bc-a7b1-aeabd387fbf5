package com.pms.czabsnight.bean;

public class RegistAccountRep {

    private Integer registId;

    private Integer bookingId;

    private Integer sumPay = 0 ;

    private Integer sumRefund = 0 ;

    private Integer sumRoom = 0  ;

    private Integer sumFood = 0 ;

    private Integer sumGoods = 0 ;

    private String peopleName ;

    private Integer resourceId ;

    private String resourceName ;

    private String roomNo ;

    // 1.登记单 2.预订单
    private Integer type ;

    // 状态
    private Integer state;

    public Integer getRegistId() {
        return registId;
    }

    public void setRegistId(Integer registId) {
        this.registId = registId;
    }

    public Integer getBookingId() {
        return bookingId;
    }

    public void setBookingId(Integer bookingId) {
        this.bookingId = bookingId;
    }

    public Integer getSumPay() {
        return sumPay;
    }

    public void setSumPay(Integer sumPay) {
        this.sumPay = sumPay;
    }

    public Integer getSumRefund() {
        return sumRefund;
    }

    public void setSumRefund(Integer sumRefund) {
        this.sumRefund = sumRefund;
    }

    public Integer getSumRoom() {
        return sumRoom;
    }

    public void setSumRoom(Integer sumRoom) {
        this.sumRoom = sumRoom;
    }

    public Integer getSumFood() {
        return sumFood;
    }

    public void setSumFood(Integer sumFood) {
        this.sumFood = sumFood;
    }

    public Integer getSumGoods() {
        return sumGoods;
    }

    public void setSumGoods(Integer sumGoods) {
        this.sumGoods = sumGoods;
    }

    public String getPeopleName() {
        return peopleName;
    }

    public void setPeopleName(String peopleName) {
        this.peopleName = peopleName;
    }

    public Integer getResourceId() {
        return resourceId;
    }

    public void setResourceId(Integer resourceId) {
        this.resourceId = resourceId;
    }



    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }


    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }
}
