package com.pms.czabsnight.bean;

// 日营业报表返回数据
public class DailyReportView {

    private Integer roomMoney =0;
    private Integer foodMoney =0 ;
    private Integer goodMoney = 0 ;

    // 会员售卡金额
    private Integer vipMoney = 0;
    // 会员储值金额
    private Integer vipRechange = 0;

    // 会议费
    private Integer meetMoney = 0;

    // 赔偿费
    private Integer compensateMoney = 0;

    private Integer otherMoney = 0;

    // 总价
    private Integer sumMoney = 0;

    private Double sumTb;

    private Double roomTb;

    private Double goodTb;

    private Double foodTb;

    private Double otherTb;

    private Double compensateTb;

    private Double meetTb;

    private Double vipMoneyTb;

    private Double vipRechangeTb;



    public Integer getRoomMoney() {
        return roomMoney;
    }

    public void setRoomMoney(Integer roomMoney) {
        this.roomMoney = roomMoney;
    }

    public Integer getFoodMoney() {
        return foodMoney;
    }

    public void setFoodMoney(Integer foodMoney) {
        this.foodMoney = foodMoney;
    }

    public Integer getGoodMoney() {
        return goodMoney;
    }

    public void setGoodMoney(Integer goodMoney) {
        this.goodMoney = goodMoney;
    }

    public Integer getVipMoney() {
        return vipMoney;
    }

    public void setVipMoney(Integer vipMoney) {
        this.vipMoney = vipMoney;
    }

    public Integer getVipRechange() {
        return vipRechange;
    }

    public void setVipRechange(Integer vipRechange) {
        this.vipRechange = vipRechange;
    }

    public Integer getMeetMoney() {
        return meetMoney;
    }

    public void setMeetMoney(Integer meetMoney) {
        this.meetMoney = meetMoney;
    }

    public Integer getCompensateMoney() {
        return compensateMoney;
    }

    public void setCompensateMoney(Integer compensateMoney) {
        this.compensateMoney = compensateMoney;
    }

    public Integer getSumMoney() {
        return sumMoney;
    }

    public void setSumMoney(Integer sumMoney) {
        this.sumMoney = sumMoney;
    }

    public Integer getOtherMoney() {
        return otherMoney;
    }

    public void setOtherMoney(Integer otherMoney) {
        this.otherMoney = otherMoney;
    }

    public Double getSumTb() {
        return sumTb;
    }

    public void setSumTb(Double sumTb) {
        this.sumTb = sumTb;
    }

    public Double getRoomTb() {
        return roomTb;
    }

    public void setRoomTb(Double roomTb) {
        this.roomTb = roomTb;
    }

    public Double getGoodTb() {
        return goodTb;
    }

    public void setGoodTb(Double goodTb) {
        this.goodTb = goodTb;
    }

    public Double getFoodTb() {
        return foodTb;
    }

    public void setFoodTb(Double foodTb) {
        this.foodTb = foodTb;
    }

    public Double getOtherTb() {
        return otherTb;
    }

    public void setOtherTb(Double otherTb) {
        this.otherTb = otherTb;
    }

    public Double getCompensateTb() {
        return compensateTb;
    }

    public void setCompensateTb(Double compensateTb) {
        this.compensateTb = compensateTb;
    }

    public Double getMeetTb() {
        return meetTb;
    }

    public void setMeetTb(Double meetTb) {
        this.meetTb = meetTb;
    }

    public Double getVipMoneyTb() {
        return vipMoneyTb;
    }

    public void setVipMoneyTb(Double vipMoneyTb) {
        this.vipMoneyTb = vipMoneyTb;
    }

    public Double getVipRechangeTb() {
        return vipRechangeTb;
    }

    public void setVipRechangeTb(Double vipRechangeTb) {
        this.vipRechangeTb = vipRechangeTb;
    }
}
