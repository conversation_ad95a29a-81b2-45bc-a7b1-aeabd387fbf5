package com.pms.czabsnight.bean.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("NightAuditMemberSearch")
public class NightAuditMemberSearch extends BaseSearch{
	private Integer nightAuditMemberId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer cardTypeId;
	private String cardType;
	private Integer cardLevelId;
	private String cardLevel;
	private Integer type;
	private Integer addNewMember;
	private Integer recharge;
	private Integer businessDay;
	private Integer auditYear;
	private Integer auditYearMonth;
	private Integer businessDayMax;
	private Integer businessDayMin;

	public void setNightAuditMemberId(Integer value) {
		this.nightAuditMemberId = value;
	}

	public Integer getNightAuditMemberId() {
		return this.nightAuditMemberId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setCardTypeId(Integer value) {
		this.cardTypeId = value;
	}

	public Integer getCardTypeId() {
		return this.cardTypeId;
	}
	public void setCardType(String value) {
		this.cardType = value;
	}

	public String getCardType() {
		return this.cardType;
	}
	public void setCardLevelId(Integer value) {
		this.cardLevelId = value;
	}

	public Integer getCardLevelId() {
		return this.cardLevelId;
	}
	public void setCardLevel(String value) {
		this.cardLevel = value;
	}

	public String getCardLevel() {
		return this.cardLevel;
	}
	public void setType(Integer value) {
		this.type = value;
	}

	public Integer getType() {
		return this.type;
	}
	public void setAddNewMember(Integer value) {
		this.addNewMember = value;
	}

	public Integer getAddNewMember() {
		return this.addNewMember;
	}
	public void setRecharge(Integer value) {
		this.recharge = value;
	}

	public Integer getRecharge() {
		return this.recharge;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer value) {
		this.auditYear = value;
	}

	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer value) {
		this.auditYearMonth = value;
	}

	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

	public Integer getBusinessDayMax() {
		return businessDayMax;
	}

	public void setBusinessDayMax(Integer businessDayMax) {
		this.businessDayMax = businessDayMax;
	}

	public Integer getBusinessDayMin() {
		return businessDayMin;
	}

	public void setBusinessDayMin(Integer businessDayMin) {
		this.businessDayMin = businessDayMin;
	}
}

