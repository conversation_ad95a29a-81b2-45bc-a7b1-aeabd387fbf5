package com.pms.czabsnight.bean;


import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;
import java.util.Date;

public class NightAuditRecord extends BaseBean implements Serializable{
	private Integer nightAuditRecordId  ;
	private Integer hid  ;
	private Integer hotelGroupId;
	private Integer state;
	private Date auditStart  ;
	private Date auditEnd  ;
	private Integer classId  ;
	private Integer businessDay  ;
	private String message  ;

	public NightAuditRecord(){
	}

	public NightAuditRecord(Integer nightAuditRecordId){
		this.nightAuditRecordId = nightAuditRecordId;
	}

	public void setNightAuditRecordId(Integer nightAuditRecordId) {
		this.nightAuditRecordId = nightAuditRecordId;
	}
	
	public Integer getNightAuditRecordId() {
		return this.nightAuditRecordId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}

	public void setAuditStart(Date auditStart) {
		this.auditStart = auditStart;
	}
	
	public Date getAuditStart() {
		return this.auditStart;
	}

	public void setAuditEnd(Date auditEnd) {
		this.auditEnd = auditEnd;
	}
	
	public Date getAuditEnd() {
		return this.auditEnd;
	}
	public void setClassId(Integer classId) {
		this.classId = classId;
	}
	
	public Integer getClassId() {
		return this.classId;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public void setMessage(String message) {
		this.message = message;
	}
	
	public String getMessage() {
		return this.message;
	}

	public Integer getHotelGroupId() {
		return hotelGroupId;
	}

	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}
}

