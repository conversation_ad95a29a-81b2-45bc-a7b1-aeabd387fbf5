package com.pms.czabsnight.bean.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("NightStepSearch")
public class NightStepSearch extends BaseSearch{
	private Integer id;
	private Integer nightSettingId;
	private String nightSettingName;
	private Integer state;
	private Integer hid;
	private Integer hotelGroupId;
	private java.util.Date beginTime;
	private java.util.Date endTime;
	private Integer finishType;
	private String createUserId;
	private String createUserName;
	private Integer businessDay;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setNightSettingId(Integer value) {
		this.nightSettingId = value;
	}
	
	public Integer getNightSettingId() {
		return this.nightSettingId;
	}
	public void setNightSettingName(String value) {
		this.nightSettingName = value;
	}
	
	public String getNightSettingName() {
		return this.nightSettingName;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

	public void setBeginTime(java.util.Date value) {
		this.beginTime = value;
	}
	
	public java.util.Date getBeginTime() {
		return this.beginTime;
	}

	public void setEndTime(java.util.Date value) {
		this.endTime = value;
	}
	
	public java.util.Date getEndTime() {
		return this.endTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getFinishType() {
		return finishType;
	}

	public void setFinishType(Integer finishType) {
		this.finishType = finishType;
	}
}

