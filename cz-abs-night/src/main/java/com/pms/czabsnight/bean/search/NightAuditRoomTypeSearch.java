package com.pms.czabsnight.bean.search;

import com.pms.czpmsutils.request.BaseRequest;
import org.apache.ibatis.type.Alias;

@Alias("NightAuditRoomTypeSearch")
public class NightAuditRoomTypeSearch extends BaseRequest {
	private Integer nightAuditRoomTypeId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomTypeId;
	private String roomTypeName;
	private Integer totalRoomCount;
	private String openRoomCount;
	private Integer repairRoomCount;
	private Integer noserviceRoomCount;
	private Integer selfRoomCount;
	private Integer totalAmount;
	private Integer averagePrice;
	//开房率
	private Double openRate;
	//单房贡献率
	private Double revpar;
	private Integer goodsMoney  ;
	private Integer foodsMoney  ;
	private Integer businessDay;
	private Integer auditYear;
	private Integer auditYearMonth;

	private Integer businessDayMin;
	private Integer businessDayMax;

	private Integer groupType;

	public void setNightAuditRoomTypeId(Integer value) {
		this.nightAuditRoomTypeId = value;
	}

	public Integer getNightAuditRoomTypeId() {
		return this.nightAuditRoomTypeId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomTypeId(Integer value) {
		this.roomTypeId = value;
	}

	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setRoomTypeName(String value) {
		this.roomTypeName = value;
	}

	public String getRoomTypeName() {
		return this.roomTypeName;
	}
	public void setTotalRoomCount(Integer value) {
		this.totalRoomCount = value;
	}

	public Integer getTotalRoomCount() {
		return this.totalRoomCount;
	}
	public void setOpenRoomCount(String value) {
		this.openRoomCount = value;
	}

	public String getOpenRoomCount() {
		return this.openRoomCount;
	}
	public void setRepairRoomCount(Integer value) {
		this.repairRoomCount = value;
	}

	public Integer getRepairRoomCount() {
		return this.repairRoomCount;
	}
	public void setNoserviceRoomCount(Integer value) {
		this.noserviceRoomCount = value;
	}

	public Integer getNoserviceRoomCount() {
		return this.noserviceRoomCount;
	}
	public void setSelfRoomCount(Integer value) {
		this.selfRoomCount = value;
	}

	public Integer getSelfRoomCount() {
		return this.selfRoomCount;
	}
	public void setTotalAmount(Integer value) {
		this.totalAmount = value;
	}

	public Integer getTotalAmount() {
		return this.totalAmount;
	}
	public void setAveragePrice(Integer value) {
		this.averagePrice = value;
	}

	public Integer getAveragePrice() {
		return this.averagePrice;
	}

	public Double getOpenRate() {
		return openRate;
	}

	public void setOpenRate(Double openRate) {
		this.openRate = openRate;
	}

	public Double getRevpar() {
		return revpar;
	}

	public void setRevpar(Double revpar) {
		this.revpar = revpar;
	}

	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer value) {
		this.auditYear = value;
	}

	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer value) {
		this.auditYearMonth = value;
	}

	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

	public Integer getGoodsMoney() {
		return goodsMoney;
	}

	public void setGoodsMoney(Integer goodsMoney) {
		this.goodsMoney = goodsMoney;
	}

	public Integer getFoodsMoney() {
		return foodsMoney;
	}

	public void setFoodsMoney(Integer foodsMoney) {
		this.foodsMoney = foodsMoney;
	}

	public Integer getBusinessDayMin() {
		return businessDayMin;
	}

	public void setBusinessDayMin(Integer businessDayMin) {
		this.businessDayMin = businessDayMin;
	}

	public Integer getBusinessDayMax() {
		return businessDayMax;
	}

	public void setBusinessDayMax(Integer businessDayMax) {
		this.businessDayMax = businessDayMax;
	}

	public Integer getGroupType() {
		return groupType;
	}

	public void setGroupType(Integer groupType) {
		this.groupType = groupType;
	}
}

