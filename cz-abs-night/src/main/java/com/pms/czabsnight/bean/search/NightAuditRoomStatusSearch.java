package com.pms.czabsnight.bean.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("NightAuditRoomStatusSearch")
public class NightAuditRoomStatusSearch extends BaseSearch{
	private Integer nightAuditRoomStatusId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer roomId;
	private String roomNum;
	private Integer registId;
	private Integer guestNum;
	private String guestName;
	private java.util.Date startTime;
	private java.util.Date endTime;
	private Integer registState;
	private Integer roomState;
	private Integer roomTypeId;
	private String roomTypeName;
	private Integer roomPrice;
	private Integer businessDay;
	private Integer auditYear;
	private Integer auditYearMonth;
	private Integer maxBusinessDay;
	private Integer minBusinessDay;

	public void setNightAuditRoomStatusId(Integer value) {
		this.nightAuditRoomStatusId = value;
	}

	public Integer getNightAuditRoomStatusId() {
		return this.nightAuditRoomStatusId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomId(Integer value) {
		this.roomId = value;
	}

	public Integer getRoomId() {
		return this.roomId;
	}
	public void setRoomNum(String value) {
		this.roomNum = value;
	}

	public String getRoomNum() {
		return this.roomNum;
	}
	public void setRegistId(Integer value) {
		this.registId = value;
	}

	public Integer getRegistId() {
		return this.registId;
	}
	public void setGuestNum(Integer value) {
		this.guestNum = value;
	}

	public Integer getGuestNum() {
		return this.guestNum;
	}
	public void setGuestName(String value) {
		this.guestName = value;
	}

	public String getGuestName() {
		return this.guestName;
	}

	public void setStartTime(java.util.Date value) {
		this.startTime = value;
	}

	public java.util.Date getStartTime() {
		return this.startTime;
	}

	public void setEndTime(java.util.Date value) {
		this.endTime = value;
	}

	public java.util.Date getEndTime() {
		return this.endTime;
	}
	public void setRegistState(Integer value) {
		this.registState = value;
	}

	public Integer getRegistState() {
		return this.registState;
	}
	public void setRoomState(Integer value) {
		this.roomState = value;
	}

	public Integer getRoomState() {
		return this.roomState;
	}
	public void setRoomTypeId(Integer value) {
		this.roomTypeId = value;
	}

	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setRoomTypeName(String value) {
		this.roomTypeName = value;
	}

	public String getRoomTypeName() {
		return this.roomTypeName;
	}
	public void setRoomPrice(Integer value) {
		this.roomPrice = value;
	}

	public Integer getRoomPrice() {
		return this.roomPrice;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer value) {
		this.auditYear = value;
	}

	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer value) {
		this.auditYearMonth = value;
	}

	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

	public Integer getMaxBusinessDay() {
		return maxBusinessDay;
	}

	public void setMaxBusinessDay(Integer maxBusinessDay) {
		this.maxBusinessDay = maxBusinessDay;
	}

	public Integer getMinBusinessDay() {
		return minBusinessDay;
	}

	public void setMinBusinessDay(Integer minBusinessDay) {
		this.minBusinessDay = minBusinessDay;
	}
}

