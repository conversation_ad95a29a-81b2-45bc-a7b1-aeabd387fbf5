package com.pms.czabsnight.bean;

import java.io.Serializable;

public class NightAuditBalance implements Serializable{
	//
	private Integer id;
	//1.宾客 2.会员 3.应收账
	private Integer type;
	//总消费
	private Integer money;
	//
	private Integer vipMoney;
	//
	private Integer arMoney;
	//
	private Integer hid;
	//
	private Integer hotelGroupId;
	//营业日期  yyyyMMdd
	private Integer businessDay;
	//订单冗余年 2017
	private Integer auditYear;
	//订单冗余年 月
	private Integer auditYearMonth;

	public NightAuditBalance(){
	}

	public NightAuditBalance(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setType(Integer type) {
		this.type = type;
	}
	
	public Integer getType() {
		return this.type;
	}
	public void setMoney(Integer money) {
		this.money = money;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setVipMoney(Integer vipMoney) {
		this.vipMoney = vipMoney;
	}
	
	public Integer getVipMoney() {
		return this.vipMoney;
	}
	public void setArMoney(Integer arMoney) {
		this.arMoney = arMoney;
	}
	
	public Integer getArMoney() {
		return this.arMoney;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer auditYear) {
		this.auditYear = auditYear;
	}
	
	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer auditYearMonth) {
		this.auditYearMonth = auditYearMonth;
	}
	
	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

}

