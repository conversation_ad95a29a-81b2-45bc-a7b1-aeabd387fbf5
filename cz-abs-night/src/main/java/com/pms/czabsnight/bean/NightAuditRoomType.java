package com.pms.czabsnight.bean;

import com.pms.czpmsutils.HotelUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.Serializable;

public class NightAuditRoomType implements Serializable{
	private static final Logger log = LogManager.getLogger(NightAuditRoomType.class);
	private Integer nightAuditRoomTypeId  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer roomTypeId  ;
	private String roomTypeName  ;
	private Integer totalRoomCount = 0 ;
	private Integer openRoomCount = 0 ;
	private Integer repairRoomCount = 0 ;
	private Integer noserviceRoomCount = 0  ;
	private Integer selfRoomCount = 0  ;
	private Integer totalAmount = 0  ;
	private Integer averagePrice  ;
	//开房率
	private Double openRate;
	//单房贡献率
	private Double revpar;
	private Integer goodsMoney  ;
	private Integer foodsMoney  ;
	private Integer businessDay  ;
	private Integer auditYear  ;
	private Integer auditYearMonth  ;

	private String dateKey ;

	public NightAuditRoomType(){
	}

	public NightAuditRoomType(Integer nightAuditRoomTypeId){
		this.nightAuditRoomTypeId = nightAuditRoomTypeId;
	}

	public void setNightAuditRoomTypeId(Integer nightAuditRoomTypeId) {
		this.nightAuditRoomTypeId = nightAuditRoomTypeId;
	}
	
	public Integer getNightAuditRoomTypeId() {
		return this.nightAuditRoomTypeId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setRoomTypeName(String roomTypeName) {
		this.roomTypeName = roomTypeName;
	}
	
	public String getRoomTypeName() {
		return this.roomTypeName;
	}
	public void setTotalRoomCount(Integer totalRoomCount) {
		this.totalRoomCount = totalRoomCount;
	}
	
	public Integer getTotalRoomCount() {
		return this.totalRoomCount;
	}
	public void setOpenRoomCount(Integer openRoomCount) {
		this.openRoomCount = openRoomCount;
	}
	
	public Integer getOpenRoomCount() {
		return this.openRoomCount;
	}
	public void setRepairRoomCount(Integer repairRoomCount) {
		this.repairRoomCount = repairRoomCount;
	}
	
	public Integer getRepairRoomCount() {
		return this.repairRoomCount;
	}
	public void setNoserviceRoomCount(Integer noserviceRoomCount) {
		this.noserviceRoomCount = noserviceRoomCount;
	}
	
	public Integer getNoserviceRoomCount() {
		return this.noserviceRoomCount;
	}
	public void setSelfRoomCount(Integer selfRoomCount) {
		this.selfRoomCount = selfRoomCount;
	}
	
	public Integer getSelfRoomCount() {
		return this.selfRoomCount;
	}
	public void setTotalAmount(Integer totalAmount) {
		this.totalAmount = totalAmount;
	}
	
	public Integer getTotalAmount() {
		return this.totalAmount;
	}
	public void setAveragePrice(Integer averagePrice) {
		this.averagePrice = averagePrice;
	}

	public Integer getAveragePrice() {
		return averagePrice;
	}

	public Double getOpenRate() {
		return openRate;
	}

	public void setOpenRate(Double openRate) {
		this.openRate = openRate;
	}

	public Double getRevpar() {
		return revpar;
	}

	public void setRevpar(Double revpar) {
		this.revpar = revpar;
	}

	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer auditYear) {
		this.auditYear = auditYear;
	}
	
	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer auditYearMonth) {
		this.auditYearMonth = auditYearMonth;
	}
	
	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

	public Integer getGoodsMoney() {
		return goodsMoney;
	}

	public void setGoodsMoney(Integer goodsMoney) {
		this.goodsMoney = goodsMoney;
	}

	public Integer getFoodsMoney() {
		return foodsMoney;
	}

	public void setFoodsMoney(Integer foodsMoney) {
		this.foodsMoney = foodsMoney;
	}

	public String getDateKey() {

		try {
			return HotelUtils.parseDate2Str(HotelUtils.parseInt2Date(this.businessDay)).substring(0, 10) + roomTypeId;
		}catch (Exception e){
			log.error("",e);
			return "s";
		}

	}

}

