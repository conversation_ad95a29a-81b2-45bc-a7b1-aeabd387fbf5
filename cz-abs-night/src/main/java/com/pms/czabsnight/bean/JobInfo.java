package com.pms.czabsnight.bean;


import org.quartz.JobDataMap;

public class JobInfo {

    private String jobClassName;

    private String jobGroupName;

    private String cronExpression;

    private String jobType;

    private Integer timeType;

    private JobDataMap data = new JobDataMap();


    public String getJobClassName() {
        return "com.pms.czabsnight.jobs."+jobClassName.trim();
    }

    public void setJobClassName(String jobClassName) {
        this.jobClassName = jobClassName;
    }

    public String getJobGroupName() {
        return jobGroupName;
    }

    public void setJobGroupName(String jobGroupName) {
        this.jobGroupName = jobGroupName;
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }

    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }

    public Integer getTimeType() {
        return timeType;
    }


    public void setTimeType(Integer timeType) {
        this.timeType = timeType;
    }

    public JobDataMap getData() {
        return data;
    }

    public void setData(JobDataMap data) {
        this.data = data;
    }
}
