package com.pms.czabsnight.bean;

import java.io.Serializable;

public class NigthAuditArPayment implements Serializable{
	//
	private Integer id;
	//
	private Integer hid;
	//
	private Integer hotelGroupId;
	//金额
	private Integer money;
	//
	private Integer payClassId;
	//
	private String payClassName;
	//营业日期  yyyyMMdd
	private Integer businessDay;
	//订单冗余年 2017
	private Integer auditYear;
	//订单冗余年 月
	private Integer auditYearMonth;

	public NigthAuditArPayment(){
	}

	public NigthAuditArPayment(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setMoney(Integer money) {
		this.money = money;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setPayClassId(Integer payClassId) {
		this.payClassId = payClassId;
	}
	
	public Integer getPayClassId() {
		return this.payClassId;
	}
	public void setPayClassName(String payClassName) {
		this.payClassName = payClassName;
	}
	
	public String getPayClassName() {
		return this.payClassName;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer auditYear) {
		this.auditYear = auditYear;
	}
	
	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer auditYearMonth) {
		this.auditYearMonth = auditYearMonth;
	}
	
	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

}

