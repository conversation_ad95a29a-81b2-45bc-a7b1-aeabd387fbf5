package com.pms.czabsnight.bean.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;


@Alias("NightAuditCompanySearch")
public class NightAuditCompanySearch extends BaseSearch {
	private Integer nightAuditCompanyId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer companyId;
	private String companyName;
	private Integer addAraccount;
	private Integer decrAraccount;
	private Integer arLimit;
	private Integer businessDay;
	private Integer auditYear;
	private Integer auditYearMonth;

	public void setNightAuditCompanyId(Integer value) {
		this.nightAuditCompanyId = value;
	}
	
	public Integer getNightAuditCompanyId() {
		return this.nightAuditCompanyId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setCompanyId(Integer value) {
		this.companyId = value;
	}
	
	public Integer getCompanyId() {
		return this.companyId;
	}
	public void setCompanyName(String value) {
		this.companyName = value;
	}
	
	public String getCompanyName() {
		return this.companyName;
	}
	public void setAddAraccount(Integer value) {
		this.addAraccount = value;
	}
	
	public Integer getAddAraccount() {
		return this.addAraccount;
	}
	public void setDecrAraccount(Integer value) {
		this.decrAraccount = value;
	}
	
	public Integer getDecrAraccount() {
		return this.decrAraccount;
	}
	public void setArLimit(Integer value) {
		this.arLimit = value;
	}
	
	public Integer getArLimit() {
		return this.arLimit;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer value) {
		this.auditYear = value;
	}
	
	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer value) {
		this.auditYearMonth = value;
	}
	
	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

}

