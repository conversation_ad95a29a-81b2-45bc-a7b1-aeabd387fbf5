package com.pms.czabsnight.bean.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class NightAuditBalanceSearch extends PageBaseRequest{
	private Integer id;
	private Integer type;
	private Integer money;
	private Integer vipMoney;
	private Integer arMoney;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer businessDay;
	private Integer auditYear;
	private Integer auditYearMonth;
	private Integer endBusinessDay;

	public void setId(Integer value) {
		this.id = value;
	}

	public Integer getId() {
		return this.id;
	}
	public void setType(Integer value) {
		this.type = value;
	}

	public Integer getType() {
		return this.type;
	}
	public void setMoney(Integer value) {
		this.money = value;
	}

	public Integer getMoney() {
		return this.money;
	}
	public void setVipMoney(Integer value) {
		this.vipMoney = value;
	}

	public Integer getVipMoney() {
		return this.vipMoney;
	}
	public void setArMoney(Integer value) {
		this.arMoney = value;
	}

	public Integer getArMoney() {
		return this.arMoney;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer value) {
		this.auditYear = value;
	}

	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer value) {
		this.auditYearMonth = value;
	}

	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

	public Integer getEndBusinessDay() {
		return endBusinessDay;
	}

	public void setEndBusinessDay(Integer endBusinessDay) {
		this.endBusinessDay = endBusinessDay;
	}
}

