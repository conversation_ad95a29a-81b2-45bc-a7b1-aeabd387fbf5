package com.pms.czabsnight.bean.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class NightAutoSettingSearch extends PageBaseRequest{
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer autoNight;
	private String autoTime;
	private Integer antoNightCallTime;
	private Integer notArrivedNoPayExecute;
	private Integer registPersonExecute;
	private Integer notArrivedPayExecute;

	public Integer getAntoNightCallTime() {
		return antoNightCallTime;
	}

	public void setAntoNightCallTime(Integer antoNightCallTime) {
		this.antoNightCallTime = antoNightCallTime;
	}

	public Integer getNotArrivedNoPayExecute() {
		return notArrivedNoPayExecute;
	}

	public void setNotArrivedNoPayExecute(Integer notArrivedNoPayExecute) {
		this.notArrivedNoPayExecute = notArrivedNoPayExecute;
	}

	public Integer getRegistPersonExecute() {
		return registPersonExecute;
	}

	public void setRegistPersonExecute(Integer registPersonExecute) {
		this.registPersonExecute = registPersonExecute;
	}

	public Integer getNotArrivedPayExecute() {
		return notArrivedPayExecute;
	}

	public void setNotArrivedPayExecute(Integer notArrivedPayExecute) {
		this.notArrivedPayExecute = notArrivedPayExecute;
	}

	public Integer getNotLeaveExecute() {
		return notLeaveExecute;
	}

	public void setNotLeaveExecute(Integer notLeaveExecute) {
		this.notLeaveExecute = notLeaveExecute;
	}

	public Integer getHourRoomExecute() {
		return hourRoomExecute;
	}

	public void setHourRoomExecute(Integer hourRoomExecute) {
		this.hourRoomExecute = hourRoomExecute;
	}

	public Integer getOoRoomExecute() {
		return ooRoomExecute;
	}

	public void setOoRoomExecute(Integer ooRoomExecute) {
		this.ooRoomExecute = ooRoomExecute;
	}

	public Integer getRoomAddAccount() {
		return roomAddAccount;
	}

	public void setRoomAddAccount(Integer roomAddAccount) {
		this.roomAddAccount = roomAddAccount;
	}

	private Integer notLeaveExecute;
	private Integer hourRoomExecute;
	private Integer ooRoomExecute;
	private Integer roomAddAccount;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setAutoNight(Integer value) {
		this.autoNight = value;
	}
	
	public Integer getAutoNight() {
		return this.autoNight;
	}
	public void setAutoTime(String value) {
		this.autoTime = value;
	}
	
	public String getAutoTime() {
		return this.autoTime;
	}

}

