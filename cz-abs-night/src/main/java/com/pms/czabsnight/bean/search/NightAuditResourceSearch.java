package com.pms.czabsnight.bean.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("NightAuditResourceSearch")
public class NightAuditResourceSearch extends BaseSearch {
	private Integer id;
	private Integer resourceId;
	private String resourceName;
	private Integer checkinNum;
	//开房数
	private Integer checkinNumMonth;
	//开房数
	private Integer checkinNumYear;
	private Integer money;
	private Integer goodsMoney  ;
	private Integer foodsMoney  ;
	//总消费
	private Integer moneyMonth;
	//
	private Integer goodsMoneyMonth;
	//
	private Integer foodsMoneyMonth;

	private Integer moneyYear ;
	//
	private Integer goodsMoneyYear ;
	//
	private Integer foodsMoneyYear ;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer businessDay;
	private Integer auditYear;
	private Integer auditYearMonth;

	private Integer businessDayMin;
	private Integer businessDayMax;

	public void setId(Integer value) {
		this.id = value;
	}

	public Integer getId() {
		return this.id;
	}
	public void setResourceId(Integer value) {
		this.resourceId = value;
	}

	public Integer getResourceId() {
		return this.resourceId;
	}
	public void setResourceName(String value) {
		this.resourceName = value;
	}

	public String getResourceName() {
		return this.resourceName;
	}
	public void setCheckinNum(Integer value) {
		this.checkinNum = value;
	}

	public Integer getCheckinNum() {
		return this.checkinNum;
	}
	public void setMoney(Integer value) {
		this.money = value;
	}

	public Integer getMoney() {
		return this.money;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer value) {
		this.auditYear = value;
	}

	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer value) {
		this.auditYearMonth = value;
	}

	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

	public Integer getGoodsMoney() {
		return goodsMoney;
	}

	public void setGoodsMoney(Integer goodsMoney) {
		this.goodsMoney = goodsMoney;
	}

	public Integer getFoodsMoney() {
		return foodsMoney;
	}

	public void setFoodsMoney(Integer foodsMoney) {
		this.foodsMoney = foodsMoney;
	}

	public Integer getMoneyMonth() {
		return moneyMonth;
	}

	public void setMoneyMonth(Integer moneyMonth) {
		this.moneyMonth = moneyMonth;
	}

	public Integer getGoodsMoneyMonth() {
		return goodsMoneyMonth;
	}

	public void setGoodsMoneyMonth(Integer goodsMoneyMonth) {
		this.goodsMoneyMonth = goodsMoneyMonth;
	}

	public Integer getFoodsMoneyMonth() {
		return foodsMoneyMonth;
	}

	public void setFoodsMoneyMonth(Integer foodsMoneyMonth) {
		this.foodsMoneyMonth = foodsMoneyMonth;
	}

	public Integer getMoneyYear() {
		return moneyYear;
	}

	public void setMoneyYear(Integer moneyYear) {
		this.moneyYear = moneyYear;
	}

	public Integer getGoodsMoneyYear() {
		return goodsMoneyYear;
	}

	public void setGoodsMoneyYear(Integer goodsMoneyYear) {
		this.goodsMoneyYear = goodsMoneyYear;
	}

	public Integer getFoodsMoneyYear() {
		return foodsMoneyYear;
	}

	public void setFoodsMoneyYear(Integer foodsMoneyYear) {
		this.foodsMoneyYear = foodsMoneyYear;
	}

	public Integer getCheckinNumMonth() {
		return checkinNumMonth;
	}

	public void setCheckinNumMonth(Integer checkinNumMonth) {
		this.checkinNumMonth = checkinNumMonth;
	}

	public Integer getCheckinNumYear() {
		return checkinNumYear;
	}

	public void setCheckinNumYear(Integer checkinNumYear) {
		this.checkinNumYear = checkinNumYear;
	}

	public Integer getBusinessDayMin() {
		return businessDayMin;
	}

	public void setBusinessDayMin(Integer businessDayMin) {
		this.businessDayMin = businessDayMin;
	}

	public Integer getBusinessDayMax() {
		return businessDayMax;
	}

	public void setBusinessDayMax(Integer businessDayMax) {
		this.businessDayMax = businessDayMax;
	}
}

