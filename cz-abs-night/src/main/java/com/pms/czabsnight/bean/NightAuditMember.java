package com.pms.czabsnight.bean;

import java.io.Serializable;

public class NightAuditMember implements Serializable{
	private Integer nightAuditMemberId  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer cardTypeId  ;
	private String cardType  ;
	private Integer cardLevelId  ;
	private String cardLevel  ;
	private Integer type  ;
	private Integer addNewMember  ;
	private Integer recharge  ;
	private Integer businessDay  ;
	private Integer auditYear  ;
	private Integer auditYearMonth  ;

	public NightAuditMember(){
	}

	public NightAuditMember(Integer nightAuditMemberId){
		this.nightAuditMemberId = nightAuditMemberId;
	}

	public void setNightAuditMemberId(Integer nightAuditMemberId) {
		this.nightAuditMemberId = nightAuditMemberId;
	}

	public Integer getNightAuditMemberId() {
		return this.nightAuditMemberId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setCardTypeId(Integer cardTypeId) {
		this.cardTypeId = cardTypeId;
	}

	public Integer getCardTypeId() {
		return this.cardTypeId;
	}
	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public String getCardType() {
		return this.cardType;
	}
	public void setCardLevelId(Integer cardLevelId) {
		this.cardLevelId = cardLevelId;
	}

	public Integer getCardLevelId() {
		return this.cardLevelId;
	}
	public void setCardLevel(String cardLevel) {
		this.cardLevel = cardLevel;
	}

	public String getCardLevel() {
		return this.cardLevel;
	}
	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getType() {
		return this.type;
	}
	public void setAddNewMember(Integer addNewMember) {
		this.addNewMember = addNewMember;
	}

	public Integer getAddNewMember() {
		return this.addNewMember;
	}
	public void setRecharge(Integer recharge) {
		this.recharge = recharge;
	}

	public Integer getRecharge() {
		return this.recharge;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer auditYear) {
		this.auditYear = auditYear;
	}

	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer auditYearMonth) {
		this.auditYearMonth = auditYearMonth;
	}

	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

}

