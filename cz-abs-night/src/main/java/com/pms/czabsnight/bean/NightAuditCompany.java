package com.pms.czabsnight.bean;

import java.io.Serializable;

public class NightAuditCompany implements Serializable{
	private Integer nightAuditCompanyId  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer companyId  ;
	private String companyName  ;
	private Integer addAraccount  ;
	private Integer decrAraccount  ;
	private Integer arLimit  ;
	private Integer businessDay  ;
	private Integer auditYear  ;
	private Integer auditYearMonth  ;

	public NightAuditCompany(){
	}

	public NightAuditCompany(Integer nightAuditCompanyId){
		this.nightAuditCompanyId = nightAuditCompanyId;
	}

	public void setNightAuditCompanyId(Integer nightAuditCompanyId) {
		this.nightAuditCompanyId = nightAuditCompanyId;
	}
	
	public Integer getNightAuditCompanyId() {
		return this.nightAuditCompanyId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}
	
	public Integer getCompanyId() {
		return this.companyId;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	
	public String getCompanyName() {
		return this.companyName;
	}
	public void setAddAraccount(Integer addAraccount) {
		this.addAraccount = addAraccount;
	}
	
	public Integer getAddAraccount() {
		return this.addAraccount;
	}
	public void setDecrAraccount(Integer decrAraccount) {
		this.decrAraccount = decrAraccount;
	}
	
	public Integer getDecrAraccount() {
		return this.decrAraccount;
	}
	public void setArLimit(Integer arLimit) {
		this.arLimit = arLimit;
	}
	
	public Integer getArLimit() {
		return this.arLimit;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer auditYear) {
		this.auditYear = auditYear;
	}
	
	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer auditYearMonth) {
		this.auditYearMonth = auditYearMonth;
	}
	
	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

}

