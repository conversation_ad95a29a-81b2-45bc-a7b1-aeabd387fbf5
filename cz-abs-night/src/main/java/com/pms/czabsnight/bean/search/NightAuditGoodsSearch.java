package com.pms.czabsnight.bean.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("NightAuditGoodsSearch")
public class NightAuditGoodsSearch extends BaseSearch {
	private Integer id;
	private Integer goodsInfoId;
	private String goodsInfoName;
	private Integer goodsClassId;
	private String goodsClassName;
	private Integer amount;
	private Integer money;
	private Integer type;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer businessDay;
	private Integer auditYear;
	private Integer auditYearMonth;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setGoodsInfoId(Integer value) {
		this.goodsInfoId = value;
	}
	
	public Integer getGoodsInfoId() {
		return this.goodsInfoId;
	}


	public String getGoodsInfoName() {
		return goodsInfoName;
	}

	public void setGoodsInfoName(String goodsInfoName) {
		this.goodsInfoName = goodsInfoName;
	}

	public void setGoodsClassId(Integer value) {
		this.goodsClassId = value;
	}
	
	public Integer getGoodsClassId() {
		return this.goodsClassId;
	}
	public void setGoodsClassName(String value) {
		this.goodsClassName = value;
	}
	
	public String getGoodsClassName() {
		return this.goodsClassName;
	}
	public void setAmount(Integer value) {
		this.amount = value;
	}
	
	public Integer getAmount() {
		return this.amount;
	}
	public void setMoney(Integer value) {
		this.money = value;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setType(Integer value) {
		this.type = value;
	}
	
	public Integer getType() {
		return this.type;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer value) {
		this.auditYear = value;
	}
	
	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer value) {
		this.auditYearMonth = value;
	}
	
	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

}

