package com.pms.czabsnight.bean;

import java.io.Serializable;
import java.util.Date;

public class NightAuditRoomStatus implements Serializable{
	private Integer nightAuditRoomStatusId  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer roomId  ;
	private String roomNum  ;
	private Integer registId  ;
	private Integer guestNum  ;
	private String guestName  ;
	private Date startTime  ;
	private Date endTime  ;
	private Integer registState  ;
	private Integer roomState  ;
	private Integer roomTypeId  ;
	private String roomTypeName  ;
	private Integer roomPrice  ;
	private Integer businessDay  ;
	private Integer auditYear  ;
	private Integer auditYearMonth  ;

	public NightAuditRoomStatus(){
	}

	public NightAuditRoomStatus(Integer nightAuditRoomStatusId){
		this.nightAuditRoomStatusId = nightAuditRoomStatusId;
	}

	public void setNightAuditRoomStatusId(Integer nightAuditRoomStatusId) {
		this.nightAuditRoomStatusId = nightAuditRoomStatusId;
	}
	
	public Integer getNightAuditRoomStatusId() {
		return this.nightAuditRoomStatusId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setRoomId(Integer roomId) {
		this.roomId = roomId;
	}
	
	public Integer getRoomId() {
		return this.roomId;
	}
	public void setRoomNum(String roomNum) {
		this.roomNum = roomNum;
	}
	
	public String getRoomNum() {
		return this.roomNum;
	}
	public void setRegistId(Integer registId) {
		this.registId = registId;
	}
	
	public Integer getRegistId() {
		return this.registId;
	}
	public void setGuestNum(Integer guestNum) {
		this.guestNum = guestNum;
	}
	
	public Integer getGuestNum() {
		return this.guestNum;
	}
	public void setGuestName(String guestName) {
		this.guestName = guestName;
	}
	
	public String getGuestName() {
		return this.guestName;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}
	
	public Date getStartTime() {
		return this.startTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	
	public Date getEndTime() {
		return this.endTime;
	}
	public void setRegistState(Integer registState) {
		this.registState = registState;
	}
	
	public Integer getRegistState() {
		return this.registState;
	}
	public void setRoomState(Integer roomState) {
		this.roomState = roomState;
	}
	
	public Integer getRoomState() {
		return this.roomState;
	}
	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}
	
	public Integer getRoomTypeId() {
		return this.roomTypeId;
	}
	public void setRoomTypeName(String roomTypeName) {
		this.roomTypeName = roomTypeName;
	}
	
	public String getRoomTypeName() {
		return this.roomTypeName;
	}
	public void setRoomPrice(Integer roomPrice) {
		this.roomPrice = roomPrice;
	}
	
	public Integer getRoomPrice() {
		return this.roomPrice;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer auditYear) {
		this.auditYear = auditYear;
	}
	
	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer auditYearMonth) {
		this.auditYearMonth = auditYearMonth;
	}
	
	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

}

