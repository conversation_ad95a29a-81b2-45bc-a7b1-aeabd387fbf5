package com.pms.czabsnight.bean;

public class DailyReportRoomView {

    private Integer totalRoomCount = 0 ;
    private Integer openRoomCount = 0 ;
    private Integer repairRoomCount = 0 ;
    private Integer noserviceRoomCount = 0  ;
    private Integer selfRoomCount = 0  ;
    private Integer totalAmount = 0  ;

    private Integer averagePrice = 0  ;
    //开房率
    private Double openRate = 0.0;
    //单房贡献率
    private Double revpar = 0.0;

    private Double relaRevpar = 0.0;

    private Integer canUseRoom ;

    private Double canUseRoomTb;

    private Double openRoomCountTb;

    private Double repairRoomCountTb;

    private Double openRateTb;

    private Double averagePriceTb;

    //单房贡献率
    private Double revparTb = 0.0;

    public DailyReportRoomView(){};

    public DailyReportRoomView(NightAuditRoomType nightAuditRoomType){
        this.totalRoomCount = nightAuditRoomType.getTotalRoomCount();
        this.openRoomCount = nightAuditRoomType.getOpenRoomCount();
        this.repairRoomCount = nightAuditRoomType.getRepairRoomCount();
        this.noserviceRoomCount = nightAuditRoomType.getNoserviceRoomCount();
        this.selfRoomCount = nightAuditRoomType.getSelfRoomCount();
        this.totalAmount = nightAuditRoomType.getTotalAmount();
        this.averagePrice = nightAuditRoomType.getAveragePrice();
        this.openRate = nightAuditRoomType.getOpenRate();
    };

    public Integer getTotalRoomCount() {
        return totalRoomCount;
    }

    public void setTotalRoomCount(Integer totalRoomCount) {
        this.totalRoomCount = totalRoomCount;
    }

    public Integer getOpenRoomCount() {
        return openRoomCount;
    }

    public void setOpenRoomCount(Integer openRoomCount) {
        this.openRoomCount = openRoomCount;
    }

    public Integer getRepairRoomCount() {
        return repairRoomCount;
    }

    public void setRepairRoomCount(Integer repairRoomCount) {
        this.repairRoomCount = repairRoomCount;
    }

    public Integer getNoserviceRoomCount() {
        return noserviceRoomCount;
    }

    public void setNoserviceRoomCount(Integer noserviceRoomCount) {
        this.noserviceRoomCount = noserviceRoomCount;
    }

    public Integer getSelfRoomCount() {
        return selfRoomCount;
    }

    public void setSelfRoomCount(Integer selfRoomCount) {
        this.selfRoomCount = selfRoomCount;
    }

    public Integer getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Integer totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getCanUseRoom() {
        return this.totalRoomCount-this.repairRoomCount-this.noserviceRoomCount;
    }

    public void setCanUseRoom(Integer canUseRoom) {
        this.canUseRoom = canUseRoom;
    }

    public Integer getAveragePrice() {
        if(openRoomCount==0){
            return 0;
        }
        return totalAmount/openRoomCount;
    }

    public void setAveragePrice(Integer averagePrice) {
        this.averagePrice = averagePrice;
    }

    public Double getOpenRate() {
        int i = totalRoomCount - noserviceRoomCount - repairRoomCount;
        if(openRoomCount==0||i==0){
            return 0.0;
        }
        return openRoomCount*100.0/i;
    }

    public void setOpenRate(Double openRate) {
        this.openRate = openRate;
    }

    public Double getRevpar() {
        return revpar;
    }

    public void setRevpar(Double revpar) {
        this.revpar = revpar;
    }

    public Double getRelaRevpar() {
        return relaRevpar;
    }

    public void setRelaRevpar(Double relaRevpar) {
        this.relaRevpar = relaRevpar;
    }

    public Double getCanUseRoomTb() {
        return canUseRoomTb;
    }

    public void setCanUseRoomTb(Double canUseRoomTb) {
        this.canUseRoomTb = canUseRoomTb;
    }

    public Double getOpenRoomCountTb() {
        return openRoomCountTb;
    }

    public void setOpenRoomCountTb(Double openRoomCountTb) {
        this.openRoomCountTb = openRoomCountTb;
    }

    public Double getRepairRoomCountTb() {
        return repairRoomCountTb;
    }

    public void setRepairRoomCountTb(Double repairRoomCountTb) {
        this.repairRoomCountTb = repairRoomCountTb;
    }

    public Double getOpenRateTb() {
        return openRateTb;
    }

    public void setOpenRateTb(Double openRateTb) {
        this.openRateTb = openRateTb;
    }

    public Double getAveragePriceTb() {
        return averagePriceTb;
    }

    public void setAveragePriceTb(Double averagePriceTb) {
        this.averagePriceTb = averagePriceTb;
    }

    public Double getRevparTb() {
        return revparTb;
    }

    public void setRevparTb(Double revparTb) {
        this.revparTb = revparTb;
    }
}
