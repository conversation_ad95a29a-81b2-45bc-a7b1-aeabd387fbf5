package com.pms.czabsnight.bean;

import java.io.Serializable;

public class NightAuditDailyRegistRecord implements Serializable{
	private Integer nightAuditDailyRegistRecordId  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private String payClassId  ;
	private String payClassName  ;
	private String payCodeId  ;
	private String payCodeName  ;
	private Integer totalAmount  ;
	private Integer totalMonthAmount  ;
	private Integer totalYearAmount  ;
	private Integer lastMonthAmount  ;
	private Integer lastYearAmount  ;
	private Integer businessDay  ;
	private Integer auditYear  ;
	private Integer auditYearMonth  ;

	public NightAuditDailyRegistRecord(){
	}

	public NightAuditDailyRegistRecord(Integer nightAuditDailyRegistRecordId){
		this.nightAuditDailyRegistRecordId = nightAuditDailyRegistRecordId;
	}

	public void setNightAuditDailyRegistRecordId(Integer nightAuditDailyRegistRecordId) {
		this.nightAuditDailyRegistRecordId = nightAuditDailyRegistRecordId;
	}
	
	public Integer getNightAuditDailyRegistRecordId() {
		return this.nightAuditDailyRegistRecordId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setPayClassId(String payClassId) {
		this.payClassId = payClassId;
	}
	
	public String getPayClassId() {
		return this.payClassId;
	}
	public void setPayClassName(String payClassName) {
		this.payClassName = payClassName;
	}
	
	public String getPayClassName() {
		return this.payClassName;
	}
	public void setPayCodeId(String payCodeId) {
		this.payCodeId = payCodeId;
	}
	
	public String getPayCodeId() {
		return this.payCodeId;
	}
	public void setPayCodeName(String payCodeName) {
		this.payCodeName = payCodeName;
	}
	
	public String getPayCodeName() {
		return this.payCodeName;
	}
	public void setTotalAmount(Integer totalAmount) {
		this.totalAmount = totalAmount;
	}
	
	public Integer getTotalAmount() {
		return this.totalAmount;
	}
	public void setTotalMonthAmount(Integer totalMonthAmount) {
		this.totalMonthAmount = totalMonthAmount;
	}
	
	public Integer getTotalMonthAmount() {
		return this.totalMonthAmount;
	}
	public void setTotalYearAmount(Integer totalYearAmount) {
		this.totalYearAmount = totalYearAmount;
	}
	
	public Integer getTotalYearAmount() {
		return this.totalYearAmount;
	}
	public void setLastMonthAmount(Integer lastMonthAmount) {
		this.lastMonthAmount = lastMonthAmount;
	}
	
	public Integer getLastMonthAmount() {
		return this.lastMonthAmount;
	}
	public void setLastYearAmount(Integer lastYearAmount) {
		this.lastYearAmount = lastYearAmount;
	}
	
	public Integer getLastYearAmount() {
		return this.lastYearAmount;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer auditYear) {
		this.auditYear = auditYear;
	}
	
	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer auditYearMonth) {
		this.auditYearMonth = auditYearMonth;
	}
	
	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

}

