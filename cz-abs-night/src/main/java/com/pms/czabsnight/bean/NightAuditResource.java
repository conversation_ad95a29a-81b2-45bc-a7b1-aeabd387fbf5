package com.pms.czabsnight.bean;

import java.io.Serializable;

public class NightAuditResource implements Serializable {
	private Integer id;
	private Integer resourceId;
	private String resourceName;
	private Integer checkinNum = 0;
	//开房数
	private Integer checkinNumMonth = 0;
	//开房数
	private Integer checkinNumYear = 0;
	private Integer money = 0;
	private Integer goodsMoney = 0;
	private Integer foodsMoney = 0;
	//总消费
	private Long moneyMonth = 0L;
	//
	private Long goodsMoneyMonth = 0L;
	//
	private Long foodsMoneyMonth = 0L;

	private Long moneyYear = 0L;
	//
	private Long goodsMoneyYear = 0L;
	//
	private Long foodsMoneyYear = 0L;

	private Integer hid;
	private Integer hotelGroupId;
	private Integer businessDay;
	private Integer auditYear;
	private Integer auditYearMonth;

	private Integer sumMoney;

	public NightAuditResource() {
	}

	public NightAuditResource(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getResourceId() {
		return resourceId;
	}

	public void setResourceId(Integer resourceId) {
		this.resourceId = resourceId;
	}

	public String getResourceName() {
		return resourceName;
	}

	public void setResourceName(String resourceName) {
		this.resourceName = resourceName;
	}

	public Integer getCheckinNum() {
		return checkinNum;
	}

	public void setCheckinNum(Integer checkinNum) {
		this.checkinNum = checkinNum;
	}

	public Integer getCheckinNumMonth() {
		return checkinNumMonth;
	}

	public void setCheckinNumMonth(Integer checkinNumMonth) {
		this.checkinNumMonth = checkinNumMonth;
	}

	public Integer getCheckinNumYear() {
		return checkinNumYear;
	}

	public void setCheckinNumYear(Integer checkinNumYear) {
		this.checkinNumYear = checkinNumYear;
	}

	public Integer getMoney() {
		return money;
	}

	public void setMoney(Integer money) {
		this.money = money;
	}

	public Integer getGoodsMoney() {
		return goodsMoney;
	}

	public void setGoodsMoney(Integer goodsMoney) {
		this.goodsMoney = goodsMoney;
	}

	public Integer getFoodsMoney() {
		return foodsMoney;
	}

	public void setFoodsMoney(Integer foodsMoney) {
		this.foodsMoney = foodsMoney;
	}

	public Long getMoneyMonth() {
		return moneyMonth;
	}

	public void setMoneyMonth(Long moneyMonth) {
		this.moneyMonth = moneyMonth;
	}

	public Long getGoodsMoneyMonth() {
		return goodsMoneyMonth;
	}

	public void setGoodsMoneyMonth(Long goodsMoneyMonth) {
		this.goodsMoneyMonth = goodsMoneyMonth;
	}

	public Long getFoodsMoneyMonth() {
		return foodsMoneyMonth;
	}

	public void setFoodsMoneyMonth(Long foodsMoneyMonth) {
		this.foodsMoneyMonth = foodsMoneyMonth;
	}

	public Long getMoneyYear() {
		return moneyYear;
	}

	public void setMoneyYear(Long moneyYear) {
		this.moneyYear = moneyYear;
	}

	public Long getGoodsMoneyYear() {
		return goodsMoneyYear;
	}

	public void setGoodsMoneyYear(Long goodsMoneyYear) {
		this.goodsMoneyYear = goodsMoneyYear;
	}

	public Long getFoodsMoneyYear() {
		return foodsMoneyYear;
	}

	public void setFoodsMoneyYear(Long foodsMoneyYear) {
		this.foodsMoneyYear = foodsMoneyYear;
	}

	public Integer getHid() {
		return hid;
	}

	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHotelGroupId() {
		return hotelGroupId;
	}

	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getBusinessDay() {
		return businessDay;
	}

	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}

	public Integer getAuditYear() {
		return auditYear;
	}

	public void setAuditYear(Integer auditYear) {
		this.auditYear = auditYear;
	}

	public Integer getAuditYearMonth() {
		return auditYearMonth;
	}

	public void setAuditYearMonth(Integer auditYearMonth) {
		this.auditYearMonth = auditYearMonth;
	}

	public Integer getSumMoney() {
		return money+foodsMoney+goodsMoney;
	}

	public void setSumMoney(Integer sumMoney) {
		this.sumMoney = sumMoney;
	}
}

