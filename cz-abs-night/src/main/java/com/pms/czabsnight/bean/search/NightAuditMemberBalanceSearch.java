package com.pms.czabsnight.bean.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class NightAuditMemberBalanceSearch extends PageBaseRequest{
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private String balance;
	private String largessBalance;
	private Integer businessDay;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setBalance(String value) {
		this.balance = value;
	}
	
	public String getBalance() {
		return this.balance;
	}
	public void setLargessBalance(String value) {
		this.largessBalance = value;
	}
	
	public String getLargessBalance() {
		return this.largessBalance;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

}

