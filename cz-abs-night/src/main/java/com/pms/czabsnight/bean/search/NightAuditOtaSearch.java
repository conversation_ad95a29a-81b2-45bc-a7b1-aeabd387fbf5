package com.pms.czabsnight.bean.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class NightAuditOtaSearch extends PageBaseRequest{
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private String otaName;
	private Integer otaType;
	private Integer checkinNum;
	private Integer checkinNumMonth;
	private Integer checkinNumYear;
	private Integer roomMoney;
	private Integer goodsMoney;
	private Integer foodsMoney;
	private Integer payMoney;
	private Integer roomMoneyMonth;
	private Integer goodsMoneyMonth;
	private Integer foodsMoneyMonth;
	private Integer businessDay;
	private Integer roomMoneyYear;
	private Integer goodsMoneyYear;
	private Integer foodsMoneyYear;
	private Integer auditYear;
	private Integer auditYearMonth;
	private Integer businessDayMin;
	private Integer businessDayMax;

	public void setId(Integer value) {
		this.id = value;
	}

	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setOtaName(String value) {
		this.otaName = value;
	}

	public String getOtaName() {
		return this.otaName;
	}
	public void setOtaType(Integer value) {
		this.otaType = value;
	}

	public Integer getOtaType() {
		return this.otaType;
	}
	public void setCheckinNum(Integer value) {
		this.checkinNum = value;
	}

	public Integer getCheckinNum() {
		return this.checkinNum;
	}
	public void setCheckinNumMonth(Integer value) {
		this.checkinNumMonth = value;
	}

	public Integer getCheckinNumMonth() {
		return this.checkinNumMonth;
	}
	public void setCheckinNumYear(Integer value) {
		this.checkinNumYear = value;
	}

	public Integer getCheckinNumYear() {
		return this.checkinNumYear;
	}
	public void setRoomMoney(Integer value) {
		this.roomMoney = value;
	}

	public Integer getRoomMoney() {
		return this.roomMoney;
	}
	public void setGoodsMoney(Integer value) {
		this.goodsMoney = value;
	}

	public Integer getGoodsMoney() {
		return this.goodsMoney;
	}
	public void setFoodsMoney(Integer value) {
		this.foodsMoney = value;
	}

	public Integer getFoodsMoney() {
		return this.foodsMoney;
	}
	public void setPayMoney(Integer value) {
		this.payMoney = value;
	}

	public Integer getPayMoney() {
		return this.payMoney;
	}
	public void setRoomMoneyMonth(Integer value) {
		this.roomMoneyMonth = value;
	}

	public Integer getRoomMoneyMonth() {
		return this.roomMoneyMonth;
	}
	public void setGoodsMoneyMonth(Integer value) {
		this.goodsMoneyMonth = value;
	}

	public Integer getGoodsMoneyMonth() {
		return this.goodsMoneyMonth;
	}
	public void setFoodsMoneyMonth(Integer value) {
		this.foodsMoneyMonth = value;
	}

	public Integer getFoodsMoneyMonth() {
		return this.foodsMoneyMonth;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setRoomMoneyYear(Integer value) {
		this.roomMoneyYear = value;
	}

	public Integer getRoomMoneyYear() {
		return this.roomMoneyYear;
	}
	public void setGoodsMoneyYear(Integer value) {
		this.goodsMoneyYear = value;
	}

	public Integer getGoodsMoneyYear() {
		return this.goodsMoneyYear;
	}
	public void setFoodsMoneyYear(Integer value) {
		this.foodsMoneyYear = value;
	}

	public Integer getFoodsMoneyYear() {
		return this.foodsMoneyYear;
	}
	public void setAuditYear(Integer value) {
		this.auditYear = value;
	}

	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer value) {
		this.auditYearMonth = value;
	}

	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

	public Integer getBusinessDayMin() {
		return businessDayMin;
	}

	public void setBusinessDayMin(Integer businessDayMin) {
		this.businessDayMin = businessDayMin;
	}

	public Integer getBusinessDayMax() {
		return businessDayMax;
	}

	public void setBusinessDayMax(Integer businessDayMax) {
		this.businessDayMax = businessDayMax;
	}
}

