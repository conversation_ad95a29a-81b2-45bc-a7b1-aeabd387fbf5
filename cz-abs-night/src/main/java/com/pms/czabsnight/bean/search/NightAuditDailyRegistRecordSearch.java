package com.pms.czabsnight.bean.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("NightAuditDailyRegistRecordSearch")
public class NightAuditDailyRegistRecordSearch extends BaseSearch {
	private Integer nightAuditDailyRegistRecordId;
	private Integer hid;
	private Integer hotelGroupId;
	private String payClassId;
	private String payClassName;
	private String payCodeId;
	private String payCodeName;
	private Integer totalAmount;
	private Integer totalMonthAmount;
	private Integer totalYearAmount;
	private Integer lastMonthAmount;
	private Integer lastYearAmount;
	private Integer businessDay;
	private Integer maxBusinessDay;
	private Integer minBusinessDay;
	private Integer auditYear;
	private Integer auditYearMonth;

	public void setNightAuditDailyRegistRecordId(Integer value) {
		this.nightAuditDailyRegistRecordId = value;
	}

	public Integer getNightAuditDailyRegistRecordId() {
		return this.nightAuditDailyRegistRecordId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setPayClassId(String value) {
		this.payClassId = value;
	}

	public String getPayClassId() {
		return this.payClassId;
	}
	public void setPayClassName(String value) {
		this.payClassName = value;
	}

	public String getPayClassName() {
		return this.payClassName;
	}
	public void setPayCodeId(String value) {
		this.payCodeId = value;
	}

	public String getPayCodeId() {
		return this.payCodeId;
	}
	public void setPayCodeName(String value) {
		this.payCodeName = value;
	}

	public String getPayCodeName() {
		return this.payCodeName;
	}
	public void setTotalAmount(Integer value) {
		this.totalAmount = value;
	}

	public Integer getTotalAmount() {
		return this.totalAmount;
	}
	public void setTotalMonthAmount(Integer value) {
		this.totalMonthAmount = value;
	}

	public Integer getTotalMonthAmount() {
		return this.totalMonthAmount;
	}
	public void setTotalYearAmount(Integer value) {
		this.totalYearAmount = value;
	}

	public Integer getTotalYearAmount() {
		return this.totalYearAmount;
	}
	public void setLastMonthAmount(Integer value) {
		this.lastMonthAmount = value;
	}

	public Integer getLastMonthAmount() {
		return this.lastMonthAmount;
	}
	public void setLastYearAmount(Integer value) {
		this.lastYearAmount = value;
	}

	public Integer getLastYearAmount() {
		return this.lastYearAmount;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setAuditYear(Integer value) {
		this.auditYear = value;
	}

	public Integer getAuditYear() {
		return this.auditYear;
	}
	public void setAuditYearMonth(Integer value) {
		this.auditYearMonth = value;
	}

	public Integer getAuditYearMonth() {
		return this.auditYearMonth;
	}

	public Integer getMaxBusinessDay() {
		return maxBusinessDay;
	}

	public void setMaxBusinessDay(Integer maxBusinessDay) {
		this.maxBusinessDay = maxBusinessDay;
	}

	public Integer getMinBusinessDay() {
		return minBusinessDay;
	}

	public void setMinBusinessDay(Integer minBusinessDay) {
		this.minBusinessDay = minBusinessDay;
	}
}

