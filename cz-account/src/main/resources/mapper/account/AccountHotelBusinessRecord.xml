<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.account.AccountHotelBusinessRecordDao">
	<resultMap id="AccountHotelBusinessRecordMap" type="com.pms.czaccount.bean.account.AccountHotelBusinessRecord">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
		<result property="createUserId" jdbcType="VARCHAR" column="create_user_id"/>
		<result property="createUserName" jdbcType="VARCHAR" column="create_user_name"/>
		<result property="businessType" jdbcType="INTEGER" column="business_type"/>
		<result property="mainId" jdbcType="VARCHAR" column="main_id"/>
		<result property="postData" jdbcType="VARCHAR" column="post_data"/>
		<result property="postFun" jdbcType="VARCHAR" column="post_fun"/>
		<result property="resultCode" jdbcType="INTEGER" column="result_code"/>
		<result property="accountThirdId" jdbcType="VARCHAR" column="account_third_id"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		hotel_group_id,
		create_time,
		create_user_id,
		create_user_name,
		business_type,
		main_id,
		post_data,
		post_fun,
		result_code,
		account_third_id
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="AccountHotelBusinessRecordMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM account_hotel_business_record 
		WHERE 
			id = #{id} 
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM account_hotel_business_record WHERE
		id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czaccount.bean.account.AccountHotelBusinessRecord" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO account_hotel_business_record (
			id,
			hid,
			hotel_group_id,
			create_time,
			create_user_id,
			create_user_name,
			business_type,
			main_id,
			post_data,
			post_fun,
			result_code,
			account_third_id
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{createTime,jdbcType=TIMESTAMP},
			#{createUserId,jdbcType=VARCHAR},
			#{createUserName,jdbcType=VARCHAR},
			#{businessType,jdbcType=INTEGER},
			#{mainId,jdbcType=VARCHAR},
			#{postData,jdbcType=VARCHAR},
			#{postFun,jdbcType=VARCHAR},
			#{resultCode,jdbcType=INTEGER},
			#{accountThirdId,jdbcType=VARCHAR}
		)
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.account.search.AccountHotelBusinessRecordSearch" resultMap="AccountHotelBusinessRecordMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM account_hotel_business_record
		<where>
			<if test="id!=null">
				and id = #{id}
			</if>
			<if test="hid!=null">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="createTime!=null">
				and create_time = #{createTime}
			</if>
			<if test="createUserId!=null and createUserId!=''">
				and create_user_id = #{createUserId}
			</if>
			<if test="createUserName!=null and createUserName!=''">
				and create_user_name = #{createUserName}
			</if>
			<if test="businessType!=null">
				and business_type = #{businessType}
			</if>
			<if test="mainId!=null and mainId!=''">
				and main_id = #{mainId}
			</if>
			<if test="postData!=null and postData!=''">
				and post_data = #{postData}
			</if>
			<if test="postFun!=null and postFun!=''">
				and post_fun = #{postFun}
			</if>
			<if test="resultCode!=null">
				and result_code = #{resultCode}
			</if>
			<if test="accountThirdId!=null and accountThirdId!=''">
				and account_third_id = #{accountThirdId}
			</if>
		</where>
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czaccount.bean.account.AccountHotelBusinessRecord">
		UPDATE account_hotel_business_record 
		<set>
			<if test="hid!=null">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="hotelGroupId!=null">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
			</if>
			<if test="createTime!=null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createUserId!=null and createUserId!=''">
				create_user_id = #{createUserId,jdbcType=VARCHAR},
			</if>
			<if test="createUserName!=null and createUserName!=''">
				create_user_name = #{createUserName,jdbcType=VARCHAR},
			</if>
			<if test="businessType!=null">
				business_type = #{businessType,jdbcType=INTEGER},
			</if>
			<if test="mainId!=null and mainId!=''">
				main_id = #{mainId,jdbcType=VARCHAR},
			</if>
			<if test="postData!=null and postData!=''">
				post_data = #{postData,jdbcType=VARCHAR},
			</if>
			<if test="postFun!=null and postFun!=''">
				post_fun = #{postFun,jdbcType=VARCHAR},
			</if>
			<if test="resultCode!=null">
				result_code = #{resultCode,jdbcType=INTEGER},
			</if>
			<if test="accountThirdId!=null and accountThirdId!=''">
				account_third_id = #{accountThirdId,jdbcType=VARCHAR}
			</if>
		</set>
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</update>
	
</mapper>