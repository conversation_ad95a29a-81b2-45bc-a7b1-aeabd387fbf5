<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.SysWechatRedpackSettingDao">
	<resultMap id="SysWechatRedpackSettingMap" type="com.pms.czaccount.bean.pay.SysWechatRedpackSetting">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="money" jdbcType="INTEGER" column="money"/>
		<result property="totalAmount" jdbcType="INTEGER" column="total_amount"/>
		<result property="minThreshold" jdbcType="DOUBLE" column="min_threshold"/>
		<result property="maxThreshold" jdbcType="DOUBLE" column="max_threshold"/>
		<result property="startTime" jdbcType="VARCHAR" column="start_time"/>
		<result property="endTime" jdbcType="VARCHAR" column="end_time"/>
		<result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
		<result property="createUser" jdbcType="VARCHAR" column="create_user"/>
		<result property="createUserId" jdbcType="VARCHAR" column="create_user_id"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		money,
		total_amount,
		min_threshold,
		max_threshold,
		start_time,
		end_time,
		create_time,
		create_user,
		create_user_id
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="SysWechatRedpackSettingMap">
		SELECT
			<include refid="BaseColumn" />
		FROM sys_wechat_redpack_setting
		WHERE
			id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="deleteSysWechatRedpackSetting" parameterType="java.lang.Integer">
		DELETE FROM sys_wechat_redpack_setting WHERE
		id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="saveSysWechatRedpackSetting" parameterType="com.pms.czaccount.bean.pay.SysWechatRedpackSetting" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO sys_wechat_redpack_setting (
			id,
			money,
			total_amount,
			min_threshold,
			max_threshold,
			start_time,
			end_time,
			create_time,
			create_user,
			create_user_id
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{money,jdbcType=INTEGER},
			#{totalAmount,jdbcType=INTEGER},
			#{minThreshold,jdbcType=DOUBLE},
			#{maxThreshold,jdbcType=DOUBLE},
			#{startTime,jdbcType=VARCHAR},
			#{endTime,jdbcType=VARCHAR},
			#{createTime,jdbcType=TIMESTAMP},
			#{createUser,jdbcType=VARCHAR},
			#{createUserId,jdbcType=VARCHAR}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.pay.search.SysWechatRedpackSettingSearch" resultMap="SysWechatRedpackSettingMap">
		SELECT
			<include refid="BaseColumn" />
		FROM sys_wechat_redpack_setting
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="money!=null and money!=''">
				and money = #{money}
			</if>
			<if test="totalAmount!=null and totalAmount!=''">
				and total_amount = #{totalAmount}
			</if>
			<if test="minThreshold!=null and minThreshold!=''">
				and min_threshold = #{minThreshold}
			</if>
			<if test="maxThreshold!=null and maxThreshold!=''">
				and max_threshold = #{maxThreshold}
			</if>
			<if test="startTime!=null ">
				and start_time = #{startTime}
			</if>
			<if test="endTime!=null">
				and end_time = #{endTime}
			</if>
			<if test="searchTime!=null ">
				and end_time > #{searchTime}
				and #{searchTime} >= start_time
			</if>
			<if test="createTime!=null">
				and create_time = #{createTime}
			</if>
			<if test="createUser!=null and createUser!=''">
				and create_user = #{createUser}
			</if>
			<if test="createUserId!=null and createUserId!=''">
				and create_user_id = #{createUserId}
			</if>
		</where>
		ORDER BY money
	</select>

	<!-- 更新 -->
	<update id="editSysWechatRedpackSetting" parameterType="com.pms.czaccount.bean.pay.SysWechatRedpackSetting">
		UPDATE sys_wechat_redpack_setting
			<set>
				<if test="money!=null and money!=''">
				money = #{money,jdbcType=INTEGER},
				</if>
				<if test="totalAmount!=null and totalAmount!=''">
				total_amount = #{totalAmount,jdbcType=INTEGER},
				</if>
				<if test="minThreshold!=null and minThreshold!=''">
				min_threshold = #{minThreshold,jdbcType=DOUBLE},
				</if>
				<if test="maxThreshold!=null and maxThreshold!=''">
				max_threshold = #{maxThreshold,jdbcType=DOUBLE},
				</if>
				<if test="startTime!=null ">
				start_time = #{startTime,jdbcType=VARCHAR},
				</if>
				<if test="endTime!=null">
				end_time = #{endTime,jdbcType=VARCHAR},
				</if>
			</set>
		WHERE
			id = #{id,jdbcType=INTEGER}
	</update>



</mapper>
