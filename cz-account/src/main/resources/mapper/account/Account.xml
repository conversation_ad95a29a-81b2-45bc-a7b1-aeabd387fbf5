<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.account.AccountDao">
    <resultMap id="AccountMap" type="com.pms.czaccount.bean.account.Account">
        <result property="accountId" jdbcType="VARCHAR" column="account_id"/>
        <result property="otherPmsAccountId" jdbcType="VARCHAR" column="other_pms_account_id"/>
        <result property="bookingId" jdbcType="INTEGER" column="booking_id"/>
        <result property="registId" jdbcType="INTEGER" column="regist_id"/>
        <result property="goodDumbId" jdbcType="INTEGER" column="good_dumb_id"/>
        <result property="price" jdbcType="INTEGER" column="price"/>
        <result property="payType" jdbcType="INTEGER" column="pay_type"/>
        <result property="hid" jdbcType="INTEGER" column="hid"/>
        <result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
        <result property="payClassId" jdbcType="INTEGER" column="pay_class_id"/>
        <result property="payClassName" jdbcType="VARCHAR" column="pay_class_name"/>
        <result property="payCodeId" jdbcType="VARCHAR" column="pay_code_id"/>
        <result property="payCodeName" jdbcType="VARCHAR" column="pay_code_name"/>
        <result property="roomInfoId" jdbcType="INTEGER" column="room_info_id"/>
        <result property="roomNum" jdbcType="VARCHAR" column="room_num"/>
        <result property="roomTypeId" jdbcType="INTEGER" column="room_type_id"/>
        <result property="accountCode" jdbcType="VARCHAR" column="account_code"/>
        <result property="isSale" jdbcType="INTEGER" column="is_sale"/>
        <result property="goodId" jdbcType="INTEGER" column="good_id"/>
        <result property="goodName" jdbcType="VARCHAR" column="good_name"/>
        <result property="uintPrice" jdbcType="INTEGER" column="uint_price"/>
        <result property="saleNum" jdbcType="INTEGER" column="sale_num"/>
        <result property="registState" jdbcType="INTEGER" column="regist_state"/>
        <result property="unit" jdbcType="VARCHAR" column="unit"/>
        <result property="remark" jdbcType="VARCHAR" column="remark"/>
        <result property="settleAccountTime" jdbcType="TIMESTAMP" column="settle_account_time"/>
        <result property="thirdAccoutId" jdbcType="VARCHAR" column="third_accout_id"/>
        <result property="reason" jdbcType="VARCHAR" column="reason"/>
        <result property="accountIndex" jdbcType="INTEGER" column="account_index"/>
        <result property="companyId" jdbcType="INTEGER" column="company_id"/>
        <result property="commissionRate" jdbcType="INTEGER" column="commission_rate"/>
        <result property="companyName" jdbcType="VARCHAR" column="company_name"/>
        <result property="memberId" jdbcType="INTEGER" column="member_id"/>
        <result property="accountYear" jdbcType="INTEGER" column="account_year"/>
        <result property="accountYearMonth" jdbcType="INTEGER" column="account_year_month"/>
        <result property="businessDay" jdbcType="INTEGER" column="business_day"/>
        <result property="classId" jdbcType="INTEGER" column="class_id"/>
        <result property="isCancel" jdbcType="INTEGER" column="is_cancel"/>
        <result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
        <result property="createUserId" jdbcType="VARCHAR" column="create_user_id"/>
        <result property="createUserName" jdbcType="VARCHAR" column="create_user_name"/>
        <result property="updateTime" jdbcType="TIMESTAMP" column="update_time"/>
        <result property="updateUserId" jdbcType="VARCHAR" column="update_user_id"/>
        <result property="updateUserName" jdbcType="VARCHAR" column="update_user_name"/>
        <result property="refundPrice" jdbcType="INTEGER" column="refund_price"/>
        <result property="thirdRefundState" jdbcType="INTEGER" column="third_refund_state"/>
        <result property="accountType" jdbcType="INTEGER" column="account_type"/>
        <result property="registPersonId" jdbcType="INTEGER" column="regist_person_id"/>
        <result property="registPersonName" jdbcType="VARCHAR" column="regist_person_name"/>
        <result property="begRegistId" jdbcType="INTEGER" column="beg_regist_id"/>
        <result property="begRegistPersonId" jdbcType="INTEGER" column="beg_regist_person_id"/>
        <result property="groupAccount" jdbcType="INTEGER" column="group_account"/>
    </resultMap>
    <sql id="BaseColumn">
        account_id
        ,
		other_pms_account_id,
		booking_id,
		regist_id,
		good_dumb_id,
		price,
		pay_type,
		hid,
		hotel_group_id,
		pay_class_id,
		pay_class_name,
		pay_code_id,
		pay_code_name,
		room_info_id,
		room_num,
		room_type_id,
		account_code,
		is_sale,
		good_id,
		good_name,
		uint_price,
		sale_num,
		regist_state,
		unit,
		remark,
		settle_account_time,
		third_accout_id,
		reason,
		account_index,
		company_id,
		commission_rate,
		company_name,
		member_id,
		account_year,
		account_year_month,
		business_day,
		class_id,
		is_cancel,
		create_time,
		create_user_id,
		create_user_name,
		update_time,
		update_user_id,
		update_user_name,
		refund_price,
		third_refund_state,
		account_type,
		regist_person_id,
		regist_person_name,
        beg_regist_id,
		beg_regist_person_id,
		group_account
    </sql>
    <select id="selectById" parameterType="java.lang.String" resultMap="AccountMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM account
        WHERE
        account_id = #{id}
    </select>

    <!-- 按Id删除 -->
    <delete id="deleteAccount" parameterType="java.lang.String">
        DELETE
        FROM account
        WHERE account_id = #{id}
    </delete>
    <!-- 插入 -->
    <insert id="saveAccount" parameterType="com.pms.czaccount.bean.account.Account" useGeneratedKeys="true"
            keyProperty="accountId">
        INSERT INTO account (account_id,
                             other_pms_account_id,
                             booking_id,
                             regist_id,
                             team_code_id,
                             good_dumb_id,
                             price,
                             pay_type,
                             hid,
                             hotel_group_id,
                             pay_class_id,
                             pay_class_name,
                             pay_code_id,
                             pay_code_name,
                             room_info_id,
                             room_num,
                             room_type_id,
                             account_code,
                             is_sale,
                             good_id,
                             good_name,
                             uint_price,
                             sale_num,
                             regist_state,
                             unit,
                             remark,
                             settle_account_time,
                             third_accout_id,
                             reason,
                             account_index,
                             company_id,
                             commission_rate,
                             company_name,
                             member_id,
                             account_year,
                             account_year_month,
                             business_day,
                             class_id,
                             is_cancel,
                             create_time,
                             create_user_id,
                             create_user_name,
                             update_time,
                             update_user_id,
                             update_user_name,
                             refund_price,
                             third_refund_state,
                             account_type,
                             regist_person_id,
                             regist_person_name,
                             beg_regist_id,
                             beg_regist_person_id,
                             group_account)
        VALUES (#{accountId,jdbcType=VARCHAR},
                #{otherPmsAccountId,jdbcType=VARCHAR},
                #{bookingId,jdbcType=INTEGER},
                #{registId,jdbcType=INTEGER},
                #{teamCodeId,jdbcType=INTEGER},
                #{goodDumbId,jdbcType=INTEGER},
                #{price,jdbcType=INTEGER},
                #{payType,jdbcType=INTEGER},
                #{hid,jdbcType=INTEGER},
                #{hotelGroupId,jdbcType=INTEGER},
                #{payClassId,jdbcType=INTEGER},
                #{payClassName,jdbcType=VARCHAR},
                #{payCodeId,jdbcType=VARCHAR},
                #{payCodeName,jdbcType=VARCHAR},
                #{roomInfoId,jdbcType=INTEGER},
                #{roomNum,jdbcType=VARCHAR},
                #{roomTypeId,jdbcType=INTEGER},
                #{accountCode,jdbcType=VARCHAR},
                #{isSale,jdbcType=INTEGER},
                #{goodId,jdbcType=INTEGER},
                #{goodName,jdbcType=VARCHAR},
                #{uintPrice,jdbcType=INTEGER},
                #{saleNum,jdbcType=INTEGER},
                #{registState,jdbcType=INTEGER},
                #{unit,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{settleAccountTime,jdbcType=TIMESTAMP},
                #{thirdAccoutId,jdbcType=VARCHAR},
                #{reason,jdbcType=VARCHAR},
                #{accountIndex,jdbcType=INTEGER},
                #{companyId,jdbcType=INTEGER},
                #{commissionRate,jdbcType=INTEGER},
                #{companyName,jdbcType=VARCHAR},
                #{memberId,jdbcType=INTEGER},
                #{accountYear,jdbcType=INTEGER},
                #{accountYearMonth,jdbcType=INTEGER},
                #{businessDay,jdbcType=INTEGER},
                #{classId,jdbcType=INTEGER},
                #{isCancel,jdbcType=INTEGER},
                #{createTime,jdbcType=TIMESTAMP},
                #{createUserId,jdbcType=VARCHAR},
                #{createUserName,jdbcType=VARCHAR},
                #{updateTime,jdbcType=TIMESTAMP},
                #{updateUserId,jdbcType=VARCHAR},
                #{updateUserName,jdbcType=VARCHAR},
                #{refundPrice,jdbcType=INTEGER},
                #{thirdRefundState,jdbcType=INTEGER},
                #{accountType,jdbcType=INTEGER},
                #{registPersonId,jdbcType=INTEGER},
                #{registPersonName,jdbcType=VARCHAR},
                #{begRegistId,jdbcType=INTEGER},
                #{begRegistPersonId,jdbcType=INTEGER},
                #{groupAccount,jdbcType=INTEGER})

    </insert>

    <!-- 批量添加 -->
    <update id="saveAccountList" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            INSERT INTO account (
            account_id,
            other_pms_account_id,
            booking_id,
            regist_id,
            team_code_id,
            good_dumb_id,
            price,
            pay_type,
            hid,
            hotel_group_id,
            pay_class_id,
            pay_class_name,
            pay_code_id,
            pay_code_name,
            room_info_id,
            room_num,
            room_type_id,
            account_code,
            is_sale,
            good_id,
            good_name,
            uint_price,
            sale_num,
            regist_state,
            unit,
            remark,
            settle_account_time,
            third_accout_id,
            reason,
            account_index,
            company_id,
            commission_rate,
            company_name,
            member_id,
            account_year,
            account_year_month,
            business_day,
            class_id,
            is_cancel,
            create_time,
            create_user_id,
            create_user_name,
            update_time,
            update_user_id,
            update_user_name,
            refund_price,
            third_refund_state,
            account_type,
            regist_person_id,
            regist_person_name,
            beg_regist_id,
            beg_regist_person_id,
            group_account
            ) VALUES (
            #{item.accountId,jdbcType=VARCHAR},
            #{item.otherPmsAccountId,jdbcType=VARCHAR},
            #{item.bookingId,jdbcType=INTEGER},
            #{item.registId,jdbcType=INTEGER},
            #{item.teamCodeId,jdbcType=INTEGER},
            #{item.goodDumbId,jdbcType=INTEGER},
            #{item.price,jdbcType=INTEGER},
            #{item.payType,jdbcType=INTEGER},
            #{item.hid,jdbcType=INTEGER},
            #{item.hotelGroupId,jdbcType=INTEGER},
            #{item.payClassId,jdbcType=INTEGER},
            #{item.payClassName,jdbcType=VARCHAR},
            #{item.payCodeId,jdbcType=VARCHAR},
            #{item.payCodeName,jdbcType=VARCHAR},
            #{item.roomInfoId,jdbcType=INTEGER},
            #{item.roomNum,jdbcType=VARCHAR},
            #{item.roomTypeId,jdbcType=INTEGER},
            #{item.accountCode,jdbcType=VARCHAR},
            #{item.isSale,jdbcType=INTEGER},
            #{item.goodId,jdbcType=INTEGER},
            #{item.goodName,jdbcType=VARCHAR},
            #{item.uintPrice,jdbcType=INTEGER},
            #{item.saleNum,jdbcType=INTEGER},
            #{item.registState,jdbcType=INTEGER},
            #{item.unit,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.settleAccountTime,jdbcType=TIMESTAMP},
            #{item.thirdAccoutId,jdbcType=VARCHAR},
            #{item.reason,jdbcType=VARCHAR},
            #{item.accountIndex,jdbcType=INTEGER},
            #{item.companyId,jdbcType=INTEGER},
            #{item.commissionRate,jdbcType=INTEGER},
            #{item.companyName,jdbcType=VARCHAR},
            #{item.memberId,jdbcType=INTEGER},
            #{item.accountYear,jdbcType=INTEGER},
            #{item.accountYearMonth,jdbcType=INTEGER},
            #{item.businessDay,jdbcType=INTEGER},
            #{item.classId,jdbcType=INTEGER},
            #{item.isCancel,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.createUserId,jdbcType=VARCHAR},
            #{item.createUserName,jdbcType=VARCHAR},
            #{item.updateTime,jdbcType=TIMESTAMP},
            #{item.updateUserId,jdbcType=VARCHAR},
            #{item.updateUserName,jdbcType=VARCHAR},
            #{item.refundPrice,jdbcType=INTEGER},
            #{item.thirdRefundState,jdbcType=INTEGER},
            #{item.accountType,jdbcType=INTEGER},
            #{item.registPersonId,jdbcType=INTEGER},
            #{item.registPersonName,jdbcType=VARCHAR},
            #{item.begRegistId,jdbcType=INTEGER},
            #{item.begRegistPersonId,jdbcType=INTEGER},
            #{item.groupAccount,jdbcType=INTEGER}
            )
        </foreach>
    </update>

    <select id="selectBySearch" parameterType="com.pms.czaccount.bean.account.search.AccountSearch"
            resultMap="AccountMap">
        SELECT
        <include refid="BaseColumn"/>
        FROM account
        <where>
            <if test="accountId!=null and accountId!=''">
                and account_id = #{accountId}
            </if>
            <if test="otherPmsAccountId!=null and otherPmsAccountId!=''">
                and other_pms_account_id = #{otherPmsAccountId}
            </if>
            <if test="bookingId!=null">
                and booking_id = #{bookingId}
            </if>
            <if test="bookingIds!=null">
                and booking_id in (${bookingIds})
            </if>
            <if test="registId!=null">
                and regist_id = #{registId}
            </if>
            <if test="registIds!=null">
                and regist_id in (${registIds})
            </if>
            <if test="regInKeys!=null and regInKeys!=''">
                and regist_id in (${regInKeys})
            </if>
            <if test="goodDumbId!=null and goodDumbId!=''">
                and good_dumb_id = #{goodDumbId}
            </if>
            <if test="price!=null">
                and price = #{price}
            </if>
            <if test="payType!=null and payType!=''">
                and pay_type = #{payType}
            </if>
            <if test="hid!=null and hid!=''">
                and hid = #{hid}
            </if>
            <if test="hotelGroupId!=null and hotelGroupId!=''">
                and hotel_group_id = #{hotelGroupId}
            </if>
            <if test="payClassId!=null and payClassId!=''">
                and pay_class_id = #{payClassId}
            </if>
            <if test="payClassName!=null and payClassName!=''">
                and pay_class_name = #{payClassName}
            </if>
            <if test="payCodeId!=null and payCodeId!=''">
                and pay_code_id = #{payCodeId}
            </if>
            <if test="payCodeName!=null and payCodeName!=''">
                and pay_code_name = #{payCodeName}
            </if>
            <if test="roomInfoId!=null and roomInfoId!=''">
                and room_info_id = #{roomInfoId}
            </if>
            <if test="roomNum!=null and roomNum!=''">
                and room_num = #{roomNum}
            </if>
            <if test="roomTypeId!=null and roomTypeId!=''">
                and room_type_id = #{roomTypeId}
            </if>
            <if test="accountCode!=null and accountCode!=''">
                and account_code = #{accountCode}
            </if>
            <if test="isSale!=null">
                and is_sale = #{isSale}
            </if>
            <if test="isDump!=null and isDump==1">
                and good_dumb_id is null
            </if>
            <if test="isDump!=null and isDump!=1">
                and good_dumb_id is not null
            </if>
            <if test="goodId!=null and goodId!=''">
                and good_id = #{goodId}
            </if>
            <if test="goodName!=null and goodName!=''">
                and good_name = #{goodName}
            </if>
            <if test="uintPrice!=null">
                and uint_price = #{uintPrice}
            </if>
            <if test="saleNum!=null and saleNum!=''">
                and sale_num = #{saleNum}
            </if>
            <if test="registState!=null">
                and regist_state = #{registState}
            </if>
            <if test="unit!=null and unit!=''">
                and unit = #{unit}
            </if>
            <if test="remark!=null and remark!=''">
                and remark = #{remark}
            </if>
            <if test="settleAccountTime!=null and settleAccountTime!=''">
                and settle_account_time = #{settleAccountTime}
            </if>
            <if test="thirdAccoutId!=null and thirdAccoutId!=''">
                and third_accout_id = #{thirdAccoutId}
            </if>
            <if test="reason!=null and reason!=''">
                and reason = #{reason}
            </if>
            <if test="accountIndex!=null and accountIndex!=''">
                and account_index = #{accountIndex}
            </if>
            <if test="companyId!=null and companyId!=''">
                and company_id = #{companyId}
            </if>
            <if test="commissionRate!=null and commissionRate!=''">
                and commission_rate = #{commissionRate}
            </if>
            <if test="companyName!=null and companyName!=''">
                and company_name = #{companyName}
            </if>
            <if test="memberId!=null and memberId!=''">
                and member_id = #{memberId}
            </if>
            <if test="accountYear!=null and accountYear!=''">
                and account_year = #{accountYear}
            </if>
            <if test="accountYearMonth!=null and accountYearMonth!=''">
                and account_year_month = #{accountYearMonth}
            </if>
            <if test="businessDay!=null and businessDay!=''">
                and business_day = #{businessDay}
            </if>
            <if test="classId!=null and classId!=''">
                and class_id = #{classId}
            </if>
            <if test="isCancel!=null">
                and is_cancel = #{isCancel}
            </if>
            <if test="createTime!=null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId!=null and createUserId!=''">
                and create_user_id = #{createUserId}
            </if>
            <if test="createUserName!=null and createUserName!=''">
                and create_user_name = #{createUserName}
            </if>
            <if test="updateTime!=null">
                and update_time = #{updateTime}
            </if>
            <if test="updateUserId!=null and updateUserId!=''">
                and update_user_id = #{updateUserId}
            </if>
            <if test="updateUserName!=null and updateUserName!=''">
                and update_user_name = #{updateUserName}
            </if>
            <if test="refundPrice!=null ">
                and refund_price = #{refundPrice}
            </if>
            <if test="thirdRefundState!=null and thirdRefundState!=''">
                and third_refund_state = #{thirdRefundState}
            </if>
            <if test="accountType!=null and accountType!=''">
                and account_type = #{accountType}
            </if>
            <if test="registPersonId!=null ">
                and regist_person_id = #{registPersonId}
            </if>

            <if test="registPersonName!=null and registPersonName!=''">
                and regist_person_name = #{registPersonName}
            </if>

            <if test="registStates!=null and registStates!=''">
                and regist_state in ${registStates}
            </if>

            <if test="accountStateList != null and accountStateList.size>0">
                and regist_state in
                <foreach collection="accountStateList" item="os" open="(" separator="," close=")">
                    #{os}
                </foreach>
            </if>

            <if test="classIds!=null and classIds!=''">
                and class_id in ${classIds}
            </if>
            <if test="registIdIsNull!=null">
                and (regist_id is null or 1 > regist_id )
            </if>
            <if test="begRegistId!=null ">
                and beg_regist_id = #{begRegistId}
            </if>
            <if test="begRegistPersonId!=null">
                and beg_regist_person_id = #{begRegistPersonId}
            </if>
            <if test="groupAccount!=null ">
                and group_account = #{groupAccount}
            </if>
            <if test="orGroupAccountId!=null and orGroupAccountId>0">
                OR (group_account =1 and #{orGroupAccountId} = regist_id )
            </if>
            <if test="startTime!=null">
                and create_time >= #{startTime}
            </if>
            <if test="endTime!=null">
                and #{endTime}>= create_time
            </if>
        </where>
        <if test="startPage!=null and pageSize!=null">
            LIMIT ${startPage},${pageSize}
        </if>
    </select>

    <select id="selectBySearchCount" parameterType="com.pms.czaccount.bean.account.search.AccountSearch"
            resultType="java.lang.Integer">
        SELECT
        count(account_id) as sumCount
        FROM account
        <where>
            <if test="accountId!=null and accountId!=''">
                and account_id = #{accountId}
            </if>
            <if test="otherPmsAccountId!=null and otherPmsAccountId!=''">
                and other_pms_account_id = #{otherPmsAccountId}
            </if>
            <if test="bookingId!=null">
                and booking_id = #{bookingId}
            </if>
            <if test="bookingIds!=null">
                and booking_id in (${bookingIds})
            </if>
            <if test="registId!=null">
                and regist_id = #{registIds}
            </if>
            <if test="registIds!=null">
                and regist_id in (${registIds})
            </if>
            <if test="regInKeys!=null and regInKeys!=''">
                and regist_id in (${regInKeys})
            </if>
            <if test="goodDumbId!=null and goodDumbId!=''">
                and good_dumb_id = #{goodDumbId}
            </if>
            <if test="price!=null">
                and price = #{price}
            </if>
            <if test="payType!=null and payType!=''">
                and pay_type = #{payType}
            </if>
            <if test="hid!=null and hid!=''">
                and hid = #{hid}
            </if>
            <if test="hotelGroupId!=null and hotelGroupId!=''">
                and hotel_group_id = #{hotelGroupId}
            </if>
            <if test="payClassId!=null and payClassId!=''">
                and pay_class_id = #{payClassId}
            </if>
            <if test="payClassName!=null and payClassName!=''">
                and pay_class_name = #{payClassName}
            </if>
            <if test="payCodeId!=null and payCodeId!=''">
                and pay_code_id = #{payCodeId}
            </if>
            <if test="payCodeName!=null and payCodeName!=''">
                and pay_code_name = #{payCodeName}
            </if>
            <if test="roomInfoId!=null and roomInfoId!=''">
                and room_info_id = #{roomInfoId}
            </if>
            <if test="roomNum!=null and roomNum!=''">
                and room_num = #{roomNum}
            </if>
            <if test="roomTypeId!=null and roomTypeId!=''">
                and room_type_id = #{roomTypeId}
            </if>
            <if test="accountCode!=null and accountCode!=''">
                and account_code = #{accountCode}
            </if>
            <if test="isSale!=null">
                and is_sale = #{isSale}
            </if>
            <if test="isDump==1">
                and good_dumb_id is null
            </if>
            <if test="isDump!=1">
                and good_dumb_id is not null
            </if>
            <if test="goodId!=null and goodId!=''">
                and good_id = #{goodId}
            </if>
            <if test="goodName!=null and goodName!=''">
                and good_name = #{goodName}
            </if>
            <if test="uintPrice!=null">
                and uint_price = #{uintPrice}
            </if>
            <if test="saleNum!=null and saleNum!=''">
                and sale_num = #{saleNum}
            </if>
            <if test="registState!=null and registState!=''">
                and regist_state = #{registState}
            </if>
            <if test="unit!=null and unit!=''">
                and unit = #{unit}
            </if>
            <if test="remark!=null and remark!=''">
                and remark = #{remark}
            </if>
            <if test="settleAccountTime!=null and settleAccountTime!=''">
                and settle_account_time = #{settleAccountTime}
            </if>
            <if test="thirdAccoutId!=null and thirdAccoutId!=''">
                and third_accout_id = #{thirdAccoutId}
            </if>
            <if test="reason!=null and reason!=''">
                and reason = #{reason}
            </if>
            <if test="accountIndex!=null and accountIndex!=''">
                and account_index = #{accountIndex}
            </if>
            <if test="companyId!=null and companyId!=''">
                and company_id = #{companyId}
            </if>
            <if test="commissionRate!=null and commissionRate!=''">
                and commission_rate = #{commissionRate}
            </if>
            <if test="companyName!=null and companyName!=''">
                and company_name = #{companyName}
            </if>
            <if test="memberId!=null and memberId!=''">
                and member_id = #{memberId}
            </if>
            <if test="accountYear!=null and accountYear!=''">
                and account_year = #{accountYear}
            </if>
            <if test="accountYearMonth!=null and accountYearMonth!=''">
                and account_year_month = #{accountYearMonth}
            </if>
            <if test="businessDay!=null and businessDay!=''">
                and business_day = #{businessDay}
            </if>
            <if test="classId!=null and classId!=''">
                and class_id = #{classId}
            </if>
            <if test="isCancel!=null">
                and is_cancel = #{isCancel}
            </if>
            <if test="createTime!=null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId!=null and createUserId!=''">
                and create_user_id = #{createUserId}
            </if>
            <if test="createUserName!=null and createUserName!=''">
                and create_user_name = #{createUserName}
            </if>
            <if test="updateTime!=null">
                and update_time = #{updateTime}
            </if>
            <if test="updateUserId!=null and updateUserId!=''">
                and update_user_id = #{updateUserId}
            </if>
            <if test="updateUserName!=null and updateUserName!=''">
                and update_user_name = #{updateUserName}
            </if>
            <if test="refundPrice!=null ">
                and refund_price = #{refundPrice}
            </if>
            <if test="thirdRefundState!=null and thirdRefundState!=''">
                and third_refund_state = #{thirdRefundState}
            </if>
            <if test="accountType!=null and accountType!=''">
                and account_type = #{accountType}
            </if>
            <if test="registPersonId!=null ">
                and regist_person_id = #{registPersonId}
            </if>

            <if test="registPersonName!=null and registPersonName!=''">
                and regist_person_name = #{registPersonName}
            </if>

            <if test="registStates!=null and registStates!=''">
                and regist_state in ${registStates}
            </if>

            <if test="accountStateList != null and accountStateList.size>0">
                and regist_state in
                <foreach collection="accountStateList" item="os" open="(" separator="," close=")">
                    #{os}
                </foreach>
            </if>

            <if test="classIds!=null and classIds!=''">
                and class_id in ${classIds}
            </if>
            <if test="begRegistId!=null ">
                and beg_regist_id = #{begRegistId}
            </if>
            <if test="begRegistPersonId!=null">
                and beg_regist_person_id = #{begRegistPersonId}
            </if>
            <if test="groupAccount!=null ">
                and group_account = #{groupAccount}
            </if>
            <if test="startTime!=null">
                and create_time >= #{startTime}
            </if>
            <if test="endTime!=null">
                and #{endTime}>= create_time
            </if>
        </where>
    </select>

    <select id="sumPrice" parameterType="com.pms.czaccount.bean.account.search.AccountSearch"
            resultType="java.lang.Integer">
        SELECT
        IFNULL(sum(price),0) as sumPrice
        FROM account
        <where>
            <if test="accountId!=null and accountId!=''">
                and account_id = #{accountId}
            </if>
            <if test="otherPmsAccountId!=null and otherPmsAccountId!=''">
                and other_pms_account_id = #{otherPmsAccountId}
            </if>
            <if test="bookingId!=null">
                and booking_id = #{bookingId}
            </if>
            <if test="registId!=null">
                and regist_id = #{registId}
            </if>
            <if test="goodDumbId!=null and goodDumbId!=''">
                and good_dumb_id = #{goodDumbId}
            </if>
            <if test="price!=null">
                and price = #{price}
            </if>
            <if test="payOrSale!=null and payOrSale == 1 ">
                and price >=0
            </if>
            <if test="payOrSale!=null and payOrSale == -1 ">
                and 0 >= price
            </if>
            <if test="payType!=null and payType!=''">
                and pay_type = #{payType}
            </if>
            <if test="hid!=null and hid!=''">
                and hid = #{hid}
            </if>
            <if test="hotelGroupId!=null and hotelGroupId!=''">
                and hotel_group_id = #{hotelGroupId}
            </if>
            <if test="payClassId!=null and payClassId!=''">
                and pay_class_id = #{payClassId}
            </if>
            <if test="payClassName!=null and payClassName!=''">
                and pay_class_name = #{payClassName}
            </if>
            <if test="payCodeId!=null and payCodeId!=''">
                and pay_code_id = #{payCodeId}
            </if>
            <if test="payCodeName!=null and payCodeName!=''">
                and pay_code_name = #{payCodeName}
            </if>
            <if test="roomInfoId!=null and roomInfoId!=''">
                and room_info_id = #{roomInfoId}
            </if>
            <if test="roomNum!=null and roomNum!=''">
                and room_num = #{roomNum}
            </if>
            <if test="roomTypeId!=null and roomTypeId!=''">
                and room_type_id = #{roomTypeId}
            </if>
            <if test="accountCode!=null and accountCode!=''">
                and account_code = #{accountCode}
            </if>
            <if test="isSale!=null">
                and is_sale = #{isSale}
            </if>
            <if test="goodId!=null and goodId!=''">
                and good_id = #{goodId}
            </if>
            <if test="goodName!=null and goodName!=''">
                and good_name = #{goodName}
            </if>
            <if test="uintPrice!=null">
                and uint_price = #{uintPrice}
            </if>
            <if test="saleNum!=null and saleNum!=''">
                and sale_num = #{saleNum}
            </if>
            <if test="registState!=null and registState!=''">
                and regist_state = #{registState}
            </if>
            <if test="unit!=null and unit!=''">
                and unit = #{unit}
            </if>
            <if test="remark!=null and remark!=''">
                and remark = #{remark}
            </if>
            <if test="settleAccountTime!=null and settleAccountTime!=''">
                and settle_account_time = #{settleAccountTime}
            </if>
            <if test="thirdAccoutId!=null and thirdAccoutId!=''">
                and third_accout_id = #{thirdAccoutId}
            </if>
            <if test="reason!=null and reason!=''">
                and reason = #{reason}
            </if>
            <if test="accountIndex!=null and accountIndex!=''">
                and account_index = #{accountIndex}
            </if>
            <if test="companyId!=null and companyId!=''">
                and company_id = #{companyId}
            </if>
            <if test="commissionRate!=null and commissionRate!=''">
                and commission_rate = #{commissionRate}
            </if>
            <if test="companyName!=null and companyName!=''">
                and company_name = #{companyName}
            </if>
            <if test="memberId!=null and memberId!=''">
                and member_id = #{memberId}
            </if>
            <if test="accountYear!=null and accountYear!=''">
                and account_year = #{accountYear}
            </if>
            <if test="accountYearMonth!=null and accountYearMonth!=''">
                and account_year_month = #{accountYearMonth}
            </if>
            <if test="businessDay!=null and businessDay!=''">
                and business_day = #{businessDay}
            </if>
            <if test="classId!=null and classId!=''">
                and class_id = #{classId}
            </if>
            <if test="isCancel!=null and isCancel!=''">
                and is_cancel = #{isCancel}
            </if>
            <if test="createTime!=null">
                and create_time = #{createTime}
            </if>
            <if test="createUserId!=null and createUserId!=''">
                and create_user_id = #{createUserId}
            </if>
            <if test="createUserName!=null and createUserName!=''">
                and create_user_name = #{createUserName}
            </if>
            <if test="updateTime!=null">
                and update_time = #{updateTime}
            </if>
            <if test="updateUserId!=null and updateUserId!=''">
                and update_user_id = #{updateUserId}
            </if>
            <if test="updateUserName!=null and updateUserName!=''">
                and update_user_name = #{updateUserName}
            </if>
            <if test="refundPrice!=null ">
                and refund_price = #{refundPrice}
            </if>
            <if test="thirdRefundState!=null and thirdRefundState!=''">
                and third_refund_state = #{thirdRefundState}
            </if>
            <if test="accountType!=null and accountType!=''">
                and account_type = #{accountType}
            </if>
            <if test="registPersonId!=null ">
                and regist_person_id = #{registPersonId}
            </if>
            <if test="registPersonName!=null and registPersonName!=''">
                and regist_person_name = #{registPersonName}
            </if>
        </where>
    </select>

    <!-- 更新 -->
    <update id="editAccount" parameterType="com.pms.czaccount.bean.account.Account">
        UPDATE account
        <set>
            <if test="otherPmsAccountId!=null and otherPmsAccountId!=''">
                other_pms_account_id = #{otherPmsAccountId,jdbcType=VARCHAR},
            </if>
            <if test="bookingId!=null">
                booking_id = #{bookingId,jdbcType=INTEGER},
            </if>
            <if test="registId!=null">
                regist_id = #{registId,jdbcType=INTEGER},
            </if>
            <if test="goodDumbId!=null and goodDumbId!=''">
                good_dumb_id = #{goodDumbId,jdbcType=INTEGER},
            </if>
            <if test="price!=null">
                price = #{price,jdbcType=INTEGER},
            </if>
            <if test="payType!=null and payType!=''">
                pay_type = #{payType,jdbcType=INTEGER},
            </if>
            <if test="hid!=null and hid!=''">
                hid = #{hid,jdbcType=INTEGER},
            </if>
            <if test="hotelGroupId!=null and hotelGroupId!=''">
                hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
            </if>
            <if test="payClassId!=null and payClassId!=''">
                pay_class_id = #{payClassId,jdbcType=INTEGER},
            </if>
            <if test="payClassName!=null and payClassName!=''">
                pay_class_name = #{payClassName,jdbcType=VARCHAR},
            </if>
            <if test="payCodeId!=null and payCodeId!=''">
                pay_code_id = #{payCodeId,jdbcType=VARCHAR},
            </if>
            <if test="payCodeName!=null and payCodeName!=''">
                pay_code_name = #{payCodeName,jdbcType=VARCHAR},
            </if>
            <if test="roomInfoId!=null and roomInfoId!=''">
                room_info_id = #{roomInfoId,jdbcType=INTEGER},
            </if>
            <if test="roomNum!=null and roomNum!=''">
                room_num = #{roomNum,jdbcType=VARCHAR},
            </if>
            <if test="roomTypeId!=null and roomTypeId!=''">
                room_type_id = #{roomTypeId,jdbcType=INTEGER},
            </if>
            <if test="accountCode!=null and accountCode!=''">
                account_code = #{accountCode,jdbcType=VARCHAR},
            </if>
            <if test="isSale!=null">
                is_sale = #{isSale,jdbcType=INTEGER},
            </if>
            <if test="goodId!=null and goodId!=''">
                good_id = #{goodId,jdbcType=INTEGER},
            </if>
            <if test="goodName!=null and goodName!=''">
                good_name = #{goodName,jdbcType=VARCHAR},
            </if>
            <if test="uintPrice!=null">
                uint_price = #{uintPrice,jdbcType=INTEGER},
            </if>
            <if test="saleNum!=null and saleNum!=''">
                sale_num = #{saleNum,jdbcType=INTEGER},
            </if>
            <if test="registState!=null">
                regist_state = #{registState,jdbcType=INTEGER},
            </if>
            <if test="unit!=null and unit!=''">
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="remark!=null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="settleAccountTime!=null">
                settle_account_time = #{settleAccountTime,jdbcType=TIMESTAMP},
            </if>
            <if test="thirdAccoutId!=null">
                third_accout_id = #{thirdAccoutId,jdbcType=VARCHAR},
            </if>
            <if test="reason!=null and reason!=''">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="accountIndex!=null and accountIndex!=''">
                account_index = #{accountIndex,jdbcType=INTEGER},
            </if>
            <if test="companyId!=null and companyId!=''">
                company_id = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="commissionRate!=null and commissionRate!=''">
                commission_rate = #{commissionRate,jdbcType=INTEGER},
            </if>
            <if test="companyName!=null and companyName!=''">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="memberId!=null and memberId!=''">
                member_id = #{memberId,jdbcType=INTEGER},
            </if>
            <if test="accountYear!=null and accountYear!=''">
                account_year = #{accountYear,jdbcType=INTEGER},
            </if>
            <if test="accountYearMonth!=null and accountYearMonth!=''">
                account_year_month = #{accountYearMonth,jdbcType=INTEGER},
            </if>
            <if test="businessDay!=null and businessDay!=''">
                business_day = #{businessDay,jdbcType=INTEGER},
            </if>
            <if test="classId!=null and classId!=''">
                class_id = #{classId,jdbcType=INTEGER},
            </if>
            <if test="isCancel!=null">
                is_cancel = #{isCancel,jdbcType=INTEGER},
            </if>
            <if test="createTime!=null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId!=null and createUserId!=''">
                create_user_id = #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createUserName!=null and createUserName!=''">
                create_user_name = #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime!=null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId!=null and updateUserId!=''">
                update_user_id = #{updateUserId,jdbcType=VARCHAR},
            </if>
            <if test="updateUserName!=null and updateUserName!=''">
                update_user_name = #{updateUserName,jdbcType=VARCHAR},
            </if>
            <if test="refundPrice!=null ">
                refund_price = #{refundPrice,jdbcType=INTEGER},
            </if>
            <if test="thirdRefundState!=null ">
                third_refund_state = #{thirdRefundState,jdbcType=INTEGER},
            </if>
            <if test="accountType!=null and accountType!=''">
                account_type = #{accountType,jdbcType=INTEGER},
            </if>
            <if test="registPersonId!=null ">
                regist_person_id = #{registPersonId,jdbcType=INTEGER},
            </if>
            <if test="registPersonName!=null and registPersonName!=''">
                regist_person_name = #{registPersonName,jdbcType=VARCHAR},
            </if>
            <if test="begRegistId!=null">
                beg_regist_id = #{begRegistId,jdbcType=INTEGER},
            </if>
            <if test="begRegistPersonId!=null">
                beg_regist_person_id = #{begRegistPersonId,jdbcType=INTEGER},
            </if>
            <if test="groupAccount!=null">
                group_account = #{groupAccount,jdbcType=INTEGER}
            </if>
        </set>
        WHERE
        account_id = #{accountId,jdbcType=VARCHAR}
    </update>

    <!-- 批量更新 -->
    <update id="editAccountList" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            UPDATE account
            <set>
                <if test="item.otherPmsAccountId!=null and item.otherPmsAccountId!=''">
                    other_pms_account_id = #{item.otherPmsAccountId,jdbcType=VARCHAR},
                </if>
                <if test="item.bookingId!=null">
                    booking_id = #{item.bookingId,jdbcType=INTEGER},
                </if>
                <if test="item.registId!=null">
                    regist_id = #{item.registId,jdbcType=INTEGER},
                </if>
                <if test="item.goodDumbId!=null and item.goodDumbId!=''">
                    good_dumb_id = #{item.goodDumbId,jdbcType=INTEGER},
                </if>
                <if test="item.price!=null">
                    price = #{item.price,jdbcType=INTEGER},
                </if>
                <if test="item.payType!=null and item.payType!=''">
                    pay_type = #{item.payType,jdbcType=INTEGER},
                </if>
                <if test="item.hid!=null and item.hid!=''">
                    hid = #{item.hid,jdbcType=INTEGER},
                </if>
                <if test="item.hotelGroupId!=null and item.hotelGroupId!=''">
                    hotel_group_id = #{item.hotelGroupId,jdbcType=INTEGER},
                </if>
                <if test="item.payClassId!=null and item.payClassId!=''">
                    pay_class_id = #{item.payClassId,jdbcType=INTEGER},
                </if>
                <if test="item.payClassName!=null and item.payClassName!=''">
                    pay_class_name = #{item.payClassName,jdbcType=VARCHAR},
                </if>
                <if test="item.payCodeId!=null and item.payCodeId!=''">
                    pay_code_id = #{item.payCodeId,jdbcType=VARCHAR},
                </if>
                <if test="item.payCodeName!=null and item.payCodeName!=''">
                    pay_code_name = #{item.payCodeName,jdbcType=VARCHAR},
                </if>
                <if test="item.roomInfoId!=null and item.roomInfoId!=''">
                    room_info_id = #{item.roomInfoId,jdbcType=INTEGER},
                </if>
                <if test="item.roomNum!=null and item.roomNum!=''">
                    room_num = #{item.roomNum,jdbcType=VARCHAR},
                </if>
                <if test="item.roomTypeId!=null and item.roomTypeId!=''">
                    room_type_id = #{item.roomTypeId,jdbcType=INTEGER},
                </if>
                <if test="item.accountCode!=null and item.accountCode!=''">
                    account_code = #{item.accountCode,jdbcType=VARCHAR},
                </if>
                <if test="item.isSale!=null">
                    is_sale = #{item.isSale,jdbcType=INTEGER},
                </if>
                <if test="item.goodId!=null and item.goodId!=''">
                    good_id = #{item.goodId,jdbcType=INTEGER},
                </if>
                <if test="item.goodName!=null and item.goodName!=''">
                    good_name = #{item.goodName,jdbcType=VARCHAR},
                </if>
                <if test="item.uintPrice!=null">
                    uint_price = #{item.uintPrice,jdbcType=INTEGER},
                </if>
                <if test="item.saleNum!=null and item.saleNum!=''">
                    sale_num = #{item.saleNum,jdbcType=INTEGER},
                </if>
                <if test="item.registState!=null">
                    regist_state = #{item.registState,jdbcType=INTEGER},
                </if>
                <if test="item.unit!=null and item.unit!=''">
                    unit = #{item.unit,jdbcType=VARCHAR},
                </if>
                <if test="item.remark!=null and item.remark!=''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.settleAccountTime!=null">
                    settle_account_time = #{item.settleAccountTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.thirdAccoutId!=null and item.thirdAccoutId!=''">
                    third_accout_id = #{item.thirdAccoutId,jdbcType=VARCHAR},
                </if>
                <if test="item.reason!=null and item.reason!=''">
                    reason = #{item.reason,jdbcType=VARCHAR},
                </if>
                <if test="item.accountIndex!=null and item.accountIndex!=''">
                    account_index = #{item.accountIndex,jdbcType=INTEGER},
                </if>
                <if test="item.companyId!=null and item.companyId!=''">
                    company_id = #{item.companyId,jdbcType=INTEGER},
                </if>
                <if test="item.commissionRate!=null and item.commissionRate!=''">
                    commission_rate = #{item.commissionRate,jdbcType=INTEGER},
                </if>
                <if test="item.companyName!=null and item.companyName!=''">
                    company_name = #{item.companyName,jdbcType=VARCHAR},
                </if>
                <if test="item.memberId!=null and item.memberId!=''">
                    member_id = #{item.memberId,jdbcType=INTEGER},
                </if>
                <if test="item.isCancel!=null">
                    is_cancel = #{item.isCancel,jdbcType=INTEGER},
                </if>
                <if test="item.updateTime!=null">
                    update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateUserId!=null and item.updateUserId!=''">
                    update_user_id = #{item.updateUserId,jdbcType=VARCHAR},
                </if>
                <if test="item.updateUserName!=null and item.updateUserName!=''">
                    update_user_name = #{item.updateUserName,jdbcType=VARCHAR},
                </if>
                <if test="item.refundPrice!=null ">
                    refund_price = #{item.refundPrice,jdbcType=INTEGER},
                </if>
                <if test="item.thirdRefundState!=null ">
                    third_refund_state = #{item.thirdRefundState,jdbcType=INTEGER},
                </if>
                <if test="item.accountType!=null and item.accountType!=''">
                    account_type = #{item.accountType,jdbcType=INTEGER},
                </if>
                <if test="item.registPersonId!=null ">
                    regist_person_id = #{item.registPersonId,jdbcType=INTEGER},
                </if>
                <if test="item.registPersonName!=null and item.registPersonName!=''">
                    regist_person_name = #{item.registPersonName,jdbcType=VARCHAR},
                </if>
                <if test="item.begRegistId!=null">
                    beg_regist_id = #{item.begRegistId},
                </if>
                <if test="item.begRegistPersonId!=null ">
                    beg_regist_person_id = #{item.begRegistPersonId},
                </if>
                <if test="item.groupAccount!=null ">
                    group_account = #{item.groupAccount}
                </if>
            </set>
            WHERE
            account_id = #{item.accountId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 前台入账明细表 -->
    <select id="accountDetails" resultType="java.util.Map" parameterType="java.util.Map">
        SELECT
        a.account_id,
        a.other_pms_account_id,
        a.booking_id,
        a.regist_id,
        a.good_dumb_id,
        a.price,
        a.pay_type,
        a.account_type,
        a.hid,
        a.hotel_group_id,
        a.pay_class_id,
        a.pay_class_name,
        a.pay_code_id,
        a.pay_code_name,
        a.room_info_id,
        a.account_code,
        a.is_sale,
        a.good_id,
        a.good_name,
        a.uint_price,
        a.sale_num,
        a.regist_state,
        a.unit,
        a.remark,
        a.settle_account_time,
        a.third_accout_id,
        a.reason,
        a.account_index,
        a.company_id,
        a.commission_rate,
        a.company_name,
        a.member_id,
        a.account_year,
        a.account_year_month,
        a.business_day,
        a.class_id,
        a.is_cancel,
        r.regist_id,
        r.other_pms_regist_id,
        r.other_pms_order_id,
        r.room_type_id,
        a.third_refund_state,
        a.refund_price,
        a.create_time,
        r.state,
        r.sn,
        r.room_num,
        t.person_name,
        r.room_num_id,
        r.room_type_name,
        a.regist_person_id,
        a.regist_person_name,
        a.beg_regist_id,
        a.beg_regist_person_id,
        a.group_account
        FROM
        account AS a
        INNER JOIN regist AS r ON a.regist_id = r.regist_id
        INNER JOIN (SELECT
        regist_person.hid,
        regist_person.person_name,
        regist_person.regist_id
        FROM
        regist_person
        WHERE hid = ${hid}
        AND is_other = 0
        ) as t ON t.regist_id = r.regist_id
        WHERE
        pay_type = 2
        AND a.hid = ${hid}
        <if test=" startTime!=null and startTime!=''">
            AND a.create_time > #{startTime}
        </if>
        <if test=" endTime!=null and endTime!=''">
            AND #{endTime} >a.create_time
        </if>
        <if test=" registId!=null">
            AND r.other_pms_regist_id = #{registId}
        </if>
        <if test="roomNum!=null and roomNum!=''">
            AND r.room_num = #{roomNum}
        </if>
        <if test="payClassId!=null and payClassId!=''">
            AND pay_class_id = #{payClassId}
        </if>
        ORDER BY a.create_time DESC
        <if test="page!=null and pageSize!=null">
            LIMIT ${page},${pageSize}
        </if>
    </select>

    <!-- 前台入账明细表 总条数 -->
    <select id="accountDetailsCount" resultType="java.util.Map" parameterType="java.util.Map">
        SELECT
        COUNT(1) as sumCount,
        SUM(a.price) as sumPrice
        FROM
        account AS a
        INNER JOIN regist AS r ON a.regist_id = r.regist_id
        WHERE
        pay_type = 2
        AND a.hid = ${hid}
        <if test=" startTime!=null and startTime!=''">
            AND a.create_time > #{startTime}
        </if>
        <if test=" endTime!=null and endTime!=''">
            AND #{endTime} >a.create_time
        </if>
        <if test=" registId!=null">
            AND r.other_pms_regist_id = #{registId}
        </if>
        <if test="roomNum!=null and roomNum!=''">
            AND r.room_num = #{roomNum}
        </if>
        <if test="payClassId!=null and payClassId!=''">
            AND pay_class_id = #{payClassId}
        </if>
    </select>

    <!--查询账务汇总-->
    <select id="accountSummary" resultType="com.pms.czpmsutils.request.AccountSummary"
            parameterType="com.pms.czpmsutils.request.search.AccountSummarySearch">
        SELECT
        COUNT( 1 ) AS sumCount,
        SUM( price ) AS sumMoney,
        pay_code_id AS payCodeId,
        pay_code_name AS payCodeName ,
        pay_class_id as payClassId,
        pay_class_name as payClassName,
        account_year as accountYear,
        account_year_month as accountMonth,
        room_type_id as roomTypeId,
        regist_id as registId,
        regist_state as rgistState
        FROM
        account
        WHERE
        hid = #{hid}
        and is_cancel = 0 and regist_id != -1
        <if test="accountYear!=null">
            and account_year = #{accountYear}
        </if>
        <if test="accountYearMonth!=null ">
            and account_year_month = #{accountYearMonth}
        </if>
        <if test="businessDay!=null ">
            and business_day = #{businessDay}
        </if>
        <if test="businessDayMin!=null ">
            and business_day >= #{businessDayMin}
        </if>
        <if test="businessDayMax!=null ">
            and #{businessDayMax}>=business_day
        </if>
        <if test="accountType!=null">
            and account_type = #{accountType}
        </if>
        <if test="registIds != null and registIds.size>0">
            and regist_id in
            <foreach collection="registIds" item="os" open="(" separator="," close=")">
                #{os}
            </foreach>
        </if>
        <if test="registStates != null and registStates.size>0">
            and regist_state in
            <foreach collection="registStates" item="os" open="(" separator="," close=")">
                #{os}
            </foreach>
        </if>
        <if test="bookingIds != null and bookingIds.size>0">
            and booking_id in
            <foreach collection="bookingIds" item="os" open="(" separator="," close=")">
                #{os}
            </foreach>
        </if>
        <if test="classId!=null">
            and class_id = #{classId}
        </if>
        <if test="payType!=null">
            and pay_type = #{payType}
        </if>
        <if test="registId!=null">
            and regist_id = #{registId}
        </if>
        <if test="regInKeys!=null and regInKeys!=''">
            and regist_id in (${regInKeys})
        </if>
        <if test="registState!=null">
            and regist_state = #{registState}
        </if>
        <if test="noPre!=null and noPre==1">
            and pay_code_id != '9100' and pay_code_id != '9620'
        </if>
        <if test="groupType!=null">
            GROUP BY
        </if>
        <if test="groupType==1">
            pay_code_id
        </if>
        <if test="groupType==2">
            pay_class_id
        </if>
        <if test="groupType==3">
            room_type_id
        </if>
        <if test="groupType==4">
            regist_id
        </if>
        <if test="groupType==5">
            pay_code_id,regist_state
        </if>
        <if test="groupType==6">
            pay_class_id,regist_id
        </if>
    </select>


    <select id="searchAccountList" resultMap="AccountMap"
            parameterType="com.pms.czpmsutils.request.AccountSearchRequest">
        SELECT
        account_id,
        other_pms_account_id,
        booking_id,
        regist_id,
        good_dumb_id,
        price,
        pay_type,
        hid,
        hotel_group_id,
        pay_class_id,
        pay_class_name,
        pay_code_id,
        pay_code_name,
        room_info_id,
        room_num,
        room_type_id,
        account_code,
        is_sale,
        good_id,
        good_name,
        uint_price,
        sale_num,
        regist_state,
        unit,
        remark,
        settle_account_time,
        third_accout_id,
        reason,
        account_index,
        company_id,
        commission_rate,
        company_name,
        member_id,
        account_year,
        account_year_month,
        business_day,
        class_id,
        is_cancel,
        create_time,
        create_user_id,
        create_user_name,
        update_time,
        update_user_id,
        update_user_name,
        refund_price,
        third_refund_state,
        account_type,
        regist_person_id,
        regist_person_name,
        beg_regist_id,
        beg_regist_person_id,
        group_account
        FROM account
        <where>
            <if test="accountId!=null and accountId!=''">
                and account_id = #{accountId}
            </if>
            <if test="otherPmsAccountId!=null and otherPmsAccountId!=''">
                and other_pms_account_id = #{otherPmsAccountId}
            </if>
            <if test="bookingId!=null">
                and booking_id = #{bookingId}
            </if>
            <if test="registIdIsNull!=null">
                and (regist_id is null or 1 > regist_id )
            </if>

            <if test="registId!=null">
                and regist_id = #{registId}
            </if>
            <if test="teamCodeId!=null">
                and team_code_id = #{teamCodeId}
            </if>
            <if test="goodDumbId!=null and goodDumbId!=''">
                and good_dumb_id = #{goodDumbId}
            </if>
            <if test="price!=null">
                and price = #{price}
            </if>
            <if test="payType!=null and payType!=''">
                and pay_type = #{payType}
            </if>
            <if test="hid!=null and hid!=''">
                and hid = #{hid}
            </if>
            <if test="hotelGroupId!=null and hotelGroupId!=''">
                and hotel_group_id = #{hotelGroupId}
            </if>
            <if test="payClassId!=null and payClassId!=''">
                and pay_class_id = #{payClassId}
            </if>
            <if test="payClassName!=null and payClassName!=''">
                and pay_class_name = #{payClassName}
            </if>
            <if test="payCodeId!=null and payCodeId!=''">
                and pay_code_id = #{payCodeId}
            </if>
            <if test="payCodeName!=null and payCodeName!=''">
                and pay_code_name = #{payCodeName}
            </if>
            <if test="roomInfoId!=null and roomInfoId!=''">
                and room_info_id = #{roomInfoId}
            </if>
            <if test="roomNum!=null and roomNum!=''">
                and room_num = #{roomNum}
            </if>
            <if test="roomTypeId!=null and roomTypeId!=''">
                and room_type_id = #{roomTypeId}
            </if>
            <if test="accountCode!=null and accountCode!=''">
                and account_code = #{accountCode}
            </if>
            <if test="isSale!=null">
                and is_sale = #{isSale}
            </if>
            <if test="goodId!=null and goodId!=''">
                and good_id = #{goodId}
            </if>
            <if test="goodName!=null and goodName!=''">
                and good_name = #{goodName}
            </if>
            <if test="uintPrice!=null">
                and uint_price = #{uintPrice}
            </if>
            <if test="saleNum!=null and saleNum!=''">
                and sale_num = #{saleNum}
            </if>
            <if test="registState!=null and registState!=''">
                and regist_state = #{registState}
            </if>
            <if test="unit!=null and unit!=''">
                and unit = #{unit}
            </if>
            <if test="remark!=null and remark!=''">
                and remark = #{remark}
            </if>
            <if test="thirdAccoutId!=null and thirdAccoutId!=''">
                and third_accout_id = #{thirdAccoutId}
            </if>
            <if test="reason!=null and reason!=''">
                and reason = #{reason}
            </if>
            <if test="accountIndex!=null and accountIndex!=''">
                and account_index = #{accountIndex}
            </if>
            <if test="companyId!=null and companyId!=''">
                and company_id = #{companyId}
            </if>
            <if test="commissionRate!=null and commissionRate!=''">
                and commission_rate = #{commissionRate}
            </if>
            <if test="companyName!=null and companyName!=''">
                and company_name = #{companyName}
            </if>
            <if test="memberId!=null and memberId!=''">
                and member_id = #{memberId}
            </if>
            <if test="accountYear!=null and accountYear!=''">
                and account_year = #{accountYear}
            </if>
            <if test="accountYearMonth!=null and accountYearMonth!=''">
                and account_year_month = #{accountYearMonth}
            </if>
            <if test="businessDay!=null and businessDay!=''">
                and business_day = #{businessDay}
            </if>

            <if test="businessDayStart!=null and businessDayStart!=''">
                and business_day >= #{businessDayStart}
            </if>

            <if test="businessDayEnd!=null and businessDayEnd!=''">
                and #{businessDayEnd} >= business_day
            </if>

            <if test="classId!=null and classId!=''">
                and class_id = #{classId}
            </if>
            <if test="isCancel!=null">
                and is_cancel = #{isCancel}
            </if>
            <if test="createUserId!=null and createUserId!=''">
                and create_user_id = #{createUserId}
            </if>
            <if test="createUserName!=null and createUserName!=''">
                and create_user_name = #{createUserName}
            </if>
            <if test="updateUserId!=null and updateUserId!=''">
                and update_user_id = #{updateUserId}
            </if>
            <if test="updateUserName!=null and updateUserName!=''">
                and update_user_name = #{updateUserName}
            </if>
            <if test="refundPrice!=null ">
                and refund_price = #{refundPrice}
            </if>
            <if test="thirdRefundState!=null">
                and third_refund_state = #{thirdRefundState}
            </if>
            <if test="accountType!=null and accountType!=''">
                and account_type = #{accountType}
            </if>
            <if test="registPersonId!=null ">
                and regist_person_id = #{registPersonId}
            </if>

            <if test="registPersonName!=null and registPersonName!=''">
                and regist_person_name = #{registPersonName}
            </if>
            <if test="begRegistId!=null">
                and beg_regist_id = #{begRegistId}
            </if>
            <if test="begRegistPersonId!=null ">
                and beg_regist_person_id = #{begRegistPersonId}
            </if>
            <if test="groupAccount!=null ">
                and group_account = #{groupAccount}
            </if>
            <if test="searchValue!=null and searchValue!=''">
                and (
                regist_person_name like concat('%', #{searchValue} , '%')
                or room_num like concat('%', #{searchValue} ,'%')
                or create_user_name like concat('%', #{searchValue} ,'%')
                or pay_code_name like concat('%', #{searchValue} , '%')
                )
            </if>
            <if test="registStates != null and registStates.size>0">
                and regist_state in
                <foreach collection="registStates" item="os" open="(" separator="," close=")">
                    #{os}
                </foreach>
            </if>

            <if test="classIds != null and classIds.size>0">
                and class_id in
                <foreach collection="classIds" item="os" open="(" separator="," close=")">
                    #{os}
                </foreach>
            </if>
            <if test="registIds != null and registIds.size>0">
                and regist_id in
                <foreach collection="registIds" item="os" open="(" separator="," close=")">
                    #{os}
                </foreach>
            </if>
            <if test="orGroupAccountId!=null and orGroupAccountId>0">
                OR (group_account =1 and #{orGroupAccountId} = regist_id )
            </if>
        </where>
        ORDER BY account.create_time DESC
    </select>

    <select id="accountBalance" parameterType="com.pms.czaccount.bean.account.search.AccountSearch"
            resultType="com.pms.czaccount.bean.account.AccountBalance">
        SELECT a.price                        AS sumSale,
               (b.price + IFNULL(d.price, 0)) AS sumPay,
               ((
                   a.price - b.price - IFNULL(d.price, 0)
                   ))                         AS diff
        FROM (
                 SELECT SUM(price) AS price,
                        business_day,
                        hid
                 FROM account
                 WHERE hid = #{hid}
                   AND #{businessDay} > business_day
                   AND pay_type = 1
                   AND is_cancel = 0
             ) AS a
                 INNER JOIN (
            SELECT SUM(price) AS price,
                   business_day,
                   hid
            FROM account
            WHERE hid = #{hid}
              AND #{businessDay} > business_day
              AND pay_type = 2
              AND pay_code_id!='9100'
		AND pay_code_id!='9620'
		AND is_cancel = 0
        ) AS b ON a.hid = b.hid
                 LEFT JOIN (
            SELECT IFNULL(SUM(price), 0) AS price,
                   business_day,
                   hid
            FROM account
            WHERE hid = #{hid}
              AND #{businessDay} > business_day
              AND pay_type = 2
              AND pay_code_id = '9100'
              AND third_refund_state = 2
              AND is_cancel = 0
        ) as d ON a.hid = d.hid;


    </select>

</mapper>
