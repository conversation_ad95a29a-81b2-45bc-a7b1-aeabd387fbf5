<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.AccountResetDao">
	<resultMap id="AccountResetMap" type="com.pms.czaccount.bean.pay.AccountReset">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="mainId" jdbcType="VARCHAR" column="main_id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="type" jdbcType="INTEGER" column="type"/>
		<result property="money" jdbcType="INTEGER" column="money"/>
		<result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
		<result property="createUserName" jdbcType="VARCHAR" column="create_user_name"/>
		<result property="createUserId" jdbcType="VARCHAR" column="create_user_id"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		main_id,
		hid,
		hotel_group_id,
		type,
		money,
		create_time,
		create_user_name,
		create_user_id
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="AccountResetMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM account_reset 
		WHERE 
			id = #{id} 
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM account_reset WHERE
		id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czaccount.bean.pay.AccountReset" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO account_reset (
			main_id,
			hid,
			hotel_group_id,
			type,
			money,
			create_time,
			create_user_name,
			create_user_id
		) VALUES (
					 #{mainId,jdbcType=VARCHAR},
					 #{hid,jdbcType=INTEGER},
					 #{hotelGroupId,jdbcType=INTEGER},
					 #{type,jdbcType=INTEGER},
					 #{money,jdbcType=INTEGER},
					 #{createTime,jdbcType=TIMESTAMP},
					 #{createUserName,jdbcType=VARCHAR},
					 #{createUserId,jdbcType=VARCHAR}
				 )
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.pay.search.AccountResetSearch" resultMap="AccountResetMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM account_reset
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="mainId!=null and mainId!=''">
				and main_id = #{mainId}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="type!=null and type!=''">
				and type = #{type}
			</if>
			<if test="money!=null and money!=''">
				and money = #{money}
			</if>
			<if test="createTime!=null and createTime!=''">
				and create_time = #{createTime}
			</if>
			<if test="createUserName!=null and createUserName!=''">
				and create_user_name = #{createUserName}
			</if>
			<if test="createUserId!=null and createUserId!=''">
				and create_user_id = #{createUserId}
			</if>
		</where>
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czaccount.bean.pay.AccountReset">
		UPDATE account_reset
		<set>
			<if test="mainId!=null and mainId!=''">
				main_id = #{mainId,jdbcType=VARCHAR},
			</if>
			<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
			</if>
			<if test="type!=null and type!=''">
				type = #{type,jdbcType=INTEGER},
			</if>
			<if test="money!=null and money!=''">
				money = #{money,jdbcType=INTEGER},
			</if>
			<if test="createTime!=null and createTime!=''">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createUserName!=null and createUserName!=''">
				create_user_name = #{createUserName,jdbcType=VARCHAR},
			</if>
			<if test="createUserId!=null and createUserId!=''">
				create_user_id = #{createUserId,jdbcType=VARCHAR}
			</if>
		</set>
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</update>
	
</mapper>