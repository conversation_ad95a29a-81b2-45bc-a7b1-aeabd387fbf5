<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.AlipayAccountDao">
	<resultMap id="AlipayAccountMap" type="com.pms.czaccount.bean.pay.AlipayAccount">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="privateKey" jdbcType="VARCHAR" column="private_key"/>
		<result property="url" jdbcType="VARCHAR" column="url"/>
		<result property="appId" jdbcType="VARCHAR" column="app_id"/>
		<result property="charSet" jdbcType="VARCHAR" column="char_set"/>
		<result property="format" jdbcType="VARCHAR" column="format"/>
		<result property="signType" jdbcType="VARCHAR" column="sign_type"/>
		<result property="alipayPublicKey" jdbcType="VARCHAR" column="alipay_publicKey"/>
		<result property="updateTime" jdbcType="TIMESTAMP" column="update_time"/>
		<result property="updateUserId" jdbcType="VARCHAR" column="update_user_id"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		private_key,
		url,
		app_id,
		char_set,
		format,
		sign_type,
		alipay_publicKey,
		update_time,
		update_user_id
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="AlipayAccountMap">
		SELECT
			<include refid="BaseColumn" />
		FROM alipay_account
		WHERE
			id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="deleteAlipayAccount" parameterType="java.lang.Integer">
		DELETE FROM alipay_account WHERE
		id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="saveAlipayAccount" parameterType="com.pms.czaccount.bean.pay.AlipayAccount" useGeneratedKeys="true" keyProperty="id">
	INSERT INTO alipay_account (

			hid,
			private_key,
			url,
			app_id,
			char_set,
			format,
			sign_type,
			alipay_publicKey,
			update_time,
			update_user_id
		) VALUES (

			#{hid,jdbcType=INTEGER},
			#{privateKey,jdbcType=VARCHAR},
			#{url,jdbcType=VARCHAR},
			#{appId,jdbcType=VARCHAR},
			#{charSet,jdbcType=VARCHAR},
			#{format,jdbcType=VARCHAR},
			#{signType,jdbcType=VARCHAR},
			#{alipayPublicKey,jdbcType=VARCHAR},
			#{updateTime,jdbcType=TIMESTAMP},
			#{updateUserId,jdbcType=VARCHAR}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.pay.search.AlipayAccountSearch" resultMap="AlipayAccountMap">
		SELECT
		<include refid="BaseColumn" />
		FROM alipay_account
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="privateKey!=null and privateKey!=''">
				and private_key = #{privateKey}
			</if>
			<if test="url!=null and url!=''">
				and url = #{url}
			</if>
			<if test="appId!=null and appId!=''">
				and app_id = #{appId}
			</if>
			<if test="charSet!=null and charSet!=''">
				and char_set = #{charSet}
			</if>
			<if test="format!=null and format!=''">
				and format = #{format}
			</if>
			<if test="signType!=null and signType!=''">
				and sign_type = #{signType}
			</if>
			<if test="alipayPublicKey!=null and alipayPublicKey!=''">
				and alipay_publicKey = #{alipayPublicKey}
			</if>
			<if test="updateTime!=null">
				and update_time = #{updateTime}
			</if>
			<if test="updateUserId!=null and updateUserId!=''">
				and update_user_id = #{updateUserId}
			</if>
		</where>
	</select>

	<!-- 更新 -->
	<update id="editAlipayAccount" parameterType="com.pms.czaccount.bean.pay.AlipayAccount">
		UPDATE alipay_account
		<set>
			<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="privateKey!=null and privateKey!=''">
				private_key = #{privateKey,jdbcType=VARCHAR},
			</if>
			<if test="url!=null and url!=''">
				url = #{url,jdbcType=VARCHAR},
			</if>
			<if test="appId!=null and appId!=''">
				app_id = #{appId,jdbcType=VARCHAR},
			</if>
			<if test="charSet!=null and charSet!=''">
				char_set = #{charSet,jdbcType=VARCHAR},
			</if>
			<if test="format!=null and format!=''">
				format = #{format,jdbcType=VARCHAR},
			</if>
			<if test="signType!=null and signType!=''">
				sign_type = #{signType,jdbcType=VARCHAR},
			</if>
			<if test="alipayPublicKey!=null and alipayPublicKey!=''">
				alipay_publicKey = #{alipayPublicKey,jdbcType=VARCHAR},
			</if>
			<if test="updateTime!=null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="updateUserId!=null and updateUserId!=''">
				update_user_id = #{updateUserId,jdbcType=VARCHAR}
			</if>
		</set>
		WHERE
		id = #{id,jdbcType=INTEGER}
	</update>

</mapper>
