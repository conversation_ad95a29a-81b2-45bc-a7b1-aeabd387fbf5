<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.SysWechatRedpackDao">
	<resultMap id="SysWechatRedpackMap" type="com.pms.czaccount.bean.pay.SysWechatRedpack">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="sn" jdbcType="VARCHAR" column="sn"/>
		<result property="otherSn" jdbcType="VARCHAR" column="other_sn"/>
		<result property="otherId" jdbcType="INTEGER" column="other_id"/>
		<result property="redpackSettingId" jdbcType="INTEGER" column="redpack_setting_id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="sendHid" jdbcType="INTEGER" column="send_hid"/>
		<result property="actName" jdbcType="VARCHAR" column="act_name"/>
		<result property="wishing" jdbcType="VARCHAR" column="wishing"/>
		<result property="sceneId" jdbcType="VARCHAR" column="sceneId"/>
		<result property="money" jdbcType="INTEGER" column="money"/>
		<result property="status" jdbcType="INTEGER" column="status"/>
		<result property="type" jdbcType="INTEGER" column="type"/>
		<result property="receiverOpenid" jdbcType="VARCHAR" column="receiver_openid"/>
		<result property="rcvTime" jdbcType="TIMESTAMP" column="rcv_time"/>
		<result property="threshold" jdbcType="DOUBLE" column="threshold"/>
		<result property="totalNum" jdbcType="INTEGER" column="total_num"/>
		<result property="refundTime" jdbcType="TIMESTAMP" column="refund_time"/>
		<result property="refundMoney" jdbcType="INTEGER" column="refund_money"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
		<result property="createUser" jdbcType="VARCHAR" column="create_user"/>
		<result property="createUserId" jdbcType="VARCHAR" column="create_user_id"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		sn,
		other_sn,
		other_id,
		redpack_setting_id,
		hid,
		send_hid,
		act_name,
		wishing,
		sceneId,
		money,
		status,
		type,
		receiver_openid,
		rcv_time,
		threshold,
		total_num,
		refund_time,
		refund_money,
		business_day,
		create_time,
		create_user,
		create_user_id
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="SysWechatRedpackMap">
		SELECT
		<include refid="BaseColumn" />
		FROM sys_wechat_redpack
		WHERE
		id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="deleteSysWechatRedpack" parameterType="java.lang.Integer">
		DELETE FROM sys_wechat_redpack WHERE
		id = #{id}
	</delete>
	<!-- 插入 -->
	<insert id="saveSysWechatRedpack" parameterType="com.pms.czaccount.bean.pay.SysWechatRedpack" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO sys_wechat_redpack (
		id,
		sn,
		other_sn,
		other_id,
		redpack_setting_id,
		hid,
		send_hid,
		act_name,
		wishing,
		sceneId,
		money,
		status,
		type,
		receiver_openid,
		rcv_time,
		threshold,
		total_num,
		refund_time,
		refund_money,
		business_day,
		create_time,
		create_user,
		create_user_id
		) VALUES (
		#{id,jdbcType=INTEGER},
		#{sn,jdbcType=VARCHAR},
		#{otherSn,jdbcType=VARCHAR},
		#{otherId,jdbcType=INTEGER},
		#{redpackSettingId,jdbcType=INTEGER},
		#{hid,jdbcType=INTEGER},
		#{sendHid,jdbcType=INTEGER},
		#{actName,jdbcType=VARCHAR},
		#{wishing,jdbcType=VARCHAR},
		#{sceneId,jdbcType=VARCHAR},
		#{money,jdbcType=INTEGER},
		#{status,jdbcType=INTEGER},
		#{type,jdbcType=INTEGER},
		#{receiverOpenid,jdbcType=VARCHAR},
		#{rcvTime,jdbcType=TIMESTAMP},
		#{threshold,jdbcType=DOUBLE},
		#{totalNum,jdbcType=INTEGER},
		#{refundTime,jdbcType=TIMESTAMP},
		#{refundMoney,jdbcType=INTEGER},
		#{businessDay,jdbcType=INTEGER},
		#{createTime,jdbcType=TIMESTAMP},
		#{createUser,jdbcType=VARCHAR},
		#{createUserId,jdbcType=VARCHAR}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.pay.search.SysWechatRedpackSearch" resultMap="SysWechatRedpackMap">
		SELECT
		<include refid="BaseColumn" />
		FROM sys_wechat_redpack
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="sn!=null and sn!=''">
				and sn = #{sn}
			</if>
			<if test="otherSn!=null and otherSn!=''">
				and other_sn = #{otherSn}
			</if>
			<if test="otherId!=null and otherId!=''">
				and other_id = #{otherId}
			</if>
			<if test="redpackSettingId!=null and redpackSettingId!=''">
				and redpack_setting_id = #{redpackSettingId}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="sendHid!=null and sendHid!=''">
				and send_hid = #{sendHid}
			</if>
			<if test="actName!=null and actName!=''">
				and act_name = #{actName}
			</if>
			<if test="wishing!=null and wishing!=''">
				and wishing = #{wishing}
			</if>
			<if test="sceneId!=null and sceneId!=''">
				and sceneId = #{sceneId}
			</if>
			<if test="money!=null and money!=''">
				and money = #{money}
			</if>
			<if test="status!=null and status!=''">
				and status = #{status}
			</if>
			<if test="type!=null and type!=''">
				and type = #{type}
			</if>
			<if test="receiverOpenid!=null and receiverOpenid!=''">
				and receiver_openid = #{receiverOpenid}
			</if>
			<if test="rcvTime!=null ">
				and rcv_time = #{rcvTime}
			</if>
			<if test="threshold!=null and threshold!=''">
				and threshold = #{threshold}
			</if>
			<if test="totalNum!=null and totalNum!=''">
				and total_num = #{totalNum}
			</if>
			<if test="refundTime!=null ">
				and refund_time = #{refundTime}
			</if>
			<if test="refundMoney!=null and refundMoney!=''">
				and refund_money = #{refundMoney}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="createTime!=null ">
				and create_time = #{createTime}
			</if>
			<if test="createUser!=null and createUser!=''">
				and create_user = #{createUser}
			</if>
			<if test="createUserId!=null and createUserId!=''">
				and create_user_id = #{createUserId}
			</if>
		</where>
	</select>

	<!-- 更新 -->
	<update id="editSysWechatRedpack" parameterType="com.pms.czaccount.bean.pay.SysWechatRedpack">
		UPDATE sys_wechat_redpack
		<set>
			<if test="sn!=null and sn!=''">
				sn = #{sn,jdbcType=VARCHAR},
			</if>
			<if test="otherSn!=null and otherSn!=''">
				other_sn = #{otherSn,jdbcType=VARCHAR},
			</if>
			<if test="otherId!=null and otherId!=''">
				other_id = #{otherId,jdbcType=INTEGER},
			</if>
			<if test="redpackSettingId!=null and redpackSettingId!=''">
				redpack_setting_id = #{redpackSettingId,jdbcType=INTEGER},
			</if>
			<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="sendHid!=null and sendHid!=''">
				send_hid = #{sendHid,jdbcType=INTEGER},
			</if>
			<if test="actName!=null and actName!=''">
				act_name = #{actName,jdbcType=VARCHAR},
			</if>
			<if test="wishing!=null and wishing!=''">
				wishing = #{wishing,jdbcType=VARCHAR},
			</if>
			<if test="sceneId!=null and sceneId!=''">
				sceneId = #{sceneId,jdbcType=VARCHAR},
			</if>
			<if test="money!=null and money!=''">
				money = #{money,jdbcType=INTEGER},
			</if>
			<if test="status!=null and status!=''">
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="type!=null and type!=''">
				type = #{type,jdbcType=INTEGER},
			</if>
			<if test="receiverOpenid!=null and receiverOpenid!=''">
				receiver_openid = #{receiverOpenid,jdbcType=VARCHAR},
			</if>
			<if test="rcvTime!=null ">
				rcv_time = #{rcvTime,jdbcType=TIMESTAMP},
			</if>
			<if test="threshold!=null and threshold!=''">
				threshold = #{threshold,jdbcType=DOUBLE},
			</if>
			<if test="totalNum!=null and totalNum!=''">
				total_num = #{totalNum,jdbcType=INTEGER},
			</if>
			<if test="refundTime!=null ">
				refund_time = #{refundTime,jdbcType=TIMESTAMP},
			</if>
			<if test="refundMoney!=null and refundMoney!=''">
				refund_money = #{refundMoney,jdbcType=INTEGER},
			</if>
			<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER},
			</if>
		</set>
		WHERE
		id = #{id,jdbcType=INTEGER}
	</update>

	<!--查询当天红包的发放情况-->
	<select id="redpackDayCount" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT
		SUM(id) AS sumCount,
		redpack_setting_id AS redPackSettingId
		FROM
		sys_wechat_redpack
		WHERE
		business_day = #{businessDay,jdbcType=INTEGER}
		GROUP BY
		redpack_setting_id
	</select>

</mapper>
