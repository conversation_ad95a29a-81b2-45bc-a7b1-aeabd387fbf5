<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.AlipayFaceTransactionDao">
	<resultMap id="AlipayFaceTransactionMap" type="com.pms.czaccount.bean.pay.AlipayFaceTransaction">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="uuid" jdbcType="VARCHAR" column="uuid"/>
		<result property="money" jdbcType="INTEGER" column="money"/>
		<result property="tradeNo" jdbcType="VARCHAR" column="trade_no"/>
		<result property="openId" jdbcType="VARCHAR" column="open_id"/>
		<result property="buyerLogonId" jdbcType="VARCHAR" column="buyer_logon_id"/>
		<result property="mainId" jdbcType="VARCHAR" column="main_id"/>
		<result property="status" jdbcType="INTEGER" column="status"/>
		<result property="payTime" jdbcType="TIMESTAMP" column="pay_time"/>
		<result property="buyerUserId" jdbcType="VARCHAR" column="buyer_user_id"/>
		<result property="refundMoney" jdbcType="INTEGER" column="refund_money"/>
		<result property="refundTime" jdbcType="TIMESTAMP" column="refund_time"/>
		<result property="scanTime" jdbcType="TIMESTAMP" column="scan_time"/>
		<result property="source" jdbcType="VARCHAR" column="source"/>
		<result property="payDesc" jdbcType="VARCHAR" column="pay_desc"/>
		<result property="registId" jdbcType="INTEGER" column="regist_id"/>
		<result property="bookingId" jdbcType="INTEGER" column="booking_id"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		uuid,
		money,
		trade_no,
		open_id,
		buyer_logon_id,
		main_id,
		status,
		pay_time,
		buyer_user_id,
		refund_money,
		refund_time,
		scan_time,
		source,
		pay_desc,
		regist_id,
		booking_id
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="AlipayFaceTransactionMap">
		SELECT
			<include refid="BaseColumn" />
		FROM alipay_face_transaction
		WHERE
			id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="deleteAlipayFaceTransaction" parameterType="java.lang.Integer">
		DELETE FROM alipay_face_transaction WHERE
		id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="saveAlipayFaceTransaction" parameterType="com.pms.czaccount.bean.pay.AlipayFaceTransaction" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO alipay_face_transaction (
			id,
			hid,
			uuid,
			money,
			trade_no,
			open_id,
			buyer_logon_id,
			main_id,
			status,
			pay_time,
			buyer_user_id,
			refund_money,
			refund_time,
			scan_time,
			source,
			pay_desc,
			regist_id,
			booking_id
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{uuid,jdbcType=VARCHAR},
			#{money,jdbcType=INTEGER},
			#{tradeNo,jdbcType=VARCHAR},
			#{openId,jdbcType=VARCHAR},
			#{buyerLogonId,jdbcType=VARCHAR},
			#{mainId,jdbcType=VARCHAR},
			#{status,jdbcType=INTEGER},
			#{payTime,jdbcType=TIMESTAMP},
			#{buyerUserId,jdbcType=VARCHAR},
			#{refundMoney,jdbcType=INTEGER},
			#{refundTime,jdbcType=TIMESTAMP},
			#{scanTime,jdbcType=TIMESTAMP},
			#{source,jdbcType=VARCHAR},
			#{payDesc,jdbcType=VARCHAR},
			#{registId,jdbcType=INTEGER},
			#{bookingId,jdbcType=INTEGER}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.pay.search.AlipayFaceTransactionSearch" resultMap="AlipayFaceTransactionMap">
		SELECT
			<include refid="BaseColumn" />
		FROM alipay_face_transaction
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="uuid!=null and uuid!=''">
				and uuid = #{uuid}
			</if>
			<if test="money!=null and money!=''">
				and money = #{money}
			</if>
			<if test="tradeNo!=null and tradeNo!=''">
				and trade_no = #{tradeNo}
			</if>
			<if test="openId!=null and openId!=''">
				and open_id = #{openId}
			</if>
			<if test="buyerLogonId!=null and buyerLogonId!=''">
				and buyer_logon_id = #{buyerLogonId}
			</if>
			<if test="mainId!=null and mainId!=''">
				and main_id = #{mainId}
			</if>
			<if test="status!=null">
				and status = #{status}
			</if>
			<if test="payTime!=null ">
				and pay_time = #{payTime}
			</if>
			<if test="buyerUserId!=null and buyerUserId!=''">
				and buyer_user_id = #{buyerUserId}
			</if>
			<if test="refundMoney!=null">
				and refund_money = #{refundMoney}
			</if>
			<if test="refundTime!=null ">
				and refund_time = #{refundTime}
			</if>
			<if test="scanTime!=null ">
				and scan_time = #{scanTime}
			</if>
			<if test="source!=null and source!=''">
				and source = #{source}
			</if>
			<if test="payDesc!=null and payDesc!=''">
				and pay_desc = #{payDesc}
			</if>
			<if test="registId!=null and registId!=''">
				and regist_id = #{registId}
			</if>
			<if test="bookingId!=null and bookingId!=''">
				and booking_id = #{bookingId}
			</if>
		</where>
	</select>

	<select id="selectNotCollection" parameterType="com.pms.czaccount.bean.pay.search.AlipayFaceTransactionSearch" resultMap="AlipayFaceTransactionMap">
		SELECT <include refid="BaseColumn" />
		FROM alipay_face_transaction WHERE main_id NOT  in (SELECT third_accout_id FROM account WHERE third_accout_id is NOT null  and hid = #{hid}) AND `status`=1 and hid = #{hid} and  pay_time >= #{payTime} order by pay_time desc
	</select>

	<!-- 更新 -->
	<update id="editAlipayFaceTransaction" parameterType="com.pms.czaccount.bean.pay.AlipayFaceTransaction">
		UPDATE alipay_face_transaction
			<set>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="uuid!=null and uuid!=''">
				uuid = #{uuid,jdbcType=VARCHAR},
				</if>
				<if test="money!=null and money!=''">
				money = #{money,jdbcType=INTEGER},
				</if>
				<if test="tradeNo!=null and tradeNo!=''">
				trade_no = #{tradeNo,jdbcType=VARCHAR},
				</if>
				<if test="openId!=null and openId!=''">
				open_id = #{openId,jdbcType=VARCHAR},
				</if>
				<if test="buyerLogonId!=null and buyerLogonId!=''">
				buyer_logon_id = #{buyerLogonId,jdbcType=VARCHAR},
				</if>
				<if test="mainId!=null and mainId!=''">
				main_id = #{mainId,jdbcType=VARCHAR},
				</if>
				<if test="status!=null">
				status = #{status,jdbcType=INTEGER},
				</if>
				<if test="payTime!=null ">
				pay_time = #{payTime,jdbcType=TIMESTAMP},
				</if>
				<if test="buyerUserId!=null and buyerUserId!=''">
				buyer_user_id = #{buyerUserId,jdbcType=VARCHAR},
				</if>
				<if test="refundMoney!=null">
				refund_money = #{refundMoney,jdbcType=INTEGER},
				</if>
				<if test="refundTime!=null ">
				refund_time = #{refundTime,jdbcType=TIMESTAMP},
				</if>
				<if test="scanTime!=null ">
				scan_time = #{scanTime,jdbcType=TIMESTAMP},
				</if>
				<if test="source!=null and source!=''">
				source = #{source,jdbcType=VARCHAR},
				</if>
				<if test="payDesc!=null and payDesc!=''">
				pay_desc = #{payDesc,jdbcType=VARCHAR}
				</if>
				<if test="registId!=null and registId!=''">
					regist_id = #{registId,jdbcType=INTEGER},
				</if>
				<if test="bookingId!=null and bookingId!=''">
					booking_id = #{bookingId,jdbcType=INTEGER}
				</if>
			</set>
		WHERE
			id = #{id,jdbcType=INTEGER}
	</update>

</mapper>
