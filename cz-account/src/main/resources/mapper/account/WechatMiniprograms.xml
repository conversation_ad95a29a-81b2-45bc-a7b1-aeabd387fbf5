<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.WechatMiniprogramsDao">
	<resultMap id="WechatMiniprogramsMap" type="com.pms.czaccount.bean.pay.WechatMiniprograms">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="name" jdbcType="VARCHAR" column="name"/>
		<result property="appid" jdbcType="VARCHAR" column="appid"/>
		<result property="payAppid" jdbcType="VARCHAR" column="pay_appid"/>
		<result property="appsecret" jdbcType="VARCHAR" column="appsecret"/>
		<result property="accessToken" jdbcType="VARCHAR" column="access_token"/>
		<result property="accessTokenTime" jdbcType="TIMESTAMP" column="access_token_time"/>
		<result property="accessTokenValidityTime" jdbcType="INTEGER" column="access_token_validity_time"/>
		<result property="jsApiTicket" jdbcType="VARCHAR" column="js_api_ticket"/>
		<result property="jsApiTicketTime" jdbcType="TIMESTAMP" column="js_api_ticket_time"/>
		<result property="jsApiTicketValidityTime" jdbcType="INTEGER" column="js_api_ticket_validity_time"/>
		<result property="mchId" jdbcType="VARCHAR" column="mch_id"/>
		<result property="apiKey" jdbcType="VARCHAR" column="api_key"/>
		<result property="filePath" jdbcType="VARCHAR" column="file_path"/>
		<result property="filePath1" jdbcType="VARCHAR" column="file_path1"/>
		<result property="notifyUrl" jdbcType="VARCHAR" column="notify_url"/>
		<result property="cardTypeId" jdbcType="INTEGER" column="card_type_id"/>
		<result property="cardLevelId" jdbcType="INTEGER" column="card_level_id"/>
		<result property="qrcode" jdbcType="LONGVARCHAR" column="qrcode"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		hotel_group_id,
		`name`,
		appid,
		pay_appid,
		appsecret,
		access_token,
		access_token_time,
		access_token_validity_time,
		js_api_ticket,
		js_api_ticket_time,
		js_api_ticket_validity_time,
		mch_id,
		api_key,
		file_path,
		file_path1,
		notify_url,
		card_type_id,
		card_level_id,
		qrcode
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="WechatMiniprogramsMap">
		SELECT
			<include refid="BaseColumn" />
		FROM wechat_miniprograms
		WHERE
			id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM wechat_miniprograms WHERE
		id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czaccount.bean.pay.WechatMiniprograms" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO wechat_miniprograms (
			id,
			hid,
			hotel_group_id,
			`name`,
			appid,
			pay_appid,
			appsecret,
			access_token,
			access_token_time,
			access_token_validity_time,
			js_api_ticket,
			js_api_ticket_time,
			js_api_ticket_validity_time,
			mch_id,
			api_key,
			file_path,
			file_path1,
			notify_url,
			card_type_id,
			card_level_id,
			qrcode
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{name,jdbcType=VARCHAR},
			#{appid,jdbcType=VARCHAR},
			#{payAppid,jdbcType=VARCHAR},
			#{appsecret,jdbcType=VARCHAR},
			#{accessToken,jdbcType=VARCHAR},
			#{accessTokenTime,jdbcType=TIMESTAMP},
			#{accessTokenValidityTime,jdbcType=INTEGER},
			#{jsApiTicket,jdbcType=VARCHAR},
			#{jsApiTicketTime,jdbcType=TIMESTAMP},
			#{jsApiTicketValidityTime,jdbcType=INTEGER},
			#{mchId,jdbcType=VARCHAR},
			#{apiKey,jdbcType=VARCHAR},
			#{filePath,jdbcType=VARCHAR},
			#{filePath1,jdbcType=VARCHAR},
			#{notifyUrl,jdbcType=VARCHAR},
			#{cardTypeId,jdbcType=INTEGER},
			#{cardLevelId,jdbcType=INTEGER},
			#{qrcode,jdbcType=LONGVARCHAR}
		)
	</insert>

	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.pay.search.WechatMiniprogramsSearch" resultMap="WechatMiniprogramsMap">
		SELECT
			<include refid="BaseColumn" />
		FROM wechat_miniprograms
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="name!=null and name!=''">
				and `name` = #{name}
			</if>
			<if test="appid!=null and appid!=''">
				and appid = #{appid}
			</if>
			<if test="payAppid!=null and payAppid!=''">
				and pay_appid = #{payAppid}
			</if>
			<if test="appsecret!=null and appsecret!=''">
				and appsecret = #{appsecret}
			</if>
			<if test="accessToken!=null and accessToken!=''">
				and access_token = #{accessToken}
			</if>
			<if test="accessTokenTime!=null ">
				and access_token_time = #{accessTokenTime}
			</if>
			<if test="accessTokenValidityTime!=null">
				and access_token_validity_time = #{accessTokenValidityTime}
			</if>
			<if test="jsApiTicket!=null and jsApiTicket!=''">
				and js_api_ticket = #{jsApiTicket}
			</if>
			<if test="jsApiTicketTime!=null">
				and js_api_ticket_time = #{jsApiTicketTime}
			</if>
			<if test="jsApiTicketValidityTime!=null ">
				and js_api_ticket_validity_time = #{jsApiTicketValidityTime}
			</if>
			<if test="mchId!=null and mchId!=''">
				and mch_id = #{mchId}
			</if>
			<if test="apiKey!=null and apiKey!=''">
				and api_key = #{apiKey}
			</if>
			<if test="filePath!=null and filePath!=''">
				and file_path = #{filePath}
			</if>
			<if test="filePath1!=null and filePath1!=''">
				and file_path1 = #{filePath1}
			</if>
			<if test="notifyUrl!=null and notifyUrl!=''">
				and notify_url = #{notifyUrl}
			</if>
			<if test="cardTypeId!=null">
				and card_type_id = #{cardTypeId}
			</if>
			<if test="cardLevelId!=null ">
				and card_level_id = #{cardLevelId}
			</if>
			<if test="qrcode!=null and qrcode!=''">
				and qrcode = #{qrcode}
			</if>
		</where>
	</select>

	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czaccount.bean.pay.WechatMiniprograms">
		UPDATE wechat_miniprograms
			<set>
				<if test="hid!=null and hid!=''">
					hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
					hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="appid!=null and appid!=''">
				appid = #{appid,jdbcType=VARCHAR},
				</if>
				<if test="payAppid!=null and payAppid!=''">
					pay_appid = #{payAppid,jdbcType=VARCHAR},
				</if>
				<if test="appsecret!=null and appsecret!=''">
				appsecret = #{appsecret,jdbcType=VARCHAR},
				</if>
				<if test="accessToken!=null and accessToken!=''">
				access_token = #{accessToken,jdbcType=VARCHAR},
				</if>
				<if test="accessTokenTime!=null ">
				access_token_time = #{accessTokenTime,jdbcType=TIMESTAMP},
				</if>
				<if test="accessTokenValidityTime!=null">
				access_token_validity_time = #{accessTokenValidityTime,jdbcType=INTEGER},
				</if>
				<if test="jsApiTicket!=null and jsApiTicket!=''">
				js_api_ticket = #{jsApiTicket,jdbcType=VARCHAR},
				</if>
				<if test="jsApiTicketTime!=null">
				js_api_ticket_time = #{jsApiTicketTime,jdbcType=TIMESTAMP},
				</if>
				<if test="jsApiTicketValidityTime!=null ">
				js_api_ticket_validity_time = #{jsApiTicketValidityTime,jdbcType=INTEGER},
				</if>
				<if test="mchId!=null and mchId!=''">
				mch_id = #{mchId,jdbcType=VARCHAR},
				</if>
				<if test="apiKey!=null and apiKey!=''">
				api_key = #{apiKey,jdbcType=VARCHAR},
				</if>
				<if test="filePath!=null and filePath!=''">
				file_path = #{filePath,jdbcType=VARCHAR},
				</if>
				<if test="filePath1!=null and filePath1!=''">
				file_path1 = #{filePath1,jdbcType=VARCHAR},
				</if>
				<if test="notifyUrl!=null and notifyUrl!=''">
				notify_url = #{notifyUrl,jdbcType=VARCHAR},
				</if>
				<if test="cardTypeId!=null">
					card_type_id = #{cardTypeId,jdbcType=INTEGER},
				</if>
				<if test="cardLevelId!=null">
					card_level_id = #{cardLevelId,jdbcType=INTEGER},
				</if>
				<if test="qrcode!=null and qrcode!=''">
					qrcode = #{qrcode}
				</if>
			</set>
		WHERE
			id = #{id,jdbcType=INTEGER}
	</update>

</mapper>
