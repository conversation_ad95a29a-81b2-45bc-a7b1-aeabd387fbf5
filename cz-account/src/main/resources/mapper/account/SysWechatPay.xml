<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.SysWechatPayDao">
	<resultMap id="SysWechatPayMap" type="com.pms.czaccount.bean.pay.SysWechatPay">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="machineUuid" jdbcType="VARCHAR" column="machine_uuid"/>
		<result property="mainId" jdbcType="VARCHAR" column="main_id"/>
		<result property="money" jdbcType="DECIMAL" column="money"/>
		<result property="moneyFen" jdbcType="INTEGER" column="money_fen"/>
		<result property="appId" jdbcType="VARCHAR" column="app_id"/>
		<result property="mchId" jdbcType="VARCHAR" column="mch_id"/>
		<result property="apiKey" jdbcType="VARCHAR" column="api_key"/>
		<result property="orderDesc" jdbcType="VARCHAR" column="order_desc"/>
		<result property="source" jdbcType="VARCHAR" column="source"/>
		<result property="payStatus" jdbcType="INTEGER" column="pay_status"/>
		<result property="updateTime" jdbcType="TIMESTAMP" column="update_time"/>
		<result property="payTime" jdbcType="TIMESTAMP" column="pay_time"/>
		<result property="transactionId" jdbcType="VARCHAR" column="transaction_id"/>
		<result property="openId" jdbcType="VARCHAR" column="open_id"/>
		<result property="isSubscribe" jdbcType="VARCHAR" column="is_subscribe"/>
		<result property="tradeType" jdbcType="VARCHAR" column="trade_type"/>
		<result property="bankType" jdbcType="VARCHAR" column="bank_type"/>
		<result property="feeType" jdbcType="VARCHAR" column="fee_type"/>
		<result property="cashFee" jdbcType="INTEGER" column="cash_fee"/>
		<result property="cashFeeType" jdbcType="VARCHAR" column="cash_fee_type"/>
		<result property="couponFee" jdbcType="INTEGER" column="coupon_fee"/>
		<result property="couponCount" jdbcType="INTEGER" column="coupon_count"/>
		<result property="couponId" jdbcType="VARCHAR" column="coupon_id"/>
		<result property="couponFeen" jdbcType="VARCHAR" column="coupon_feen"/>
		<result property="refundMoney" jdbcType="INTEGER" column="refund_money"/>
		<result property="timeEnd" jdbcType="TIMESTAMP" column="time_end"/>
		<result property="refundTime" jdbcType="TIMESTAMP" column="refund_time"/>
		<result property="registId" jdbcType="INTEGER" column="regist_id"/>
		<result property="bookingId" jdbcType="INTEGER" column="booking_id"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		machine_uuid,
		main_id,
		money,
		money_fen,
		app_id,
		mch_id,
		api_key,
		order_desc,
		source,
		pay_status,
		update_time,
		pay_time,
		transaction_id,
		open_id,
		is_subscribe,
		trade_type,
		bank_type,
		fee_type,
		cash_fee,
		cash_fee_type,
		coupon_fee,
		coupon_count,
		coupon_id,
		coupon_feen,
		refund_money,
		time_end,
		refund_time,
		regist_id,
		booking_id
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="SysWechatPayMap">
		SELECT
		<include refid="BaseColumn" />
		FROM sys_wechat_pay
		WHERE
		id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="deleteSysWechatPay" parameterType="java.lang.Integer">
		DELETE FROM sys_wechat_pay WHERE
		id = #{id}
	</delete>
	<!-- 插入 -->
	<insert id="saveSysWechatPay" parameterType="com.pms.czaccount.bean.pay.SysWechatPay" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO sys_wechat_pay (
			id,
			hid,
			machine_uuid,
			main_id,
			money,
			money_fen,
			app_id,
			mch_id,
			api_key,
			order_desc,
			source,
			pay_status,
			update_time,
			pay_time,
			transaction_id,
			open_id,
			is_subscribe,
			trade_type,
			bank_type,
			fee_type,
			cash_fee,
			cash_fee_type,
			coupon_fee,
			coupon_count,
			coupon_id,
			coupon_feen,
			refund_money,
			time_end,
			refund_time,
			regist_id,
			booking_id
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{machineUuid,jdbcType=VARCHAR},
			#{mainId,jdbcType=VARCHAR},
			#{money,jdbcType=DECIMAL},
			#{moneyFen,jdbcType=INTEGER},
			#{appId,jdbcType=VARCHAR},
			#{mchId,jdbcType=VARCHAR},
			#{apiKey,jdbcType=VARCHAR},
			#{orderDesc,jdbcType=VARCHAR},
			#{source,jdbcType=VARCHAR},
			#{payStatus,jdbcType=INTEGER},
			#{updateTime,jdbcType=TIMESTAMP},
			#{payTime,jdbcType=TIMESTAMP},
			#{transactionId,jdbcType=VARCHAR},
			#{openId,jdbcType=VARCHAR},
			#{isSubscribe,jdbcType=VARCHAR},
			#{tradeType,jdbcType=VARCHAR},
			#{bankType,jdbcType=VARCHAR},
			#{feeType,jdbcType=VARCHAR},
			#{cashFee,jdbcType=INTEGER},
			#{cashFeeType,jdbcType=VARCHAR},
			#{couponFee,jdbcType=INTEGER},
			#{couponCount,jdbcType=INTEGER},
			#{couponId,jdbcType=VARCHAR},
			#{couponFeen,jdbcType=VARCHAR},
			#{refundMoney,jdbcType=INTEGER},
			#{timeEnd,jdbcType=TIMESTAMP},
			#{refundTime,jdbcType=TIMESTAMP},
			#{registId,jdbcType=INTEGER},
			#{bookingId,jdbcType=INTEGER}
		)
	</insert>

	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.pay.search.SysWechatPaySearch" resultMap="SysWechatPayMap">
		SELECT
		<include refid="BaseColumn" />
		FROM sys_wechat_pay
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="machineUuid!=null and machineUuid!=''">
				and machine_uuid = #{machineUuid}
			</if>
			<if test="mainId!=null and mainId!=''">
				and main_id = #{mainId}
			</if>
			<if test="money!=null and money!=''">
				and money = #{money}
			</if>
			<if test="moneyFen!=null and moneyFen!=''">
				and money_fen = #{moneyFen}
			</if>
			<if test="appId!=null and appId!=''">
				and app_id = #{appId}
			</if>
			<if test="mchId!=null and mchId!=''">
				and mch_id = #{mchId}
			</if>
			<if test="apiKey!=null and apiKey!=''">
				and api_key = #{apiKey}
			</if>
			<if test="orderDesc!=null and orderDesc!=''">
				and order_desc = #{orderDesc}
			</if>
			<if test="source!=null and source!=''">
				and source = #{source}
			</if>
			<if test="payStatus!=null ">
				and pay_status = #{payStatus}
			</if>
			<if test="updateTime!=null ">
				and update_time = #{updateTime}
			</if>
			<if test="payTime!=null ">
				and pay_time = #{payTime}
			</if>
			<if test="transactionId!=null and transactionId!=''">
				and transaction_id = #{transactionId}
			</if>
			<if test="openId!=null and openId!=''">
				and open_id = #{openId}
			</if>
			<if test="isSubscribe!=null and isSubscribe!=''">
				and is_subscribe = #{isSubscribe}
			</if>
			<if test="tradeType!=null and tradeType!=''">
				and trade_type = #{tradeType}
			</if>
			<if test="bankType!=null and bankType!=''">
				and bank_type = #{bankType}
			</if>
			<if test="feeType!=null and feeType!=''">
				and fee_type = #{feeType}
			</if>
			<if test="cashFee!=null and cashFee!=''">
				and cash_fee = #{cashFee}
			</if>
			<if test="cashFeeType!=null and cashFeeType!=''">
				and cash_fee_type = #{cashFeeType}
			</if>
			<if test="couponFee!=null and couponFee!=''">
				and coupon_fee = #{couponFee}
			</if>
			<if test="couponCount!=null and couponCount!=''">
				and coupon_count = #{couponCount}
			</if>
			<if test="couponId!=null and couponId!=''">
				and coupon_id = #{couponId}
			</if>
			<if test="couponFeen!=null and couponFeen!=''">
				and coupon_feen = #{couponFeen}
			</if>
			<if test="refundMoney!=null">
				and refund_money = #{refundMoney}
			</if>
			<if test="timeEnd!=null ">
				and time_end = #{timeEnd}
			</if>
			<if test="refundTime!=null ">
				and refund_time = #{refundTime}
			</if>
			<if test="registId!=null and registId!=''">
				and regist_id = #{registId}
			</if>
			<if test="bookingId!=null and bookingId!=''">
				and booking_id = #{bookingId}
			</if>
		</where>
	</select>

	<select id="selectNotCollection"  parameterType="com.pms.czaccount.bean.pay.search.SysWechatPaySearch" resultMap="SysWechatPayMap">
		SELECT <include refid="BaseColumn" />
		FROM sys_wechat_pay WHERE main_id NOT in (SELECT third_accout_id FROM account WHERE third_accout_id is NOT null and hid = #{hid}) AND pay_status = 1 and hid = #{hid} and pay_time >= #{payTime}  order by pay_time desc
	</select>

	<!-- 更新 -->
	<update id="editSysWechatPay" parameterType="com.pms.czaccount.bean.pay.SysWechatPay">
		UPDATE sys_wechat_pay
		<set>
			<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="machineUuid!=null and machineUuid!=''">
				machine_uuid = #{machineUuid,jdbcType=VARCHAR},
			</if>
			<if test="mainId!=null and mainId!=''">
				main_id = #{mainId,jdbcType=VARCHAR},
			</if>
			<if test="money!=null and money!=''">
				money = #{money,jdbcType=DECIMAL},
			</if>
			<if test="moneyFen!=null and moneyFen!=''">
				money_fen = #{moneyFen,jdbcType=INTEGER},
			</if>
			<if test="appId!=null and appId!=''">
				app_id = #{appId,jdbcType=VARCHAR},
			</if>
			<if test="mchId!=null and mchId!=''">
				mch_id = #{mchId,jdbcType=VARCHAR},
			</if>
			<if test="apiKey!=null and apiKey!=''">
				api_key = #{apiKey,jdbcType=VARCHAR},
			</if>
			<if test="orderDesc!=null and orderDesc!=''">
				order_desc = #{orderDesc,jdbcType=VARCHAR},
			</if>
			<if test="source!=null and source!=''">
				source = #{source,jdbcType=VARCHAR},
			</if>
			<if test="payStatus!=null ">
				pay_status = #{payStatus,jdbcType=INTEGER},
			</if>
			<if test="updateTime!=null ">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="payTime!=null ">
				pay_time = #{payTime,jdbcType=TIMESTAMP},
			</if>
			<if test="transactionId!=null and transactionId!=''">
				transaction_id = #{transactionId,jdbcType=VARCHAR},
			</if>
			<if test="openId!=null and openId!=''">
				open_id = #{openId,jdbcType=VARCHAR},
			</if>
			<if test="isSubscribe!=null and isSubscribe!=''">
				is_subscribe = #{isSubscribe,jdbcType=VARCHAR},
			</if>
			<if test="tradeType!=null and tradeType!=''">
				trade_type = #{tradeType,jdbcType=VARCHAR},
			</if>
			<if test="bankType!=null and bankType!=''">
				bank_type = #{bankType,jdbcType=VARCHAR},
			</if>
			<if test="feeType!=null and feeType!=''">
				fee_type = #{feeType,jdbcType=VARCHAR},
			</if>
			<if test="cashFee!=null and cashFee!=''">
				cash_fee = #{cashFee,jdbcType=INTEGER},
			</if>
			<if test="cashFeeType!=null and cashFeeType!=''">
				cash_fee_type = #{cashFeeType,jdbcType=VARCHAR},
			</if>
			<if test="couponFee!=null and couponFee!=''">
				coupon_fee = #{couponFee,jdbcType=INTEGER},
			</if>
			<if test="couponCount!=null and couponCount!=''">
				coupon_count = #{couponCount,jdbcType=INTEGER},
			</if>
			<if test="couponId!=null and couponId!=''">
				coupon_id = #{couponId,jdbcType=VARCHAR},
			</if>
			<if test="couponFeen!=null and couponFeen!=''">
				coupon_feen = #{couponFeen,jdbcType=VARCHAR},
			</if>
			<if test="refundMoney!=null">
				refund_money = #{refundMoney,jdbcType=INTEGER},
			</if>
			<if test="timeEnd!=null ">
				time_end = #{timeEnd,jdbcType=TIMESTAMP},
			</if>
			<if test="refundTime!=null ">
				refund_time = #{refundTime,jdbcType=TIMESTAMP},
			</if>
			<if test="registId!=null and registId!=''">
				regist_id = #{registId,jdbcType=INTEGER},
			</if>
			<if test="bookingId!=null and bookingId!=''">
				booking_id = #{bookingId,jdbcType=INTEGER}
			</if>
		</set>
		WHERE
		id = #{id,jdbcType=INTEGER}
	</update>

</mapper>
