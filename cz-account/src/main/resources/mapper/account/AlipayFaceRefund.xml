<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.AlipayFaceRefundDao">
	<resultMap id="AlipayFaceRefundMap" type="com.pms.czaccount.bean.pay.AlipayFaceRefund">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="mainId" jdbcType="VARCHAR" column="main_id"/>
		<result property="payMainId" jdbcType="VARCHAR" column="pay_main_id"/>
		<result property="refundMoney" jdbcType="INTEGER" column="refund_money"/>
		<result property="refundTime" jdbcType="TIMESTAMP" column="refund_time"/>
		<result property="status" jdbcType="INTEGER" column="status"/>
		<result property="buyerLogonId" jdbcType="VARCHAR" column="buyer_logon_id"/>
		<result property="source" jdbcType="VARCHAR" column="source"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		main_id,
		pay_main_id,
		refund_money,
		refund_time,
		status,
		buyer_logon_id,
		source
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="AlipayFaceRefundMap">
		SELECT
			<include refid="BaseColumn" />
		FROM alipay_face_refund
		WHERE
			id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="deleteAlipayFaceRefund" parameterType="java.lang.Integer">
		DELETE FROM alipay_face_refund WHERE
		id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="saveAlipayFaceRefund" parameterType="com.pms.czaccount.bean.pay.AlipayFaceRefund" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO alipay_face_refund (
			id,
			hid,
			main_id,
			pay_main_id,
			refund_money,
			refund_time,
			status,
			buyer_logon_id,
			source
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{mainId,jdbcType=VARCHAR},
			#{payMainId,jdbcType=VARCHAR},
			#{refundMoney,jdbcType=INTEGER},
			#{refundTime,jdbcType=TIMESTAMP},
			#{status,jdbcType=INTEGER},
			#{buyerLogonId,jdbcType=VARCHAR},
			#{source,jdbcType=VARCHAR}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.pay.search.AlipayFaceRefundSearch" resultMap="AlipayFaceRefundMap">
		SELECT
			<include refid="BaseColumn" />
		FROM alipay_face_refund
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="mainId!=null and mainId!=''">
				and main_id = #{mainId}
			</if>
			<if test="payMainId!=null and payMainId!=''">
				and pay_main_id = #{payMainId}
			</if>
			<if test="refundMoney!=null and refundMoney!=''">
				and refund_money = #{refundMoney}
			</if>
			<if test="refundTime!=null and refundTime!=''">
				and refund_time = #{refundTime}
			</if>
			<if test="status!=null and status!=''">
				and status = #{status}
			</if>
			<if test="buyerLogonId!=null and buyerLogonId!=''">
				and buyer_logon_id = #{buyerLogonId}
			</if>
			<if test="source!=null and source!=''">
				and source = #{source}
			</if>
		</where>
	</select>

	<!-- 更新 -->
	<update id="editAlipayFaceRefund" parameterType="com.pms.czaccount.bean.pay.AlipayFaceRefund">
		UPDATE alipay_face_refund
			<set>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="mainId!=null and mainId!=''">
				main_id = #{mainId,jdbcType=VARCHAR},
				</if>
				<if test="payMainId!=null and payMainId!=''">
				pay_main_id = #{payMainId,jdbcType=VARCHAR},
				</if>
				<if test="refundMoney!=null and refundMoney!=''">
				refund_money = #{refundMoney,jdbcType=INTEGER},
				</if>
				<if test="refundTime!=null and refundTime!=''">
				refund_time = #{refundTime,jdbcType=TIMESTAMP},
				</if>
				<if test="status!=null and status!=''">
				status = #{status,jdbcType=INTEGER},
				</if>
				<if test="buyerLogonId!=null and buyerLogonId!=''">
				buyer_logon_id = #{buyerLogonId,jdbcType=VARCHAR},
				</if>
				<if test="source!=null and source!=''">
				source = #{source,jdbcType=VARCHAR}
				</if>
			</set>
		WHERE
			id = #{id,jdbcType=INTEGER}
	</update>

</mapper>
