<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.account.AccountThirdPayRecodeDao">
	<resultMap id="AccountThirdPayRecodeMap" type="com.pms.czaccount.bean.account.AccountThirdPayRecode">
		<result property="accountThirdId" jdbcType="VARCHAR" column="account_third_id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="accountId" jdbcType="VARCHAR" column="account_id"/>
		<result property="counterId" jdbcType="VARCHAR" column="counter_id"/>
		<result property="operatorId" jdbcType="VARCHAR" column="operator_id"/>
		<result property="transType" jdbcType="VARCHAR" column="trans_type"/>
		<result property="amount" jdbcType="INTEGER" column="amount"/>
		<result property="oldTrace" jdbcType="VARCHAR" column="old_trace"/>
		<result property="oldDate" jdbcType="VARCHAR" column="old_date"/>
		<result property="oldRef" jdbcType="VARCHAR" column="old_ref"/>
		<result property="oldAuth" jdbcType="VARCHAR" column="old_auth"/>
		<result property="oldBatch" jdbcType="VARCHAR" column="old_batch"/>
		<result property="memo" jdbcType="VARCHAR" column="memo"/>
		<result property="lrc" jdbcType="VARCHAR" column="lrc"/>
		<result property="trace" jdbcType="VARCHAR" column="trace"/>
		<result property="barkId" jdbcType="VARCHAR" column="bark_id"/>
		<result property="batch" jdbcType="VARCHAR" column="batch"/>
		<result property="transDate" jdbcType="VARCHAR" column="trans_date"/>
		<result property="transTime" jdbcType="VARCHAR" column="trans_time"/>
		<result property="ref" jdbcType="VARCHAR" column="ref"/>
		<result property="auth" jdbcType="VARCHAR" column="auth"/>
		<result property="mid" jdbcType="VARCHAR" column="m_Id"/>
		<result property="tid" jdbcType="VARCHAR" column="t_Id"/>
		<result property="effectiveDays" jdbcType="VARCHAR" column="effective_days"/>
		<result property="payType" jdbcType="INTEGER" column="pay_type"/>
		<result property="price" jdbcType="INTEGER" column="price"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="finishBusinessDay" jdbcType="INTEGER" column="finish_business_day"/>
		<result property="refund" jdbcType="INTEGER" column="refund"/>
		<result property="thirdState" jdbcType="INTEGER" column="third_state"/>
		<result property="thirdRefundState" jdbcType="INTEGER" column="third_refund_state"/>
		<result property="classId" jdbcType="INTEGER" column="class_id"/>
		<result property="updateTime" jdbcType="TIMESTAMP" column="update_time"/>
		<result property="updateUserId" jdbcType="VARCHAR" column="update_user_id"/>
		<result property="updateCalssId" jdbcType="INTEGER" column="update_calss_id"/>
		<result property="updateUserName" jdbcType="VARCHAR" column="update_user_name"/>
		<result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
		<result property="createUserId" jdbcType="VARCHAR" column="create_user_id"/>
		<result property="createUserName" jdbcType="VARCHAR" column="create_user_name"/>
	</resultMap>
	<sql id="BaseColumn">
		account_third_id,
		hid,
		hotel_group_id,
		account_id,
		counter_id,
		operator_id,
		trans_type,
		amount,
		old_trace,
		old_date,
		old_ref,
		old_auth,
		old_batch,
		memo,
		lrc,
		trace,
		bark_id,
		batch,
		trans_date,
		trans_time,
		ref,
		auth,
		m_Id,
		t_Id,
		effective_days,
		pay_type,
		price,
		business_day,
		finish_business_day,
		refund,
		third_state,
		third_refund_state,
		class_id,
		update_time,
		update_user_id,
		update_calss_id,
		update_user_name,
		create_time,
		create_user_id,
		create_user_name
	</sql>
	<select id="selectById" parameterType="java.lang.String" resultMap="AccountThirdPayRecodeMap">
		SELECT
			<include refid="BaseColumn" />
		FROM account_third_pay_recode
		WHERE
			account_third_id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.String">
		DELETE FROM account_third_pay_recode WHERE
		account_third_id = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czaccount.bean.account.AccountThirdPayRecode" useGeneratedKeys="true" keyProperty="accountThirdId">
		INSERT INTO account_third_pay_recode (
			account_third_id,
			hid,
			hotel_group_id,
			account_id,
			counter_id,
			operator_id,
			trans_type,
			amount,
			old_trace,
			old_date,
			old_ref,
			old_auth,
			old_batch,
			memo,
			lrc,
			trace,
			bark_id,
			batch,
			trans_date,
			trans_time,
			ref,
			auth,
			m_Id,
			t_Id,
			effective_days,
			pay_type,
			price,
			business_day,
			finish_business_day,
			refund,
			third_state,
			third_refund_state,
			class_id,
			update_time,
			update_user_id,
			update_calss_id,
			update_user_name,
			create_time,
			create_user_id,
			create_user_name
		) VALUES (
			#{accountThirdId,jdbcType=VARCHAR},
			#{hid,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{accountId,jdbcType=VARCHAR},
			#{counterId,jdbcType=VARCHAR},
			#{operatorId,jdbcType=VARCHAR},
			#{transType,jdbcType=VARCHAR},
			#{amount,jdbcType=INTEGER},
			#{oldTrace,jdbcType=VARCHAR},
			#{oldDate,jdbcType=VARCHAR},
			#{oldRef,jdbcType=VARCHAR},
			#{oldAuth,jdbcType=VARCHAR},
			#{oldBatch,jdbcType=VARCHAR},
			#{memo,jdbcType=VARCHAR},
			#{lrc,jdbcType=VARCHAR},
			#{trace,jdbcType=VARCHAR},
			#{barkId,jdbcType=VARCHAR},
			#{batch,jdbcType=VARCHAR},
			#{transDate,jdbcType=VARCHAR},
			#{transTime,jdbcType=VARCHAR},
			#{ref,jdbcType=VARCHAR},
			#{auth,jdbcType=VARCHAR},
			#{mid,jdbcType=VARCHAR},
			#{tid,jdbcType=VARCHAR},
			#{effectiveDays,jdbcType=VARCHAR},
			#{payType,jdbcType=INTEGER},
			#{price,jdbcType=INTEGER},
			#{businessDay,jdbcType=INTEGER},
			#{finishBusinessDay,jdbcType=INTEGER},
			#{refund,jdbcType=INTEGER},
			#{thirdState,jdbcType=INTEGER},
			#{thirdRefundState,jdbcType=INTEGER},
			#{classId,jdbcType=INTEGER},
			#{updateTime,jdbcType=TIMESTAMP},
			#{updateUserId,jdbcType=VARCHAR},
			#{updateCalssId,jdbcType=INTEGER},
			#{updateUserName,jdbcType=VARCHAR},
			#{createTime,jdbcType=TIMESTAMP},
			#{createUserId,jdbcType=VARCHAR},
			#{createUserName,jdbcType=VARCHAR}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.account.search.AccountThirdPayRecodeSearch" resultMap="AccountThirdPayRecodeMap">
		SELECT
			<include refid="BaseColumn" />
		FROM account_third_pay_recode
		<where>
			<if test="accountThirdId!=null and accountThirdId!=''">
				and account_third_id = #{accountThirdId}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="accountId!=null and accountId!=''">
				and account_id = #{accountId}
			</if>
			<if test="counterId!=null and counterId!=''">
				and counter_id = #{counterId}
			</if>
			<if test="operatorId!=null and operatorId!=''">
				and operator_id = #{operatorId}
			</if>
			<if test="transType!=null and transType!=''">
				and trans_type = #{transType}
			</if>
			<if test="amount!=null and amount!=''">
				and amount = #{amount}
			</if>
			<if test="oldTrace!=null and oldTrace!=''">
				and old_trace = #{oldTrace}
			</if>
			<if test="oldDate!=null and oldDate!=''">
				and old_date = #{oldDate}
			</if>
			<if test="oldRef!=null and oldRef!=''">
				and old_ref = #{oldRef}
			</if>
			<if test="oldAuth!=null and oldAuth!=''">
				and old_auth = #{oldAuth}
			</if>
			<if test="oldBatch!=null and oldBatch!=''">
				and old_batch = #{oldBatch}
			</if>
			<if test="memo!=null and memo!=''">
				and memo = #{memo}
			</if>
			<if test="lrc!=null and lrc!=''">
				and lrc = #{lrc}
			</if>
			<if test="trace!=null and trace!=''">
				and trace = #{trace}
			</if>
			<if test="barkId!=null and barkId!=''">
				and bark_id = #{barkId}
			</if>
			<if test="batch!=null and batch!=''">
				and batch = #{batch}
			</if>
			<if test="transDate!=null and transDate!=''">
				and trans_date = #{transDate}
			</if>
			<if test="transTime!=null and transTime!=''">
				and trans_time = #{transTime}
			</if>
			<if test="ref!=null and ref!=''">
				and ref = #{ref}
			</if>
			<if test="auth!=null and auth!=''">
				and auth = #{auth}
			</if>
			<if test="mid!=null and mid!=''">
				and m_Id = #{mid}
			</if>
			<if test="tid!=null and tid!=''">
				and t_Id = #{tid}
			</if>
			<if test="effectiveDays!=null and effectiveDays!=''">
				and effective_days = #{effectiveDays}
			</if>
			<if test="payType!=null and payType!=''">
				and pay_type = #{payType}
			</if>
			<if test="price!=null and price!=''">
				and price = #{price}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="finishBusinessDay!=null and finishBusinessDay!=''">
				and finish_business_day = #{finishBusinessDay}
			</if>
			<if test="refund!=null and refund!=''">
				and refund = #{refund}
			</if>
			<if test="thirdState!=null and thirdState!=''">
				and third_state = #{thirdState}
			</if>
			<if test="thirdRefundState!=null and thirdRefundState!=''">
				and third_refund_state = #{thirdRefundState}
			</if>
			<if test="classId!=null and classId!=''">
				and class_id = #{classId}
			</if>
			<if test="updateTime!=null">
				and update_time = #{updateTime}
			</if>
			<if test="updateUserId!=null and updateUserId!=''">
				and update_user_id = #{updateUserId}
			</if>
			<if test="updateCalssId!=null and updateCalssId!=''">
				and update_calss_id = #{updateCalssId}
			</if>
			<if test="updateUserName!=null and updateUserName!=''">
				and update_user_name = #{updateUserName}
			</if>
			<if test="createTime!=null">
				and create_time = #{createTime}
			</if>
			<if test="createUserId!=null and createUserId!=''">
				and create_user_id = #{createUserId}
			</if>
			<if test="createUserName!=null and createUserName!=''">
				and create_user_name = #{createUserName}
			</if>
		</where>
	</select>

	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czaccount.bean.account.AccountThirdPayRecode">
		UPDATE account_third_pay_recode
			<set>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="accountId!=null and accountId!=''">
				account_id = #{accountId,jdbcType=VARCHAR},
				</if>
				<if test="counterId!=null and counterId!=''">
				counter_id = #{counterId,jdbcType=VARCHAR},
				</if>
				<if test="operatorId!=null and operatorId!=''">
				operator_id = #{operatorId,jdbcType=VARCHAR},
				</if>
				<if test="transType!=null and transType!=''">
				trans_type = #{transType,jdbcType=VARCHAR},
				</if>
				<if test="amount!=null and amount!=''">
				amount = #{amount,jdbcType=INTEGER},
				</if>
				<if test="oldTrace!=null and oldTrace!=''">
				old_trace = #{oldTrace,jdbcType=VARCHAR},
				</if>
				<if test="oldDate!=null and oldDate!=''">
				old_date = #{oldDate,jdbcType=VARCHAR},
				</if>
				<if test="oldRef!=null and oldRef!=''">
				old_ref = #{oldRef,jdbcType=VARCHAR},
				</if>
				<if test="oldAuth!=null and oldAuth!=''">
				old_auth = #{oldAuth,jdbcType=VARCHAR},
				</if>
				<if test="oldBatch!=null and oldBatch!=''">
				old_batch = #{oldBatch,jdbcType=VARCHAR},
				</if>
				<if test="memo!=null and memo!=''">
				memo = #{memo,jdbcType=VARCHAR},
				</if>
				<if test="lrc!=null and lrc!=''">
				lrc = #{lrc,jdbcType=VARCHAR},
				</if>
				<if test="trace!=null and trace!=''">
				trace = #{trace,jdbcType=VARCHAR},
				</if>
				<if test="barkId!=null and barkId!=''">
				bark_id = #{barkId,jdbcType=VARCHAR},
				</if>
				<if test="batch!=null and batch!=''">
				batch = #{batch,jdbcType=VARCHAR},
				</if>
				<if test="transDate!=null and transDate!=''">
				trans_date = #{transDate,jdbcType=VARCHAR},
				</if>
				<if test="transTime!=null and transTime!=''">
				trans_time = #{transTime,jdbcType=VARCHAR},
				</if>
				<if test="ref!=null and ref!=''">
				ref = #{ref,jdbcType=VARCHAR},
				</if>
				<if test="auth!=null and auth!=''">
				auth = #{auth,jdbcType=VARCHAR},
				</if>
				<if test="mid!=null and mid!=''">
				m_Id = #{mid,jdbcType=VARCHAR},
				</if>
				<if test="tid!=null and tid!=''">
				t_Id = #{tid,jdbcType=VARCHAR},
				</if>
				<if test="effectiveDays!=null and effectiveDays!=''">
				effective_days = #{effectiveDays,jdbcType=VARCHAR},
				</if>
				<if test="payType!=null and payType!=''">
				pay_type = #{payType,jdbcType=INTEGER},
				</if>
				<if test="price!=null">
				price = #{price,jdbcType=INTEGER},
				</if>
				<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER},
				</if>
				<if test="finishBusinessDay!=null and finishBusinessDay!=''">
				finish_business_day = #{finishBusinessDay,jdbcType=INTEGER},
				</if>
				<if test="refund!=null">
				refund = #{refund,jdbcType=INTEGER},
				</if>
				<if test="thirdState!=null and thirdState!=''">
				third_state = #{thirdState,jdbcType=INTEGER},
				</if>
				<if test="thirdRefundState!=null">
				third_refund_state = #{thirdRefundState,jdbcType=INTEGER},
				</if>
				<if test="classId!=null and classId!=''">
				class_id = #{classId,jdbcType=INTEGER},
				</if>
				<if test="updateTime!=null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
				</if>
				<if test="updateUserId!=null ">
				update_user_id = #{updateUserId,jdbcType=VARCHAR},
				</if>
				<if test="updateCalssId!=null and updateCalssId!=''">
				update_calss_id = #{updateCalssId,jdbcType=INTEGER},
				</if>
				<if test="updateUserName!=null and updateUserName!=''">
				update_user_name = #{updateUserName,jdbcType=VARCHAR},
				</if>
				<if test="createTime!=null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
				</if>
				<if test="createUserId!=null and createUserId!=''">
				create_user_id = #{createUserId,jdbcType=VARCHAR},
				</if>
				<if test="createUserName!=null and createUserName!=''">
				create_user_name = #{createUserName,jdbcType=VARCHAR}
				</if>
			</set>
		WHERE
			account_third_id = #{accountThirdId,jdbcType=VARCHAR}
	</update>

	<select id="accountThirdSummary" parameterType="com.pms.czaccount.bean.account.search.AccountThirdSummarySearch" resultType="com.pms.czaccount.bean.account.AccountThirdSummary" >
		SELECT
			SUM( amount ) AS sumMoney,
			pay_type AS payType,
			sum(refund) as refund
		FROM
			account_third_pay_recode
		WHERE
			hid = #{hid}
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="finishBusinessDay!=null and finishBusinessDay!=''">
				and finish_business_day = #{finishBusinessDay}
			</if>
			<if test="thirdState!=null and thirdState!=''">
				and third_state = #{thirdState}
			</if>
			<if test="thirdRefundState!=null and thirdRefundState!=''">
				and third_refund_state = #{thirdRefundState}
			</if>
		GROUP BY
	pay_type
	</select>

</mapper>
