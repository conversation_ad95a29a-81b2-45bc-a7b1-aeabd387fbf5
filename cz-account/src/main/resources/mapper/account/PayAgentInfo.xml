<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.PayAgentInfoDao">
	<resultMap id="PayAgentInfoMap" type="com.pms.czaccount.bean.PayAgentInfo">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="alipaySellerId" jdbcType="VARCHAR" column="alipay_seller_id"/>
		<result property="alipayAppAuthToken" jdbcType="VARCHAR" column="alipay_app_auth_token"/>
		<result property="weiChatPaySubMchId" jdbcType="VARCHAR" column="wei_chat_pay_sub_mch_id"/>
		<result property="sqbActiveCode" jdbcType="VARCHAR" column="sqb_active_code"/>
		<result property="sqbBcTerminalSn" jdbcType="VARCHAR" column="sqb_bc_terminal_sn"/>
		<result property="sqbBcTerminalKey" jdbcType="VARCHAR" column="sqb_bc__terminal_key"/>
		<result property="sqbCbTerminalSn" jdbcType="VARCHAR" column="sqb_cb_terminal_sn"/>
		<result property="sqbCbTerminalKey" jdbcType="VARCHAR" column="sqb_cb_terminal_key"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		alipay_seller_id,
		alipay_app_auth_token,
		wei_chat_pay_sub_mch_id,
		sqb_active_code,
		sqb_bc_terminal_sn,
		sqb_bc__terminal_key,
		sqb_cb_terminal_sn,
		sqb_cb_terminal_key
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="PayAgentInfoMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM pay_agent_info 
		WHERE 
			id = #{id} 
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM pay_agent_info WHERE
		id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czaccount.bean.PayAgentInfo" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO pay_agent_info (
			id,
			hid,
			alipay_seller_id,
			alipay_app_auth_token,
			wei_chat_pay_sub_mch_id,
			sqb_active_code,
			sqb_bc_terminal_sn,
			sqb_bc__terminal_key,
			sqb_cb_terminal_sn,
			sqb_cb_terminal_key
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{alipaySellerId,jdbcType=VARCHAR},
			#{alipayAppAuthToken,jdbcType=VARCHAR},
			#{weiChatPaySubMchId,jdbcType=VARCHAR},
			#{sqbActiveCode,jdbcType=VARCHAR},
			#{sqbBcTerminalSn,jdbcType=VARCHAR},
			#{sqbBcTerminalKey,jdbcType=VARCHAR},
			#{sqbCbTerminalSn,jdbcType=VARCHAR},
			#{sqbCbTerminalKey,jdbcType=VARCHAR}
		)
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.search.PayAgentInfoSearch" resultMap="PayAgentInfoMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM pay_agent_info
		<where>
			<if test="id!=null">
				and id = #{id}
			</if>
			<if test="hid!=null">
				and hid = #{hid}
			</if>
			<if test="alipaySellerId!=null and alipaySellerId!=''">
				and alipay_seller_id = #{alipaySellerId}
			</if>
			<if test="alipayAppAuthToken!=null and alipayAppAuthToken!=''">
				and alipay_app_auth_token = #{alipayAppAuthToken}
			</if>
			<if test="weiChatPaySubMchId!=null and weiChatPaySubMchId!=''">
				and wei_chat_pay_sub_mch_id = #{weiChatPaySubMchId}
			</if>
			<if test="sqbActiveCode!=null and sqbActiveCode!=''">
				and sqb_active_code = #{sqbActiveCode}
			</if>
			<if test="sqbBcTerminalSn!=null and sqbBcTerminalSn!=''">
				and sqb_bc_terminal_sn = #{sqbBcTerminalSn}
			</if>
			<if test="sqbBcTerminalKey!=null and sqbBcTerminalKey!=''">
				and sqb_bc__terminal_key = #{sqbBcTerminalKey}
			</if>
			<if test="sqbCbTerminalSn!=null and sqbCbTerminalSn!=''">
				and sqb_cb_terminal_sn = #{sqbCbTerminalSn}
			</if>
			<if test="sqbCbTerminalKey!=null and sqbCbTerminalKey!=''">
				and sqb_cb_terminal_key = #{sqbCbTerminalKey}
			</if>
		</where>
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czaccount.bean.PayAgentInfo">
		UPDATE pay_agent_info 
		<set>
			<if test="hid!=null">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="alipaySellerId!=null and alipaySellerId!=''">
				alipay_seller_id = #{alipaySellerId,jdbcType=VARCHAR},
			</if>
			<if test="alipayAppAuthToken!=null and alipayAppAuthToken!=''">
				alipay_app_auth_token = #{alipayAppAuthToken,jdbcType=VARCHAR},
			</if>
			<if test="weiChatPaySubMchId!=null and weiChatPaySubMchId!=''">
				wei_chat_pay_sub_mch_id = #{weiChatPaySubMchId,jdbcType=VARCHAR},
			</if>
			<if test="sqbActiveCode!=null and sqbActiveCode!=''">
				sqb_active_code = #{sqbActiveCode,jdbcType=VARCHAR},
			</if>
			<if test="sqbBcTerminalSn!=null and sqbBcTerminalSn!=''">
				sqb_bc_terminal_sn = #{sqbBcTerminalSn,jdbcType=VARCHAR},
			</if>
			<if test="sqbBcTerminalKey!=null and sqbBcTerminalKey!=''">
				sqb_bc__terminal_key = #{sqbBcTerminalKey,jdbcType=VARCHAR},
			</if>
			<if test="sqbCbTerminalSn!=null and sqbCbTerminalSn!=''">
				sqb_cb_terminal_sn = #{sqbCbTerminalSn,jdbcType=VARCHAR},
			</if>
			<if test="sqbCbTerminalKey!=null and sqbCbTerminalKey!=''">
				sqb_cb_terminal_key = #{sqbCbTerminalKey,jdbcType=VARCHAR}
			</if>
		</set>
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</update>
	
</mapper>