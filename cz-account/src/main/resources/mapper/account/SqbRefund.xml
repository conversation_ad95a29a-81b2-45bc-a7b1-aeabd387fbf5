<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.SqbRefundDao">
	<resultMap id="SqbRefundMap" type="com.pms.czaccount.bean.SqbRefund">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="mainId" jdbcType="VARCHAR" column="main_id"/>
		<result property="payMainId" jdbcType="VARCHAR" column="pay_main_id"/>
		<result property="refundMoney" jdbcType="INTEGER" column="refund_money"/>
		<result property="refundTime" jdbcType="TIMESTAMP" column="refund_time"/>
		<result property="status" jdbcType="INTEGER" column="status"/>
		<result property="payUserId" jdbcType="VARCHAR" column="pay_user_id"/>
		<result property="source" jdbcType="VARCHAR" column="source"/>
		<result property="terminalSn" jdbcType="VARCHAR" column="terminal_sn"/>
		<result property="payway" jdbcType="INTEGER" column="payway"/>
		<result property="paywayName" jdbcType="VARCHAR" column="payway_name"/>
		<result property="payTradeNo" jdbcType="VARCHAR" column="pay_trade_no"/>
		<result property="interfaceRawResult" jdbcType="LONGVARCHAR" column="interface_raw_result"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		main_id,
		pay_main_id,
		refund_money,
		refund_time,
		status,
		pay_user_id,
		source,
		terminal_sn,
		payway,
		payway_name,
		pay_trade_no,
		interface_raw_result
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="SqbRefundMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM sqb_refund 
		WHERE 
			id = #{id} 
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM sqb_refund WHERE
		id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czaccount.bean.SqbRefund" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO sqb_refund (
			id,
			hid,
			main_id,
			pay_main_id,
			refund_money,
			refund_time,
			status,
			pay_user_id,
			source,
			terminal_sn,
			payway,
			payway_name,
			pay_trade_no,
			interface_raw_result
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{mainId,jdbcType=VARCHAR},
			#{payMainId,jdbcType=VARCHAR},
			#{refundMoney,jdbcType=INTEGER},
			#{refundTime,jdbcType=TIMESTAMP},
			#{status,jdbcType=INTEGER},
			#{payUserId,jdbcType=VARCHAR},
			#{source,jdbcType=VARCHAR},
			#{terminalSn,jdbcType=VARCHAR},
			#{payway,jdbcType=INTEGER},
			#{paywayName,jdbcType=VARCHAR},
			#{payTradeNo,jdbcType=VARCHAR},
			#{interfaceRawResult,jdbcType=LONGVARCHAR}
		)
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.search.SqbRefundSearch" resultMap="SqbRefundMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM sqb_refund
		<where>
			<if test="id!=null">
				and id = #{id}
			</if>
			<if test="hid!=null">
				and hid = #{hid}
			</if>
			<if test="mainId!=null and mainId!=''">
				and main_id = #{mainId}
			</if>
			<if test="payMainId!=null and payMainId!=''">
				and pay_main_id = #{payMainId}
			</if>
			<if test="refundMoney!=null">
				and refund_money = #{refundMoney}
			</if>
			<if test="refundTime!=null">
				and refund_time = #{refundTime}
			</if>
			<if test="status!=null">
				and status = #{status}
			</if>
			<if test="payUserId!=null and payUserId!=''">
				and pay_user_id = #{payUserId}
			</if>
			<if test="source!=null and source!=''">
				and source = #{source}
			</if>
			<if test="terminalSn!=null and terminalSn!=''">
				and terminal_sn = #{terminalSn}
			</if>
			<if test="payway!=null">
				and payway = #{payway}
			</if>
			<if test="paywayName!=null and paywayName!=''">
				and payway_name = #{paywayName}
			</if>
			<if test="payTradeNo!=null and payTradeNo!=''">
				and pay_trade_no = #{payTradeNo}
			</if>
			<if test="interfaceRawResult!=null">
				and interface_raw_result = #{interfaceRawResult}
			</if>
		</where>
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czaccount.bean.SqbRefund">
		UPDATE sqb_refund 
		<set>
			<if test="hid!=null">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="mainId!=null and mainId!=''">
				main_id = #{mainId,jdbcType=VARCHAR},
			</if>
			<if test="payMainId!=null and payMainId!=''">
				pay_main_id = #{payMainId,jdbcType=VARCHAR},
			</if>
			<if test="refundMoney!=null">
				refund_money = #{refundMoney,jdbcType=INTEGER},
			</if>
			<if test="refundTime!=null">
				refund_time = #{refundTime,jdbcType=TIMESTAMP},
			</if>
			<if test="status!=null">
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="payUserId!=null and payUserId!=''">
				pay_user_id = #{payUserId,jdbcType=VARCHAR},
			</if>
			<if test="source!=null and source!=''">
				source = #{source,jdbcType=VARCHAR},
			</if>
			<if test="terminalSn!=null and terminalSn!=''">
				terminal_sn = #{terminalSn,jdbcType=VARCHAR},
			</if>
			<if test="payway!=null">
				payway = #{payway,jdbcType=INTEGER},
			</if>
			<if test="paywayName!=null and paywayName!=''">
				payway_name = #{paywayName,jdbcType=VARCHAR},
			</if>
			<if test="payTradeNo!=null and payTradeNo!=''">
				pay_trade_no = #{payTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="interfaceRawResult!=null">
				interface_raw_result = #{interfaceRawResult,jdbcType=LONGVARCHAR}
			</if>
		</set>
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</update>
	
</mapper>