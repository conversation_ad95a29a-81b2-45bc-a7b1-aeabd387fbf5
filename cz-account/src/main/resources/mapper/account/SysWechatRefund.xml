<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.SysWechatRefundDao">
	<resultMap id="SysWechatRefundMap" type="com.pms.czaccount.bean.pay.SysWechatRefund">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="appId" jdbcType="VARCHAR" column="app_id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="uuid" jdbcType="VARCHAR" column="uuid"/>
		<result property="mchId" jdbcType="VARCHAR" column="mch_id"/>
		<result property="payMainId" jdbcType="VARCHAR" column="pay_main_id"/>
		<result property="mainId" jdbcType="VARCHAR" column="main_id"/>
		<result property="outRefundNo" jdbcType="VARCHAR" column="out_refund_no"/>
		<result property="refundId" jdbcType="VARCHAR" column="refund_id"/>
		<result property="totalFee" jdbcType="INTEGER" column="total_fee"/>
		<result property="refundFee" jdbcType="INTEGER" column="refund_fee"/>
		<result property="updateTime" jdbcType="TIMESTAMP" column="update_time"/>
		<result property="source" jdbcType="VARCHAR" column="source"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		app_id,
		hid,
		uuid,
		mch_id,
		pay_main_id,
		main_id,
		out_refund_no,
		refund_id,
		total_fee,
		refund_fee,
		update_time,
		source
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="SysWechatRefundMap">
		SELECT
		<include refid="BaseColumn" />
		FROM sys_wechat_refund
		WHERE
		id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="deleteSysWechatRefund" parameterType="java.lang.Integer">
		DELETE FROM sys_wechat_refund WHERE
		id = #{id}
	</delete>
	<!-- 插入 -->
	<insert id="saveSysWechatRefund" parameterType="com.pms.czaccount.bean.pay.SysWechatRefund" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO sys_wechat_refund (
		id,
		app_id,
		hid,
		uuid,
		mch_id,
		pay_main_id,
		main_id,
		out_refund_no,
		refund_id,
		total_fee,
		refund_fee,
		update_time,
		source
		) VALUES (
		#{id,jdbcType=INTEGER},
		#{appId,jdbcType=VARCHAR},
		#{hid,jdbcType=INTEGER},
		#{uuid,jdbcType=VARCHAR},
		#{mchId,jdbcType=VARCHAR},
		#{payMainId,jdbcType=VARCHAR},
		#{mainId,jdbcType=VARCHAR},
		#{outRefundNo,jdbcType=VARCHAR},
		#{refundId,jdbcType=VARCHAR},
		#{totalFee,jdbcType=INTEGER},
		#{refundFee,jdbcType=INTEGER},
		#{updateTime,jdbcType=TIMESTAMP},
		#{source,jdbcType=VARCHAR}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.pay.search.SysWechatRefundSearch" resultMap="SysWechatRefundMap">
		SELECT
		<include refid="BaseColumn" />
		FROM sys_wechat_refund
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="appId!=null and appId!=''">
				and app_id = #{appId}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="uuid!=null and uuid!=''">
				and uuid = #{uuid}
			</if>
			<if test="mchId!=null and mchId!=''">
				and mch_id = #{mchId}
			</if>
			<if test="payMainId!=null and payMainId!=''">
				and pay_main_id = #{payMainId}
			</if>
			<if test="mainId!=null and mainId!=''">
				and main_id = #{mainId}
			</if>
			<if test="outRefundNo!=null and outRefundNo!=''">
				and out_refund_no = #{outRefundNo}
			</if>
			<if test="refundId!=null and refundId!=''">
				and refund_id = #{refundId}
			</if>
			<if test="totalFee!=null and totalFee!=''">
				and total_fee = #{totalFee}
			</if>
			<if test="refundFee!=null and refundFee!=''">
				and refund_fee = #{refundFee}
			</if>
			<if test="updateTime!=null">
				and update_time = #{updateTime}
			</if>
			<if test="source!=null and source!=''">
				and source = #{source}
			</if>
		</where>
	</select>

	<!-- 更新 -->
	<update id="editSysWechatRefund" parameterType="com.pms.czaccount.bean.pay.SysWechatRefund">
		UPDATE sys_wechat_refund
		<set>
			<if test="appId!=null and appId!=''">
				app_id = #{appId,jdbcType=VARCHAR},
			</if>
			<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="uuid!=null and uuid!=''">
				uuid = #{uuid,jdbcType=VARCHAR},
			</if>
			<if test="mchId!=null and mchId!=''">
				mch_id = #{mchId,jdbcType=VARCHAR},
			</if>
			<if test="payMainId!=null and payMainId!=''">
				pay_main_id = #{payMainId,jdbcType=VARCHAR},
			</if>
			<if test="mainId!=null and mainId!=''">
				main_id = #{mainId,jdbcType=VARCHAR},
			</if>
			<if test="outRefundNo!=null and outRefundNo!=''">
				out_refund_no = #{outRefundNo,jdbcType=VARCHAR},
			</if>
			<if test="refundId!=null and refundId!=''">
				refund_id = #{refundId,jdbcType=VARCHAR},
			</if>
			<if test="totalFee!=null and totalFee!=''">
				total_fee = #{totalFee,jdbcType=INTEGER},
			</if>
			<if test="refundFee!=null and refundFee!=''">
				refund_fee = #{refundFee,jdbcType=INTEGER},
			</if>
			<if test="updateTime!=null">
				update_time = #{updateTime,jdbcType=TIMESTAMP},
			</if>
			<if test="source!=null and source!=''">
				source = #{source,jdbcType=VARCHAR}
			</if>
		</set>
		WHERE
		id = #{id,jdbcType=INTEGER}
	</update>

</mapper>
