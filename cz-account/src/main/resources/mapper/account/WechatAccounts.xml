<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.WechatAccountsDao">
	<resultMap id="WechatAccountsMap" type="com.pms.czaccount.bean.pay.WechatAccounts">
		<result property="id" jdbcType="INTEGER" column="ID"/>
		<result property="hid" jdbcType="VARCHAR" column="hid"/>
		<result property="hotelOpenId" jdbcType="VARCHAR" column="hotel_open_id"/>
		<result property="hotelWechatAccount" jdbcType="VARCHAR" column="hotel_wechat_account"/>
		<result property="hotelWechatPassword" jdbcType="VARCHAR" column="hotel_wechat_password"/>
		<result property="appId" jdbcType="VARCHAR" column="app_id"/>
		<result property="appSecret" jdbcType="VARCHAR" column="app_secret"/>
		<result property="accessToken" jdbcType="VARCHAR" column="access_token"/>
		<result property="isSercret" jdbcType="VARCHAR" column="is_sercret"/>
		<result property="encryptionKey" jdbcType="VARCHAR" column="encryption_key"/>
		<result property="getTime" jdbcType="VARCHAR" column="get_time"/>
		<result property="getMillisecond" jdbcType="VARCHAR" column="get_millisecond"/>
		<result property="validateMillisecond" jdbcType="VARCHAR" column="validate_millisecond"/>
		<result property="validateTime" jdbcType="VARCHAR" column="validate_time"/>
		<result property="updateTime" jdbcType="VARCHAR" column="update_time"/>
		<result property="updatePerson" jdbcType="VARCHAR" column="update_person"/>
		<result property="updatePersonId" jdbcType="VARCHAR" column="update_person_id"/>
		<result property="jsApiTicket" jdbcType="VARCHAR" column="js_api_ticket"/>
		<result property="jsGetTime" jdbcType="VARCHAR" column="js_get_time"/>
		<result property="jsGetMillisecond" jdbcType="VARCHAR" column="js_get_millisecond"/>
		<result property="jsValidateMillisecond" jdbcType="VARCHAR" column="js_validate_millisecond"/>
		<result property="isPay" jdbcType="VARCHAR" column="is_pay"/>
		<result property="mchId" jdbcType="VARCHAR" column="mch_id"/>
		<result property="apiKey" jdbcType="VARCHAR" column="api_key"/>
		<result property="filePath" jdbcType="VARCHAR" column="file_path"/>
		<result property="filePath1" jdbcType="VARCHAR" column="file_path1"/>
		<result property="notifyUrl" jdbcType="VARCHAR" column="notify_url"/>
	</resultMap>
	<sql id="BaseColumn">
		ID,
		hid,
		hotel_open_id,
		hotel_wechat_account,
		hotel_wechat_password,
		app_id,
		app_secret,
		access_token,
		is_sercret,
		encryption_key,
		get_time,
		get_millisecond,
		validate_millisecond,
		validate_time,
		update_time,
		update_person,
		update_person_id,
		js_api_ticket,
		js_get_time,
		js_get_millisecond,
		js_validate_millisecond,
		is_pay,
		mch_id,
		api_key,
		file_path,
		file_path1,
		notify_url
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="WechatAccountsMap">
		SELECT
			<include refid="BaseColumn" />
		FROM wechat_accounts
		WHERE
			ID = #{id}
	</select>

	<select id="selectByHid" parameterType="java.lang.Integer" resultMap="WechatAccountsMap">
		SELECT
		<include refid="BaseColumn" />
		FROM wechat_accounts
		WHERE
		hid = #{hid}
	</select>

	<!-- 按Id删除 -->
	<delete id="deleteWechatAccounts" parameterType="java.lang.Integer">
		DELETE FROM wechat_accounts WHERE
		ID = #{id}
	</delete>
	 <!-- 插入 -->
	<insert id="saveWechatAccounts" parameterType="com.pms.czaccount.bean.pay.WechatAccounts" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO wechat_accounts (
			ID,
			hid,
			hotel_open_id,
			hotel_wechat_account,
			hotel_wechat_password,
			app_id,
			app_secret,
			access_token,
			is_sercret,
			encryption_key,
			get_time,
			get_millisecond,
			validate_millisecond,
			validate_time,
			update_time,
			update_person,
			update_person_id,
			js_api_ticket,
			js_get_time,
			js_get_millisecond,
			js_validate_millisecond,
			is_pay,
			mch_id,
			api_key,
			file_path,
			file_path1
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=VARCHAR},
			#{hotelOpenId,jdbcType=VARCHAR},
			#{hotelWechatAccount,jdbcType=VARCHAR},
			#{hotelWechatPassword,jdbcType=VARCHAR},
			#{appId,jdbcType=VARCHAR},
			#{appSecret,jdbcType=VARCHAR},
			#{accessToken,jdbcType=VARCHAR},
			#{isSercret,jdbcType=VARCHAR},
			#{encryptionKey,jdbcType=VARCHAR},
			#{getTime,jdbcType=VARCHAR},
			#{getMillisecond,jdbcType=VARCHAR},
			#{validateMillisecond,jdbcType=VARCHAR},
			#{validateTime,jdbcType=VARCHAR},
			#{updateTime,jdbcType=VARCHAR},
			#{updatePerson,jdbcType=VARCHAR},
			#{updatePersonId,jdbcType=VARCHAR},
			#{jsApiTicket,jdbcType=VARCHAR},
			#{jsGetTime,jdbcType=VARCHAR},
			#{jsGetMillisecond,jdbcType=VARCHAR},
			#{jsValidateMillisecond,jdbcType=VARCHAR},
			#{isPay,jdbcType=VARCHAR},
			#{mchId,jdbcType=VARCHAR},
			#{apiKey,jdbcType=VARCHAR},
			#{filePath,jdbcType=VARCHAR},
			#{filePath1,jdbcType=VARCHAR}
		)

	</insert>

	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.pay.search.WechatAccountsSearch" resultMap="WechatAccountsMap">
		SELECT
			<include refid="BaseColumn" />
		FROM wechat_accounts
		<where>
			<if test="id!=null and id!=''">
				and ID = #{id}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelOpenId!=null and hotelOpenId!=''">
				and hotel_open_id = #{hotelOpenId}
			</if>
			<if test="hotelWechatAccount!=null and hotelWechatAccount!=''">
				and hotel_wechat_account = #{hotelWechatAccount}
			</if>
			<if test="hotelWechatPassword!=null and hotelWechatPassword!=''">
				and hotel_wechat_password = #{hotelWechatPassword}
			</if>
			<if test="appId!=null and appId!=''">
				and app_id = #{appId}
			</if>
			<if test="appSecret!=null and appSecret!=''">
				and app_secret = #{appSecret}
			</if>
			<if test="accessToken!=null and accessToken!=''">
				and access_token = #{accessToken}
			</if>
			<if test="isSercret!=null and isSercret!=''">
				and is_sercret = #{isSercret}
			</if>
			<if test="encryptionKey!=null and encryptionKey!=''">
				and encryption_key = #{encryptionKey}
			</if>
			<if test="getTime!=null and getTime!=''">
				and get_time = #{getTime}
			</if>
			<if test="getMillisecond!=null and getMillisecond!=''">
				and get_millisecond = #{getMillisecond}
			</if>
			<if test="validateMillisecond!=null and validateMillisecond!=''">
				and validate_millisecond = #{validateMillisecond}
			</if>
			<if test="validateTime!=null and validateTime!=''">
				and validate_time = #{validateTime}
			</if>
			<if test="updateTime!=null and updateTime!=''">
				and update_time = #{updateTime}
			</if>
			<if test="updatePerson!=null and updatePerson!=''">
				and update_person = #{updatePerson}
			</if>
			<if test="updatePersonId!=null and updatePersonId!=''">
				and update_person_id = #{updatePersonId}
			</if>
			<if test="jsApiTicket!=null and jsApiTicket!=''">
				and js_api_ticket = #{jsApiTicket}
			</if>
			<if test="jsGetTime!=null and jsGetTime!=''">
				and js_get_time = #{jsGetTime}
			</if>
			<if test="jsGetMillisecond!=null and jsGetMillisecond!=''">
				and js_get_millisecond = #{jsGetMillisecond}
			</if>
			<if test="jsValidateMillisecond!=null and jsValidateMillisecond!=''">
				and js_validate_millisecond = #{jsValidateMillisecond}
			</if>
			<if test="isPay!=null and isPay!=''">
				and is_pay = #{isPay}
			</if>
			<if test="mchId!=null and mchId!=''">
				and mch_id = #{mchId}
			</if>
			<if test="apiKey!=null and apiKey!=''">
				and api_key = #{apiKey}
			</if>
			<if test="filePath!=null and filePath!=''">
				and file_path = #{filePath}
			</if>
			<if test="filePath1!=null and filePath1!=''">
				and file_path1 = #{filePath1}
			</if>
		</where>
	</select>

	<!-- 更新 -->
	<update id="editWechatAccounts" parameterType="com.pms.czaccount.bean.pay.WechatAccounts">
		UPDATE wechat_accounts
			<set>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=VARCHAR},
				</if>
				<if test="hotelOpenId!=null and hotelOpenId!=''">
				hotel_open_id = #{hotelOpenId,jdbcType=VARCHAR},
				</if>
				<if test="hotelWechatAccount!=null and hotelWechatAccount!=''">
				hotel_wechat_account = #{hotelWechatAccount,jdbcType=VARCHAR},
				</if>
				<if test="hotelWechatPassword!=null and hotelWechatPassword!=''">
				hotel_wechat_password = #{hotelWechatPassword,jdbcType=VARCHAR},
				</if>
				<if test="appId!=null and appId!=''">
				app_id = #{appId,jdbcType=VARCHAR},
				</if>
				<if test="appSecret!=null and appSecret!=''">
				app_secret = #{appSecret,jdbcType=VARCHAR},
				</if>
				<if test="accessToken!=null and accessToken!=''">
				access_token = #{accessToken,jdbcType=VARCHAR},
				</if>
				<if test="isSercret!=null and isSercret!=''">
				is_sercret = #{isSercret,jdbcType=VARCHAR},
				</if>
				<if test="encryptionKey!=null and encryptionKey!=''">
				encryption_key = #{encryptionKey,jdbcType=VARCHAR},
				</if>
				<if test="getTime!=null and getTime!=''">
				get_time = #{getTime,jdbcType=VARCHAR},
				</if>
				<if test="getMillisecond!=null and getMillisecond!=''">
				get_millisecond = #{getMillisecond,jdbcType=VARCHAR},
				</if>
				<if test="validateMillisecond!=null and validateMillisecond!=''">
				validate_millisecond = #{validateMillisecond,jdbcType=VARCHAR},
				</if>
				<if test="validateTime!=null and validateTime!=''">
				validate_time = #{validateTime,jdbcType=VARCHAR},
				</if>
				<if test="updateTime!=null and updateTime!=''">
				update_time = #{updateTime,jdbcType=VARCHAR},
				</if>
				<if test="updatePerson!=null and updatePerson!=''">
				update_person = #{updatePerson,jdbcType=VARCHAR},
				</if>
				<if test="updatePersonId!=null and updatePersonId!=''">
				update_person_id = #{updatePersonId,jdbcType=VARCHAR},
				</if>
				<if test="jsApiTicket!=null and jsApiTicket!=''">
				js_api_ticket = #{jsApiTicket,jdbcType=VARCHAR},
				</if>
				<if test="jsGetTime!=null and jsGetTime!=''">
				js_get_time = #{jsGetTime,jdbcType=VARCHAR},
				</if>
				<if test="jsGetMillisecond!=null and jsGetMillisecond!=''">
				js_get_millisecond = #{jsGetMillisecond,jdbcType=VARCHAR},
				</if>
				<if test="jsValidateMillisecond!=null and jsValidateMillisecond!=''">
				js_validate_millisecond = #{jsValidateMillisecond,jdbcType=VARCHAR},
				</if>
				<if test="isPay!=null and isPay!=''">
				is_pay = #{isPay,jdbcType=VARCHAR},
				</if>
				<if test="mchId!=null and mchId!=''">
				mch_id = #{mchId,jdbcType=VARCHAR},
				</if>
				<if test="apiKey!=null and apiKey!=''">
				api_key = #{apiKey,jdbcType=VARCHAR},
				</if>
				<if test="filePath!=null and filePath!=''">
				file_path = #{filePath,jdbcType=VARCHAR},
				</if>
				<if test="filePath1!=null and filePath1!=''">
				file_path1 = #{filePath1,jdbcType=VARCHAR}
				</if>
			</set>
		WHERE
			ID = #{id,jdbcType=INTEGER}
	</update>

</mapper>
