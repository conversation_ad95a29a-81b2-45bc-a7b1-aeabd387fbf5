<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.SqbTransactionDao">
	<resultMap id="SqbTransactionMap" type="com.pms.czaccount.bean.SqbTransaction">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="uuid" jdbcType="VARCHAR" column="uuid"/>
		<result property="source" jdbcType="VARCHAR" column="source"/>
		<result property="money" jdbcType="INTEGER" column="money"/>
		<result property="tradeNo" jdbcType="VARCHAR" column="trade_no"/>
		<result property="openId" jdbcType="VARCHAR" column="open_id"/>
		<result property="payerUid" jdbcType="VARCHAR" column="payer_uid"/>
		<result property="mainId" jdbcType="VARCHAR" column="main_id"/>
		<result property="status" jdbcType="INTEGER" column="status"/>
		<result property="payTime" jdbcType="TIMESTAMP" column="pay_time"/>
		<result property="buyerUserId" jdbcType="VARCHAR" column="buyer_user_id"/>
		<result property="refundMoney" jdbcType="INTEGER" column="refund_money"/>
		<result property="refundTime" jdbcType="TIMESTAMP" column="refund_time"/>
		<result property="scanTime" jdbcType="TIMESTAMP" column="scan_time"/>
		<result property="registId" jdbcType="INTEGER" column="regist_id"/>
		<result property="bookingId" jdbcType="INTEGER" column="booking_id"/>
		<result property="terminalSn" jdbcType="VARCHAR" column="terminal_sn"/>
		<result property="payway" jdbcType="INTEGER" column="payway"/>
		<result property="paywayName" jdbcType="VARCHAR" column="payway_name"/>
		<result property="payTradeNo" jdbcType="VARCHAR" column="pay_trade_no"/>
		<result property="interfaceRawResult" jdbcType="LONGVARCHAR" column="interface_raw_result"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hid,
		uuid,
		source,
		money,
		trade_no,
		open_id,
		payer_uid,
		main_id,
		status,
		pay_time,
		buyer_user_id,
		refund_money,
		refund_time,
		scan_time,
		regist_id,
		booking_id,
		terminal_sn,
		payway,
		payway_name,
		pay_trade_no,
		interface_raw_result
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="SqbTransactionMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM sqb_transaction 
		WHERE 
			id = #{id} 
	</select>

	<select id="selectNotCollection"  parameterType="com.pms.czaccount.bean.search.SqbTransactionSearch" resultMap="SqbTransactionMap">
		SELECT <include refid="BaseColumn" />
		FROM sqb_transaction WHERE main_id NOT in (SELECT third_accout_id FROM account WHERE third_accout_id is NOT null and hid = #{hid}) AND status = 1 and hid = #{hid} and pay_time >= #{payTime}  order by pay_time desc
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM sqb_transaction WHERE
		id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czaccount.bean.SqbTransaction" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO sqb_transaction (
			id,
			hid,
			uuid,
			source,
			money,
			trade_no,
			open_id,
			payer_uid,
			main_id,
			status,
			pay_time,
			buyer_user_id,
			refund_money,
			refund_time,
			scan_time,
			regist_id,
			booking_id,
			terminal_sn,
			payway,
			payway_name,
			pay_trade_no,
			interface_raw_result
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{uuid,jdbcType=VARCHAR},
			#{source,jdbcType=VARCHAR},
			#{money,jdbcType=INTEGER},
			#{tradeNo,jdbcType=VARCHAR},
			#{openId,jdbcType=VARCHAR},
			#{payerUid,jdbcType=VARCHAR},
			#{mainId,jdbcType=VARCHAR},
			#{status,jdbcType=INTEGER},
			#{payTime,jdbcType=TIMESTAMP},
			#{buyerUserId,jdbcType=VARCHAR},
			#{refundMoney,jdbcType=INTEGER},
			#{refundTime,jdbcType=TIMESTAMP},
			#{scanTime,jdbcType=TIMESTAMP},
			#{registId,jdbcType=INTEGER},
			#{bookingId,jdbcType=INTEGER},
			#{terminalSn,jdbcType=VARCHAR},
			#{payway,jdbcType=INTEGER},
			#{paywayName,jdbcType=VARCHAR},
			#{payTradeNo,jdbcType=VARCHAR},
			#{interfaceRawResult,jdbcType=LONGVARCHAR}
		)
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.search.SqbTransactionSearch" resultMap="SqbTransactionMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM sqb_transaction
		<where>
			<if test="id!=null">
				and id = #{id}
			</if>
			<if test="hid!=null">
				and hid = #{hid}
			</if>
			<if test="uuid!=null and uuid!=''">
				and uuid = #{uuid}
			</if>
			<if test="source!=null and source!=''">
				and source = #{source}
			</if>
			<if test="money!=null">
				and money = #{money}
			</if>
			<if test="tradeNo!=null and tradeNo!=''">
				and trade_no = #{tradeNo}
			</if>
			<if test="openId!=null and openId!=''">
				and open_id = #{openId}
			</if>
			<if test="payerUid!=null and payerUid!=''">
				and payer_uid = #{payerUid}
			</if>
			<if test="mainId!=null and mainId!=''">
				and main_id = #{mainId}
			</if>
			<if test="status!=null">
				and status = #{status}
			</if>
			<if test="payTime!=null">
				and pay_time = #{payTime}
			</if>
			<if test="buyerUserId!=null and buyerUserId!=''">
				and buyer_user_id = #{buyerUserId}
			</if>
			<if test="refundMoney!=null">
				and refund_money = #{refundMoney}
			</if>
			<if test="refundTime!=null">
				and refund_time = #{refundTime}
			</if>
			<if test="scanTime!=null">
				and scan_time = #{scanTime}
			</if>
			<if test="registId!=null">
				and regist_id = #{registId}
			</if>
			<if test="bookingId!=null">
				and booking_id = #{bookingId}
			</if>
			<if test="terminalSn!=null and terminalSn!=''">
				and terminal_sn = #{terminalSn}
			</if>
			<if test="payway!=null">
				and payway = #{payway}
			</if>
			<if test="paywayName!=null and paywayName!=''">
				and payway_name = #{paywayName}
			</if>
			<if test="payTradeNo!=null and payTradeNo!=''">
				and pay_trade_no = #{payTradeNo}
			</if>
			<if test="interfaceRawResult!=null">
				and interface_raw_result = #{interfaceRawResult}
			</if>
		</where>
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czaccount.bean.SqbTransaction">
		UPDATE sqb_transaction 
		<set>
			<if test="hid!=null">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="uuid!=null and uuid!=''">
				uuid = #{uuid,jdbcType=VARCHAR},
			</if>
			<if test="source!=null and source!=''">
				source = #{source,jdbcType=VARCHAR},
			</if>
			<if test="money!=null">
				money = #{money,jdbcType=INTEGER},
			</if>
			<if test="tradeNo!=null and tradeNo!=''">
				trade_no = #{tradeNo,jdbcType=VARCHAR},
			</if>
			<if test="openId!=null and openId!=''">
				open_id = #{openId,jdbcType=VARCHAR},
			</if>
			<if test="payerUid!=null and payerUid!=''">
				payer_uid = #{payerUid,jdbcType=VARCHAR},
			</if>
			<if test="mainId!=null and mainId!=''">
				main_id = #{mainId,jdbcType=VARCHAR},
			</if>
			<if test="status!=null">
				status = #{status,jdbcType=INTEGER},
			</if>
			<if test="payTime!=null">
				pay_time = #{payTime,jdbcType=TIMESTAMP},
			</if>
			<if test="buyerUserId!=null and buyerUserId!=''">
				buyer_user_id = #{buyerUserId,jdbcType=VARCHAR},
			</if>
			<if test="refundMoney!=null">
				refund_money = #{refundMoney,jdbcType=INTEGER},
			</if>
			<if test="refundTime!=null">
				refund_time = #{refundTime,jdbcType=TIMESTAMP},
			</if>
			<if test="scanTime!=null">
				scan_time = #{scanTime,jdbcType=TIMESTAMP},
			</if>
			<if test="registId!=null">
				regist_id = #{registId,jdbcType=INTEGER},
			</if>
			<if test="bookingId!=null">
				booking_id = #{bookingId,jdbcType=INTEGER},
			</if>
			<if test="terminalSn!=null and terminalSn!=''">
				terminal_sn = #{terminalSn,jdbcType=VARCHAR},
			</if>
			<if test="payway!=null">
				payway = #{payway,jdbcType=INTEGER},
			</if>
			<if test="paywayName!=null and paywayName!=''">
				payway_name = #{paywayName,jdbcType=VARCHAR},
			</if>
			<if test="payTradeNo!=null and payTradeNo!=''">
				pay_trade_no = #{payTradeNo,jdbcType=VARCHAR},
			</if>
			<if test="interfaceRawResult!=null">
				interface_raw_result = #{interfaceRawResult,jdbcType=LONGVARCHAR}
			</if>
		</set>
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</update>
	
</mapper>