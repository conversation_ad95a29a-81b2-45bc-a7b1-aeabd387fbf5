<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.pay.WechatSubscribeMsgDao">
	<resultMap id="WechatSubscribeMsgMap" type="com.pms.czaccount.bean.pay.WechatSubscribeMsg">
		<result property="id" jdbcType="INTEGER" column="id"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="msgType" jdbcType="INTEGER" column="msg_type"/>
		<result property="msgId" jdbcType="VARCHAR" column="msg_id"/>
		<result property="msgName" jdbcType="VARCHAR" column="msg_name"/>
	</resultMap>
	<sql id="BaseColumn">
		id,
		hotel_group_id,
		hid,
		msg_type,
		msg_id,
		msg_name
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="WechatSubscribeMsgMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM wechat_subscribe_msg 
		WHERE 
			id = #{id} 
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM wechat_subscribe_msg WHERE
		id = #{id} 
	</delete>
	 <!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czaccount.bean.pay.WechatSubscribeMsg" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO wechat_subscribe_msg (
			id,
			hotel_group_id,
			hid,
			msg_type,
			msg_id,
			msg_name
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{hotelGroupId,jdbcType=INTEGER},
			#{hid,jdbcType=INTEGER},
			#{msgType,jdbcType=INTEGER},
			#{msgId,jdbcType=VARCHAR},
			#{msgName,jdbcType=VARCHAR}
		)
		
	</insert>
	
	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.pay.search.WechatSubscribeMsgSearch" resultMap="WechatSubscribeMsgMap">
		SELECT 
			<include refid="BaseColumn" />
		FROM wechat_subscribe_msg
		<where>
			<if test="id!=null and id!=''">
				and id = #{id}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="msgType!=null and msgType!=''">
				and msg_type = #{msgType}
			</if>
			<if test="msgId!=null and msgId!=''">
				and msg_id = #{msgId}
			</if>
			<if test="msgName!=null and msgName!=''">
				and msg_name = #{msgName}
			</if>
		</where>
	</select>
	
	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czaccount.bean.pay.WechatSubscribeMsg">
		UPDATE wechat_subscribe_msg 
			<set>
				<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
				</if>
				<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
				</if>
				<if test="msgType!=null and msgType!=''">
				msg_type = #{msgType,jdbcType=INTEGER},
				</if>
				<if test="msgId!=null and msgId!=''">
				msg_id = #{msgId,jdbcType=VARCHAR},
				</if>
				<if test="msgName!=null and msgName!=''">
				msg_name = #{msgName,jdbcType=VARCHAR}
				</if>
			</set>
		WHERE 
			id = #{id,jdbcType=INTEGER}
	</update>
	
</mapper>