<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.pms.czaccount.dao.account.AccountCancelDao">
	<resultMap id="AccountCancelMap" type="com.pms.czaccount.bean.account.AccountCancel">
		<result property="accountCancelId" jdbcType="INTEGER" column="account_cancel_id"/>
		<result property="accountId" jdbcType="VARCHAR" column="account_id"/>
		<result property="price" jdbcType="INTEGER" column="price"/>
		<result property="payType" jdbcType="INTEGER" column="pay_type"/>
		<result property="hid" jdbcType="INTEGER" column="hid"/>
		<result property="hotelGroupId" jdbcType="INTEGER" column="hotel_group_id"/>
		<result property="payClassId" jdbcType="INTEGER" column="pay_class_id"/>
		<result property="payClassName" jdbcType="VARCHAR" column="pay_class_name"/>
		<result property="payCodeId" jdbcType="INTEGER" column="pay_code_id"/>
		<result property="payCodeName" jdbcType="VARCHAR" column="pay_code_name"/>
		<result property="roomInfoId" jdbcType="INTEGER" column="room_info_id"/>
		<result property="roomNum" jdbcType="VARCHAR" column="room_num"/>
		<result property="accountCode" jdbcType="VARCHAR" column="account_code"/>
		<result property="accountCreateUserName" jdbcType="VARCHAR" column="account_create_user_name"/>
		<result property="isSale" jdbcType="INTEGER" column="is_sale"/>
		<result property="businessDay" jdbcType="INTEGER" column="business_day"/>
		<result property="classId" jdbcType="INTEGER" column="class_id"/>
		<result property="createTime" jdbcType="TIMESTAMP" column="create_time"/>
		<result property="createUserId" jdbcType="VARCHAR" column="create_user_id"/>
		<result property="bookingId" jdbcType="INTEGER" column="booking_id"/>
		<result property="registId" jdbcType="INTEGER" column="regist_id"/>
		<result property="registPersonName" jdbcType="VARCHAR" column="regist_person_name"/>
		<result property="registPersonId" jdbcType="INTEGER" column="regist_person_id"/>
		<result property="teamCodeId" jdbcType="INTEGER" column="team_code_id"/>
		<result property="createUserName" jdbcType="VARCHAR" column="create_user_name"/>
		<result property="sourceRoomNum" jdbcType="VARCHAR" column="source_room_num"/>
		<result property="sourceRegistId" jdbcType="INTEGER" column="source_regist_id"/>
		<result property="sourceRegistPersonName" jdbcType="VARCHAR" column="source_regist_person_name"/>
		<result property="sourceRegistPersonId" jdbcType="INTEGER" column="source_regist_person_id"/>
		<result property="cancelType" jdbcType="INTEGER" column="cancel_type"/>
		<result property="reason" jdbcType="VARCHAR" column="reason"/>
		<result property="remark" jdbcType="VARCHAR" column="remark"/>
	</resultMap>
	<sql id="BaseColumn">
		account_cancel_id,
		account_id,
		price,
		pay_type,
		hid,
		hotel_group_id,
		pay_class_id,
		pay_class_name,
		pay_code_id,
		pay_code_name,
		room_info_id,
		room_num,
		account_code,
		account_create_user_name,
		is_sale,
		business_day,
		class_id,
		create_time,
		create_user_id,
		booking_id,
		regist_id,
		regist_person_name,
		regist_person_id,
		team_code_id,
		create_user_name,
		source_room_num,
		source_regist_id,
		source_regist_person_name,
		source_regist_person_id,
		cancel_type,
		reason,
		remark
	</sql>
	<select id="selectById" parameterType="java.lang.Integer" resultMap="AccountCancelMap">
		SELECT
		<include refid="BaseColumn" />
		FROM account_cancel
		WHERE
		account_cancel_id = #{id}
	</select>

	<!-- 按Id删除 -->
	<delete id="delete" parameterType="java.lang.Integer">
		DELETE FROM account_cancel WHERE
			account_cancel_id = #{id}
	</delete>
	<!-- 插入 -->
	<insert id="insert" parameterType="com.pms.czaccount.bean.account.AccountCancel" useGeneratedKeys="true" keyProperty="accountCancelId">
		INSERT INTO account_cancel (
			account_id,
			price,
			pay_type,
			hid,
			hotel_group_id,
			pay_class_id,
			pay_class_name,
			pay_code_id,
			pay_code_name,
			room_info_id,
			room_num,
			account_code,
			account_create_user_name,
			is_sale,
			business_day,
			class_id,
			create_time,
			create_user_id,
			booking_id,
			regist_id,
			regist_person_name,
			regist_person_id,
			team_code_id,
			create_user_name,
			source_room_num,
			source_regist_id,
			source_regist_person_name,
			source_regist_person_id,
			cancel_type,
			reason,
			remark
		) VALUES (
					 #{accountId,jdbcType=VARCHAR},
					 #{price,jdbcType=INTEGER},
					 #{payType,jdbcType=INTEGER},
					 #{hid,jdbcType=INTEGER},
					 #{hotelGroupId,jdbcType=INTEGER},
					 #{payClassId,jdbcType=INTEGER},
					 #{payClassName,jdbcType=VARCHAR},
					 #{payCodeId,jdbcType=INTEGER},
					 #{payCodeName,jdbcType=VARCHAR},
					 #{roomInfoId,jdbcType=INTEGER},
					 #{roomNum,jdbcType=VARCHAR},
					 #{accountCode,jdbcType=VARCHAR},
					 #{accountCreateUserName,jdbcType=VARCHAR},
					 #{isSale,jdbcType=INTEGER},
					 #{businessDay,jdbcType=INTEGER},
					 #{classId,jdbcType=INTEGER},
					 #{createTime,jdbcType=TIMESTAMP},
					 #{createUserId,jdbcType=VARCHAR},
					 #{bookingId,jdbcType=INTEGER},
					 #{registId,jdbcType=INTEGER},
					 #{registPersonName,jdbcType=VARCHAR},
					 #{registPersonId,jdbcType=INTEGER},
					 #{teamCodeId,jdbcType=INTEGER},
					 #{createUserName,jdbcType=VARCHAR},
					 #{sourceRoomNum,jdbcType=VARCHAR},
					 #{sourceRegistId,jdbcType=INTEGER},
					 #{sourceRegistPersonName,jdbcType=VARCHAR},
					 #{sourceRegistPersonId,jdbcType=INTEGER},
					 #{cancelType,jdbcType=INTEGER},
					 #{reason,jdbcType=VARCHAR},
					 #{remark,jdbcType=VARCHAR}
				 )

	</insert>


	<!-- 批量添加 -->
	<update id="insertList" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open="" close="" separator=";">
			INSERT INTO account_cancel (
			account_id,
			price,
			pay_type,
			hid,
			hotel_group_id,
			pay_class_id,
			pay_class_name,
			pay_code_id,
			pay_code_name,
			room_info_id,
			room_num,
			account_code,
			account_create_user_name,
			is_sale,
			business_day,
			class_id,
			create_time,
			create_user_id,
			booking_id,
			regist_id,
			regist_person_name,
			regist_person_id,
			team_code_id,
			create_user_name,
			source_room_num,
			source_regist_id,
			source_regist_person_name,
			source_regist_person_id,
			cancel_type,
			reason,
			remark
			) VALUES (
			#{item.accountId,jdbcType=VARCHAR},
			#{item.price,jdbcType=INTEGER},
			#{item.payType,jdbcType=INTEGER},
			#{item.hid,jdbcType=INTEGER},
			#{item.hotelGroupId,jdbcType=INTEGER},
			#{item.payClassId,jdbcType=INTEGER},
			#{item.payClassName,jdbcType=VARCHAR},
			#{item.payCodeId,jdbcType=INTEGER},
			#{item.payCodeName,jdbcType=VARCHAR},
			#{item.roomInfoId,jdbcType=INTEGER},
			#{item.roomNum,jdbcType=VARCHAR},
			#{item.accountCode,jdbcType=VARCHAR},
			#{item.accountCreateUserName,jdbcType=VARCHAR},
			#{item.isSale,jdbcType=INTEGER},
			#{item.businessDay,jdbcType=INTEGER},
			#{item.classId,jdbcType=INTEGER},
			#{item.createTime,jdbcType=TIMESTAMP},
			#{item.createUserId,jdbcType=VARCHAR},
			#{item.bookingId,jdbcType=INTEGER},
			#{item.registId,jdbcType=INTEGER},
			#{item.registPersonName,jdbcType=VARCHAR},
			#{item.registPersonId,jdbcType=INTEGER},
			#{item.teamCodeId,jdbcType=INTEGER},
			#{item.createUserName,jdbcType=VARCHAR},
			#{item.sourceRoomNum,jdbcType=VARCHAR},
			#{item.sourceRegistId,jdbcType=INTEGER},
			#{item.sourceRegistPersonName,jdbcType=VARCHAR},
			#{item.sourceRegistPersonId,jdbcType=INTEGER},
			#{item.cancelType,jdbcType=INTEGER},
			#{item.reason,jdbcType=VARCHAR},
			#{item.remark,jdbcType=VARCHAR}
			)

		</foreach>
	</update>

	<select id="selectBySearch" parameterType="com.pms.czaccount.bean.account.search.AccountCancelSearch" resultMap="AccountCancelMap">
		SELECT
		<include refid="BaseColumn" />
		FROM account_cancel
		<where>
			<if test="accountCancelId!=null and accountCancelId!=''">
				and account_cancel_id = #{accountCancelId}
			</if>
			<if test="accountId!=null and accountId!=''">
				and account_id = #{accountId}
			</if>
			<if test="price!=null and price!=''">
				and price = #{price}
			</if>
			<if test="payType!=null and payType!=''">
				and pay_type = #{payType}
			</if>
			<if test="hid!=null and hid!=''">
				and hid = #{hid}
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				and hotel_group_id = #{hotelGroupId}
			</if>
			<if test="payClassId!=null and payClassId!=''">
				and pay_class_id = #{payClassId}
			</if>
			<if test="payClassName!=null and payClassName!=''">
				and pay_class_name = #{payClassName}
			</if>
			<if test="payCodeId!=null and payCodeId!=''">
				and pay_code_id = #{payCodeId}
			</if>
			<if test="payCodeName!=null and payCodeName!=''">
				and pay_code_name = #{payCodeName}
			</if>
			<if test="roomInfoId!=null and roomInfoId!=''">
				and room_info_id = #{roomInfoId}
			</if>
			<if test="roomNum!=null and roomNum!=''">
				and room_num = #{roomNum}
			</if>
			<if test="accountCode!=null and accountCode!=''">
				and account_code = #{accountCode}
			</if>
			<if test="accountCreateUserName!=null and accountCreateUserName!=''">
				and account_create_user_name = #{accountCreateUserName}
			</if>
			<if test="isSale!=null and isSale!=''">
				and is_sale = #{isSale}
			</if>
			<if test="businessDay!=null and businessDay!=''">
				and business_day = #{businessDay}
			</if>
			<if test="classId!=null and classId!=''">
				and class_id = #{classId}
			</if>
			<if test="createTime!=null and createTime!=''">
				and create_time = #{createTime}
			</if>
			<if test="createUserId!=null and createUserId!=''">
				and create_user_id = #{createUserId}
			</if>
			<if test="bookingId!=null and bookingId!=''">
				and booking_id = #{bookingId}
			</if>
			<if test="registId!=null and registId!=''">
				and regist_id = #{registId}
			</if>
			<if test="registPersonName!=null and registPersonName!=''">
				and regist_person_name = #{registPersonName}
			</if>
			<if test="registPersonId!=null and registPersonId!=''">
				and regist_person_id = #{registPersonId}
			</if>
			<if test="teamCodeId!=null and teamCodeId!=''">
				and team_code_id = #{teamCodeId}
			</if>
			<if test="createUserName!=null and createUserName!=''">
				and create_user_name = #{createUserName}
			</if>
			<if test="sourceRoomNum!=null and sourceRoomNum!=''">
				and source_room_num = #{sourceRoomNum}
			</if>
			<if test="sourceRegistId!=null and sourceRegistId!=''">
				and source_regist_id = #{sourceRegistId}
			</if>
			<if test="sourceRegistPersonName!=null and sourceRegistPersonName!=''">
				and source_regist_person_name = #{sourceRegistPersonName}
			</if>
			<if test="sourceRegistPersonId!=null and sourceRegistPersonId!=''">
				and source_regist_person_id = #{sourceRegistPersonId}
			</if>
			<if test="cancelType!=null and cancelType!=''">
				and cancel_type = #{cancelType}
			</if>
			<if test="reason!=null and reason!=''">
				and reason = #{reason}
			</if>
			<if test="remark!=null and remark!=''">
				and remark = #{remark}
			</if>
		</where>
	</select>

	<!-- 更新 -->
	<update id="update" parameterType="com.pms.czaccount.bean.account.AccountCancel">
		UPDATE account_cancel
		<set>
			<if test="accountId!=null and accountId!=''">
				account_id = #{accountId,jdbcType=VARCHAR},
			</if>
			<if test="price!=null and price!=''">
				price = #{price,jdbcType=INTEGER},
			</if>
			<if test="payType!=null and payType!=''">
				pay_type = #{payType,jdbcType=INTEGER},
			</if>
			<if test="hid!=null and hid!=''">
				hid = #{hid,jdbcType=INTEGER},
			</if>
			<if test="hotelGroupId!=null and hotelGroupId!=''">
				hotel_group_id = #{hotelGroupId,jdbcType=INTEGER},
			</if>
			<if test="payClassId!=null and payClassId!=''">
				pay_class_id = #{payClassId,jdbcType=INTEGER},
			</if>
			<if test="payClassName!=null and payClassName!=''">
				pay_class_name = #{payClassName,jdbcType=VARCHAR},
			</if>
			<if test="payCodeId!=null and payCodeId!=''">
				pay_code_id = #{payCodeId,jdbcType=INTEGER},
			</if>
			<if test="payCodeName!=null and payCodeName!=''">
				pay_code_name = #{payCodeName,jdbcType=VARCHAR},
			</if>
			<if test="roomInfoId!=null and roomInfoId!=''">
				room_info_id = #{roomInfoId,jdbcType=INTEGER},
			</if>
			<if test="roomNum!=null and roomNum!=''">
				room_num = #{roomNum,jdbcType=VARCHAR},
			</if>
			<if test="accountCode!=null and accountCode!=''">
				account_code = #{accountCode,jdbcType=VARCHAR},
			</if>
			<if test="accountCreateUserName!=null and accountCreateUserName!=''">
				account_create_user_name = #{accountCreateUserName,jdbcType=VARCHAR},
			</if>
			<if test="isSale!=null and isSale!=''">
				is_sale = #{isSale,jdbcType=INTEGER},
			</if>
			<if test="businessDay!=null and businessDay!=''">
				business_day = #{businessDay,jdbcType=INTEGER},
			</if>
			<if test="classId!=null and classId!=''">
				class_id = #{classId,jdbcType=INTEGER},
			</if>
			<if test="createTime!=null and createTime!=''">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="createUserId!=null and createUserId!=''">
				create_user_id = #{createUserId,jdbcType=VARCHAR},
			</if>
			<if test="bookingId!=null and bookingId!=''">
				booking_id = #{bookingId,jdbcType=INTEGER},
			</if>
			<if test="registId!=null and registId!=''">
				regist_id = #{registId,jdbcType=INTEGER},
			</if>
			<if test="registPersonName!=null and registPersonName!=''">
				regist_person_name = #{registPersonName,jdbcType=VARCHAR},
			</if>
			<if test="registPersonId!=null and registPersonId!=''">
				regist_person_id = #{registPersonId,jdbcType=INTEGER},
			</if>
			<if test="teamCodeId!=null and teamCodeId!=''">
				team_code_id = #{teamCodeId,jdbcType=INTEGER},
			</if>
			<if test="createUserName!=null and createUserName!=''">
				create_user_name = #{createUserName,jdbcType=VARCHAR},
			</if>
			<if test="sourceRoomNum!=null and sourceRoomNum!=''">
				source_room_num = #{sourceRoomNum,jdbcType=VARCHAR},
			</if>
			<if test="sourceRegistId!=null and sourceRegistId!=''">
				source_regist_id = #{sourceRegistId,jdbcType=INTEGER},
			</if>
			<if test="sourceRegistPersonName!=null and sourceRegistPersonName!=''">
				source_regist_person_name = #{sourceRegistPersonName,jdbcType=VARCHAR},
			</if>
			<if test="sourceRegistPersonId!=null and sourceRegistPersonId!=''">
				source_regist_person_id = #{sourceRegistPersonId,jdbcType=INTEGER},
			</if>
			<if test="cancelType!=null and cancelType!=''">
				cancel_type = #{cancelType,jdbcType=INTEGER},
			</if>
			<if test="reason!=null and reason!=''">
				reason = #{reason,jdbcType=VARCHAR},
			</if>
			<if test="remark!=null and remark!=''">
				remark = #{remark,jdbcType=VARCHAR}
			</if>
		</set>
		WHERE
		account_cancel_id = #{accountCancelId,jdbcType=INTEGER}
	</update>

</mapper>
