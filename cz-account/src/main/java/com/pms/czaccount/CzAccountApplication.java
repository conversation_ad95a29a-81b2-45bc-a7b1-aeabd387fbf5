package com.pms.czaccount;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication
@MapperScan({"com.pms.*.*","com.pms.*.*.*"})
@ComponentScan("com.pms.*")
@EnableDiscoveryClient
public class CzAccountApplication {

    public static void main(String[] args) {
        SpringApplication.run(CzAccountApplication.class, args);
    }
    @LoadBalanced
    @Bean
    protected RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
