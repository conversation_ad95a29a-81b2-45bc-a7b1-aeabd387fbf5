package com.pms.czaccount.util;

import com.pms.czpmsutils.GsonUtil;
import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
import com.wechat.pay.contrib.apache.httpclient.auth.AutoUpdateCertificatesVerifier;
import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Validator;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.PrivateKey;
import java.util.HashMap;
import java.util.UUID;


public class WeChatUtil {

    public static final String apiV3Key = "46daf1d75d8d56dba69310967b68c0bb";
    public static final String apiKey = "62f8894a07a4566ab23a0f95ea34ea35";
    public static final String APPID = "wxbc1a4eeef87ecfee";
    public static final String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCuVjGnrltdPzjWWTFv5JvFiVa5HEkSdRr7HCszvb1aJEp3kPGVvURjT+pBVgqXLS/KSA7mTeX5P0KIHcBfVhJh0ZXezu+vHQ0jlY3YB7oG97JdJbFWdklW0GXuRZvpM6etIaR6cjf9rZvm+2DUj/Rqfi9EWUk8e3RdVENx29XXuc3l5uSGsGjJ6cEgm4CG/GNTANI/lBkwHnMQMrKzjMZN8UqqoOQdY70atyexYE+iybaP8WvLb1hoO3ipHvLIkJBI8o1qkQ2f9NhNNcq8BFTLm/PCEJ6KrUaIZH1LlSqDpmfICPChgwJxjlXjj3ljRQzX+lRK0Irs/Y6WuDE3Kxa3AgMBAAECggEAF3x7MgjWPCixQoc/lsBoj+MhsMxzt63ZFa1E0LKmGMe81IDNWwvlc6+ncmfiA13OC2VLxJEIA10XXHkNmopg7ZarpxmfxqqWQ435TvozgI7ME6ppSqmi1bQ7ly7/gxo3Y6adnvVWP7GsyCBjeh9uoQ4g1gdlS2WiVpRshaFFHMutON3D+b+X8sfLoY6EpMNQukFDT6KKvJtdaSIoP7J4jQy5kT40Rfn0ICnvsm+s4Sl/vkXxwPVcAVQGxMb4OGNAoVrBfWUNy1Rx+cEAd4EZ156LrSIpD5q87uxRvQ4WNiqt0we38WAOiqSrUeWDR114/VXjS7kBRIxWO4vchZ8DyQKBgQDiG8B4WlSQ9jCl8ILcRnntCKaAjX4pcTMYFIOFg/LAXspA3rRpQkN1X0zo6Z2QswjBPnHdiTcm5r2vZwR0H8u/9avZ+VfAlRSgmGYrUIUTKcl2mtnujcD2E73Rd72fb372ccj2PQSO2OTddBIXV9Bu010EppkHDfGiVGcq11F/ZQKBgQDFYlFtYE1L051RFH0EiZRAQWwp8itwtnqQHf+RzdZiYqIMNEFzSG6X/OJ+Nhszlu4T6kE+b/EgdzJ0E1dxbVCKuAYakwuvjr1ipIQroHSEnpetf2wXXP2UdaiYPqp6DRUWw4vyW63rWxlHEapoXojRFZv87dPM5QCIuKw+CBDB6wKBgQCkBUewxq4z3jNaHMeA7SsDVNQbywhrHa/X1wG1eAs/m0OSnTs4ZBY1baWIC/f+ALHoJNlKwIAemyGcvNlRN8OBA8DfRW5vyHWHLiZnY2i1hwmbHSfcUj5ZA+cdrYQDxUjPk6kZXxgQW6xiLRH2lwAfdPo0Jno50NapUBrqy/lVtQKBgEE6vqIgwyqd4k1bMf1/4ehiNnaeO1DiXRqnb0AmAAU/h0H1OvP4KeQ8hDUleFPSOxeFwM37fHo4KgpGIg2wDH/rVMkPczsvNtMUf0FTo234YzPI+lW2O28GexLlEr2G+UMumF8dgRpYXsAnzGL1as1+ELDDf80kmMqWDQXq8G7FAoGARwU5VX2J4mJLv/8VZzHhXTOmQduGpbFe/9TKvHUPM3DyN/LlotM68/SdgNNPayiZ1eCTSvwON4GVpXed024JtwNH5gX4pQ76DauUDogTz51Sr7inVCnGxo2erJ033LcBK/ei6WalNxzrSNaR3DMKbMgtYKf3ATevJBLgukvPpt8=";
    public static final String mchId = "**********";
    public static final String mchSerialNo = "772650E0928BDBB7B68CF3074E94400321AEB657";

    public static CloseableHttpClient getHttpClent() throws UnsupportedEncodingException {
        // 加载商户私钥（privateKey：私钥字符串）
        PrivateKey merchantPrivateKey = PemUtil
                .loadPrivateKey(new ByteArrayInputStream(privateKey.getBytes("utf-8")));

        // 加载平台证书（mchId：商户号,mchSerialNo：商户证书序列号,apiV3Key：V3密钥）
        AutoUpdateCertificatesVerifier verifier = new AutoUpdateCertificatesVerifier(
                new WechatPay2Credentials(mchId, new PrivateKeySigner(mchSerialNo, merchantPrivateKey)), apiV3Key.getBytes("utf-8"));

        // 初始化httpClient
        CloseableHttpClient client = WechatPayHttpClientBuilder.create()
                .withMerchant(mchId, mchSerialNo, merchantPrivateKey)
                .withValidator(new WechatPay2Validator(verifier)).build();
        return client;
    }
    public static void main1(String[] args) throws IOException {
        HashMap<String, Object> data = new HashMap<>();
        data.put("appid", "wxbc1a4eeef87ecfee");
        data.put("mchid", mchId);
        data.put("description", "czpms");
        data.put("notify_url", "http://czpms.cn");
        //data.put("sub_mch_id", "1611335496");
        data.put("out_trade_no", UUID.randomUUID().toString().replaceAll("-", ""));
        HashMap<String, Object> amount = new HashMap<>();
        amount.put("total", 1);
        amount.put("currency", "CNY");
        data.put("amount", amount);
        String postData = GsonUtil.bean2Json(data);

        System.out.println(postData);

        CloseableHttpClient httpClent = getHttpClent();
        StringEntity s = new StringEntity(postData);
        s.setContentEncoding("UTF-8");
        s.setContentType("application/json");//发送json数据需要设置contentType

        HttpPost post = new HttpPost("https://api.mch.weixin.qq.com/v3/pay/transactions/native");
        post.setHeader("Accept", "application/json");
        post.setEntity(s);
        CloseableHttpResponse response = httpClent.execute(post);
        response.getEntity().writeTo(System.out);

    }


}
