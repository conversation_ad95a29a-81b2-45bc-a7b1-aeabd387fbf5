package com.pms.czaccount.dao.pay;



import com.pms.czaccount.bean.pay.AlipayAccount;
import com.pms.czaccount.bean.pay.search.AlipayAccountSearch;

import java.util.List;

/**
 * 支付宝账号信息
 * <AUTHOR>
 */
public interface AlipayAccountDao {

    public Integer saveAlipayAccount(AlipayAccount alipayAccount) ;

    public Integer editAlipayAccount(AlipayAccount alipayAccount);

    public Integer deleteAlipayAccount(Integer alipayAccountId) ;

    public AlipayAccount selectById(Integer alipayAccountId);

    public List<AlipayAccount> selectBySearch(AlipayAccountSearch alipayAccountSearch);

}
