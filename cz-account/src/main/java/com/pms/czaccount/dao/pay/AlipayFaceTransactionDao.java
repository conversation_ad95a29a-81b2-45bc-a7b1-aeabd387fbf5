package com.pms.czaccount.dao.pay;



import com.pms.czaccount.bean.pay.AlipayFaceTransaction;
import com.pms.czaccount.bean.pay.search.AlipayFaceTransactionSearch;

import java.util.List;

/**
 * 支付宝支付记录
 * <AUTHOR>
 */
public interface AlipayFaceTransactionDao {

    public Integer saveAlipayFaceTransaction(AlipayFaceTransaction alipayFaceTransaction) ;

    public Integer editAlipayFaceTransaction(AlipayFaceTransaction alipayFaceTransaction);

    public Integer deleteAlipayFaceTransaction(Integer alipayFaceTransactionId) ;

    public AlipayFaceTransaction selectById(Integer alipayFaceTransactionId);

    public List<AlipayFaceTransaction> selectBySearch(AlipayFaceTransactionSearch alipayFaceTransactionSearch);

    public List<AlipayFaceTransaction> selectNotCollection(AlipayFaceTransactionSearch alipayFaceTransactionSearch);

}
