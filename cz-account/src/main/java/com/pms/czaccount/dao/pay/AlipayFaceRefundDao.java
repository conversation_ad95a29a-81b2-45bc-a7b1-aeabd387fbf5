package com.pms.czaccount.dao.pay;



import com.pms.czaccount.bean.pay.AlipayFaceRefund;
import com.pms.czaccount.bean.pay.search.AlipayFaceRefundSearch;

import java.util.List;

public interface AlipayFaceRefundDao {

    public Integer saveAlipayFaceRefund(AlipayFaceRefund alipayFaceRefund) ;

    public Integer editAlipayFaceRefund(AlipayFaceRefund alipayFaceRefund);

    public Integer deleteAlipayFaceRefund(Integer alipayFaceRefundId) ;

    public AlipayFaceRefund selectById(Integer alipayFaceRefundId);

    public List<AlipayFaceRefund> selectBySearch(AlipayFaceRefundSearch alipayFaceRefundSearch);

}
