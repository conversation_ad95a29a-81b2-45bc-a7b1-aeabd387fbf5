package com.pms.czaccount.dao.pay;




import com.pms.czaccount.bean.pay.SysWechatRefund;
import com.pms.czaccount.bean.pay.search.SysWechatRefundSearch;

import java.util.List;

public interface SysWechatRefundDao {

    public Integer saveSysWechatRefund(SysWechatRefund sysWechatRefund) ;

    public Integer editSysWechatRefund(SysWechatRefund sysWechatRefund);

    public Integer deleteSysWechatRefund(Integer sysWechatRefundId) ;

    public SysWechatRefund selectById(Integer sysWechatRefundId);

    public List<SysWechatRefund> selectBySearch(SysWechatRefundSearch sysWechatRefundSearch);

}
