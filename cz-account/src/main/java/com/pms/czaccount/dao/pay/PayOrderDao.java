package com.pms.czaccount.dao.pay;


import com.pms.czaccount.bean.pay.PayOrder;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface PayOrderDao {

    @Insert("insert into pay_order " +
            "       (hid, main_id, status, pay_type, session_token, account, add_account_param, pay_param)  " +
            "values (#{hid}, #{mainId},#{status},#{payType},#{sessionToken},#{account},#{addAccountParam},#{payParam})")
    public Integer savePayOrder(PayOrder payOrder);


    @Select("select id,hid, main_id mainId, status, pay_type payType, session_token sessionToken, account, add_account_param addAccountParam, pay_param payParam,create_time createTime from pay_order where status = 1")
    public List<PayOrder> selectPayOrderList();

    /**
     * 修改payOrder状态
     *
     * @param payOrderId
     * @param status
     * @return
     */
    @Update("update pay_order set status = #{status} where id = #{id}")
    public int updatePayOrderStatus(@Param("id") int id, @Param("status") int status);

    /**
     * 开启payOrder行锁
     *
     * @param payOrderId
     */
    @Select("select * from pay_order where id = #{id}  for update ")
    public PayOrder lineLock(@Param("id") int id);
}
