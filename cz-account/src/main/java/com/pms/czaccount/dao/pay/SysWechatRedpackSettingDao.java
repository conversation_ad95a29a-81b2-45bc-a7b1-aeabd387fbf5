package com.pms.czaccount.dao.pay;




import com.pms.czaccount.bean.pay.SysWechatRedpackSetting;
import com.pms.czaccount.bean.pay.search.SysWechatRedpackSettingSearch;

import java.util.List;

public interface SysWechatRedpackSettingDao {

    public Integer saveSysWechatRedpackSetting(SysWechatRedpackSetting sysWechatRedpackSetting) ;

    public Integer editSysWechatRedpackSetting(SysWechatRedpackSetting sysWechatRedpackSetting);

    public Integer deleteSysWechatRedpackSetting(Integer sysWechatRedpackSettingId);

    public SysWechatRedpackSetting selectById(Integer sysWechatRedpackSettingId);

    public List<SysWechatRedpackSetting> selectBySearch(SysWechatRedpackSettingSearch sysWechatRedpackSettingSearch);

}
