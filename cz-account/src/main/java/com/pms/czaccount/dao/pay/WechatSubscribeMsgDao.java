package com.pms.czaccount.dao.pay;

import com.github.pagehelper.Page;
import com.pms.czaccount.bean.pay.WechatSubscribeMsg;
import com.pms.czaccount.bean.pay.search.WechatSubscribeMsgSearch;

public interface WechatSubscribeMsgDao{

	public Integer insert(WechatSubscribeMsg wechatSubscribeMsg);

	public Integer update(WechatSubscribeMsg wechatSubscribeMsg);

	public Integer delete(Integer id);

	public WechatSubscribeMsg selectById(Integer id);

	public Page<WechatSubscribeMsg> selectBySearch( WechatSubscribeMsgSearch wechatSubscribeMsgSearch);

}
