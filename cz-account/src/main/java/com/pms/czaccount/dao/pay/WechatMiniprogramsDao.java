package com.pms.czaccount.dao.pay;

import com.pms.czaccount.bean.pay.WechatMiniprograms;
import com.pms.czaccount.bean.pay.search.WechatMiniprogramsSearch;

import java.util.List;

public interface WechatMiniprogramsDao {

    public Integer insert(WechatMiniprograms wechatMiniprograms);

    public Integer update(WechatMiniprograms wechatMiniprograms);

    public Integer delete(Long wechatMiniprogramsId);

    public WechatMiniprograms selectById(Long wechatMiniprogramsId);

    public List<WechatMiniprograms> selectBySearch(WechatMiniprogramsSearch wechatMiniprogramsSearch);

}
