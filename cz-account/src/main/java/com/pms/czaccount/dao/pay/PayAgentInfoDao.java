package com.pms.czaccount.dao.pay;

import com.github.pagehelper.Page;
import com.pms.czaccount.bean.PayAgentInfo;
import com.pms.czaccount.bean.search.PayAgentInfoSearch;

public interface PayAgentInfoDao{

	public Integer insert(PayAgentInfo payAgentInfo);

	public Integer update(PayAgentInfo payAgentInfo);

	public Integer delete(Integer id);

	public PayAgentInfo selectById(Integer id);

	public Page<PayAgentInfo> selectBySearch( PayAgentInfoSearch payAgentInfoSearch);

}
