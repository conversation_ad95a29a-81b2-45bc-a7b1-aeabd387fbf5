package com.pms.czaccount.dao.pay;

import com.github.pagehelper.Page;
import com.pms.czaccount.bean.pay.AccountReset;
import com.pms.czaccount.bean.pay.search.AccountResetSearch;

public interface AccountResetDao{

	public Integer insert(AccountReset accountReset);

	public Integer update(AccountReset accountReset);

	public Integer delete(Integer id);

	public AccountReset selectById(Integer id);

	public Page<AccountReset> selectBySearch( AccountResetSearch accountResetSearch);

}
