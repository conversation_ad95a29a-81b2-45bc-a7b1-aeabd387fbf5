package com.pms.czaccount.dao.account;


import com.github.pagehelper.Page;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountBalance;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czpmsutils.request.AccountSearchRequest;
import com.pms.czpmsutils.request.AccountSummary;
import com.pms.czpmsutils.request.search.AccountSummarySearch;
import net.sf.json.JSONObject;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface AccountDao {
    public Integer saveAccount(final Account account) ;

    public void saveAccountList(List<Account> accounts) ;

    public Integer editAccount(Account account);

    public Integer sumPrice(AccountSearch accountSearch);

    public Integer deleteAccount(String accountId) ;

    public Account selectById(String accountId);

    public List<Account> selectBySearch(AccountSearch accountSearch);

    public Integer selectBySearchCount(AccountSearch accountSearch);

    public Integer editAccountList(List<Account> accounts);

    /**
     * 桥台入账明细
     * @param jsonObject
     * @return
     */
    public List<Map<String,Object>> accountDetails(JSONObject jsonObject);


    /**
     * 桥台入账明细 数据汇总
     * @param jsonObject
     * @return
     */
    public Map<String,Object> accountDetailsCount(JSONObject jsonObject);

    /**
     * 查询账务汇总
     * @param accountSummarySearch
     * @return
     */
    public List<AccountSummary> accountSummary(AccountSummarySearch accountSummarySearch);


    /**
     * 新的帐务信息查询
     * @param accountSearchRequest
     * @return
     */
    public Page<Account> searchAccountList(AccountSearchRequest accountSearchRequest);


    public AccountBalance accountBalance(AccountSearch accountSearch);

    @Insert("insert into account_info(id) values(#{id})")
    public Integer insertAccountInfo(@Param("id") String id);

}
