package com.pms.czaccount.dao.pay;

import com.github.pagehelper.Page;
import com.pms.czaccount.bean.SqbRefund;
import com.pms.czaccount.bean.search.SqbRefundSearch;

public interface SqbRefundDao{

	public Integer insert(SqbRefund sqbRefund);

	public Integer update(SqbRefund sqbRefund);

	public Integer delete(Integer id);

	public SqbRefund selectById(Integer id);

	public Page<SqbRefund> selectBySearch( SqbRefundSearch sqbRefundSearch);

}
