package com.pms.czaccount.dao.account;


import com.github.pagehelper.Page;
import com.pms.czaccount.bean.account.AccountCancel;
import com.pms.czaccount.bean.account.search.AccountCancelSearch;

import java.util.List;

public interface AccountCancelDao{

	public Integer insert(AccountCancel accountCancel);

	public void insertList(List<AccountCancel> accountCancels);

	public Integer update(AccountCancel accountCancel);

	public Integer delete(Integer id);

	public AccountCancel selectById(Integer id);

	public Page<AccountCancel> selectBySearch( AccountCancelSearch accountCancelSearch);
}
