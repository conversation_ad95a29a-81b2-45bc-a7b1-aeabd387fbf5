package com.pms.czaccount.dao.pay;



import com.pms.czaccount.bean.pay.WechatAccounts;
import com.pms.czaccount.bean.pay.search.WechatAccountsSearch;

import java.util.List;

/**
 * 查询酒店微信配置基础信息
 */
public interface WechatAccountsDao {

    public Integer saveWechatAccounts(WechatAccounts wechatAccounts) ;

    public Integer editWechatAccounts(WechatAccounts wechatAccounts) ;

    public Integer deleteWechatAccounts(Integer wechatAccountsId);

    public WechatAccounts selectById(Integer wechatAccountsId) ;

    public WechatAccounts selectByHid(Integer hid) ;

    public List<WechatAccounts> selectBySearch(WechatAccountsSearch wechatAccountsSearch);

}
