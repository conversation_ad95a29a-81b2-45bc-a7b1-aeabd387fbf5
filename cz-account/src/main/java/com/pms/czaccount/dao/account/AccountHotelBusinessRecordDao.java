package com.pms.czaccount.dao.account;

import com.github.pagehelper.Page;
import com.pms.czaccount.bean.account.AccountHotelBusinessRecord;
import com.pms.czaccount.bean.account.search.AccountHotelBusinessRecordSearch;

public interface AccountHotelBusinessRecordDao{

	public Integer insert(AccountHotelBusinessRecord accountHotelBusinessRecord);

	public Integer update(AccountHotelBusinessRecord accountHotelBusinessRecord);

	public Integer delete(Integer id);

	public AccountHotelBusinessRecord selectById(Integer id);

	public Page<AccountHotelBusinessRecord> selectBySearch( AccountHotelBusinessRecordSearch accountHotelBusinessRecordSearch);

}
