package com.pms.czaccount.dao.account;




import com.pms.czaccount.bean.account.AccountThirdPayRecode;
import com.pms.czaccount.bean.account.AccountThirdSummary;
import com.pms.czaccount.bean.account.search.AccountThirdPayRecodeSearch;
import com.pms.czaccount.bean.account.search.AccountThirdSummarySearch;

import java.util.List;

public interface AccountThirdPayRecodeDao {
    public Integer insert(AccountThirdPayRecode accountThirdPayRecode);

    public Integer update(AccountThirdPayRecode accountThirdPayRecode);

    public Integer delete(String accountThirdPayRecodeId);

    public AccountThirdPayRecode selectById(String accountThirdPayRecodeId);

    public List<AccountThirdPayRecode> selectBySearch(AccountThirdPayRecodeSearch accountThirdPayRecodeSearch);

    public List<AccountThirdSummary> accountThirdSummary(AccountThirdSummarySearch accountThirdSummarySearch);

}
