package com.pms.czaccount.dao.pay;

import com.github.pagehelper.Page;
import com.pms.czaccount.bean.SqbTransaction;
import com.pms.czaccount.bean.search.SqbTransactionSearch;

import java.util.List;

public interface SqbTransactionDao{

	public Integer insert(SqbTransaction sqbTransaction);

	public Integer update(SqbTransaction sqbTransaction);

	public Integer delete(Integer id);

	public SqbTransaction selectById(Integer id);

	public Page<SqbTransaction> selectBySearch( SqbTransactionSearch sqbTransactionSearch);


	public List<SqbTransaction> selectNotCollection(SqbTransactionSearch sqbTransactionSearch);
}
