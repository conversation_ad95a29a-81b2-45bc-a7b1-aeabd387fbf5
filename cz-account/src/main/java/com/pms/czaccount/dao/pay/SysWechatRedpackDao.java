package com.pms.czaccount.dao.pay;

import com.pms.czaccount.bean.pay.SysWechatRedpack;
import com.pms.czaccount.bean.pay.search.SysWechatRedpackSearch;
import net.sf.json.JSONObject;

import java.util.HashMap;
import java.util.List;

public interface SysWechatRedpackDao {

    public Integer saveSysWechatRedpack(SysWechatRedpack sysWechatRedpack) ;

    public Integer editSysWechatRedpack(SysWechatRedpack sysWechatRedpack);

    public Integer deleteSysWechatRedpack(Integer sysWechatRedpackId);

    public SysWechatRedpack selectById(Integer sysWechatRedpackId);

    public List<SysWechatRedpack> selectBySearch(SysWechatRedpackSearch sysWechatRedpackSearch);

    public List<HashMap<String,Integer>> redpackDayCount(JSONObject param);

}
