package com.pms.czaccount.bean.pay.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class AccountResetSearch extends PageBaseRequest{
	private Integer id;
	private String mainId;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer type;
	private Integer money;
	private java.util.Date createTime;
	private String createUserName;
	private String createUserId;

	public void setId(Integer value) {
		this.id = value;
	}

	public Integer getId() {
		return this.id;
	}
	public void setMainId(String value) {
		this.mainId = value;
	}

	public String getMainId() {
		return this.mainId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setType(Integer value) {
		this.type = value;
	}

	public Integer getType() {
		return this.type;
	}
	public void setMoney(Integer value) {
		this.money = value;
	}

	public Integer getMoney() {
		return this.money;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}

	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}

	public String getCreateUserName() {
		return this.createUserName;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}

	public String getCreateUserId() {
		return this.createUserId;
	}

}

