package com.pms.czaccount.bean.pay.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("SysWechatRedpackSettingSearch")
public class SysWechatRedpackSettingSearch extends BaseSearch {
	private Integer id;
	private Integer money;
	private Integer totalAmount;
	private Double minThreshold;
	private Double maxThreshold;
	private String startTime;
	private String endTime;
	private String searchTime ;
	private java.util.Date createTime;
	private String createUser;
	private String createUserId;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setMoney(Integer value) {
		this.money = value;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setTotalAmount(Integer value) {
		this.totalAmount = value;
	}
	
	public Integer getTotalAmount() {
		return this.totalAmount;
	}
	public void setMinThreshold(Double value) {
		this.minThreshold = value;
	}
	
	public Double getMinThreshold() {
		return this.minThreshold;
	}
	public void setMaxThreshold(Double value) {
		this.maxThreshold = value;
	}
	
	public Double getMaxThreshold() {
		return this.maxThreshold;
	}
	public void setStartTime(String value) {
		this.startTime = value;
	}
	
	public String getStartTime() {
		return this.startTime;
	}
	public void setEndTime(String value) {
		this.endTime = value;
	}
	
	public String getEndTime() {
		return this.endTime;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUser(String value) {
		this.createUser = value;
	}
	
	public String getCreateUser() {
		return this.createUser;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}

	public String getSearchTime() {
		return searchTime;
	}

	public void setSearchTime(String searchTime) {
		this.searchTime = searchTime;
	}
}

