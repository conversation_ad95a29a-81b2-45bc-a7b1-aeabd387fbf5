package com.pms.czaccount.bean.pay.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("AlipayFaceRefundSearch")
public class AlipayFaceRefundSearch extends BaseSearch {
	private Integer id;
	private Integer hid;
	private String mainId;
	private String payMainId;
	private Integer refundMoney;
	private java.util.Date refundTime;
	private Integer status;
	private String buyerLogonId;
	private String source;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setMainId(String value) {
		this.mainId = value;
	}
	
	public String getMainId() {
		return this.mainId;
	}
	public void setPayMainId(String value) {
		this.payMainId = value;
	}
	
	public String getPayMainId() {
		return this.payMainId;
	}
	public void setRefundMoney(Integer value) {
		this.refundMoney = value;
	}
	
	public Integer getRefundMoney() {
		return this.refundMoney;
	}

	public void setRefundTime(java.util.Date value) {
		this.refundTime = value;
	}
	
	public java.util.Date getRefundTime() {
		return this.refundTime;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}
	
	public Integer getStatus() {
		return this.status;
	}
	public void setBuyerLogonId(String value) {
		this.buyerLogonId = value;
	}
	
	public String getBuyerLogonId() {
		return this.buyerLogonId;
	}
	public void setSource(String value) {
		this.source = value;
	}
	
	public String getSource() {
		return this.source;
	}

}

