package com.pms.czaccount.bean.pay;

import java.io.Serializable;

/**
 * 酒店配置的微信账户信息
 */
public class WechatAccounts implements Serializable {
	private Integer id  ;
	private String hid  ;
	private String hotelOpenId  ;
	private String hotelWechatAccount  ;
	private String hotelWechatPassword  ;
	private String appId  ;
	private String appSecret  ;
	private String accessToken  ;
	private String isSercret  ;
	private String encryptionKey  ;
	private String getTime  ;
	private String getMillisecond  ;
	private String validateMillisecond  ;
	private String validateTime  ;
	private String updateTime  ;
	private String updatePerson  ;
	private String updatePersonId  ;
	private String jsApiTicket  ;
	private String jsGetTime  ;
	private String jsGetMillisecond  ;
	private String jsValidateMillisecond  ;
	private String isPay  ;
	private String mchId  ;
	private String apiKey  ;
	private String filePath  ;
	private String filePath1  ;
	private String notifyUrl  ;

	public WechatAccounts(){
	}

	public WechatAccounts(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(String hid) {
		this.hid = hid;
	}
	
	public String getHid() {
		return this.hid;
	}
	public void setHotelOpenId(String hotelOpenId) {
		this.hotelOpenId = hotelOpenId;
	}
	
	public String getHotelOpenId() {
		return this.hotelOpenId;
	}
	public void setHotelWechatAccount(String hotelWechatAccount) {
		this.hotelWechatAccount = hotelWechatAccount;
	}
	
	public String getHotelWechatAccount() {
		return this.hotelWechatAccount;
	}
	public void setHotelWechatPassword(String hotelWechatPassword) {
		this.hotelWechatPassword = hotelWechatPassword;
	}
	
	public String getHotelWechatPassword() {
		return this.hotelWechatPassword;
	}
	public void setAppId(String appId) {
		this.appId = appId;
	}
	
	public String getAppId() {
		return this.appId;
	}
	public void setAppSecret(String appSecret) {
		this.appSecret = appSecret;
	}
	
	public String getAppSecret() {
		return this.appSecret;
	}
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}
	
	public String getAccessToken() {
		return this.accessToken;
	}
	public void setIsSercret(String isSercret) {
		this.isSercret = isSercret;
	}
	
	public String getIsSercret() {
		return this.isSercret;
	}
	public void setEncryptionKey(String encryptionKey) {
		this.encryptionKey = encryptionKey;
	}
	
	public String getEncryptionKey() {
		return this.encryptionKey;
	}
	public void setGetTime(String getTime) {
		this.getTime = getTime;
	}
	
	public String getGetTime() {
		return this.getTime;
	}
	public void setGetMillisecond(String getMillisecond) {
		this.getMillisecond = getMillisecond;
	}
	
	public String getGetMillisecond() {
		return this.getMillisecond;
	}
	public void setValidateMillisecond(String validateMillisecond) {
		this.validateMillisecond = validateMillisecond;
	}
	
	public String getValidateMillisecond() {
		return this.validateMillisecond;
	}
	public void setValidateTime(String validateTime) {
		this.validateTime = validateTime;
	}
	
	public String getValidateTime() {
		return this.validateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	
	public String getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdatePerson(String updatePerson) {
		this.updatePerson = updatePerson;
	}
	
	public String getUpdatePerson() {
		return this.updatePerson;
	}
	public void setUpdatePersonId(String updatePersonId) {
		this.updatePersonId = updatePersonId;
	}
	
	public String getUpdatePersonId() {
		return this.updatePersonId;
	}
	public void setJsApiTicket(String jsApiTicket) {
		this.jsApiTicket = jsApiTicket;
	}
	
	public String getJsApiTicket() {
		return this.jsApiTicket;
	}
	public void setJsGetTime(String jsGetTime) {
		this.jsGetTime = jsGetTime;
	}
	
	public String getJsGetTime() {
		return this.jsGetTime;
	}
	public void setJsGetMillisecond(String jsGetMillisecond) {
		this.jsGetMillisecond = jsGetMillisecond;
	}
	
	public String getJsGetMillisecond() {
		return this.jsGetMillisecond;
	}
	public void setJsValidateMillisecond(String jsValidateMillisecond) {
		this.jsValidateMillisecond = jsValidateMillisecond;
	}
	
	public String getJsValidateMillisecond() {
		return this.jsValidateMillisecond;
	}
	public void setIsPay(String isPay) {
		this.isPay = isPay;
	}
	
	public String getIsPay() {
		return this.isPay;
	}
	public void setMchId(String mchId) {
		this.mchId = mchId;
	}
	
	public String getMchId() {
		return this.mchId;
	}
	public void setApiKey(String apiKey) {
		this.apiKey = apiKey;
	}
	
	public String getApiKey() {
		return this.apiKey;
	}
	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}
	
	public String getFilePath() {
		return this.filePath;
	}
	public void setFilePath1(String filePath1) {
		this.filePath1 = filePath1;
	}
	
	public String getFilePath1() {
		return this.filePath1;
	}

	public String getNotifyUrl() {
		return notifyUrl;
	}

	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}
}

