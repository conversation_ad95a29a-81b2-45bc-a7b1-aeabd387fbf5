package com.pms.czaccount.bean.account.search;


import com.pms.czpmsutils.request.PageBaseRequest;
import org.apache.ibatis.type.Alias;

import java.util.Date;

@Alias("AccountCancelSearch")
public class AccountCancelSearch extends PageBaseRequest {
	private Integer accountCancelId;
	private String accountId;
	private Integer price;
	private Integer payType;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer payClassId;
	private String payClassName;
	private Integer payCodeId;
	private String payCodeName;
	private Integer roomInfoId;
	private String roomNum;
	private String accountCode;
	private String accountCreateUserName;
	private Integer isSale;
	private Integer businessDay;
	private Integer classId;
	private java.util.Date createTime;
	private String createUserId;
	private Integer bookingId;
	private Integer registId;
	private String registPersonName;
	private Integer registPersonId;
	private Integer teamCodeId;
	private String createUserName;
	private String sourceRoomNum;
	private Integer sourceRegistId;
	private String sourceRegistPersonName;
	private Integer sourceRegistPersonId;
	private Integer cancelType;
	private String reason;
	private String remark;

	public Integer getAccountCancelId() {
		return accountCancelId;
	}

	public void setAccountCancelId(Integer accountCancelId) {
		this.accountCancelId = accountCancelId;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public Integer getPrice() {
		return price;
	}

	public void setPrice(Integer price) {
		this.price = price;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Integer getHid() {
		return hid;
	}

	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHotelGroupId() {
		return hotelGroupId;
	}

	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getPayClassId() {
		return payClassId;
	}

	public void setPayClassId(Integer payClassId) {
		this.payClassId = payClassId;
	}

	public String getPayClassName() {
		return payClassName;
	}

	public void setPayClassName(String payClassName) {
		this.payClassName = payClassName;
	}

	public Integer getPayCodeId() {
		return payCodeId;
	}

	public void setPayCodeId(Integer payCodeId) {
		this.payCodeId = payCodeId;
	}

	public String getPayCodeName() {
		return payCodeName;
	}

	public void setPayCodeName(String payCodeName) {
		this.payCodeName = payCodeName;
	}

	public Integer getRoomInfoId() {
		return roomInfoId;
	}

	public void setRoomInfoId(Integer roomInfoId) {
		this.roomInfoId = roomInfoId;
	}

	public String getRoomNum() {
		return roomNum;
	}

	public void setRoomNum(String roomNum) {
		this.roomNum = roomNum;
	}

	public String getAccountCode() {
		return accountCode;
	}

	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}

	public String getAccountCreateUserName() {
		return accountCreateUserName;
	}

	public void setAccountCreateUserName(String accountCreateUserName) {
		this.accountCreateUserName = accountCreateUserName;
	}

	public Integer getIsSale() {
		return isSale;
	}

	public void setIsSale(Integer isSale) {
		this.isSale = isSale;
	}

	public Integer getBusinessDay() {
		return businessDay;
	}

	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}

	public Integer getClassId() {
		return classId;
	}

	public void setClassId(Integer classId) {
		this.classId = classId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	public Integer getBookingId() {
		return bookingId;
	}

	public void setBookingId(Integer bookingId) {
		this.bookingId = bookingId;
	}

	public Integer getRegistId() {
		return registId;
	}

	public void setRegistId(Integer registId) {
		this.registId = registId;
	}

	public String getRegistPersonName() {
		return registPersonName;
	}

	public void setRegistPersonName(String registPersonName) {
		this.registPersonName = registPersonName;
	}

	public Integer getRegistPersonId() {
		return registPersonId;
	}

	public void setRegistPersonId(Integer registPersonId) {
		this.registPersonId = registPersonId;
	}

	public Integer getTeamCodeId() {
		return teamCodeId;
	}

	public void setTeamCodeId(Integer teamCodeId) {
		this.teamCodeId = teamCodeId;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

	public String getSourceRoomNum() {
		return sourceRoomNum;
	}

	public void setSourceRoomNum(String sourceRoomNum) {
		this.sourceRoomNum = sourceRoomNum;
	}

	public Integer getSourceRegistId() {
		return sourceRegistId;
	}

	public void setSourceRegistId(Integer sourceRegistId) {
		this.sourceRegistId = sourceRegistId;
	}

	public String getSourceRegistPersonName() {
		return sourceRegistPersonName;
	}

	public void setSourceRegistPersonName(String sourceRegistPersonName) {
		this.sourceRegistPersonName = sourceRegistPersonName;
	}

	public Integer getSourceRegistPersonId() {
		return sourceRegistPersonId;
	}

	public void setSourceRegistPersonId(Integer sourceRegistPersonId) {
		this.sourceRegistPersonId = sourceRegistPersonId;
	}

	public Integer getCancelType() {
		return cancelType;
	}

	public void setCancelType(Integer cancelType) {
		this.cancelType = cancelType;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}

