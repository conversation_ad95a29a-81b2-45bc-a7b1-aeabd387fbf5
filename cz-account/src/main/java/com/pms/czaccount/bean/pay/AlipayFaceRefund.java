package com.pms.czaccount.bean.pay;

import java.io.Serializable;
import java.util.Date;

public class AlipayFaceRefund implements Serializable {
	private Integer id  ;
	private Integer hid  ;
	private String mainId  ;
	private String payMainId  ;
	private Integer refundMoney  ;
	private Date refundTime  ;
	private Integer status  ;
	private String buyerLogonId  ;
	private String source  ;

	public AlipayFaceRefund(){
	}

	public AlipayFaceRefund(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setMainId(String mainId) {
		this.mainId = mainId;
	}
	
	public String getMainId() {
		return this.mainId;
	}
	public void setPayMainId(String payMainId) {
		this.payMainId = payMainId;
	}
	
	public String getPayMainId() {
		return this.payMainId;
	}
	public void setRefundMoney(Integer refundMoney) {
		this.refundMoney = refundMoney;
	}
	
	public Integer getRefundMoney() {
		return this.refundMoney;
	}

	public void setRefundTime(Date refundTime) {
		this.refundTime = refundTime;
	}
	
	public Date getRefundTime() {
		return this.refundTime;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	public Integer getStatus() {
		return this.status;
	}
	public void setBuyerLogonId(String buyerLogonId) {
		this.buyerLogonId = buyerLogonId;
	}
	
	public String getBuyerLogonId() {
		return this.buyerLogonId;
	}
	public void setSource(String source) {
		this.source = source;
	}
	
	public String getSource() {
		return this.source;
	}

}

