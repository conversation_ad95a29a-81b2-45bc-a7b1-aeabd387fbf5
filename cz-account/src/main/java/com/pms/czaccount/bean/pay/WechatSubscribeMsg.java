package com.pms.czaccount.bean.pay;

import java.io.Serializable;

public class WechatSubscribeMsg implements Serializable{
	//
	private Integer id;
	//
	private Integer hotelGroupId;
	//
	private Integer hid;
	//消息类型 1.小程序商场  2.预订单信息 3.充值信息 4.消费信息
	private Integer msgType;
	//消息模板id
	private String msgId;
	//消息模板名称
	private String msgName;

	public WechatSubscribeMsg(){
	}

	public WechatSubscribeMsg(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setMsgType(Integer msgType) {
		this.msgType = msgType;
	}
	
	public Integer getMsgType() {
		return this.msgType;
	}
	public void setMsgId(String msgId) {
		this.msgId = msgId;
	}
	
	public String getMsgId() {
		return this.msgId;
	}
	public void setMsgName(String msgName) {
		this.msgName = msgName;
	}
	
	public String getMsgName() {
		return this.msgName;
	}

}

