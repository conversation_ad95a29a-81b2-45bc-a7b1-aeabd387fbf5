package com.pms.czaccount.bean.pay;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.util.Hashtable;
import java.util.Map;


public class CreatCode {
    private static final int BLACK = 0x02000000;
    private static final int WHITE = 0x02FFFFFF;
    private static final int RED = 0xFF00AAEF;

    private CreatCode() {
    }

    public static BufferedImage toBufferedImage(BitMatrix matrix, String logo) {
        int width = matrix.getWidth();
        int height = matrix.getHeight();
        BufferedImage image = new BufferedImage(width, height,
                BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, matrix.get(x, y) ? RED : WHITE);
            }
        }
        if (logo.length() > 0) {
            try {
                // 插入LOGO
                Graphics2D graph = image.createGraphics();
                int x = (width - 50) / 2;
                int y = (height - 50) / 2;
                Image src;
                src = ImageIO.read(new URL(logo));
                graph.drawImage(src, x, y, 50, 50, null);
                Shape shape = new RoundRectangle2D.Float(-100, 0, 50, 50, 6, 6);
                graph.setStroke(new BasicStroke(3f));
                graph.draw(shape);
                graph.dispose();
            } catch (IOException e) {
                System.out.println(e.getMessage());
            }
        }
        return image;
    }

    public static void writeToFile(BitMatrix matrix, String format, String logo, File file)
            throws IOException {
        BufferedImage image = toBufferedImage(matrix, logo);
        if (!ImageIO.write(image, format, file)) {
            throw new IOException("Could not write an image of format "
                    + format + " to " + file);
        }
    }

    public static void writeToStream(BitMatrix matrix, String format, String logo,
                                     OutputStream stream) throws IOException {
        BufferedImage image = toBufferedImage(matrix, logo);
        if (!ImageIO.write(image, format, stream)) {
            throw new IOException("Could not write an image of format "
                    + format);
        }
    }
    public static void createCodeFile(String content, int width, int height, String format, String logo, String filePath) throws Exception {
        Hashtable hints = new Hashtable();
        //内容所使用编码
        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
        BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);
        //生成二维码
        File outputFile = new File(filePath);
        writeToFile(bitMatrix, format, logo, outputFile);
    }

    public static void createCodeStream(String content, int width, int height, String format, String logo, HttpServletResponse response) throws Exception {
        Hashtable hints = new Hashtable();
        //内容所使用编码
        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
        BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);
        //生成二维码
        writeToStream(bitMatrix, format, logo, response.getOutputStream());
    }

    public static void createCodeStream1(Map<String, String> map, HttpServletResponse response) throws Exception {
        Hashtable hints = new Hashtable();
        if (!map.containsKey("size")) {
            map.put("size", "500");
        }
        //内容所使用编码
        hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
        BitMatrix bitMatrix = new MultiFormatWriter().encode(map.get("content").toString(), BarcodeFormat.QR_CODE, Integer.parseInt(map.get("size").toString()), Integer.parseInt(map.get("size").toString()), hints);
        //生成二维码
        int color = BLACK;
        int bg = WHITE;
        if (map.containsKey("color")) {
            Long longStr = Long.parseLong(map.get("color").substring(2), 16);
            color = longStr.intValue();
        }
        if (map.containsKey("bg")) {
            Long longStr = Long.parseLong(map.get("bg").substring(2), 16);
            bg = longStr.intValue();
        }
        int width = bitMatrix.getWidth();
        int height = bitMatrix.getHeight();
        BufferedImage image = new BufferedImage(width, height,
                BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? color : bg);
            }
        }
        if (map.containsKey("logo")) {
            String logo = map.get("logo");
            if (logo.length() > 0) {
                try {
                    // 插入LOGO
                    Graphics2D graph = image.createGraphics();
                    int x = (width - 50) / 2;
                    int y = (height - 50) / 2;
                    Image src;
                    src = ImageIO.read(new URL(logo));
                    graph.drawImage(src, x, y, 50, 50, null);
                    Shape shape = new RoundRectangle2D.Float(-100, 0, 50, 50, 6, 6);
                    graph.setStroke(new BasicStroke(3f));
                    graph.draw(shape);
                    graph.dispose();
                } catch (IOException e) {
                    System.out.println(e.getMessage());
                }
            }
        }
        if (!ImageIO.write(image, "png", response.getOutputStream())) {
            throw new IOException("Could not write an image of format "
                    + "png");
        }
    }

}
