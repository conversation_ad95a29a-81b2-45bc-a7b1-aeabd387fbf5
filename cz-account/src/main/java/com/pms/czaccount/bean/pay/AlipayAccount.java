package com.pms.czaccount.bean.pay;

import java.io.Serializable;
import java.util.Date;

public class AlipayAccount implements Serializable {
	private Integer id  ;
	private Integer hid  ;
	private String privateKey  ;
	private String url  ;
	private String appId  ;
	private String charSet  ;
	private String format  ;
	private String signType  ;
	private String alipayPublicKey  ;
	private Date updateTime  ;
	private String updateUserId  ;

	public AlipayAccount(){
	}

	public AlipayAccount(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setPrivateKey(String privateKey) {
		this.privateKey = privateKey;
	}
	
	public String getPrivateKey() {
		return this.privateKey;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	
	public String getUrl() {
		return this.url;
	}
	public void setAppId(String appId) {
		this.appId = appId;
	}
	
	public String getAppId() {
		return this.appId;
	}
	public void setCharSet(String charSet) {
		this.charSet = charSet;
	}
	
	public String getCharSet() {
		return this.charSet;
	}
	public void setFormat(String format) {
		this.format = format;
	}
	
	public String getFormat() {
		return this.format;
	}
	public void setAlipayPublicKey(String alipayPublicKey) {
		this.alipayPublicKey = alipayPublicKey;
	}
	
	public String getAlipayPublicKey() {
		return this.alipayPublicKey;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String updateUserId) {
		this.updateUserId = updateUserId;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}

	public String getSignType() {
		return signType;
	}

	public void setSignType(String signType) {
		this.signType = signType;
	}


}

