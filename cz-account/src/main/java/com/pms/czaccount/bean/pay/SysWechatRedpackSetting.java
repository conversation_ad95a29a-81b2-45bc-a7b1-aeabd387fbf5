package com.pms.czaccount.bean.pay;

import java.io.Serializable;
import java.util.Date;

public class SysWechatRedpackSetting implements Serializable {
	private Integer id  ;
	private Integer money  ;
	private Integer totalAmount  ;
	private Double minThreshold  ;
	private Double maxThreshold  ;
	private String startTime  ;
	private String endTime  ;
	private Date createTime  ;
	private String createUser  ;
	private String createUserId  ;

	public SysWechatRedpackSetting(){
	}

	public SysWechatRedpackSetting(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setMoney(Integer money) {
		this.money = money;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setTotalAmount(Integer totalAmount) {
		this.totalAmount = totalAmount;
	}
	
	public Integer getTotalAmount() {
		return this.totalAmount;
	}
	public void setMinThreshold(Double minThreshold) {
		this.minThreshold = minThreshold;
	}
	
	public Double getMinThreshold() {
		return this.minThreshold;
	}
	public void setMaxThreshold(Double maxThreshold) {
		this.maxThreshold = maxThreshold;
	}
	
	public Double getMaxThreshold() {
		return this.maxThreshold;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	
	public String getStartTime() {
		return this.startTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	
	public String getEndTime() {
		return this.endTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}
	
	public String getCreateUser() {
		return this.createUser;
	}
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}

}

