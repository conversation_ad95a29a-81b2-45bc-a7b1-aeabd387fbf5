package com.pms.czaccount.bean.pay;

import java.io.Serializable;

public class SysWechatRedpack implements Serializable {
	private Integer id  ;
	private String sn  ;
	private String otherSn  ;
	private Integer otherId  ;
	private Integer redpackSettingId  ;
	private Integer hid  ;
	private Integer sendHid  ;
	private String actName  ;
	private String wishing  ;
	private String sceneId  ;
	private Integer money  ;
	private Integer status  ;
	private Integer type  ;
	private String receiverOpenid  ;
	private java.util.Date rcvTime  ;
	private Double threshold  ;
	private Integer totalNum  ;
	private java.util.Date refundTime  ;
	private Integer refundMoney  ;
	private Integer businessDay  ;
	private java.util.Date createTime  ;
	private String createUser  ;
	private String createUserId  ;

	public SysWechatRedpack(){
	}

	public SysWechatRedpack(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return this.id;
	}
	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getSn() {
		return this.sn;
	}
	public void setOtherSn(String otherSn) {
		this.otherSn = otherSn;
	}

	public String getOtherSn() {
		return this.otherSn;
	}
	public void setOtherId(Integer otherId) {
		this.otherId = otherId;
	}

	public Integer getOtherId() {
		return this.otherId;
	}
	public void setRedpackSettingId(Integer redpackSettingId) {
		this.redpackSettingId = redpackSettingId;
	}

	public Integer getRedpackSettingId() {
		return this.redpackSettingId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setSendHid(Integer sendHid) {
		this.sendHid = sendHid;
	}

	public Integer getSendHid() {
		return this.sendHid;
	}
	public void setActName(String actName) {
		this.actName = actName;
	}

	public String getActName() {
		return this.actName;
	}
	public void setWishing(String wishing) {
		this.wishing = wishing;
	}

	public String getWishing() {
		return this.wishing;
	}
	public void setSceneId(String sceneId) {
		this.sceneId = sceneId;
	}

	public String getSceneId() {
		return this.sceneId;
	}
	public void setMoney(Integer money) {
		this.money = money;
	}

	public Integer getMoney() {
		return this.money;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getStatus() {
		return this.status;
	}
	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getType() {
		return this.type;
	}
	public void setReceiverOpenid(String receiverOpenid) {
		this.receiverOpenid = receiverOpenid;
	}

	public String getReceiverOpenid() {
		return this.receiverOpenid;
	}

	public void setRcvTime(java.util.Date rcvTime) {
		this.rcvTime = rcvTime;
	}

	public java.util.Date getRcvTime() {
		return this.rcvTime;
	}
	public void setThreshold(Double threshold) {
		this.threshold = threshold;
	}

	public Double getThreshold() {
		return this.threshold;
	}
	public void setTotalNum(Integer totalNum) {
		this.totalNum = totalNum;
	}

	public Integer getTotalNum() {
		return this.totalNum;
	}

	public void setRefundTime(java.util.Date refundTime) {
		this.refundTime = refundTime;
	}

	public java.util.Date getRefundTime() {
		return this.refundTime;
	}
	public void setRefundMoney(Integer refundMoney) {
		this.refundMoney = refundMoney;
	}

	public Integer getRefundMoney() {
		return this.refundMoney;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public void setCreateTime(java.util.Date createTime) {
		this.createTime = createTime;
	}

	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUser(String createUser) {
		this.createUser = createUser;
	}

	public String getCreateUser() {
		return this.createUser;
	}
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	public String getCreateUserId() {
		return this.createUserId;
	}

}

