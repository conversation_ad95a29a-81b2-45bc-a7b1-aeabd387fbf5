package com.pms.czaccount.bean.account.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

import java.util.List;

@Alias("AccountSearch")
public class AccountSearch extends BaseSearch {
	private String accountId;
	private String otherPmsAccountId;
	private Integer bookingId;
	private String bookingIds ;
	private Integer registId;
	private String registIds;
	private Integer teamCodeId ;
	private Integer goodDumbId;
	private Integer price;
	private Integer payType;
	private Integer hid;
	private Integer hotelGroupId;
	private Integer payClassId;
	private String payClassName;
	private String payCodeId;
	private String payCodeName;
	private Integer roomInfoId;
	private String roomNum;
	private String accountCode;
	private Integer isSale;
	private Integer goodId;
	private String goodName;
	private Integer uintPrice;
	private Integer saleNum;
	private Integer registState;
	private Integer isDump = 1 ; // 默认不是零售
	private String unit;
	private String remark;
	private java.util.Date settleAccountTime;
	private String thirdAccoutId;
	private String reason;
	private Integer accountIndex;
	private Integer companyId;
	private Integer commissionRate;
	private String companyName;
	private Integer memberId;
	private Integer accountYear;
	private Integer accountYearMonth;
	private Integer businessDay;
	private Integer classId;
	private Integer isCancel;
	private java.util.Date createTime;
	private String createUserId;
	private String createUserName;
	private java.util.Date updateTime;
	private String updateUserId;
	private String updateUserName;
	private Integer refundPrice;
	private Integer thirdRefundState;
	private Integer accountType  ;
	private Integer registPersonId  ;
	private String registPersonName;

	public List<Integer> getAccountStateList() {
		return accountStateList;
	}

	public void setAccountStateList(List<Integer> accountStateList) {
		this.accountStateList = accountStateList;
	}

	private List<Integer> accountStateList;

	private String roomTypeId;

	private String registStates;

	private String classIds;

	private Integer businessDayStart;

	private Integer businessDayEnd;

	//创建时的订单id
	private Integer begRegistId;
	//创建时的人员id
	private Integer begRegistPersonId;
	//默认0   0.regist_id为登记单id 1.regist_id为团队id
	private Integer groupAccount;

	// 查询团队账务id
	private Integer orGroupAccountId;

	public Integer getRegistIdIsNull() {
		return registIdIsNull;
	}

	public void setRegistIdIsNull(Integer registIdIsNull) {
		this.registIdIsNull = registIdIsNull;
	}

	private Integer registIdIsNull;

	public Integer getBusinessDayStart(){
		return this.businessDayStart;
	}
	public void setBusinessDayStart(Integer businessDayStart){
		this.businessDayStart = businessDayStart;
	}

	public Integer getBusinessDayEnd(){
		return this.businessDayEnd;
	}

	public void setBusinessDayEnd(Integer businessDayEnd){
		this.businessDayEnd = businessDayEnd;
	}

	public String getRegistStates(){
		return this.registStates;
	}
	public void setRegistStates(String registStates){
		this.registStates = registStates;
	}

	public String getClassIds(){
		return  this.classIds;
	}

	public void setClassIds(String classIds){
		this.classIds = classIds;
	}

	//收款 1 or 退款-1
	private Integer payOrSale;

	// 包含的登记单id
	private String regInKeys;

	private String startTime;

	private String endTime ;

	public void setAccountId(String value) {
		this.accountId = value;
	}

	public String getAccountId() {
		return this.accountId;
	}
	public void setOtherPmsAccountId(String value) {
		this.otherPmsAccountId = value;
	}

	public String getOtherPmsAccountId() {
		return this.otherPmsAccountId;
	}
	public void setBookingId(Integer value) {
		this.bookingId = value;
	}

	public Integer getBookingId() {
		return this.bookingId;
	}
	public void setRegistId(Integer value) {
		this.registId = value;
	}

	public Integer getRegistId() {
		return this.registId;
	}
	public void setGoodDumbId(Integer value) {
		this.goodDumbId = value;
	}

	public Integer getGoodDumbId() {
		return this.goodDumbId;
	}
	public void setPrice(Integer value) {
		this.price = value;
	}

	public Integer getPrice() {
		return this.price;
	}
	public void setPayType(Integer value) {
		this.payType = value;
	}

	public Integer getPayType() {
		return this.payType;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setPayClassId(Integer value) {
		this.payClassId = value;
	}

	public Integer getPayClassId() {
		return this.payClassId;
	}
	public void setPayClassName(String value) {
		this.payClassName = value;
	}

	public String getPayClassName() {
		return this.payClassName;
	}
	public void setPayCodeId(String value) {
		this.payCodeId = value;
	}

	public String getPayCodeId() {
		return this.payCodeId;
	}
	public void setPayCodeName(String value) {
		this.payCodeName = value;
	}

	public String getPayCodeName() {
		return this.payCodeName;
	}
	public void setRoomInfoId(Integer value) {
		this.roomInfoId = value;
	}

	public Integer getRoomInfoId() {
		return this.roomInfoId;
	}
	public void setRoomNum(String value) {
		this.roomNum = value;
	}

	public String getRoomNum() {
		return this.roomNum;
	}
	public void setAccountCode(String value) {
		this.accountCode = value;
	}

	public String getAccountCode() {
		return this.accountCode;
	}
	public void setIsSale(Integer value) {
		this.isSale = value;
	}

	public Integer getIsSale() {
		return this.isSale;
	}
	public void setGoodId(Integer value) {
		this.goodId = value;
	}

	public Integer getGoodId() {
		return this.goodId;
	}
	public void setGoodName(String value) {
		this.goodName = value;
	}

	public String getGoodName() {
		return this.goodName;
	}
	public void setUintPrice(Integer value) {
		this.uintPrice = value;
	}

	public Integer getUintPrice() {
		return this.uintPrice;
	}
	public void setSaleNum(Integer value) {
		this.saleNum = value;
	}

	public Integer getSaleNum() {
		return this.saleNum;
	}
	public void setRegistState(Integer value) {
		this.registState = value;
	}

	public Integer getRegistState() {
		return this.registState;
	}
	public void setUnit(String value) {
		this.unit = value;
	}

	public String getUnit() {
		return this.unit;
	}
	public void setRemark(String value) {
		this.remark = value;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setSettleAccountTime(java.util.Date value) {
		this.settleAccountTime = value;
	}

	public java.util.Date getSettleAccountTime() {
		return this.settleAccountTime;
	}
	public void setThirdAccoutId(String value) {
		this.thirdAccoutId = value;
	}

	public String getThirdAccoutId() {
		return this.thirdAccoutId;
	}
	public void setReason(String value) {
		this.reason = value;
	}

	public String getReason() {
		return this.reason;
	}
	public void setAccountIndex(Integer value) {
		this.accountIndex = value;
	}

	public Integer getAccountIndex() {
		return this.accountIndex;
	}
	public void setCompanyId(Integer value) {
		this.companyId = value;
	}

	public Integer getCompanyId() {
		return this.companyId;
	}
	public void setCommissionRate(Integer value) {
		this.commissionRate = value;
	}

	public Integer getCommissionRate() {
		return this.commissionRate;
	}
	public void setCompanyName(String value) {
		this.companyName = value;
	}

	public String getCompanyName() {
		return this.companyName;
	}
	public void setMemberId(Integer value) {
		this.memberId = value;
	}

	public Integer getMemberId() {
		return this.memberId;
	}
	public void setAccountYear(Integer value) {
		this.accountYear = value;
	}

	public Integer getAccountYear() {
		return this.accountYear;
	}
	public void setAccountYearMonth(Integer value) {
		this.accountYearMonth = value;
	}

	public Integer getAccountYearMonth() {
		return this.accountYearMonth;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setClassId(Integer value) {
		this.classId = value;
	}

	public Integer getClassId() {
		return this.classId;
	}
	public void setIsCancel(Integer value) {
		this.isCancel = value;
	}

	public Integer getIsCancel() {
		return this.isCancel;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}

	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}

	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}

	public String getCreateUserName() {
		return this.createUserName;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}

	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}

	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}

	public String getUpdateUserName() {
		return this.updateUserName;
	}
	public void setRefundPrice(Integer value) {
		this.refundPrice = value;
	}

	public Integer getRefundPrice() {
		return this.refundPrice;
	}
	public void setThirdRefundState(Integer value) {
		this.thirdRefundState = value;
	}

	public Integer getThirdRefundState() {
		return this.thirdRefundState;
	}


	public Integer getPayOrSale() {
		return payOrSale;
	}

	public void setPayOrSale(Integer payOrSale) {
		this.payOrSale = payOrSale;
	}

	public Integer getAccountType() {
		return accountType;
	}

	public void setAccountType(Integer accountType) {
		this.accountType = accountType;
	}

	public Integer getRegistPersonId() {
		return registPersonId;
	}

	public void setRegistPersonId(Integer registPersonId) {
		this.registPersonId = registPersonId;
	}


	public Integer getTeamCodeId() {
		return teamCodeId;
	}

	public void setTeamCodeId(Integer teamCodeId) {
		this.teamCodeId = teamCodeId;
	}

	public String getRegistPersonName() {
		return registPersonName;
	}

	public void setRegistPersonName(String registPersonName) {
		this.registPersonName = registPersonName;
	}

	public String getRoomTypeId() {
		return roomTypeId;
	}

	public void setRoomTypeId(String roomTypeId) {
		this.roomTypeId = roomTypeId;
	}

	public String getRegInKeys() {
		return regInKeys;
	}

	public void setRegInKeys(String regInKeys) {
		this.regInKeys = regInKeys;
	}


	public Integer getIsDump() {
		return isDump;
	}

	public void setIsDump(Integer isDump) {
		this.isDump = isDump;
	}

	public String getRegistIds() {
		return registIds;
	}

	public void setRegistIds(String registIds) {
		this.registIds = registIds;
	}

	public String getBookingIds() {
		return bookingIds;
	}

	public void setBookingIds(String bookingIds) {
		this.bookingIds = bookingIds;
	}

	public Integer getBegRegistId() {
		return begRegistId;
	}

	public void setBegRegistId(Integer begRegistId) {
		this.begRegistId = begRegistId;
	}

	public Integer getBegRegistPersonId() {
		return begRegistPersonId;
	}

	public void setBegRegistPersonId(Integer begRegistPersonId) {
		this.begRegistPersonId = begRegistPersonId;
	}

	public Integer getGroupAccount() {
		return groupAccount;
	}

	public void setGroupAccount(Integer groupAccount) {
		this.groupAccount = groupAccount;
	}

	public Integer getOrGroupAccountId() {
		return orGroupAccountId;
	}

	public void setOrGroupAccountId(Integer orGroupAccountId) {
		this.orGroupAccountId = orGroupAccountId;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
}

