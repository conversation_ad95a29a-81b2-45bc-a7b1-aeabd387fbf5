package com.pms.czaccount.bean.pay.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("WechatAccountsSearch")
public class WechatAccountsSearch extends BaseSearch {
	private Integer id;
	private Integer hid;
	private String hotelOpenId;
	private String hotelWechatAccount;
	private String hotelWechatPassword;
	private String appId;
	private String appSecret;
	private String accessToken;
	private String isSercret;
	private String encryptionKey;
	private String getTime;
	private String getMillisecond;
	private String validateMillisecond;
	private String validateTime;
	private String updateTime;
	private String updatePerson;
	private String updatePersonId;
	private String jsApiTicket;
	private String jsGetTime;
	private String jsGetMillisecond;
	private String jsValidateMillisecond;
	private String isPay;
	private String mchId;
	private String apiKey;
	private String filePath;
	private String filePath1;
	private String notifyUrl  ;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelOpenId(String value) {
		this.hotelOpenId = value;
	}
	
	public String getHotelOpenId() {
		return this.hotelOpenId;
	}
	public void setHotelWechatAccount(String value) {
		this.hotelWechatAccount = value;
	}
	
	public String getHotelWechatAccount() {
		return this.hotelWechatAccount;
	}
	public void setHotelWechatPassword(String value) {
		this.hotelWechatPassword = value;
	}
	
	public String getHotelWechatPassword() {
		return this.hotelWechatPassword;
	}
	public void setAppId(String value) {
		this.appId = value;
	}
	
	public String getAppId() {
		return this.appId;
	}
	public void setAppSecret(String value) {
		this.appSecret = value;
	}
	
	public String getAppSecret() {
		return this.appSecret;
	}
	public void setAccessToken(String value) {
		this.accessToken = value;
	}
	
	public String getAccessToken() {
		return this.accessToken;
	}
	public void setIsSercret(String value) {
		this.isSercret = value;
	}
	
	public String getIsSercret() {
		return this.isSercret;
	}
	public void setEncryptionKey(String value) {
		this.encryptionKey = value;
	}
	
	public String getEncryptionKey() {
		return this.encryptionKey;
	}
	public void setGetTime(String value) {
		this.getTime = value;
	}
	
	public String getGetTime() {
		return this.getTime;
	}
	public void setGetMillisecond(String value) {
		this.getMillisecond = value;
	}
	
	public String getGetMillisecond() {
		return this.getMillisecond;
	}
	public void setValidateMillisecond(String value) {
		this.validateMillisecond = value;
	}
	
	public String getValidateMillisecond() {
		return this.validateMillisecond;
	}
	public void setValidateTime(String value) {
		this.validateTime = value;
	}
	
	public String getValidateTime() {
		return this.validateTime;
	}
	public void setUpdateTime(String value) {
		this.updateTime = value;
	}
	
	public String getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdatePerson(String value) {
		this.updatePerson = value;
	}
	
	public String getUpdatePerson() {
		return this.updatePerson;
	}
	public void setUpdatePersonId(String value) {
		this.updatePersonId = value;
	}
	
	public String getUpdatePersonId() {
		return this.updatePersonId;
	}
	public void setJsApiTicket(String value) {
		this.jsApiTicket = value;
	}
	
	public String getJsApiTicket() {
		return this.jsApiTicket;
	}
	public void setJsGetTime(String value) {
		this.jsGetTime = value;
	}
	
	public String getJsGetTime() {
		return this.jsGetTime;
	}
	public void setJsGetMillisecond(String value) {
		this.jsGetMillisecond = value;
	}
	
	public String getJsGetMillisecond() {
		return this.jsGetMillisecond;
	}
	public void setJsValidateMillisecond(String value) {
		this.jsValidateMillisecond = value;
	}
	
	public String getJsValidateMillisecond() {
		return this.jsValidateMillisecond;
	}
	public void setIsPay(String value) {
		this.isPay = value;
	}
	
	public String getIsPay() {
		return this.isPay;
	}
	public void setMchId(String value) {
		this.mchId = value;
	}
	
	public String getMchId() {
		return this.mchId;
	}
	public void setApiKey(String value) {
		this.apiKey = value;
	}
	
	public String getApiKey() {
		return this.apiKey;
	}
	public void setFilePath(String value) {
		this.filePath = value;
	}
	
	public String getFilePath() {
		return this.filePath;
	}
	public void setFilePath1(String value) {
		this.filePath1 = value;
	}
	
	public String getFilePath1() {
		return this.filePath1;
	}

	public String getNotifyUrl() {
		return notifyUrl;
	}

	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}
}

