package com.pms.czaccount.bean.account.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("AccountSummarySearch")
public class AccountThirdSummarySearch  extends BaseSearch {

    private Integer hid;

    private Integer businessDay;

    private Integer finishBusinessDay;

    private Integer thirdState;

    private Integer thirdRefundState;

    public Integer getHid() {
        return hid;
    }

    public void setHid(Integer hid) {
        this.hid = hid;
    }

    public Integer getBusinessDay() {
        return businessDay;
    }

    public void setBusinessDay(Integer businessDay) {
        this.businessDay = businessDay;
    }

    public Integer getFinishBusinessDay() {
        return finishBusinessDay;
    }

    public void setFinishBusinessDay(Integer finishBusinessDay) {
        this.finishBusinessDay = finishBusinessDay;
    }

    public Integer getThirdState() {
        return thirdState;
    }

    public void setThirdState(Integer thirdState) {
        this.thirdState = thirdState;
    }

    public Integer getThirdRefundState() {
        return thirdRefundState;
    }

    public void setThirdRefundState(Integer thirdRefundState) {
        this.thirdRefundState = thirdRefundState;
    }
}
