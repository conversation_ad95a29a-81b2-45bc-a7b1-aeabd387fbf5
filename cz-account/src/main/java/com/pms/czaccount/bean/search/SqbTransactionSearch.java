package com.pms.czaccount.bean.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class SqbTransactionSearch extends PageBaseRequest{
	private Integer id;
	private Integer hid;
	private String uuid;
	private String source;
	private Integer money;
	private String tradeNo;
	private String openId;
	private String payerUid;
	private String mainId;
	private Integer status;
	private java.util.Date payTime;
	private String buyerUserId;
	private Integer refundMoney;
	private java.util.Date refundTime;
	private java.util.Date scanTime;
	private Integer registId;
	private Integer bookingId;
	private String terminalSn;
	private Integer payway;
	private String paywayName;
	private String payTradeNo;
	private String interfaceRawResult;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setUuid(String value) {
		this.uuid = value;
	}
	
	public String getUuid() {
		return this.uuid;
	}
	public void setSource(String value) {
		this.source = value;
	}
	
	public String getSource() {
		return this.source;
	}
	public void setMoney(Integer value) {
		this.money = value;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setTradeNo(String value) {
		this.tradeNo = value;
	}
	
	public String getTradeNo() {
		return this.tradeNo;
	}
	public void setOpenId(String value) {
		this.openId = value;
	}
	
	public String getOpenId() {
		return this.openId;
	}
	public void setPayerUid(String value) {
		this.payerUid = value;
	}
	
	public String getPayerUid() {
		return this.payerUid;
	}
	public void setMainId(String value) {
		this.mainId = value;
	}
	
	public String getMainId() {
		return this.mainId;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}
	
	public Integer getStatus() {
		return this.status;
	}

	public void setPayTime(java.util.Date value) {
		this.payTime = value;
	}
	
	public java.util.Date getPayTime() {
		return this.payTime;
	}
	public void setBuyerUserId(String value) {
		this.buyerUserId = value;
	}
	
	public String getBuyerUserId() {
		return this.buyerUserId;
	}
	public void setRefundMoney(Integer value) {
		this.refundMoney = value;
	}
	
	public Integer getRefundMoney() {
		return this.refundMoney;
	}

	public void setRefundTime(java.util.Date value) {
		this.refundTime = value;
	}
	
	public java.util.Date getRefundTime() {
		return this.refundTime;
	}

	public void setScanTime(java.util.Date value) {
		this.scanTime = value;
	}
	
	public java.util.Date getScanTime() {
		return this.scanTime;
	}
	public void setRegistId(Integer value) {
		this.registId = value;
	}
	
	public Integer getRegistId() {
		return this.registId;
	}
	public void setBookingId(Integer value) {
		this.bookingId = value;
	}
	
	public Integer getBookingId() {
		return this.bookingId;
	}
	public void setTerminalSn(String value) {
		this.terminalSn = value;
	}
	
	public String getTerminalSn() {
		return this.terminalSn;
	}
	public void setPayway(Integer value) {
		this.payway = value;
	}
	
	public Integer getPayway() {
		return this.payway;
	}
	public void setPaywayName(String value) {
		this.paywayName = value;
	}
	
	public String getPaywayName() {
		return this.paywayName;
	}
	public void setPayTradeNo(String value) {
		this.payTradeNo = value;
	}
	
	public String getPayTradeNo() {
		return this.payTradeNo;
	}
	public void setInterfaceRawResult(String value) {
		this.interfaceRawResult = value;
	}
	
	public String getInterfaceRawResult() {
		return this.interfaceRawResult;
	}

}

