package com.pms.czaccount.bean.account;


import com.pms.czpmsutils.constant.BaseBean;
import com.pms.czpmsutils.constant.user.TbUserSession;

import java.io.Serializable;
import java.util.Date;

public class Account extends BaseBean implements Serializable {

	private String accountId  ;
	private String otherPmsAccountId  ;
	private Integer bookingId  ;
	//入住单id // 账户id    group_account: 0.regist_id为登记单id 1.regist_id为团队id
	private Integer registId;
	private Integer teamCodeId ;
	private Integer goodDumbId  ;
	private Integer price  ;
	private Integer payType  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private Integer payClassId  ;
	private String payClassName  ;
	private String payCodeId  ;
	private String payCodeName  ;
	private Integer roomInfoId  ;
	private String roomNum  ;
	private String accountCode  ;
	private Integer isSale ; // 默认不是零售
	private Integer goodId  ;
	private String goodName  ;
	private Integer uintPrice  ;
	private Integer saleNum  ;
	private Integer registState  ;
	private String unit  ;
	private String remark  ;
	private Date settleAccountTime  = new Date()  ;
	private String thirdAccoutId  ;
	private String reason  ;
	private Integer accountIndex  ;
	private Integer companyId  ;
	private Integer commissionRate  ;
	private String companyName  ;
	private Integer memberId  ;
	private Integer accountYear  ;
	private Integer accountYearMonth  ;
	private Integer businessDay  ;
	private Integer classId  ;
	private Integer isCancel  ;
	private Integer refundPrice  ;
	private Integer thirdRefundState  ;
	private Integer accountType  ;
	private Integer registPersonId =0  ;

	private String registPersonName;

	private String registStates;

	private String classIds;

	private Integer businessDayStart;

	private Integer businessDayEnd;

	private Integer roomTypeId;

	private AccountThirdPayRecode accountThirdPayRecode ;

	//创建时的订单id
	private Integer begRegistId;
	//创建时的人员id
	private Integer begRegistPersonId;
	//默认0   0.regist_id为登记单id 1.regist_id为团队id
	private Integer groupAccount = 0;


	public Account(){
	}

	public Account(TbUserSession user){
		businessDay = user.getBusinessDay();
		setCreateUserId(user.getUserId());
		setCreateTime(new Date());
		setUpdateUserName(user.getUserName());
		setUpdateUserName(user.getUserName());
		setUpdateUserId(user.getUserId());
		setUpdateTime(new Date());
		hid=user.getHid();
		hotelGroupId = user.getHotelGroupId();
		classId = user.getClassId();
		setUpdateCalssId(user.getClassId());
		setAccountYear(user.getBusinessYear());
		setAccountYearMonth(user.getBusinessMonth());
	}

	public Integer getBusinessDayStart(){
		return this.businessDayStart;
	}
	public void setBusinessDayStart(Integer businessDayStart){
		this.businessDayStart = businessDayStart;
	}

	public Integer getBusinessDayEnd(){
		return this.businessDayEnd;
	}

	public void setBusinessDayEnd(Integer businessDayEnd){
		this.businessDayEnd = businessDayEnd;
	}

	public String getRegistStates(){
		return this.registStates;
	}
	public void setRegistStates(String registStates){
		this.registStates = registStates;
	}

	public String getClassIds(){
		return  this.classIds;
	}

	public void setClassIds(String classIds){
		this.classIds = classIds;
	}

	public Account(String accountId){
		this.accountId = accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getAccountId() {
		return this.accountId;
	}
	public void setOtherPmsAccountId(String otherPmsAccountId) {
		this.otherPmsAccountId = otherPmsAccountId;
	}

	public String getOtherPmsAccountId() {
		return this.otherPmsAccountId;
	}
	public void setBookingId(Integer bookingId) {
		this.bookingId = bookingId;
	}

	public Integer getBookingId() {
		return this.bookingId;
	}
	public void setRegistId(Integer registId) {
		this.registId = registId;
	}

	public Integer getRegistId() {
		return this.registId==null?-1:this.registId;
	}
	public void setGoodDumbId(Integer goodDumbId) {
		this.goodDumbId = goodDumbId;
	}

	public Integer getGoodDumbId() {
		return this.goodDumbId;
	}
	public void setPrice(Integer price) {
		this.price = price;
	}

	public Integer getPrice() {
		return this.price;
	}
	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Integer getPayType() {
		return this.payType;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setPayClassId(Integer payClassId) {
		this.payClassId = payClassId;
	}

	public Integer getPayClassId() {
		return this.payClassId;
	}
	public void setPayClassName(String payClassName) {
		this.payClassName = payClassName;
	}

	public String getPayClassName() {
		return this.payClassName;
	}
	public void setPayCodeId(String payCodeId) {
		this.payCodeId = payCodeId;
	}

	public String getPayCodeId() {
		return this.payCodeId;
	}
	public void setPayCodeName(String payCodeName) {
		this.payCodeName = payCodeName;
	}

	public String getPayCodeName() {
		return this.payCodeName;
	}
	public void setRoomInfoId(Integer roomInfoId) {
		this.roomInfoId = roomInfoId;
	}

	public Integer getRoomInfoId() {
		return this.roomInfoId;
	}
	public void setRoomNum(String roomNum) {
		this.roomNum = roomNum;
	}

	public String getRoomNum() {
		return this.roomNum;
	}
	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}

	public String getAccountCode() {
		return this.accountCode;
	}
	public void setIsSale(Integer isSale) {
		this.isSale = isSale;
	}

	public Integer getIsSale() {
		return this.isSale;
	}
	public void setGoodId(Integer goodId) {
		this.goodId = goodId;
	}

	public Integer getGoodId() {
		return this.goodId;
	}
	public void setGoodName(String goodName) {
		this.goodName = goodName;
	}

	public String getGoodName() {
		return this.goodName;
	}
	public void setUintPrice(Integer uintPrice) {
		this.uintPrice = uintPrice;
	}

	public Integer getUintPrice() {
		return this.uintPrice;
	}
	public void setSaleNum(Integer saleNum) {
		this.saleNum = saleNum;
	}

	public Integer getSaleNum() {
		return this.saleNum;
	}
	public void setRegistState(Integer registState) {
		this.registState = registState;
	}

	public Integer getRegistState() {
		return this.registState;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getUnit() {
		return this.unit;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getRemark() {
		return this.remark;
	}

	public void setSettleAccountTime(Date settleAccountTime) {
		this.settleAccountTime = settleAccountTime;
	}

	public Date getSettleAccountTime() {
		return this.settleAccountTime;
	}
	public void setThirdAccoutId(String thirdAccoutId) {
		this.thirdAccoutId = thirdAccoutId;
	}

	public String getThirdAccoutId() {
		return this.thirdAccoutId;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getReason() {
		return this.reason;
	}
	public void setAccountIndex(Integer accountIndex) {
		this.accountIndex = accountIndex;
	}

	public Integer getAccountIndex() {
		return this.accountIndex;
	}
	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	public Integer getCompanyId() {
		return this.companyId;
	}
	public void setCommissionRate(Integer commissionRate) {
		this.commissionRate = commissionRate;
	}

	public Integer getCommissionRate() {
		return this.commissionRate;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getCompanyName() {
		return this.companyName;
	}
	public void setMemberId(Integer memberId) {
		this.memberId = memberId;
	}

	public Integer getMemberId() {
		return this.memberId;
	}
	public void setAccountYear(Integer accountYear) {
		this.accountYear = accountYear;
	}

	public Integer getAccountYear() {
		return this.accountYear;
	}
	public void setAccountYearMonth(Integer accountYearMonth) {
		this.accountYearMonth = accountYearMonth;
	}

	public Integer getAccountYearMonth() {
		return this.accountYearMonth;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}

	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setClassId(Integer classId) {
		this.classId = classId;
	}

	public Integer getClassId() {
		return this.classId;
	}
	public void setIsCancel(Integer isCancel) {
		this.isCancel = isCancel;
	}

	public Integer getIsCancel() {
		return this.isCancel;
	}

	public void setRefundPrice(Integer refundPrice) {
		this.refundPrice = refundPrice;
	}

	public Integer getRefundPrice() {
		return this.refundPrice;
	}
	public void setThirdRefundState(Integer thirdRefundState) {
		this.thirdRefundState = thirdRefundState;
	}

	public Integer getThirdRefundState() {
		return this.thirdRefundState;
	}

	public Integer getAccountType() {
		return accountType;
	}

	public void setAccountType(Integer accountType) {
		this.accountType = accountType;
	}

	public Integer getRegistPersonId() {
		return registPersonId;
	}

	public void setRegistPersonId(Integer registPersonId) {
		this.registPersonId = registPersonId;
	}

	public Integer getTeamCodeId() {
		return teamCodeId;
	}

	public void setTeamCodeId(Integer teamCodeId) {
		this.teamCodeId = teamCodeId;
	}

	public Integer getRoomTypeId() {
		return roomTypeId;
	}

	public void setRoomTypeId(Integer roomTypeId) {
		this.roomTypeId = roomTypeId;
	}

	public AccountThirdPayRecode getAccountThirdPayRecode() {
		return accountThirdPayRecode;
	}

	public void setAccountThirdPayRecode(AccountThirdPayRecode accountThirdPayRecode) {
		this.accountThirdPayRecode = accountThirdPayRecode;
	}

	public String getRegistPersonName() {
		return registPersonName;
	}

	public void setRegistPersonName(String registPersonName) {
		this.registPersonName = registPersonName;
	}

	public Integer getBegRegistId() {
		return begRegistId;
	}

	public void setBegRegistId(Integer begRegistId) {
		this.begRegistId = begRegistId;
	}

	public Integer getBegRegistPersonId() {
		return begRegistPersonId;
	}

	public void setBegRegistPersonId(Integer begRegistPersonId) {
		this.begRegistPersonId = begRegistPersonId;
	}

	public Integer getGroupAccount() {
		return groupAccount;
	}

	public void setGroupAccount(Integer groupAccount) {
		this.groupAccount = groupAccount;
	}
}

