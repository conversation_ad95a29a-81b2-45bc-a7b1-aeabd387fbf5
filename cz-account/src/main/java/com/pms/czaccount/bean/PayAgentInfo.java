package com.pms.czaccount.bean;

import java.io.Serializable;

/**
 * 
 */
public class PayAgentInfo implements Serializable{
	//
	private Integer id;
	//酒店id
	private Integer hid;
	//支付宝商户ID
	private String alipaySellerId;
	//支付宝授权token
	private String alipayAppAuthToken;
	//微信子账号id
	private String weiChatPaySubMchId;
	//收钱吧激活码
	private String sqbActiveCode;
	//收钱吧B扫C终端号
	private String sqbBcTerminalSn;
	//收钱吧B扫C终端密匙
	private String sqbBcTerminalKey;
	//收钱吧C扫B终端号
	private String sqbCbTerminalSn;
	//收钱吧C扫B终端密匙
	private String sqbCbTerminalKey;

	public PayAgentInfo(){
	}

	public PayAgentInfo(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setAlipaySellerId(String alipaySellerId) {
		this.alipaySellerId = alipaySellerId;
	}
	
	public String getAlipaySellerId() {
		return this.alipaySellerId;
	}
	public void setAlipayAppAuthToken(String alipayAppAuthToken) {
		this.alipayAppAuthToken = alipayAppAuthToken;
	}
	
	public String getAlipayAppAuthToken() {
		return this.alipayAppAuthToken;
	}
	public void setWeiChatPaySubMchId(String weiChatPaySubMchId) {
		this.weiChatPaySubMchId = weiChatPaySubMchId;
	}
	
	public String getWeiChatPaySubMchId() {
		return this.weiChatPaySubMchId;
	}
	public void setSqbActiveCode(String sqbActiveCode) {
		this.sqbActiveCode = sqbActiveCode;
	}
	
	public String getSqbActiveCode() {
		return this.sqbActiveCode;
	}
	public void setSqbBcTerminalSn(String sqbBcTerminalSn) {
		this.sqbBcTerminalSn = sqbBcTerminalSn;
	}
	
	public String getSqbBcTerminalSn() {
		return this.sqbBcTerminalSn;
	}
	public void setSqbBcTerminalKey(String sqbBcTerminalKey) {
		this.sqbBcTerminalKey = sqbBcTerminalKey;
	}
	
	public String getSqbBcTerminalKey() {
		return this.sqbBcTerminalKey;
	}
	public void setSqbCbTerminalSn(String sqbCbTerminalSn) {
		this.sqbCbTerminalSn = sqbCbTerminalSn;
	}
	
	public String getSqbCbTerminalSn() {
		return this.sqbCbTerminalSn;
	}
	public void setSqbCbTerminalKey(String sqbCbTerminalKey) {
		this.sqbCbTerminalKey = sqbCbTerminalKey;
	}
	
	public String getSqbCbTerminalKey() {
		return this.sqbCbTerminalKey;
	}

}

