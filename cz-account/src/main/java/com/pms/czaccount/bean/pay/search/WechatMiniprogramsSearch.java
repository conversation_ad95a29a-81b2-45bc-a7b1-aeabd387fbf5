package com.pms.czaccount.bean.pay.search;

import com.pms.czpmsutils.request.PageBaseRequest;
import org.apache.ibatis.type.Alias;


@Alias("WechatMiniprogramsSearch")
public class WechatMiniprogramsSearch extends PageBaseRequest {
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private String name;
	private String appid;
	private String payAppid;
	private String appsecret;
	private String accessToken;
	private java.util.Date accessTokenTime;
	private Integer accessTokenValidityTime;
	private String jsApiTicket;
	private java.util.Date jsApiTicketTime;
	private Integer jsApiTicketValidityTime;
	private String mchId;
	private String apiKey;
	private String filePath;
	private String filePath1;
	private String notifyUrl;
	private Integer cardTypeId  ;
	private Integer cardLevelId  ;
	private String qrcode  ;

	// 生成类型 1 房间 2.待定
	private Integer createType = 1;

	private String roomNo ;

	// 生成的主表id
	private String dataId ;

	/**
	 * 最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~，
	 * 其它字符请自行编码为合法字符（因不支持%，中文无法使用 urlencode 处理，请使用其他编码方式）
	 */
	private String scene;
	/**
	 * 必须是已经发布的小程序存在的页面（否则报错），例如 pages/index/index,
	 * 根路径前不要填加 /,不能携带参数（参数请放在scene字段里），如果不填写这个字段，默认跳主页面
	 */
	private String page;
	// 二维码的宽度，单位 px，最小 280px，最大 1280px
	private Integer width = 430;
	// 自动配置线条颜色，如果颜色依然是黑色，则说明不建议配置主色调，默认 false
	private Boolean autoColor = false;
	// auto_color 为 false 时生效，使用 rgb 设置颜色 例如 {"r":"xxx","g":"xxx","b":"xxx"} 十进制表示
	private String lineColor = "{\"r\":0,\"g\":0,\"b\":0}";

	// 是否需要透明底色，为 true 时，生成透明底色的小程序
	private Boolean hyaline = false;

	public void setId(Integer value) {
		this.id = value;
	}

	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setName(String value) {
		this.name = value;
	}

	public String getName() {
		return this.name;
	}
	public void setAppid(String value) {
		this.appid = value;
	}

	public String getAppid() {
		return this.appid;
	}
	public void setPayAppid(String value) {
		this.payAppid = value;
	}

	public String getPayAppid() {
		return this.payAppid;
	}
	public void setAppsecret(String value) {
		this.appsecret = value;
	}

	public String getAppsecret() {
		return this.appsecret;
	}
	public void setAccessToken(String value) {
		this.accessToken = value;
	}

	public String getAccessToken() {
		return this.accessToken;
	}

	public void setAccessTokenTime(java.util.Date value) {
		this.accessTokenTime = value;
	}

	public java.util.Date getAccessTokenTime() {
		return this.accessTokenTime;
	}
	public void setAccessTokenValidityTime(Integer value) {
		this.accessTokenValidityTime = value;
	}

	public Integer getAccessTokenValidityTime() {
		return this.accessTokenValidityTime;
	}
	public void setJsApiTicket(String value) {
		this.jsApiTicket = value;
	}

	public String getJsApiTicket() {
		return this.jsApiTicket;
	}

	public void setJsApiTicketTime(java.util.Date value) {
		this.jsApiTicketTime = value;
	}

	public java.util.Date getJsApiTicketTime() {
		return this.jsApiTicketTime;
	}
	public void setJsApiTicketValidityTime(Integer value) {
		this.jsApiTicketValidityTime = value;
	}

	public Integer getJsApiTicketValidityTime() {
		return this.jsApiTicketValidityTime;
	}
	public void setMchId(String value) {
		this.mchId = value;
	}

	public String getMchId() {
		return this.mchId;
	}
	public void setApiKey(String value) {
		this.apiKey = value;
	}

	public String getApiKey() {
		return this.apiKey;
	}
	public void setFilePath(String value) {
		this.filePath = value;
	}

	public String getFilePath() {
		return this.filePath;
	}
	public void setFilePath1(String value) {
		this.filePath1 = value;
	}

	public String getFilePath1() {
		return this.filePath1;
	}
	public void setNotifyUrl(String value) {
		this.notifyUrl = value;
	}

	public String getNotifyUrl() {
		return this.notifyUrl;
	}

	public Integer getCardTypeId() {
		return cardTypeId;
	}

	public void setCardTypeId(Integer cardTypeId) {
		this.cardTypeId = cardTypeId;
	}

	public Integer getCardLevelId() {
		return cardLevelId;
	}

	public void setCardLevelId(Integer cardLevelId) {
		this.cardLevelId = cardLevelId;
	}

	public String getScene() {
		return scene;
	}

	public void setScene(String scene) {
		this.scene = scene;
	}

	public String getPage() {
		return page;
	}

	public void setPage(String page) {
		this.page = page;
	}

	public Integer getWidth() {
		return width;
	}

	public void setWidth(Integer width) {
		this.width = width;
	}

	public Boolean getAutoColor() {
		return autoColor;
	}

	public void setAutoColor(Boolean autoColor) {
		this.autoColor = autoColor;
	}

	public String getLineColor() {
		return lineColor;
	}

	public void setLineColor(String lineColor) {
		this.lineColor = lineColor;
	}

	public Boolean getHyaline() {
		return hyaline;
	}

	public void setHyaline(Boolean hyaline) {
		hyaline = hyaline;
	}


	public String getQrcode() {
		return qrcode;
	}

	public void setQrcode(String qrcode) {
		this.qrcode = qrcode;
	}

	public Integer getCreateType() {
		return createType;
	}

	public void setCreateType(Integer createType) {
		this.createType = createType;
	}

	public String getDataId() {
		return dataId;
	}

	public void setDataId(String dataId) {
		this.dataId = dataId;
	}

	public String getRoomNo() {
		return roomNo;
	}

	public void setRoomNo(String roomNo) {
		this.roomNo = roomNo;
	}
}

