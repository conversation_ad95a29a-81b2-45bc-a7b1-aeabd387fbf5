package com.pms.czaccount.bean;

import java.io.Serializable;

/**
 * 收钱吧退款记录表
 */
public class SqbRefund implements Serializable{
	//ID
	private Integer id;
	//酒店编号
	private Integer hid;
	//帐单编号
	private String mainId;
	//支付的mainid
	private String payMainId;
	//退款金额
	private Integer refundMoney;
	//退款时间
	private java.util.Date refundTime;
	//退款状态(1:成功,0:失败)
	private Integer status;
	//支付人账号
	private String payUserId;
	//来源
	private String source;
	//收钱吧终端ID
	private String terminalSn;
	//支付方式
	private Integer payway;
	//支付方式
	private String paywayName;
	//支付宝或微信的订单号
	private String payTradeNo;
	//接口返回的原始内容
	private String interfaceRawResult;

	public SqbRefund(){
	}

	public SqbRefund(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setMainId(String mainId) {
		this.mainId = mainId;
	}
	
	public String getMainId() {
		return this.mainId;
	}
	public void setPayMainId(String payMainId) {
		this.payMainId = payMainId;
	}
	
	public String getPayMainId() {
		return this.payMainId;
	}
	public void setRefundMoney(Integer refundMoney) {
		this.refundMoney = refundMoney;
	}
	
	public Integer getRefundMoney() {
		return this.refundMoney;
	}

	public void setRefundTime(java.util.Date refundTime) {
		this.refundTime = refundTime;
	}
	
	public java.util.Date getRefundTime() {
		return this.refundTime;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	public Integer getStatus() {
		return this.status;
	}
	public void setPayUserId(String payUserId) {
		this.payUserId = payUserId;
	}
	
	public String getPayUserId() {
		return this.payUserId;
	}
	public void setSource(String source) {
		this.source = source;
	}
	
	public String getSource() {
		return this.source;
	}
	public void setTerminalSn(String terminalSn) {
		this.terminalSn = terminalSn;
	}
	
	public String getTerminalSn() {
		return this.terminalSn;
	}
	public void setPayway(Integer payway) {
		this.payway = payway;
	}
	
	public Integer getPayway() {
		return this.payway;
	}
	public void setPaywayName(String paywayName) {
		this.paywayName = paywayName;
	}
	
	public String getPaywayName() {
		return this.paywayName;
	}
	public void setPayTradeNo(String payTradeNo) {
		this.payTradeNo = payTradeNo;
	}
	
	public String getPayTradeNo() {
		return this.payTradeNo;
	}
	public void setInterfaceRawResult(String interfaceRawResult) {
		this.interfaceRawResult = interfaceRawResult;
	}
	
	public String getInterfaceRawResult() {
		return this.interfaceRawResult;
	}

}

