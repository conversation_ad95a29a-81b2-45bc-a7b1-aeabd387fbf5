package com.pms.czaccount.bean.pay;

import java.io.Serializable;
import java.util.Date;

public class WechatMiniprograms implements Serializable{
	private Integer id  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private String name  ;
	private String appid  ;
	private String payAppid  ;
	private String appsecret  ;
	private String accessToken  ;
	private java.util.Date accessTokenTime = new Date()  ;
	private Integer accessTokenValidityTime  ;
	private String jsApiTicket  ;
	private java.util.Date jsApiTicketTime  ;
	private Integer jsApiTicketValidityTime  ;
	private String mchId  ;
	private String apiKey  ;
	private String filePath  ;
	private String filePath1  ;
	private String notifyUrl  ;
	private Integer cardTypeId  ;
	private Integer cardLevelId  ;
	private String qrcode  ;

	public WechatMiniprograms(){
	}

	public WechatMiniprograms(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setName(String name) {
		this.name = name;
	}

	public String getName() {
		return this.name;
	}
	public void setAppid(String appid) {
		this.appid = appid;
	}

	public String getAppid() {
		return this.appid;
	}
	public void setPayAppid(String payAppid) {
		this.payAppid = payAppid;
	}

	public String getPayAppid() {
		return this.payAppid;
	}
	public void setAppsecret(String appsecret) {
		this.appsecret = appsecret;
	}

	public String getAppsecret() {
		return this.appsecret;
	}
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken;
	}

	public String getAccessToken() {
		return this.accessToken;
	}

	public void setAccessTokenTime(java.util.Date accessTokenTime) {
		this.accessTokenTime = accessTokenTime;
	}

	public java.util.Date getAccessTokenTime() {
		return this.accessTokenTime;
	}
	public void setAccessTokenValidityTime(Integer accessTokenValidityTime) {
		this.accessTokenValidityTime = accessTokenValidityTime;
	}

	public Integer getAccessTokenValidityTime() {
		return this.accessTokenValidityTime;
	}
	public void setJsApiTicket(String jsApiTicket) {
		this.jsApiTicket = jsApiTicket;
	}

	public String getJsApiTicket() {
		return this.jsApiTicket;
	}

	public void setJsApiTicketTime(java.util.Date jsApiTicketTime) {
		this.jsApiTicketTime = jsApiTicketTime;
	}

	public java.util.Date getJsApiTicketTime() {
		return this.jsApiTicketTime;
	}
	public void setJsApiTicketValidityTime(Integer jsApiTicketValidityTime) {
		this.jsApiTicketValidityTime = jsApiTicketValidityTime;
	}

	public Integer getJsApiTicketValidityTime() {
		return this.jsApiTicketValidityTime;
	}
	public void setMchId(String mchId) {
		this.mchId = mchId;
	}

	public String getMchId() {
		return this.mchId;
	}
	public void setApiKey(String apiKey) {
		this.apiKey = apiKey;
	}

	public String getApiKey() {
		return this.apiKey;
	}
	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	public String getFilePath() {
		return this.filePath;
	}
	public void setFilePath1(String filePath1) {
		this.filePath1 = filePath1;
	}

	public String getFilePath1() {
		return this.filePath1;
	}
	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}

	public String getNotifyUrl() {
		return this.notifyUrl;
	}

	public Integer getCardTypeId() {
		return cardTypeId;
	}

	public void setCardTypeId(Integer cardTypeId) {
		this.cardTypeId = cardTypeId;
	}

	public Integer getCardLevelId() {
		return cardLevelId;
	}

	public void setCardLevelId(Integer cardLevelId) {
		this.cardLevelId = cardLevelId;
	}

	public String getQrcode() {
		return qrcode;
	}

	public void setQrcode(String qrcode) {
		this.qrcode = qrcode;
	}
}

