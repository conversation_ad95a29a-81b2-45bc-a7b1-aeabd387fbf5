package com.pms.czaccount.bean.pay;

import java.io.Serializable;

public class SysWechatPay implements Serializable {
	private Integer id  ;
	private Integer hid  ;
	private String machineUuid  ;
	private String mainId  ;
	private Double money  ;
	private Integer moneyFen  ;
	private String appId  ;
	private String mchId  ;
	private String apiKey  ;
	private String orderDesc  ;
	private String source  ;
	private Integer payStatus  ;
	private java.util.Date updateTime  ;
	private java.util.Date payTime  ;
	private String transactionId  ;
	private String openId  ;
	private String isSubscribe  ;
	private String tradeType  ;
	private String bankType  ;
	private String feeType  ;
	private Integer cashFee  ;
	private String cashFeeType  ;
	private Integer couponFee  ;
	private Integer couponCount  ;
	private String couponId  ;
	private String couponFeen  ;
	private Integer refundMoney  ;
	private java.util.Date timeEnd  ;
	private java.util.Date refundTime  ;
	private Integer registId  ;
	private Integer bookingId  ;

	public SysWechatPay(){
	}

	public SysWechatPay(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setMachineUuid(String machineUuid) {
		this.machineUuid = machineUuid;
	}

	public String getMachineUuid() {
		return this.machineUuid;
	}
	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getMainId() {
		return this.mainId;
	}
	public void setMoney(Double money) {
		this.money = money;
	}

	public Double getMoney() {
		return this.money;
	}
	public void setMoneyFen(Integer moneyFen) {
		this.moneyFen = moneyFen;
	}

	public Integer getMoneyFen() {
		return this.moneyFen;
	}
	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getAppId() {
		return this.appId;
	}
	public void setMchId(String mchId) {
		this.mchId = mchId;
	}

	public String getMchId() {
		return this.mchId;
	}
	public void setApiKey(String apiKey) {
		this.apiKey = apiKey;
	}

	public String getApiKey() {
		return this.apiKey;
	}
	public void setOrderDesc(String orderDesc) {
		this.orderDesc = orderDesc;
	}

	public String getOrderDesc() {
		return this.orderDesc;
	}
	public void setSource(String source) {
		this.source = source;
	}

	public String getSource() {
		return this.source;
	}
	public void setPayStatus(Integer payStatus) {
		this.payStatus = payStatus;
	}

	public Integer getPayStatus() {
		return this.payStatus;
	}

	public void setUpdateTime(java.util.Date updateTime) {
		this.updateTime = updateTime;
	}

	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}

	public void setPayTime(java.util.Date payTime) {
		this.payTime = payTime;
	}

	public java.util.Date getPayTime() {
		return this.payTime;
	}
	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	public String getTransactionId() {
		return this.transactionId;
	}
	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getOpenId() {
		return this.openId;
	}
	public void setIsSubscribe(String isSubscribe) {
		this.isSubscribe = isSubscribe;
	}

	public String getIsSubscribe() {
		return this.isSubscribe;
	}
	public void setTradeType(String tradeType) {
		this.tradeType = tradeType;
	}

	public String getTradeType() {
		return this.tradeType;
	}
	public void setBankType(String bankType) {
		this.bankType = bankType;
	}

	public String getBankType() {
		return this.bankType;
	}
	public void setFeeType(String feeType) {
		this.feeType = feeType;
	}

	public String getFeeType() {
		return this.feeType;
	}
	public void setCashFee(Integer cashFee) {
		this.cashFee = cashFee;
	}

	public Integer getCashFee() {
		return this.cashFee;
	}
	public void setCashFeeType(String cashFeeType) {
		this.cashFeeType = cashFeeType;
	}

	public String getCashFeeType() {
		return this.cashFeeType;
	}
	public void setCouponFee(Integer couponFee) {
		this.couponFee = couponFee;
	}

	public Integer getCouponFee() {
		return this.couponFee;
	}
	public void setCouponCount(Integer couponCount) {
		this.couponCount = couponCount;
	}

	public Integer getCouponCount() {
		return this.couponCount;
	}
	public void setCouponId(String couponId) {
		this.couponId = couponId;
	}

	public String getCouponId() {
		return this.couponId;
	}
	public void setCouponFeen(String couponFeen) {
		this.couponFeen = couponFeen;
	}

	public String getCouponFeen() {
		return this.couponFeen;
	}
	public void setRefundMoney(Integer refundMoney) {
		this.refundMoney = refundMoney;
	}

	public Integer getRefundMoney() {
		return this.refundMoney;
	}

	public void setTimeEnd(java.util.Date timeEnd) {
		this.timeEnd = timeEnd;
	}

	public java.util.Date getTimeEnd() {
		return this.timeEnd;
	}

	public void setRefundTime(java.util.Date refundTime) {
		this.refundTime = refundTime;
	}

	public java.util.Date getRefundTime() {
		return this.refundTime;
	}

	public Integer getRegistId() {
		return registId;
	}

	public void setRegistId(Integer registId) {
		this.registId = registId;
	}

	public Integer getBookingId() {
		return bookingId;
	}

	public void setBookingId(Integer bookingId) {
		this.bookingId = bookingId;
	}
}

