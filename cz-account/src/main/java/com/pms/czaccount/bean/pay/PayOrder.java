package com.pms.czaccount.bean.pay;


import java.util.Date;

public class PayOrder {

    public Integer id;
    public String mainId;
    /**
     * 1.待支付,2.已支付,3.支付失败,4.超时
     */
    public Integer status;
    public String account; //账户
    /**
     * 1.微信,2.支付宝,3.收钱吧BC,4.收钱吧CB
     */
    public Integer payType;
    public String addAccountParam; //添加账户参数
    public String payParam; //支付时的参数
    public String sessionToken;
    public Integer hid;

    public Date createTime;
    public Date updateTime;


    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public String getPayParam() {
        return payParam;
    }

    public void setPayParam(String payParam) {
        this.payParam = payParam;
    }

    public Integer getHid() {
        return hid;
    }

    public void setHid(Integer hid) {
        this.hid = hid;
    }

    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getAddAccountParam() {
        return addAccountParam;
    }

    public void setAddAccountParam(String addAccountParam) {
        this.addAccountParam = addAccountParam;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMainId() {
        return mainId;
    }

    public void setMainId(String mainId) {
        this.mainId = mainId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
