package com.pms.czaccount.bean.pay.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("SysWechatRefundSearch")
public class SysWechatRefundSearch extends BaseSearch {
	private Integer id;
	private String appId;
	private Integer hid;
	private String uuid;
	private String mchId;
	private String mainId;
	private String payMainId  ;
	private String outRefundNo;
	private String refundId;
	private Integer totalFee;
	private Integer refundFee;
	private java.util.Date updateTime;
	private String source;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setAppId(String value) {
		this.appId = value;
	}
	
	public String getAppId() {
		return this.appId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setUuid(String value) {
		this.uuid = value;
	}
	
	public String getUuid() {
		return this.uuid;
	}
	public void setMchId(String value) {
		this.mchId = value;
	}
	
	public String getMchId() {
		return this.mchId;
	}
	public void setMainId(String value) {
		this.mainId = value;
	}
	
	public String getMainId() {
		return this.mainId;
	}
	public void setOutRefundNo(String value) {
		this.outRefundNo = value;
	}
	
	public String getOutRefundNo() {
		return this.outRefundNo;
	}
	public void setRefundId(String value) {
		this.refundId = value;
	}
	
	public String getRefundId() {
		return this.refundId;
	}
	public void setTotalFee(Integer value) {
		this.totalFee = value;
	}
	
	public Integer getTotalFee() {
		return this.totalFee;
	}
	public void setRefundFee(Integer value) {
		this.refundFee = value;
	}
	
	public Integer getRefundFee() {
		return this.refundFee;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setSource(String value) {
		this.source = value;
	}
	
	public String getSource() {
		return this.source;
	}

	public String getPayMainId() {
		return payMainId;
	}

	public void setPayMainId(String payMainId) {
		this.payMainId = payMainId;
	}
}

