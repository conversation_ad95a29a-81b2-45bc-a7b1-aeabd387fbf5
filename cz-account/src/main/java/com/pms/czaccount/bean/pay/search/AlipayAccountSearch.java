package com.pms.czaccount.bean.pay.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("AlipayAccountSearch")
public class AlipayAccountSearch extends BaseSearch {
	private Integer id;
	private Integer hid;
	private String privateKey;
	private String url;
	private String appId;
	private String charSet;
	private String format;
	private String signType  ;
	private String alipayPublicKey;
	private java.util.Date updateTime;
	private String updateUserId;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setPrivateKey(String value) {
		this.privateKey = value;
	}
	
	public String getPrivateKey() {
		return this.privateKey;
	}
	public void setUrl(String value) {
		this.url = value;
	}
	
	public String getUrl() {
		return this.url;
	}
	public void setAppId(String value) {
		this.appId = value;
	}
	
	public String getAppId() {
		return this.appId;
	}
	public void setCharSet(String value) {
		this.charSet = value;
	}
	
	public String getCharSet() {
		return this.charSet;
	}
	public void setFormat(String value) {
		this.format = value;
	}
	
	public String getFormat() {
		return this.format;
	}
	public void setAlipayPublicKey(String value) {
		this.alipayPublicKey = value;
	}
	
	public String getAlipayPublicKey() {
		return this.alipayPublicKey;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}

	public String getSignType() {
		return signType;
	}

	public void setSignType(String signType) {
		this.signType = signType;
	}


}

