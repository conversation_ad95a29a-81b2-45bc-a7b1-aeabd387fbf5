package com.pms.czaccount.bean.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class SqbRefundSearch extends PageBaseRequest{
	private Integer id;
	private Integer hid;
	private String mainId;
	private String payMainId;
	private Integer refundMoney;
	private java.util.Date refundTime;
	private Integer status;
	private String payUserId;
	private String source;
	private String terminalSn;
	private Integer payway;
	private String paywayName;
	private String payTradeNo;
	private String interfaceRawResult;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setMainId(String value) {
		this.mainId = value;
	}
	
	public String getMainId() {
		return this.mainId;
	}
	public void setPayMainId(String value) {
		this.payMainId = value;
	}
	
	public String getPayMainId() {
		return this.payMainId;
	}
	public void setRefundMoney(Integer value) {
		this.refundMoney = value;
	}
	
	public Integer getRefundMoney() {
		return this.refundMoney;
	}

	public void setRefundTime(java.util.Date value) {
		this.refundTime = value;
	}
	
	public java.util.Date getRefundTime() {
		return this.refundTime;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}
	
	public Integer getStatus() {
		return this.status;
	}
	public void setPayUserId(String value) {
		this.payUserId = value;
	}
	
	public String getPayUserId() {
		return this.payUserId;
	}
	public void setSource(String value) {
		this.source = value;
	}
	
	public String getSource() {
		return this.source;
	}
	public void setTerminalSn(String value) {
		this.terminalSn = value;
	}
	
	public String getTerminalSn() {
		return this.terminalSn;
	}
	public void setPayway(Integer value) {
		this.payway = value;
	}
	
	public Integer getPayway() {
		return this.payway;
	}
	public void setPaywayName(String value) {
		this.paywayName = value;
	}
	
	public String getPaywayName() {
		return this.paywayName;
	}
	public void setPayTradeNo(String value) {
		this.payTradeNo = value;
	}
	
	public String getPayTradeNo() {
		return this.payTradeNo;
	}
	public void setInterfaceRawResult(String value) {
		this.interfaceRawResult = value;
	}
	
	public String getInterfaceRawResult() {
		return this.interfaceRawResult;
	}

}

