package com.pms.czaccount.bean.pay.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("SysWechatRedpackSearch")
public class SysWechatRedpackSearch extends BaseSearch {
	private Integer id;
	private String sn;
	private String otherSn;
	private Integer otherId;
	private Integer hid;
	private Integer sendHid;
	private String actName;
	private Integer redpackSettingId  ;
	private String wishing;
	private String sceneId;
	private Integer money;
	private Integer type  ;
	private Integer status;
	private String receiverOpenid;
	private java.util.Date rcvTime;
	private Double threshold;
	private Integer totalNum;
	private java.util.Date refundTime;
	private Integer refundMoney;
	private Integer businessDay;
	private java.util.Date createTime;
	private String createUser;
	private String createUserId;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setSn(String value) {
		this.sn = value;
	}
	
	public String getSn() {
		return this.sn;
	}
	public void setOtherSn(String value) {
		this.otherSn = value;
	}
	
	public String getOtherSn() {
		return this.otherSn;
	}
	public void setOtherId(Integer value) {
		this.otherId = value;
	}
	
	public Integer getOtherId() {
		return this.otherId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setSendHid(Integer value) {
		this.sendHid = value;
	}
	
	public Integer getSendHid() {
		return this.sendHid;
	}
	public void setActName(String value) {
		this.actName = value;
	}
	
	public String getActName() {
		return this.actName;
	}
	public void setWishing(String value) {
		this.wishing = value;
	}
	
	public String getWishing() {
		return this.wishing;
	}
	public void setSceneId(String value) {
		this.sceneId = value;
	}
	
	public String getSceneId() {
		return this.sceneId;
	}
	public void setMoney(Integer value) {
		this.money = value;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}
	
	public Integer getStatus() {
		return this.status;
	}
	public void setReceiverOpenid(String value) {
		this.receiverOpenid = value;
	}
	
	public String getReceiverOpenid() {
		return this.receiverOpenid;
	}

	public void setRcvTime(java.util.Date value) {
		this.rcvTime = value;
	}
	
	public java.util.Date getRcvTime() {
		return this.rcvTime;
	}
	public void setThreshold(Double value) {
		this.threshold = value;
	}
	
	public Double getThreshold() {
		return this.threshold;
	}
	public void setTotalNum(Integer value) {
		this.totalNum = value;
	}
	
	public Integer getTotalNum() {
		return this.totalNum;
	}

	public void setRefundTime(java.util.Date value) {
		this.refundTime = value;
	}
	
	public java.util.Date getRefundTime() {
		return this.refundTime;
	}
	public void setRefundMoney(Integer value) {
		this.refundMoney = value;
	}
	
	public Integer getRefundMoney() {
		return this.refundMoney;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUser(String value) {
		this.createUser = value;
	}
	
	public String getCreateUser() {
		return this.createUser;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}


	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getRedpackSettingId() {
		return redpackSettingId;
	}

	public void setRedpackSettingId(Integer redpackSettingId) {
		this.redpackSettingId = redpackSettingId;
	}
}

