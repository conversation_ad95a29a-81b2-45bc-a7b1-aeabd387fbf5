package com.pms.czaccount.bean.account;


import java.io.Serializable;

/**
 * 第三方账务汇总
 */
public class AccountThirdSummary  implements Serializable {

    // 金额
    private Integer sumMoney;

    // 完成金额、退款金额
    private Integer refund;

    // 支付类型 1微信 2支付宝 3预授权 4银行卡消费 5会员消费/冻结
    private Integer payType;


    public Integer getSumMoney() {
        return sumMoney;
    }

    public void setSumMoney(Integer sumMoney) {
        this.sumMoney = sumMoney;
    }

    public Integer getRefund() {
        return refund;
    }

    public void setRefund(Integer refund) {
        this.refund = refund;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }
}
