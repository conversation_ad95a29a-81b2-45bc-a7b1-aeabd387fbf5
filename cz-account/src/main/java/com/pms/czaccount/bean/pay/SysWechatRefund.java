package com.pms.czaccount.bean.pay;

import java.io.Serializable;
import java.util.Date;

public class SysWechatRefund implements Serializable {
	private Integer id  ;
	private String appId  ;
	private Integer hid  ;
	private String uuid  ;
	private String mchId  ;
	private String mainId  ;
	private String payMainId  ;
	private String outRefundNo  ;
	private String refundId  ;
	private Integer totalFee  ;
	private Integer refundFee  ;
	private Date updateTime  ;
	private String source  ;

	public SysWechatRefund(){
	}

	public SysWechatRefund(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setAppId(String appId) {
		this.appId = appId;
	}
	
	public String getAppId() {
		return this.appId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setUuid(String uuid) {
		this.uuid = uuid;
	}
	
	public String getUuid() {
		return this.uuid;
	}
	public void setMchId(String mchId) {
		this.mchId = mchId;
	}
	
	public String getMchId() {
		return this.mchId;
	}
	public void setMainId(String mainId) {
		this.mainId = mainId;
	}
	
	public String getMainId() {
		return this.mainId;
	}
	public void setOutRefundNo(String outRefundNo) {
		this.outRefundNo = outRefundNo;
	}
	
	public String getOutRefundNo() {
		return this.outRefundNo;
	}
	public void setRefundId(String refundId) {
		this.refundId = refundId;
	}
	
	public String getRefundId() {
		return this.refundId;
	}
	public void setTotalFee(Integer totalFee) {
		this.totalFee = totalFee;
	}
	
	public Integer getTotalFee() {
		return this.totalFee;
	}
	public void setRefundFee(Integer refundFee) {
		this.refundFee = refundFee;
	}
	
	public Integer getRefundFee() {
		return this.refundFee;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public Date getUpdateTime() {
		return this.updateTime;
	}
	public void setSource(String source) {
		this.source = source;
	}
	
	public String getSource() {
		return this.source;
	}

	public String getPayMainId() {
		return payMainId;
	}

	public void setPayMainId(String payMainId) {
		this.payMainId = payMainId;
	}
}

