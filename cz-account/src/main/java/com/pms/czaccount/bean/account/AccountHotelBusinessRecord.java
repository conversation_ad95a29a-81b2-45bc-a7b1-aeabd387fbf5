package com.pms.czaccount.bean.account;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 */
public class AccountHotelBusinessRecord implements Serializable{
	//第三方支付与酒店业务记录表
	private Integer id;
	//酒店id
	private Integer hid;
	//连锁酒店id
	private Integer hotelGroupId;
	//创建时间
	private Date createTime;
	//创建人
	private String createUserId;
	//创建人名称
	private String createUserName;
	//业务场景  1-房间相关入住 2-小商品入账 3-会员注册 7-会员储值
	private Integer businessType;
	//业务ID
	private String mainId;
	//请求参数
	private String postData;
	//请求方法
	private String postFun;
	//1-成功 0-失败
	private Integer resultCode;
	//第三方支付ID
	private String accountThirdId;

	public AccountHotelBusinessRecord(){
	}

	public AccountHotelBusinessRecord(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}
	public void setBusinessType(Integer businessType) {
		this.businessType = businessType;
	}
	
	public Integer getBusinessType() {
		return this.businessType;
	}
	public void setMainId(String mainId) {
		this.mainId = mainId;
	}
	
	public String getMainId() {
		return this.mainId;
	}
	public void setPostData(String postData) {
		this.postData = postData;
	}
	
	public String getPostData() {
		return this.postData;
	}
	public void setPostFun(String postFun) {
		this.postFun = postFun;
	}
	
	public String getPostFun() {
		return this.postFun;
	}
	public void setResultCode(Integer resultCode) {
		this.resultCode = resultCode;
	}
	
	public Integer getResultCode() {
		return this.resultCode;
	}
	public void setAccountThirdId(String accountThirdId) {
		this.accountThirdId = accountThirdId;
	}
	
	public String getAccountThirdId() {
		return this.accountThirdId;
	}

}

