package com.pms.czaccount.bean.pay;

import java.io.Serializable;

public class AccountReset implements Serializable{
	//
	private Integer id;
	//
	private String mainId;
	//
	private Integer hid;
	//
	private Integer hotelGroupId;
	// 1.微信 2.支付宝
	private Integer type;
	//重置的退款金额
	private Integer money;
	//
	private java.util.Date createTime;
	//
	private String createUserName;
	//
	private String createUserId;

	public AccountReset(){
	}

	public AccountReset(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return this.id;
	}
	public void setMainId(String mainId) {
		this.mainId = mainId;
	}

	public String getMainId() {
		return this.mainId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getType() {
		return this.type;
	}
	public void setMoney(Integer money) {
		this.money = money;
	}

	public Integer getMoney() {
		return this.money;
	}

	public void setCreateTime(java.util.Date createTime) {
		this.createTime = createTime;
	}

	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

	public String getCreateUserName() {
		return this.createUserName;
	}
	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	public String getCreateUserId() {
		return this.createUserId;
	}

}

