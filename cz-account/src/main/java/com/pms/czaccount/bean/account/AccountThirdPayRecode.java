package com.pms.czaccount.bean.account;


import com.pms.czpmsutils.constant.BaseBean;

import java.io.Serializable;

public class AccountThirdPayRecode extends BaseBean implements Serializable{
	private String accountThirdId  ;
	private Integer hid  ;
	private Integer hotelGroupId  ;
	private String accountId  ;
	private String counterId  ;
	private String operatorId  ;
	private String transType  ;
	private Integer amount  ;
	private String oldTrace  ;
	private String oldDate  ;
	private String oldRef  ;
	private String oldAuth  ;
	private String oldBatch  ;
	private String memo  ;
	private String lrc  ;
	private String trace  ;
	private String barkId  ;
	private String batch  ;
	private String transDate  ;
	private String transTime  ;
	private String ref  ;
	private String auth  ;
	private String mid  ;
	private String tid  ;
	private String effectiveDays  ;
	private Integer payType  ;
	private Integer price  ;
	private Integer businessDay  ;
	private Integer finishBusinessDay  ;
	private Integer refund  ;
	private Integer thirdState  ;
	private Integer thirdRefundState  ;
	private Integer classId  ;


	public AccountThirdPayRecode(){
	}

	public AccountThirdPayRecode(String accountThirdId){
		this.accountThirdId = accountThirdId;
	}

	public void setAccountThirdId(String accountThirdId) {
		this.accountThirdId = accountThirdId;
	}
	
	public String getAccountThirdId() {
		return this.accountThirdId;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}
	
	public String getAccountId() {
		return this.accountId;
	}
	public void setCounterId(String counterId) {
		this.counterId = counterId;
	}
	
	public String getCounterId() {
		return this.counterId;
	}
	public void setOperatorId(String operatorId) {
		this.operatorId = operatorId;
	}
	
	public String getOperatorId() {
		return this.operatorId;
	}
	public void setTransType(String transType) {
		this.transType = transType;
	}
	
	public String getTransType() {
		return this.transType;
	}
	public void setAmount(Integer amount) {
		this.amount = amount;
	}
	
	public Integer getAmount() {
		return this.amount;
	}
	public void setOldTrace(String oldTrace) {
		this.oldTrace = oldTrace;
	}
	
	public String getOldTrace() {
		return this.oldTrace;
	}
	public void setOldDate(String oldDate) {
		this.oldDate = oldDate;
	}
	
	public String getOldDate() {
		return this.oldDate;
	}
	public void setOldRef(String oldRef) {
		this.oldRef = oldRef;
	}
	
	public String getOldRef() {
		return this.oldRef;
	}
	public void setOldAuth(String oldAuth) {
		this.oldAuth = oldAuth;
	}
	
	public String getOldAuth() {
		return this.oldAuth;
	}
	public void setOldBatch(String oldBatch) {
		this.oldBatch = oldBatch;
	}
	
	public String getOldBatch() {
		return this.oldBatch;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	
	public String getMemo() {
		return this.memo;
	}
	public void setLrc(String lrc) {
		this.lrc = lrc;
	}
	
	public String getLrc() {
		return this.lrc;
	}
	public void setTrace(String trace) {
		this.trace = trace;
	}
	
	public String getTrace() {
		return this.trace;
	}
	public void setBarkId(String barkId) {
		this.barkId = barkId;
	}
	
	public String getBarkId() {
		return this.barkId;
	}
	public void setBatch(String batch) {
		this.batch = batch;
	}
	
	public String getBatch() {
		return this.batch;
	}
	public void setTransDate(String transDate) {
		this.transDate = transDate;
	}
	
	public String getTransDate() {
		return this.transDate;
	}
	public void setTransTime(String transTime) {
		this.transTime = transTime;
	}
	
	public String getTransTime() {
		return this.transTime;
	}
	public void setRef(String ref) {
		this.ref = ref;
	}
	
	public String getRef() {
		return this.ref;
	}
	public void setAuth(String auth) {
		this.auth = auth;
	}
	
	public String getAuth() {
		return this.auth;
	}
	public void setMid(String mid) {
		this.mid = mid;
	}
	
	public String getMid() {
		return this.mid;
	}
	public void setTid(String tid) {
		this.tid = tid;
	}
	
	public String getTid() {
		return this.tid;
	}
	public void setEffectiveDays(String effectiveDays) {
		this.effectiveDays = effectiveDays;
	}
	
	public String getEffectiveDays() {
		return this.effectiveDays;
	}
	public void setPayType(Integer payType) {
		this.payType = payType;
	}
	
	public Integer getPayType() {
		return this.payType;
	}
	public void setPrice(Integer price) {
		this.price = price;
	}
	
	public Integer getPrice() {
		return this.price;
	}
	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setFinishBusinessDay(Integer finishBusinessDay) {
		this.finishBusinessDay = finishBusinessDay;
	}
	
	public Integer getFinishBusinessDay() {
		return this.finishBusinessDay;
	}
	public void setRefund(Integer refund) {
		this.refund = refund;
	}
	
	public Integer getRefund() {
		return this.refund;
	}
	public void setThirdState(Integer thirdState) {
		this.thirdState = thirdState;
	}
	
	public Integer getThirdState() {
		return this.thirdState;
	}
	public void setThirdRefundState(Integer thirdRefundState) {
		this.thirdRefundState = thirdRefundState;
	}
	
	public Integer getThirdRefundState() {
		return this.thirdRefundState;
	}
	public void setClassId(Integer classId) {
		this.classId = classId;
	}
	
	public Integer getClassId() {
		return this.classId;
	}


}

