package com.pms.czaccount.bean.account.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@Alias("AccountThirdPayRecodeSearch")
public class AccountThirdPayRecodeSearch extends BaseSearch {
	private String accountThirdId;
	private Integer hid;
	private Integer hotelGroupId;
	private String accountId;
	private String counterId;
	private String operatorId;
	private String transType;
	private Integer amount;
	private String oldTrace;
	private String oldDate;
	private String oldRef;
	private String oldAuth;
	private String oldBatch;
	private String memo;
	private String lrc;
	private String trace;
	private String barkId;
	private String batch;
	private String transDate;
	private String transTime;
	private String ref;
	private String auth;
	private String mid;
	private String tid;
	private String effectiveDays;
	private Integer payType;
	private Integer price;
	private Integer businessDay;
	private Integer finishBusinessDay;
	private Integer refund;
	private Integer thirdState;
	private Integer thirdRefundState;
	private Integer classId;
	private java.util.Date updateTime;
	private String updateUserId;
	private Integer updateCalssId;
	private String updateUserName;
	private java.util.Date createTime;
	private String createUserId;
	private String createUserName;

	public void setAccountThirdId(String value) {
		this.accountThirdId = value;
	}
	
	public String getAccountThirdId() {
		return this.accountThirdId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setAccountId(String value) {
		this.accountId = value;
	}
	
	public String getAccountId() {
		return this.accountId;
	}
	public void setCounterId(String value) {
		this.counterId = value;
	}
	
	public String getCounterId() {
		return this.counterId;
	}
	public void setOperatorId(String value) {
		this.operatorId = value;
	}
	
	public String getOperatorId() {
		return this.operatorId;
	}
	public void setTransType(String value) {
		this.transType = value;
	}
	
	public String getTransType() {
		return this.transType;
	}
	public void setAmount(Integer value) {
		this.amount = value;
	}
	
	public Integer getAmount() {
		return this.amount;
	}
	public void setOldTrace(String value) {
		this.oldTrace = value;
	}
	
	public String getOldTrace() {
		return this.oldTrace;
	}
	public void setOldDate(String value) {
		this.oldDate = value;
	}
	
	public String getOldDate() {
		return this.oldDate;
	}
	public void setOldRef(String value) {
		this.oldRef = value;
	}
	
	public String getOldRef() {
		return this.oldRef;
	}
	public void setOldAuth(String value) {
		this.oldAuth = value;
	}
	
	public String getOldAuth() {
		return this.oldAuth;
	}
	public void setOldBatch(String value) {
		this.oldBatch = value;
	}
	
	public String getOldBatch() {
		return this.oldBatch;
	}
	public void setMemo(String value) {
		this.memo = value;
	}
	
	public String getMemo() {
		return this.memo;
	}
	public void setLrc(String value) {
		this.lrc = value;
	}
	
	public String getLrc() {
		return this.lrc;
	}
	public void setTrace(String value) {
		this.trace = value;
	}
	
	public String getTrace() {
		return this.trace;
	}
	public void setBarkId(String value) {
		this.barkId = value;
	}
	
	public String getBarkId() {
		return this.barkId;
	}
	public void setBatch(String value) {
		this.batch = value;
	}
	
	public String getBatch() {
		return this.batch;
	}
	public void setTransDate(String value) {
		this.transDate = value;
	}
	
	public String getTransDate() {
		return this.transDate;
	}
	public void setTransTime(String value) {
		this.transTime = value;
	}
	
	public String getTransTime() {
		return this.transTime;
	}
	public void setRef(String value) {
		this.ref = value;
	}
	
	public String getRef() {
		return this.ref;
	}
	public void setAuth(String value) {
		this.auth = value;
	}
	
	public String getAuth() {
		return this.auth;
	}
	public void setMid(String value) {
		this.mid = value;
	}
	
	public String getMid() {
		return this.mid;
	}
	public void setTid(String value) {
		this.tid = value;
	}
	
	public String getTid() {
		return this.tid;
	}
	public void setEffectiveDays(String value) {
		this.effectiveDays = value;
	}
	
	public String getEffectiveDays() {
		return this.effectiveDays;
	}
	public void setPayType(Integer value) {
		this.payType = value;
	}
	
	public Integer getPayType() {
		return this.payType;
	}
	public void setPrice(Integer value) {
		this.price = value;
	}
	
	public Integer getPrice() {
		return this.price;
	}
	public void setBusinessDay(Integer value) {
		this.businessDay = value;
	}
	
	public Integer getBusinessDay() {
		return this.businessDay;
	}
	public void setFinishBusinessDay(Integer value) {
		this.finishBusinessDay = value;
	}
	
	public Integer getFinishBusinessDay() {
		return this.finishBusinessDay;
	}
	public void setRefund(Integer value) {
		this.refund = value;
	}
	
	public Integer getRefund() {
		return this.refund;
	}
	public void setThirdState(Integer value) {
		this.thirdState = value;
	}
	
	public Integer getThirdState() {
		return this.thirdState;
	}
	public void setThirdRefundState(Integer value) {
		this.thirdRefundState = value;
	}
	
	public Integer getThirdRefundState() {
		return this.thirdRefundState;
	}
	public void setClassId(Integer value) {
		this.classId = value;
	}
	
	public Integer getClassId() {
		return this.classId;
	}

	public void setUpdateTime(java.util.Date value) {
		this.updateTime = value;
	}
	
	public java.util.Date getUpdateTime() {
		return this.updateTime;
	}
	public void setUpdateUserId(String value) {
		this.updateUserId = value;
	}
	
	public String getUpdateUserId() {
		return this.updateUserId;
	}
	public void setUpdateCalssId(Integer value) {
		this.updateCalssId = value;
	}
	
	public Integer getUpdateCalssId() {
		return this.updateCalssId;
	}
	public void setUpdateUserName(String value) {
		this.updateUserName = value;
	}
	
	public String getUpdateUserName() {
		return this.updateUserName;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}

}

