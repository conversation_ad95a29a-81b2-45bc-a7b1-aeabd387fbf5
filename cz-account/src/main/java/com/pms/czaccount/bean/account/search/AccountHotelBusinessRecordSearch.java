package com.pms.czaccount.bean.account.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class AccountHotelBusinessRecordSearch extends PageBaseRequest{
	private Integer id;
	private Integer hid;
	private Integer hotelGroupId;
	private java.util.Date createTime;
	private String createUserId;
	private String createUserName;
	private Integer businessType;
	private String mainId;
	private String postData;
	private String postFun;
	private Integer resultCode;
	private String accountThirdId;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}

	public void setCreateTime(java.util.Date value) {
		this.createTime = value;
	}
	
	public java.util.Date getCreateTime() {
		return this.createTime;
	}
	public void setCreateUserId(String value) {
		this.createUserId = value;
	}
	
	public String getCreateUserId() {
		return this.createUserId;
	}
	public void setCreateUserName(String value) {
		this.createUserName = value;
	}
	
	public String getCreateUserName() {
		return this.createUserName;
	}
	public void setBusinessType(Integer value) {
		this.businessType = value;
	}
	
	public Integer getBusinessType() {
		return this.businessType;
	}
	public void setMainId(String value) {
		this.mainId = value;
	}
	
	public String getMainId() {
		return this.mainId;
	}
	public void setPostData(String value) {
		this.postData = value;
	}
	
	public String getPostData() {
		return this.postData;
	}
	public void setPostFun(String value) {
		this.postFun = value;
	}
	
	public String getPostFun() {
		return this.postFun;
	}
	public void setResultCode(Integer value) {
		this.resultCode = value;
	}
	
	public Integer getResultCode() {
		return this.resultCode;
	}
	public void setAccountThirdId(String value) {
		this.accountThirdId = value;
	}
	
	public String getAccountThirdId() {
		return this.accountThirdId;
	}

}

