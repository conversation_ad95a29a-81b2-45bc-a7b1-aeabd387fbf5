package com.pms.czaccount.bean.pay.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class WechatSubscribeMsgSearch extends PageBaseRequest{
	private Integer id;
	private Integer hotelGroupId;
	private Integer hid;
	private Integer msgType;
	private String msgId;
	private String msgName;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHotelGroupId(Integer value) {
		this.hotelGroupId = value;
	}
	
	public Integer getHotelGroupId() {
		return this.hotelGroupId;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setMsgType(Integer value) {
		this.msgType = value;
	}
	
	public Integer getMsgType() {
		return this.msgType;
	}
	public void setMsgId(String value) {
		this.msgId = value;
	}
	
	public String getMsgId() {
		return this.msgId;
	}
	public void setMsgName(String value) {
		this.msgName = value;
	}
	
	public String getMsgName() {
		return this.msgName;
	}

}

