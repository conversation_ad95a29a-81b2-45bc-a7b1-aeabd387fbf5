package com.pms.czaccount.bean.pay.search;

import com.pms.czpmsutils.constant.BaseSearch;
import org.apache.ibatis.type.Alias;

@<PERSON>as("AlipayFaceTransactionSearch")
public class AlipayFaceTransactionSearch extends BaseSearch {
	private Integer id;
	private Integer hid;
	private String uuid;
	private Integer money;
	private String tradeNo;
	private String openId;
	private String buyerLogonId;
	private String mainId;
	private Integer status;
	private java.util.Date payTime;
	private String buyerUserId;
	private Integer refundMoney;
	private java.util.Date refundTime;
	private java.util.Date scanTime;
	private String source;
	private String payDesc;
	private Integer registId  ;
	private Integer bookingId  ;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setUuid(String value) {
		this.uuid = value;
	}
	
	public String getUuid() {
		return this.uuid;
	}
	public void setMoney(Integer value) {
		this.money = value;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setTradeNo(String value) {
		this.tradeNo = value;
	}
	
	public String getTradeNo() {
		return this.tradeNo;
	}
	public void setOpenId(String value) {
		this.openId = value;
	}
	
	public String getOpenId() {
		return this.openId;
	}
	public void setBuyerLogonId(String value) {
		this.buyerLogonId = value;
	}
	
	public String getBuyerLogonId() {
		return this.buyerLogonId;
	}
	public void setMainId(String value) {
		this.mainId = value;
	}
	
	public String getMainId() {
		return this.mainId;
	}
	public void setStatus(Integer value) {
		this.status = value;
	}
	
	public Integer getStatus() {
		return this.status;
	}

	public void setPayTime(java.util.Date value) {
		this.payTime = value;
	}
	
	public java.util.Date getPayTime() {
		return this.payTime;
	}
	public void setBuyerUserId(String value) {
		this.buyerUserId = value;
	}
	
	public String getBuyerUserId() {
		return this.buyerUserId;
	}
	public void setRefundMoney(Integer value) {
		this.refundMoney = value;
	}
	
	public Integer getRefundMoney() {
		return this.refundMoney;
	}

	public void setRefundTime(java.util.Date value) {
		this.refundTime = value;
	}
	
	public java.util.Date getRefundTime() {
		return this.refundTime;
	}

	public void setScanTime(java.util.Date value) {
		this.scanTime = value;
	}
	
	public java.util.Date getScanTime() {
		return this.scanTime;
	}
	public void setSource(String value) {
		this.source = value;
	}
	
	public String getSource() {
		return this.source;
	}
	public void setPayDesc(String value) {
		this.payDesc = value;
	}
	
	public String getPayDesc() {
		return this.payDesc;
	}

	public Integer getRegistId() {
		return registId;
	}

	public void setRegistId(Integer registId) {
		this.registId = registId;
	}

	public Integer getBookingId() {
		return bookingId;
	}

	public void setBookingId(Integer bookingId) {
		this.bookingId = bookingId;
	}
}

