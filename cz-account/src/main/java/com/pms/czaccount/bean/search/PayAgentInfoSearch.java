package com.pms.czaccount.bean.search;
import com.pms.czpmsutils.request.PageBaseRequest;

public class PayAgentInfoSearch extends PageBaseRequest{
	private Integer id;
	private Integer hid;
	private String alipaySellerId;
	private String alipayAppAuthToken;
	private String weiChatPaySubMchId;
	private String sqbActiveCode;
	private String sqbBcTerminalSn;
	private String sqbBcTerminalKey;
	private String sqbCbTerminalSn;
	private String sqbCbTerminalKey;

	public void setId(Integer value) {
		this.id = value;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer value) {
		this.hid = value;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setAlipaySellerId(String value) {
		this.alipaySellerId = value;
	}
	
	public String getAlipaySellerId() {
		return this.alipaySellerId;
	}
	public void setAlipayAppAuthToken(String value) {
		this.alipayAppAuthToken = value;
	}
	
	public String getAlipayAppAuthToken() {
		return this.alipayAppAuthToken;
	}
	public void setWeiChatPaySubMchId(String value) {
		this.weiChatPaySubMchId = value;
	}
	
	public String getWeiChatPaySubMchId() {
		return this.weiChatPaySubMchId;
	}
	public void setSqbActiveCode(String value) {
		this.sqbActiveCode = value;
	}
	
	public String getSqbActiveCode() {
		return this.sqbActiveCode;
	}
	public void setSqbBcTerminalSn(String value) {
		this.sqbBcTerminalSn = value;
	}
	
	public String getSqbBcTerminalSn() {
		return this.sqbBcTerminalSn;
	}
	public void setSqbBcTerminalKey(String value) {
		this.sqbBcTerminalKey = value;
	}
	
	public String getSqbBcTerminalKey() {
		return this.sqbBcTerminalKey;
	}
	public void setSqbCbTerminalSn(String value) {
		this.sqbCbTerminalSn = value;
	}
	
	public String getSqbCbTerminalSn() {
		return this.sqbCbTerminalSn;
	}
	public void setSqbCbTerminalKey(String value) {
		this.sqbCbTerminalKey = value;
	}
	
	public String getSqbCbTerminalKey() {
		return this.sqbCbTerminalKey;
	}

}

