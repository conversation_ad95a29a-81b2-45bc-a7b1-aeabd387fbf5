package com.pms.czaccount.bean.search;

import com.pms.czpmsutils.request.BaseRequest;

// 分账
public class SplitAccountReset extends BaseRequest {
	// 分账id
	private String accountId;
	// 入住人姓名
	private String personName = "";
	//
	private Integer registPersonId;
	// 分账金额
	private Integer money;

	private String remark;


	public SplitAccountReset(){
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public String getPersonName() {
		return personName;
	}

	public void setPersonName(String personName) {
		this.personName = personName;
	}

	public Integer getRegistPersonId() {
		return registPersonId;
	}

	public void setRegistPersonId(Integer registPersonId) {
		this.registPersonId = registPersonId;
	}

	public Integer getMoney() {
		return money;
	}

	public void setMoney(Integer money) {
		this.money = money;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}

