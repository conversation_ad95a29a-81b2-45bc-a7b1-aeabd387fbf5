package com.pms.czaccount.bean.account;

import java.io.Serializable;
import java.util.Date;

public class AccountCancel implements Serializable{
	//
	private Integer accountCancelId;
	//账单ID
	private String accountId;
	//账单金额
	private Integer price;
	//消费0 /付款1
	private Integer payType;
	//酒店id
	private Integer hid;
	//连锁酒店id
	private Integer hotelGroupId;
	//费用码大类id
	private Integer payClassId;
	//大类名称
	private String payClassName;
	//费用码小类id
	private Integer payCodeId;
	//小类名称
	private String payCodeName;
	//房间id
	private Integer roomInfoId;
	//房间号
	private String roomNum;
	//账单编号
	private String accountCode;
	//入账人姓名
	private String accountCreateUserName;
	//是否售卖，既哑房账 0是/1不是
	private Integer isSale;
	//
	private Integer businessDay;
	//班次ID
	private Integer classId;
	//创建时间
	private java.util.Date createTime;
	//创建人
	private String createUserId;
	//预订单ID
	private Integer bookingId;
	//房单ID 用来查询
	private Integer registId;
	//
	private String registPersonName;
	//
	private Integer registPersonId;
	//团队id
	private Integer teamCodeId;
	//冲账人姓名
	private String createUserName;
	//原房间号
	private String sourceRoomNum;
	//原登记号
	private Integer sourceRegistId;
	//
	private String sourceRegistPersonName;
	//
	private Integer sourceRegistPersonId;
	//1.转账 2.冲账
	private Integer cancelType;
	//事由
	private String reason;
	//备注
	private String remark;



	public AccountCancel(){
	}

	public AccountCancel(Integer accountCancelId){
		this.accountCancelId = accountCancelId;
	}

	public Integer getAccountCancelId() {
		return accountCancelId;
	}

	public void setAccountCancelId(Integer accountCancelId) {
		this.accountCancelId = accountCancelId;
	}

	public String getAccountId() {
		return accountId;
	}

	public void setAccountId(String accountId) {
		this.accountId = accountId;
	}

	public Integer getPrice() {
		return price;
	}

	public void setPrice(Integer price) {
		this.price = price;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Integer getHid() {
		return hid;
	}

	public void setHid(Integer hid) {
		this.hid = hid;
	}

	public Integer getHotelGroupId() {
		return hotelGroupId;
	}

	public void setHotelGroupId(Integer hotelGroupId) {
		this.hotelGroupId = hotelGroupId;
	}

	public Integer getPayClassId() {
		return payClassId;
	}

	public void setPayClassId(Integer payClassId) {
		this.payClassId = payClassId;
	}

	public String getPayClassName() {
		return payClassName;
	}

	public void setPayClassName(String payClassName) {
		this.payClassName = payClassName;
	}

	public Integer getPayCodeId() {
		return payCodeId;
	}

	public void setPayCodeId(Integer payCodeId) {
		this.payCodeId = payCodeId;
	}

	public String getPayCodeName() {
		return payCodeName;
	}

	public void setPayCodeName(String payCodeName) {
		this.payCodeName = payCodeName;
	}

	public Integer getRoomInfoId() {
		return roomInfoId;
	}

	public void setRoomInfoId(Integer roomInfoId) {
		this.roomInfoId = roomInfoId;
	}

	public String getRoomNum() {
		return roomNum;
	}

	public void setRoomNum(String roomNum) {
		this.roomNum = roomNum;
	}

	public String getAccountCode() {
		return accountCode;
	}

	public void setAccountCode(String accountCode) {
		this.accountCode = accountCode;
	}

	public String getAccountCreateUserName() {
		return accountCreateUserName;
	}

	public void setAccountCreateUserName(String accountCreateUserName) {
		this.accountCreateUserName = accountCreateUserName;
	}

	public Integer getIsSale() {
		return isSale;
	}

	public void setIsSale(Integer isSale) {
		this.isSale = isSale;
	}

	public Integer getBusinessDay() {
		return businessDay;
	}

	public void setBusinessDay(Integer businessDay) {
		this.businessDay = businessDay;
	}

	public Integer getClassId() {
		return classId;
	}

	public void setClassId(Integer classId) {
		this.classId = classId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	public Integer getBookingId() {
		return bookingId;
	}

	public void setBookingId(Integer bookingId) {
		this.bookingId = bookingId;
	}

	public Integer getRegistId() {
		return registId;
	}

	public void setRegistId(Integer registId) {
		this.registId = registId;
	}

	public String getRegistPersonName() {
		return registPersonName;
	}

	public void setRegistPersonName(String registPersonName) {
		this.registPersonName = registPersonName;
	}

	public Integer getRegistPersonId() {
		return registPersonId;
	}

	public void setRegistPersonId(Integer registPersonId) {
		this.registPersonId = registPersonId;
	}

	public Integer getTeamCodeId() {
		return teamCodeId;
	}

	public void setTeamCodeId(Integer teamCodeId) {
		this.teamCodeId = teamCodeId;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

	public String getSourceRoomNum() {
		return sourceRoomNum;
	}

	public void setSourceRoomNum(String sourceRoomNum) {
		this.sourceRoomNum = sourceRoomNum;
	}

	public Integer getSourceRegistId() {
		return sourceRegistId;
	}

	public void setSourceRegistId(Integer sourceRegistId) {
		this.sourceRegistId = sourceRegistId;
	}

	public String getSourceRegistPersonName() {
		return sourceRegistPersonName;
	}

	public void setSourceRegistPersonName(String sourceRegistPersonName) {
		this.sourceRegistPersonName = sourceRegistPersonName;
	}

	public Integer getSourceRegistPersonId() {
		return sourceRegistPersonId;
	}

	public void setSourceRegistPersonId(Integer sourceRegistPersonId) {
		this.sourceRegistPersonId = sourceRegistPersonId;
	}

	public Integer getCancelType() {
		return cancelType;
	}

	public void setCancelType(Integer cancelType) {
		this.cancelType = cancelType;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
}

