package com.pms.czaccount.bean.pay;

import java.io.Serializable;
import java.util.Date;

public class AlipayFaceTransaction implements Serializable {
	private Integer id  ;
	private Integer hid  ;
	private String uuid  ;
	private Integer money  ;
	private String tradeNo  ;
	private String openId  ;
	private String buyerLogonId  ;
	private String mainId  ;
	private Integer status  ;
	private Date payTime  ;
	private String buyerUserId  ;
	private Integer refundMoney  ;
	private Date refundTime  ;
	private Date scanTime  ;
	private String source  ;
	private String payDesc  ;
	private Integer registId  ;
	private Integer bookingId  ;

	public AlipayFaceTransaction(){
	}

	public AlipayFaceTransaction(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setUuid(String uuid) {
		this.uuid = uuid;
	}
	
	public String getUuid() {
		return this.uuid;
	}
	public void setMoney(Integer money) {
		this.money = money;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}
	
	public String getTradeNo() {
		return this.tradeNo;
	}
	public void setOpenId(String openId) {
		this.openId = openId;
	}
	
	public String getOpenId() {
		return this.openId;
	}
	public void setBuyerLogonId(String buyerLogonId) {
		this.buyerLogonId = buyerLogonId;
	}
	
	public String getBuyerLogonId() {
		return this.buyerLogonId;
	}
	public void setMainId(String mainId) {
		this.mainId = mainId;
	}
	
	public String getMainId() {
		return this.mainId;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	public Integer getStatus() {
		return this.status;
	}

	public void setPayTime(Date payTime) {
		this.payTime = payTime;
	}
	
	public Date getPayTime() {
		return this.payTime;
	}
	public void setBuyerUserId(String buyerUserId) {
		this.buyerUserId = buyerUserId;
	}
	
	public String getBuyerUserId() {
		return this.buyerUserId;
	}
	public void setRefundMoney(Integer refundMoney) {
		this.refundMoney = refundMoney;
	}
	
	public Integer getRefundMoney() {
		return this.refundMoney;
	}

	public void setRefundTime(Date refundTime) {
		this.refundTime = refundTime;
	}
	
	public Date getRefundTime() {
		return this.refundTime;
	}

	public void setScanTime(Date scanTime) {
		this.scanTime = scanTime;
	}
	
	public Date getScanTime() {
		return this.scanTime;
	}
	public void setSource(String source) {
		this.source = source;
	}
	
	public String getSource() {
		return this.source;
	}
	public void setPayDesc(String payDesc) {
		this.payDesc = payDesc;
	}
	
	public String getPayDesc() {
		return this.payDesc;
	}

	public Integer getRegistId() {
		return registId;
	}

	public void setRegistId(Integer registId) {
		this.registId = registId;
	}

	public Integer getBookingId() {
		return bookingId;
	}

	public void setBookingId(Integer bookingId) {
		this.bookingId = bookingId;
	}
}

