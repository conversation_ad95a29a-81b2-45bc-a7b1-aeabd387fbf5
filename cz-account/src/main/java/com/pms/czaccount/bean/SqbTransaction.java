package com.pms.czaccount.bean;

import java.io.Serializable;

/**
 * 收钱吧交易表
 */
public class SqbTransaction implements Serializable{
	//ID
	private Integer id;
	//酒店编号
	private Integer hid;
	//自助机编号
	private String uuid;
	//自助机编号
	private String source;
	//支付金额
	private Integer money;
	//交易编号
	private String tradeNo;
	//开发者编号
	private String openId;
	//支付账号
	private String payerUid;
	//帐单编号
	private String mainId;
	//交易状态(-1：已退款，0:未支付，1，支付成功，2，退款一部分 3，已取消)
	private Integer status;
	//支付时间
	private java.util.Date payTime;
	//支付人ID
	private String buyerUserId;
	//退款金额
	private Integer refundMoney;
	//退款时间
	private java.util.Date refundTime;
	//扫码时间
	private java.util.Date scanTime;
	//
	private Integer registId;
	//
	private Integer bookingId;
	//收钱吧终端ID
	private String terminalSn;
	//支付方式
	private Integer payway;
	//支付方式
	private String paywayName;
	//支付宝或微信的订单号
	private String payTradeNo;
	//接口返回的原始内容
	private String interfaceRawResult;

	public SqbTransaction(){
	}

	public SqbTransaction(Integer id){
		this.id = id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
	public Integer getId() {
		return this.id;
	}
	public void setHid(Integer hid) {
		this.hid = hid;
	}
	
	public Integer getHid() {
		return this.hid;
	}
	public void setUuid(String uuid) {
		this.uuid = uuid;
	}
	
	public String getUuid() {
		return this.uuid;
	}
	public void setSource(String source) {
		this.source = source;
	}
	
	public String getSource() {
		return this.source;
	}
	public void setMoney(Integer money) {
		this.money = money;
	}
	
	public Integer getMoney() {
		return this.money;
	}
	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}
	
	public String getTradeNo() {
		return this.tradeNo;
	}
	public void setOpenId(String openId) {
		this.openId = openId;
	}
	
	public String getOpenId() {
		return this.openId;
	}
	public void setPayerUid(String payerUid) {
		this.payerUid = payerUid;
	}
	
	public String getPayerUid() {
		return this.payerUid;
	}
	public void setMainId(String mainId) {
		this.mainId = mainId;
	}
	
	public String getMainId() {
		return this.mainId;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	
	public Integer getStatus() {
		return this.status;
	}

	public void setPayTime(java.util.Date payTime) {
		this.payTime = payTime;
	}
	
	public java.util.Date getPayTime() {
		return this.payTime;
	}
	public void setBuyerUserId(String buyerUserId) {
		this.buyerUserId = buyerUserId;
	}
	
	public String getBuyerUserId() {
		return this.buyerUserId;
	}
	public void setRefundMoney(Integer refundMoney) {
		this.refundMoney = refundMoney;
	}
	
	public Integer getRefundMoney() {
		return this.refundMoney;
	}

	public void setRefundTime(java.util.Date refundTime) {
		this.refundTime = refundTime;
	}
	
	public java.util.Date getRefundTime() {
		return this.refundTime;
	}

	public void setScanTime(java.util.Date scanTime) {
		this.scanTime = scanTime;
	}
	
	public java.util.Date getScanTime() {
		return this.scanTime;
	}
	public void setRegistId(Integer registId) {
		this.registId = registId;
	}
	
	public Integer getRegistId() {
		return this.registId;
	}
	public void setBookingId(Integer bookingId) {
		this.bookingId = bookingId;
	}
	
	public Integer getBookingId() {
		return this.bookingId;
	}
	public void setTerminalSn(String terminalSn) {
		this.terminalSn = terminalSn;
	}
	
	public String getTerminalSn() {
		return this.terminalSn;
	}
	public void setPayway(Integer payway) {
		this.payway = payway;
	}
	
	public Integer getPayway() {
		return this.payway;
	}
	public void setPaywayName(String paywayName) {
		this.paywayName = paywayName;
	}
	
	public String getPaywayName() {
		return this.paywayName;
	}
	public void setPayTradeNo(String payTradeNo) {
		this.payTradeNo = payTradeNo;
	}
	
	public String getPayTradeNo() {
		return this.payTradeNo;
	}
	public void setInterfaceRawResult(String interfaceRawResult) {
		this.interfaceRawResult = interfaceRawResult;
	}
	
	public String getInterfaceRawResult() {
		return this.interfaceRawResult;
	}

}

