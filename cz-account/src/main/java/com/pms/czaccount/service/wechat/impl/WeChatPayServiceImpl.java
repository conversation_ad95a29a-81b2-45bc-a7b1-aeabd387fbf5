package com.pms.czaccount.service.wechat.impl;

import com.github.pagehelper.Page;
import com.pms.czaccount.bean.PayAgentInfo;
import com.pms.czaccount.bean.account.AccountHotelBusinessRecord;
import com.pms.czaccount.bean.account.search.AccountHotelBusinessRecordSearch;
import com.pms.czaccount.bean.pay.*;
import com.pms.czaccount.bean.pay.search.SysWechatPaySearch;
import com.pms.czaccount.bean.pay.search.WechatMiniprogramsSearch;
import com.pms.czaccount.bean.search.PayAgentInfoSearch;
import com.pms.czaccount.dao.account.AccountHotelBusinessRecordDao;
import com.pms.czaccount.dao.pay.*;
import com.pms.czaccount.service.common.CommonService;
import com.pms.czaccount.service.shouqianba.ShouQianBaService;
import com.pms.czaccount.service.wechat.WeChatPayService;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.M_CACHE;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.paymsg.PAY_RES;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.AddAccountParam;
import lombok.extern.log4j.Log4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.net.ssl.SSLContext;
import java.io.*;
import java.net.InetAddress;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLDecoder;
import java.security.KeyStore;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Primary
@Log4j
public class WeChatPayServiceImpl extends BaseService implements WeChatPayService {


    @Resource
    private WechatAccountsDao wechatAccountsDao;

    @Resource
    private SysWechatPayDao sysWechatPayDao;

    @Resource
    private PayOrderDao payOrderDao;


    @Resource
    private SysWechatRefundDao sysWechatRefundDao;

    @Resource
    private WechatMiniprogramsDao wechatMiniprogramsDao;

    @Resource
    private PayAgentInfoDao payAgentInfoDao;

    @Resource
    private AccountHotelBusinessRecordDao accountHotelBusinessRecordDao;

    public PayAgentInfo getPayAgentInfo(Integer hid) {
        PayAgentInfoSearch payAgentInfoSearch = new PayAgentInfoSearch();
        payAgentInfoSearch.setHid(hid);

        Page<PayAgentInfo> payAgentInfos = payAgentInfoDao.selectBySearch(payAgentInfoSearch);
        if (!payAgentInfos.isEmpty()) {
            return payAgentInfos.get(0);
        }
        return null;
    }

    public static final String APPID = "wxbc1a4eeef87ecfee";
    public static final String mchId = "**********";
    public static final String apiKey = "62f8894a07a4566ab23a0f95ea34ea35";


    public WechatAccounts getPayAgentWechatAccounts() {
        WechatAccounts wechatAccounts = new WechatAccounts();
        wechatAccounts.setAppId(APPID);
        wechatAccounts.setMchId(mchId);
        wechatAccounts.setNotifyUrl("https://czpms.cn");
        wechatAccounts.setApiKey(apiKey);
        wechatAccounts.setFilePath1("https://hotel-file-**********.cos.ap-shanghai.myqcloud.com/apiclient_cert.p12");
        return wechatAccounts;
    }


    @Resource
    CommonService commonService;

    @Resource
    ShouQianBaService shouQianBaService;

    /**
     * 获取缓存方法的对象
     */
    @Resource(name = "stringRedisTemplate")
    protected StringRedisTemplate stringRedisTemplate;

    @Override
    public Map<String, Object> getWeChatQrcode(Map<String, Object> map) {

        //判断是否是向pms付费
        boolean isPay2PMSSystem = Objects.equals(Boolean.TRUE, map.getOrDefault("isPay2PMSSystem", false));

        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            /*1.获取用户信息*/
            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if (commonService.isSqb(user) && !isPay2PMSSystem) {
                map.put("payway", "3");
                return shouQianBaService.getSQBQrCode(map);
            }

            if (sessionToken.equals("WECHAT")) {
                user.setHid(Integer.parseInt(map.get("hid").toString()));
            }

            if (isPay2PMSSystem) {
                user.setHid(2082);
            }

            WechatAccounts wechatAccounts = wechatAccountsDao.selectByHid(user.getHid());

            PayAgentInfo payAgentInfo = null;
            if (wechatAccounts == null) {
                payAgentInfo = getPayAgentInfo(user.getHid());
                if (payAgentInfo == null || payAgentInfo.getWeiChatPaySubMchId() == null) {
                    throw new Exception(PAY_RES.NO_WECHAT_MSG);
                }
                wechatAccounts = getPayAgentWechatAccounts();
            }

            /*2.生成二维码*/
            String Desc = "czpms";
            if (map.containsKey("Desc")) {
                Desc = URLDecoder.decode(map.get("Desc").toString(), "UTF-8");
            }
            int width = 300;
            if (map.containsKey("width")) {
                width = Integer.parseInt(map.get("width").toString());
            }
            int height = 300;
            if (map.containsKey("height")) {
                width = Integer.parseInt(map.get("height").toString());
            }
            if (!map.containsKey("attach")) {
                map.put("attach", "");
            }
            String ip = InetAddress.getLocalHost().getHostAddress();
            if (!map.containsKey("ip") && map.get("ip") != null) {
                ip = map.get("ip").toString();
            }
            String mainId = HotelUtils.getHIDUUID32("WX", user.getHid());

            SortedMap<Object, Object> json = new TreeMap<Object, Object>();
            if (payAgentInfo != null) {
                json.put("sub_mch_id", payAgentInfo.getWeiChatPaySubMchId());
            }
            json.put("appid", wechatAccounts.getAppId());
            json.put("attach", mainId);
            json.put("mch_id", wechatAccounts.getMchId());
            json.put("body", Desc);
            String nonce_str = TenpayUtil.CreateNoncestr();
            json.put("nonce_str", nonce_str);
            json.put("notify_url", wechatAccounts.getNotifyUrl());
            json.put("out_trade_no", mainId);

            json.put("spbill_create_ip", ip);
            Double money = Double.parseDouble(map.get("money").toString());
            int moneyFen = (int) (money * 100);
            json.put("total_fee", moneyFen);
            json.put("trade_type", "NATIVE");
            json.put("notify_url", "https://czpms.cn/hotel/pay/weChatPayNotify.do");
            json.put("time_expire", yyyyMMddHHmmss.format(new Date(System.currentTimeMillis() + 2 * 60 * 1000)));
            String sign = TenpayUtil.createSign2("UTF-8", json, wechatAccounts.getApiKey());
            json.put("sign", sign);


            String xml = TenpayUtil.creatPay2returnXML(json);
            //sessionToken放入Redis, 回调的时候用
            stringRedisTemplate.opsForValue().set("SESSION_TOKEN:" + mainId, sessionToken, 60L, TimeUnit.MINUTES);

            String result = HttpRequest.sendPost("https://api.mch.weixin.qq.com/pay/unifiedorder", xml);
            Map wxResult = XMLUtil.doXMLParse(result);
            if (wxResult.containsKey("return_code")) {
                if ("SUCCESS".equals(wxResult.get("return_code"))) {
                    resultMap.put(ER.MSG, wxResult.get("code_url"));
                    resultMap.put("mainId", mainId);
                } else {
                    resultMap.put(ER.RES, ER.ERR);
                    resultMap.put(ER.MSG, wxResult.get("return_msg"));
                }
            }

        } catch (Exception e) {

            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());

        }

        return resultMap;
    }


    /**
     * 获取微信支付结果
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> getWeChatPayResult(Map<String, Object> map, WechatAccounts wechatAccounts) {
        boolean isPay2PMSSystem = Objects.equals(Boolean.TRUE, map.getOrDefault("isPay2PMSSystem", false));

        //返回参数
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            //向pms系统支付
            if (isPay2PMSSystem) {
                user.setHid(2082);
            }

            /*1.查询微信订单状态*/
            String appid = wechatAccounts.getAppId();
            String mchId = wechatAccounts.getMchId();
            String nonceStr = TenpayUtil.CreateNoncestr();
            String key = wechatAccounts.getApiKey();

            SortedMap<Object, Object> json1 = new TreeMap<Object, Object>();
            if (map.get("payAgentInfo") != null) {
                // PayAgentInfo payAgentInfo = (PayAgentInfo) map.get("payAgentInfo");
                PayAgentInfo payAgentInfo = (PayAgentInfo) JSONObject.toBean(JSONObject.fromObject(map.get("payAgentInfo")), PayAgentInfo.class);
                json1.put("sub_mch_id", payAgentInfo.getWeiChatPaySubMchId());
            }
            json1.put("appid", appid);
            json1.put("mch_id", mchId);
            json1.put("nonce_str", nonceStr);

            /*先查询当前订单是否存在，不存在插入到数据库*/
            SysWechatPaySearch sysWechatPaySearch = new SysWechatPaySearch();
            sysWechatPaySearch.setHid(user.getHid());

            if (map.containsKey("mainId")) {
                json1.put("out_trade_no", map.get("mainId"));
                sysWechatPaySearch.setMainId(map.get("mainId").toString());
            }

            if (map.containsKey("transactionId")) {
                json1.put("transaction_id", map.get("transactionId"));
                sysWechatPaySearch.setTransactionId(map.get("transactionId").toString());
            }

            List<SysWechatPay> sysWechatPays = sysWechatPayDao.selectBySearch(sysWechatPaySearch);

            if (sysWechatPays.size() > 0) {
                SysWechatPay sysWechatPay = sysWechatPays.get(0);
                if (sysWechatPay.getPayStatus().equals(PAY_RES.STATE_SUCC)) {
                    resultMap.put(ER.MSG, PAY_RES.PAY_SUCC_MSG);
                    resultMap.put("mainId", sysWechatPay.getMainId());
                    resultMap.put("money", sysWechatPay.getMoney());
                    return resultMap;
                }

            }

            String sign = TenpayUtil.createSign2("UTF-8", json1, key);
            json1.put("sign", sign);
            String xml = TenpayUtil.creatPay2returnXML(json1);
            String result = HttpRequest.sendPost("https://api.mch.weixin.qq.com/pay/orderquery", xml);

            //转化结果
            JSONObject wxResult = JSONObject.fromObject(XMLUtil.doXMLParse(result));

            if (wxResult.containsKey("return_code")) {
                if ("SUCCESS".equals(wxResult.get("return_code"))) {
                    if (wxResult.containsKey("result_code") && wxResult.get("result_code").equals("FAIL")) {
                        resultMap.put("message", wxResult.get("err_code_des"));
                        resultMap.put("errcode", wxResult.get("err_code"));
                        resultMap.put(ER.RES, ER.ERR);
                    } else {
                        SysWechatPay wechatPay = new SysWechatPay();
                        wechatPay.setHid(user.getHid());
                        if (map.containsKey("UUID")) {
                            wechatPay.setMachineUuid(map.get("UUID").toString());
                        }
                        wechatPay.setMainId(wxResult.getString("out_trade_no"));
                        wechatPay.setMoney(Double.parseDouble(wxResult.get("total_fee").toString()) / 100);
                        wechatPay.setMoneyFen(wxResult.getInt("total_fee"));
                        if (map.containsKey("source")) {
                            wechatPay.setSource(map.get("source").toString());
                        }
                        if (map.containsKey("desc")) {
                            wechatPay.setOrderDesc(map.get("desc").toString());
                        }
                        wechatPay.setPayStatus(1);
                        if (wxResult.containsKey("is_subscribe")) {
                            wechatPay.setIsSubscribe(wxResult.getString("is_subscribe"));
                        }
                        if (wxResult.containsKey("appid")) {
                            wechatPay.setAppId(wxResult.getString("appid"));
                        }
                        if (wxResult.containsKey("transaction_id")) {
                            wechatPay.setTransactionId(wxResult.getString("transaction_id"));
                        }
                        if (wxResult.containsKey("trade_type")) {
                            wechatPay.setTradeType(wxResult.getString("trade_type"));
                        }
                        if (wxResult.containsKey("mch_id")) {
                            wechatPay.setMchId(wxResult.getString("mch_id"));
                        }
                        if (wxResult.containsKey("time_end")) {

                            long time_end = wxResult.getLong("time_end");
                            Long yearL = time_end / 10000000000L;
                            Long monthL = time_end / 100000000L % 100;
                            Long dayL = time_end / 1000000L % 100;
                            Long hourL = time_end / 10000L % 100;
                            Long minL = time_end / 100L % 100;
                            Long sL = time_end % 100;
                            int year = yearL.intValue();
                            int month = monthL.intValue();
                            int day = dayL.intValue();
                            int hour = hourL.intValue();
                            int min = minL.intValue();
                            int s = sL.intValue();
                            StringBuilder sb = new StringBuilder();
                            sb.append(year);
                            sb.append("-");
                            if (month < 10) {
                                sb.append("0");
                            }
                            sb.append(month);
                            sb.append("-");
                            if (day < 10) {
                                sb.append("0");
                            }
                            sb.append(day);
                            sb.append(" ");
                            if (hour < 10) {
                                sb.append("0");
                            }
                            sb.append(hour);
                            sb.append(":");
                            if (min < 10) {
                                sb.append("0");
                            }
                            sb.append(min);
                            sb.append(":");
                            if (s < 10) {
                                sb.append("0");
                            }
                            sb.append(s);
                            wechatPay.setTimeEnd(HotelUtils.parseStr2Date(sb.toString()));
                        }
                        if (wxResult.containsKey("openid")) {
                            wechatPay.setOpenId(wxResult.getString("openid"));
                        }
                        if (wxResult.containsKey("bank_type")) {
                            wechatPay.setBankType(wxResult.getString("bank_type"));
                        }
                        if (wxResult.containsKey("cash_fee")) {
                            wechatPay.setCashFee(wxResult.getInt("cash_fee"));
                        }
                        if (wxResult.containsKey("total_fee")) {
                            int cashFee = wxResult.getInt("total_fee") - wxResult.getInt("cash_fee");
                            wechatPay.setCashFee(cashFee);
                        }
                        wechatPay.setUpdateTime(new Date());
                        wechatPay.setPayTime(new Date());
                        wechatPay.setRefundMoney(0);


                        Integer insertValue = sysWechatPayDao.saveSysWechatPay(wechatPay);

                        if (insertValue < 1) {
                            throw new Exception(PAY_RES.SAVE_PAY_FAILD);
                        }
                        resultMap.put(ER.MSG, PAY_RES.PAY_SUCC_MSG);
                        resultMap.put("mainId", map.get("mainId"));
                        resultMap.put("money", wxResult.getInt("total_fee") / 100.0);
                    }
                } else {
                    resultMap.put(ER.RES, ER.ERR);
                    resultMap.put(ER.MSG, wxResult.get("err_code_des"));
                    resultMap.put(PAY_RES.ERR_CODE, wxResult.get("err_code"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    /**
     * 关闭微信支付
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> weChatPayClose(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(ER.RES, ER.SUCC);

        try {
            /*1.获取用户信息*/
            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (commonService.isSqb(user)) {
                return shouQianBaService.paySQBClose(param);
            }

            // 如果支付成功，则不允许取消
            Map<String, Object> map = this.handleWeChatPayResult(param);
            if (map.get(ER.RES).toString().equals(ER.SUCC)) {
                return resultMap;
            }

            WechatAccounts wechatAccounts = wechatAccountsDao.selectByHid(user.getHid());

            PayAgentInfo payAgentInfo = null;
            if (wechatAccounts == null) {
                payAgentInfo = getPayAgentInfo(user.getHid());
                if (payAgentInfo == null || payAgentInfo.getWeiChatPaySubMchId() == null) {
                    throw new Exception(PAY_RES.NO_WECHAT_MSG);
                }
                wechatAccounts = getPayAgentWechatAccounts();
            }

            String appid = wechatAccounts.getAppId();
            String mchId = wechatAccounts.getMchId();
            String nonceStr = TenpayUtil.CreateNoncestr();
            String key = wechatAccounts.getApiKey();

            //商户订单号
            String mainId = param.getString("mainId");

            SortedMap<Object, Object> closePay = new TreeMap<Object, Object>();
            if (payAgentInfo != null) {
                closePay.put("sub_mch_id", payAgentInfo.getWeiChatPaySubMchId());
            }
            closePay.put("appid", appid);
            closePay.put("mch_id", mchId);
            closePay.put("nonce_str", nonceStr);
            closePay.put("out_trade_no", mainId);

            String sign = TenpayUtil.createSign2("UTF-8", closePay, key);
            closePay.put("sign", sign);
            String xml = TenpayUtil.creatPay2returnXML(closePay);
            String result = HttpRequest.sendPost("https://api.mch.weixin.qq.com/pay/closeorder", xml);

            //转化结果
            JSONObject wxResult = JSONObject.fromObject(XMLUtil.doXMLParse(result));

            if (!wxResult.getString("result_code").equals("SUCCESS")) {

                xml = new String(xml.getBytes(), "UTF-8");
                KeyStore keyStore = KeyStore.getInstance("PKCS12");
                URL url = new URL(wechatAccounts.getFilePath1());
                URLConnection connection = url.openConnection();
                InputStream ins = connection.getInputStream();

                //对退款整数进行验证
                CloseableHttpResponse response = null;
                CloseableHttpClient httpclient = null;
                keyStore.load(ins, mchId.toCharArray());
                SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore, mchId.toCharArray()).build();
                SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslcontext, new String[]{"TLSv1"}, null, SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
                httpclient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
                HttpPost httppost = new HttpPost("https://api.mch.weixin.qq.com/secapi/pay/reverse");
                StringEntity stringEntity = new StringEntity(xml);
                stringEntity.setContentType("application/x-www-form-urlencoded");
                httppost.setEntity(stringEntity);
                response = httpclient.execute(httppost);
                HttpEntity entity = response.getEntity();

                if (entity != null) {
                    BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent()));
                    String text = "";
                    String results = "";
                    while ((text = bufferedReader.readLine()) != null) {
                        results += text;
                    }
                    results = new String(results.getBytes(), "UTF-8");
                    //转化结果
                    JSONObject wxResultCard = JSONObject.fromObject(XMLUtil.doXMLParse(results));

                    if (!wxResultCard.getString("result_code").equals("SUCCESS")) {
                        resultMap.put(ER.RES, ER.ERR);
                        resultMap.put(ER.MSG, wxResultCard.get("result_msg"));
                    }
                } else {
                    resultMap.put(ER.RES, ER.ERR);
                    resultMap.put(ER.MSG, "退款失败");
                }


            }

        } catch (Exception e) {

            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());

        }

        return resultMap;
    }

    /**
     * 对微信返回结果做处理
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> handleWeChatPayResult(Map<String, Object> map) {
        boolean isPay2PMSSystem = Objects.equals(Boolean.TRUE, map.getOrDefault("isPay2PMSSystem", false));

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (commonService.isSqb(user) && !isPay2PMSSystem) {
                return shouQianBaService.handleSQBResult(map);
            }

            //向pms系统支付
            if (isPay2PMSSystem) {
                user.setHid(2082);
            }

            /*1.查询酒店微信信息*/
            WechatAccounts wechatAccounts = wechatAccountsDao.selectByHid(user.getHid());

            PayAgentInfo payAgentInfo = payAgentInfoDao.selectById(user.getHid());

            if (wechatAccounts == null && payAgentInfo != null) {
                wechatAccounts = getPayAgentWechatAccounts();
                map.put("payAgentInfo", payAgentInfo);
            } else if (wechatAccounts != null) {

            } else {
                throw new Exception(PAY_RES.NO_WECHAT_MSG);
            }

            resultMap = this.getWeChatPayResult(map, wechatAccounts);

            return resultMap;

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;

    }

    /**
     * 微信退款
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> wechatRefund(Map<String, Object> map) {

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (commonService.isSqb(user)) {
                return shouQianBaService.SQBRefund(map);
            }

            /*1.查询酒店微信信息级参数的完整性*/
            WechatAccounts wechatAccounts = wechatAccountsDao.selectByHid(user.getHid());

            PayAgentInfo payAgentInfo = payAgentInfoDao.selectById(user.getHid());

            Boolean isSubMch = false;

            if (wechatAccounts == null && payAgentInfo != null) {
                wechatAccounts = getPayAgentWechatAccounts();
                isSubMch = true;
            } else if (wechatAccounts != null) {

            } else {
                throw new Exception(PAY_RES.NO_WECHAT_MSG);
            }
            String mainId = "mainId";
            if (!map.containsKey(mainId)) {
                throw new Exception(PAY_RES.REFUND_MAINID_NOT_NULL);
            }

            String refundMoney = "refundMoney";
            if (!map.containsKey(refundMoney)) {
                throw new Exception(PAY_RES.REFUND_MONEY_NOT_NULL);
            }
            String smainId = map.get(mainId).toString();
            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            Object hotelWechatPay = userCahe.get("hotelWechatPay", smainId);
            if (hotelWechatPay != null) {
                int i = Integer.parseInt(hotelWechatPay.toString());
                if (i > 0) {
                    throw new Exception("以退过款不允许退款，退款金额：" + i / 100.0);
                }
            }

            /*2.根据mainId查询支付信息，并验证退款金额是否合法*/
            SysWechatPaySearch paySearch = new SysWechatPaySearch();
            paySearch.setMainId(map.get(mainId).toString());
            paySearch.setHid(user.getHid());
            List<SysWechatPay> sysWechatPays = sysWechatPayDao.selectBySearch(paySearch);
            if (sysWechatPays.size() < 1) {
                throw new Exception(PAY_RES.REFUND_MAINID_NOT_NULL);
            }

            //支付账单信息
            SysWechatPay sysWechatPay = sysWechatPays.get(0);

            //退款金额
            int refund = Integer.parseInt(map.get(refundMoney).toString());

            if (sysWechatPay.getRefundMoney() == null) {
                sysWechatPay.setRefundMoney(0);
            }

            //已经退款的不能继续退款
            boolean validateRefund = refund < 0 || sysWechatPay.getRefundMoney() > 0;

            if (validateRefund) {
                throw new Exception(PAY_RES.HAS_REFUND);
            }

            /*3.退款*/
            String refundMainId = HotelUtils.getHIDUUID32("RWX", user.getHid());

            String appId = wechatAccounts.getAppId();
            String mchId = wechatAccounts.getMchId();
            String nonceStr = TenpayUtil.CreateNoncestr();
            String key = wechatAccounts.getApiKey();
            SortedMap<Object, Object> json1 = new TreeMap<Object, Object>();
            json1.put("appid", appId);
            json1.put("mch_id", mchId);
            if (isSubMch) {
                json1.put("sub_mch_id", payAgentInfo.getWeiChatPaySubMchId());
            }
            json1.put("nonce_str", nonceStr);
            json1.put("out_trade_no", map.get(mainId));
            json1.put("out_refund_no", refundMainId);
            json1.put("total_fee", sysWechatPay.getMoneyFen());
            json1.put("refund_fee", refund);
            json1.put("op_user_id", mchId);
            if (map.containsKey("refund_account")) {
                System.out.println("微信可用余额退款");
                json1.put("refund_account", "REFUND_SOURCE_RECHARGE_FUNDS");
            }

            String sign = TenpayUtil.createSign2("UTF-8", json1, key);

            json1.put("sign", sign);
            String xml = TenpayUtil.creatRefundReturnXML(json1);
            xml = new String(xml.getBytes(), "UTF-8");
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            URL url = new URL(wechatAccounts.getFilePath1());
            URLConnection connection = url.openConnection();
            InputStream ins = connection.getInputStream();

            //对退款整数进行验证
            CloseableHttpResponse response = null;
            CloseableHttpClient httpclient = null;
            keyStore.load(ins, mchId.toCharArray());
            SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore, mchId.toCharArray()).build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslcontext, new String[]{"TLSv1"}, null, SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
            httpclient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
            HttpPost httppost = new HttpPost("https://api.mch.weixin.qq.com/secapi/pay/refund");
            StringEntity stringEntity = new StringEntity(xml);
            stringEntity.setContentType("application/x-www-form-urlencoded");
            httppost.setEntity(stringEntity);
            response = httpclient.execute(httppost);
            HttpEntity entity = response.getEntity();

            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent()));
                String text = "";
                String result = "";
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
                result = new String(result.getBytes(), "UTF-8");

                System.out.println("退款结果result:" + result);

                JSONObject map1 = JSONObject.fromObject(XMLUtil.doXMLParse(result));
                if (map1.containsKey("return_code")) {
                    if ("SUCCESS".equals(map1.get("return_code")) && "SUCCESS".equals(map1.get("result_code"))) {
                        //返回成功 记录表退款信息
                        SysWechatRefund sysWechatRefund = new SysWechatRefund();
                        sysWechatRefund.setHid(user.getHid());
                        if (map.containsKey("UUID")) {
                            sysWechatRefund.setUuid(map.get("UUID").toString());
                        }

                        sysWechatRefund.setAppId(appId);
                        sysWechatRefund.setMchId(mchId);
                        sysWechatRefund.setPayMainId(map.get(mainId).toString());
                        sysWechatRefund.setMainId(refundMainId);
                        sysWechatRefund.setUpdateTime(new Date());
                        if (map1.containsKey("refund_id")) {
                            sysWechatRefund.setRefundId(map1.getString("refund_id"));
                        }
                        if (map1.containsKey("total_fee")) {
                            sysWechatRefund.setTotalFee(map1.getInt("total_fee"));
                        }
                        if (map1.containsKey("refund_fee")) {
                            sysWechatRefund.setRefundFee(map1.getInt("refund_fee"));
                        }
                        Date nowDate = new Date();
                        sysWechatPay.setUpdateTime(nowDate);
                        if (map.containsKey("source")) {
                            sysWechatPay.setSource(map.get("source").toString());
                        }

                        Integer refundId = sysWechatRefundDao.saveSysWechatRefund(sysWechatRefund);

                        /*4.修改退款信息*/
                        SysWechatPay updatePay = new SysWechatPay();
                        updatePay.setId(sysWechatPay.getId());
                        updatePay.setUpdateTime(nowDate);
                        updatePay.setRefundTime(nowDate);
                        updatePay.setRefundMoney(map1.getInt("refund_fee") + sysWechatPay.getRefundMoney());
                        if (map1.getInt("total_fee") > updatePay.getRefundMoney()) {
                            //退款一部分
                            updatePay.setPayStatus(2);
                        } else {
                            updatePay.setPayStatus(-1);
                        }

                        sysWechatPayDao.editSysWechatPay(updatePay);
                        resultMap.put(ER.MSG, PAY_RES.REFUND_SUCC);
                        resultMap.put("refundId", refundId);
                        userCahe.put("hotelWechatPay", smainId, map1.get("refund_fee"));
                    } else {
                        resultMap.put(ER.RES, ER.ERR);
                        if (map1.containsKey("err_code")) {
                            resultMap.put(PAY_RES.ERR_CODE, map1.get("err_code"));
                        } else if (map1.getString("err_code_des").equals("交易未结算资金不足，请使用可用余额退款")) {
                            resultMap.put(ER.MSG, PAY_RES.WX_NO_ENOUGH_MONEY);
                        } else {
                            resultMap.put(ER.MSG, map1.get("err_code_des"));
                        }
                    }
                }
            }
            EntityUtils.consume(entity);

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;

    }

    @Override
    public Map<String, Object> wechatMinigroRefund(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*1.查询酒店微信信息级参数的完整性*/
            WechatMiniprogramsSearch wechatMiniprogramsSearch = new WechatMiniprogramsSearch();
            wechatMiniprogramsSearch.setHotelGroupId(user.getHotelGroupId());
            WechatMiniprograms wechatMiniprograms = wechatMiniprogramsDao.selectBySearch(wechatMiniprogramsSearch).get(0);

            if (wechatMiniprograms == null) {
                throw new Exception(PAY_RES.NO_WECHAT_MSG);
            }
            String mainId = "mainId";
            if (!map.containsKey(mainId)) {
                throw new Exception(PAY_RES.REFUND_MAINID_NOT_NULL);
            }
            String refundMoney = "refundMoney";
            if (!map.containsKey(refundMoney)) {
                throw new Exception(PAY_RES.REFUND_MONEY_NOT_NULL);
            }

            /*2.根据mainId查询支付信息，并验证退款金额是否合法*/
            SysWechatPaySearch paySearch = new SysWechatPaySearch();
            paySearch.setMainId(map.get(mainId).toString());
            paySearch.setHid(user.getHid());
            List<SysWechatPay> sysWechatPays = sysWechatPayDao.selectBySearch(paySearch);
            if (sysWechatPays.size() < 1) {
                throw new Exception(PAY_RES.REFUND_MAINID_NOT_NULL);
            }

            //支付账单信息
            SysWechatPay sysWechatPay = sysWechatPays.get(0);

            //退款金额
            int refund = Integer.parseInt(map.get(refundMoney).toString());

            if (sysWechatPay.getRefundMoney() == null) {
                sysWechatPay.setRefundMoney(0);
            }

            //已经退款的不能继续退款
            boolean validateRefund = refund < 0 || sysWechatPay.getRefundMoney() > 0;

            if (validateRefund) {
                throw new Exception(PAY_RES.HAS_REFUND);
            }

            /*3.退款*/
            String refundMainId = HotelUtils.getHIDUUID32("RWX", user.getHid());

            String appId = wechatMiniprograms.getAppid();
            String mchId = wechatMiniprograms.getMchId();
            String nonceStr = TenpayUtil.CreateNoncestr();
            String key = wechatMiniprograms.getApiKey();
            SortedMap<Object, Object> json1 = new TreeMap<Object, Object>();
            json1.put("appid", appId);
            json1.put("mch_id", mchId);
            // 服务商  是
            int hid = user.getHid();
            PayAgentInfo payAgentInfo = payAgentInfoDao.selectById(hid);
            if (payAgentInfo != null && payAgentInfo.getWeiChatPaySubMchId() != null && payAgentInfo.getWeiChatPaySubMchId().length() > 5) {
                appId = WeChatPayServiceImpl.APPID;
                mchId = WeChatPayServiceImpl.mchId;
                json1.put("mch_id", WeChatPayServiceImpl.mchId);
                json1.put("appid", WeChatPayServiceImpl.APPID);
                key = WeChatPayServiceImpl.apiKey;
                json1.put("sub_mch_id", payAgentInfo.getWeiChatPaySubMchId());
                //json1.put("sub_appid", wechatMiniprograms.getAppid());
            }
            json1.put("nonce_str", nonceStr);
            json1.put("out_trade_no", map.get(mainId));
            json1.put("out_refund_no", refundMainId);
            json1.put("total_fee", sysWechatPay.getMoneyFen());
            json1.put("refund_fee", refund);
            json1.put("op_user_id", mchId);
            if (map.containsKey("refund_account")) {
                System.out.println("微信可用余额退款");
                json1.put("refund_account", "REFUND_SOURCE_RECHARGE_FUNDS");
            }

            String sign = TenpayUtil.createSign2("UTF-8", json1, key);

            json1.put("sign", sign);
            String xml = TenpayUtil.creatRefundReturnXML(json1);
            xml = new String(xml.getBytes(), "UTF-8");
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            URL url = new URL(wechatMiniprograms.getFilePath1());
            URLConnection connection = url.openConnection();
            InputStream ins = connection.getInputStream();

            //对退款整数进行验证
            CloseableHttpResponse response = null;
            CloseableHttpClient httpclient = null;
            keyStore.load(ins, mchId.toCharArray());
            SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore, mchId.toCharArray()).build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslcontext, new String[]{"TLSv1"}, null, SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
            httpclient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
            HttpPost httppost = new HttpPost("https://api.mch.weixin.qq.com/secapi/pay/refund");
            StringEntity stringEntity = new StringEntity(xml);
            stringEntity.setContentType("application/x-www-form-urlencoded");
            httppost.setEntity(stringEntity);
            response = httpclient.execute(httppost);
            HttpEntity entity = response.getEntity();

            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent()));
                String text = "";
                String result = "";
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
                result = new String(result.getBytes(), "UTF-8");

                System.out.println("退款结果result:" + result);

                JSONObject map1 = JSONObject.fromObject(XMLUtil.doXMLParse(result));
                if (map1.containsKey("return_code")) {
                    if ("SUCCESS".equals(map1.get("return_code")) && "SUCCESS".equals(map1.get("result_code"))) {
                        //返回成功 记录表退款信息
                        SysWechatRefund sysWechatRefund = new SysWechatRefund();
                        sysWechatRefund.setHid(user.getHid());
                        if (map.containsKey("UUID")) {
                            sysWechatRefund.setUuid(map.get("UUID").toString());
                        }
                        sysWechatRefund.setAppId(appId);
                        sysWechatRefund.setMchId(mchId);
                        sysWechatRefund.setPayMainId(map.get(mainId).toString());
                        sysWechatRefund.setMainId(refundMainId);
                        sysWechatRefund.setUpdateTime(new Date());

                        if (map1.containsKey("refund_id")) {
                            sysWechatRefund.setRefundId(map1.getString("refund_id"));
                        }
                        if (map1.containsKey("total_fee")) {
                            sysWechatRefund.setTotalFee(map1.getInt("total_fee"));
                        }
                        if (map1.containsKey("refund_fee")) {
                            sysWechatRefund.setRefundFee(map1.getInt("refund_fee"));
                        }
                        Date nowDate = new Date();
                        sysWechatPay.setUpdateTime(nowDate);
                        if (map.containsKey("source")) {
                            sysWechatPay.setSource(map.get("source").toString());
                        }

                        Integer refundId = sysWechatRefundDao.saveSysWechatRefund(sysWechatRefund);

                        /*4.修改退款信息*/
                        SysWechatPay updatePay = new SysWechatPay();
                        updatePay.setId(sysWechatPay.getId());
                        updatePay.setUpdateTime(nowDate);
                        updatePay.setRefundTime(nowDate);
                        updatePay.setRefundMoney(map1.getInt("refund_fee") + sysWechatPay.getRefundMoney());
                        if (map1.getInt("total_fee") > updatePay.getRefundMoney()) {
                            //退款一部分
                            updatePay.setPayStatus(2);
                        } else {
                            updatePay.setPayStatus(-1);
                        }

                        sysWechatPayDao.editSysWechatPay(updatePay);
                        resultMap.put(ER.MSG, PAY_RES.REFUND_SUCC);
                        resultMap.put("refundId", refundId);

                    } else {
                        resultMap.put(ER.RES, ER.ERR);
                        if (map1.containsKey("err_code")) {
                            resultMap.put(PAY_RES.ERR_CODE, map1.get("err_code"));
                        } else if (map1.getString("err_code_des").equals("交易未结算资金不足，请使用可用余额退款")) {
                            resultMap.put(ER.MSG, PAY_RES.WX_NO_ENOUGH_MONEY);
                        } else {
                            resultMap.put(ER.MSG, map1.get("err_code_des"));
                        }
                    }
                }
            }
            EntityUtils.consume(entity);

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    /**
     * 获取微信支付信息
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> getWeChatPayMsg(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Integer hid = user.getHid();
            if (user.getSessionType().equals(M_CACHE.MANAGER_TYPE)) {
                hid = Integer.parseInt(map.get("hid").toString());
            }
            /*1.查询酒店微信信息*/
            WechatAccounts wechatAccounts = wechatAccountsDao.selectByHid(hid);
            if (wechatAccounts == null) {
                throw new Exception(PAY_RES.NO_WECHAT_MSG);
            }

            resultMap.put("data", wechatAccounts);

            return resultMap;

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    /**
     * 修改或者保存微信支付信息
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> saveOrUpdateWeChatPayMsg(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Oprecord oprecord = new Oprecord(user);

            Integer hid = user.getHid();

            if (user.getSessionType().equals(2)) {
                hid = Integer.parseInt(map.get("hid").toString());
            }

            /*1.查询酒店微信信息*/
            WechatAccounts wechatAccounts = null;

            try {
                wechatAccounts = wechatAccountsDao.selectByHid(hid);
            } catch (Exception e) {

            }

            StringBuilder sb = new StringBuilder();
            sb.append("微信支付信息");

            //修改
            int type = 1;

            if (wechatAccounts == null) {
                //保存
                type = 2;
                wechatAccounts = new WechatAccounts();
                wechatAccounts.setHid(hid.toString());

            }

            if (type == 1) {
                sb.append("修改");
            } else {
                sb.append("添加");
            }

            JSONObject weChatPayMsg = JSONObject.fromObject(map.get("weChatPayMsg"));

            if (weChatPayMsg.get("appId") != null && !"".equals(weChatPayMsg.getString("appId"))) {
                sb.append(",appId由:");
                sb.append(wechatAccounts.getAppId());
                sb.append("变为:");
                sb.append(weChatPayMsg.getString("appId"));
                wechatAccounts.setAppId(weChatPayMsg.getString("appId"));
            }

            if (weChatPayMsg.get("hotelOpenId") != null && !"".equals(weChatPayMsg.getString("hotelOpenId"))) {
                sb.append(",酒店注册原始ID由:");
                sb.append(wechatAccounts.getHotelOpenId());
                sb.append("变为:");
                sb.append(weChatPayMsg.getString("hotelOpenId"));
                wechatAccounts.setHotelOpenId(weChatPayMsg.getString("hotelOpenId"));
            }

            if (weChatPayMsg.get("hotelWechatAccount") != null && !"".equals(weChatPayMsg.getString("hotelWechatAccount"))) {
                sb.append(",微信公众号:");
                sb.append(wechatAccounts.getHotelWechatAccount());
                sb.append("变为:");
                sb.append(weChatPayMsg.getString("hotelWechatAccount"));
                wechatAccounts.setHotelWechatAccount(weChatPayMsg.getString("hotelWechatAccount"));
            }

            if (weChatPayMsg.get("hotelWechatPassword") != null && !"".equals(weChatPayMsg.getString("hotelWechatPassword"))) {
                sb.append(",公众号密码由:");
                sb.append(wechatAccounts.getHotelWechatPassword());
                sb.append("变为:");
                sb.append(weChatPayMsg.getString("hotelWechatPassword"));
                wechatAccounts.setHotelWechatPassword(weChatPayMsg.getString("hotelWechatPassword"));
            }

            if (weChatPayMsg.get("appSecret") != null && !"".equals(weChatPayMsg.getString("appSecret"))) {
                sb.append(",AppSecret(应用密钥)由:");
                sb.append(wechatAccounts.getAppSecret());
                sb.append("变为:");
                sb.append(weChatPayMsg.getString("appSecret"));
                wechatAccounts.setAppSecret(weChatPayMsg.getString("appSecret"));
            }

            if (weChatPayMsg.get("mchId") != null && !"".equals(weChatPayMsg.getString("mchId"))) {
                sb.append(",微信支付商户号由:");
                sb.append(wechatAccounts.getMchId());
                sb.append("变为:");
                sb.append(weChatPayMsg.getString("mchId"));
                wechatAccounts.setMchId(weChatPayMsg.getString("mchId"));
            }

            if (weChatPayMsg.get("apiKey") != null && !"".equals(weChatPayMsg.getString("apiKey"))) {
                sb.append(",微信支付API密钥由:");
                sb.append(wechatAccounts.getApiKey());
                sb.append("变为:");
                sb.append(weChatPayMsg.getString("apiKey"));
                wechatAccounts.setApiKey(weChatPayMsg.getString("apiKey"));
            }

            if (type == 1) {
                wechatAccountsDao.editWechatAccounts(wechatAccounts);
            } else {

                wechatAccountsDao.saveWechatAccounts(wechatAccounts);
            }

            oprecord.setDescription(sb.toString());
            this.addOprecords(oprecord);

            return resultMap;

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    private static final  SimpleDateFormat yyyyMMddHHmmss = new SimpleDateFormat("yyyyMMddHHmmss");
    /**
     * 微信反扫
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> weChatMicropay(JSONObject map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (commonService.isSqb(user)) {
                return shouQianBaService.SQBMicropay(map);
            }


            /*1.查询酒店微信信息*/
            WechatAccounts wechatAccounts = wechatAccountsDao.selectByHid(user.getHid());

            PayAgentInfo payAgentInfo = payAgentInfoDao.selectById(user.getHid());

            Boolean isSubMch = false;

            if (wechatAccounts == null && payAgentInfo != null) {
                wechatAccounts = getPayAgentWechatAccounts();
                isSubMch = true;
            } else if (wechatAccounts != null) {

            } else {
                throw new Exception(PAY_RES.NO_WECHAT_MSG);
            }

            String appid = wechatAccounts.getAppId();
            String mch_id = wechatAccounts.getMchId();
            String nonce_str = TenpayUtil.CreateNoncestr();
            String key = wechatAccounts.getApiKey();
            String mainId = HotelUtils.getHIDUUID32("WX", user.getHid());
            String Desc = "WeiXinPay";
            if (map.containsKey("Desc")) {
                Desc = URLDecoder.decode(map.get("Desc").toString(), "UTF-8");
            }

            if (!map.containsKey("attach")) {
                map.put("attach", "");
            }
            SortedMap<Object, Object> json = new TreeMap<Object, Object>();
            json.put("appid", appid);
            json.put("attach", mainId);
            json.put("mch_id", mch_id);
            if (isSubMch) {
                json.put("sub_mch_id", payAgentInfo.getWeiChatPaySubMchId());
            }
            json.put("body", Desc);
            json.put("nonce_str", nonce_str);
            json.put("out_trade_no", mainId);
            json.put("spbill_create_ip", user.getIp());
            json.put("auth_code", map.get("scanCode"));
            json.put("total_fee", (int) (Double.parseDouble(map.get("money").toString()) * 100));
            json.put("time_expire", yyyyMMddHHmmss.format(new Date(System.currentTimeMillis() + 2 * 60 * 1000)));

            String sign = TenpayUtil.createSign2("UTF-8", json, key);
            json.put("sign", sign);
            String xml = TenpayUtil.creatPay2returnXML(json);
            System.out.println(xml);
            //sessionToken放入Redis, 回调的时候用
            String addAccountData = "";
            stringRedisTemplate.opsForValue().set("SESSION_TOKEN:" + mainId, sessionToken, 60L, TimeUnit.MINUTES);
            if (map.containsKey("addAccountData")) {
                addAccountData = map.getString("addAccountData");
            }

            //创建订单
            PayOrder payOrder = new PayOrder();
            payOrder.mainId = mainId;
            payOrder.status = 1;
            payOrder.account = GsonUtil.bean2Json(wechatAccounts);
            payOrder.payType = 1;
            payOrder.addAccountParam = addAccountData;
            payOrder.sessionToken = sessionToken;
            payOrder.hid = user.getHid();
            payOrder.payParam = GsonUtil.bean2Json(map);

            if (payOrderDao.savePayOrder(payOrder) < 1) {
                throw new Exception("支付失败");
            }

            String result = HttpRequest.sendPost("https://api.mch.weixin.qq.com/pay/micropay", xml);
            System.out.println(result);
            Map map0 = XMLUtil.doXMLParse(result);
            System.out.println(map0);
            //转化结果
            JSONObject wxResult = JSONObject.fromObject(XMLUtil.doXMLParse(result));

            if (wxResult.containsKey("return_code")) {
                if ("SUCCESS".equals(wxResult.get("return_code"))) {
                    if (wxResult.containsKey("result_code") && wxResult.get("result_code").equals("FAIL")) {
                        resultMap.put("message", wxResult.get("err_code_des"));
                        resultMap.put("errcode", wxResult.get("err_code"));
                        resultMap.put(ER.RES, ER.ERR);
                    } else {
                        SysWechatPay wechatPay = new SysWechatPay();
                        wechatPay.setHid(user.getHid());
                        if (map.containsKey("UUID")) {
                            wechatPay.setMachineUuid(map.get("UUID").toString());
                        }
                        wechatPay.setMainId(wxResult.getString("out_trade_no"));
                        wechatPay.setMoney(Double.parseDouble(wxResult.get("total_fee").toString()) / 100);
                        wechatPay.setMoneyFen(wxResult.getInt("total_fee"));
                        if (map.containsKey("source")) {
                            wechatPay.setSource(map.get("source").toString());
                        }
                        if (map.containsKey("desc")) {
                            wechatPay.setOrderDesc(map.get("desc").toString());
                        }
                        wechatPay.setPayStatus(1);
                        if (wxResult.containsKey("is_subscribe")) {
                            wechatPay.setIsSubscribe(wxResult.getString("is_subscribe"));
                        }
                        if (wxResult.containsKey("appid")) {
                            wechatPay.setAppId(wxResult.getString("appid"));
                        }
                        if (wxResult.containsKey("transaction_id")) {
                            wechatPay.setTransactionId(wxResult.getString("transaction_id"));
                        }
                        if (wxResult.containsKey("trade_type")) {
                            wechatPay.setTradeType(wxResult.getString("trade_type"));
                        }
                        if (wxResult.containsKey("mch_id")) {
                            wechatPay.setMchId(wxResult.getString("mch_id"));
                        }
                        if (wxResult.containsKey("time_end")) {

                            long time_end = wxResult.getLong("time_end");
                            Long yearL = time_end / 10000000000L;
                            Long monthL = time_end / 100000000L % 100;
                            Long dayL = time_end / 1000000L % 100;
                            Long hourL = time_end / 10000L % 100;
                            Long minL = time_end / 100L % 100;
                            Long sL = time_end % 100;
                            int year = yearL.intValue();
                            int month = monthL.intValue();
                            int day = dayL.intValue();
                            int hour = hourL.intValue();
                            int min = minL.intValue();
                            int s = sL.intValue();
                            StringBuilder sb = new StringBuilder();
                            sb.append(year);
                            sb.append("-");
                            if (month < 10) {
                                sb.append("0");
                            }
                            sb.append(month);
                            sb.append("-");
                            if (day < 10) {
                                sb.append("0");
                            }
                            sb.append(day);
                            sb.append(" ");
                            if (hour < 10) {
                                sb.append("0");
                            }
                            sb.append(hour);
                            sb.append(":");
                            if (min < 10) {
                                sb.append("0");
                            }
                            sb.append(min);
                            sb.append(":");
                            if (s < 10) {
                                sb.append("0");
                            }
                            sb.append(s);
                            wechatPay.setTimeEnd(HotelUtils.parseStr2Date(sb.toString()));
                        }
                        if (wxResult.containsKey("openid")) {
                            wechatPay.setOpenId(wxResult.getString("openid"));
                        }
                        if (wxResult.containsKey("bank_type")) {
                            wechatPay.setBankType(wxResult.getString("bank_type"));
                        }
                        if (wxResult.containsKey("cash_fee")) {
                            wechatPay.setCashFee(wxResult.getInt("cash_fee"));
                        }
                        wechatPay.setUpdateTime(new Date());
                        wechatPay.setPayTime(new Date());
                        wechatPay.setRefundMoney(0);

                        Integer insertValue = sysWechatPayDao.saveSysWechatPay(wechatPay);

                        if (insertValue < 1) {
                            throw new Exception(PAY_RES.SAVE_PAY_FAILD);
                        }
                        resultMap.put(ER.MSG, PAY_RES.PAY_SUCC_MSG);
                        resultMap.put("mainId", map.get("mainId"));
                        resultMap.put("money", wxResult.getInt("cash_fee") / 100.0);
                    }
                } else {
                    resultMap.put(ER.RES, ER.ERR);
                    resultMap.put(ER.MSG, wxResult.get("err_code_des"));
                    resultMap.put(PAY_RES.ERR_CODE, wxResult.get("err_code"));
                }
            }
            resultMap.put("mainId", mainId);
            return resultMap;

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    /**
     * 保存微信证书
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> addWxCert(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {


            String fileStr = URLDecoder.decode(param.getString("file"), "utf-8");
            byte[] fileBytes = fileStr.getBytes();

            OutputStream os = new FileOutputStream("E:\\test.docx");
            os.write(fileBytes);
            os.flush();
            os.close();
            return resultMap;

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    /**
     * 发送微信红包
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> senRedPack(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*1.查询酒店微信信息级参数的完整性*/
            int redPackHid = param.getInt("redPackHid");
            WechatAccounts wechatAccounts = wechatAccountsDao.selectByHid(redPackHid);
            if (wechatAccounts == null) {
                throw new Exception(PAY_RES.NO_WECHAT_MSG);
            }

            //openid
            String openId = param.getString("openId");
            if (param.get("openId") == null || param.get("openId").equals("")) {
                throw new Exception("openId不能为空");
            }

            //红包金额
            Integer money = param.getInt("money");
            if (param.get("money") == null || param.get("money").equals("")) {
                throw new Exception("发送金额 money 不能为空");
            }

            //红包数量
            int num = 1;
            if (param.get("num") != null && !param.get("num").equals("")) {
                num = param.getInt("num");
            }

            //红包祝福语
            String wishing = "May you be happy and prosperous";
            if (param.get("wishing") != null && !param.get("wishing").equals("")) {
                wishing = param.getString("wishing");
            }

            //活动名称
            String actName = "checkIn redpack";
            if (param.get("actName") != null && !param.get("actName").equals("")) {
                actName = param.getString("actName");
            }

            //使用场景
            String sceneId = "PRODUCT_2";

            if (param.get("sceneId") != null && !param.get("sceneId").equals("")) {
                sceneId = param.getString("sceneId");
            }

            String remark = "恭喜发财";
            if (param.get("remark") != null && !param.get("remark").equals("")) {
                remark = param.getString("remark");
            }

            String nonce_str = TenpayUtil.CreateNoncestr();
            String appid = wechatAccounts.getAppId();
            String mchId = wechatAccounts.getMchId();
            String key = wechatAccounts.getApiKey();
            String mainId = param.getString("mainId");

            SortedMap<Object, Object> json = new TreeMap<Object, Object>();
            json.put("nonce_str", nonce_str);
            json.put("mch_billno", mainId);
            json.put("mch_id", wechatAccounts.getMchId());
            json.put("wxappid", appid);
            json.put("total_amount", money);

            String sendName = "liming red pack";
            if (param.get("sendName") != null) {
                sendName = param.getString("sendName");
            }
            json.put("send_name", sendName);
            json.put("re_openid", openId);
            json.put("total_amount", money);
            json.put("total_num", num);
            json.put("wishing", wishing);
            json.put("client_ip", param.get("IP"));
            json.put("act_name", actName);
            json.put("scene_id", sceneId);

            String sign = TenpayUtil.createSign2("UTF-8", json, key);
            json.put("sign", sign);
            String xml = TenpayUtil.creatJSONReturnXML(json);
            System.out.println(xml);
            xml = new String(xml.getBytes(), "UTF-8");
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            URL url = new URL(wechatAccounts.getFilePath1());
            URLConnection connection = url.openConnection();
            InputStream ins = connection.getInputStream();

            //对退款整数进行验证
            CloseableHttpResponse response = null;
            CloseableHttpClient httpclient = null;
            keyStore.load(ins, mchId.toCharArray());
            SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore, mchId.toCharArray()).build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslcontext, new String[]{"TLSv1"}, null, SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
            httpclient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
            HttpPost httppost = new HttpPost("https://api.mch.weixin.qq.com/mmpaymkttransfers/sendredpack");
            StringEntity stringEntity = new StringEntity(xml);
            stringEntity.setContentType("application/x-www-form-urlencoded");
            httppost.setEntity(stringEntity);
            response = httpclient.execute(httppost);
            HttpEntity entity = response.getEntity();

            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent()));
                String text = "";
                String result = "";
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
                result = new String(result.getBytes(), "UTF-8");

                System.out.println("发送结果result:" + result);

                JSONObject map1 = JSONObject.fromObject(XMLUtil.doXMLParse(result));
                if (map1.containsKey("return_code")) {
                    if ("SUCCESS".equals(map1.get("return_code")) && "SUCCESS".equals(map1.get("result_code"))) {

                    } else {
                        throw new Exception(map1.get("return_msg").toString());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    @Override
    public Map<String, Object> searchRedPackMsg(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*1.查询酒店微信信息级参数的完整性*/
            int redPackHid = param.getInt("redPackHid");
            WechatAccounts wechatAccounts = wechatAccountsDao.selectByHid(redPackHid);
            if (wechatAccounts == null) {
                throw new Exception(PAY_RES.NO_WECHAT_MSG);
            }

            String nonce_str = TenpayUtil.CreateNoncestr();
            String appid = wechatAccounts.getAppId();
            String mchId = wechatAccounts.getMchId();
            String key = wechatAccounts.getApiKey();
            String mainId = param.getString("mainId");

            SortedMap<Object, Object> json = new TreeMap<Object, Object>();
            json.put("nonce_str", nonce_str);
            json.put("mch_billno", mainId);
            json.put("mch_id", wechatAccounts.getMchId());
            json.put("appid", appid);
            json.put("client_ip", param.get("IP"));
            json.put("bill_type", "MCHT");

            String sign = TenpayUtil.createSign2("UTF-8", json, key);
            json.put("sign", sign);
            String xml = TenpayUtil.creatJSONReturnXML(json);
            System.out.println(xml);
            xml = new String(xml.getBytes(), "UTF-8");
            KeyStore keyStore = KeyStore.getInstance("PKCS12");
            URL url = new URL(wechatAccounts.getFilePath1());
            URLConnection connection = url.openConnection();
            InputStream ins = connection.getInputStream();

            //对退款整数进行验证
            CloseableHttpResponse response = null;
            CloseableHttpClient httpclient = null;
            keyStore.load(ins, mchId.toCharArray());
            SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(keyStore, mchId.toCharArray()).build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslcontext, new String[]{"TLSv1"}, null, SSLConnectionSocketFactory.BROWSER_COMPATIBLE_HOSTNAME_VERIFIER);
            httpclient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
            HttpPost httppost = new HttpPost("https://api.mch.weixin.qq.com/mmpaymkttransfers/gethbinfo");
            StringEntity stringEntity = new StringEntity(xml);
            stringEntity.setContentType("application/x-www-form-urlencoded");
            httppost.setEntity(stringEntity);
            response = httpclient.execute(httppost);
            HttpEntity entity = response.getEntity();

            if (entity != null) {
                BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(entity.getContent()));
                String text = "";
                String result = "";
                while ((text = bufferedReader.readLine()) != null) {
                    result += text;
                }
                result = new String(result.getBytes(), "UTF-8");

                System.out.println("发送结果result:" + result);

                JSONObject map1 = JSONObject.fromObject(XMLUtil.doXMLParse(result));
                if (map1.containsKey("return_code")) {
                    if ("SUCCESS".equals(map1.get("return_code")) && "SUCCESS".equals(map1.get("result_code"))) {

                    } else {
                        throw new Exception(map1.get("return_msg").toString());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }


    @Override
    public Map<String, Object> addAccount(String accountData, String sessionToken, String mainId) {
        try {
            //获取数据
            JSONObject dataInfo = JSONObject.fromObject(accountData);
            int businessType = dataInfo.getInt("businessType");
            Map<String, Object> resultMap = new HashMap<>();
            AccountHotelBusinessRecord accountHotelBusinessRecord = new AccountHotelBusinessRecord();
            TbUserSession tbUserSession = this.getTbUserSession(sessionToken);
            accountHotelBusinessRecord.setBusinessType(businessType);
            accountHotelBusinessRecord.setCreateTime(new Date());
            accountHotelBusinessRecord.setCreateUserName(tbUserSession.getUserName());
            accountHotelBusinessRecord.setHid(tbUserSession.getHid());
            accountHotelBusinessRecord.setHotelGroupId(tbUserSession.getHotelGroupId());
            accountHotelBusinessRecord.setCreateUserId(tbUserSession.getUserId());
            accountHotelBusinessRecord.setResultCode(0);

            if (businessType == 1) {
                AddAccountParam param = GsonUtil.json2Bean(accountData, AddAccountParam.class);
                param.setGroupAccount(dataInfo.containsKey("groupAccount") && !dataInfo.getString("groupAccount").equals("") ? dataInfo.getInt("groupAccount") : 0);
                param.setSessionToken(sessionToken);
                param.setThirdAccoutId(mainId);
                resultMap = this.addAccountFunc(param);
                accountHotelBusinessRecord.setAccountThirdId(mainId);
                accountHotelBusinessRecord.setMainId(mainId);
                accountHotelBusinessRecord.setPostData(JSONObject.fromObject(param).toString());
                accountHotelBusinessRecord.setPostFun("/hotel/absaccount/addAccount.do");
            }
            //小商品入账
            else if (businessType == 2) {
                JSONObject postData = JSONObject.fromObject(accountData);
                postData.put(ER.SESSION_TOKEN, sessionToken);
                postData.put("mainId", mainId);
                //需要拿到第三方支付ID
                JSONObject goodsDumbData = postData.getJSONObject("goodsDumbData");
                JSONArray payInfoList = goodsDumbData.getJSONArray("payInfoList");
                for (int i = 0; i < payInfoList.size(); i++) {
                    JSONObject payInfo = payInfoList.getJSONObject(i);
                    payInfo.put("thirdAccoutId", mainId);
                }
                resultMap = this.addOrUpdateGoodsDumbFunc(postData);
                accountHotelBusinessRecord.setAccountThirdId(mainId);
                accountHotelBusinessRecord.setMainId(mainId);
                accountHotelBusinessRecord.setPostData(postData.toString());
                accountHotelBusinessRecord.setPostFun("/hotel/absGoods/addOrUpdateGoodsDumb.do");

            }
            //会员注册入账
            else if (businessType == 3) {
                JSONObject postData = JSONObject.fromObject(accountData);
                postData.put(ER.SESSION_TOKEN, sessionToken);
                JSONObject costCode = postData.getJSONObject("costCode");
                costCode.put("thirdAccoutId", mainId);
                postData.put("mainId", mainId);
                //需要拿到第三方支付ID
                //postData.costCode.thirdAccoutId = addAccountData.thirdAccoutId;
                resultMap = this.registMemberFunc(postData);

                accountHotelBusinessRecord.setAccountThirdId(mainId);
                accountHotelBusinessRecord.setMainId(mainId);
                accountHotelBusinessRecord.setPostData(postData.toString());
                accountHotelBusinessRecord.setPostFun("/hotel/member/registMember.do");
            }
            //会员储值
            else if (businessType == 7) {
                JSONObject postData = JSONObject.fromObject(accountData);
                postData.put(ER.SESSION_TOKEN, sessionToken);
                postData.put("refundPrice", 0);
                postData.put("thirdRefundState", 0);
                postData.put("thirdAccoutId", mainId);
                postData.put("mainId", mainId);
                resultMap = this.memberTranslateFunc(postData);
                accountHotelBusinessRecord.setAccountThirdId(mainId);
                accountHotelBusinessRecord.setMainId(mainId);
                accountHotelBusinessRecord.setPostData(postData.toString());
                accountHotelBusinessRecord.setPostFun("/hotel/member/memberTranslate.do");
            }

            log.info(String.format("addAccount:[%s] result:[%s]", sessionToken, GsonUtil.bean2Json(resultMap)));
            if (resultMap.get(ER.MSG).toString().equals(ER.EXECUTE_SUCCESS) || resultMap.get(ER.CODE).toString().equals("1")) {
                resultMap = new HashMap<>();
                resultMap.put(ER.RES, ER.SUCC);
                resultMap.put("mainId", mainId);
                accountHotelBusinessRecord.setResultCode(1);
                //记录业务数据
                HotelUtils.cachedThreadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            AccountHotelBusinessRecordSearch accountHotelBusinessRecordSearch = new AccountHotelBusinessRecordSearch();
                            accountHotelBusinessRecordSearch.setHid(tbUserSession.getHid());
                            accountHotelBusinessRecordSearch.setMainId(accountHotelBusinessRecord.getMainId());
                            Page<AccountHotelBusinessRecord> accountHotelBusinessRecords = accountHotelBusinessRecordDao.selectBySearch(accountHotelBusinessRecordSearch);
                            if (null == accountHotelBusinessRecords || accountHotelBusinessRecords.size() < 1) {
                                Integer insert = accountHotelBusinessRecordDao.insert(accountHotelBusinessRecord);
                            }

                        } catch (Exception e) {

                        }
                    }
                });
                return resultMap;
            }
            //记录业务数据
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        AccountHotelBusinessRecordSearch accountHotelBusinessRecordSearch = new AccountHotelBusinessRecordSearch();
                        accountHotelBusinessRecordSearch.setHid(tbUserSession.getHid());
                        accountHotelBusinessRecordSearch.setMainId(accountHotelBusinessRecord.getMainId());
                        Page<AccountHotelBusinessRecord> accountHotelBusinessRecords = accountHotelBusinessRecordDao.selectBySearch(accountHotelBusinessRecordSearch);
                        if (null == accountHotelBusinessRecords || accountHotelBusinessRecords.size() < 1) {
                            Integer insert = accountHotelBusinessRecordDao.insert(accountHotelBusinessRecord);
                        }

                    } catch (Exception e) {

                    }
                }
            });
            return resultMap;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static final ThreadLocal<SysWechatPay> sysWechatPayTL = new ThreadLocal<>();

    /**
     * @param payOrder
     * @return 订单是否支付
     * @throws Exception
     */
    @Override
    @Transactional
    public boolean dealPayOrder(PayOrder payOrder) throws Exception {

        //0.查询微信支付订单
        int rt = getPayResultByPayOrder(payOrder);
        if (rt == queryPayOrderFail) {
            return false;
        }

        boolean isPaySuccess = false;
        if (rt == payOrderSaveTransactionDone || rt == paySuccess) {
            payOrder.status = 2;
            isPaySuccess = true;
        }
        //1.加mysql行锁
        PayOrder lock = payOrderDao.lineLock(payOrder.id);
        if (lock.status != 1) {
            throw new Exception("current update");
        }
        //2.修改payOrder 的状态
        Integer insertCount = payOrderDao.updatePayOrderStatus(payOrder.id, payOrder.status);
        if (insertCount < 1) {
            throw new Exception("updatePayOrderStatus fail");
        }

        //3.如果支付成功,并且没有创建付款记录,插入付款记录
        if (rt == paySuccess) {
            insertCount = sysWechatPayDao.saveSysWechatPay(sysWechatPayTL.get());
            if (insertCount < 1) {
                throw new Exception("saveSysWechatPay fail");
            }
        }
        return isPaySuccess;
    }

    public static final int payOrderSaveTransactionDone = 1; // 保存交易记录成功
    public static final int queryPayOrderFail = 2; // 查询失败
    public static final int paySuccess = 3; // 支付成功

    public static final String SUCCESS = "SUCCESS";


    private int getPayResultByPayOrder(PayOrder payOrder) throws Exception {
        String mainId = payOrder.mainId;
        WechatAccounts wechatAccounts = GsonUtil.json2Bean(payOrder.account, WechatAccounts.class);
        /*1.查询微信订单状态*/
        String appid = wechatAccounts.getAppId();
        String mchId = wechatAccounts.getMchId();
        String nonceStr = TenpayUtil.CreateNoncestr();
        String key = wechatAccounts.getApiKey();

        SortedMap<Object, Object> json1 = new TreeMap<Object, Object>();
        Integer hid = payOrder.hid;
        PayAgentInfo payAgentInfo = payAgentInfoDao.selectById(hid);

        if (payAgentInfo != null && payAgentInfo.getWeiChatPaySubMchId() != null) {
            json1.put("sub_mch_id", payAgentInfo.getWeiChatPaySubMchId());
        }
        json1.put("appid", appid);
        json1.put("mch_id", mchId);
        json1.put("nonce_str", nonceStr);

        /*先查询当前订单是否存在，不存在插入到数据库*/
        SysWechatPaySearch sysWechatPaySearch = new SysWechatPaySearch();
        sysWechatPaySearch.setHid(hid);
        json1.put("out_trade_no", mainId);
        sysWechatPaySearch.setMainId(mainId);
        List<SysWechatPay> sysWechatPays = sysWechatPayDao.selectBySearch(sysWechatPaySearch);
        if (sysWechatPays.size() > 0) {
            SysWechatPay sysWechatPay = sysWechatPays.get(0);
            if (sysWechatPay.getPayStatus().equals(PAY_RES.STATE_SUCC)) {
                return payOrderSaveTransactionDone;  //保存支付记录成功了
            }

        }
        String sign = TenpayUtil.createSign2("UTF-8", json1, key);
        json1.put("sign", sign);
        String xml = TenpayUtil.creatPay2returnXML(json1);
        String result = HttpRequest.sendPost("https://api.mch.weixin.qq.com/pay/orderquery", xml);
        JSONObject map = GsonUtil.json2Bean(payOrder.payParam, JSONObject.class);
        //转化结果
        JSONObject wxResult = JSONObject.fromObject(XMLUtil.doXMLParse(result));
        if (Objects.equals(SUCCESS, wxResult.get("return_code"))
                && Objects.equals(SUCCESS, wxResult.get("result_code"))
                && Objects.equals(SUCCESS, wxResult.get("trade_state"))) {
            SysWechatPay wechatPay = new SysWechatPay();
            wechatPay.setHid(hid);
            if (map.containsKey("UUID")) {
                wechatPay.setMachineUuid(map.get("UUID").toString());
            }
            wechatPay.setMainId(wxResult.getString("out_trade_no"));
            wechatPay.setMoney(Double.parseDouble(wxResult.get("total_fee").toString()) / 100);
            wechatPay.setMoneyFen(wxResult.getInt("total_fee"));
            if (map.containsKey("source")) {
                wechatPay.setSource(map.get("source").toString());
            }
            if (map.containsKey("desc")) {
                wechatPay.setOrderDesc(map.get("desc").toString());
            }
            wechatPay.setPayStatus(1);
            if (wxResult.containsKey("is_subscribe")) {
                wechatPay.setIsSubscribe(wxResult.getString("is_subscribe"));
            }
            if (wxResult.containsKey("appid")) {
                wechatPay.setAppId(wxResult.getString("appid"));
            }
            if (wxResult.containsKey("transaction_id")) {
                wechatPay.setTransactionId(wxResult.getString("transaction_id"));
            }
            if (wxResult.containsKey("trade_type")) {
                wechatPay.setTradeType(wxResult.getString("trade_type"));
            }
            if (wxResult.containsKey("mch_id")) {
                wechatPay.setMchId(wxResult.getString("mch_id"));
            }
            if (wxResult.containsKey("time_end")) {
                long time_end = wxResult.getLong("time_end");
                Long yearL = time_end / 10000000000L;
                Long monthL = time_end / 100000000L % 100;
                Long dayL = time_end / 1000000L % 100;
                Long hourL = time_end / 10000L % 100;
                Long minL = time_end / 100L % 100;
                Long sL = time_end % 100;
                int year = yearL.intValue();
                int month = monthL.intValue();
                int day = dayL.intValue();
                int hour = hourL.intValue();
                int min = minL.intValue();
                int s = sL.intValue();
                StringBuilder sb = new StringBuilder();
                sb.append(year);
                sb.append("-");
                if (month < 10) {
                    sb.append("0");
                }
                sb.append(month);
                sb.append("-");
                if (day < 10) {
                    sb.append("0");
                }
                sb.append(day);
                sb.append(" ");
                if (hour < 10) {
                    sb.append("0");
                }
                sb.append(hour);
                sb.append(":");
                if (min < 10) {
                    sb.append("0");
                }
                sb.append(min);
                sb.append(":");
                if (s < 10) {
                    sb.append("0");
                }
                sb.append(s);
                wechatPay.setTimeEnd(HotelUtils.parseStr2Date(sb.toString()));
            }
            if (wxResult.containsKey("openid")) {
                wechatPay.setOpenId(wxResult.getString("openid"));
            }
            if (wxResult.containsKey("bank_type")) {
                wechatPay.setBankType(wxResult.getString("bank_type"));
            }
            if (wxResult.containsKey("cash_fee")) {
                wechatPay.setCashFee(wxResult.getInt("cash_fee"));
            }
            if (wxResult.containsKey("total_fee")) {
                int cashFee = wxResult.getInt("total_fee") - wxResult.getInt("cash_fee");
                wechatPay.setCashFee(cashFee);
            }

            wechatPay.setUpdateTime(new Date());
            wechatPay.setPayTime(new Date());
            wechatPay.setRefundMoney(0);
            sysWechatPayTL.set(wechatPay);
            return paySuccess;
        }
        return queryPayOrderFail;
    }
}

