package com.pms.czaccount.service.common;


import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.HotelSettingByParamId;
import net.sf.json.JSONObject;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Primary
public class CommonService extends BaseService {

    /**
     * 判断是否为收钱吧支付
     *
     * @param user
     * @return
     * @throws Exception
     */
    public boolean isSqb(TbUserSession user) throws Exception {
        HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
        hotelSettingByParamId.setParentId(5);
        hotelSettingByParamId.setHid(user.getHid());
        JSONObject hotelPrintSettingMap = this.findHotelPrintSettingMap(hotelSettingByParamId);
        if (hotelPrintSettingMap == null) {
            return false;
        }
        return Objects.equals("1", hotelPrintSettingMap.getOrDefault("767", ""));
    }
}
