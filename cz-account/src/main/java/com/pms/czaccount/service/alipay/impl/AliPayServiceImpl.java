package com.pms.czaccount.service.alipay.impl;


import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.*;
import com.alipay.api.response.AlipayTradeCloseResponse;
import com.alipay.api.response.AlipayTradePrecreateResponse;
import com.github.pagehelper.Page;
import com.pms.czaccount.bean.PayAgentInfo;
import com.pms.czaccount.bean.pay.AlipayAccount;
import com.pms.czaccount.bean.pay.AlipayFaceRefund;
import com.pms.czaccount.bean.pay.AlipayFaceTransaction;
import com.pms.czaccount.bean.pay.search.AlipayAccountSearch;
import com.pms.czaccount.bean.pay.search.AlipayFaceTransactionSearch;
import com.pms.czaccount.bean.search.PayAgentInfoSearch;
import com.pms.czaccount.dao.pay.AlipayAccountDao;
import com.pms.czaccount.dao.pay.AlipayFaceRefundDao;
import com.pms.czaccount.dao.pay.AlipayFaceTransactionDao;
import com.pms.czaccount.dao.pay.PayAgentInfoDao;
import com.pms.czaccount.service.alipay.AliPayService;
import com.pms.czaccount.service.common.CommonService;
import com.pms.czaccount.service.shouqianba.ShouQianBaService;
import com.pms.czpmsutils.GsonUtil;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.M_CACHE;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.paymsg.PAY_RES;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import net.sf.json.JSONObject;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Primary
public class AliPayServiceImpl extends BaseService implements AliPayService {


    @Resource
    private AlipayAccountDao alipayAccountDao;

    @Resource
    private AlipayFaceTransactionDao alipayFaceTransactionDao;

    @Resource
    private AlipayFaceRefundDao alipayFaceRefundDao;

    @Resource
    private PayAgentInfoDao payAgentInfoDao;

    public static final String APP_ID = "****************";
    public static final String PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDMuonab9xkb5FKiUiZUJ8gd2+cT5HGBQov/oEJRcNVgB9vcjRGq+hIuxJHQdNvQE+rT9RW1dyr72lS4yotBZmNZl7o8WXz5PoH9GgReEQX5Uzi8NTOcnRcgjRolejlBgZWcVcxsp2cm4wZolh2qF9TNOx1fE1ndNPMzUSsLGTFS8jInI+DakLdqra0d1tfmg1x1P9DHtVBndTtFHUoJldEiqSyvN5+FjjDlQIbqxQ6iXtC3z4D+C97VHlhk5NbeSR+yMypQWJ9Eq5HeBV0YgGx90v5h45sJIK2J2oCwwuAVMqKYVgV5o1pA2yUmZJFqkQYRMDAtRb/ZeovxuDs0DA1AgMBAAECggEAMMnbhETYHs8/rqWcuXyvD0unO3by5F/bF6jtE0qezEdmNmbe8mCRUJPUPUsAmiiXU9oIXlFE72x0ix47tNL6zumwgI7WnxLYSwEBs/gXMiKg+gwuiqxX8pvM5YekIsAWzo7gGqXYTbP75Tf9IPnrDac1GqJux9Ldjpb0YJuLwEBsBEHoCFPKNE5Jx9aMIb0+kQtMqXkhwShniZq6iNd1FTHO/0YJeVVy+XJ/m8oBIwyYtykbbG9bvHmbtJX+co3wWYPTV+gWSy/CFMsnF7P+2WDtj6gpRh0M+qbRt/hpffOAtJgHGIo+/OLFSypyVvlVCj3a8bpcCmihE9DsTAxewQKBgQDsFq4i7ULI0oOU/XxKaQG1No0aC5JP/VK22ssvbRAy8oCU+KCsZv0oMUSkjru8DKa/JrpMPs9SZmaQGjSoOCemD1QVDytY9Mo5APGM112hk2alnGKdlRRFv4HmFqgMAsL8TwL9P4Hy7JNEa1YsbaIbJ8TxPbDDY4jswTHmcWEmSQKBgQDd/sZ1EswBG06AlfaTVdbKaK/jCmkYW6HQ92Zp9nTBAtJcSiPrjVzaseRtcgbVnw5Ojd7QC1WkVnCc5FbWR3inhWJ51/03q2R3bhtWDGUTSXsxz9Kbsu58od+y0kVDgjVY/9shfcJnCBBDu4LXJnx5AHMjFOlI4FXV2yAbN1VKjQKBgQCs9QClvcPLc83J/NptePgzVb+RcSDDUQajxy5LlE7lxBrHCE5NfzW/mDh9hcOrRGl0aF7bNLiV2UcSEuC4zErfsSMcOny7MrpjgHl1gOGrWV09vo/QekLbxizX74k1I2DNjuTVuwzsdrx8x1XePoMf+caiJVu2CNlq+S4hcLaHYQKBgQDN/JkTiuSG5lqaUAxjPBQ47pi+GCvacTQeHWIHUquVaNPe1OCtshcFymlF8LdMwvDYjSXBb7MA8UA/JFU7MUhQUSFGI9ePL1bixuVQm+Gx2s4YM1meJZLLTLywRhIFCS4NLiOb2QBo7/9/id9nWgvHj1ZGqCGrAJZFALWy604WBQKBgHegT+SOfIoTBYTZvoK0MuHj49BZjWW1kZGCo4eR6FzHgbqGjmjFcVvGGeYNOKaB3dUqEqvxvoImEza6nPvTLDqopiciOckHsUO7duHVTYcAkXxH39iJ2Aow7VObyKCBPaTlRlx6wRJZGph9x9jQNBGz7XfFN+z0nQhXEKwTTq8N";
    public static final String ALIPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvQbeiGn/7JZMlyg6oi+bUEFVWX4aHjwvyMEbKRLMuF23Q94siGwkdeELhstd9Joq9d1mfVCciRq3icgLPku4pxQeLCN50VzffXUnuJzZN/eO/R9HNJ7zX1xIEK7PhR23t3oB0P4uNzqnSDWH0oIoIzfyChIjuddDrU02PzcTGqUc53pkXW1dafRJMrVB4+ZJYJrVBC+5Z7arM5XlYMlNetSg0KSlBWjQQv5zE1kvY2g9Zhv8xP8X8scV1TmTTHULOUpKdQ87G3WCQ+90MEJZn0tgB3WJAHSd98PuzmgoTX5/f9oB4fuZo/OyAbFYN7pksNH5mXvVtsF7iXOQhq6hMQIDAQAB";

    /**
     * 获取服务商支付client
     *
     * @return
     */
    public static AlipayClient getAgentClient() {
        return new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", APP_ID, PRIVATE_KEY, "json", "utf-8", ALIPAY_PUBLIC_KEY, "RSA2");
    }


    public PayAgentInfo getPayAgentInfo(Integer hid) {
        PayAgentInfoSearch payAgentInfoSearch = new PayAgentInfoSearch();
        payAgentInfoSearch.setHid(hid);
        Page<PayAgentInfo> payAgentInfos = payAgentInfoDao.selectBySearch(payAgentInfoSearch);
        if (!payAgentInfos.isEmpty()) {
            return payAgentInfos.get(0);
        }
        return null;
    }

    @Resource
    CommonService commonService;

    @Resource
    ShouQianBaService shouQianBaService;

    /**
     * 获取缓存方法的对象
     */
    @Resource(name = "stringRedisTemplate")
    protected StringRedisTemplate stringRedisTemplate;

    /**
     * 获取支付宝二维码
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> getAlipayQrCode(Map<String, Object> map) {
        //判断是否是向pms付费
        boolean isPay2PMSSystem = Objects.equals(Boolean.TRUE, map.getOrDefault("isPay2PMSSystem", false));

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (commonService.isSqb(user) && !isPay2PMSSystem) {
                map.put("payway", "1");
                return shouQianBaService.getSQBQrCode(map);
            }

            if (isPay2PMSSystem) {
                user.setHid(2082);
            }

            //是否为服务商模式
            boolean isAgentPayModel = false;

            /*1.查询支付宝账号信息 及验资参数是否合法*/
            AlipayAccountSearch alipayAccountSearch = new AlipayAccountSearch();
            alipayAccountSearch.setHid(user.getHid());
            List<AlipayAccount> alipayAccounts = alipayAccountDao.selectBySearch(alipayAccountSearch);
            PayAgentInfo payAgentInfo = null;

            if (alipayAccounts.size() < 1) {
                payAgentInfo = getPayAgentInfo(user.getHid());
                if (payAgentInfo == null || payAgentInfo.getAlipaySellerId() == null || payAgentInfo.getAlipayAppAuthToken() == null) {
                    throw new Exception(PAY_RES.NO_ALIPAY_MSG);
                }
                isAgentPayModel = true;
            }

            //支付金额
            String moneyParam = "money";
            double moneyYuan = Double.parseDouble(map.get(moneyParam).toString());
            int money = (int) moneyYuan * 100;
            //账单号
            String mainId = HotelUtils.getHIDUUID32("ALI", user.getHid());

            //备注
            String memoParam = "Desc";
            String memoStr = "支付宝支付";
            if (map.containsKey(memoParam)) {
                memoStr = map.get(memoParam).toString();
            }

            JSONObject param = new JSONObject();
            param.put("out_trade_no", mainId);
            param.put("total_amount", moneyYuan);
            param.put("subject", memoStr);
            param.put("time_expire",simpleDateFormat.format(new Date(System.currentTimeMillis() + 2 * 60 * 1000)));

            AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();
            request.setBizContent(param.toString());
            AlipayClient client;

            request.setNotifyUrl("https://czpms.cn/hotel/pay/aliPayNotify.do");

            if (isAgentPayModel) {
                alipayAccounts.add(getPayAgentAccount(user.getHid()));
                request.putOtherTextParam("seller_id", payAgentInfo.getAlipaySellerId());
                request.putOtherTextParam("app_auth_token", payAgentInfo.getAlipayAppAuthToken());
            }

            AlipayAccount alipayAccount = alipayAccounts.get(0);


            client = new DefaultAlipayClient(
                    alipayAccount.getUrl(), alipayAccount.getAppId(), alipayAccount.getPrivateKey(),
                    alipayAccount.getFormat(), alipayAccount.getCharSet(), alipayAccount.getAlipayPublicKey(), alipayAccount.getSignType());

            //sessionToken放入Redis, 回调的时候用
            stringRedisTemplate.opsForValue().set("SESSION_TOKEN:" + mainId, sessionToken, 60L, TimeUnit.MINUTES);

            AlipayTradePrecreateResponse req = client.execute(request);

            JSONObject reqjson = JSONObject.fromObject(req.getBody());
            if (JSONObject.fromObject(reqjson.get("alipay_trade_precreate_response")).get("msg").toString().toLowerCase().equals("success")) {
                System.out.println(reqjson.get("alipay_trade_precreate_response"));

                resultMap.put(ER.MSG, JSONObject.fromObject(reqjson.get("alipay_trade_precreate_response")).get("qr_code"));
                resultMap.put("mainId", JSONObject.fromObject(reqjson.get("alipay_trade_precreate_response")).get("out_trade_no"));
            } else {
                resultMap.put(ER.RES, ER.ERR);
                resultMap.put(ER.MSG, JSONObject.fromObject(reqjson.get("alipay_trade_precreate_response")).get("sub_msg"));
            }

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取支付宝支付结果
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> getAliPayResult(Map<String, Object> map, AlipayAccount alipayAccount) {
        //判断是否是向pms付费
        boolean isPay2PMSSystem = Objects.equals(Boolean.TRUE, map.getOrDefault("isPay2PMSSystem", false));

        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            /*1.获取用户信息 并 验证参数的完整性 */
            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            String mainId = "mainId";
            if (!map.containsKey(mainId)) {
                throw new Exception(PAY_RES.REFUND_MAINID_NOT_NULL);
            }

            //向pms系统支付
            if (isPay2PMSSystem) {
                user.setHid(2082);
            }

            AlipayFaceTransactionSearch alipayFaceTransaction = new AlipayFaceTransactionSearch();
            alipayFaceTransaction.setMainId(mainId);
            alipayFaceTransaction.setHid(user.getHid());

            List<AlipayFaceTransaction> alipayFaceTransactions = alipayFaceTransactionDao.selectBySearch(alipayFaceTransaction);

            if (alipayFaceTransactions != null && alipayFaceTransactions.size() > 0) {

                AlipayFaceTransaction alipayFaceTransaction1 = alipayFaceTransactions.get(0);

                Integer status = alipayFaceTransaction1.getStatus();

                if (status == 1 || status == -1 || status == -2) {
                    resultMap.put(ER.MSG, PAY_RES.PAY_SUCC_MSG);
                    resultMap.put("mainId", alipayFaceTransaction1.getMainId());
                    resultMap.put("money", alipayFaceTransaction1.getMoney() / 100.0);
                    resultMap.put("tradeNo", alipayFaceTransaction1.getTradeNo());
                    return resultMap;
                }

            }


            /*2.获取支付结果*/
            JSONObject param = new JSONObject();
            param.put("out_trade_no", map.get(mainId));


            AlipayTradeQueryRequest request = new AlipayTradeQueryRequest();
            if (map.get("payAgentInfo") != null) {
                PayAgentInfo payAgentInfo = (PayAgentInfo) map.get("payAgentInfo");
                request.putOtherTextParam("seller_id", payAgentInfo.getAlipaySellerId());
                request.putOtherTextParam("app_auth_token", payAgentInfo.getAlipayAppAuthToken());
            }
            request.setBizContent(param.toString());
            AlipayClient client = new DefaultAlipayClient(
                    alipayAccount.getUrl(), alipayAccount.getAppId(), alipayAccount.getPrivateKey(),
                    alipayAccount.getFormat(), alipayAccount.getCharSet(), alipayAccount.getAlipayPublicKey(), alipayAccount.getSignType());

            Object res = client.execute(request);
            JSONObject resobj = JSONObject.fromObject(res);
            System.out.println(resobj);
            if (JSONObject.fromObject(JSONObject.fromObject(resobj.get("body")).get("alipay_trade_query_response")).get("msg").toString().toLowerCase().equals("success")) {

                AlipayFaceTransaction addPayMsg = new AlipayFaceTransaction();
                if (map.containsKey("uuid")) {
                    addPayMsg.setUuid(map.get("uuid").toString());
                }
                if (map.containsKey("source")) {
                    addPayMsg.setSource(map.get("source").toString());
                }
                JSONObject payobj = JSONObject.fromObject(JSONObject.fromObject(resobj.get("body")).get("alipay_trade_query_response"));
                if (JSONObject.fromObject(JSONObject.fromObject(resobj.get("body")).get("alipay_trade_query_response")).get("trade_status").toString().toUpperCase().equals("TRADE_SUCCESS")) {
                    resultMap.put("result", true);
                    resultMap.put("message", "success");
                    resultMap.put("mainId", map.get(mainId));
                    resultMap.put("money", payobj.getDouble("total_amount"));
                    addPayMsg.setMoney((int) (payobj.getDouble("total_amount") * 100));
                    addPayMsg.setTradeNo(payobj.getString("trade_no"));
                    addPayMsg.setHid(user.getHid());
                    if (payobj.containsKey("open_id")) {
                        addPayMsg.setOpenId(payobj.getString("open_id"));
                    }
                    addPayMsg.setBuyerLogonId(payobj.getString("buyer_logon_id"));
                    addPayMsg.setMainId(payobj.getString("out_trade_no"));
                    addPayMsg.setStatus(1);
                    addPayMsg.setPayTime(new Date());
                    addPayMsg.setBuyerUserId(payobj.getString("buyer_user_id"));

                    alipayFaceTransactionDao.saveAlipayFaceTransaction(addPayMsg);

                } else {
                    resultMap.put(ER.RES, ER.ERR);
                    resultMap.put(ER.MSG, JSONObject.fromObject(JSONObject.fromObject(resobj.get("body")).get("alipay_trade_query_response")).get("sub_msg"));
                }
            } else {
                resultMap.put(ER.RES, ER.ERR);
                resultMap.put(ER.MSG, JSONObject.fromObject(JSONObject.fromObject(resobj.get("body")).get("alipay_trade_query_response")).get("sub_msg"));
            }

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }


    /**
     * 支付宝交易关闭
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> alipayClose(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (commonService.isSqb(user)) {
                return shouQianBaService.paySQBClose(map);
            }

            /*1.查询支付宝账号信息 及验资参数是否合法*/
            AlipayAccountSearch alipayAccountSearch = new AlipayAccountSearch();
            alipayAccountSearch.setHid(user.getHid());
            List<AlipayAccount> alipayAccounts = alipayAccountDao.selectBySearch(alipayAccountSearch);

            PayAgentInfo payAgentInfo = null;
            if (alipayAccounts.size() < 1) {
                payAgentInfo = getPayAgentInfo(user.getHid());
                if (payAgentInfo == null || payAgentInfo.getAlipaySellerId() == null || payAgentInfo.getAlipayAppAuthToken() == null) {
                    throw new Exception(PAY_RES.NO_ALIPAY_MSG);
                }
                map.put("payAgentInfo", payAgentInfo);
                alipayAccounts.add(getPayAgentAccount(user.getHid()));
            }
            AlipayAccount alipayAccount = alipayAccounts.get(0);

            //账单号
            String mainId = map.get("mainId").toString();

            /**
             *  获取支付宝账单信息
             */

            AlipayClient alipayClient = new DefaultAlipayClient(alipayAccount.getUrl(), alipayAccount.getAppId(), alipayAccount.getPrivateKey(), alipayAccount.getFormat(), alipayAccount.getCharSet(), alipayAccount.getAlipayPublicKey(), alipayAccount.getSignType());
            AlipayTradeCloseRequest request = new AlipayTradeCloseRequest();
            JSONObject closeParam = new JSONObject();
            closeParam.put("out_trade_no", mainId);
            closeParam.put("operator_id", "LMZN");
            request.setBizContent(closeParam.toString());

            if (payAgentInfo != null) {
                request.putOtherTextParam("seller_id", payAgentInfo.getAlipaySellerId());
                request.putOtherTextParam("app_auth_token", payAgentInfo.getAlipayAppAuthToken());
            }

            AlipayTradeCloseResponse response = alipayClient.execute(request);
            System.out.println(GsonUtil.bean2Json(response));
            if (!response.isSuccess()) {
                resultMap.put(ER.RES, ER.ERR);
                resultMap.put(ER.MSG, response.getMsg());
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    /**
     * 处理支付宝返回结果
     */
    @Override
    public Map<String, Object> handleAliPayResult(Map<String, Object> map) {

        //判断是否是向pms付费
        boolean isPay2PMSSystem = Objects.equals(Boolean.TRUE, map.getOrDefault("isPay2PMSSystem", false));

        HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.putAll(map);
        map = requestMap;

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {
            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if (commonService.isSqb(user) && !isPay2PMSSystem) {
                return shouQianBaService.handleSQBResult(map);
            }

            //向pms系统支付
            if (isPay2PMSSystem) {
                user.setHid(2082);
            }

            /*1.查询酒店微信信息*/
            AlipayAccountSearch alipayAccountSearch = new AlipayAccountSearch();
            alipayAccountSearch.setHid(user.getHid());
            List<AlipayAccount> alipayAccounts = alipayAccountDao.selectBySearch(alipayAccountSearch);

            if (alipayAccounts.size() < 1) {
                PayAgentInfo payAgentInfo = getPayAgentInfo(user.getHid());
                if (payAgentInfo == null || payAgentInfo.getAlipaySellerId() == null || payAgentInfo.getAlipayAppAuthToken() == null) {
                    throw new Exception(PAY_RES.NO_ALIPAY_MSG);
                }
                map.put("payAgentInfo", payAgentInfo);
                alipayAccounts.add(getPayAgentAccount(user.getHid()));
            }
            AlipayAccount alipayAccount = alipayAccounts.get(0);
            resultMap = this.getAliPayResult(map, alipayAccount);
            return resultMap;

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;

    }

    private AlipayAccount getPayAgentAccount(Integer hid) {
        AlipayAccount alipayAccount = new AlipayAccount();
        alipayAccount.setAlipayPublicKey(ALIPAY_PUBLIC_KEY);
        alipayAccount.setAppId(APP_ID);
        alipayAccount.setPrivateKey(PRIVATE_KEY);
        //DefaultAlipayClient("https://openapi.alipay.com/gateway.do", APP_ID, PRIVATE_KEY, "json", "utf-8", ALIPAY_PUBLIC_KEY, "RSA2");
        alipayAccount.setCharSet("utf-8");
        alipayAccount.setFormat("json");
        alipayAccount.setSignType("RSA2");
        alipayAccount.setUrl("https://openapi.alipay.com/gateway.do");
        alipayAccount.setHid(hid);
        return alipayAccount;
    }

    /**
     * 支付宝退款
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> alipayRefund(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (commonService.isSqb(user)) {
                return shouQianBaService.SQBRefund(map);
            }

            /*1.查询酒店支付宝信息并验证参数的合法性*/

            /*验证支付宝账号是否存在*/
            AlipayAccountSearch alipayAccountSearch = new AlipayAccountSearch();
            alipayAccountSearch.setHid(user.getHid());
            List<AlipayAccount> alipayAccounts = alipayAccountDao.selectBySearch(alipayAccountSearch);


            PayAgentInfo payAgentInfo = null;
            if (alipayAccounts.size() < 1) {
                payAgentInfo = getPayAgentInfo(user.getHid());
                if (payAgentInfo == null || payAgentInfo.getAlipaySellerId() == null || payAgentInfo.getAlipayAppAuthToken() == null) {
                    throw new Exception(PAY_RES.NO_ALIPAY_MSG);
                }
                alipayAccounts.add(getPayAgentAccount(user.getHid()));
            }
            AlipayAccount alipayAccount = alipayAccounts.get(0);

            /*验证退款账单和退款金额*/
            String mainId = "mainId";
            if (!map.containsKey(mainId)) {
                throw new Exception(PAY_RES.REFUND_MAINID_NOT_NULL);
            }

            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            String smainId = map.get(mainId).toString();
            Object hotelWechatPay = userCahe.get("hotelaliPay", smainId);
            if (hotelWechatPay != null) {
                int i = Integer.parseInt(hotelWechatPay.toString());
                if (i > 0) {
                    throw new Exception("以退过款不允许退款，退款金额：" + i / 100.0);
                }
            }


            String refundMoney = "refundMoney";
            if (!map.containsKey(refundMoney)) {
                throw new Exception(PAY_RES.REFUND_MONEY_NOT_NULL);
            }
            int refund = Integer.parseInt(map.get(refundMoney).toString());

            /*查询支付账单是否存在*/
            AlipayFaceTransactionSearch search = new AlipayFaceTransactionSearch();
            search.setHid(user.getHid());
            search.setMainId(map.get(mainId).toString());
            List<AlipayFaceTransaction> alipayFaceTransactions = alipayFaceTransactionDao.selectBySearch(search);

            if (alipayFaceTransactions.size() < 1) {
                throw new Exception(PAY_RES.REFUND_MAINID_NOT_NULL);
            }
            AlipayFaceTransaction faceTransaction = alipayFaceTransactions.get(0);

            if (faceTransaction.getRefundMoney() == null) {
                faceTransaction.setRefundMoney(0);
            }

            boolean validateRefund = refund < 0 || faceTransaction.getRefundMoney() > 0;
            if (validateRefund) {
                throw new Exception(PAY_RES.HAS_REFUND);
            }

            /*2.执行退款*/
            JSONObject param = new JSONObject();
            param.put("trade_no", faceTransaction.getTradeNo());
            param.put("refund_amount", refund / 100.0);
            //账单号
            String outRequestNo = HotelUtils.getHIDUUID32("ALI", user.getHid());
            param.put("out_request_no", outRequestNo);

            AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
            if (payAgentInfo != null) {
                request.putOtherTextParam("seller_id", payAgentInfo.getAlipaySellerId());
                request.putOtherTextParam("app_auth_token", payAgentInfo.getAlipayAppAuthToken());
            }
            request.setBizContent(param.toString());
            AlipayClient client = new DefaultAlipayClient(
                    alipayAccount.getUrl(), alipayAccount.getAppId(), alipayAccount.getPrivateKey(),
                    alipayAccount.getFormat(), alipayAccount.getCharSet(), alipayAccount.getAlipayPublicKey(), alipayAccount.getSignType());
            Object res = client.execute(request);
            JSONObject resobj = JSONObject.fromObject(JSONObject.fromObject(JSONObject.fromObject(res).get("body")).get("alipay_trade_refund_response"));
            if (resobj.containsKey("msg") && resobj.get("msg").toString().toLowerCase().equals("success")) {
                //退款成功
                resultMap.put(ER.MSG, PAY_RES.REFUND_SUCC);

                //添加退款记录
                AlipayFaceRefund faceRefund = new AlipayFaceRefund();
                faceRefund.setRefundMoney(refund);
                faceRefund.setHid(user.getHid());
                faceRefund.setMainId(outRequestNo);
                faceRefund.setPayMainId(faceTransaction.getTradeNo());
                faceRefund.setRefundTime(new Date());
                faceRefund.setBuyerLogonId(resobj.getString("buyer_logon_id"));

                //当前可用金额
                int canUseMoney = faceTransaction.getMoney() - faceTransaction.getRefundMoney();

                faceRefund.setStatus(1);


                if (map.containsKey("source")) {
                    faceRefund.setSource(map.get("source").toString());
                }

                Integer refundId = alipayFaceRefundDao.saveAlipayFaceRefund(faceRefund);
                resultMap.put("refundId", refundId);
                //修改支付的退款金额
                AlipayFaceTransaction updateAliPay = new AlipayFaceTransaction();
                updateAliPay.setId(faceTransaction.getId());
                if (canUseMoney > refund) {
                    updateAliPay.setStatus(PAY_RES.STATE_REFUND);
                } else {
                    updateAliPay.setStatus(PAY_RES.STATE_REFUND_ALL);
                }
                updateAliPay.setRefundMoney(faceTransaction.getRefundMoney() + refund);
                if (map.containsKey("source")) {
                    updateAliPay.setSource(map.get("source").toString());
                }
                alipayFaceTransactionDao.editAlipayFaceTransaction(updateAliPay);
                userCahe.put("hotelaliPay", smainId, refund + "");
            } else {
                resultMap.put(ER.MSG, resobj.get("sub_msg"));
                resultMap.put(ER.RES, ER.ERR);
            }
            return resultMap;

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;

    }


    /**
     * 获取支付宝支付信息
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> getAlipayMsg(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*1.查询酒店微信信息*/
            Integer hid = user.getHid();
            if (user.getSessionType().equals(M_CACHE.MANAGER_TYPE)) {
                hid = Integer.parseInt(map.get("hid").toString());
            }
            AlipayAccountSearch alipayAccountSearch = new AlipayAccountSearch();
            alipayAccountSearch.setHid(hid);
            List<AlipayAccount> alipayAccounts = alipayAccountDao.selectBySearch(alipayAccountSearch);

            if (alipayAccounts.size() < 1) {
                throw new Exception(PAY_RES.NO_ALIPAY_MSG);
            }

            resultMap.put("data", alipayAccounts.get(0));

            return resultMap;

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    /**
     * 修改或添加支付宝支付信息
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> saveOrUpdateAlipayMsg(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Oprecord oprecord = new Oprecord(user);

            Integer hid = user.getHid();

            if (user.getSessionType().equals(2)) {
                hid = Integer.parseInt(map.get("hid").toString());
            }

            /*1.查询酒店微信信息*/
            AlipayAccountSearch alipayAccountSearch = new AlipayAccountSearch();
            alipayAccountSearch.setHid(user.getHid());
            List<AlipayAccount> alipayAccounts = alipayAccountDao.selectBySearch(alipayAccountSearch);

            AlipayAccount alipayAccount = new AlipayAccount();
            alipayAccount.setHid(hid);

            //1.新增
            int type = 1;

            if (alipayAccounts.size() > 0) {

                //2.修改
                type = 2;

                alipayAccount = alipayAccounts.get(0);

            }

            /**
             * 支付宝信息
             */
            JSONObject aliPayMsg = JSONObject.fromObject(map.get("aliPayMsg"));

            StringBuilder sb = new StringBuilder();
            sb.append("支付宝信息");

            if (type == 1) {
                sb.append("新增");
            } else {
                sb.append("修改");
            }

            if (aliPayMsg.get("appId") != null && !"".equals(aliPayMsg.getString("appId"))) {

                sb.append(",appId由:");
                sb.append(alipayAccount.getAppId());
                sb.append("改为:");
                sb.append(aliPayMsg.get("appId"));

                alipayAccount.setAppId(aliPayMsg.getString("appId"));

            }

            if (aliPayMsg.get("privateKey") != null && !"".equals(aliPayMsg.getString("privateKey"))) {

                sb.append(",应用私钥由:");
                sb.append(alipayAccount.getPrivateKey());
                sb.append("改为:");
                sb.append(aliPayMsg.get("privateKey"));

                alipayAccount.setPrivateKey(aliPayMsg.getString("privateKey"));

            }

            if (aliPayMsg.get("alipayPublicKey") != null && !"".equals(aliPayMsg.getString("alipayPublicKey"))) {

                sb.append(",支付宝公钥由:");
                sb.append(alipayAccount.getAlipayPublicKey());
                sb.append("改为:");
                sb.append(aliPayMsg.get("alipayPublicKey"));

                alipayAccount.setAlipayPublicKey(aliPayMsg.getString("alipayPublicKey"));

            }

            if (type == 1) {
                alipayAccountDao.saveAlipayAccount(alipayAccount);
            } else {
                alipayAccountDao.editAlipayAccount(alipayAccount);
            }

            oprecord.setDescription(sb.toString());
            this.addOprecords(oprecord);

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }


    /**
     * 支付宝反扫
     *
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> alipayMicropay(JSONObject map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (commonService.isSqb(user)) {
                return shouQianBaService.SQBMicropay(map);
            }

            PayAgentInfo payAgentInfo = null;
            /*1.查询酒店微信信息*/
            AlipayAccountSearch alipayAccountSearch = new AlipayAccountSearch();
            alipayAccountSearch.setHid(user.getHid());
            List<AlipayAccount> alipayAccounts = alipayAccountDao.selectBySearch(alipayAccountSearch);
            if (alipayAccounts.size() < 1) {
                payAgentInfo = getPayAgentInfo(user.getHid());
                if (payAgentInfo == null || payAgentInfo.getAlipaySellerId() == null || payAgentInfo.getAlipayAppAuthToken() == null) {
                    throw new Exception(PAY_RES.NO_ALIPAY_MSG);
                }
                alipayAccounts.add(getPayAgentAccount(user.getHid()));
            }
            AlipayAccount alipayAccount = alipayAccounts.get(0);

            //支付金额
            String moneyParam = "money";
            double moneyYuan = Double.parseDouble(map.get(moneyParam).toString());
            int money = (int) moneyYuan * 100;
            //账单号
            String mainId = HotelUtils.getHIDUUID32("ALI", user.getHid());

            //备注
            String memoParam = "memo";
            String memoStr = "支付宝支付";
            if (map.containsKey(memoParam)) {
                memoStr = map.get(memoParam).toString();
            }

            JSONObject param = new JSONObject();
            param.put("out_trade_no", mainId);
            param.put("total_amount", moneyYuan);
            param.put("subject", memoStr);
            param.put("scene", "bar_code");
            param.put("auth_code", map.get("scanCode"));
            param.put("time_expire",simpleDateFormat.format(new Date(System.currentTimeMillis() + 2 * 60 * 1000)));


            /**
             * 获取支付宝支付结果
             */
            AlipayTradePayRequest request = new AlipayTradePayRequest();
            if (payAgentInfo != null) {
                request.putOtherTextParam("seller_id", payAgentInfo.getAlipaySellerId());
                request.putOtherTextParam("app_auth_token", payAgentInfo.getAlipayAppAuthToken());
            }

            request.setBizContent(param.toString());
            request.setNotifyUrl("https://czpms.cn/hotel/pay/aliPayNotify.do");
            AlipayClient client = new DefaultAlipayClient(
                    alipayAccount.getUrl(), alipayAccount.getAppId(), alipayAccount.getPrivateKey(),
                    alipayAccount.getFormat(), alipayAccount.getCharSet(), alipayAccount.getAlipayPublicKey(), alipayAccount.getSignType());

            //sessionToken放入Redis, 回调的时候用
            stringRedisTemplate.opsForValue().set("SESSION_TOKEN:" + mainId, sessionToken, 60L, TimeUnit.MINUTES);
            if (map.containsKey("addAccountData")){
                stringRedisTemplate.opsForValue().set("ADD_ACCOUNT_DATA:" + mainId, map.getString("addAccountData"), 60L, TimeUnit.MINUTES);
            }

            Object res = client.execute(request).getBody();
            JSONObject resobj = JSONObject.fromObject(res);
            JSONObject alipay_trade_pay_response = JSONObject.fromObject(resobj.get("alipay_trade_pay_response"));

            if (alipay_trade_pay_response.get("msg").toString().toLowerCase().equals("success")) {
                AlipayFaceTransaction addPayMsg = new AlipayFaceTransaction();
                if (map.containsKey("uuid")) {
                    addPayMsg.setUuid(map.get("uuid").toString());
                }
                if (map.containsKey("source")) {
                    addPayMsg.setSource(map.get("source").toString());
                }
                JSONObject payobj = JSONObject.fromObject(resobj.get("alipay_trade_pay_response"));
                if (payobj.get("msg").toString().toLowerCase().equals("success")) {
                    resultMap.put("result", true);
                    resultMap.put("message", "success");
                    resultMap.put("mainId", map.get(mainId));
                    resultMap.put("money", payobj.getDouble("total_amount"));
                    addPayMsg.setMoney((int) payobj.getDouble("total_amount") * 100);
                    addPayMsg.setTradeNo(payobj.getString("trade_no"));
                    addPayMsg.setHid(user.getHid());
                    if (payobj.containsKey("open_id")) {
                        addPayMsg.setOpenId(payobj.getString("open_id"));
                    }
                    addPayMsg.setBuyerLogonId(payobj.getString("buyer_logon_id"));
                    addPayMsg.setMainId(payobj.getString("out_trade_no"));
                    addPayMsg.setStatus(1);
                    addPayMsg.setPayTime(new Date());
                    addPayMsg.setBuyerUserId(payobj.getString("buyer_user_id"));

                    alipayFaceTransactionDao.saveAlipayFaceTransaction(addPayMsg);

                } else if (alipay_trade_pay_response.getString("code").equals("10003")) {
                    resultMap.put(ER.RES, ER.ERR);
                    resultMap.put(ER.MSG, "等待用户支付");
                } else {
                    resultMap.put(ER.RES, ER.ERR);
                    resultMap.put(ER.MSG, JSONObject.fromObject(JSONObject.fromObject(resobj.get("body")).get("alipay_trade_pay_response")).get("sub_msg"));
                }
            } else if (alipay_trade_pay_response.getString("code").equals("10003")) {
                resultMap.put(ER.RES, ER.ERR);
                resultMap.put(ER.MSG, "等待用户支付");
            } else {
                resultMap.put(ER.RES, ER.ERR);
                resultMap.put(ER.MSG, JSONObject.fromObject(JSONObject.fromObject(resobj.get("body")).get("alipay_trade_pay_response")).get("sub_msg"));
            }

            resultMap.put("mainId", mainId);
            return resultMap;

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }
}
