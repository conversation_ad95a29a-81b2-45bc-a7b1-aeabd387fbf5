package com.pms.czaccount.service.agent;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.FileItem;
import com.alipay.api.request.AlipayOpenAgentConfirmRequest;
import com.alipay.api.request.AlipayOpenAgentCreateRequest;
import com.alipay.api.request.AlipayOpenAgentFacetofaceSignRequest;
import com.alipay.api.request.AlipayOpenAgentOrderQueryRequest;
import com.alipay.api.response.AlipayOpenAgentConfirmResponse;
import com.alipay.api.response.AlipayOpenAgentCreateResponse;
import com.alipay.api.response.AlipayOpenAgentFacetofaceSignResponse;
import com.alipay.api.response.AlipayOpenAgentOrderQueryResponse;
import com.pms.czpmsutils.GsonUtil;
import org.springframework.stereotype.Service;

/**
 * 阿里服务商功能
 * https://opendocs.alipay.com/isv/01a8ym
 * <p>
 * 代商家提交资料
 * ISV 可以通过开放平台提供的接口，代替商家提交签约资料，代商家签约的接口调用顺序如下图所示：
 * 通过 alipay.open.agent.create（开启代商户签约、创建应用事务）接口创建应用事务，返回生成 batch_no。
 * 通过 alipay.open.agent.facetoface.sign（代商家申请签约当面付产品）接口代替商家签约当面付收单产品，传入第一步生成的 batch_no 参数。
 * 通过 alipay.open.agent.confirm （提交代商户签约、创建应用事务）接口提交事务，同样需要传入第一步生成的 batch_no 参数。
 * 通过 alipay.open.agent.order.query （查询申请单状态）查询代签约的结果，审核时间通常是在 1 个工作日左右。
 */
@Service
public class AliPayAgentService {
    public static final String APP_ID = "2021002150637270";
    public static final String PRIVATE_KEY = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDMuonab9xkb5FKiUiZUJ8gd2+cT5HGBQov/oEJRcNVgB9vcjRGq+hIuxJHQdNvQE+rT9RW1dyr72lS4yotBZmNZl7o8WXz5PoH9GgReEQX5Uzi8NTOcnRcgjRolejlBgZWcVcxsp2cm4wZolh2qF9TNOx1fE1ndNPMzUSsLGTFS8jInI+DakLdqra0d1tfmg1x1P9DHtVBndTtFHUoJldEiqSyvN5+FjjDlQIbqxQ6iXtC3z4D+C97VHlhk5NbeSR+yMypQWJ9Eq5HeBV0YgGx90v5h45sJIK2J2oCwwuAVMqKYVgV5o1pA2yUmZJFqkQYRMDAtRb/ZeovxuDs0DA1AgMBAAECggEAMMnbhETYHs8/rqWcuXyvD0unO3by5F/bF6jtE0qezEdmNmbe8mCRUJPUPUsAmiiXU9oIXlFE72x0ix47tNL6zumwgI7WnxLYSwEBs/gXMiKg+gwuiqxX8pvM5YekIsAWzo7gGqXYTbP75Tf9IPnrDac1GqJux9Ldjpb0YJuLwEBsBEHoCFPKNE5Jx9aMIb0+kQtMqXkhwShniZq6iNd1FTHO/0YJeVVy+XJ/m8oBIwyYtykbbG9bvHmbtJX+co3wWYPTV+gWSy/CFMsnF7P+2WDtj6gpRh0M+qbRt/hpffOAtJgHGIo+/OLFSypyVvlVCj3a8bpcCmihE9DsTAxewQKBgQDsFq4i7ULI0oOU/XxKaQG1No0aC5JP/VK22ssvbRAy8oCU+KCsZv0oMUSkjru8DKa/JrpMPs9SZmaQGjSoOCemD1QVDytY9Mo5APGM112hk2alnGKdlRRFv4HmFqgMAsL8TwL9P4Hy7JNEa1YsbaIbJ8TxPbDDY4jswTHmcWEmSQKBgQDd/sZ1EswBG06AlfaTVdbKaK/jCmkYW6HQ92Zp9nTBAtJcSiPrjVzaseRtcgbVnw5Ojd7QC1WkVnCc5FbWR3inhWJ51/03q2R3bhtWDGUTSXsxz9Kbsu58od+y0kVDgjVY/9shfcJnCBBDu4LXJnx5AHMjFOlI4FXV2yAbN1VKjQKBgQCs9QClvcPLc83J/NptePgzVb+RcSDDUQajxy5LlE7lxBrHCE5NfzW/mDh9hcOrRGl0aF7bNLiV2UcSEuC4zErfsSMcOny7MrpjgHl1gOGrWV09vo/QekLbxizX74k1I2DNjuTVuwzsdrx8x1XePoMf+caiJVu2CNlq+S4hcLaHYQKBgQDN/JkTiuSG5lqaUAxjPBQ47pi+GCvacTQeHWIHUquVaNPe1OCtshcFymlF8LdMwvDYjSXBb7MA8UA/JFU7MUhQUSFGI9ePL1bixuVQm+Gx2s4YM1meJZLLTLywRhIFCS4NLiOb2QBo7/9/id9nWgvHj1ZGqCGrAJZFALWy604WBQKBgHegT+SOfIoTBYTZvoK0MuHj49BZjWW1kZGCo4eR6FzHgbqGjmjFcVvGGeYNOKaB3dUqEqvxvoImEza6nPvTLDqopiciOckHsUO7duHVTYcAkXxH39iJ2Aow7VObyKCBPaTlRlx6wRJZGph9x9jQNBGz7XfFN+z0nQhXEKwTTq8N";
    public static final String ALIPAY_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvQbeiGn/7JZMlyg6oi+bUEFVWX4aHjwvyMEbKRLMuF23Q94siGwkdeELhstd9Joq9d1mfVCciRq3icgLPku4pxQeLCN50VzffXUnuJzZN/eO/R9HNJ7zX1xIEK7PhR23t3oB0P4uNzqnSDWH0oIoIzfyChIjuddDrU02PzcTGqUc53pkXW1dafRJMrVB4+ZJYJrVBC+5Z7arM5XlYMlNetSg0KSlBWjQQv5zE1kvY2g9Zhv8xP8X8scV1TmTTHULOUpKdQ87G3WCQ+90MEJZn0tgB3WJAHSd98PuzmgoTX5/f9oB4fuZo/OyAbFYN7pksNH5mXvVtsF7iXOQhq6hMQIDAQAB";

    public static class AgentAccount {
        private String account;
        private String order_ticket;
        private AgentAccountInfo contact_info;

        public static class AgentAccountInfo {
            private String contact_name;
            private String contact_mobile;
            private String contact_email;
        }
    }


    /**
     * alipay.open.agent.create(开启代商户签约、创建应用事务)
     * 参数	类型	是否必填	最大长度	描述	示例值
     * account	String	必选	128	isv代操作的商户账号，可以是支付宝账号，也可以是pid（2088开头）	<EMAIL>
     * contact_info	ContactModel	必选		商户联系人信息，包含联系人名称、手机、邮箱信息。联系人信息将用于接受签约后的重要通知，如确认协议、到期提醒等。
     * contact_name	String	必填	64	联系人名称	张三
     * contact_mobile	String	必填	16	联系人手机号码	***********
     * contact_email	String	可选	64	联系人邮箱	<EMAIL>
     * order_ticket	String	可选	40	订单授权凭证。若传入本参数，则对应事务提交后进入预授权模式。	00ee2d475f374ad097ee0f1ac223fX00
     */



    public void openAgentCreate(AgentAccount agentAccount) {
        AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", APP_ID, PRIVATE_KEY, "json", "utf-8", ALIPAY_PUBLIC_KEY, "RSA2");
        AlipayOpenAgentCreateRequest request = new AlipayOpenAgentCreateRequest();
        request.setBizContent(GsonUtil.bean2Json(agentAccount));
        AlipayOpenAgentCreateResponse response = null;
        try {
            response = alipayClient.execute(request);
            System.out.println(response.getBatchNo());
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        if (response.isSuccess()) {
            System.out.println("调用成功");
            response.getBatchNo();
        } else {
            System.out.println("调用失败");
        }
    }

    /**
     * https://opendocs.alipay.com/apis/api_50/alipay.open.agent.facetoface.sign/
     * <p>
     * 提交当面付
     * batch_no	String	必选	25	代商户操作事务编号，通过https://opendocs.alipay.com/apis/api_50/alipay.open.agent.create (开启代商户签约、创建应用事务)接口进行事务创建后获取。	2017110616474516400082883
     * mcc_code	String	必选	16	商家经营类目编码。https://alipay.open.taobao.com/doc2/detail.htm?spm=a219a.7629140.0.0.59bgD2&treeId=222&articleId=105364&docType=1#s1
     * 商家经营类目 中的“经营类目编码”	A_A03_4582
     * special_license_pic	Byte_array	特殊可选	5242880	企业特殊资质图片，当mcc_code为需要特殊资质类目时必填。可参考
     * 商家经营类目 中的“需要的特殊资质证书”，最小5KB ，最大5M（暂不限制图片宽高），图片格式必须为：png、bmp、gif、jpg、jpeg	-
     * rate	String	特殊可选	3	服务费率（%），0.38~0.6 之间（小数点后两位，可取0.38%及0.6%）。
     * 当签约且授权标识 sign_and_auth=true 时，该费率信息必填。	0.38
     * sign_and_auth	Boolean	可选	1	签约且授权标识，默认为false，只进行签约操作； 如果设置为true，则表示签约成功后，会自动进行应用授权操作。	false
     * business_license_no	String	可选	32	营业执照号码	1532501100006302
     * business_license_pic	Byte_array	可选	5242880	营业执照图片。被代创建商户运营主体为个人账户必填，企业账户无需填写，最小5KB，最大5M（暂不限制图片宽高），图片格式必须为：png、bmp、gif、jpg、jpeg	-
     * business_license_auth_pic	Byte_array	可选	5242880	营业执照授权函图片，个体工商户如果使用总公司或其他公司的营业执照认证需上传该授权函图片，最小5KB，最大5M（暂不限制图片宽高），图片格式必须为：png、bmp、gif、jpg、jpeg	-
     * long_term	Boolean	可选	1	营业期限是否长期有效	true
     * date_limitation	String	可选	10	营业期限	2017-11-11
     * shop_scene_pic	Byte_array	可选	5242880	店铺内景图片，最小5KB，最大5M（暂不限制图片宽高），图片格式必须为：png、bmp、gif、jpg、jpeg	-
     * shop_sign_board_pic	Byte_array	可选	5242880	店铺门头照图片，最小5KB，最大5M（暂不限制图片宽高），图片格式必须为：png、bmp、gif、jpg、jpeg
     */
    public void agentFacetofaceSign() {
        AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", "app_id", "your private_key", "json", "GBK", "alipay_public_key", "RSA2");
        AlipayOpenAgentFacetofaceSignRequest request = new AlipayOpenAgentFacetofaceSignRequest();
        request.setBatchNo("2017110616474516400082883");
        request.setMccCode("A_A03_4582");
        FileItem SpecialLicensePic = new FileItem("C:/Downloads/ooopic_963991_7eea1f5426105f9e6069/16365_1271139700.jpg");
        request.setSpecialLicensePic(SpecialLicensePic);
        request.setBusinessLicenseNo("1532501100006302");
        FileItem BusinessLicensePic = new FileItem("C:/Downloads/ooopic_963991_7eea1f5426105f9e6069/16365_1271139700.jpg");
        request.setBusinessLicensePic(BusinessLicensePic);
        FileItem BusinessLicenseAuthPic = new FileItem("C:/Downloads/ooopic_963991_7eea1f5426105f9e6069/16365_1271139700.jpg");
        request.setBusinessLicenseAuthPic(BusinessLicenseAuthPic);
        request.setLongTerm(true);
        request.setDateLimitation("2017-11-11");
        FileItem ShopScenePic = new FileItem("C:/Downloads/ooopic_963991_7eea1f5426105f9e6069/16365_1271139700.jpg");
        request.setShopScenePic(ShopScenePic);
        FileItem ShopSignBoardPic = new FileItem("C:/Downloads/ooopic_963991_7eea1f5426105f9e6069/16365_1271139700.jpg");
        request.setShopSignBoardPic(ShopSignBoardPic);
        AlipayOpenAgentFacetofaceSignResponse response = null;
        try {
            response = alipayClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }

        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
        }
    }

    /**
     * alipay.open.agent.confirm(提交代商户签约、创建应用事务)
     * batch_no	String	必选	25	ISV 代商户操作事务编号，通过调用alipay.open.agent.create(开启代商户签约、创建应用事务)接口返回，详见 https://opendocs.alipay.com/apis/api_50/alipay.open.agent.create/ 。	2017110616474516400082883
     */
    private void agentConfirm(String batch_no) {
        AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", "app_id", "your private_key", "json", "GBK", "alipay_public_key", "RSA2");
        AlipayOpenAgentConfirmRequest request = new AlipayOpenAgentConfirmRequest();
        request.setBizContent("{" +
                "\"batch_no\":\"" + batch_no + "\"" +
                "  }");
        AlipayOpenAgentConfirmResponse response = null;
        try {
            response = alipayClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
        }
    }

    /**
     * alipay.open.agent.order.query(查询申请单状态)
     */
    private void agentOrderQuery() {
        AlipayClient alipayClient = new DefaultAlipayClient("https://openapi.alipay.com/gateway.do", "app_id", "your private_key", "json", "GBK", "alipay_public_key", "RSA2");
        AlipayOpenAgentOrderQueryRequest request = new AlipayOpenAgentOrderQueryRequest();
        request.setBizContent("{" +
                "\"batch_no\":\"2017110616474516400082883\"" +
                "  }");
        AlipayOpenAgentOrderQueryResponse response = null;
        try {
            response = alipayClient.execute(request);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        if (response.isSuccess()) {
            System.out.println("调用成功");
        } else {
            System.out.println("调用失败");
        }
    }

}
