package com.pms.czaccount.service.alipay;





import com.pms.czaccount.bean.pay.AlipayAccount;
import net.sf.json.JSONObject;

import java.util.Map;

/**
 * 支付宝支付信息
 * <AUTHOR>
 */
public interface AliPayService {

      /**
       * 生成支付宝二维码
       * @param map
       * @return
       */
      public Map<String, Object> getAlipayQrCode(Map<String, Object> map);

      /**
       * 获取支付宝支付结果
       * @param map
       * @return
       */
      public Map<String, Object> getAliPayResult(Map<String, Object> map, AlipayAccount alipayAccount);

      /**
       * 对支付宝返回结果做处理
       * @param map
       * @return
       */
      public Map<String, Object> handleAliPayResult(Map<String, Object> map);

      /**
       * 支付宝退款
       * @param map
       * @return
       */
      public Map<String, Object> alipayRefund(Map<String, Object> map);

      /**
       * 支付宝交易关闭
       * @param map
       * @return
       */
      public Map<String,Object> alipayClose(Map<String, Object> map);


      /**
       * 获取支付宝支付信息
       * @param map
       * @return
       */
      public Map<String,Object> getAlipayMsg(Map<String, Object> map);

      /**
       * 修改或添加支付宝支付信息
       * @param map
       * @return
       */
      public Map<String,Object> saveOrUpdateAlipayMsg(Map<String, Object> map);

      /**
       * 支付宝反扫
       * @param map
       * @return
       */
      public Map<String,Object> alipayMicropay(JSONObject map);

}
