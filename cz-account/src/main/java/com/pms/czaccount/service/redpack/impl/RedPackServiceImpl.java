package com.pms.czaccount.service.redpack.impl;

import com.pms.czaccount.bean.pay.SysWechatRedpack;
import com.pms.czaccount.bean.pay.SysWechatRedpackSetting;
import com.pms.czaccount.bean.pay.search.SysWechatRedpackSearch;
import com.pms.czaccount.bean.pay.search.SysWechatRedpackSettingSearch;
import com.pms.czaccount.dao.pay.SysWechatRedpackDao;
import com.pms.czaccount.dao.pay.SysWechatRedpackSettingDao;
import com.pms.czaccount.service.redpack.RedPackService;
import com.pms.czaccount.service.wechat.WeChatPayService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.*;

@Service
@Primary
public class RedPackServiceImpl extends BaseService implements RedPackService {


    @Autowired
    private SysWechatRedpackSettingDao sysWechatRedpackSettingDao;


    @Autowired
    private SysWechatRedpackDao sysWechatRedpackDao;

    @Autowired
    private WeChatPayService weChatPayService;

    /**
     * 新增或修改红包设置
     * @param param
     * @return
     */
    @Override
    public JSONObject updateRedPackSetting(JSONObject param) {
        JSONObject resultMap = new JSONObject();
        resultMap.put(ER.RES, ER.SUCC);
        try {

            /**
             * 1.取出用户信息
             */
            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /**
             * 2.取出传递参数
             */
            JSONObject setobj = param.getJSONObject("setobj");
            SysWechatRedpackSetting sysWechatRedpackSetting = (SysWechatRedpackSetting) JSONObject.toBean(setobj, SysWechatRedpackSetting.class);

            Oprecord oprecord = new Oprecord(user);

            this.addOprecords(oprecord);


            /**
             * 3. 如果id 为空 或 小于1  则说明是添加  反则修改
             */
            if(sysWechatRedpackSetting.getId()==null||sysWechatRedpackSetting.getId()<1){
                sysWechatRedpackSetting.setId(null);
                sysWechatRedpackSetting.setCreateTime(new Date());
                sysWechatRedpackSetting.setCreateUser(user.getUserName());
                sysWechatRedpackSetting.setCreateUserId(user.getUserId());
                Integer integer = sysWechatRedpackSettingDao.saveSysWechatRedpackSetting(sysWechatRedpackSetting);

                if(integer<1){
                    throw new Exception("添加红包设置失败");
                }

                oprecord.setDescription("添加红包设置:");
                oprecord.setSourceValue(JSONObject.fromObject(sysWechatRedpackSetting).toString());
            }else {

                SysWechatRedpackSetting oldSetting = sysWechatRedpackSettingDao.selectById(sysWechatRedpackSetting.getId());

                Integer integer = sysWechatRedpackSettingDao.editSysWechatRedpackSetting(sysWechatRedpackSetting);

                if(integer<1){
                    throw new Exception("修改红包设置失败");
                }
                oprecord.setDescription("修改红包设置:");
                oprecord.setSourceValue(JSONObject.fromObject(oldSetting).toString());
                oprecord.setChangedValue(JSONObject.fromObject(sysWechatRedpackSetting).toString());
            }

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    @Override
    public JSONObject searchRedPackSetting(JSONObject param) {
        JSONObject resultMap = new JSONObject();
        resultMap.put(ER.RES, ER.SUCC);
        try {

            List<SysWechatRedpackSetting> sysWechatRedpackSettings = sysWechatRedpackSettingDao.selectBySearch(null);

            resultMap.put("list",sysWechatRedpackSettings);

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    /**
     * 发送 入住 红包
     * @param param
     * @return
     */
    @Transactional
    @Override
    public JSONObject sendCheckInRedpack(JSONObject param) {
        JSONObject resultMap = new JSONObject();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            /**
             * 1.取出用户信息
             */
            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            // 有没有指定发送红包账户，没有 默认为1878
            Object redPackHid = param.get("redPackHid");
            if(redPackHid==null){
                param.put("redPackHid",1878);
            }

            JSONObject regist = param.getJSONObject("regist");

            /**
             * 2.根据流水号 查询订单信息 判断是在自助机开过房
             */
          /*  String sn = param.getString("sn");

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setSn(sn);

            List<Regist> regists = registDao.selectBySearch(registSearch);
            if(regists.size()<1){
                throw new Exception("未查到相应的订单信息");
            }
            Regist regist = regists.get(0);
*/
            /**
             * 3.查询当前流水号是否生成过红包
             */
            SysWechatRedpackSearch sysWechatRedpackSearch = new SysWechatRedpackSearch();
            sysWechatRedpackSearch.setOtherSn(regist.getString("sn"));
            List<SysWechatRedpack> sysWechatRedpacks = sysWechatRedpackDao.selectBySearch(sysWechatRedpackSearch);
            if(sysWechatRedpacks.size()>0){
                throw new Exception("当前订单已领取过红包");
            }

            /**
             * 4.查询当前openId 是否领过入住红包
             */
            String openid = param.getString("openId");
            sysWechatRedpackSearch = new SysWechatRedpackSearch();
            sysWechatRedpackSearch.setReceiverOpenid(openid);
            sysWechatRedpacks = sysWechatRedpackDao.selectBySearch(sysWechatRedpackSearch);

            if(sysWechatRedpacks.size()>0){
                throw new Exception("当前用户已领取过入住红包");
            }

            /**
             * 5.查询当前红包个数
             */
            param.put("businessDay",user.getBusinessDay());
            List<HashMap<String, Integer>> redpackCountList = sysWechatRedpackDao.redpackDayCount(param);

            //不同设置类型红包对应 所发放的数量
            HashMap<Integer, Integer> redpackCountMap = new HashMap<>();
            for(HashMap<String, Integer> pc:redpackCountList){
                redpackCountMap.put(pc.get("redPackSettingId"),pc.get("sumCount"));
            }

            /**
             * 6.查询当前时间段的红包设置
             */
            SysWechatRedpackSettingSearch sysWechatRedpackSettingSearch = new SysWechatRedpackSettingSearch();
            sysWechatRedpackSettingSearch.setSearchTime(HotelUtils.currentDate());
            List<SysWechatRedpackSetting> sysWechatRedpackSettings = sysWechatRedpackSettingDao.selectBySearch(sysWechatRedpackSettingSearch);
            if(sysWechatRedpackSettings.size()<1){
                throw new Exception("未查到相应的红包活动");
            }

            /**
             * 7.计算阈值
             *    三次比对
             *    判断阈值在哪个红包设置里
             */
            double v1 = Math.random() * 10000;
            double v2 = Math.random() * 10000;
            double v3 = Math.random() * 10000;

            int threshold = (int)((v1 + v2 + v3) / 300);

            //记录还有发放数量的红包设置
            List<SysWechatRedpackSetting> hasCountSetting = new ArrayList<>();

            // 已选择阈值范围内的红包设置
            SysWechatRedpackSetting checkSetting = null;

            for (SysWechatRedpackSetting swrs:sysWechatRedpackSettings){

                Double minThreshold = swrs.getMinThreshold();
                Double maxThreshold = swrs.getMaxThreshold();

                Integer id = swrs.getId();

                //当天已发放数量
                Integer count = redpackCountMap.get(id);

                //记录当天发放数量为空或者小于 可发放数
                if(count==null||count<swrs.getTotalAmount()){
                    hasCountSetting.add(swrs);
                }else {
                    continue;
                }

                if(threshold>=minThreshold&&threshold<maxThreshold){
                    checkSetting = swrs;
                }

            }

            //如果阈值内的红包已发放完毕，则寻找未发放完的红包代替,价格由低到高
            if(checkSetting==null){
                if(hasCountSetting.size()<1){
                    throw new Exception("当天红包已全部发放完毕。");
                }
                checkSetting = hasCountSetting.get(0);
            }

            /**
             * 8.生成红包记录
             */
            String wrp = HotelUtils.getHIDUUID32("WRP", user.getHid()).substring(0,27);
            param.put("mainId",wrp);
            SysWechatRedpack sysWechatRedpack = new SysWechatRedpack();
            sysWechatRedpack.setHid(user.getHid());
            sysWechatRedpack.setSn(wrp);
            sysWechatRedpack.setOtherSn(regist.getString("sn"));
            sysWechatRedpack.setOtherId(regist.getInt("registId"));
            sysWechatRedpack.setRedpackSettingId(checkSetting.getId());
            sysWechatRedpack.setSendHid(param.getInt("redPackHid"));
            sysWechatRedpack.setActName("入住送红包");
            sysWechatRedpack.setWishing("恭喜发财");
            sysWechatRedpack.setSceneId("PRODUCT_2");
            sysWechatRedpack.setMoney(checkSetting.getMoney());
            sysWechatRedpack.setStatus(1);
            sysWechatRedpack.setType(1);
            sysWechatRedpack.setReceiverOpenid(openid);
            sysWechatRedpack.setThreshold(threshold*1.0);
            sysWechatRedpack.setTotalNum(1);
            sysWechatRedpack.setBusinessDay(user.getBusinessDay());
            sysWechatRedpack.setCreateUser(user.getUserName());
            sysWechatRedpack.setCreateTime(new Date());
            sysWechatRedpack.setCreateUserId(user.getUserId());

            param.put("money",checkSetting.getMoney());

            Integer integer = sysWechatRedpackDao.saveSysWechatRedpack(sysWechatRedpack);

            if(integer<1){
                throw new Exception("创建微信红包失败");
            }

            /**
             * 9.调用微信红包接口
             */
            Map<String, Object> stringObjectMap = weChatPayService.senRedPack(param);
            /*if(stringObjectMap.get(ER.RES).toString().equals(ER.ERR)){
                throw new Exception(stringObjectMap.get(ER.MSG).toString());
            }*/

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return resultMap;
    }

}
