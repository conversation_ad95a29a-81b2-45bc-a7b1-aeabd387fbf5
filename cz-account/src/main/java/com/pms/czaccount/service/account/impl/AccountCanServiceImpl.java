package com.pms.czaccount.service.account.impl;


import com.github.pagehelper.Page;
import com.pms.czaccount.bean.SqbTransaction;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountCancel;
import com.pms.czaccount.bean.account.AccountThirdPayRecode;
import com.pms.czaccount.bean.account.search.AccountCancelSearch;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.bean.pay.AccountReset;
import com.pms.czaccount.bean.pay.AlipayFaceTransaction;
import com.pms.czaccount.bean.pay.SysWechatPay;
import com.pms.czaccount.bean.pay.search.AlipayFaceTransactionSearch;
import com.pms.czaccount.bean.pay.search.SysWechatPaySearch;
import com.pms.czaccount.bean.search.SplitAccountReset;
import com.pms.czaccount.bean.search.SqbTransactionSearch;
import com.pms.czaccount.dao.account.AccountCancelDao;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czaccount.dao.account.AccountThirdPayRecodeDao;
import com.pms.czaccount.dao.pay.AccountResetDao;
import com.pms.czaccount.dao.pay.AlipayFaceTransactionDao;
import com.pms.czaccount.dao.pay.SqbTransactionDao;
import com.pms.czaccount.dao.pay.SysWechatPayDao;
import com.pms.czaccount.service.account.AccountCanService;
import com.pms.czaccount.service.alipay.AliPayService;
import com.pms.czaccount.service.wechat.WeChatPayService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.OrderNumUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.AccountCancelRequest;
import com.pms.czpmsutils.request.AccountSearchRequest;
import com.pms.czpmsutils.request.AccountSummary;
import com.pms.czpmsutils.request.UpdateAccountRequest;
import com.pms.czpmsutils.request.search.AccountSummarySearch;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.util.*;

@Service
@Primary
public class AccountCanServiceImpl extends BaseService implements AccountCanService {

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private AccountCancelDao accountCancelDao;


    @Autowired
    private WeChatPayService weChatPayService;

    @Autowired
    private AliPayService aliPayService;

    /* @Autowired
     private LvyunInterfaceService lvyunInterfaceService;
 */


   /* @Autowired
    private XiRuanService xiRuanService;
*/

    @Autowired
    private SysWechatPayDao wechatPayDao;


    @Autowired
    private AlipayFaceTransactionDao alipayFaceTransactionDao;

    @Autowired
    private AccountThirdPayRecodeDao accountThirdPayRecodeDao;

    @Resource
    private SqbTransactionDao sqbTransactionDao;

   /* @Autowired
    private CardInfoDao cardInfoDao;*/
/*
    @Autowired
    private MemberTransactionService memberTransactionService;*/

   /* @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;*/

   /* @Autowired
    private AccountTransaction accountTransaction;*/

    @Autowired
    private AccountResetDao accountResetDao;


    @Override
    public ResponseData findAccountByRegistId(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);


            /**
             * 2.查询账务信息
             */
            AccountSearch accountSearch = new AccountSearch();

            Object registIdObj = param.get("registId");
            if (registIdObj != null && !"".equals(registIdObj.toString())) {
                accountSearch.setRegistId(param.getInt("registId"));
                accountSearch.setBookingId(null);
            }

            Object bookingId = param.get("bookingId");
            if (bookingId != null && !"".equals(bookingId.toString())) {
                accountSearch.setRegistId(null);
                accountSearch.setBookingId(Integer.parseInt(bookingId.toString()));
            }


            if (param.get("page") != null && param.get("pageSize") != null) {
                int page = param.getInt("page");
                int pageSize = param.getInt("pageSize");

                int i = (page - 1) * pageSize;
                accountSearch.setStartPage(i);
                accountSearch.setPageSize(pageSize);
            }


            if (param.get("registStates") != null && !param.getString("registStates").equals("")) {
                accountSearch.setRegistStates(param.getString("registStates"));
            }

            if (param.get("classIds") != null && !param.getString("classIds").equals("")) {
                accountSearch.setClassIds(param.getString("classIds"));
            }

            if (param.get("businessDayStart") != null && !param.getString("businessDayStart").equals("")) {
                accountSearch.setBusinessDayStart(param.getInt("businessDayStart"));
            }

            if (param.get("businessDayEnd") != null && !param.getString("businessDayEnd").equals("")) {
                accountSearch.setBusinessDayEnd(param.getInt("businessDayEnd"));
            }

            if (param.get("isCancel") != null && !param.getString("isCancel").equals("")) {
                accountSearch.setIsCancel(param.getInt("isCancel"));
            }

            accountSearch.setHid(user.getHid());

            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            Integer integer = accountDao.selectBySearchCount(accountSearch);

            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("sumCount", integer);
            resultMap.put("list", accounts);
            responseData.setData(resultMap);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    public ResponseData findPrintAccountByResist(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            int registId = param.getInt("registId");

            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setRegistId(registId);
            accountSearch.setIsCancel(0);

            Object personId = param.get("personId");
            if (personId != null) {
                accountSearch.setRegistPersonId(Integer.parseInt(personId.toString()));
            }

            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            responseData.setData(accounts);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData findPrintAccountSummaryByResist(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            int registId = param.getInt("registId");

            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setRegistId(registId);
            accountSearch.setIsCancel(0);

            Object personId = param.get("personId");
            if (personId != null) {
                accountSearch.setRegistPersonId(Integer.parseInt(personId.toString()));
            }

            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            Map<String, Account> integerAccountHashMap = new HashMap<>();

            for (Account account : accounts) {

                Account account1 = integerAccountHashMap.get(account.getPayCodeId());

                if (account1 == null) {
                    account1 = new Account();
                    account1.setPrice(0);
                    account1.setPayClassId(account.getPayClassId());
                    account1.setPayClassName(account.getPayClassName());
                    account1.setPayCodeId(account.getPayCodeId());
                    account1.setPayCodeName(account.getPayCodeName());
                    account1.setPayType(account.getPayType());
                    account1.setAccountType(account.getAccountType());
                }

                account1.setPrice(account1.getPrice() + account.getPrice());

                integerAccountHashMap.put(account.getPayCodeId(), account1);

            }

            ArrayList<Account> accounts1 = new ArrayList<>(integerAccountHashMap.values());

            responseData.setData(accounts1);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional
    public Map<String, Object> setOff(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*获取操作日志记录*/
            Oprecord oprecord = new Oprecord(user);
            ArrayList<Oprecord> oprecords = new ArrayList<>();

            if (param.get("accounts") == null || param.getString("accounts").equals("")) {
                throw new Exception("账务信息不能空");
            }

            JSONArray accounts = JSONArray.fromObject(URLDecoder.decode(param.getString("accounts"), "utf-8"));

            if (accounts.size() < 1) {
                throw new Exception("账务信息不能空");
            }

            ArrayList<Account> accountList = new ArrayList<>();

            for (int i = 0; i < accounts.size(); i++) {
                Account account = (Account) JSONObject.toBean(accounts.getJSONObject(i), Account.class);

                if (account == null) {
                    throw new Exception("为查询到账务信息");
                }

                if (!account.getCreateUserId().equals(user.getUserId())) {
                    throw new Exception("不用冲其他人员的账务");
                }

                if (!account.getClassId().equals(user.getClassId())) {
                    throw new Exception("不允许冲其他班次的账务");
                }
                if (!account.getBusinessDay().equals(user.getBusinessDay())) {
                    throw new Exception("不允许冲其他营业日的账务");
                }

                //  Date date = new Date();

                account.setIsCancel(1);
                //2020-08-27修改了，冲账的状态是 3
                account.setRegistState(3);
                accountList.add(account);
            }

            for (int i = 0; i < accountList.size(); i++) {
                Date date = new Date();
                Account account = accountList.get(i);
                Integer integer = accountDao.editAccount(account);
                if (integer < 1) {
                    throw new Exception("冲账失败");
                }
                AccountCancel accountCancel = new AccountCancel();
                accountCancel.setHid(account.getHid());
                accountCancel.setHotelGroupId(account.getHotelGroupId());
                accountCancel.setAccountId(account.getAccountId());
                accountCancel.setPrice(account.getPrice());
                accountCancel.setPayType(account.getPayType());
                accountCancel.setPayClassName(account.getPayClassName());
                accountCancel.setPayCodeId(Integer.parseInt(account.getPayCodeId()));
                accountCancel.setPayCodeName(account.getPayCodeName());
                accountCancel.setRoomInfoId(account.getRoomInfoId());
                accountCancel.setRoomNum(account.getRoomNum());
                accountCancel.setAccountCode(account.getAccountCode());
                accountCancel.setAccountCreateUserName(account.getCreateUserName());
                accountCancel.setIsSale(account.getIsSale());
                accountCancel.setBusinessDay(account.getBusinessDay());
                accountCancel.setClassId(account.getClassId());
                accountCancel.setCreateTime(date);
                accountCancel.setCreateUserId(user.getUserId());
                accountCancel.setCreateUserName(user.getUserName());
                accountCancel.setRegistId(account.getRegistId());
                accountCancel.setBookingId(account.getBookingId());
                accountCancel.setCancelType(2);

                integer = accountCancelDao.insert(accountCancel);

                if (integer < 1) {
                    throw new Exception("冲账失败");
                }

                /**
                 * 创建子线程，处理辅助房态
                 */
            }

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return resultMap;
    }

    @Override
    @Transactional
    public Map<String, Object> transferAccount(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Oprecord oprecord = new Oprecord(user);
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            if (param.get("accounts") == null || param.getString("accounts").equals("")) {
                throw new Exception("账务信息不能空");
            }
            JSONArray accounts = JSONArray.fromObject(URLDecoder.decode(param.getString("accounts"), "utf-8"));
            /**
             * 获取转账前的registId
             */
            if (param.get("registInfoBefore") == null || param.getString("registInfoBefore").equals("")) {
                throw new Exception("登记信息不能空");
            }
            JSONObject registInfoBefore = JSONObject.fromObject(URLDecoder.decode(param.getString("registInfoBefore"), "utf-8"));

            /**
             * 获取转账后的registId
             */
            if (param.get("registInfoAfter") == null || param.getString("registInfoAfter").equals("")) {
                throw new Exception("登记信息不能空");
            }
            int registPersonId = 0;

            if (param.get("registPersonId") != null && param.getInt("registPersonId") > 0) {
                registPersonId = param.getInt("registPersonId");
            }


            JSONObject registInfoAfter = JSONObject.fromObject(URLDecoder.decode(param.getString("registInfoAfter"), "utf-8"));

            JSONObject transferAccountParam = JSONObject.fromObject(URLDecoder.decode(param.getString("transferAccountParam"), "utf-8"));

            ArrayList<AccountCancel> accountCancels = new ArrayList<>();
            /**
             * 插入新的账务数据到账务表中
             */
            for (int i = 0; i < accounts.size(); i++) {
                Account account = (Account) JSONObject.toBean(accounts.getJSONObject(i), Account.class);
                if (account == null) {
                    throw new Exception("未查询到账务信息");
                }
                if (account.getRegistState() != 0) {
                    throw new Exception("请检查账务状态");
                }
                account.setRegistId(registInfoAfter.getInt("registId"));
                if (registPersonId > 0) {
                    account.setRegistPersonId(registPersonId);
                    account.setAccountCode(URLDecoder.decode(param.getString("personName"), "utf-8"));
                }

                int value = accountDao.editAccount(account);
                if (value < 1) {
                    throw new Exception("更新账务信息失败");
                }
                Date date = new Date();
                AccountCancel accountCancel = new AccountCancel();
                accountCancel.setHid(account.getHid());
                accountCancel.setHotelGroupId(account.getHotelGroupId());
                accountCancel.setAccountId(account.getAccountId());
                accountCancel.setPrice(account.getPrice());
                accountCancel.setPayType(account.getPayType());
                accountCancel.setPayClassName(account.getPayClassName());
                accountCancel.setPayCodeId(Integer.parseInt(account.getPayCodeId()));
                accountCancel.setPayCodeName(account.getPayCodeName());
                accountCancel.setRoomInfoId(account.getRoomInfoId());
                accountCancel.setRoomNum(account.getRoomNum());
                accountCancel.setAccountCode(account.getAccountCode());
                accountCancel.setAccountCreateUserName(account.getCreateUserName());
                accountCancel.setIsSale(account.getIsSale());
                accountCancel.setBusinessDay(account.getBusinessDay());
                accountCancel.setClassId(account.getClassId());
                accountCancel.setCreateTime(date);
                accountCancel.setCreateUserId(user.getUserId());
                accountCancel.setCreateUserName(user.getUserName());
                accountCancel.setRegistId(registInfoAfter.getInt("registId"));
                accountCancel.setBookingId(registInfoAfter.getInt("bookingOrderId"));
                accountCancel.setRoomNum(registInfoAfter.getString("roomNum"));
                accountCancel.setRoomInfoId(registInfoAfter.getInt("roomNumId"));
                accountCancel.setCancelType(1);
                accountCancel.setSourceRegistId(registInfoBefore.getInt("registId"));
                accountCancel.setSourceRoomNum(registInfoBefore.getString("roomNum"));
                accountCancel.setReason(transferAccountParam != null ? transferAccountParam.containsKey("reason") ? transferAccountParam.getString("reason") : "" : "");
                accountCancel.setRemark(transferAccountParam != null ? transferAccountParam.containsKey("remark") ? transferAccountParam.getString("remark") : "" : "");
                value = accountCancelDao.insert(accountCancel);
                if (value < 1) {
                    throw new Exception("更新账务信息失败");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return resultMap;
    }


    @Override
    public ResponseData findAccountCancelByRegistId(AccountCancelRequest accountCancelRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (null == accountCancelRequest.getRegistId() && null == accountCancelRequest.getBookingId()) {
                throw new Exception("订单号或预订号不能为空");
            }
            AccountCancelSearch accountCancelSearch = new AccountCancelSearch();
            if (null != accountCancelRequest.getCancelType()) {
                accountCancelSearch.setCancelType(accountCancelRequest.getCancelType());
            }
            if (null != accountCancelRequest.getRegistId()) {
                accountCancelSearch.setSourceRegistId(accountCancelRequest.getRegistId());
            }
            Page<AccountCancel> accountCancels = accountCancelDao.selectBySearch(accountCancelSearch);
            responseData.setData(accountCancels);

//            /**
//             * 1.获取登录信息
//             */
//            String sessionToken = param.getString(ER.SESSION_TOKEN);
//            final TbUserSession user = this.getTbUserSession(sessionToken);
//
//
//            /**
//             * 2.查询账务信息
//             */
//            AccountCancelSearch accountCancelSearch = new AccountCancelSearch();
//
//            Object registId = param.get("registId");
//            if (registId != null && !registId.toString().equals("")) {
//                accountCancelSearch.setRegistId(param.getInt("registId"));
//            }
//            Object bookingId = param.get("bookingId");
//            if (bookingId != null && !"".equals(bookingId.toString())) {
//                accountCancelSearch.setRegistId(null);
//                accountCancelSearch.setBookingId(Integer.parseInt(bookingId.toString()));
//            }
//
//            if (param.get("page") != null && param.get("pageSize") != null) {
//                int page = param.getInt("page");
//                int pageSize = param.getInt("pageSize");
//                int i = (page - 1) * pageSize;
//                accountCancelSearch.setStartPage(i);
//                accountCancelSearch.setPageSize(pageSize);
//            }
//
//
//            if (param.get("cancelType") != null && !param.getString("cancelType").equals("")) {
//                accountCancelSearch.setCancelType(param.getInt("cancelType"));
//            }
//
//            accountCancelSearch.setHid(user.getHid());
//            List<AccountCancel> accountCancels = accountCancelDao.selectBySearch(accountCancelSearch);
//            Integer integer = accountCancelDao.selectBySearchCount(accountCancelSearch);
//            resultMap.put("sumCount", integer);
//            resultMap.put("accountCancelMsg", accountCancels);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    @Override
    public ResponseData getAccountCountByRegistId(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            Map<String, Object> accountInfo = new HashMap<>();
            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            /**
             * 2.查询账务信息
             */
            AccountSearch accountSearch = new AccountSearch();

            Object registId = param.get("registId");
            if (registId != null && !registId.toString().equals("")) {
                accountSearch.setRegistId(param.getInt("registId"));
            }
            Object bookingId = param.get("bookingOrderId");
            if (bookingId != null && !"".equals(bookingId.toString())) {
                accountSearch.setRegistId(null);
                Object registIdIsNull = param.get("registIdIsNull");
                if (registIdIsNull != null && !"".equals(registIdIsNull.toString())) {
                    accountSearch.setRegistIdIsNull(param.getInt("registIdIsNull"));
                }
                accountSearch.setBookingId(Integer.parseInt(bookingId.toString()));
            }
            Object registIds = param.get("registIds");
            if (registIds != null && !"".equals(registIds.toString())) {
                accountSearch.setRegistId(null);
                accountSearch.setBookingId(null);
                accountSearch.setRegistIds(registIds.toString());
            }
            if (null != param.get("registPersonId") && !"".equals(param.getString("registPersonId"))) {
                accountSearch.setRegistPersonId(param.getInt("registPersonId"));
            }
            if (param.get("orGroupAccountId") != null) {
                accountSearch.setOrGroupAccountId(param.getInt("orGroupAccountId"));
            }

            if (param.get("groupAccount") != null) {
                accountSearch.setGroupAccount(param.getInt("groupAccount"));
            }

            accountSearch.setHid(user.getHid());
            accountSearch.setIsCancel(0);

            if (param.get("accountStateList") != null) {
                JSONArray jsonArray = param.getJSONArray("accountStateList");
                List<Integer> accountStateList = new ArrayList<>();
                for (int i = 0; i < jsonArray.size(); i++) {
                    accountStateList.add(jsonArray.getInt(i));
                }
                accountSearch.setAccountStateList(accountStateList);
            }

            if (accountSearch.getAccountStateList() == null || accountSearch.getAccountStateList().size() < 1) {
                accountSearch.setRegistState(0);
            }

            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            double cost = 0.00;
            double cash = 0.00;
            double free = 0.00;
            double count = 0.00;

            ArrayList<Account> costList = new ArrayList<>();

            ArrayList<Account> cashList = new ArrayList<>();

            ArrayList<Account> freeList = new ArrayList<>();

            for (int i = 0; i < accounts.size(); i++) {
                Account account = accounts.get(i);

                Integer payType = account.getPayType();
                Integer isCancel = account.getIsCancel();
                Integer price = account.getPrice();

                if (payType == 1 && isCancel == 0) {
                    cost += price;
                    costList.add(account);
                } else if (payType == 2 && isCancel == 0) {
                    cash += price;
                    cashList.add(account);
                }

                if ((account.getPayCodeId().equals("9100") || account.getPayCodeId().equals("9620")) && account.getThirdRefundState() == 0 && isCancel == 0) {
                    free += price;
                    freeList.add(account);
                }
            }
            cash = cash - free;
            count = cash + free - cost;
            if (cash + free >= 0) {
                count = cash + free - cost;
            } else {
                count = cash + free + cost;
            }
            accountInfo.put("cost", cost);
            accountInfo.put("cash", cash);
            accountInfo.put("free", free);
            accountInfo.put("count", count);
            accountInfo.put("costList", costList);
            accountInfo.put("cashList", cashList);
            accountInfo.put("freeList", freeList);
            responseData.setData(accountInfo);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional
    public Map<String, Object> settleAccount(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*获取操作日志记录*/
            Oprecord oprecord = new Oprecord(user);
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            if (param.get("accounts") == null || param.getString("accounts").equals("")) {
                throw new Exception("账务信息不能空");
            }
            JSONArray accounts = JSONArray.fromObject(URLDecoder.decode(param.getString("accounts"), "utf-8"));

            for (int i = 0; i < accounts.size(); i++) {
                Date date = new Date();
                if (accounts.getJSONObject(i).containsKey("accountId")) {
                    Account accountInfo = (Account) JSONObject.toBean(accounts.getJSONObject(i), Account.class);
                    accountInfo.setRegistState(1);
                    accountInfo.setSettleAccountTime(date);
                    int result = accountDao.editAccount(accountInfo);
                    if (result < 1) {
                        throw new Exception("修改账务信息失败");
                    }
                } else {
                    JSONObject accountInfo = accounts.getJSONObject(i);
                    String a = HotelUtils.getHIDUUID32("A", user.getHid());
                    Account account = new Account();
                    account.setAccountId(a);
                    account.setHid(user.getHid());
                    account.setCreateUserId(user.getUserId());
                    account.setCreateUserName(user.getUserName());
                    account.setCreateTime(new Date());
                    account.setIsCancel(0);
                    account.setAccountYear(user.getBusinessYear());
                    account.setAccountYearMonth(user.getBusinessMonth());
                    account.setBusinessDay(user.getBusinessDay());
                    account.setClassId(user.getClassId());
                    account.setPrice(accountInfo.getInt("money"));
                    account.setSettleAccountTime(new Date());
                    account.setThirdRefundState(0);
                    account.setSaleNum(1);
                    account.setUintPrice(accountInfo.getInt("money") / 1);
                    //类型 ：消费、付款
                    int payType = accountInfo.getInt("payType");
                    account.setPayType(payType);
                    //费用码
                    int costClassId = accountInfo.getInt("costClassId");
                    String costClassName = accountInfo.getString("costClassName");
                    String costCodeId = accountInfo.getString("costCodeId");
                    String costCodeName = accountInfo.getString("costCodeName");

                    account.setPayClassId(costClassId);
                    account.setPayClassName(costClassName);
                    account.setPayCodeId(costCodeId);
                    account.setPayCodeName(costCodeName);

                    account.setRegistId(accountInfo.getInt("registId"));
                    account.setRoomNum(accountInfo.getString("roomNum"));
                    account.setRoomInfoId(accountInfo.getInt("roomNumId"));
                    account.setRegistState(1);
                    account.setRegistPersonId(accountInfo.getInt("registPersonId"));
                    account.setAccountCode(accountInfo.getString("accountCode"));
                    account.setRemark(accountInfo.getString("remark"));
                    account.setReason(accountInfo.getString("reason"));
                    account.setRefundPrice(0);

                    /**
                     * 根据不同的费用码判断进行不同的操作
                     *  微信/支付宝 获取 扫码账单
                     */
                    switch (costCodeId) {
                        case "9320": //微信扫码支付
                            param.put("money", account.getPrice() / 100.0);
                            param.put("Desc", account.getRegistId() + "-" + account.getRoomNum());
                            account.setThirdRefundState(-1);
                            Map<String, Object> weChatQrcode = weChatPayService.getWeChatQrcode(param);
                            if (weChatQrcode.get(ER.RES).toString().equals(ER.ERR)) {
                                throw new Exception(weChatQrcode.get(ER.MSG).toString());
                            }
                            resultMap.put("qrcode", weChatQrcode.get(ER.MSG));
                            String wxmainId = weChatQrcode.get("mainId").toString();
                            account.setThirdAccoutId(wxmainId);
                            account.setAccountCode(weChatQrcode.get(ER.MSG).toString());
                            resultMap.put("mainId", wxmainId);
                            break;

                        case "9300": //支付宝扫码支付
                            account.setThirdRefundState(-1);
                            param.put("money", account.getPrice() / 100.0);
                            param.put("Desc", account.getRegistId() + "-" + account.getRoomNum());
                            Map<String, Object> alipayQrCode = aliPayService.getAlipayQrCode(param);
                            if (alipayQrCode.get(ER.RES).toString().equals(ER.ERR)) {
                                throw new Exception(alipayQrCode.get(ER.MSG).toString());
                            }
                            resultMap.put("qrcode", alipayQrCode.get(ER.MSG));
                            String alimainId = alipayQrCode.get("mainId").toString();
                            account.setThirdAccoutId(alimainId);
                            resultMap.put("mainId", alimainId);
                            account.setAccountCode(alipayQrCode.get(ER.MSG).toString());
                            break;

                        case "9100": //银行卡预授权

                            AccountThirdPayRecode accountThirdPayRecode = new AccountThirdPayRecode();
                            String at = HotelUtils.getHIDUUID32("AT", user.getHid());
                            accountThirdPayRecode.setHid(user.getHid());
                            accountThirdPayRecode.setAccountId(account.getAccountId());
                            accountThirdPayRecode.setHotelGroupId(user.getHotelGroupId());
                            accountThirdPayRecode.setAccountThirdId(at);
                            //款台号
                            accountThirdPayRecode.setCounterId(HotelUtils.validaStr("counterId"));
                            //操作员号
                            accountThirdPayRecode.setOperatorId(HotelUtils.validaStr("operatorId"));
                            //交易编号
                            accountThirdPayRecode.setTransType(HotelUtils.validaStr("transType"));
                            //金额
                            accountThirdPayRecode.setAmount(account.getPrice());
                            //48域附加信息
                            accountThirdPayRecode.setMemo(HotelUtils.validaStr("memo"));
                            //三个校验字符串
                            accountThirdPayRecode.setLrc(HotelUtils.validaStr("lrc"));
                            //终端流水号
                            accountThirdPayRecode.setTrace(HotelUtils.validaStr("trace"));
                            //银行id
                            accountThirdPayRecode.setBarkId(HotelUtils.validaStr("barkId"));
                            //批次号
                            accountThirdPayRecode.setBatch(HotelUtils.validaStr("batch"));
                            //交易日期 yyyyMMdd
                            accountThirdPayRecode.setTransDate(HotelUtils.validaStr("transDate"));
                            //交易时间 hhmmss
                            accountThirdPayRecode.setTransTime(HotelUtils.validaStr("transTime"));
                            //系统参考号
                            accountThirdPayRecode.setRef(HotelUtils.validaStr("ref"));
                            //授权号
                            accountThirdPayRecode.setAuth(HotelUtils.validaStr("auth"));
                            //商户号
                            accountThirdPayRecode.setMid(HotelUtils.validaStr("mId"));
                            //终端号
                            accountThirdPayRecode.setTid(HotelUtils.validaStr(param.get("tId")));
                            //有效期
                            accountThirdPayRecode.setEffectiveDays(HotelUtils.validaStr(param.get("effectiveDays")));
                            //预授权
                            accountThirdPayRecode.setPayType(3);
                            //营业日
                            accountThirdPayRecode.setBusinessDay(user.getBusinessDay());
                            //日期
                            accountThirdPayRecode.setCreateTime(date);
                            accountThirdPayRecode.setClassId(user.getClassId());
                            accountThirdPayRecode.setCreateUserId(user.getUserId());
                            accountThirdPayRecode.setCreateUserName(user.getUserName());

                            Integer integer = accountThirdPayRecodeDao.insert(accountThirdPayRecode);
                            if (integer < 1) {
                                throw new Exception("添加第三方预授权账务失败");
                            }

                            break;

                        case "9600": //会员储值卡

                 /*   int cardId = param.getInt("cardId");
                    CardInfo cardInfo = cardInfoDao.selectById(cardId);

                    if(cardInfo==null){
                        throw new Exception("未查到相关的会员信息");
                    }
                    Integer cardConsuptionRecordId = memberTransactionService.cardConsuptionRecord(user, cardInfo, account, account.getRoomNum() + "：会员支付");
                    account.setThirdAccoutId(cardConsuptionRecordId.toString());
                    account.setMemberId(cardId);*/

                            break;
                    }
                    int result = 0;
                    try {
                        result = accountDao.saveAccount(account);
                    } catch (Exception e) {
                        String uuid = HotelUtils.getUUID();
                        account.setAccountId(uuid);
                        result = accountDao.saveAccount(account);
                    }
                    if (result < 1) {
                        throw new Exception("插入账务信息失败");
                    }
                }
            }


        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return resultMap;
    }


    /**
     * 第三方对接平台退款
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData refundMoneyForOtherPms(final JSONObject param) {


        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*获取操作日志记录*/
            Oprecord oprecord = new Oprecord(user);
            ArrayList<Oprecord> oprecords = new ArrayList<>();

            //退款金额
            int refundMoney = param.getInt("refundMoney");

            /**
             * 2.查询账务信息
             */
            final Account account = accountDao.selectById(param.getString("accountId"));
            if (account == null) {
                throw new Exception("未查到账务信息");
            }
            oprecord.setRegistId(account.getRegistId());
            oprecord.setMainId(account.getAccountId());

            String a = HotelUtils.getHIDUUID32("A", user.getHid());
            Account addRefund = new Account();
            addRefund.setAccountId(a);
            addRefund.setPayType(2);
            addRefund.setRegistId(account.getRegistId());
            addRefund.setBookingId(account.getBookingId());
            addRefund.setBusinessDay(user.getBusinessDay());
            addRefund.setOtherPmsAccountId(account.getAccountId());
            addRefund.setIsCancel(0);
            addRefund.setPayClassName(account.getPayClassName());
            addRefund.setPayClassId(account.getPayClassId());
            addRefund.setUpdateUserName(user.getUserName());
            addRefund.setRegistState(account.getRegistState());
            addRefund.setUpdateTime(new Date());
            addRefund.setUpdateUserName(user.getUserName());
            addRefund.setHid(account.getHid());
            addRefund.setHotelGroupId(account.getHotelGroupId());
            addRefund.setSaleNum(1);
            addRefund.setPrice(refundMoney * -1);
            addRefund.setClassId(user.getClassId());
            addRefund.setUintPrice(refundMoney * -1);
            addRefund.setAccountYear(user.getBusinessYear());
            addRefund.setAccountYearMonth(user.getBusinessMonth());
            addRefund.setCreateTime(new Date());
            addRefund.setCreateUserName(user.getUserName());
            addRefund.setRoomNum(account.getRoomNum());
            addRefund.setGroupAccount(0);


            Map<String, Object> refundMap = new HashMap<>();

            HashMap<String, Object> refundParam = new HashMap<>();

            refundParam.put(ER.SESSION_TOKEN, sessionToken);
            refundParam.put("mainId", account.getThirdAccoutId());
            refundParam.put("refundMoney", refundMoney);


            // 9320 微信
            if (account.getPayCodeId().equals("9320")) {

                oprecord.setDescription("微信扫码支付退款:" + refundMoney / 100.0 + "元。");
                oprecords.add(oprecord);

                addRefund.setPayCodeId("9329");
                addRefund.setPayCodeName("微信扫码支付退款");

                refundMap = weChatPayService.wechatRefund(refundParam);

            } else {

                oprecord.setDescription("支付宝退款:" + refundMoney / 100.0 + "元。");
                oprecords.add(oprecord);

                addRefund.setPayCodeId("9309");
                addRefund.setPayCodeName("支付宝退款");

                refundMap = aliPayService.alipayRefund(refundParam);

            }

            /**
             * 退款失败返回错误信息
             */
            if (refundMap.get(ER.RES).toString().equals(ER.ERR)) {
                throw new Exception(refundMap.get(ER.MSG).toString());
            }

            /**
             * 退款成功进行入账
             */
            addRefund.setRegistId(account.getRegistId());
            addRefund.setRoomTypeId(account.getRoomTypeId());
            addRefund.setRoomInfoId(addRefund.getRoomInfoId());
            addRefund.setRoomNum(addRefund.getRoomNum());
            addRefund.setThirdRefundState(1);
            addRefund.setThirdAccoutId(refundMap.get("refundId") + "");
            accountDao.saveAccount(addRefund);

            /**
             * 修改原账务信息
             */
            account.setThirdRefundState(1);
            account.setRefundPrice(refundMoney * -1);
            accountDao.editAccount(account);

            this.addOprecords(oprecords);

            param.put("registId", account.getRegistId());
            param.put("accountId", account.getAccountId());

            responseData.setData(param);
            /* *//**
             * 查看是否需要向PMS推送数据
             *//*
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    *//**
             * 获取设置信息，查看是否需要推送数据
             *//*
                    try {
                        Map<String, Object> allSetting = hotelSettingService.getAllSetting(param);
                        JSONObject setJson = JSONObject.fromObject(allSetting.get("json"));

                        int paramValue = setJson.getJSONObject(HOTEL_SETTING.REFUND_SEND_PMS).getInt("paramValue");

                        switch (paramValue){

                            case 2:
                                lvyunInterfaceService.addAccount(param);
                                break;
                            default:
                                break;
                        }
                    } catch (Exception e){
                        e.printStackTrace();
                    }

                }
            });*/

            return responseData;

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }


    @Override
    public ResponseData refundMoneyJiaTuiKuan(final JSONObject param) {


        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*获取操作日志记录*/
            Oprecord oprecord = new Oprecord(user);
            ArrayList<Oprecord> oprecords = new ArrayList<>();

            //退款金额
            int refundMoney = param.getInt("refundMoney");

            /**
             * 2.查询账务信息
             */
            final Account account = accountDao.selectById(param.getString("accountId"));
            if (account == null) {
                throw new Exception("未查到账务信息");
            }
            oprecord.setRegistId(account.getRegistId());
            oprecord.setMainId(account.getAccountId());

            String a = HotelUtils.getHIDUUID32("A", user.getHid());
            Account addRefund = new Account();
            addRefund.setAccountId(a);
            addRefund.setPayType(2);
            addRefund.setRegistId(account.getRegistId());
            addRefund.setBookingId(account.getBookingId());
            addRefund.setBusinessDay(user.getBusinessDay());
            addRefund.setOtherPmsAccountId(account.getAccountId());
            addRefund.setIsCancel(0);
            addRefund.setPayClassName(account.getPayClassName());
            addRefund.setPayClassId(account.getPayClassId());
            addRefund.setUpdateUserName(user.getUserName());
            addRefund.setRegistState(account.getRegistState());
            addRefund.setUpdateTime(new Date());
            addRefund.setUpdateUserName(user.getUserName());
            addRefund.setHid(account.getHid());
            addRefund.setHotelGroupId(account.getHotelGroupId());
            addRefund.setSaleNum(1);
            addRefund.setPrice(refundMoney * -1);
            addRefund.setClassId(user.getClassId());
            addRefund.setUintPrice(refundMoney * -1);
            addRefund.setAccountYear(user.getBusinessYear());
            addRefund.setAccountYearMonth(user.getBusinessMonth());
            addRefund.setCreateTime(new Date());
            addRefund.setCreateUserName(user.getUserName());
            addRefund.setRoomNum(account.getRoomNum());
            addRefund.setGroupAccount(0);


            Map<String, Object> refundMap = new HashMap<>();

            HashMap<String, Object> refundParam = new HashMap<>();

            refundParam.put(ER.SESSION_TOKEN, sessionToken);
            refundParam.put("mainId", account.getThirdAccoutId());
            refundParam.put("refundMoney", refundMoney);


            // 9320 微信
            if (account.getPayCodeId().equals("9320")) {

                oprecord.setDescription("微信退款:" + refundMoney / 100.0 + "元。");
                oprecords.add(oprecord);

                addRefund.setPayCodeId("9329");
                addRefund.setPayCodeName("微信退款");

//                refundMap = weChatPayService.wechatRefund(refundParam);

            } else {

                oprecord.setDescription("支付宝退款:" + refundMoney / 100.0 + "元。");
                oprecords.add(oprecord);

                addRefund.setPayCodeId("9309");
                addRefund.setPayCodeName("支付宝退款");

//                refundMap = aliPayService.alipayRefund(refundParam);

            }

            /**
             * 退款失败返回错误信息
             if (refundMap.get(ER.RES).toString().equals(ER.ERR)) {
             throw new Exception(refundMap.get(ER.MSG).toString());
             }
             */
            /**
             * 退款成功进行入账
             */
            addRefund.setRegistId(account.getRegistId());
            addRefund.setRoomTypeId(account.getRoomTypeId());
            addRefund.setRoomInfoId(addRefund.getRoomInfoId());
            addRefund.setRoomNum(addRefund.getRoomNum());
            addRefund.setThirdRefundState(1);
            addRefund.setThirdAccoutId(account.getThirdAccoutId() + "");
            accountDao.saveAccount(addRefund);

            /**
             * 修改原账务信息
             */
            account.setThirdRefundState(1);
            account.setRefundPrice(refundMoney * -1);
            accountDao.editAccount(account);

            this.addOprecords(oprecords);

            param.put("registId", account.getRegistId());
            param.put("accountId", account.getAccountId());

            responseData.setData(param);
            /* *//**
             * 查看是否需要向PMS推送数据
             *//*
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    *//**
             * 获取设置信息，查看是否需要推送数据
             *//*
                    try {
                        Map<String, Object> allSetting = hotelSettingService.getAllSetting(param);
                        JSONObject setJson = JSONObject.fromObject(allSetting.get("json"));

                        int paramValue = setJson.getJSONObject(HOTEL_SETTING.REFUND_SEND_PMS).getInt("paramValue");

                        switch (paramValue){

                            case 2:
                                lvyunInterfaceService.addAccount(param);
                                break;
                            default:
                                break;
                        }
                    } catch (Exception e){
                        e.printStackTrace();
                    }

                }
            });*/

            return responseData;

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    @Override
    public Map<String, Object> refundMoneyForOtherPmsAndPush(final JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*获取操作日志记录*/
            Oprecord oprecord = new Oprecord(user);
            ArrayList<Oprecord> oprecords = new ArrayList<>();

            //退款金额
            int refundMoney = param.getInt("refundMoney");

            /**
             * 2.查询账务信息
             */
            final Account account = accountDao.selectById(param.getString("accountId"));
            if (account == null) {
                throw new Exception("未查到账务信息");
            }
            oprecord.setRegistId(account.getRegistId());
            oprecord.setMainId(account.getAccountId());

            String a = HotelUtils.getHIDUUID32("A", user.getHid());
            Account addRefund = new Account();
            addRefund.setAccountId(a);
            addRefund.setPayType(2);
            addRefund.setRegistId(account.getRegistId());
            addRefund.setBookingId(account.getBookingId());
            addRefund.setBusinessDay(user.getBusinessDay());
            addRefund.setOtherPmsAccountId(account.getAccountId());
            addRefund.setIsCancel(0);
            addRefund.setPayClassName(account.getPayClassName());
            addRefund.setPayClassId(account.getPayClassId());
            addRefund.setUpdateUserName(user.getUserName());
            addRefund.setRegistState(account.getRegistState());
            addRefund.setUpdateTime(new Date());
            addRefund.setUpdateUserName(user.getUserName());
            addRefund.setHid(account.getHid());
            addRefund.setHotelGroupId(account.getHotelGroupId());
            addRefund.setSaleNum(1);
            addRefund.setPrice(refundMoney * -1);
            addRefund.setUintPrice(refundMoney * -1);
            addRefund.setAccountYear(user.getBusinessYear());
            addRefund.setAccountYearMonth(user.getBusinessMonth());
            addRefund.setCreateTime(new Date());
            addRefund.setCreateUserName(user.getUserName());
            addRefund.setRoomNum(account.getRoomNum());
            addRefund.setGroupAccount(0);

            Map<String, Object> refundMap = new HashMap<>();

            HashMap<String, Object> refundParam = new HashMap<>();

            refundParam.put(ER.SESSION_TOKEN, sessionToken);
            refundParam.put("mainId", account.getThirdAccoutId());
            refundParam.put("refundMoney", refundMoney);


            // 9320 微信
            if (account.getPayCodeId().equals("9320")) {

                oprecord.setDescription("微信退款:" + refundMoney / 100.0 + "元。");
                oprecords.add(oprecord);

                addRefund.setPayCodeId("9329");
                addRefund.setPayCodeName("微信退款");

                refundMap = weChatPayService.wechatRefund(refundParam);

            } else {

                oprecord.setDescription("支付宝退款:" + refundMoney / 100.0 + "元。");
                oprecords.add(oprecord);

                addRefund.setPayCodeId("9309");
                addRefund.setPayCodeName("支付宝退款");

                refundMap = aliPayService.alipayRefund(refundParam);

            }

            /**
             * 退款失败返回错误信息
             */
            if (refundMap.get(ER.RES).toString().equals(ER.ERR)) {
                throw new Exception(refundMap.get(ER.MSG).toString());
            }

            /**
             * 退款成功进行入账
             */
            addRefund.setThirdRefundState(1);
            addRefund.setThirdAccoutId(refundMap.get("refundId") + "");
            accountDao.saveAccount(addRefund);

            /**
             * 修改原账务信息
             */
            account.setThirdRefundState(1);
            account.setRefundPrice(refundMoney * -1);
            accountDao.editAccount(account);

            this.addOprecords(oprecords);

            param.put("registId", account.getRegistId());
            param.put("accountId", account.getAccountId());
            /**
             * 查看是否需要向PMS推送数据*/

           /* HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    *//*       *
             * 获取设置信息，查看是否需要推送数据
             *//*
                    try {
                        Map<String, Object> allSetting = hotelSettingService.getAllSetting(param);
                        JSONObject setJson = JSONObject.fromObject(allSetting.get("json"));

                        int paramValue = setJson.getJSONObject(HOTEL_SETTING.REFUND_SEND_PMS).getInt("paramValue");

                        switch (paramValue){
                            case 1:
                                //  xiRuanService.refundForXiRuanPms(param);
                                break;
                            case 2:
                                //   lvyunInterfaceService.addAccount(param);
                                break;

                            default:
                                break;
                        }
                    } catch (Exception e){
                        e.printStackTrace();
                    }

                }
            });*/

            return resultMap;

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    @Override
    public ResponseData updateAccountParam(UpdateAccountRequest updateAccountRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            final TbUserSession user = this.getTbUserSession(updateAccountRequest);
            /**
             * 2.查询账务信息
             */
            final Account account = accountDao.selectById(updateAccountRequest.getAccountId());
            if (account == null) {
                throw new Exception("未查到账务信息");
            }
            if (null != updateAccountRequest.getRemark()) {
                account.setRemark(updateAccountRequest.getRemark());
            }
//            if (null != updateAccountRequest.getThirdAccoutId()) {
//                account.setThirdAccoutId(updateAccountRequest.getThirdAccoutId());
//            }
            Integer integer = accountDao.editAccount(account);
            if (integer < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    public Map<String, Object> addAccount(JSONObject param) {


        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*获取操作日志记录*/
            Oprecord oprecord = new Oprecord(user);

            Date date = new Date();

            //金额
            int money = param.getInt("money");

            String a = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
            Account account = new Account();
            account.setAccountId(a);
            account.setHid(user.getHid());
            account.setCreateUserId(user.getUserId());
            account.setCreateUserName(user.getUserName());
            account.setCreateTime(new Date());
            account.setIsCancel(0);
            account.setAccountYear(user.getBusinessYear());
            account.setAccountYearMonth(user.getBusinessMonth());
            account.setBusinessDay(user.getBusinessDay());
            account.setClassId(user.getClassId());
            account.setPrice(money);
            account.setSettleAccountTime(new Date());

            account.setThirdRefundState(0);
            //类型 ：消费、付款
            int payType = param.getInt("payType");
            account.setPayType(payType);

            if (param.get("saleNum") != null) {
                account.setSaleNum(param.getInt("saleNum"));
            } else {
                account.setSaleNum(1);
            }
            account.setUintPrice(money / account.getSaleNum());
            //费用码
            int costClassId = param.getInt("costClassId");
            String costClassName = URLDecoder.decode(param.getString("costClassName"), "utf-8");
            String costCodeId = param.getString("costCodeId");
            String costCodeName = URLDecoder.decode(param.getString("costCodeName"), "utf-8");

            account.setPayClassId(costClassId);
            account.setPayClassName(costClassName);
            account.setPayCodeId(costCodeId);
            account.setPayCodeName(costCodeName);
            //设置账务关联人id为0
            account.setRegistPersonId(0);

            Object accountType = param.get("accountType");
            if (accountType != null) {
                account.setAccountType(Integer.parseInt(accountType.toString()));
            }

            //登记账单id
            Object registIdObj = param.get("registId");
            if (registIdObj != null) {

               /* int registId = Integer.parseInt(registIdObj.toString());
                Regist regist = registDao.selectById(registId);
                if(regist==null||!regist.getHid().equals(user.getHid())){
                    throw new Exception("未找到相关的入住记录");
                }
                if(regist.getState()!=0&&regist.getState()!=4) {
                    throw new Exception("当前房间状态不允许入账");
                }
                account.setRegistId(registId);
                account.setRoomNum(regist.getRoomNum());
                account.setRoomInfoId(regist.getRoomNumId());
                account.setRegistState(regist.getState());*/
                account.setRegistId(Integer.parseInt(registIdObj.toString()));
                account.setRoomNum(param.getString("roomNum"));
                account.setRoomInfoId(param.getInt("roomNumId"));
                account.setRegistState(param.getInt("registState"));
            }

            Object registPersonId = param.get("registPersonId");
            if (registPersonId != null) {
                account.setRegistPersonId(Integer.parseInt(registPersonId.toString()));
                account.setAccountCode(URLDecoder.decode(param.getString("accountCode")));
            }


            Object bookingIdObject = param.get("bookingId");
            if (bookingIdObject != null && !bookingIdObject.toString().equals("")) {
                int bookingId = Integer.parseInt(bookingIdObject.toString());
//                BookingOrder bookingOrder = bookingOrderDao.selectById(bookingId);
//                if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())){
//                    throw new Exception("为查询到预订单");
//                }
//                if (bookingOrder.getOrderStatus() != 1){
//                    throw new Exception("当前订单无法入账");
//                }
                account.setBookingId(bookingId);
                account.setRegistState(0);
            }


            if (param.get("accountType") != null) {
                account.setAccountType(param.getInt("accountType"));
            }


            /**
             * 根据不同的费用码判断进行不同的操作
             *  微信/支付宝 获取 扫码账单
             */
            switch (costCodeId) {
                case "9320": //微信扫码支付
                    param.put("money", account.getPrice() / 100.0);
                    param.put("Desc", account.getRegistId() + "-" + account.getRoomNum());
                    account.setThirdRefundState(-1);
                    Map<String, Object> weChatQrcode = weChatPayService.getWeChatQrcode(param);
                    if (weChatQrcode.get(ER.RES).toString().equals(ER.ERR)) {
                        throw new Exception(weChatQrcode.get(ER.MSG).toString());
                    }
                    resultMap.put("qrcode", weChatQrcode.get(ER.MSG));
                    String wxmainId = weChatQrcode.get("mainId").toString();
                    account.setThirdAccoutId(wxmainId);
                    account.setAccountCode(weChatQrcode.get(ER.MSG).toString());
                    resultMap.put("mainId", wxmainId);
                    break;

                case "9300": //支付宝扫码支付
                    account.setThirdRefundState(-1);
                    param.put("money", account.getPrice() / 100.0);
                    param.put("Desc", account.getRegistId() + "-" + account.getRoomNum());
                    Map<String, Object> alipayQrCode = aliPayService.getAlipayQrCode(param);
                    if (alipayQrCode.get(ER.RES).toString().equals(ER.ERR)) {
                        throw new Exception(alipayQrCode.get(ER.MSG).toString());
                    }
                    resultMap.put("qrcode", alipayQrCode.get(ER.MSG));
                    String alimainId = alipayQrCode.get("mainId").toString();
                    account.setThirdAccoutId(alimainId);
                    resultMap.put("mainId", alimainId);
                    account.setAccountCode(alipayQrCode.get(ER.MSG).toString());
                    break;

                case "9100": //银行卡预授权

                    AccountThirdPayRecode accountThirdPayRecode = new AccountThirdPayRecode();
                    String at = HotelUtils.getHIDUUID32("AT", user.getHid());
                    accountThirdPayRecode.setHid(user.getHid());
                    accountThirdPayRecode.setAccountId(account.getAccountId());
                    accountThirdPayRecode.setHotelGroupId(user.getHotelGroupId());
                    accountThirdPayRecode.setAccountThirdId(at);
                    //款台号
                    accountThirdPayRecode.setCounterId(HotelUtils.validaStr("counterId"));
                    //操作员号
                    accountThirdPayRecode.setOperatorId(HotelUtils.validaStr("operatorId"));
                    //交易编号
                    accountThirdPayRecode.setTransType(HotelUtils.validaStr("transType"));
                    //金额
                    accountThirdPayRecode.setAmount(account.getPrice());
                    //48域附加信息
                    accountThirdPayRecode.setMemo(HotelUtils.validaStr("memo"));
                    //三个校验字符串
                    accountThirdPayRecode.setLrc(HotelUtils.validaStr("lrc"));
                    //终端流水号
                    accountThirdPayRecode.setTrace(HotelUtils.validaStr("trace"));
                    //银行id
                    accountThirdPayRecode.setBarkId(HotelUtils.validaStr("barkId"));
                    //批次号
                    accountThirdPayRecode.setBatch(HotelUtils.validaStr("batch"));
                    //交易日期 yyyyMMdd
                    accountThirdPayRecode.setTransDate(HotelUtils.validaStr("transDate"));
                    //交易时间 hhmmss
                    accountThirdPayRecode.setTransTime(HotelUtils.validaStr("transTime"));
                    //系统参考号
                    accountThirdPayRecode.setRef(HotelUtils.validaStr("ref"));
                    //授权号
                    accountThirdPayRecode.setAuth(HotelUtils.validaStr("auth"));
                    //商户号
                    accountThirdPayRecode.setMid(HotelUtils.validaStr("mId"));
                    //终端号
                    accountThirdPayRecode.setTid(HotelUtils.validaStr(param.get("tId")));
                    //有效期
                    accountThirdPayRecode.setEffectiveDays(HotelUtils.validaStr(param.get("effectiveDays")));
                    //预授权
                    accountThirdPayRecode.setPayType(3);
                    //营业日
                    accountThirdPayRecode.setBusinessDay(user.getBusinessDay());
                    //日期
                    accountThirdPayRecode.setCreateTime(date);
                    accountThirdPayRecode.setClassId(user.getClassId());
                    accountThirdPayRecode.setCreateUserId(user.getUserId());
                    accountThirdPayRecode.setCreateUserName(user.getUserName());

                    Integer integer = accountThirdPayRecodeDao.insert(accountThirdPayRecode);
                    if (integer < 1) {
                        throw new Exception("添加第三方预授权账务失败");
                    }

                    break;

                case "9600": //会员储值卡

                 /*   int cardId = param.getInt("cardId");
                    CardInfo cardInfo = cardInfoDao.selectById(cardId);

                    if(cardInfo==null){
                        throw new Exception("未查到相关的会员信息");
                    }
                    Integer cardConsuptionRecordId = memberTransactionService.cardConsuptionRecord(user, cardInfo, account, account.getRoomNum() + "：会员支付");
                    account.setThirdAccoutId(cardConsuptionRecordId.toString());
                    account.setMemberId(cardId);*/

                    break;
            }

            //备注
            if (param.get("remark") != null) {
                String remark = URLDecoder.decode(param.getString("remark"), "utf-8");
                account.setRemark(remark);
            }

            //理由
            account.setReason(HotelUtils.validaStr(param.get("reason")));
            account.setRefundPrice(0);
            account.setGroupAccount(0);

            accountDao.saveAccount(account);

            oprecord.setMainId(account.getAccountId());

            oprecord.setDescription(account.getPayCodeName() + ",数量:" + account.getSaleNum() + ",总金额为:" + account.getPrice() / 100.0 + "元");

            resultMap.put("account", account);

            return resultMap;

        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }


    @Override
    public ResponseData searchIndexAccount(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setClassId(user.getClassId());
            accountSearch.setIsCancel(0);
            accountSearch.setPayType(2);
            accountSearch.setBusinessDay(user.getBusinessDay());
            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            HashMap<String, Integer> accountMap = new HashMap<>();
            accountMap.put("9000", 0); // 现金
            accountMap.put("9400", 0); // 预授权
            accountMap.put("1", 0);    // 其他收款

            for (Account account : accounts) {
                String payCodeId = account.getPayCodeId();
                switch (payCodeId) {
                    case "9000":
                        accountMap.put(payCodeId, accountMap.get(payCodeId) + account.getPrice());
                        break;
                    case "9400":
                        accountMap.put(payCodeId, accountMap.get(payCodeId) + account.getPrice());
                        break;
                    default:
                        accountMap.put("1", accountMap.get("1") + account.getPrice());
                        break;
                }
            }

            responseData.setData(accountMap);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public Page<Account> searchAccountList(AccountSearchRequest accountSearchRequest) throws Exception {
        String sessionToken = accountSearchRequest.getSessionToken();
        TbUserSession tbUserSession = getTbUserSession(sessionToken);
        accountSearchRequest.setHid(tbUserSession.getHid());
        return accountDao.searchAccountList(accountSearchRequest);
    }

    @Override
    public ResponseData selectNotCollection(AccountSearchRequest accountSearchRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            final TbUserSession user = this.getTbUserSession(accountSearchRequest);

            Date date = new Date();

            Date date1 = new Date(date.getTime() - 7200000);

            SysWechatPaySearch sysWechatPaySearch = new SysWechatPaySearch();
            sysWechatPaySearch.setHid(user.getHid());
            sysWechatPaySearch.setPayTime(date1);

            List<SysWechatPay> sysWechatPays = wechatPayDao.selectNotCollection(sysWechatPaySearch);

            AlipayFaceTransactionSearch alipayFaceTransactionSearch = new AlipayFaceTransactionSearch();
            alipayFaceTransactionSearch.setHid(user.getHid());
            alipayFaceTransactionSearch.setPayTime(date1);
            List<AlipayFaceTransaction> alipayFaceTransactions = alipayFaceTransactionDao.selectNotCollection(alipayFaceTransactionSearch);


            SqbTransactionSearch sqbTransactionSearch = new SqbTransactionSearch();
            sqbTransactionSearch.setHid(user.getHid());
            sqbTransactionSearch.setPayTime(date1);
            List<SqbTransaction> sqbTransactions = sqbTransactionDao.selectNotCollection(sqbTransactionSearch);

            JSONArray jsonArray = new JSONArray();

            for (SysWechatPay wp : sysWechatPays) {
                JSONObject jo = new JSONObject();
                jo.put("mainId", wp.getMainId());
                jo.put("date", HotelUtils.parseDate2Str(wp.getPayTime()));
                jo.put("payUser", wp.getOpenId());
                jo.put("transactionId", wp.getTransactionId());
                jo.put("money", wp.getMoneyFen());
                jo.put("type", 1);
                jo.put("typeName", "微信");
                jsonArray.add(jo);
            }

            for (AlipayFaceTransaction wp : alipayFaceTransactions) {
                JSONObject jo = new JSONObject();
                jo.put("mainId", wp.getMainId());
                jo.put("date", HotelUtils.parseDate2Str(wp.getPayTime()));
                jo.put("payUser", wp.getBuyerLogonId());
                jo.put("transactionId", wp.getTradeNo());
                jo.put("money", wp.getMoney());
                jo.put("type", 2);
                jo.put("typeName", "支付宝");
                jsonArray.add(jo);
            }

            for (SqbTransaction st : sqbTransactions) {
                Integer type = 2;
                if (st.getPaywayName().equals("微信")) {
                    type = 1;
                }
                JSONObject jo = new JSONObject();
                jo.put("mainId", st.getMainId());
                jo.put("date", HotelUtils.parseDate2Str(st.getPayTime()));
                jo.put("payUser", st.getBuyerUserId());
                jo.put("transactionId", st.getTradeNo());
                jo.put("money", st.getMoney());
                jo.put("type", type);
                jo.put("typeName", st.getPaywayName());
                jsonArray.add(jo);
            }

            responseData.setData(jsonArray);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData accountResetFunc(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            // 账务id
            String accountId = param.getString("accountId");

            Account account = accountDao.selectById(accountId);
            if (account == null || !account.getHotelGroupId().equals(user.getHotelGroupId())) {
                throw new Exception("未查询到账务信息");
            }

            // 不是未结状态，则不允许重置
            if (account.getRegistState() != 0 || account.getIsCancel() != 0) {
                throw new Exception("当前账单状态不是未结");
            }

            int type = 0;

            Integer payClassId = account.getPayClassId();

            AccountReset accountReset = new AccountReset();
            accountReset.setHid(user.getHid());
            accountReset.setHotelGroupId(user.getHotelGroupId());
            accountReset.setMoney(account.getRefundPrice());
            accountReset.setCreateTime(new Date());
            accountReset.setCreateUserId(user.getUserId());
            accountReset.setCreateUserName(user.getUserName());
            accountReset.setMainId(account.getThirdAccoutId());

            Oprecord oprecord = new Oprecord(user);
            oprecord.setRegistId(account.getRegistId());
            oprecord.setRoomNum(account.getRoomNum());
            oprecord.setMainId(account.getThirdAccoutId());
            oprecord.setChangedValue(account.getRefundPrice() / 100.0 + "");
            oprecord.setSourceValue("0");

            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();

            switch (payClassId) {
                case 12:
                    type = 2;
                    SysWechatPaySearch sysWechatPaySearch = new SysWechatPaySearch();
                    sysWechatPaySearch.setMainId(account.getThirdAccoutId());
                    sysWechatPaySearch.setHid(user.getHid());

                    List<SysWechatPay> sysWechatPays = wechatPayDao.selectBySearch(sysWechatPaySearch);

                    if (sysWechatPays == null || sysWechatPays.size() < 1) {
                        throw new Exception("未查询到微信支付信息");
                    }

                    SysWechatPay sysWechatPay = sysWechatPays.get(0);

                    if (sysWechatPay.getPayStatus() < 1 || sysWechatPay.getPayStatus() == 3) {
                        throw new Exception("状态不可用");
                    }

                    sysWechatPay.setPayStatus(1);
                    sysWechatPay.setRefundMoney(0);

                    Integer upawe = wechatPayDao.editSysWechatPay(sysWechatPay);


                    if (upawe < 1) {
                        throw new Exception("重置退款状态失败");
                    }

                    userCahe.delete("hotelWechatPay", account.getThirdAccoutId());

                    oprecord.setDescription("微信支付账单：" + account.getAccountId() + " 已款" + account.getRefundPrice() / 100.0 + "元，进行退款状态重置。");

                    break;
                case 11:
                    type = 1;
                    AlipayFaceTransactionSearch alipayFaceTransactionSearch = new AlipayFaceTransactionSearch();
                    alipayFaceTransactionSearch.setMainId(account.getThirdAccoutId());
                    alipayFaceTransactionSearch.setHid(user.getHid());
                    List<AlipayFaceTransaction> alipayFaceTransactions = alipayFaceTransactionDao.selectBySearch(alipayFaceTransactionSearch);

                    if (alipayFaceTransactions == null || alipayFaceTransactions.size() < 1) {
                        throw new Exception("未查询到支付宝支付信息");
                    }

                    AlipayFaceTransaction alipayFaceTransaction = alipayFaceTransactions.get(0);


                    if (alipayFaceTransaction.getStatus() < 1 || alipayFaceTransaction.getStatus() == 3) {
                        throw new Exception("状态不可用");
                    }

                    alipayFaceTransaction.setStatus(1);
                    alipayFaceTransaction.setRefundMoney(0);

                    Integer upaal = alipayFaceTransactionDao.editAlipayFaceTransaction(alipayFaceTransaction);

                    if (upaal < 1) {
                        throw new Exception("重置退款状态失败");
                    }

                    userCahe.delete("hotelaliPay", account.getThirdAccoutId());

                    oprecord.setDescription("支付宝账单：" + account.getAccountId() + " 已款" + account.getRefundPrice() / 100.0 + "元，进行退款状态重置。");
                    break;
                default:
                    throw new Exception("非第三方支付类型账务");
            }


            accountReset.setType(type);

            accountResetDao.insert(accountReset);


            account.setThirdRefundState(0);
            account.setRefundPrice(0);

            accountDao.editAccount(account);

            this.addOprecords(oprecord);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData accountSummary(AccountSummarySearch param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            final TbUserSession user = this.getTbUserSession(param);

            List<AccountSummary> accountSummaries = accountDao.accountSummary(param);

            responseData.setData(accountSummaries);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData splitAccount(SplitAccountReset splitAccountReset) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            final TbUserSession user = this.getTbUserSession(splitAccountReset);

            // 日志
            Oprecord oprecord = new Oprecord(user);
            // 13 代表分账
            oprecord.setType(13);

            String accountId = splitAccountReset.getAccountId();

            Account account = accountDao.selectById(accountId);

            if (account == null || account.getIsCancel() != 0) {
                throw new Exception("当前账务状态不允许分账");
            }

            Integer payType = account.getPayType();

//            if (payType != 1) {
//                throw new Exception("只允许拆分消费");
//            }

            if (account.getPayCodeId().equals("'9300'") || account.getPayCodeId().equals("'9320'") || account.getPayCodeId().equals("'9340'") || account.getPayCodeId().equals("'9600'") || account.getPayCodeId().equals("'9600'") || account.getPayCodeId().equals("'9620'") || account.getPayCodeId().equals("'9630'") || account.getPayCodeId().equals("'9100'") || account.getPayCodeId().equals("'9800'")) {
                throw new Exception("当前付款方式不允许拆账");
            }

            // 需要拆分的金额
            Integer money = splitAccountReset.getMoney();
            Integer price = account.getPrice();

            if (money >= price) {

                throw new Exception("分账金额不得大于等于账单金额");

            }

            oprecord.setRegistId(account.getRegistId());
            oprecord.setRoomNum(account.getRoomNum());
            oprecord.setMainId(accountId);
            oprecord.setBookingOrderId(account.getBookingId());
            oprecord.setSourceValue(JSONObject.fromObject(account).toString());

            Account newAccount = new Account();

            BeanUtils.copyProperties(account, newAccount);

            newAccount.setRemark("由：" + accountId + "账单拆出。");
            if (splitAccountReset.getRemark() != null) {
                newAccount.setRemark(splitAccountReset.getRemark());
            }

            // 新账务处理
            int diff = price - money;
            newAccount.setPrice(money);
            newAccount.setUintPrice(money);
            if (splitAccountReset.getRegistPersonId() == null) {
                splitAccountReset.setRegistPersonId(0);
            }
            newAccount.setRegistPersonId(splitAccountReset.getRegistPersonId());
            newAccount.setRegistPersonName(splitAccountReset.getPersonName());

            String no = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
            // 新账务
            newAccount.setAccountId(no);
            Integer integer = accountDao.saveAccount(newAccount);

            if (integer < 1) {
                throw new Exception("添加新账务失败");
            }

            // 老账务修改

            account.setPrice(diff);
            account.setUintPrice(diff);
            account.setAccountId(accountId);

            Integer integer1 = accountDao.editAccount(account);
            if (integer1 < 1) {
                throw new Exception("拆分老账务失败");
            }

            oprecord.setDescription(accountId + "账单拆出，拆出金额：" + money / 100.0 + "，入账账户:" + splitAccountReset.getPersonName() + ",单号：" + no);
            this.addOprecords(oprecord);

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
