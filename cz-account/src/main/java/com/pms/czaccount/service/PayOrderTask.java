package com.pms.czaccount.service;

import com.pms.czaccount.bean.pay.PayOrder;
import com.pms.czaccount.dao.pay.PayOrderDao;
import com.pms.czaccount.service.wechat.WeChatPayService;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
public class PayOrderTask implements ApplicationRunner {

    private static final Logger LOGGER = LoggerFactory.getLogger(PayOrderTask.class);

    @Autowired
    private WeChatPayService weChatPayService;

    public static ScheduledExecutorService payOrderDealPool = new ScheduledThreadPoolExecutor(16,
            new BasicThreadFactory.Builder().namingPattern("payOrderDeal-%d").daemon(true).build());

    @Resource(name = "stringRedisTemplate")
    protected StringRedisTemplate stringRedisTemplate;

    @Resource
    PayOrderDao payOrderDao;

    public static final long MINUTES = 60 * 1000;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        new Thread(() -> {
            while (true) {
                List<PayOrder> payOrders = payOrderDao.selectPayOrderList();
                for (int i = 0; i < payOrders.size(); i++) {
                    final PayOrder payOrder = payOrders.get(i);
                    payOrderDealPool.execute(() -> {
                        String key = String.format("DealPayOrder:%d", payOrder.id);
                        if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(key, "", 10, TimeUnit.SECONDS))) {
                            LOGGER.info(String.format("DealPayOrder   skip, id:%d, mainId:%s", payOrder.id, payOrder.mainId));
                        } else {
                            try {
                                LOGGER.info(String.format("DealPayOrder id:%d, mainId:%s", payOrder.id, payOrder.mainId));
                                boolean paySuccess = weChatPayService.dealPayOrder(payOrder);
                                if (paySuccess) {
                                    //4.调用业务处理方法
                                    Map<String, Object> result = weChatPayService.addAccount(payOrder.addAccountParam, payOrder.sessionToken, payOrder.mainId);
                                    LOGGER.info("PayOrderTask addAccount rt:" + result);
                                }

                                //如果超过1分钟, 直接改状态为:超时
                                if (System.currentTimeMillis() - payOrder.getCreateTime().getTime() > 3 * MINUTES && payOrder.status == 1) {
                                    payOrderDao.updatePayOrderStatus(payOrder.id, 4);
                                    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                    LOGGER.info(String.format("DealPayOrder id:%d, mainId:%s, 订单超时,订单创建时间:%s 当前时间:%s", payOrder.id, payOrder.mainId, df.format(payOrder.createTime), df.format(new Date())));
                                }

                            } catch (Exception e) {
                                e.printStackTrace();
                            } finally {
                                stringRedisTemplate.delete(key);
                            }
                        }
                    });
                }

                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }


}
