package com.pms.czaccount.service.shouqianba;

import com.github.pagehelper.Page;
import com.pms.czaccount.bean.PayAgentInfo;
import com.pms.czaccount.bean.SqbRefund;
import com.pms.czaccount.bean.SqbTransaction;
import com.pms.czaccount.bean.search.PayAgentInfoSearch;
import com.pms.czaccount.bean.search.SqbTransactionSearch;
import com.pms.czaccount.dao.pay.PayAgentInfoDao;
import com.pms.czaccount.dao.pay.SqbRefundDao;
import com.pms.czaccount.dao.pay.SqbTransactionDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.paymsg.PAY_RES;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.TimeUnit;


@Service
@Primary
@Slf4j
public class ShouQianBaService extends BaseService {


    @Resource(name = "stringRedisTemplate")
    protected StringRedisTemplate stringRedisTemplate;

    private static String api_domain = "https://vsi-api.shouqianba.com";
    private final static String CHARSET_UTF8 = "utf8";


    /**
     * 上海崇泽
     * * @param vendor_sn:服务商序列号
     * * @param vendor_key:服务商密钥
     * * @param app_id:应用编号
     */
    //需要修改服务商的时候 修改此参数即可 --此四个对应苏州客户
    private final static String VENDOR_KEY = "9775b293b8af99002d3b7c567e5f0a3c";
    private final static String APP_ID_BC = "2022051800004774";
    private final static String APP_ID_CB = "2022051800004775";
    private final static String VENDOR_SN = "91801550";


//    /** 苏州吴总
//     * * @param vendor_sn:服务商序列号
//     * * @param vendor_key:服务商密钥
//     * * @param app_id:应用编号
//     */
//    //需要修改服务商的时候 修改此参数即可 --此四个对应苏州客户
//    private final static String VENDOR_KEY = "f2cc83ce1b21eff9abd7ebf8f45e2e23";
//    private final static String APP_ID_BC = "2021110100004287";
//    private final static String APP_ID_CB = "2021110100004288";
//    private final static String VENDOR_SN = "91801435";


    //    /** 西安程总
//     * * @param vendor_sn:服务商序列号
//     * * @param vendor_key:服务商密钥
//     * * @param app_id:应用编号
//     */
    //需要修改服务商的时候 修改此参数即可 --此四个对应苏州客户
//    private final static String VENDOR_KEY = "b2ccb4a6632ed3223d85ce7ddbc79b3a";
//    private final static String APP_ID_BC = "2022082700004996";
//    private final static String APP_ID_CB = "2022082700004997";
//    private final static String VENDOR_SN = "91801611";


    private static String getClient_Sn(int codeLenth) {
        while (true) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < codeLenth; i++) {
                if (i == 0)
                    sb.append(new Random().nextInt(9) + 1); // first field will not start with 0.
                else
                    sb.append(new Random().nextInt(10));
            }
            return sb.toString();
        }
    }

    private static String getSign(String signStr) {
        try {
            String md5 = MD5Util.encryptMd5(signStr);
            return md5;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Resource
    private PayAgentInfoDao payAgentInfoDao;


    public PayAgentInfo getPayAgentInfo(Integer hid) throws Exception {
        PayAgentInfoSearch payAgentInfoSearch = new PayAgentInfoSearch();
        payAgentInfoSearch.setHid(hid);
        Page<PayAgentInfo> payAgentInfos = payAgentInfoDao.selectBySearch(payAgentInfoSearch);
        if (!payAgentInfos.isEmpty()) {
            PayAgentInfo payAgentInfo = payAgentInfos.get(0);
            if (payAgentInfo.getSqbBcTerminalKey() == null
                    || payAgentInfo.getSqbBcTerminalSn() == null
                    || payAgentInfo.getSqbCbTerminalSn() == null
                    || payAgentInfo.getSqbCbTerminalKey() == null
            ) {
                String key = String.format("activity_%s_%d", payAgentInfo.getSqbActiveCode(), hid);
                if (stringRedisTemplate.opsForValue().setIfAbsent(key, "1", 5, TimeUnit.SECONDS)) {
                    activity(payAgentInfo);
                    stringRedisTemplate.delete(key);
                } else {
                    Thread.sleep(1000);
                    payAgentInfos = payAgentInfoDao.selectBySearch(payAgentInfoSearch);
                    if (!payAgentInfos.isEmpty()) {
                        payAgentInfo = payAgentInfos.get(0);
                    }
                }
            }
            return payAgentInfo;
        }
        return null;
    }

    private void activity(PayAgentInfo payAgentInfo) throws Exception {
        //激活
        JSONObject activateCb = activate(payAgentInfo.getSqbActiveCode(), false);
        JSONObject activateBc = activate(payAgentInfo.getSqbActiveCode(), true);
        if (activateCb == null || activateBc == null) {
            throw new Exception("激活终端失败!");
        }
        payAgentInfo.setSqbCbTerminalKey(activateCb.get("terminal_key").toString());
        payAgentInfo.setSqbCbTerminalSn(activateCb.get("terminal_sn").toString());

        payAgentInfo.setSqbBcTerminalKey(activateBc.get("terminal_key").toString());
        payAgentInfo.setSqbBcTerminalSn(activateBc.get("terminal_sn").toString());

        PayAgentInfo payAgentInfo1 = new PayAgentInfo();
        payAgentInfo1.setId(payAgentInfo.getId());
        payAgentInfo1.setSqbCbTerminalSn(payAgentInfo.getSqbCbTerminalSn());
        payAgentInfo1.setSqbCbTerminalKey(payAgentInfo.getSqbCbTerminalKey());
        payAgentInfo1.setSqbBcTerminalKey(payAgentInfo.getSqbBcTerminalKey());
        payAgentInfo1.setSqbBcTerminalSn(payAgentInfo.getSqbBcTerminalSn());
        payAgentInfoDao.update(payAgentInfo1);
    }

    /**
     * 终端激活
     *
     * @param code:激活码
     * @return {terminal_sn:"$终端号",terminal_key:"$终端密钥"}
     */
    public JSONObject activate(String code, boolean is_BC) {
        String url = api_domain + "/terminal/activate";
        JSONObject params = new JSONObject();
        try {
            params.put("app_id", is_BC ? APP_ID_BC : APP_ID_CB);                                  //app_id，必填
            params.put("code", code);                                     //激活码，必填
            params.put("device_id", UUID.randomUUID().toString());                     //客户方收银终端序列号，需保证同一app_id下唯一，必填。为方便识别，建议格式为“品牌名+门店编号+‘POS’+POS编号“
            //PARAMS.put("client_sn", UUID.randomUUID().toString());                             //客户方终端编号，一般客户方或系统给收银终端的编号，必填
            String sign = getSign(params.toString() + VENDOR_KEY);
            String result = HttpUtil.httpPost(url, params.toString(), sign, VENDOR_SN);
            log.info("activate result: {}", result);
            JSONObject retObj = new JSONObject(result);
            String resCode = retObj.get("result_code").toString();
            if (!resCode.equals("200"))
                return null;
            String responseStr = retObj.get("biz_response").toString();
            JSONObject terminal = new JSONObject(responseStr);
            if (terminal.get("terminal_sn") == null || terminal.get("terminal_key") == null)
                return null;
            return terminal;
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 生成收钱吧二维码
     *
     * @param map
     * @return
     */
    public Map<String, Object> getSQBQrCode(Map<String, Object> map) {

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*1.查询账号信息 及验资参数是否合法*/
            PayAgentInfo payAgentInfo = getPayAgentInfo(user.getHid());
            if (payAgentInfo == null || payAgentInfo.getSqbActiveCode() == null
                    || payAgentInfo.getSqbCbTerminalKey() == null
                    || payAgentInfo.getSqbCbTerminalSn() == null) {
                throw new Exception(PAY_RES.NO_SQB_MSG);
            }
            String url = api_domain + "/upay/v2/precreate";

            double moneyYuan = Double.parseDouble(map.get("money").toString());
            Integer money = (int) (moneyYuan * 100);

            JSONObject params = new JSONObject();
            params.put("terminal_sn", payAgentInfo.getSqbCbTerminalSn());           //收钱吧终端ID
            //账单号
            String mainId = HotelUtils.getHIDUUID32("SQB", user.getHid());
            params.put("client_sn", mainId); //商户系统订单号,必须在商户系统内唯一；且长度不超过32字节
            params.put("total_amount", money.toString());               //交易总金额
            params.put("notify_url", "https://czpms.cn/hotel/pay/sqbPayNotify.do");
            /**
             payway	payway_name	note
             1、2	支付宝	支付宝支付
             3	微信	微信支付
             9	和包支付	*
             17	银联云闪付	*
             18	翼支付	*
             19	Weixin-Local	境外微信本地钱包收款
             22	索迪斯预付卡	*
             23	数字人民币	*
             101	储值卡	*
             */
            params.put("payway", map.getOrDefault("payway", "1"));               //支付方式
            params.put("subject", "酒店支付");                       //交易简介
            params.put("operator", user.getUserName());                     //门店操作员
            /**
             1	条码支付
             2	二维码支付
             3	wap支付
             4	小程序支付
             5	APP支付
             6	H5支付
             */
            params.put("sub_payway", "2");                     //二级支付方式
            String sign = getSign(params.toString() + payAgentInfo.getSqbCbTerminalKey());
            //sessionToken放入Redis, 回调的时候用
            stringRedisTemplate.opsForValue().set("SESSION_TOKEN:" + mainId, sessionToken, 60L, TimeUnit.MINUTES);
            stringRedisTemplate.opsForValue().set("IS_QR:" + mainId, "1", 60L, TimeUnit.MINUTES);

            String result = HttpUtil.httpPost(url, params.toString(), sign, payAgentInfo.getSqbCbTerminalSn());
            /**
             {
             "result_code": "200",
             "biz_response": {
             "result_code": "PRECREATE_SUCCESS",
             "data": {
             "sn": "7894259244096169",
             "client_sn": "765432112",
             "status": "IN_PROG",
             "order_status": "CREATED",
             "total_amount": "1",
             "net_amount": "1",
             "operator ": "张三丰",
             "subject ": "coca cola",
             "qr_code": "https://qr.alipay.com/bax8z75ihyoqpgkv5f"
             }
             }
             }
             */
            boolean success = false;
            Object qrCode = null;
            mainId = null;
            Object sn = null;
            net.sf.json.JSONObject reqjson = net.sf.json.JSONObject.fromObject(result);
            if (reqjson.containsKey("biz_response")) {
                net.sf.json.JSONObject bizResponse = reqjson.getJSONObject("biz_response");
                if (bizResponse != null) {
                    if (Objects.equals("PRECREATE_SUCCESS", bizResponse.getOrDefault("result_code", ""))) {
                        net.sf.json.JSONObject data = bizResponse.getJSONObject("data");
                        if (data != null) {
                            success = true;
                            qrCode = data.getOrDefault("qr_code", "");
                            mainId = data.getOrDefault("client_sn", "").toString();
                            sn = data.getOrDefault("sn", "");
                        }
                    }
                }
            }
            if (success) {
                resultMap.put(ER.MSG, qrCode);
                resultMap.put("mainId", mainId);
                resultMap.put("sn", sn);
            } else {
                resultMap.put(ER.RES, ER.ERR);
                resultMap.put(ER.MSG, "获取二维码失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }


    @Resource
    private SqbTransactionDao sqbTransactionDao;

    /**
     * 对收钱吧返回结果做处理
     *
     * @param map
     * @return
     */
    public Map<String, Object> handleSQBResult(Map<String, Object> map) {

        HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.putAll(map);
        map = requestMap;

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            String mainId = "mainId";
            if (!map.containsKey(mainId)) {
                throw new Exception(PAY_RES.REFUND_MAINID_NOT_NULL);
            }
            SqbTransactionSearch sqbTransactionSearch = new SqbTransactionSearch();
            sqbTransactionSearch.setMainId(map.get("mainId").toString());


            Page<SqbTransaction> sqbTransactions = sqbTransactionDao.selectBySearch(sqbTransactionSearch);
            SqbTransaction transaction = null;
            if (sqbTransactions != null && sqbTransactions.size() > 0) {
                transaction = sqbTransactions.get(0);
                Integer status = transaction.getStatus();
                if (status == 1 || status == -1 || status == -2) {
                    resultMap.put(ER.MSG, PAY_RES.PAY_SUCC_MSG);
                    resultMap.put("mainId", transaction.getMainId());
                    resultMap.put("money", transaction.getMoney() / 100.0);
                    resultMap.put("tradeNo", transaction.getTradeNo());
                    return resultMap;
                }
            }


            /*1.查询账号信息 及验资参数是否合法*/
            PayAgentInfo payAgentInfo = getPayAgentInfo(user.getHid());
            if (payAgentInfo == null || payAgentInfo.getSqbActiveCode() == null
                    || payAgentInfo.getSqbCbTerminalKey() == null
                    || payAgentInfo.getSqbCbTerminalSn() == null) {
                throw new Exception(PAY_RES.NO_SQB_MSG);
            }
            Object isQr = map.get("isQr");
            String terminal_sn = payAgentInfo.getSqbBcTerminalSn();
            String terminal_key = payAgentInfo.getSqbBcTerminalKey();
            //如果是二维码 C扫B
            if (Objects.equals(isQr, "1")) {
                terminal_sn = payAgentInfo.getSqbCbTerminalSn();
                terminal_key = payAgentInfo.getSqbCbTerminalKey();
            }

            JSONObject params = new JSONObject();
            params.put("terminal_sn", terminal_sn);           //终端号
            Object sn = map.get("sn");
            if ((sn == null || "".equals(sn)) && transaction != null) {
                sn = transaction.getTradeNo();
            }
            params.put("sn", sn);             //收钱吧系统内部唯一订单号
            //params.put("client_sn", map.get("mainId"));  //商户系统订单号,必须在商户系统内唯一；且长度不超过64字节

            String sign = getSign(params.toString() + terminal_key);
            String url = api_domain + "/upay/v2/query";
            String result = HttpUtil.httpPost(url, params.toString(), sign, terminal_sn);

            boolean success = false;
            net.sf.json.JSONObject reqjson = net.sf.json.JSONObject.fromObject(result);
            if (reqjson.containsKey("biz_response")) {
                net.sf.json.JSONObject bizResponse = reqjson.getJSONObject("biz_response");
                if (bizResponse != null) {
                    if (Objects.equals("SUCCESS", bizResponse.getOrDefault("result_code", ""))) {
                        net.sf.json.JSONObject data = bizResponse.getJSONObject("data");
                        if (data != null && Objects.equals("PAID", data.get("order_status"))) {
                            success = true;
                            SqbTransaction sqbTransaction = new SqbTransaction();
                            sqbTransaction.setHid(user.getHid());
                            if (map.containsKey("uuid")) {
                                sqbTransaction.setUuid(map.get("uuid").toString());
                            }
                            if (map.containsKey("source")) {
                                sqbTransaction.setSource(map.get("source").toString());
                            }
                            sqbTransaction.setMoney(data.getInt("total_amount"));
                            sqbTransaction.setTradeNo(data.getString("sn"));
                            sqbTransaction.setPayerUid(data.getString("payer_uid"));
                            sqbTransaction.setMainId(data.getString("client_sn"));
                            sqbTransaction.setStatus(1);
                            sqbTransaction.setPayTime(new Date());
                            sqbTransaction.setTerminalSn(terminal_sn);
                            sqbTransaction.setPayway(data.getInt("payway"));
                            sqbTransaction.setPaywayName(data.getString("payway_name"));
                            sqbTransaction.setPayTradeNo(data.getString("trade_no"));
                            sqbTransaction.setInterfaceRawResult(result);
                            sqbTransactionDao.insert(sqbTransaction);

                            resultMap.put("result", true);
                            resultMap.put("message", "success");
                            resultMap.put("mainId", map.get(mainId));
                            resultMap.put("money", data.getDouble("total_amount") / 100.0);
                        }
                    }
                }
            }

            if (!success) {
                resultMap.put(ER.RES, ER.ERR);
                resultMap.put(ER.MSG, "");
            }
            return resultMap;
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    @Resource
    private SqbRefundDao sqbRefundDao;

    /**
     * 收钱吧退款
     *
     * @param map
     * @return
     */
    public Map<String, Object> SQBRefund(Map<String, Object> map) {

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*1.查询账号信息 及验资参数是否合法*/
            PayAgentInfo payAgentInfo = getPayAgentInfo(user.getHid());
            if (payAgentInfo == null || payAgentInfo.getSqbActiveCode() == null
                    || payAgentInfo.getSqbCbTerminalKey() == null
                    || payAgentInfo.getSqbCbTerminalSn() == null) {
                throw new Exception(PAY_RES.NO_SQB_MSG);
            }

            /*验证退款账单和退款金额*/
            String mainId = "mainId";
            if (!map.containsKey(mainId)) {
                throw new Exception(PAY_RES.REFUND_MAINID_NOT_NULL);
            }

            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            String smainId = map.get(mainId).toString();
            Object hotelWechatPay = userCahe.get("hotelaliPay", smainId);
            if (hotelWechatPay != null) {
                int i = Integer.parseInt(hotelWechatPay.toString());
                if (i > 0) {
                    throw new Exception("以退过款不允许退款，退款金额：" + i / 100.0);
                }
            }


            String refundMoney = "refundMoney";
            if (!map.containsKey(refundMoney)) {
                throw new Exception(PAY_RES.REFUND_MONEY_NOT_NULL);
            }
            Integer refund = Integer.parseInt(map.get(refundMoney).toString());

            /*查询支付账单是否存在*/
            SqbTransactionSearch search = new SqbTransactionSearch();
            search.setHid(user.getHid());
            search.setMainId(map.get(mainId).toString());
            List<SqbTransaction> SqbTransactions = sqbTransactionDao.selectBySearch(search);

            if (SqbTransactions.size() < 1) {
                throw new Exception(PAY_RES.REFUND_MAINID_NOT_NULL);
            }

            SqbTransaction sqbTransaction = SqbTransactions.get(0);

            if (sqbTransaction.getRefundMoney() == null) {
                sqbTransaction.setRefundMoney(0);
            }

            String terminal_sn = sqbTransaction.getTerminalSn();
            String terminal_key = terminal_sn.equals(payAgentInfo.getSqbBcTerminalSn()) ? payAgentInfo.getSqbBcTerminalKey() : payAgentInfo.getSqbCbTerminalKey();

            boolean validateRefund = refund < 0 || sqbTransaction.getRefundMoney() > 0;
            if (validateRefund) {
                throw new Exception(PAY_RES.HAS_REFUND);
            }
            String url = api_domain + "/upay/v2/refund";
            JSONObject params = new JSONObject();
            //账单号
            String outRequestNo = HotelUtils.getHIDUUID32("SQB", user.getHid());
            params.put("terminal_sn", terminal_sn);                     //收钱吧终端ID
            params.put("sn", sqbTransaction.getTradeNo());              //收钱吧系统内部唯一订单号
            //params.put("client_sn", outRequestNo);                    //商户系统订单号,必须在商户系统内唯一；且长度不超过64字节
            params.put("refund_amount", refund.toString());                        //退款金额
            params.put("refund_request_no", "1");                         //商户退款所需序列号,表明是第几次退款
            params.put("operator", user.getUserName());                  //门店操作员
            System.out.println(params.toString());
            String sign = getSign(params.toString() + terminal_key);
            System.out.println("sign:" + sign + "  terminal_key: " + terminal_key);
            String result = HttpUtil.httpPost(url, params.toString(), sign, terminal_sn);


            net.sf.json.JSONObject rt = net.sf.json.JSONObject.fromObject(result);
            String msg = "退款失败";
            resultMap.put(ER.MSG, msg);
            resultMap.put(ER.RES, ER.ERR);

            if (!Objects.equals("200", rt.get("result_code"))) {
                return resultMap;
            }

            net.sf.json.JSONObject bizResponse = rt.getJSONObject("biz_response");
            if (bizResponse == null) {
                return resultMap;
            }

            Set<String> passCode = new HashSet<>();
            passCode.add("REFUNDED");
            passCode.add("REFUND_SUCCESS");
            passCode.add("REFUND_INPROGRESS");
            if (!passCode.contains(bizResponse.getOrDefault("result_code", "").toString())) {
                resultMap.put(ER.MSG, bizResponse.get("error_message"));
                return resultMap;
            }

            net.sf.json.JSONObject data = bizResponse.getJSONObject("data");
            if (data == null) {
                return resultMap;
            }


            //添加退款记录
            SqbRefund sqbRefund = new SqbRefund();
            sqbRefund.setRefundMoney(refund);
            sqbRefund.setHid(user.getHid());
            sqbRefund.setMainId(outRequestNo);
            sqbRefund.setPayMainId(sqbTransaction.getTradeNo());
            sqbRefund.setRefundTime(new Date());
            sqbRefund.setPayUserId(sqbTransaction.getPayerUid());
            sqbRefund.setTerminalSn(terminal_sn);
            sqbRefund.setPayway(sqbTransaction.getPayway());
            sqbRefund.setPaywayName(sqbTransaction.getPaywayName());
            sqbRefund.setPayTradeNo(data.getOrDefault("trade_no", "").toString());
            sqbRefund.setInterfaceRawResult(result);


            //当前可用金额
            int canUseMoney = sqbTransaction.getMoney() - sqbTransaction.getRefundMoney();
            sqbRefund.setStatus(1);

            if (map.containsKey("source")) {
                sqbRefund.setSource(map.get("source").toString());
            }

            Integer refundId = sqbRefundDao.insert(sqbRefund);
            resultMap.put("refundId", refundId);

            //修改支付的退款金额
            SqbTransaction sqbTransaction1 = new SqbTransaction();
            sqbTransaction1.setId(sqbTransaction.getId());
            if (canUseMoney > refund) {
                sqbTransaction1.setStatus(PAY_RES.STATE_REFUND);
            } else {
                sqbTransaction1.setStatus(PAY_RES.STATE_REFUND_ALL);
            }
            sqbTransaction1.setRefundMoney(sqbTransaction.getRefundMoney() + refund);
            if (map.containsKey("source")) {
                sqbTransaction1.setSource(map.get("source").toString());
            }
            sqbTransactionDao.update(sqbTransaction1);
            //退款成功
            resultMap.put(ER.MSG, PAY_RES.REFUND_SUCC);
            resultMap.put(ER.RES, ER.SUCC);
            userCahe.put("hotelaliPay", smainId, refund + "");
            return resultMap;
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }

    /**
     * 收钱吧交易关闭
     *
     * @param map
     * @return
     */
    public Map<String, Object> paySQBClose(Map<String, Object> map) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        try {

            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*1.查询账号信息 及验资参数是否合法*/
            PayAgentInfo payAgentInfo = getPayAgentInfo(user.getHid());
            if (payAgentInfo == null || payAgentInfo.getSqbActiveCode() == null
                    || payAgentInfo.getSqbCbTerminalKey() == null
                    || payAgentInfo.getSqbCbTerminalSn() == null) {
                throw new Exception(PAY_RES.NO_SQB_MSG);
            }
            Object isQr = map.get("isQr");
            String terminal_sn = payAgentInfo.getSqbBcTerminalSn();
            String terminal_key = payAgentInfo.getSqbBcTerminalKey();
            //如果是二维码 C扫B
            if (Objects.equals(isQr, "1")) {
                terminal_sn = payAgentInfo.getSqbCbTerminalSn();
                terminal_key = payAgentInfo.getSqbCbTerminalKey();
            }

            //账单号
            String mainId = map.get("mainId").toString();
            Map<String, Object> result = this.handleSQBResult(map);
            if (!Objects.equals(Boolean.TRUE, result.get("result"))) {
                String url = api_domain + "/upay/v2/cancel";
                JSONObject params = new JSONObject();
                params.put("terminal_sn", terminal_sn);           //终端号
                params.put("sn", map.getOrDefault("sn", "").toString()); //收钱吧系统内部唯一订单号
                params.put("client_sn", mainId);  //商户系统订单号,必须在商户系统内唯一；且长度不超过64字节
                //     String sign = getSign(params.toString() + terminal_key);
                //      HttpUtil.httpPost(url, params.toString(), sign, terminal_sn);
            }
            // 去掉交易关闭接口
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }


    /**
     * 收钱吧反扫
     *
     * @param map
     * @return
     */
    public Map<String, Object> SQBMicropay(net.sf.json.JSONObject map) {
        HashMap<String, Object> requestMap = new HashMap<>();
        requestMap.putAll(map);


        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            String sessionToken = map.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*1.查询账号信息 及验资参数是否合法*/
            PayAgentInfo payAgentInfo = getPayAgentInfo(user.getHid());
            if (payAgentInfo == null || payAgentInfo.getSqbActiveCode() == null
                    || payAgentInfo.getSqbCbTerminalKey() == null
                    || payAgentInfo.getSqbCbTerminalSn() == null) {
                throw new Exception(PAY_RES.NO_SQB_MSG);
            }
            Object isQr = map.get("isQr");
            String terminal_sn = payAgentInfo.getSqbBcTerminalSn();
            String terminal_key = payAgentInfo.getSqbBcTerminalKey();


            //支付金额
            String moneyParam = "money";
            double moneyYuan = Double.parseDouble(map.get(moneyParam).toString());
            Integer money = (int) (moneyYuan * 100);
            //账单号
            String mainId = HotelUtils.getHIDUUID32("SQB", user.getHid());

            //备注
            String memoParam = "memo";
            String memoStr = "收钱吧支付";
            if (map.containsKey(memoParam)) {
                memoStr = map.get(memoParam).toString();
            }

            JSONObject params = new JSONObject();
            params.put("terminal_sn", terminal_sn);           //终端号
            params.put("client_sn", mainId);  //商户系统订单号,必须在商户系统内唯一；且长度不超过64字节
            params.put("total_amount", money.toString());               //交易总金额,以分为单位
            params.put("dynamic_id", map.get("scanCode"));     //条码内容
            params.put("subject", memoStr);                     //交易简介
            params.put("operator", user.getUserName());                     //门店操作员
            params.put("notify_url", "https://czpms.cn/hotel/pay/sqbPayNotify.do");

            String sign = getSign(params.toString() + terminal_key);
            String url = api_domain + "/upay/v2/pay";

            //sessionToken放入Redis, 回调的时候用
            stringRedisTemplate.opsForValue().set("SESSION_TOKEN:" + mainId, sessionToken, 60L, TimeUnit.MINUTES);
            if (map.containsKey("addAccountData")) {
                stringRedisTemplate.opsForValue().set("ADD_ACCOUNT_DATA:" + mainId, map.getString("addAccountData"), 60L, TimeUnit.MINUTES);
            }

            String result = HttpUtil.httpPost(url, params.toString(), sign, terminal_sn);
            System.out.println(result);


            net.sf.json.JSONObject reqjson = net.sf.json.JSONObject.fromObject(result);
            boolean isSucess = false;
            Object sn = null;

            if (Objects.equals(reqjson.get("result_code"), "400")) {
                resultMap.put(ER.RES, ER.ERR);
                resultMap.put(ER.MSG, reqjson.get("error_message"));
                return resultMap;
            }

            if (reqjson.containsKey("biz_response")) {
                net.sf.json.JSONObject bizResponse = reqjson.getJSONObject("biz_response");
                if (bizResponse != null) {
                    Object result_code = bizResponse.get("result_code");
                    if (Objects.equals(result_code, "PAY_FAIL")) {
                        resultMap.put(ER.RES, ER.ERR);
                        resultMap.put(ER.MSG, bizResponse.get("error_code"));
                        return resultMap;
                    }

                    if (Objects.equals(result_code, "PAY_IN_PROGRESS")) {
                        resultMap.put(ER.RES, ER.ERR);
                        resultMap.put(ER.MSG, "等待用户支付");
                        return resultMap;
                    }

                    if (Objects.equals(result_code, "PAY_SUCCESS")) {
                        net.sf.json.JSONObject data = bizResponse.getJSONObject("data");
                        if (data != null) {
                            SqbTransaction sqbTransaction = new SqbTransaction();
                            sqbTransaction.setHid(user.getHid());

                            if (map.containsKey("uuid")) {
                                sqbTransaction.setUuid(map.get("uuid").toString());
                            }
                            if (map.containsKey("source")) {
                                sqbTransaction.setSource(map.get("source").toString());
                            }

                            sqbTransaction.setMoney(data.getInt("total_amount"));
                            sqbTransaction.setTradeNo(data.getString("sn"));
                            sn = data.getString("sn");
                            sqbTransaction.setPayerUid(data.getString("payer_uid"));
                            sqbTransaction.setMainId(data.getString("client_sn"));
                            sqbTransaction.setStatus(1);
                            sqbTransaction.setPayTime(new Date());
                            sqbTransaction.setTerminalSn(terminal_sn);
                            sqbTransaction.setPayway(data.getInt("payway"));
                            sqbTransaction.setPaywayName(data.getString("payway_name"));
                            sqbTransaction.setPayTradeNo(data.getString("trade_no"));
                            sqbTransaction.setInterfaceRawResult(result);
                            //sqbTransactionDao.insert(sqbTransaction);

                            resultMap.put("result", true);
                            resultMap.put("message", "success");
                            resultMap.put("mainId", mainId);
                            resultMap.put("sn", sn);
                            resultMap.put("money", data.getDouble("total_amount"));
                            isSucess = true;
                        }
                    }
                }
            }


            if (!isSucess) {
                resultMap.put(ER.RES, ER.ERR);
            }
            return resultMap;
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return resultMap;
    }


}
