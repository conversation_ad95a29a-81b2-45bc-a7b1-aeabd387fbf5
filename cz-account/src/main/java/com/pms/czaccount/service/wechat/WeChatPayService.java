package com.pms.czaccount.service.wechat;


import com.pms.czaccount.bean.pay.PayOrder;
import com.pms.czaccount.bean.pay.WechatAccounts;
import net.sf.json.JSONObject;

import java.util.Map;

public interface WeChatPayService {


      /**
       * 获取微信支付二维码
       * @param map
       * @return
       */
      public Map<String,Object> getWeChatQrcode(Map<String, Object> map);

      /**
       * 获取微信支付结果
       * @param map
       * @return
       */
      public Map<String,Object> getWeChatPayResult(Map<String, Object> map, WechatAccounts wechatAccounts);


      public Map<String,Object> weChatPayClose(JSONObject param);

      /**
       * 对微信返回结果做处理
       * @param map
       * @return
       */
      public Map<String,Object> handleWeChatPayResult(Map<String, Object> map);

      /**
       * 微信退款
       * @param map
       * @return
       */
      public Map<String,Object> wechatRefund(Map<String, Object> map);

      /**
       * 微信退款
       * @param map
       * @return
       */
      public Map<String,Object> wechatMinigroRefund(Map<String, Object> map);

      /**
       * 获取微信支付信息
       * @param map
       * @return
       */
      public Map<String,Object> getWeChatPayMsg(Map<String, Object> map);

      /**
       * 修改或者保存微信支付信息
       * @param map
       * @return
       */
      public Map<String,Object> saveOrUpdateWeChatPayMsg(Map<String, Object> map);


      /**
       * 微信反扫
       * @param map
       * @return
       */
      public Map<String,Object> weChatMicropay(JSONObject map);


      /**
       * 保存微信证书
       * @param param
       * @return
       */
      public Map<String,Object> addWxCert(JSONObject param);

      /**
       * 发送微信红包
       * @param param
       * @return
       */
      public Map<String,Object> senRedPack(JSONObject param);


      /**
       * 查询红包发送结果
       * @param param
       * @return
       */
      public Map<String,Object> searchRedPackMsg(JSONObject param);



      Map<String, Object> addAccount(String accountData, String sessionToken,String mainId);

      boolean dealPayOrder(PayOrder payOrder) throws Exception;
}
