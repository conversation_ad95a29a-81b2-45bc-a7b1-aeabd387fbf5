package com.pms.czaccount.service.account;

import com.github.pagehelper.Page;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.search.SplitAccountReset;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.AccountCancelRequest;
import com.pms.czpmsutils.request.AccountSearchRequest;
import com.pms.czpmsutils.request.UpdateAccountRequest;
import com.pms.czpmsutils.request.search.AccountSummarySearch;
import net.sf.json.JSONObject;

import java.util.Map;

/**
 * 关于账务的处理
 */
public interface AccountCanService {

    /**
     * 根据registId获取账务信息
     * @param param
     * @return
     */
    public ResponseData findAccountByRegistId(JSONObject param);

    /**
     *  查询账务信息
     * @return
     */
    public ResponseData findPrintAccountByResist(JSONObject param);

    /**
     *  查询账务信息
     * @return
     */
    public ResponseData findPrintAccountSummaryByResist(JSONObject param);

    /**
     * 对接第三方支付平台退款
     * @param param
     * @return
     */
    public ResponseData refundMoneyForOtherPms(JSONObject param);



    public ResponseData refundMoneyJiaTuiKuan(JSONObject param);

    /**
     * 对接第三方支付平台退款
     *  并推送到第三方账务表中
     * @param param
     * @return
     */
    public Map<String,Object> refundMoneyForOtherPmsAndPush(JSONObject param);



    public ResponseData updateAccountParam(UpdateAccountRequest updateAccountRequest);

    /**
     * 入账
     * @param param
     * @return
     */
    public Map<String,Object> addAccount(JSONObject param);


    /**
     * 冲账
     * @param param
     * @return
     */
    public Map<String,Object> setOff(JSONObject param);

    /**
     * 转账
     * @param param
     * @return
     */
    public Map<String,Object> transferAccount(JSONObject param);

    /**
     * 查询冲账，转账
     * @param param
     * @return
     */
    public ResponseData findAccountCancelByRegistId(AccountCancelRequest accountCancelRequest);

    /**
     * 获取账务总和
     * @param param
     * @return
     */
    public ResponseData getAccountCountByRegistId(JSONObject param);

    /**
     * 部分结账
     * @param param
     * @return
     */
    public Map<String ,Object> settleAccount(JSONObject param);

    /**
     * 查询首页账务信息
     * @param param
     * @return
     */
    public ResponseData searchIndexAccount(JSONObject param);


    /**
     * 新的帐务查询方法
     * @param accountSearchRequest
     * @return
     */
    public Page<Account> searchAccountList(AccountSearchRequest accountSearchRequest) throws Exception;

    /**
     * 查询未关联的账务信息
     * @param accountSearchRequest
     * @return
     */
    public ResponseData selectNotCollection(AccountSearchRequest accountSearchRequest);

    /**
     * 账务重置
     * @param param
     * @return
     */
    public ResponseData accountResetFunc(JSONObject param);


    /**
     * 查询账务汇总信息
     * @param param
     * @return
     */
    public ResponseData accountSummary(AccountSummarySearch param);

    /**
     * 拆账
     * @return
     */
    public ResponseData splitAccount(SplitAccountReset splitAccountReset);
}
