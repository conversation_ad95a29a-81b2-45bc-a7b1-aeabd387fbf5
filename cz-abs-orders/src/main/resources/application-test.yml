# 数据库操作
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: 2222f27cfecd13ba146f595ba63fd3bd93e2608996a398b2415c39b5192dfcaa68c829723b5aef564240b63955e1e4a9008f16962e87c92ba6f27aa6c03fc6311aa037641b5d01a3e41ec71fa2b989d808053432fe3365915587fb7a73a58789582f8423d2cb3eef
    username: 750a2aa24bf8807efeb959b7d4642fcb
    password: f5dd7cf74eb6fb0afeb959b7d4642fcb
    #配置初始化大小/最小/最大
    hikari:
      connection-timeout: 30000
      maximum-pool-size: 200
      minimum-idle: 20
      idle-timeout: 60000
      max-lifetime: 60000
      connection-test-query: SELECT 1
  redis:
    host: ***********
    port: 6379
    password: 643e145d0e5c42ef073f4f39c975ff0efeb959b7d4642fcb
    database: 10
    timeout: 30000
  application:
    name: CZ-ABS-ORDERS
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true
  jta:
    enabled: true


mybatis:
  mapper-locations: classpath*:mapper/*/**.xml,mapper/**.xml  #注意：一定要对应mapper映射xml文件的所在路径
  configuration:
    log-impl: com.pms.czpmsutils.MybatisLoggerImpl

eureka:
  client:
    service-url:
      defaultZone: ************************************/eureka
  instance:
    hostname: CZ-ABS-ORDERS
    prefer-ip-address: true
server:
  port: 8132
  tomcat:
    max-http-post-size: 10485760

pagehelper:
  auto-dialect: true
  supportMethodsArguments: true

file:
  local:
    save:
      path: /home/<USER>
  #      path: C:\Users\<USER>\Desktop\hotel
  server:
    url: http://hotel.gateway.com/uploadAuth

sys:
  iot:
    address: http://hotel.northgateway.com
    accessKey: k0JDQFourvakovdhEA3LB1EtbHw6JG25
    appSecret: kSe3hhP8e0vmuSKOnKH8rYatG5PkZXq2
    sassTokenSecret: zofHGBnc9yUw@eHl