<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!--&lt;!&ndash; 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 &ndash;&gt;-->
<!--&lt;!&ndash; scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true &ndash;&gt;-->
<!--&lt;!&ndash; scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。 &ndash;&gt;-->
<!--&lt;!&ndash; debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 &ndash;&gt;-->
<!--<configuration  scan="true" scanPeriod="10 seconds">-->
<!--    <contextListener class="com.pms.czpmsutils.CustomLogContextListener" />-->

<!--    &lt;!&ndash;<include resource="org/springframework/boot/logging/logback/base.xml" />&ndash;&gt;-->

<!--    <contextName>logback</contextName>-->
<!--    &lt;!&ndash; name的值是变量的名称，value的值时变量定义的值。通过定义的值会被插入到logger上下文中。定义变量后，可以使“${}”来使用变量。 &ndash;&gt;-->
<!--    &lt;!&ndash;<property name="log.path" value="/home/<USER>/log" />&ndash;&gt;-->
<!--    &lt;!&ndash;<property name="log.path" value="D:\ugainterface\log" />&ndash;&gt;-->
<!--    &lt;!&ndash;    <property name="log.path" value="/Users/<USER>/data/data/apps/cz-zuul" />&ndash;&gt;-->
<!--    <property name="APP_NAME" value="cz-abs-orders" />-->
<!--    &lt;!&ndash; 彩色日志 &ndash;&gt;-->
<!--    &lt;!&ndash; 彩色日志依赖的渲染类 &ndash;&gt;-->
<!--    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />-->
<!--    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />-->
<!--    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />-->
<!--    &lt;!&ndash; 彩色日志格式 &ndash;&gt;-->
<!--    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(-&#45;&#45;){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>-->
<!--    &lt;!&ndash;输出到控制台&ndash;&gt;-->
<!--    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">-->
<!--        &lt;!&ndash;此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息&ndash;&gt;-->
<!--        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">-->
<!--            <level>info</level>-->
<!--        </filter>-->
<!--        <encoder>-->
<!--            <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>-->
<!--            &lt;!&ndash; 设置字符集 &ndash;&gt;-->
<!--            <charset>UTF-8</charset>-->
<!--        </encoder>-->
<!--    </appender>-->


<!--    &lt;!&ndash;输出到文件&ndash;&gt;-->

<!--    &lt;!&ndash; 时间滚动输出 level为 DEBUG 日志 &ndash;&gt;-->
<!--    <appender name="DEBUG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
<!--        &lt;!&ndash; 正在记录的日志文件的路径及文件名 &ndash;&gt;-->
<!--        <file>${LOG_PATH}/${APP_NAME}/log_debug.log</file>-->
<!--        &lt;!&ndash;日志文件输出格式&ndash;&gt;-->
<!--        <encoder>-->
<!--            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>-->
<!--            <charset>UTF-8</charset> &lt;!&ndash; 设置字符集 &ndash;&gt;-->
<!--        </encoder>-->
<!--        &lt;!&ndash; 日志记录器的滚动策略，按日期，按大小记录 &ndash;&gt;-->
<!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
<!--            &lt;!&ndash; 日志归档 &ndash;&gt;-->
<!--            <fileNamePattern>${LOG_PATH}/${APP_NAME}/debug/log-debug-%d{yyyy-MM-dd}.%i.log</fileNamePattern>-->
<!--            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
<!--                <maxFileSize>100MB</maxFileSize>-->
<!--            </timeBasedFileNamingAndTriggeringPolicy>-->
<!--            &lt;!&ndash;日志文件保留天数&ndash;&gt;-->
<!--            <maxHistory>15</maxHistory>-->
<!--        </rollingPolicy>-->
<!--        &lt;!&ndash; 此日志文件只记录debug级别的 &ndash;&gt;-->
<!--        <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
<!--            <level>debug</level>-->
<!--            <onMatch>ACCEPT</onMatch>-->
<!--            <onMismatch>DENY</onMismatch>-->
<!--        </filter>-->
<!--    </appender>-->

<!--    &lt;!&ndash; 时间滚动输出 level为 INFO 日志 &ndash;&gt;-->
<!--    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
<!--        &lt;!&ndash; 正在记录的日志文件的路径及文件名 &ndash;&gt;-->
<!--        <file>${LOG_PATH}/${APP_NAME}/log_info.log</file>-->
<!--        &lt;!&ndash;日志文件输出格式&ndash;&gt;-->
<!--        <encoder>-->
<!--            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>-->
<!--            <charset>UTF-8</charset>-->
<!--        </encoder>-->
<!--        &lt;!&ndash; 日志记录器的滚动策略，按日期，按大小记录 &ndash;&gt;-->
<!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
<!--            &lt;!&ndash; 每天日志归档路径以及格式 &ndash;&gt;-->
<!--            <fileNamePattern>${LOG_PATH}/${APP_NAME}/info/log-info-%d{yyyy-MM-dd}.%i.log</fileNamePattern>-->
<!--            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
<!--                <maxFileSize>100MB</maxFileSize>-->
<!--            </timeBasedFileNamingAndTriggeringPolicy>-->
<!--            &lt;!&ndash;日志文件保留天数&ndash;&gt;-->
<!--            <maxHistory>15</maxHistory>-->
<!--        </rollingPolicy>-->
<!--        &lt;!&ndash; 此日志文件只记录info级别的 &ndash;&gt;-->
<!--        <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
<!--            <level>info</level>-->
<!--            <onMatch>ACCEPT</onMatch>-->
<!--            <onMismatch>DENY</onMismatch>-->
<!--        </filter>-->
<!--    </appender>-->

<!--    &lt;!&ndash; 时间滚动输出 level为 WARN 日志 &ndash;&gt;-->
<!--    <appender name="WARN_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
<!--        &lt;!&ndash; 正在记录的日志文件的路径及文件名 &ndash;&gt;-->
<!--        <file>${LOG_PATH}/${APP_NAME}/log_warn.log</file>-->
<!--        &lt;!&ndash;日志文件输出格式&ndash;&gt;-->
<!--        <encoder>-->
<!--            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>-->
<!--            <charset>UTF-8</charset> &lt;!&ndash; 此处设置字符集 &ndash;&gt;-->
<!--        </encoder>-->
<!--        &lt;!&ndash; 日志记录器的滚动策略，按日期，按大小记录 &ndash;&gt;-->
<!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
<!--            <fileNamePattern>${LOG_PATH}/${APP_NAME}/warn/log-warn-%d{yyyy-MM-dd}.%i.log</fileNamePattern>-->
<!--            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
<!--                <maxFileSize>100MB</maxFileSize>-->
<!--            </timeBasedFileNamingAndTriggeringPolicy>-->
<!--            &lt;!&ndash;日志文件保留天数&ndash;&gt;-->
<!--            <maxHistory>15</maxHistory>-->
<!--        </rollingPolicy>-->
<!--        &lt;!&ndash; 此日志文件只记录warn级别的 &ndash;&gt;-->
<!--        <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
<!--            <level>warn</level>-->
<!--            <onMatch>ACCEPT</onMatch>-->
<!--            <onMismatch>DENY</onMismatch>-->
<!--        </filter>-->
<!--    </appender>-->


<!--    &lt;!&ndash; 时间滚动输出 level为 ERROR 日志 &ndash;&gt;-->
<!--    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
<!--        &lt;!&ndash; 正在记录的日志文件的路径及文件名 &ndash;&gt;-->
<!--        <file>${LOG_PATH}/${APP_NAME}/log_error.log</file>-->
<!--        &lt;!&ndash;日志文件输出格式&ndash;&gt;-->
<!--        <encoder>-->
<!--            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>-->
<!--            <charset>UTF-8</charset> &lt;!&ndash; 此处设置字符集 &ndash;&gt;-->
<!--        </encoder>-->
<!--        &lt;!&ndash; 日志记录器的滚动策略，按日期，按大小记录 &ndash;&gt;-->
<!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
<!--            <fileNamePattern>${LOG_PATH}/${APP_NAME}/error/log-error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>-->
<!--            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
<!--                <maxFileSize>100MB</maxFileSize>-->
<!--            </timeBasedFileNamingAndTriggeringPolicy>-->
<!--            &lt;!&ndash;日志文件保留天数&ndash;&gt;-->
<!--            <maxHistory>15</maxHistory>-->
<!--        </rollingPolicy>-->
<!--        &lt;!&ndash; 此日志文件只记录ERROR级别的 &ndash;&gt;-->
<!--        <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
<!--            <level>ERROR</level>-->
<!--            <onMatch>ACCEPT</onMatch>-->
<!--            <onMismatch>DENY</onMismatch>-->
<!--        </filter>-->
<!--    </appender>-->

<!--    &lt;!&ndash;-->
<!--        <logger>用来设置某一个包或者具体的某一个类的日志打印级别、-->
<!--        以及指定<appender>。<logger>仅有一个name属性，-->
<!--        一个可选的level和一个可选的addtivity属性。-->
<!--        name:用来指定受此logger约束的某一个包或者具体的某一个类。-->
<!--        level:用来设置打印级别，大小写无关：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，-->
<!--              还有一个特俗值INHERITED或者同义词NULL，代表强制执行上级的级别。-->
<!--              如果未设置此属性，那么当前logger将会继承上级的级别。-->
<!--        addtivity:是否向上级logger传递打印信息。默认是true。-->
<!--    &ndash;&gt;-->
<!--    &lt;!&ndash;<logger name="org.springframework.web" level="info"/>&ndash;&gt;-->
<!--    &lt;!&ndash;<logger name="org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor" level="INFO"/>&ndash;&gt;-->
<!--    &lt;!&ndash;-->
<!--        使用mybatis的时候，sql语句是debug下才会打印，而这里我们只配置了info，所以想要查看sql语句的话，有以下两种操作：-->
<!--        第一种把<root level="info">改成<root level="DEBUG">这样就会打印sql，不过这样日志那边会出现很多其他消息-->
<!--        第二种就是单独给dao下目录配置debug模式，代码如下，这样配置sql语句会打印，其他还是正常info级别：-->
<!--     &ndash;&gt;-->


<!--    &lt;!&ndash;-->
<!--        root节点是必选节点，用来指定最基础的日志输出级别，只有一个level属性-->
<!--        level:用来设置打印级别，大小写无关：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，-->
<!--        不能设置为INHERITED或者同义词NULL。默认是DEBUG-->
<!--        可以包含零个或多个元素，标识这个appender将会添加到这个logger。-->
<!--    &ndash;&gt;-->

<!--    &lt;!&ndash;开发环境:打印控制台&ndash;&gt;-->
<!--    <springProfile name="dev">-->
<!--        <logger name="com.nmys.view" level="debug"/>-->
<!--    </springProfile>-->

<!--    <root level="info">-->
<!--        <appender-ref ref="CONSOLE" />-->
<!--        <appender-ref ref="DEBUG_FILE" />-->
<!--        <appender-ref ref="INFO_FILE" />-->
<!--        <appender-ref ref="WARN_FILE" />-->
<!--        <appender-ref ref="ERROR_FILE" />-->
<!--    </root>-->

<!--    &lt;!&ndash;生产环境:输出到文件&ndash;&gt;-->
<!--    &lt;!&ndash;  <springProfile name="pro">-->
<!--      <root level="info">-->
<!--      <appender-ref ref="CONSOLE" />-->
<!--      <appender-ref ref="DEBUG_FILE" />-->
<!--      <appender-ref ref="INFO_FILE" />-->
<!--      <appender-ref ref="ERROR_FILE" />-->
<!--      <appender-ref ref="WARN_FILE" />-->
<!--      </root>-->
<!--      </springProfile>&ndash;&gt;-->
<!--</configuration>-->
