#!/bin/bash
# 支持前缀匹配最新JAR包的多环境管理脚本（2025-05-15更新）

# ------------------------ 配置区 ------------------------
APP_PREFIX="cz-abs-order"               # Jar包名前缀（如your-app-1.0.0.jar）
JAVA_OPTS="-Xms256m -Xmx512m"              # JVM参数
DEFAULT_ENV="dev"                          # 默认启动环境

# ------------------------ 动态获取最新JAR路径 ------------------------
# 按版本号排序获取最新JAR（需文件名含版本号，如your-app-1.2.3.jar）
LATEST_JAR=$(ls ${APP_PREFIX}*.jar | sort -Vr | head -n1)

# 若按时间排序（适用于文件名不含版本号）：ls -t ${APP_PREFIX}*.jar | head -n1

if [ -z "$LATEST_JAR" ]; then
    echo "错误：未找到以${APP_PREFIX}开头的JAR包"
    exit 1
fi

# ------------------------ 函数定义 ------------------------
# 用法提示
usage() {
    echo "Usage: $0 {start|stop|restart|status} [env]"
    echo "可用环境: dev, test, prod"
    echo "当前JAR: $LATEST_JAR"
    exit 1
}

# 环境校验
validate_env() {
    case $1 in
        dev|test|prod) return 0 ;;
        *) echo "错误：无效环境 '$1'"; return 1 ;;
    esac
}

# 获取进程PID
get_pid() {
    pgrep -f "$(basename $LATEST_JAR)"
}

# 停止应用
stop_app() {
    local pid=$(get_pid)
    [ -n "$pid" ] && kill -9 $pid && sleep 2
    [ -z "$(get_pid)" ] && echo "已停止" || echo "停止失败"
}

# 启动应用
start_app() {
    local env=$1
    nohup java $JAVA_OPTS -Dloader.path=./lib/ -jar $LATEST_JAR --spring.profiles.active=$env --spring.config.location=file:./config/ --logging.config=file:./config/logback-spring.xml > /dev/null 2>&1 &
    echo "启动 $LATEST_JAR 环境[$env]..."
    sleep 3
    [ -n "$(get_pid)" ] && echo "成功! PID: $(get_pid)" || echo "失败"
}

# 查看状态
status_check() {
    local pid=$(get_pid)
    [ -n "$pid" ] && echo "运行中 (PID: $pid)" || echo "未运行"
}

# ------------------------ 主逻辑 ------------------------
case "$1" in
    start)
        ENV=${2:-$DEFAULT_ENV}
        validate_env $ENV || exit 1
        stop_app
        start_app $ENV
        ;;
    stop)
        stop_app
        ;;
    restart)
        ENV=${2:-$DEFAULT_ENV}
        validate_env $ENV || exit 1
        stop_app
        start_app $ENV
        ;;
    status)
        status_check
        ;;
    *)
        usage
        ;;
esac
exit 0