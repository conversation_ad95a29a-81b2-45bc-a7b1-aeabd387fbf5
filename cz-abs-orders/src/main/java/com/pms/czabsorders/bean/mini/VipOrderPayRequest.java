package com.pms.czabsorders.bean.mini;

import com.pms.czpmsutils.request.BaseRequest;

public class VipOrderPayRequest extends BaseRequest {

    // 类型 1.订单支付  2.续住支付
    private Integer type;

    // 支付金额
    private Integer money;

    // 小时数
    private Integer hour;


    private Integer bookingOrderId;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getMoney() {
        return money;
    }

    public void setMoney(Integer money) {
        this.money = money;
    }

    public Integer getHour() {
        return hour;
    }

    public void setHour(Integer hour) {
        this.hour = hour;
    }

    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }
}

