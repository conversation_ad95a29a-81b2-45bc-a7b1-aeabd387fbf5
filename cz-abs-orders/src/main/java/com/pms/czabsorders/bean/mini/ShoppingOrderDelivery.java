package com.pms.czabsorders.bean.mini;

import com.pms.czpmsutils.request.PageBaseRequest;

// 订单商场送货
public class ShoppingOrderDelivery extends PageBaseRequest {

    private Integer id ;

    private Integer type;

    // 送物类型  1.人工 2机器人
    private Integer sendType = 1;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getSendType() {
        return sendType;
    }

    public void setSendType(Integer sendType) {
        this.sendType = sendType;
    }
}
