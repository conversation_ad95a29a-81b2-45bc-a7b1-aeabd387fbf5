package com.pms.czabsorders.bean;

import java.util.Map;

public class HourTrunAlwaysResponse {

    private Integer roomTypeId;

    private String roomTypeName;

    private String roomNo;

    private Integer roomInfoId;

    private Map<Integer,Map<Integer,Boolean>> dayDetails;


    public Integer getRoomTypeId() {
        return roomTypeId;
    }

    public void setRoomTypeId(Integer roomTypeId) {
        this.roomTypeId = roomTypeId;
    }

    public String getRoomTypeName() {
        return roomTypeName;
    }

    public void setRoomTypeName(String roomTypeName) {
        this.roomTypeName = roomTypeName;
    }

    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    public Integer getRoomInfoId() {
        return roomInfoId;
    }

    public void setRoomInfoId(Integer roomInfoId) {
        this.roomInfoId = roomInfoId;
    }

    public Map<Integer, Map<Integer, Boolean>> getDayDetails() {
        return dayDetails;
    }

    public void setDayDetails(Map<Integer, Map<Integer, Boolean>> dayDetails) {
        this.dayDetails = dayDetails;
    }
}
