package com.pms.czabsorders.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpenOrderUpdateStatusDto implements Serializable {
    private String sn;//订单流水号sn
    private Integer noticeType;//通知类型：1-协商取消，2-订单修改，3-其他状态变更
    private Integer result;//取消结果：1-同意，2-拒绝 （协商取消和订单修改时必传）
    private String refundAmount;//退款金额（协商取消必传，订单修改时如果有退款需传）
    private String refundFeeAmount;//退款手续费金额
    private String reason;//协商取消拒绝、订单修改拒绝和其他情况的状态变更时必传
}
