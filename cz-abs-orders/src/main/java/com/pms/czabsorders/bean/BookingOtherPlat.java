package com.pms.czabsorders.bean;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> @date 2022/01/26 08:17:10
 **/
@Data
public class BookingOtherPlat {

    //分布式唯一主键
    private Integer bookingOrderId;
    //酒店id
    private Integer hid;
    //连锁酒店id
    private Integer hotelGroupId;
    //
    private Integer teamCodeId;
    //订房数量
    private Integer roomCount;
    //订单时间
    private String orderTime;
    //预订人电话
    private String bookingPhone;
    //预订人信息
    private String bookingName;
    //拼音简拼
    private String bookingNamePyjp;
    //入住人身份证号
    private String bookingIdCode;
    //客源类型
    private Integer resourceId;
    //会员卡id
    private Integer cardId;
    //会员卡号
    private String cardNo;
    //协议单位id
    private Integer companyId;
    //协议单位名称
    private String companyName;
    //挂账账户
    private Integer companyAccountId;
    //入住开始时间
    private String checkinTime;
    //入住结束时间
    private String checkoutTime;
    //平台优惠金额
    private Integer preferentialPrice;
    //总金额-房费总额
    private Integer totalPrice;
    //付款金额
    private Integer payPrice;
    //住几天
    private Integer dayCount;
    //单价(缓存字段用于平台奖励)
    private Integer unitPrice;
    //保留时间判断
    private String keepTime;
    //来源酒店
    private Integer fromHid;
    //第三方平台订单号
    private String thirdPlatformOrderCode;
    //取卡码
    private String orderCode;
    //订单类型【日租 钟点 长包】
    private Integer orderType;
    //是否是 会议 0不是 1是
    private Integer isMeeting;
    //接单时间
    private String acceptTime;
    //订单状态 1.有效 2.NoShow 3.部分入住 4.全部入住 5.已取消 6.入住完成
    private Integer orderStatus;
    //下单来源【1.前台 2.携程预付 3.携程到付 4.携程闪住 5.美团预付 6.美团到付 7.美团溜溜住 8.艺龙预付 9.艺龙到付 10.去哪儿预付 11.去哪儿到付 12.飞猪预付 13.飞猪到付 14.阿里信用住  25.微信小程序  26.其他】
    private Integer fromType;
    //1.现付 2.预付
    private Integer payType;
    //订单冗余年 2017
    private Integer orderYear;
    //订单冗余年月
    private Integer orderYearMonth;
    //20170101
    private Integer businessDay;
    //班次ID
    private Integer classId;
    //房价方案
    private Integer rateCodeId;
    //创建时间
    private String createTime;
    //创建人
    private String createUserId;
    //
    private String createUserName;
    //修改时间
    private String updateTime;
    //修改人
    private String updateUserId;
    //
    private String updateUserName;
    //PMS对接ID
    private String pmsOrderId;
    //
    private String pmsHid;
    //优惠券
    private String userMagicId;
    //0非ota订单1已确认2未确认3已取消
    private Integer otaStatus;
    //
    private String uid;
    //押金
    private Integer deposit;
    //订单类型 0 前台 1APP 2小程序 3自助机
    private Integer type;
    //房型的汇总简称
    private String roomTypeSummary;
    //备注
    private String remark;
    //
    private String teamCodeName;
    //流水号
    private String sn;
    //预订取消原因
    private String cancelBookingLation;

    private List<RoomType> roomTypeList;


    /**
     * 预订房型
     */
    @Data
    public static class RoomType{

        // 预订房型id
        private Integer id;
        //房型ID
        private Integer roomTypeId;
        //预订房型数量
        private Integer roomTypeNum;
        //已排房数量
        private Integer hasRoomNum;
        //房价码ID
        private Integer priceCodeId;
        //房价码编号
        private String priceCode;
        //1有效 5已取消  订单状态
        private Integer orderState;
        //状态 1有效 0无效
        private Integer state;

        private List<Room> rooms;

    }

    /**
     * 预订房间
     */
    @Data
    public static class Room{
        //主键
        private Integer id;
        //房型idid
        private Integer bookingOrderRoomTypeId;
        //预订单ID
        private Integer bookingOrderId;
        //酒店id
        private Integer hid;
        //连锁酒店id
        private Integer hotelGroupId;
        //取卡码
        private String roomCode;
        //房型ID
        private Integer roomTypeId;
        //预订房间ID 0 标识未分房
        private Integer roomNumId;
        //订单状态 1有效 5已取消
        private Integer orderState;
        //登记ID
        private Integer registId;
        //预订房间号
        private String roomNum;
        //是否已住 0-未入住 1-已入住
        private Integer isCheckin;
        //是否已退房 0-未退房 1-已经退房
        private Integer isCheckout;
        //入住开始时间
        private String checkinTime;
        //入住结束时间
        private String checkoutTime;
        //是否已经排房 0 未排房 1 已排房
        private Integer rowRoom;
    }


}
