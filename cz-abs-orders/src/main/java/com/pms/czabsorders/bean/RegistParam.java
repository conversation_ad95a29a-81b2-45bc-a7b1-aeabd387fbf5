package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.BaseRequest;

import java.util.Date;

public class RegistParam extends BaseRequest {
    private Integer registId  ;
    private Integer companyId  ;
    private String compayName  ;
    private Integer companyAccountId  ;
    private Integer roomRateCodeId  ;
    private String roomRateCodeName  ;
    private Integer resourceId  ;
    private String resourceName  ;

    private Integer memberId  ;
    private String memberCard  ;

    private Integer checkinType;

    
    private Date checkoutTime  ;

    private Boolean noDposit;
    private Boolean noPrice;
    private Boolean autoAr;

    private String regInKeys;

    public Integer getRegistId() {
        return registId;
    }

    public void setRegistId(Integer registId) {
        this.registId = registId;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public String getCompayName() {
        return compayName;
    }

    public void setCompayName(String compayName) {
        this.compayName = compayName;
    }

    public Integer getCompanyAccountId() {
        return companyAccountId;
    }

    public void setCompanyAccountId(Integer companyAccountId) {
        this.companyAccountId = companyAccountId;
    }

    public Integer getRoomRateCodeId() {
        return roomRateCodeId;
    }

    public void setRoomRateCodeId(Integer roomRateCodeId) {
        this.roomRateCodeId = roomRateCodeId;
    }

    public String getRoomRateCodeName() {
        return roomRateCodeName;
    }

    public void setRoomRateCodeName(String roomRateCodeName) {
        this.roomRateCodeName = roomRateCodeName;
    }

    public Integer getResourceId() {
        return resourceId;
    }

    public void setResourceId(Integer resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public Date getCheckoutTime() {
        return checkoutTime;
    }

    public void setCheckoutTime(Date checkoutTime) {
        this.checkoutTime = checkoutTime;
    }

    public Boolean getNoDposit() {
        return noDposit;
    }

    public void setNoDposit(Boolean noDposit) {
        this.noDposit = noDposit;
    }

    public Boolean getNoPrice() {
        return noPrice;
    }

    public void setNoPrice(Boolean noPrice) {
        this.noPrice = noPrice;
    }

    public Boolean getAutoAr() {
        return autoAr;
    }

    public void setAutoAr(Boolean autoAr) {
        this.autoAr = autoAr;
    }

    public Integer getMemberId() {
        return memberId;
    }

    public void setMemberId(Integer memberId) {
        this.memberId = memberId;
    }

    public String getMemberCard() {
        return memberCard;
    }

    public void setMemberCard(String memberCard) {
        this.memberCard = memberCard;
    }

    public Integer getCheckinType() {
        return checkinType;
    }

    public void setCheckinType(Integer checkinType) {
        this.checkinType = checkinType;
    }

    public String getRegInKeys() {
        return regInKeys;
    }

    public void setRegInKeys(String regInKeys) {
        this.regInKeys = regInKeys;
    }
}
