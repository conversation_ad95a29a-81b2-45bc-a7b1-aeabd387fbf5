package com.pms.czabsorders.bean.mini;

import com.pms.czpmsutils.request.BaseRequest;

public class FindHotelMsg extends BaseRequest {

    private Integer roomId;

    private Integer hotelGroupId ;

    private String hotelId;

    private Integer roomInfoId;


    public Integer getRoomId() {
        return roomId;
    }

    public void setRoomId(Integer roomId) {
        this.roomId = roomId;
    }

    public Integer getHotelGroupId() {
        return hotelGroupId;
    }

    public void setHotelGroupId(Integer hotelGroupId) {
        this.hotelGroupId = hotelGroupId;
    }

    public String getHotelId() {
        return hotelId;
    }

    public void setHotelId(String hotelId) {
        this.hotelId = hotelId;
    }

    public Integer getRoomInfoId() {
        return roomId;
    }

    public void setRoomInfoId(Integer roomInfoId) {
        this.roomId = roomInfoId;
    }
}
