package com.pms.czabsorders.bean.mini;

import com.pms.czpmsutils.request.PageBaseRequest;

public class WxShoppingSearch extends PageBaseRequest {

    //
    private Integer registId;
    //
    private Integer roomNumId;
    //酒店id
    private Integer hid;
    //1.登记单 2.房间扫码
    private Integer goodType;
    //open_id
    private String openId;

    private  Integer sumPrice;

    public Integer getRegistId() {
        return registId;
    }

    public void setRegistId(Integer registId) {
        this.registId = registId;
    }

    public Integer getRoomNumId() {
        return roomNumId;
    }

    public void setRoomNumId(Integer roomNumId) {
        this.roomNumId = roomNumId;
    }

    public Integer getSumPrice() {
        return sumPrice;
    }

    public void setSumPrice(Integer sumPrice) {
        this.sumPrice = sumPrice;
    }

    @Override
    public Integer getHid() {
        return hid;
    }

    @Override
    public void setHid(Integer hid) {
        this.hid = hid;
    }

    public Integer getGoodType() {
        return goodType;
    }

    public void setGoodType(Integer goodType) {
        this.goodType = goodType;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
}
