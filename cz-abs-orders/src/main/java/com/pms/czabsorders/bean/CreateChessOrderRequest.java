package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.BaseRequest;

import java.util.Date;

public class CreateChessOrderRequest extends BaseRequest {
    //
    private Integer chessOrderId;
    //预订人电话
    private String bookingPhone;
    //预订人信息
    private String bookingName;
    //总金额-房费总额
    private Integer totalPrice;
    //付款金额
    private Integer payPrice;
    //保留时间判断
    private String keepTime;
    //订单类型 1-按小时  2-按天
    private Integer orderType;
    //订单状态 1.未支付 2-待消费 3-消费中  4-已完成 5-已取消
    private Integer orderStatus;
    //下单来源 1-微信小程序
    private Integer fromType;
    //********
    private Integer businessDay;
    //备注
    private String remark;
    //
    private Integer chessRoomInfoId;
    //
    private String chessRoomNo;
    //微信openid
    private String openId;
    //微信头像
    private String orderImage;
    //开始时间
    private Date beginTime;
    //结束时间
    private Date endTime;
    //结账时间
    private Date accountTime;
    //优惠金额
    private Integer prePrice;
    //套餐类型
    private Integer rateCodeId;
    //套餐名称
    private String rateCodeName;

    public void setChessOrderId(Integer chessOrderId) {
        this.chessOrderId = chessOrderId;
    }

    public Integer getChessOrderId() {
        return this.chessOrderId;
    }

    public void setBookingPhone(String bookingPhone) {
        this.bookingPhone = bookingPhone;
    }

    public String getBookingPhone() {
        return this.bookingPhone;
    }
    public void setBookingName(String bookingName) {
        this.bookingName = bookingName;
    }

    public String getBookingName() {
        return this.bookingName;
    }
    public void setTotalPrice(Integer totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Integer getTotalPrice() {
        return this.totalPrice;
    }
    public void setPayPrice(Integer payPrice) {
        this.payPrice = payPrice;
    }

    public Integer getPayPrice() {
        return this.payPrice;
    }
    public void setKeepTime(String keepTime) {
        this.keepTime = keepTime;
    }

    public String getKeepTime() {
        return this.keepTime;
    }
    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getOrderType() {
        return this.orderType;
    }
    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getOrderStatus() {
        return this.orderStatus;
    }
    public void setFromType(Integer fromType) {
        this.fromType = fromType;
    }

    public Integer getFromType() {
        return this.fromType;
    }
    public void setBusinessDay(Integer businessDay) {
        this.businessDay = businessDay;
    }

    public Integer getBusinessDay() {
        return this.businessDay;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return this.remark;
    }
    public void setChessRoomInfoId(Integer chessRoomInfoId) {
        this.chessRoomInfoId = chessRoomInfoId;
    }

    public Integer getChessRoomInfoId() {
        return this.chessRoomInfoId;
    }
    public void setChessRoomNo(String chessRoomNo) {
        this.chessRoomNo = chessRoomNo;
    }

    public String getChessRoomNo() {
        return this.chessRoomNo;
    }
    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getOpenId() {
        return this.openId;
    }
    public void setOrderImage(String orderImage) {
        this.orderImage = orderImage;
    }

    public String getOrderImage() {
        return this.orderImage;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getBeginTime() {
        return this.beginTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getEndTime() {
        return this.endTime;
    }

    public void setAccountTime(Date accountTime) {
        this.accountTime = accountTime;
    }

    public Date getAccountTime() {
        return this.accountTime;
    }
    public void setPrePrice(Integer prePrice) {
        this.prePrice = prePrice;
    }

    public Integer getPrePrice() {
        return this.prePrice;
    }
    public void setRateCodeId(Integer rateCodeId) {
        this.rateCodeId = rateCodeId;
    }

    public Integer getRateCodeId() {
        return this.rateCodeId;
    }
    public void setRateCodeName(String rateCodeName) {
        this.rateCodeName = rateCodeName;
    }

    public String getRateCodeName() {
        return this.rateCodeName;
    }
}
