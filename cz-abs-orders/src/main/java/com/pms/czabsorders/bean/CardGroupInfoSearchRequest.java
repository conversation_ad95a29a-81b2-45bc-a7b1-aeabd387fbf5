package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.PageBaseRequest;

public class CardGroupInfoSearchRequest extends PageBaseRequest {
    private Integer hotelGroupId;
    private Integer hid;

    private Integer id;
    private String cardNo;
    private String cardNo2;
    private String openId  ;
    private Integer innerCardNo;
    private String cardMaster;
    private String sta;
    private Integer cardMoney;
    private Integer cardTypeId;
    private String cardType;
    private Integer cardLevelId;
    private String cardLevel;
    private String cardSrc;
    private Integer guestId;
    private String cardName;
    private Integer cardSex;
    private String cardPhone;
    private String cardIdCode;
    private String priceCode;
    private Integer priceCodeId;
    private String posmode;
    private String code3;
    private String code4;
    private String code5;
    private String checkOutTime;
    private java.util.Date startTime;
    private java.util.Date endTime;
    private String password;
    private Integer salesPersonId;
    private String salesPersonName;
    private String extraFlag;
    private String cardFlag;
    private String crc;
    private String remark;
    private Integer pointPay;
    private Integer pointCharge;
    private Integer pointLastNum;
    private Integer pointLastNumLink;
    private Integer largessBalance;
    private Integer sale;
    private Integer pay;
    private String virtualChildCards;
    private Integer freeze;
    private Integer largessFreeze  ;
    private Integer credit;
    private Integer receipt;
    private Integer balance;
    private Integer lastNum;
    private Integer lastNumLink;
    private Integer businessDay;
    private Integer yearMonth;
    private Integer year;
    private java.util.Date staTransDate;
    private java.util.Date lastUserTime;
    private String createUserName;
    private String createUserId;
    private java.util.Date createTime;
    private String issHotel;
    private String updateUserName;
    private String updateUserId;
    private java.util.Date updateTime;

    public void setHotelGroupId(Integer value) {
        this.hotelGroupId = value;
    }

    public Integer getHotelGroupId() {
        return this.hotelGroupId;
    }
    public void setHid(Integer value) {
        this.hid = value;
    }

    public Integer getHid() {
        return this.hid;
    }
    public void setId(Integer value) {
        this.id = value;
    }

    public Integer getId() {
        return this.id;
    }
    public void setCardNo(String value) {
        this.cardNo = value;
    }

    public String getCardNo() {
        return this.cardNo;
    }
    public void setCardNo2(String value) {
        this.cardNo2 = value;
    }

    public String getCardNo2() {
        return this.cardNo2;
    }
    public void setInnerCardNo(Integer value) {
        this.innerCardNo = value;
    }

    public Integer getInnerCardNo() {
        return this.innerCardNo;
    }
    public void setCardMaster(String value) {
        this.cardMaster = value;
    }

    public String getCardMaster() {
        return this.cardMaster;
    }
    public void setSta(String value) {
        this.sta = value;
    }

    public String getSta() {
        return this.sta;
    }
    public void setCardMoney(Integer value) {
        this.cardMoney = value;
    }

    public Integer getCardMoney() {
        return this.cardMoney;
    }
    public void setCardTypeId(Integer value) {
        this.cardTypeId = value;
    }

    public Integer getCardTypeId() {
        return this.cardTypeId;
    }
    public void setCardType(String value) {
        this.cardType = value;
    }

    public String getCardType() {
        return this.cardType;
    }
    public void setCardLevelId(Integer value) {
        this.cardLevelId = value;
    }

    public Integer getCardLevelId() {
        return this.cardLevelId;
    }
    public void setCardLevel(String value) {
        this.cardLevel = value;
    }

    public String getCardLevel() {
        return this.cardLevel;
    }
    public void setCardSrc(String value) {
        this.cardSrc = value;
    }

    public String getCardSrc() {
        return this.cardSrc;
    }
    public void setGuestId(Integer value) {
        this.guestId = value;
    }

    public Integer getGuestId() {
        return this.guestId;
    }
    public void setCardName(String value) {
        this.cardName = value;
    }

    public String getCardName() {
        return this.cardName;
    }
    public void setCardPhone(String value) {
        this.cardPhone = value;
    }

    public String getCardPhone() {
        return this.cardPhone;
    }
    public void setCardIdCode(String value) {
        this.cardIdCode = value;
    }

    public String getCardIdCode() {
        return this.cardIdCode;
    }
    public void setPriceCode(String value) {
        this.priceCode = value;
    }

    public String getPriceCode() {
        return this.priceCode;
    }
    public void setPriceCodeId(Integer value) {
        this.priceCodeId = value;
    }

    public Integer getPriceCodeId() {
        return this.priceCodeId;
    }
    public void setPosmode(String value) {
        this.posmode = value;
    }

    public String getPosmode() {
        return this.posmode;
    }
    public void setCode3(String value) {
        this.code3 = value;
    }

    public String getCode3() {
        return this.code3;
    }
    public void setCode4(String value) {
        this.code4 = value;
    }

    public String getCode4() {
        return this.code4;
    }
    public void setCode5(String value) {
        this.code5 = value;
    }

    public String getCode5() {
        return this.code5;
    }
    public void setCheckOutTime(String value) {
        this.checkOutTime = value;
    }

    public String getCheckOutTime() {
        return this.checkOutTime;
    }

    public void setStartTime(java.util.Date value) {
        this.startTime = value;
    }

    public java.util.Date getStartTime() {
        return this.startTime;
    }

    public void setEndTime(java.util.Date value) {
        this.endTime = value;
    }

    public java.util.Date getEndTime() {
        return this.endTime;
    }
    public void setPassword(String value) {
        this.password = value;
    }

    public String getPassword() {
        return this.password;
    }
    public void setSalesPersonId(Integer value) {
        this.salesPersonId = value;
    }

    public Integer getSalesPersonId() {
        return this.salesPersonId;
    }
    public void setSalesPersonName(String value) {
        this.salesPersonName = value;
    }

    public String getSalesPersonName() {
        return this.salesPersonName;
    }
    public void setExtraFlag(String value) {
        this.extraFlag = value;
    }

    public String getExtraFlag() {
        return this.extraFlag;
    }
    public void setCardFlag(String value) {
        this.cardFlag = value;
    }

    public String getCardFlag() {
        return this.cardFlag;
    }
    public void setCrc(String value) {
        this.crc = value;
    }

    public String getCrc() {
        return this.crc;
    }
    public void setRemark(String value) {
        this.remark = value;
    }

    public String getRemark() {
        return this.remark;
    }
    public void setPointPay(Integer value) {
        this.pointPay = value;
    }

    public Integer getPointPay() {
        return this.pointPay;
    }
    public void setPointCharge(Integer value) {
        this.pointCharge = value;
    }

    public Integer getPointCharge() {
        return this.pointCharge;
    }
    public void setPointLastNum(Integer value) {
        this.pointLastNum = value;
    }

    public Integer getPointLastNum() {
        return this.pointLastNum;
    }
    public void setPointLastNumLink(Integer value) {
        this.pointLastNumLink = value;
    }

    public Integer getPointLastNumLink() {
        return this.pointLastNumLink;
    }
    public void setLargessBalance(Integer value) {
        this.largessBalance = value;
    }

    public Integer getLargessBalance() {
        return this.largessBalance;
    }
    public void setSale(Integer value) {
        this.sale = value;
    }

    public Integer getSale() {
        return this.sale;
    }
    public void setPay(Integer value) {
        this.pay = value;
    }

    public Integer getPay() {
        return this.pay;
    }
    public void setVirtualChildCards(String value) {
        this.virtualChildCards = value;
    }

    public String getVirtualChildCards() {
        return this.virtualChildCards;
    }
    public void setFreeze(Integer value) {
        this.freeze = value;
    }

    public Integer getFreeze() {
        return this.freeze;
    }
    public void setCredit(Integer value) {
        this.credit = value;
    }

    public Integer getCredit() {
        return this.credit;
    }
    public void setReceipt(Integer value) {
        this.receipt = value;
    }

    public Integer getReceipt() {
        return this.receipt;
    }
    public void setBalance(Integer value) {
        this.balance = value;
    }

    public Integer getBalance() {
        return this.balance;
    }
    public void setLastNum(Integer value) {
        this.lastNum = value;
    }

    public Integer getLastNum() {
        return this.lastNum;
    }
    public void setLastNumLink(Integer value) {
        this.lastNumLink = value;
    }

    public Integer getLastNumLink() {
        return this.lastNumLink;
    }
    public void setBusinessDay(Integer value) {
        this.businessDay = value;
    }

    public Integer getBusinessDay() {
        return this.businessDay;
    }
    public void setYearMonth(Integer value) {
        this.yearMonth = value;
    }

    public Integer getYearMonth() {
        return this.yearMonth;
    }
    public void setYear(Integer value) {
        this.year = value;
    }

    public Integer getYear() {
        return this.year;
    }

    public void setStaTransDate(java.util.Date value) {
        this.staTransDate = value;
    }

    public java.util.Date getStaTransDate() {
        return this.staTransDate;
    }

    public void setLastUserTime(java.util.Date value) {
        this.lastUserTime = value;
    }

    public java.util.Date getLastUserTime() {
        return this.lastUserTime;
    }
    public void setCreateUserName(String value) {
        this.createUserName = value;
    }

    public String getCreateUserName() {
        return this.createUserName;
    }
    public void setCreateUserId(String value) {
        this.createUserId = value;
    }

    public String getCreateUserId() {
        return this.createUserId;
    }

    public void setCreateTime(java.util.Date value) {
        this.createTime = value;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }
    public void setIssHotel(String value) {
        this.issHotel = value;
    }

    public String getIssHotel() {
        return this.issHotel;
    }
    public void setUpdateUserName(String value) {
        this.updateUserName = value;
    }

    public String getUpdateUserName() {
        return this.updateUserName;
    }
    public void setUpdateUserId(String value) {
        this.updateUserId = value;
    }

    public String getUpdateUserId() {
        return this.updateUserId;
    }

    public void setUpdateTime(java.util.Date value) {
        this.updateTime = value;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public Integer getLargessFreeze() {
        return largessFreeze;
    }

    public void setLargessFreeze(Integer largessFreeze) {
        this.largessFreeze = largessFreeze;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Integer getCardSex() {
        return cardSex;
    }

    public void setCardSex(Integer cardSex) {
        this.cardSex = cardSex;
    }
}


