package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.BaseRequest;

import java.util.List;

public class TransferAccontRequest extends BaseRequest {

    // 新的房间登记单
    private Integer registId;

    // 新的人
    private Integer registPersonId;

    // 转账的id列表
    private List<String> accountIds;

    // 上面账务列表中的registId集合
    private List<Integer> registIds;

    // 事由
    private String reason;

    // 备注
    private String remark;

    // 是否是团队账
    private Integer groupAccount;

    public Integer getRegistId() {
        return registId;
    }

    public void setRegistId(Integer registId) {
        this.registId = registId;
    }

    public Integer getRegistPersonId() {
        return registPersonId;
    }

    public void setRegistPersonId(Integer registPersonId) {
        this.registPersonId = registPersonId;
    }

    public List<String> getAccountIds() {
        return accountIds;
    }

    public void setAccountIds(List<String> accountIds) {
        this.accountIds = accountIds;
    }

    public List<Integer> getRegistIds() {
        return registIds;
    }

    public void setRegistIds(List<Integer> registIds) {
        this.registIds = registIds;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getGroupAccount() {
        return groupAccount;
    }

    public void setGroupAccount(Integer groupAccount) {
        this.groupAccount = groupAccount;
    }
}
