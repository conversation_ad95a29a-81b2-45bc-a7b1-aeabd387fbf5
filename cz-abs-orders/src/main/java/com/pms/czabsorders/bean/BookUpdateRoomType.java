package com.pms.czabsorders.bean;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 修改预订房型的参数
 */
public class BookUpdateRoomType  implements Serializable {

    private String sessionToken;

    // 预订单id
    private Integer bookId;

    // 开始时间
    private String startTime;

    // 结束时间
    private String endTime;

    // 时间是否修改  1.是 0.否
    private Integer timeUpa;

    // 预订房型集合
    private List<RoomType> roomTypes;



    // 预订房型
    public static class RoomType  {

        // 预订房型信息
        private Integer bookRoomTypeId;

        private Integer roomTypeId;

        // 价格是否修改
        private Integer priceUpa;

        // 预订房间数量
        private Integer num;

        // 排房数
        private Integer hasNum;

        // 房价方案
        private Integer priceCodeId;
        private String priceCode;

        // 添加
        private Boolean isAdd = true ;

        private List<PriceMsg> priceList;


        // 当前房型下的房间信息，如果新增或减少预订排房数，此集合里面都要有对应的加减，确保排房数和此集合的数量一致
        private List<Room> roomList;

        public Integer getBookRoomTypeId() {
            return bookRoomTypeId;
        }

        public void setBookRoomTypeId(Integer bookRoomTypeId) {
            this.bookRoomTypeId = bookRoomTypeId;
        }

        public Integer getPriceUpa() {
            return priceUpa;
        }

        public void setPriceUpa(Integer priceUpa) {
            this.priceUpa = priceUpa;
        }

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer hasRoomNum) {
            this.num = hasRoomNum;
        }

        public Integer getPriceCodeId() {
            return priceCodeId;
        }

        public void setPriceCodeId(Integer priceCodeId) {
            this.priceCodeId = priceCodeId;
        }

        public String getPriceCode() {
            return priceCode;
        }

        public Integer getRoomTypeId() {
            return roomTypeId;
        }

        public void setRoomTypeId(Integer roomTypeId) {
            this.roomTypeId = roomTypeId;
        }

        public void setPriceCode(String priceCode) {
            this.priceCode = priceCode;
        }

        public List<PriceMsg> getPriceList() {
            return priceList;
        }

        public void setPriceList(List<PriceMsg> priceList) {
            this.priceList = priceList;
        }

        public List<Room> getRoomList() {
            return roomList;
        }

        public void setRoomList(List<Room> roomList) {
            this.roomList = roomList;
        }

        public Boolean getAdd() {
            return isAdd;
        }

        public void setAdd(Boolean add) {
            isAdd = add;
        }

        public Integer getHasNum() {
            return hasNum;
        }

        public void setHasNum(Integer hasNum) {
            this.hasNum = hasNum;
        }
    }

    public static class PriceMsg  {

        private Integer date;
        private Integer price;

        public Integer getDate() {
            return date;
        }

        public void setDate(Integer date) {
            this.date = date;
        }

        public Integer getPrice() {
            return price;
        }

        public void setPrice(Integer price) {
            this.price = price;
        }
    }

    public static   class Room{
        // 预订排房id，添加则为0
        private Integer bookingOrderRoomNumId;
        private String roomNum;
        private Integer roomInfoId;

        public Integer getBookingOrderRoomNumId() {
            return bookingOrderRoomNumId;
        }

        public void setBookingOrderRoomNumId(Integer bookingOrderRoomNumId) {
            this.bookingOrderRoomNumId = bookingOrderRoomNumId;
        }

        public String getRoomNum() {
            return roomNum;
        }

        public void setRoomNum(String roomNum) {
            this.roomNum = roomNum;
        }

        public Integer getRoomInfoId() {
            return roomInfoId;
        }

        public void setRoomInfoId(Integer roomInfoId) {
            this.roomInfoId = roomInfoId;
        }
    }

    public Integer getBookId() {
        return bookId;
    }

    public void setBookId(Integer bookId) {
        this.bookId = bookId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getTimeUpa() {
        return timeUpa;
    }

    public void setTimeUpa(Integer timeUpa) {
        this.timeUpa = timeUpa;
    }

    public List<RoomType> getRoomTypes() {
        return roomTypes;
    }

    public Map<Integer,RoomType> getRoomTypeMap(){
        return roomTypes.stream().collect(Collectors.toMap(RoomType::getRoomTypeId, a -> a, (k1, k2) -> k1));
    };

    public void setRoomTypes(List<RoomType> roomTypes) {
        this.roomTypes = roomTypes;
    }


    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }


}
