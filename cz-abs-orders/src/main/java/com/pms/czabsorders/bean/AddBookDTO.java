package com.pms.czabsorders.bean;

import lombok.Data;

import java.util.List;

@Data
public class AddBookDTO {

    // 酒店id
    private String hotelId;

    // 预订人
    private String bookingName;
    // 预订电话
    private String bookingPhone;

    private String phone;

    // 入住类型  1.日租房 7.凌晨房  可以只传1
    private int checkInType;

    // 入住时间
    private String checkinTime;

    // 离店时间
    private String checkoutTime;

    // 来源 传18  阿拉丁平台
    private int fromType;

    // 是否团队  可空
    private int isGroup;
    // 团队名称  可空
    private String groupName;

    // 保留时间 HH:mm 预抵日 或者 预抵时间第二天
    private String keepTime;

    // 客源类型  1.散客 2.会员 3.协议单位 4.旅行社 5.订房平台 6.阿拉丁平台
    private int resourceId;

    // 备注
    private String remark;

    // 第三方订单号
    private String thirdPlatformOrderCode;

    // 以下  0.不开启 1.开启
    // 价格保密
    private int priceSecrecy = 0;
    // 信息保密
    private int infoSecrecy =0 ;
    // 0 不允许自助机办理 1 可以自助办理
    private int autoCheckIn = 1;
    // 自助免押金
    private int noDposit = 0;
    // 自助入住 免房费
    private int noPrice = 0;
    // 可自助续住
    private int continueRes = 1;
    // 结账自动转AR
    private int autoAr = 0;

    // 欢迎果盘
    private Integer welcomeFruitTray=0;
    // 查房
    private Integer checkRoom=0;
    // 生日礼物
    private Integer birthdayPresent=0;
    //早餐
    private Integer breakfastNum=0;

    private Integer type=0;

    // 房价码
    private int rateCodeId;
    // 房价码
    private String rateCodeName;

    // 会员信息
    private VipMsg vipMsg;

    // 会员信息
    private ArMsg arMsg;

    // 房间数
    private int roomCount;
    private List<RoomType> roomTypeList;

    // 房间简介 一帆风顺大床*1间 。与众不同大床*1间 。
    private String roomTypeSummary;

    @Data
    public static class RoomType {

        // 房型id
        private int roomTypeId;
        // 房型名称
        private String roomTypeName;
        // 房间id
        private int rateId;
        // 房价代码
        private String rateCode;
        // 房间数
        private int num;
        // 只填已选房的房间
        private List<Room> roomList;
        // 价格信息
        private List<Price> priceList;


    }

    @Data
    public static class ArMsg {
        // 订房平台id
        private int arId;
        // 订房平台名称
        private String arName;
    }
    @Data
    public static class ArAntMsg {
        //订房平台AR账户id
        private int id;
    }

    @Data
    public static class VipMsg {

        // 会员卡id
        private int cardId;
        // 会员卡号
            private String cardNo;

    }

    @Data
    public static class Price {

        // 日期
        private String date;
        // 价格
        private int price;

    }

    @Data
    public static class Room {

        // 房间id
        private int roomInfoId;
        // 房间号
        private String roomNum;

        private String name;

        private String code;

    }



}
