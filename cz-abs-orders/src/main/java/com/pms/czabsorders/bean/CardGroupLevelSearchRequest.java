package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.PageBaseRequest;

public class CardGroupLevelSearchRequest extends PageBaseRequest {
    private Integer hotelGroupId;
    private Integer hid;
    private Integer id;
    private String code;
    private String descript;
    private String descriptEn;
    private Integer cardTypeId;
    private String cardType;
    private Integer cardMoney;
    private Integer cardBalance;
    //售卡后卡积分
    private Integer cardPoint;
    private Integer cardUpgradeSort;
    private Integer cardUpgradeMinMoney;
    private Integer cardUpgradeMinCheckCount;
    private String guestCardType;
    private String extraInfo;
    private String isHalt;
    private String isEmember;
    private String numGenerateRule;
    private String notBlankControl;
    private String openRegisVerify;
    private String lastGeneratedNo;
    private Integer firstPayMin;
    private Integer firstPayMax;
    private Integer firstRechargeGivePercentage;
    private Integer firstRechargeGivePointPercentage;
    private Integer continuePayMin;
    private Integer continuePayMax;
    private Integer totalPayMin;
    private Integer regisValue;
    private Integer patchValue;
    private Integer continuedValue;
    private String pic;
    private Integer noShowPoint;
    private Integer pointUseLimit;
    private String nextDayPointUse;
    private String allowPointPrepay;
    private Integer limitBalance;
    private String isPhysical;
    private String isMustread;
    private String passwordValidate;
    private String suitScope;
    private String suitHotelCode;
    private Integer rechargeGivePercentage;
    private Integer rechargeGivePointPercentage;
    //结账消费积分赠送百分比
    private Integer checkOutPointPercentage;
    private Integer brithdayRechargeGivePercentage;
    private Integer brithdayRechargeGivePointPercentage;
    private String checkOutTime;
    private Integer priceCodeId;
    private String priceCode;
    private String machineDeposit;
    private String chargeProportion;
    private String isUnableManualUpgrade;
    private Integer listOrder;
    private String createUserName;
    private String createUserId;
    private java.util.Date createTime;
    private String updateUserName;
    private String updateUserId;
    private java.util.Date updateTime;

    public void setHotelGroupId(Integer value) {
        this.hotelGroupId = value;
    }

    public Integer getHotelGroupId() {
        return this.hotelGroupId;
    }
    public void setHid(Integer value) {
        this.hid = value;
    }

    public Integer getHid() {
        return this.hid;
    }
    public void setId(Integer value) {
        this.id = value;
    }

    public Integer getId() {
        return this.id;
    }
    public void setCode(String value) {
        this.code = value;
    }

    public String getCode() {
        return this.code;
    }
    public void setDescript(String value) {
        this.descript = value;
    }

    public String getDescript() {
        return this.descript;
    }
    public void setDescriptEn(String value) {
        this.descriptEn = value;
    }

    public String getDescriptEn() {
        return this.descriptEn;
    }
    public void setCardTypeId(Integer value) {
        this.cardTypeId = value;
    }

    public Integer getCardTypeId() {
        return this.cardTypeId;
    }
    public void setCardType(String value) {
        this.cardType = value;
    }

    public String getCardType() {
        return this.cardType;
    }
    public void setCardMoney(Integer value) {
        this.cardMoney = value;
    }

    public Integer getCheckOutPointPercentage() {
        return checkOutPointPercentage;
    }

    public void setCheckOutPointPercentage(Integer checkOutPointPercentage) {
        this.checkOutPointPercentage = checkOutPointPercentage;
    }

    public Integer getCardMoney() {
        return this.cardMoney;
    }
    public void setCardBalance(Integer value) {
        this.cardBalance = value;
    }

    public Integer getCardBalance() {
        return this.cardBalance;
    }
    public void setCardUpgradeSort(Integer value) {
        this.cardUpgradeSort = value;
    }

    public Integer getCardUpgradeSort() {
        return this.cardUpgradeSort;
    }
    public void setCardUpgradeMinMoney(Integer value) {
        this.cardUpgradeMinMoney = value;
    }

    public Integer getCardUpgradeMinMoney() {
        return this.cardUpgradeMinMoney;
    }
    public void setCardUpgradeMinCheckCount(Integer value) {
        this.cardUpgradeMinCheckCount = value;
    }

    public Integer getCardUpgradeMinCheckCount() {
        return this.cardUpgradeMinCheckCount;
    }
    public void setGuestCardType(String value) {
        this.guestCardType = value;
    }

    public String getGuestCardType() {
        return this.guestCardType;
    }
    public void setExtraInfo(String value) {
        this.extraInfo = value;
    }

    public String getExtraInfo() {
        return this.extraInfo;
    }
    public void setIsHalt(String value) {
        this.isHalt = value;
    }

    public String getIsHalt() {
        return this.isHalt;
    }
    public void setIsEmember(String value) {
        this.isEmember = value;
    }

    public String getIsEmember() {
        return this.isEmember;
    }
    public void setNumGenerateRule(String value) {
        this.numGenerateRule = value;
    }

    public String getNumGenerateRule() {
        return this.numGenerateRule;
    }
    public void setNotBlankControl(String value) {
        this.notBlankControl = value;
    }

    public String getNotBlankControl() {
        return this.notBlankControl;
    }
    public void setOpenRegisVerify(String value) {
        this.openRegisVerify = value;
    }

    public String getOpenRegisVerify() {
        return this.openRegisVerify;
    }
    public void setLastGeneratedNo(String value) {
        this.lastGeneratedNo = value;
    }

    public String getLastGeneratedNo() {
        return this.lastGeneratedNo;
    }
    public void setFirstPayMin(Integer value) {
        this.firstPayMin = value;
    }

    public Integer getFirstPayMin() {
        return this.firstPayMin;
    }
    public void setFirstPayMax(Integer value) {
        this.firstPayMax = value;
    }

    public Integer getFirstPayMax() {
        return this.firstPayMax;
    }
    public void setFirstRechargeGivePercentage(Integer value) {
        this.firstRechargeGivePercentage = value;
    }

    public Integer getFirstRechargeGivePercentage() {
        return this.firstRechargeGivePercentage;
    }
    public void setFirstRechargeGivePointPercentage(Integer value) {
        this.firstRechargeGivePointPercentage = value;
    }

    public Integer getFirstRechargeGivePointPercentage() {
        return this.firstRechargeGivePointPercentage;
    }
    public void setContinuePayMin(Integer value) {
        this.continuePayMin = value;
    }

    public Integer getContinuePayMin() {
        return this.continuePayMin;
    }
    public void setContinuePayMax(Integer value) {
        this.continuePayMax = value;
    }

    public Integer getContinuePayMax() {
        return this.continuePayMax;
    }
    public void setTotalPayMin(Integer value) {
        this.totalPayMin = value;
    }

    public Integer getTotalPayMin() {
        return this.totalPayMin;
    }
    public void setRegisValue(Integer value) {
        this.regisValue = value;
    }

    public Integer getRegisValue() {
        return this.regisValue;
    }
    public void setPatchValue(Integer value) {
        this.patchValue = value;
    }

    public Integer getPatchValue() {
        return this.patchValue;
    }
    public void setContinuedValue(Integer value) {
        this.continuedValue = value;
    }

    public Integer getContinuedValue() {
        return this.continuedValue;
    }
    public void setPic(String value) {
        this.pic = value;
    }

    public String getPic() {
        return this.pic;
    }
    public void setNoShowPoint(Integer value) {
        this.noShowPoint = value;
    }

    public Integer getNoShowPoint() {
        return this.noShowPoint;
    }
    public void setPointUseLimit(Integer value) {
        this.pointUseLimit = value;
    }

    public Integer getPointUseLimit() {
        return this.pointUseLimit;
    }
    public void setNextDayPointUse(String value) {
        this.nextDayPointUse = value;
    }

    public String getNextDayPointUse() {
        return this.nextDayPointUse;
    }
    public void setAllowPointPrepay(String value) {
        this.allowPointPrepay = value;
    }

    public String getAllowPointPrepay() {
        return this.allowPointPrepay;
    }
    public void setLimitBalance(Integer value) {
        this.limitBalance = value;
    }

    public Integer getLimitBalance() {
        return this.limitBalance;
    }
    public void setIsPhysical(String value) {
        this.isPhysical = value;
    }

    public String getIsPhysical() {
        return this.isPhysical;
    }
    public void setIsMustread(String value) {
        this.isMustread = value;
    }

    public String getIsMustread() {
        return this.isMustread;
    }
    public void setPasswordValidate(String value) {
        this.passwordValidate = value;
    }

    public String getPasswordValidate() {
        return this.passwordValidate;
    }
    public void setSuitScope(String value) {
        this.suitScope = value;
    }

    public String getSuitScope() {
        return this.suitScope;
    }
    public void setSuitHotelCode(String value) {
        this.suitHotelCode = value;
    }

    public String getSuitHotelCode() {
        return this.suitHotelCode;
    }
    public void setRechargeGivePercentage(Integer value) {
        this.rechargeGivePercentage = value;
    }

    public Integer getRechargeGivePercentage() {
        return this.rechargeGivePercentage;
    }
    public void setRechargeGivePointPercentage(Integer value) {
        this.rechargeGivePointPercentage = value;
    }

    public Integer getRechargeGivePointPercentage() {
        return this.rechargeGivePointPercentage;
    }
    public void setBrithdayRechargeGivePercentage(Integer value) {
        this.brithdayRechargeGivePercentage = value;
    }

    public Integer getBrithdayRechargeGivePercentage() {
        return this.brithdayRechargeGivePercentage;
    }
    public void setBrithdayRechargeGivePointPercentage(Integer value) {
        this.brithdayRechargeGivePointPercentage = value;
    }

    public Integer getBrithdayRechargeGivePointPercentage() {
        return this.brithdayRechargeGivePointPercentage;
    }
    public void setCheckOutTime(String value) {
        this.checkOutTime = value;
    }

    public String getCheckOutTime() {
        return this.checkOutTime;
    }
    public void setPriceCodeId(Integer value) {
        this.priceCodeId = value;
    }

    public Integer getPriceCodeId() {
        return this.priceCodeId;
    }
    public void setPriceCode(String value) {
        this.priceCode = value;
    }

    public String getPriceCode() {
        return this.priceCode;
    }
    public void setMachineDeposit(String value) {
        this.machineDeposit = value;
    }

    public String getMachineDeposit() {
        return this.machineDeposit;
    }
    public void setChargeProportion(String value) {
        this.chargeProportion = value;
    }

    public String getChargeProportion() {
        return this.chargeProportion;
    }
    public void setIsUnableManualUpgrade(String value) {
        this.isUnableManualUpgrade = value;
    }

    public String getIsUnableManualUpgrade() {
        return this.isUnableManualUpgrade;
    }
    public void setListOrder(Integer value) {
        this.listOrder = value;
    }

    public Integer getListOrder() {
        return this.listOrder;
    }
    public void setCreateUserName(String value) {
        this.createUserName = value;
    }

    public String getCreateUserName() {
        return this.createUserName;
    }
    public void setCreateUserId(String value) {
        this.createUserId = value;
    }

    public String getCreateUserId() {
        return this.createUserId;
    }

    public void setCreateTime(java.util.Date value) {
        this.createTime = value;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }
    public void setUpdateUserName(String value) {
        this.updateUserName = value;
    }

    public String getUpdateUserName() {
        return this.updateUserName;

    }
    public void setUpdateUserId(String value) {
        this.updateUserId = value;
    }

    public String getUpdateUserId() {
        return this.updateUserId;
    }

    public void setUpdateTime(java.util.Date value) {
        this.updateTime = value;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public Integer getCardPoint() {
        return cardPoint;
    }

    public void setCardPoint(Integer cardPoint) {
        this.cardPoint = cardPoint;
    }
}
