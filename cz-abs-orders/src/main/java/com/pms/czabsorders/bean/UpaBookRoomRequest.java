package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.BaseRequest;
import com.pms.pmsorder.bean.search.BookingOrderRoomNumSearch;

import java.util.List;

public class UpaBookRoomRequest extends BaseRequest {

    private Integer bookingOrderId;

    private String startTime;

    private String endTime;

    private List<BookingOrderRoomNumSearch> ids;

    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public List<BookingOrderRoomNumSearch> getIds() {
        return ids;
    }

    public void setIds(List<BookingOrderRoomNumSearch> ids) {
        this.ids = ids;
    }
}

