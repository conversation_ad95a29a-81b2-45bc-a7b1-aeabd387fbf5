package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.UpdateBookingOrderInfoRequest;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 修改预订房型的参数
 *  测试版
 *
 *  房型传递：
 *      价格、分房数量、已排房集合（已入住的房间不需要传）、房型价格
 *      房间信息：房间号，房间id
 *
 */
public class BookUpdateRoomTypeTwo implements Serializable {

    private String sessionToken;

    // 预订单id
    private Integer bookId;

    // 时间是否修改  1.是 0.否
    private Integer timeUpa = 1;

    private String startTime;

    private String endTime;

    // 预订房型集合
    private List<RoomType> roomTypes;


    private UpdateBookingOrderInfoRequest updateBookingOrderInfoRequest;

    // 预订房型
    public static class RoomType  {

        private Integer roomTypeId;


        // 预订房间数量，预订的总条数
        private Integer num;

        // 房价方案
        private Integer priceCodeId;
        private String priceCode;

        private List<PriceMsg> priceList;

        /**
         * 传排房未入住的房间列表
         */
        private List<Room> roomList;

        public Integer getNum() {
            return num;
        }

        public void setNum(Integer hasRoomNum) {
            this.num = hasRoomNum;
        }

        public Integer getPriceCodeId() {
            return priceCodeId;
        }

        public void setPriceCodeId(Integer priceCodeId) {
            this.priceCodeId = priceCodeId;
        }

        public String getPriceCode() {
            return priceCode;
        }

        public Integer getRoomTypeId() {
            return roomTypeId;
        }

        public void setRoomTypeId(Integer roomTypeId) {
            this.roomTypeId = roomTypeId;
        }

        public void setPriceCode(String priceCode) {
            this.priceCode = priceCode;
        }

        public List<PriceMsg> getPriceList() {
            return priceList;
        }

        public void setPriceList(List<PriceMsg> priceList) {
            this.priceList = priceList;
        }

        public List<Room> getRoomList() {
            return roomList;
        }

        public void setRoomList(List<Room> roomList) {
            this.roomList = roomList;
        }
    }

    public static class PriceMsg  {

        private Integer date;
        private Integer price;

        public Integer getDate() {
            return date;
        }

        public void setDate(Integer date) {
            this.date = date;
        }

        public Integer getPrice() {
            return price;
        }

        public void setPrice(Integer price) {
            this.price = price;
        }
    }

    public static class Room{

        private String roomNum;
        private Integer roomInfoId;

        public String getRoomNum() {
            return roomNum;
        }

        public void setRoomNum(String roomNum) {
            this.roomNum = roomNum;
        }

        public Integer getRoomInfoId() {
            return roomInfoId;
        }

        public void setRoomInfoId(Integer roomInfoId) {
            this.roomInfoId = roomInfoId;
        }
    }

    public Integer getBookId() {
        return bookId;
    }

    public void setBookId(Integer bookId) {
        this.bookId = bookId;
    }

    public List<RoomType> getRoomTypes() {
        return roomTypes;
    }

    public Map<Integer,RoomType> getRoomTypeMap(){
        return roomTypes.stream().collect(Collectors.toMap(RoomType::getRoomTypeId, a -> a, (k1, k2) -> k1));
    };

    public void setRoomTypes(List<RoomType> roomTypes) {
        this.roomTypes = roomTypes;
    }


    public String getSessionToken() {
        return sessionToken;
    }

    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }


    public Integer getTimeUpa() {
        return timeUpa;
    }

    public void setTimeUpa(Integer timeUpa) {
        this.timeUpa = timeUpa;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public UpdateBookingOrderInfoRequest getUpdateBookingOrderInfoRequest() {
        return updateBookingOrderInfoRequest;
    }

    public void setUpdateBookingOrderInfoRequest(UpdateBookingOrderInfoRequest updateBookingOrderInfoRequest) {
        this.updateBookingOrderInfoRequest = updateBookingOrderInfoRequest;
    }
}
