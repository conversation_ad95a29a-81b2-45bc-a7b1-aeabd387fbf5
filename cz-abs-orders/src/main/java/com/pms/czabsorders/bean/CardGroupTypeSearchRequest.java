package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.PageBaseRequest;

public class CardGroupTypeSearchRequest extends PageBaseRequest {
    private Integer hotelGroupId;
    private Integer hid;
    private Integer id;
    private String code;
    private String descript;
    private String descriptEn;
    private String isPoint;
    private String isPointInSelf;
    private String isPointOutSelf;
    private Integer pointValidDays;
    private String allowTransPointType;
    private String isAccount;
    private String largessWay;
    private String paySelfHotel;
    private String isPostSelf;
    private String isAnonymous;
    private String allowSupplement;
    private String allowAccount;
    private String allowOnlineUse;
    private String isGroupCard;
    private String isGroup;
    private String regisLimit;
    private String groupCode;
    private String numGenerateRule;
    private String lastGeneratedNo;
    private String issMode;
    private String newCardLocationSta;
    private String activeWay;
    private Integer validDays;
    private String initPasswd;
    private String scope;
    private String isPhysical;
    private String isMustread;
    private String passwordValidate;
    private String remark;
    private String extraInfo;
    private String isTreatAccnt;
    private String grpCardCtl;
    private String isResrvCard;
    private String pointUseCtl;
    private String pointBackCard;
    private String isDivide;
    private String isHalt;
    private Integer listOrder;
    private String createUserName;
    private String createUserId;
    private java.util.Date createTime;
    private String updateUserName;
    private String updateUserId;
    private java.util.Date updateTime;

    public void setHotelGroupId(Integer value) {
        this.hotelGroupId = value;
    }

    public Integer getHotelGroupId() {
        return this.hotelGroupId;
    }
    public void setHid(Integer value) {
        this.hid = value;
    }

    public Integer getHid() {
        return this.hid;
    }
    public void setId(Integer value) {
        this.id = value;
    }

    public Integer getId() {
        return this.id;
    }
    public void setCode(String value) {
        this.code = value;
    }

    public String getCode() {
        return this.code;
    }
    public void setDescript(String value) {
        this.descript = value;
    }

    public String getDescript() {
        return this.descript;
    }
    public void setDescriptEn(String value) {
        this.descriptEn = value;
    }

    public String getDescriptEn() {
        return this.descriptEn;
    }
    public void setIsPoint(String value) {
        this.isPoint = value;
    }

    public String getIsPoint() {
        return this.isPoint;
    }
    public void setIsPointInSelf(String value) {
        this.isPointInSelf = value;
    }

    public String getIsPointInSelf() {
        return this.isPointInSelf;
    }
    public void setIsPointOutSelf(String value) {
        this.isPointOutSelf = value;
    }

    public String getIsPointOutSelf() {
        return this.isPointOutSelf;
    }
    public void setPointValidDays(Integer value) {
        this.pointValidDays = value;
    }

    public Integer getPointValidDays() {
        return this.pointValidDays;
    }
    public void setAllowTransPointType(String value) {
        this.allowTransPointType = value;
    }

    public String getAllowTransPointType() {
        return this.allowTransPointType;
    }
    public void setIsAccount(String value) {
        this.isAccount = value;
    }

    public String getIsAccount() {
        return this.isAccount;
    }
    public void setLargessWay(String value) {
        this.largessWay = value;
    }

    public String getLargessWay() {
        return this.largessWay;
    }
    public void setPaySelfHotel(String value) {
        this.paySelfHotel = value;
    }

    public String getPaySelfHotel() {
        return this.paySelfHotel;
    }
    public void setIsPostSelf(String value) {
        this.isPostSelf = value;
    }

    public String getIsPostSelf() {
        return this.isPostSelf;
    }
    public void setIsAnonymous(String value) {
        this.isAnonymous = value;
    }

    public String getIsAnonymous() {
        return this.isAnonymous;
    }
    public void setAllowSupplement(String value) {
        this.allowSupplement = value;
    }

    public String getAllowSupplement() {
        return this.allowSupplement;
    }
    public void setAllowAccount(String value) {
        this.allowAccount = value;
    }

    public String getAllowAccount() {
        return this.allowAccount;
    }
    public void setAllowOnlineUse(String value) {
        this.allowOnlineUse = value;
    }

    public String getAllowOnlineUse() {
        return this.allowOnlineUse;
    }
    public void setIsGroupCard(String value) {
        this.isGroupCard = value;
    }

    public String getIsGroupCard() {
        return this.isGroupCard;
    }
    public void setIsGroup(String value) {
        this.isGroup = value;
    }

    public String getIsGroup() {
        return this.isGroup;
    }
    public void setRegisLimit(String value) {
        this.regisLimit = value;
    }

    public String getRegisLimit() {
        return this.regisLimit;
    }
    public void setGroupCode(String value) {
        this.groupCode = value;
    }

    public String getGroupCode() {
        return this.groupCode;
    }
    public void setNumGenerateRule(String value) {
        this.numGenerateRule = value;
    }

    public String getNumGenerateRule() {
        return this.numGenerateRule;
    }
    public void setLastGeneratedNo(String value) {
        this.lastGeneratedNo = value;
    }

    public String getLastGeneratedNo() {
        return this.lastGeneratedNo;
    }
    public void setIssMode(String value) {
        this.issMode = value;
    }

    public String getIssMode() {
        return this.issMode;
    }
    public void setNewCardLocationSta(String value) {
        this.newCardLocationSta = value;
    }

    public String getNewCardLocationSta() {
        return this.newCardLocationSta;
    }
    public void setActiveWay(String value) {
        this.activeWay = value;
    }

    public String getActiveWay() {
        return this.activeWay;
    }
    public void setValidDays(Integer value) {
        this.validDays = value;
    }

    public Integer getValidDays() {
        return this.validDays;
    }
    public void setInitPasswd(String value) {
        this.initPasswd = value;
    }

    public String getInitPasswd() {
        return this.initPasswd;
    }
    public void setScope(String value) {
        this.scope = value;
    }

    public String getScope() {
        return this.scope;
    }
    public void setIsPhysical(String value) {
        this.isPhysical = value;
    }

    public String getIsPhysical() {
        return this.isPhysical;
    }
    public void setIsMustread(String value) {
        this.isMustread = value;
    }

    public String getIsMustread() {
        return this.isMustread;
    }
    public void setPasswordValidate(String value) {
        this.passwordValidate = value;
    }

    public String getPasswordValidate() {
        return this.passwordValidate;
    }
    public void setRemark(String value) {
        this.remark = value;
    }

    public String getRemark() {
        return this.remark;
    }
    public void setExtraInfo(String value) {
        this.extraInfo = value;
    }

    public String getExtraInfo() {
        return this.extraInfo;
    }
    public void setIsTreatAccnt(String value) {
        this.isTreatAccnt = value;
    }

    public String getIsTreatAccnt() {
        return this.isTreatAccnt;
    }
    public void setGrpCardCtl(String value) {
        this.grpCardCtl = value;
    }

    public String getGrpCardCtl() {
        return this.grpCardCtl;
    }
    public void setIsResrvCard(String value) {
        this.isResrvCard = value;
    }

    public String getIsResrvCard() {
        return this.isResrvCard;
    }
    public void setPointUseCtl(String value) {
        this.pointUseCtl = value;
    }

    public String getPointUseCtl() {
        return this.pointUseCtl;
    }
    public void setPointBackCard(String value) {
        this.pointBackCard = value;
    }

    public String getPointBackCard() {
        return this.pointBackCard;
    }
    public void setIsDivide(String value) {
        this.isDivide = value;
    }

    public String getIsDivide() {
        return this.isDivide;
    }
    public void setIsHalt(String value) {
        this.isHalt = value;
    }

    public String getIsHalt() {
        return this.isHalt;
    }
    public void setListOrder(Integer value) {
        this.listOrder = value;
    }

    public Integer getListOrder() {
        return this.listOrder;
    }
    public void setCreateUserName(String value) {
        this.createUserName = value;
    }

    public String getCreateUserName() {
        return this.createUserName;
    }
    public void setCreateUserId(String value) {
        this.createUserId = value;
    }

    public String getCreateUserId() {
        return this.createUserId;
    }

    public void setCreateTime(java.util.Date value) {
        this.createTime = value;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }
    public void setUpdateUserName(String value) {
        this.updateUserName = value;
    }

    public String getUpdateUserName() {
        return this.updateUserName;
    }
    public void setUpdateUserId(String value) {
        this.updateUserId = value;
    }

    public String getUpdateUserId() {
        return this.updateUserId;
    }

    public void setUpdateTime(java.util.Date value) {
        this.updateTime = value;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }
}
