package com.pms.czabsorders.bean.out;

public class OutHotelGroupInfo {
    //唯一标识
    private String uuid;

    private Integer chainId  ;
    //集团名称
    private String chainName  ;
    //集团电话
    private String telephone ;
    //集团描述
    private String chainDesc  ;
    //集团地址
    private String addr  ;
    //负责人
    private String leader  ;
    //联系人电话
    private String phone  ;
    //是否启用
    private Integer enable  ;
    //被谁创建
    private String createBy;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getChainName() {
        return chainName;
    }

    public void setChainName(String chainName) {
        this.chainName = chainName;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getChainDesc() {
        return chainDesc;
    }

    public void setChainDesc(String chainDesc) {
        this.chainDesc = chainDesc;
    }

    public String getAddr() {
        return addr;
    }

    public void setAddr(String addr) {
        this.addr = addr;
    }

    public String getLeader() {
        return leader;
    }

    public void setLeader(String leader) {
        this.leader = leader;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Integer getChainId() {
        return chainId;
    }
    public void setChainId(Integer chainId) {
        this.chainId = chainId;
    }
}
