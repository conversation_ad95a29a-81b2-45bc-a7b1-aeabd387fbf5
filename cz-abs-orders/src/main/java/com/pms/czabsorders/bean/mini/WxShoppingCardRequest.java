package com.pms.czabsorders.bean.mini;

import com.pms.czpmsutils.request.PageBaseRequest;

import java.util.List;

public class WxShoppingCardRequest extends PageBaseRequest {

    //
    private Integer registId;
    //
    private Integer roomNumId;
    //
    private String roomNum;
    //总价
    private Integer sumPrice;
    //酒店id
    private Integer hid;

    private Integer cardId ;

    private Integer cardGroupId ;

    private String cardNo;
    //是否核算库存 1.核算 0.不核算
    private Integer hotelIsCheckGoodsNum;
    //
    private Integer hotelGroupId;
    //1.登记单 2.房间扫码
    private Integer goodType;
    //open_id
    private String openId;

    private String hotelId;

    private List<GoodInfo> goodList;


    public static class GoodInfo {

        private Integer goodsNum;
        private Integer price;
        private Integer goodsInfoId;
        private String goodsInfoName;
        private Integer goodsClassId;
        private String goodsClassName;

        public Integer getGoodsNum() {
            return goodsNum;
        }

        public void setGoodsNum(Integer goodsNum) {
            this.goodsNum = goodsNum;
        }

        public Integer getPrice() {
            return price;
        }

        public void setPrice(Integer price) {
            this.price = price;
        }

        public Integer getGoodsInfoId() {
            return goodsInfoId;
        }

        public void setGoodsInfoId(Integer goodsInfoId) {
            this.goodsInfoId = goodsInfoId;
        }

        public String getGoodsInfoName() {
            return goodsInfoName;
        }

        public void setGoodsInfoName(String goodsInfoName) {
            this.goodsInfoName = goodsInfoName;
        }

        public Integer getGoodsClassId() {
            return goodsClassId;
        }

        public void setGoodsClassId(Integer goodsClassId) {
            this.goodsClassId = goodsClassId;
        }

        public String getGoodsClassName() {
            return goodsClassName;
        }

        public void setGoodsClassName(String goodsClassName) {
            this.goodsClassName = goodsClassName;
        }
    }


    public Integer getRegistId() {
        return registId;
    }

    public void setRegistId(Integer registId) {
        this.registId = registId;
    }

    public Integer getRoomNumId() {
        return roomNumId;
    }

    public void setRoomNumId(Integer roomNumId) {
        this.roomNumId = roomNumId;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public Integer getSumPrice() {
        return sumPrice;
    }

    public void setSumPrice(Integer sumPrice) {
        this.sumPrice = sumPrice;
    }

    @Override
    public Integer getHid() {
        return hid;
    }

    @Override
    public void setHid(Integer hid) {
        this.hid = hid;
    }

    @Override
    public Integer getHotelGroupId() {
        return hotelGroupId;
    }

    @Override
    public void setHotelGroupId(Integer hotelGroupId) {
        this.hotelGroupId = hotelGroupId;
    }

    public Integer getGoodType() {
        return goodType;
    }

    public void setGoodType(Integer goodType) {
        this.goodType = goodType;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public List<GoodInfo> getGoodList() {
        return goodList;
    }

    public void setGoodList(List<GoodInfo> goodList) {
        this.goodList = goodList;
    }

    public Integer getCardId() {
        return cardId;
    }

    public void setCardId(Integer cardId) {
        this.cardId = cardId;
    }

    public Integer getCardGroupId() {
        return cardGroupId;
    }

    public void setCardGroupId(Integer cardGroupId) {
        this.cardGroupId = cardGroupId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Integer getHotelIsCheckGoodsNum() {
        return hotelIsCheckGoodsNum;
    }

    public void setHotelIsCheckGoodsNum(Integer hotelIsCheckGoodsNum) {
        this.hotelIsCheckGoodsNum = hotelIsCheckGoodsNum;
    }

    public String getHotelId() {
        return hotelId;
    }

    public void setHotelId(String hotelId) {
        this.hotelId = hotelId;
    }
}
