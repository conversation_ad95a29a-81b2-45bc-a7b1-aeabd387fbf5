package com.pms.czabsorders.bean;

import com.pms.pmsorder.bean.view.BookingOrderRoomNumView;

import java.util.List;

/**
 * 房态图返回
 */
public class RoomStateResult {

    private Integer roomInfoId;
    private String roomNum;
    private Integer roomTypeId;
    private String roomTypeName;
    private Integer roomNumState;

    private Integer buildingId;
    private Integer floorId;

    private Integer registId;
    private Integer bookingOrderId;
    private Integer bookingOrderRoomNumId;
    private String peopleName;
    private Integer teamCodeId = 0;
    private String teamCodeName;
    private Integer isMainRoom  = 0;
    private Integer sumPay;
    private Integer sumSale;

    private Integer roomRateId;
    private String rateCode;


    // 客源类型 名称
    private Integer resourceId ;
    private String resourceName ;
    private String resTitle;

    // 预订列表
    private List<BookingOrderRoomNumView> bookingOrderRoomNumList;

    // 维修id
    private Integer roomRepairId;
    private String descr ;

    private String beginTime;
    private String endTime;

    private Integer otaType = 0;
    private String otaIcon ;
    private String otaPayType;



    public Integer getRoomInfoId() {
        return roomInfoId;
    }

    public void setRoomInfoId(Integer roomInfoId) {
        this.roomInfoId = roomInfoId;
    }

    public String getRoomNum() {
        return roomNum;
    }

    public void setRoomNum(String roomNum) {
        this.roomNum = roomNum;
    }

    public Integer getRoomTypeId() {
        return roomTypeId;
    }
    public String getRoomTypeIdStr() {
        return roomTypeId+",";
    }

    public void setRoomTypeId(Integer roomTypeId) {
        this.roomTypeId = roomTypeId;
    }

    public String getRoomTypeName() {
        return roomTypeName;
    }

    public void setRoomTypeName(String roomTypeName) {
        this.roomTypeName = roomTypeName;
    }

    public Integer getRoomNumState() {
        return roomNumState;
    }

    public void setRoomNumState(Integer roomNumState) {
        this.roomNumState = roomNumState;
    }

    public Integer getRegistId() {
        return registId;
    }

    public void setRegistId(Integer registId) {
        this.registId = registId;
    }

    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }

    public Integer getBookingOrderRoomNumId() {
        return bookingOrderRoomNumId;
    }

    public void setBookingOrderRoomNumId(Integer bookingOrderRoomNumId) {
        this.bookingOrderRoomNumId = bookingOrderRoomNumId;
    }

    public String getPeopleName() {
        return peopleName;
    }

    public void setPeopleName(String peopleName) {
        this.peopleName = peopleName;
    }

    public Integer getTeamCodeId() {
        return teamCodeId;
    }

    public void setTeamCodeId(Integer teamCodeId) {
        this.teamCodeId = teamCodeId;
    }

    public String getTeamCodeName() {
        return teamCodeName;
    }

    public void setTeamCodeName(String teamCodeName) {
        this.teamCodeName = teamCodeName;
    }

    public Integer getRoomRepairId() {
        return roomRepairId;
    }

    public void setRoomRepairId(Integer roomRepairId) {
        this.roomRepairId = roomRepairId;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getSumPay() {
        return sumPay;
    }

    public void setSumPay(Integer sumPay) {
        this.sumPay = sumPay;
    }

    public Integer getSumSale() {
        return sumSale;
    }

    public void setSumSale(Integer sumSale) {
        this.sumSale = sumSale;
    }

    public Integer getResourceId() {
        return resourceId;
    }

    public void setResourceId(Integer resourceId) {
        this.resourceId = resourceId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public List<BookingOrderRoomNumView> getBookingOrderRoomNumList() {
        return bookingOrderRoomNumList;
    }

    public void setBookingOrderRoomNumList(List<BookingOrderRoomNumView> bookingOrderRoomNumList) {
        this.bookingOrderRoomNumList = bookingOrderRoomNumList;
    }

    public String getDescr() {
        return descr;
    }

    public void setDescr(String descr) {
        this.descr = descr;
    }

    public Integer getBuildingId() {
        return buildingId;
    }

    public void setBuildingId(Integer buildingId) {
        this.buildingId = buildingId;
    }

    public Integer getFloorId() {
        return floorId;
    }

    public void setFloorId(Integer floorId) {
        this.floorId = floorId;
    }

    public Integer getIsMainRoom() {
        return isMainRoom;
    }

    public void setIsMainRoom(Integer isMainRoom) {
        this.isMainRoom = isMainRoom;
    }


    public Integer getRoomRateId() {
        return roomRateId;
    }

    public void setRoomRateId(Integer roomRateId) {
        this.roomRateId = roomRateId;
    }

    public String getRateCode() {
        return rateCode;
    }

    public void setRateCode(String rateCode) {
        this.rateCode = rateCode;
    }

    public String getResTitle() {
        return resTitle;
    }

    public void setResTitle(String resTitle) {
        this.resTitle = resTitle;
    }

    public Integer getOtaType() {
        return otaType;
    }

    public void setOtaType(Integer otaType) {
        this.otaType = otaType;
    }

    public String getOtaIcon() {
        return otaIcon;
    }

    public void setOtaIcon(String otaIcon) {
        this.otaIcon = otaIcon;
    }

    public String getOtaPayType() {
        return otaPayType;
    }

    public void setOtaPayType(String otaPayType) {
        this.otaPayType = otaPayType;
    }
}
