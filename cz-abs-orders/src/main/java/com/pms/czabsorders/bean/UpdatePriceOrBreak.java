package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.BaseRequest;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;

import java.util.List;

public class UpdatePriceOrBreak  extends BaseRequest {

    private Integer bookingOrderId;

    private Integer registId;

    // 1.房价 2.早餐
    private Integer type;


    private List<BookingOrderDailyPrice> priceList;

    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<BookingOrderDailyPrice> getPriceList() {
        return priceList;
    }

    public void setPriceList(List<BookingOrderDailyPrice> priceList) {
        this.priceList = priceList;
    }

    public Integer getRegistId() {
        return registId;
    }

    public void setRegistId(Integer registId) {
        this.registId = registId;
    }
}
