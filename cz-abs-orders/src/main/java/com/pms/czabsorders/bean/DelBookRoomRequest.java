package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.BaseRequest;
import com.pms.pmsorder.bean.search.BookingOrderRoomNumSearch;

import java.util.List;

public class DelBookRoomRequest extends BaseRequest {

    private Integer bookingOrderId;

    private List<BookingOrderRoomNumSearch> bookingOrderRoomNumSearches;

    public List<BookingOrderRoomNumSearch> getBookingOrderRoomNumSearches() {
        return bookingOrderRoomNumSearches;
    }

    public void setBookingOrderRoomNumSearches(List<BookingOrderRoomNumSearch> bookingOrderRoomNumSearches) {
        this.bookingOrderRoomNumSearches = bookingOrderRoomNumSearches;
    }

    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }
}
