package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.BaseRequest;
import com.pms.pmsorder.bean.BookingOrderRoomNum;

import java.util.List;

// 分配房间
public class PlanBookRoomRequest extends BaseRequest {

    private Integer bookingOrderId;

    private List<BookingOrderRoomNum> roomList;

    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }

    public List<BookingOrderRoomNum> getRoomList() {
        return roomList;
    }

    public void setRoomList(List<BookingOrderRoomNum> roomList) {
        this.roomList = roomList;
    }
}
