package com.pms.czabsorders.bean;

import com.pms.czaccount.bean.account.Account;
import com.pms.czpmsutils.request.BaseRequest;

import java.util.List;

public class TransferAccountForBookingParam extends BaseRequest {
    private Integer registId ;

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    private String reason;

    private String remark;


    public String getRegistPersonName() {
        return registPersonName;
    }

    public void setRegistPersonName(String registPersonName) {
        this.registPersonName = registPersonName;
    }

    public Integer getRegistPersonId() {
        return registPersonId;
    }

    public void setRegistPersonId(Integer registPersonId) {
        this.registPersonId = registPersonId;
    }

    private String registPersonName;
    private Integer registPersonId;

    public Integer getRegistId() {
        return registId;
    }

    public void setRegistId(Integer registId) {
        this.registId = registId;
    }

    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }

    public List<Account> getAccounts() {
        return accounts;
    }

    public void setAccounts(List<Account> accounts) {
        this.accounts = accounts;
    }

    private Integer bookingOrderId;
    private List<Account> accounts;
}
