package com.pms.czabsorders.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SearchRoomTypeMsgView {

    //房型ID
    private Integer roomTypeId;
    //房间类型名
    private String roomTypeName;
    //英文名称描述
    private String roomTypeNameEn;
    //简写
    private String shortName;
    //基础房价
    private Integer price;

    //描述
    private String des;
    //英文描述
    private String desEn;
    //状态 1可以 0停用
    private Integer state;

    // 面积
    private String sqm;

    private String bedType;

    // 房间数量
    private Integer roomCount ;

    private List<String> imgs;

}
