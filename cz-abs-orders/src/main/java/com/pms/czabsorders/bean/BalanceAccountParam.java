package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.BaseRequest;

public class BalanceAccountParam extends BaseRequest {
    private String accountId;
    private Integer money;
    private Integer registId;
    private  Integer bookingOrderId;


    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public Integer getMoney() {
        return money;
    }

    public void setMoney(Integer money) {
        this.money = money;
    }

    public Integer getRegistId() {
        return registId;
    }

    public void setRegistId(Integer registId) {
        this.registId = registId;
    }

    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }
}