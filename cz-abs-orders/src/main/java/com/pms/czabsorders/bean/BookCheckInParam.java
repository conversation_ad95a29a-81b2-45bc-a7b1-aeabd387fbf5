package com.pms.czabsorders.bean;

import java.util.List;

/**
 * 预订转入住接口 bookingCheckIn.do 传入的参数
 * 传输时 { bookParam: encodeURI(BookCheckInParam)}
 */
public class BookCheckInParam {

    // 预订单id
    private Integer bookingOrderId;

    // 入住房间集合
    private List<CheckInRoom> checkInRoomList;

    // 入住房间信息
    class CheckInRoom{

        private Integer bookingRoomId;

        private Integer roomId;

        private String roomNum;

        // 房价码
        private String rateCode;

        // 房价码id
        private Integer rateId;

        private List<Guest> guestList;

        public Integer getBookingRoomId() {
            return bookingRoomId;
        }

        public void setBookingRoomId(Integer bookingRoomId) {
            this.bookingRoomId = bookingRoomId;
        }

        public Integer getRoomId() {
            return roomId;
        }

        public void setRoomId(Integer roomId) {
            this.roomId = roomId;
        }

        public String getRoomNum() {
            return roomNum;
        }

        public void setRoomNum(String roomNum) {
            this.roomNum = roomNum;
        }

        public String getRateCode() {
            return rateCode;
        }

        public void setRateCode(String rateCode) {
            this.rateCode = rateCode;
        }

        public Integer getRateId() {
            return rateId;
        }

        public void setRateId(Integer rateId) {
            this.rateId = rateId;
        }

        public List<Guest> getGuestList() {
            return guestList;
        }

        public void setGuestList(List<Guest> guestList) {
            this.guestList = guestList;
        }
    }


    class Guest{

        private String name;

        private String sex;

        // 身份证类型 默认1
        private Integer idType ;

        private String address;

        private String idCode;

        private String phone;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getSex() {
            return sex;
        }

        public void setSex(String sex) {
            this.sex = sex;
        }

        public Integer getIdType() {
            return idType;
        }

        public void setIdType(Integer idType) {
            this.idType = idType;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getIdCode() {
            return idCode;
        }

        public void setIdCode(String idCode) {
            this.idCode = idCode;
        }

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }
    }


    public Integer getBookingOrderId() {
        return bookingOrderId;
    }

    public void setBookingOrderId(Integer bookingOrderId) {
        this.bookingOrderId = bookingOrderId;
    }

    public List<CheckInRoom> getCheckInRoomList() {
        return checkInRoomList;
    }

    public void setCheckInRoomList(List<CheckInRoom> checkInRoomList) {
        this.checkInRoomList = checkInRoomList;
    }
}
