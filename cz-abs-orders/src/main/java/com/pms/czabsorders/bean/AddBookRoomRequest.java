package com.pms.czabsorders.bean;

import com.pms.czpmsutils.request.BaseRequest;
import lombok.Data;

import java.util.List;

// 添加预订房间信息
@Data
public class AddBookRoomRequest extends BaseRequest {

    private Integer bookingOrderId;

    private String startTime;

    private String endTime;

    private List<RoomType> roomTypeList;


    @Data
    public static class RoomType {

        // 房型id
        private int roomTypeId;
        // 房型名称
        private String roomTypeName;
        // 房间id
        private int rateId;
        // 房价代码
        private String rateCode;
        // 房间数
        private int num;
        // 只填已选房的房间
        private List<Room> roomList;
        // 价格信息
        private List<Price> priceList;

    }


    @Data
    public static class Room {

        // 房间id
        private int roomInfoId;
        // 房间号
        private String roomNum;

        private String name;

        private String code;

    }

    @Data
    public static class Price {

        // 日期
        private String date;
        // 价格
        private int price;

        // 早餐
        private int breakfastNum;

    }


}
