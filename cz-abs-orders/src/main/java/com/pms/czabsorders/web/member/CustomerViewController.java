package com.pms.czabsorders.web.member;

import com.pms.czmembership.bean.requst.CustomerViewRequest;
import com.pms.czmembership.bean.search.CustomerViewSearch;
import com.pms.czmembership.service.CustomerService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;


@Controller
@RequestMapping("/hotel/member/")
@Slf4j
public class CustomerViewController {


    @Resource
    private CustomerService customerService;


    /**
     * 添加评论
     * @param request
     * @return
     */
    @RequestMapping("addOrUpdateCustomerView.do")
    @ResponseBody
    public ResponseData addOrUpdateCustomerView(@RequestBody CustomerViewRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            customerService.addOrUpdateCustomerView(request);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 添加评论
     * @param search
     * @return
     */
    @RequestMapping("listCustomerView.do")
    @ResponseBody
    public ResponseData listCustomerView(@RequestBody CustomerViewSearch search) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
           responseData.setData(customerService.listCustomerView(search));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}

