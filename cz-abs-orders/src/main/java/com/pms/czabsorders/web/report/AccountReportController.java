package com.pms.czabsorders.web.report;

import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.search.AccountSummarySearch;
import com.pms.czreport.bean.search.AccountThirdPayRecodeSearch;
import com.pms.czreport.service.impl.RoomRevReport;
import com.pms.czreport.service.impl.jjb.AccountJjbService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/hotel/report/")
public class AccountReportController {


    @Resource
    private AccountJjbService accountJjbService;


    @Resource
    private RoomRevReport roomRevReport;

    @RequestMapping("frontDeskEntrySimple.do")
    public ResponseData frontDeskEntrySimple(@RequestBody AccountSummarySearch accountSummarySearch) {
       return  accountJjbService.frontDeskEntrySimple(accountSummarySearch);
    }

    @RequestMapping("frontDeskEntrySummary.do")
    public ResponseData frontDeskEntrySummary(@RequestBody AccountSummarySearch accountSummarySearch) {
        return  accountJjbService.frontDeskEntrySummary(accountSummarySearch);
    }

    @RequestMapping("findRoomRev.do")
    public ResponseData findRoomRev(@RequestBody AccountSummarySearch accountSummarySearch) {
        return  roomRevReport.findRoomRev(accountSummarySearch);
    }
    @RequestMapping("searchAccountThirdPayRecode.do")
    public ResponseData searchAccountThirdPayRecode(@RequestBody AccountThirdPayRecodeSearch accountSummarySearch) {
        return  accountJjbService.searchAccountThirdPayRecode(accountSummarySearch);
    }


}
