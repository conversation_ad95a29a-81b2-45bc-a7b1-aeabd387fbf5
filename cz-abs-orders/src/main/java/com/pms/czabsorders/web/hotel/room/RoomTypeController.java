package com.pms.czabsorders.web.hotel.room;


import com.github.pagehelper.Page;
import com.pms.czhotelfoundation.bean.RoomStatePlan;
import com.pms.czhotelfoundation.bean.request.RoomStatePlanRequest;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeSearch;
import com.pms.czhotelfoundation.service.price.RoomDayPriceService;
import com.pms.czhotelfoundation.service.room.RoomTypeService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.UpdatePushDayPriceRequest;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/roomType/")
@Slf4j
public class RoomTypeController {

    @Autowired
    private RoomTypeService roomTypeService;

    @Autowired
    private RoomDayPriceService roomDayPriceService;


    @RequestMapping("findAllRoomType.do")
    @ResponseBody
    public ResponseData findAllRoomType(@RequestBody RoomTypeSearch roomTypeSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(roomTypeService.findAllRoomType(roomTypeSearch));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("findAllSmartType.do")
    @ResponseBody
    public ResponseData findAllSmartType(@RequestBody JSONObject param) {
        return roomTypeService.findAllSmartType(param);
    }

    @RequestMapping("addOrUpdateRoomType.do")
    @ResponseBody
    public ResponseData addOrUpdateRoomType(@RequestBody JSONObject param) {

        return roomTypeService.addOrUpdateRoomType(param);
    }


    @RequestMapping("findRoomPrice.do")
    @ResponseBody
    public ResponseData findRoomPrice(@RequestBody JSONObject param) {

        return roomDayPriceService.findRoomPrice(param);

    }

    @RequestMapping("updatePushDayPrice.do")
    @ResponseBody
    public ResponseData updatePushDayPrice(@RequestBody UpdatePushDayPriceRequest updatePushDayPriceRequest) {

        return roomDayPriceService.updatePushDayPrice(updatePushDayPriceRequest);
    }


    @RequestMapping("updatePriceByDate.do")
    @ResponseBody
    public ResponseData updatePriceByDate(@RequestBody JSONObject param) {

        return roomDayPriceService.updatePriceByDate(param);
    }

    @RequestMapping("updatePriceByDateSection.do")
    @ResponseBody
    public ResponseData updatePriceByDateSection(@RequestBody JSONObject param) {

        return roomDayPriceService.updatePriceByDateSection(param);

    }

    @RequestMapping("findRoomStatePlan.do")
    @ResponseBody
    public ResponseData findRoomStatePlan(@RequestBody JSONObject param) {
        return roomTypeService.findRoomStatePlan(param);
    }

    @RequestMapping("findRoomStatePlan2.do")
    @ResponseBody
    public ResponseData findRoomStatePlan2(@RequestBody com.pms.czhotelfoundation.bean.search.RoomStatePlanSearch search) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Page<RoomStatePlan> statePlan2 = roomTypeService.findRoomStatePlan2(search);
            responseData.setData(statePlan2);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("saveRoomStatePlan.do")
    @ResponseBody
    public ResponseData saveRoomStatePlan(@RequestBody RoomStatePlanRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            roomTypeService.saveRoomStatePlan(request);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("findRoomAuxiliary.do")
    @ResponseBody
    public ResponseData findRoomAuxiliary(@RequestBody JSONObject param) {
        return roomTypeService.findRoomAuxiliary(param);
    }

    @RequestMapping("findRoomTypeImg.do")
    @ResponseBody
    public ResponseData findRoomTypeImg(@RequestBody JSONObject param) {

        ResponseData roomTypeImg = roomTypeService.findRoomTypeImg(param);

        return roomTypeImg;
    }

    @RequestMapping("setRoomTypeImg.do")
    @ResponseBody
    public ResponseData setRoomTypeImg(@RequestBody JSONObject param) {
        return roomTypeService.setRoomTypeImg(param);
    }

    @RequestMapping("findRoomTypeNum.do")
    @ResponseBody
    public ResponseData findRoomTypeNum(@RequestBody JSONObject param) {
        return roomTypeService.findRoomTypeNum(param);
    }

}
