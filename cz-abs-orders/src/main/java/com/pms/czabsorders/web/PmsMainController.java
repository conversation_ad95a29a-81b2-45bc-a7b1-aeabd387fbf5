package com.pms.czabsorders.web;

import com.pms.czabsorders.service.checkin.CheckInService;
import com.pms.czpmsutils.ResponseData;
import com.pms.pmsorder.bean.request.GetHotelDataInfoParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/checkin/")
public class PmsMainController {

    @Autowired
    private CheckInService checkInService;

    @RequestMapping("getHotelDataInfo.do")
    @ResponseBody
    public ResponseData getHotelDataInfo(@RequestBody GetHotelDataInfoParam param) {
        ResponseData responseData = checkInService.getHotelDataInfo(param);
        return responseData;
    }

    @RequestMapping("getHotelRoomTypeRegister.do")
    @ResponseBody
    public ResponseData getHotelRoomTypeRegister(@RequestBody GetHotelDataInfoParam param) {
        ResponseData responseData = checkInService.getHotelRoomTypeRegister(param);
        return responseData;
    }


    @RequestMapping("getHotelRegisterNum.do")
    @ResponseBody
    public ResponseData getHotelRegisterNum(@RequestBody GetHotelDataInfoParam param) {
        ResponseData responseData = checkInService.getHotelRegisterNum(param);
        return responseData;
    }
}
