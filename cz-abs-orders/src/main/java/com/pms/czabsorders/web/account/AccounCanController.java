package com.pms.czabsorders.web.account;


import com.github.pagehelper.Page;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.search.SplitAccountReset;
import com.pms.czaccount.service.account.AccountCanService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.AccountCancelRequest;
import com.pms.czpmsutils.request.AccountSearchRequest;
import com.pms.czpmsutils.request.UpdateAccountRequest;
import com.pms.czpmsutils.request.search.AccountSummarySearch;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Controller
@RequestMapping("/hotel/account/")
@Slf4j
public class AccounCanController {

    @Autowired
    private AccountCanService accountCanService;

    @RequestMapping("findAccountByRegistId.do")
    @ResponseBody
    public ResponseData findAccountByRegistId(@RequestBody JSONObject param) {
        return accountCanService.findAccountByRegistId(param);
    }

    @RequestMapping("findPrintAccountByResist.do")
    @ResponseBody
    public ResponseData findPrintAccountByResist(@RequestBody JSONObject param) {
        ResponseData printAccountByResist = accountCanService.findPrintAccountByResist(param);
        return printAccountByResist;
    }

    @RequestMapping("findPrintAccountSummaryByResist.do")
    @ResponseBody
    public ResponseData findPrintAccountSummaryByResist(@RequestBody JSONObject param) {
        ResponseData printAccountByResist = accountCanService.findPrintAccountSummaryByResist(param);
        return printAccountByResist;
    }


    @RequestMapping("searchIndexAccount.do")
    @ResponseBody
    public ResponseData searchIndexAccount(@RequestBody JSONObject param) {
        return accountCanService.searchIndexAccount(param);
    }

    @RequestMapping("addAccount.do")
    @ResponseBody
    public Map<String, Object> addAccount(@RequestBody JSONObject param) {

        Map<String, Object> responseMap = accountCanService.addAccount(param);

        return responseMap;
    }

    @RequestMapping("setOff.do")
    @ResponseBody
    public Map<String, Object> setOff(@RequestBody JSONObject param) {
        return accountCanService.setOff(param);
    }

    @RequestMapping("transferAccount.do")
    @ResponseBody
    public Map<String, Object> transferAccount(@RequestBody JSONObject param) {
        return accountCanService.transferAccount(param);
    }

    @RequestMapping("settleAccount.do")
    @ResponseBody
    public Map<String, Object> settleAccount(@RequestBody JSONObject param) {
        return accountCanService.settleAccount(param);
    }

    @RequestMapping("getAccountCountByRegistId.do")
    @ResponseBody
    public ResponseData getAccountCountByRegistId(@RequestBody JSONObject param) {
        return accountCanService.getAccountCountByRegistId(param);
    }

    @RequestMapping("findAccountCancelByRegistId.do")
    @ResponseBody
    public ResponseData findAccountCancelByRegistId(@RequestBody AccountCancelRequest accountCancelRequest) {
        return accountCanService.findAccountCancelByRegistId(accountCancelRequest);
    }

    @RequestMapping("refundMoneyForOtherPms.do")
    @ResponseBody
    public Map<String, Object> refundMoneyForOtherPms(@RequestBody JSONObject param) {

        Map<String, Object> refundMoneyForOtherPms = accountCanService.refundMoneyForOtherPmsAndPush(param);

        return refundMoneyForOtherPms;
    }

    @RequestMapping("updateAccount.do")
    @ResponseBody
    public ResponseData updateAccountParam(@RequestBody UpdateAccountRequest updateAccountRequest) {
        return accountCanService.updateAccountParam(updateAccountRequest);
    }

    @RequestMapping("searchAccountList.do")
    @ResponseBody
    public ResponseData searchAccountList(@RequestBody AccountSearchRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Page<Account> accounts = accountCanService.searchAccountList(request);
            responseData.setData(accounts);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("selectNotCollection.do")
    @ResponseBody
    public ResponseData selectNotCollection(@RequestBody AccountSearchRequest request) {
        return accountCanService.selectNotCollection(request);
    }

    @RequestMapping("accountSummaryFunc.do")
    @ResponseBody
    public ResponseData accountSummary(@RequestBody AccountSummarySearch request) {
        return accountCanService.accountSummary(request);
    }

    @RequestMapping("accountResetFunc.do")
    @ResponseBody
    public ResponseData accountResetFunc(@RequestBody JSONObject param) {
        return accountCanService.accountResetFunc(param);
    }

    @RequestMapping("splitAccount.do")
    @ResponseBody
    public ResponseData splitAccount(@RequestBody SplitAccountReset splitAccountReset) {
        return accountCanService.splitAccount(splitAccountReset);
    }

}
