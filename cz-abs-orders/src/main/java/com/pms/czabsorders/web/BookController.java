package com.pms.czabsorders.web;


import com.github.pagehelper.Page;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.BookingRequest;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.request.BookingOrderPageRequest;
import com.pms.pmsorder.bean.search.BookingOrderDailyPriceSearch;
import com.pms.pmsorder.bean.search.BookingOrderRoomNumSearch;
import com.pms.pmsorder.bean.search.BookingOrderRoomTypeSearch;
import com.pms.pmsorder.service.BookingOrderService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 订单相关业务
 */
@Controller
@RequestMapping("/hotel/book/")
@Slf4j
public class BookController {

    @Autowired
    private BookingOrderService bookingOrderService;


    /**
     * 查询所有预订单
     *
     * @param request
     * @return
     */
    @RequestMapping("searchBooking.do")
    @ResponseBody
    public ResponseData searchBooking(@RequestBody JSONObject request) {
        return bookingOrderService.searchBooking(request);
    }


    /**
     * 查询所有预订单2
     *
     * @param request
     * @return
     */
    @RequestMapping("searchBooking2.do")
    @ResponseBody
    public ResponseData searchBooking2(@RequestBody BookingOrderPageRequest request) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            Page<BookingOrder> goodsInfoByPage = bookingOrderService.searchBookingPage(request);
            responseData.setData(goodsInfoByPage);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }


    /**
     * 根据BookingOrderId 查询预订单
     *
     * @param request
     * @return
     */
    @RequestMapping("searchBookingById.do")
    @ResponseBody
    public ResponseData searchBookingById(@RequestBody BookingOrderPageRequest request) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            BookingOrder bookingOrder = bookingOrderService.searchBookingById(request);
            responseData.setData(bookingOrder);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }

    /**
     * 查询所有预订房型
     *
     * @param bookingOrderRoomTypeSearch
     * @return
     */
    @RequestMapping("serachBookingRoomType.do")
    @ResponseBody
    public ResponseData serachBookingRoomType(@RequestBody BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch) {
        return bookingOrderService.serachBookingRoomType(bookingOrderRoomTypeSearch);
    }

    /**
     * 查询所有预房间
     *
     * @param bookingOrderRoomNumSearch
     * @return
     */
    @RequestMapping("serachBookingRoom.do")
    @ResponseBody
    public ResponseData serachBookingRoom(@RequestBody BookingOrderRoomNumSearch bookingOrderRoomNumSearch) {
        return bookingOrderService.serachBookingRoom(bookingOrderRoomNumSearch);
    }

    /**
     * 查询所有预房间2
     *
     * @param bookingOrderRoomNumSearch
     * @return
     */
    @RequestMapping("searchBookRoomView.do")
    @ResponseBody
    public ResponseData searchBookRoomView(@RequestBody BookingOrderRoomNumSearch bookingOrderRoomNumSearch) {
        return bookingOrderService.searchBookRoomView(bookingOrderRoomNumSearch);
    }


    /**
     * 查询所有预房间 按照日期分组
     *
     * @param bookingOrderRoomNumSearch
     * @return
     */
    @RequestMapping("serachBookingRoomForDay.do")
    @ResponseBody
    public ResponseData serachBookingRoomForDay(@RequestBody BookingOrderRoomNumSearch bookingOrderRoomNumSearch) {
        return bookingOrderService.serachBookingRoomForDay(bookingOrderRoomNumSearch);
    }


    /**
     * 订单修改
     *
     * @param param
     * @return
     */
    @RequestMapping("updateBooking.do")
    @ResponseBody
    public ResponseData updateBooking(@RequestBody JSONObject param) {
        return bookingOrderService.updateBooking(param);
    }

    /**
     * 查询登记单价格
     * @param bookingOrderDailyPriceSearch
     * @return
     */
    @RequestMapping("searchRegistDayPrice.do")
    @ResponseBody
    public ResponseData searchRegistDayPrice(@RequestBody BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch) {
        return bookingOrderService.searchRegistDayPrice(bookingOrderDailyPriceSearch);
    }

    /**
     * 修改登记单价格
     * @param param
     * @return
     */
    @RequestMapping("updateDayPrice.do")
    @ResponseBody
    public ResponseData updateDayPrice(@RequestBody JSONObject param) {
        return bookingOrderService.updateDayPrice(param);
    }

    /**
     * 查询预订登记人员
     * @param bookingRequest
     * @return
     */
    @RequestMapping("searchPersonForBookingRoom.do")
    @ResponseBody
    public ResponseData searchPersonForBookingRoom(@RequestBody BookingRequest bookingRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(bookingOrderService.searchPersonForBookingRoom(bookingRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 删除预订登记人员
     * @param request
     * @return
     */
    @RequestMapping("delPersonForBookingRoom.do")
    @ResponseBody
    public ResponseData delPersonForBookingRoom(@RequestBody JSONObject request) {
        return bookingOrderService.delPersonForBookingRoom(request);
    }

    /**
     * 查询预订配置信息
     * @param request
     * @return
     */
    @RequestMapping("searchBookingOrderConfig.do")
    @ResponseBody
    public ResponseData searchBookingOrderConfig(@RequestBody JSONObject request) {
        return bookingOrderService.searchBookingOrderConfig(request);
    }

    /**
     * 修改预订配置信息
     * @param request
     * @return
     */
    @RequestMapping("updateBookingOrderConfig.do")
    @ResponseBody
    public ResponseData updateBookingOrderConfig(@RequestBody JSONObject request) {
        return bookingOrderService.updateBookingOrderConfig(request);
    }


}
