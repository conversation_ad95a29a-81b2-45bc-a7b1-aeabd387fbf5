package com.pms.czabsorders.web.mini;

import com.pms.czabsorders.bean.mini.FindHotelMsg;
import com.pms.czabsorders.bean.mini.ShoppingOrderDelivery;
import com.pms.czabsorders.service.mini.WxHotelService;
import com.pms.czaccount.bean.pay.search.WechatMiniprogramsSearch;
import com.pms.czhotelfoundation.bean.hotel.search.HotelBaseInfoSearch;
import com.pms.czmembership.bean.member.search.CardGroupUrlSearch;
import com.pms.czpmsutils.ResponseData;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/minipro/")
public class HotelMiniproController {

    @Autowired
    private WxHotelService wxHotelService;

    /**
     * 获取openid
     * @param param
     * @return
     */
    @RequestMapping("createHotelWxQrCode.do")
    @ResponseBody
    public ResponseData createHotelWxQrCode(@RequestBody WechatMiniprogramsSearch param){
        return wxHotelService.createHotelWxQrCode(param);
    }


    /**
     * 获取openid
     * @param param
     * @return
     */
    @RequestMapping("searchVipUrl.do")
    @ResponseBody
    public ResponseData searchVipUrl(@RequestBody CardGroupUrlSearch param){
        return wxHotelService.searchVipUrl(param);
    }


    /**
     * 获取openid
     * @param param
     * @return
     */
    @RequestMapping("createHotelWxQrCodeOther.do")
    @ResponseBody
    public ResponseData createHotelWxQrCodeOther(@RequestBody WechatMiniprogramsSearch param){
        return wxHotelService.createHotelWxQrCodeOther(param);
    }

    /**
     * 获取openid
     * @param param
     * @return
     */
    @RequestMapping("findHotelWxMinipro.do")
    @ResponseBody
    public ResponseData findHotelWxMinipro(@RequestBody JSONObject param){
        return wxHotelService.findHotelWxMinipro(param);
    }

    /**
     * 获取openid
     * @param param
     * @return
     */
    @RequestMapping("allWxHotel.do")
    @ResponseBody
    public ResponseData allWxHotel(@RequestBody HotelBaseInfoSearch param){
        return wxHotelService.allWxHotel(param);
    }

    /**
     * 获取openid
     * @param param
     * @return
     */
    @RequestMapping("findHotelJson.do")
    @ResponseBody
    public ResponseData findHotelJson(@RequestBody FindHotelMsg param){
        return wxHotelService.findHotelJson(param);
    }

    /**
     * 获取openid
     * @param param
     * @return
     */
    @RequestMapping("findHotelJson")
    @ResponseBody
    public ResponseData findHotelJsonOther(@RequestBody FindHotelMsg param){
        return wxHotelService.findHotelJsonOther(param);
    }


    /**
     * 获取openid
     * @param param
     * @return
     */
    @RequestMapping("shoppingOrderDeliveryFunc.do")
    @ResponseBody
    public ResponseData ShoppingOrderDeliveryFunc(@RequestBody ShoppingOrderDelivery param){
        return wxHotelService.ShoppingOrderDeliveryFunc(param);
    }

    /**
     * 获取openid
     * @param param
     * @return
     */
    @RequestMapping("hotelMap.do")
    @ResponseBody
    public ResponseData hotelMap(@RequestBody WechatMiniprogramsSearch param){
        return wxHotelService.hotelMap(param);
    }
}
