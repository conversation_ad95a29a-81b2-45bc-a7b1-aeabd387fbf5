package com.pms.czabsorders.web;


import com.pms.czpmsutils.ResponseData;
import com.pms.pmsorder.bean.request.MachineErrorRequest;
import com.pms.pmsorder.bean.search.MachineCardErrorSearch;
import com.pms.pmsorder.bean.search.MachinePoliceErrorSearch;
import com.pms.pmsorder.service.RegistErrService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/regist/")
public class RegistErrController {

    @Autowired
    private RegistErrService registErrService;


    @RequestMapping("addMachineCardError.do")
    @ResponseBody
    public ResponseData addMachineCardError(@RequestBody MachineErrorRequest machineErrorRequest) {
        return registErrService.addMachineCardError(machineErrorRequest);
    }

    @RequestMapping("addMachinePoliceError.do")
    @ResponseBody
    public ResponseData addMachinePoliceError(@RequestBody MachineErrorRequest machineErrorRequest) {
        return registErrService.addMachinePoliceError(machineErrorRequest);
    }

    @RequestMapping("searchMachinePoliceError.do")
    @ResponseBody
    public ResponseData searchMachinePoliceError(@RequestBody MachinePoliceErrorSearch machineErrorRequest) {
        return registErrService.searchMachinePoliceError(machineErrorRequest);
    }

    @RequestMapping("searchMachineCardError.do")
    @ResponseBody
    public ResponseData searchMachineCardError(@RequestBody MachineCardErrorSearch machineErrorRequest) {
        return registErrService.searchMachineCardError(machineErrorRequest);
    }
}
