package com.pms.czabsorders.web;

import com.pms.czabsorders.bean.*;
import com.pms.czabsorders.service.order.OrderService;
import com.pms.czabsorders.service.roomstateimg.RoomStateImgService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.*;
import com.pms.pmsorder.bean.request.RegistPageRequest;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Controller
@RequestMapping("/hotel/absorder/")
public class OrderController {

    @Autowired
    private OrderService bookingOrderService;

    @Autowired
    private RoomStateImgService roomStateImgService;

    /**
     * 查询房态图
     *
     * @param request
     * @return
     */
    @RequestMapping("searchRoomStateBag.do")
    @ResponseBody
    public ResponseData searchRoomStateBag(@RequestBody JSONObject param) {
        return roomStateImgService.searchRoomStateBag(param);
    }

    /**
     * 查询房态图
     *
     * @param request
     * @return
     */
    @RequestMapping("roomDayData.do")
    @ResponseBody
    public ResponseData searchRoomStateBag(@RequestBody RegistPageRequest param) {
        return roomStateImgService.roomDayData(param);
    }


    /**
     * 查询房态图
     *
     * @param param
     * @return
     */
    @RequestMapping("searchRegistAndBook.do")
    @ResponseBody
    public ResponseData searchRegistAndBook(@RequestBody JSONObject param) {
        return bookingOrderService.searchRegistAndBook(param);
    }

    /**
     * 查询房态图
     *
     * @param request
     * @return
     */
    @RequestMapping("searchRoomStateBagTwo.do")
    @ResponseBody
    public ResponseData searchRoomStateBagTwo(@RequestBody JSONObject param) {
        return roomStateImgService.searchRoomStateBagTwo(param);
    }

    /**
     * 新增预订单
     *
     * @param param
     * @return
     */
    @RequestMapping("addBook.do")
    @ResponseBody
    public JSONObject addBook(@RequestBody JSONObject param) {
        return bookingOrderService.addBookNew(param);
    }


    /**
     * 新增钟点房订单
     *
     * @param createBookingOrderRequest
     * @return
     */
    @RequestMapping("createBookingOrder.do")
    @ResponseBody
    public ResponseData createBookingOrder(@RequestBody CreateBookingOrderRequest createBookingOrderRequest) {
        return bookingOrderService.createBookingOrder(createBookingOrderRequest);
    }


    /**
     * 修改预定房型
     *
     * @param bookUpdateRoomType
     * @return
     */
    @RequestMapping("updateBookRoomType.do")
    @ResponseBody
    public ResponseData updateBookRoomType(@RequestBody BookUpdateRoomTypeTwo bookUpdateRoomType) {
        return bookingOrderService.updateBookRoomType(bookUpdateRoomType);
    }

    /**
     * 修改预定房型
     *
     * @param param
     * @return
     */
    @RequestMapping("addBookOrderRoom.do")
    @ResponseBody
    public ResponseData addBookOrderRoom(@RequestBody JSONObject param) {
        return bookingOrderService.addBookOrderRoomNew(param);
    }


    /**
     * 预订排房针对酒店自助机项目
     *
     * @param param
     * @return
     */
    @RequestMapping("addBookOrderRoomForMachine.do")
    @ResponseBody
    public JSONObject addBookOrderRoomForMachine(@RequestBody JSONObject param) {
        JSONObject resultMap = new JSONObject();
        resultMap.put(ER.RES, ER.SUCC);
        ResponseData responseData = bookingOrderService.addBookOrderRoomAll(param);
        if (responseData.getCode() != 1) {
            resultMap.put(ER.RES, ER.SUCC);
        }
        return resultMap;
    }

    /**
     * 取消预订
     *
     * @param param
     * @return
     */
    @RequestMapping("cancelBook.do")
    @ResponseBody
    public ResponseData cancelBook(@RequestBody JSONObject param) {
        return bookingOrderService.cancelBook(param);
    }


    /**
     * 订单修改
     *
     * @param request
     * @return
     */
    @RequestMapping("updateBooking.do")
    @ResponseBody
    public JSONObject updateBooking(@RequestBody JSONObject param) {
        return bookingOrderService.updateBooking(param);
    }

    /**
     * 恢复订单
     *
     * @param request
     * @return
     */
    @RequestMapping("orderRecovery.do")
    @ResponseBody
    public ResponseData orderRecovery(@RequestBody JSONObject param) {
        return bookingOrderService.orderRecovery(param);
    }


    @RequestMapping("updateDayPrice.do")
    @ResponseBody
    public JSONObject updateDayPrice(@RequestBody JSONObject param) {
        return bookingOrderService.updateDayPrice(param);
    }

    @RequestMapping("cancelBookingRoomNo.do")
    @ResponseBody
    public ResponseData cancelBookingRoomNo(@RequestBody JSONObject param) {
        return bookingOrderService.cancelBookingRoomNo(param);
    }


    @RequestMapping("addPersonForBookingRoom.do")
    @ResponseBody
    public JSONObject addPersonForBookingRoom(@RequestBody JSONObject param) {
        return bookingOrderService.addPersonForBookingRoom(param);
    }


    @RequestMapping("updateBookingOrderConfig.do")
    @ResponseBody
    public Map<String, Object> updateBookingOrderConfig(@RequestBody JSONObject param) {
        return bookingOrderService.updateBookingOrderConfig(param);
    }

    @RequestMapping("updateBookingOrderInfo.do")
    @ResponseBody
    public ResponseData updateBookingOrderInfo(@RequestBody UpdateBookingOrderInfoRequest request) {
        return bookingOrderService.updateBookingOrderInfo(request);
    }

    @RequestMapping("getRegistPeopleName")
    @ResponseBody
    public String getRegistPeopleName(@RequestBody Integer registId) {
        return bookingOrderService.getRegistPeopleName(registId);
    }

    @RequestMapping("getTodaySituation")
    @ResponseBody
    public ResponseData getTodaySituation(@RequestBody BaseRequest baseRequest) {
        return bookingOrderService.getTodaySituation(baseRequest);
    }


    @RequestMapping("dayRevenue.do")
    @ResponseBody
    public ResponseData dayRevenue(@RequestBody JSONObject param) {
        return bookingOrderService.dayRevenue(param);
    }

    @RequestMapping("hotelLiveInfo.do")
    @ResponseBody
    public ResponseData hotelLiveInfo(@RequestBody JSONObject param) {
        return bookingOrderService.hotelLiveInfo(param);
    }


    @RequestMapping("addBookRoomFunc.do")
    @ResponseBody
    public ResponseData addBookRoomFunc(@RequestBody AddBookRoomRequest param) {
        return bookingOrderService.addBookRoomFunc(param);
    }

    @RequestMapping("upaBookRoomFunc.do")
    @ResponseBody
    public ResponseData upaBookRoomFunc(@RequestBody UpaBookRoomRequest param) {
        return bookingOrderService.upaBookRoomFunc(param);
    }

    @RequestMapping("delBookRoomFunc.do")
    @ResponseBody
    public ResponseData delBookRoomFunc(@RequestBody DelBookRoomRequest param) {
        return bookingOrderService.delBookRoomFunc(param);
    }

    @RequestMapping("planRoomFunc.do")
    @ResponseBody
    public ResponseData planRoomFunc(@RequestBody PlanBookRoomRequest param) {
        return bookingOrderService.planRoomFunc(param);
    }

    @RequestMapping("updatePriceOrBreakFunc.do")
    @ResponseBody
    public ResponseData updatePriceOrBreakFunc(@RequestBody UpdatePriceOrBreak param) {
        return bookingOrderService.updatePriceOrBreakFunc(param);
    }

    @RequestMapping("copyBookPrice.do")
    @ResponseBody
    public ResponseData copyBookPrice(@RequestBody UpdatePriceOrBreak param) {
        return bookingOrderService.copyPrice();
    }

    @RequestMapping("getHotelTodayComingBookingList.do")
    @ResponseBody
    public ResponseData getHotelTodayComingBookingList(@RequestBody TodayComingRequest todayComingRequest) {
        return bookingOrderService.getHotelTodayComingBookingList(todayComingRequest);
    }

    @RequestMapping("getOrderListPriceListAndAccountList.do")
    @ResponseBody
    public ResponseData getHotelTodayComingBookingList(@RequestBody GetOrderListPriceListAndAccountListRequest getOrderListPriceListAndAccountListRequest) {
        return bookingOrderService.getOrderListPriceListAndAccountList(getOrderListPriceListAndAccountListRequest);
    }


}
