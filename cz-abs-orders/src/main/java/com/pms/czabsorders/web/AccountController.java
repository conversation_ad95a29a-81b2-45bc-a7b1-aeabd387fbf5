package com.pms.czabsorders.web;


import com.pms.czabsorders.bean.*;
import com.pms.czabsorders.service.account.AccountService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.AddAccountForArRequest;
import com.pms.czpmsutils.request.AddAccountParam;
import com.pms.czpmsutils.request.CheckoutPayStatisticsRequest;
import com.pms.czpmsutils.request.FinishMemberFreezeRequest;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;

/**
 * 账务操作相关业务接口
 */
@Controller
@RequestMapping("/hotel/absaccount")
@Slf4j
public class AccountController {
    @Autowired
    private AccountService accountService;

    /**
     * 添加账务
     * @param addAccountParam
     * @return
     */
    @RequestMapping("addAccount.do")
    @ResponseBody
    public ResponseData addAccount(@RequestBody AddAccountParam addAccountParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData = accountService.addAccount(addAccountParam);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 添加AR账
     * @param addAccountForArRequest
     * @return
     */
    @RequestMapping("addAccountForAr.do")
    @ResponseBody
    public ResponseData addAccountForAr(@RequestBody AddAccountForArRequest addAccountForArRequest) {
        return accountService.addAccountForAr(addAccountForArRequest);
    }

    /**
     * 部分结账
     * @param settleAccountParam
     * @return
     */
    @RequestMapping("settleAccount.do")
    @ResponseBody
    public ResponseData settleAccount(@RequestBody SettleAccountParam settleAccountParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData = accountService.settleAccount(settleAccountParam);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 撤销结账
     * @param cancelAccountParam
     * @return
     */
    @RequestMapping("cancelAccount.do")
    @ResponseBody
    public ResponseData cancelAccount(@RequestBody CancelAccountParam cancelAccountParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData = accountService.cancelAccount(cancelAccountParam);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 分账
     * @param balanceAccountParam
     * @return
     */
    @RequestMapping("balanceAccount.do")
    @ResponseBody
    public ResponseData balanceAccount(@RequestBody BalanceAccountParam balanceAccountParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData = accountService.balanceAccount(balanceAccountParam);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 转账
     * @param param
     * @return
     */
    @RequestMapping("transferAccount.do")
    @ResponseBody
    public ResponseData transferAccountNew(@RequestBody TransferAccontRequest param) {
        return accountService.transferAccountNew(param);
    }

    /**
     * 预订单转账
     * @param param
     * @return
     */
    @RequestMapping("transferAccountForBooking.do")
    @ResponseBody
    public ResponseData transferAccountForBooking(@RequestBody TransferAccountForBookingParam param) {
        return accountService.transferAccountForBooking(param);
    }


    /**
     * 冲账
     * @param settleAccountParam
     * @return
     */
    @RequestMapping("setOff.do")
    @ResponseBody
    public ResponseData setOff(@RequestBody JSONObject settleAccountParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            SetOffAccountParam setOffAccountParam = new SetOffAccountParam();
            setOffAccountParam.setSessionToken(settleAccountParam.getString(ER.SESSION_TOKEN));
            List<Account> accountList = new ArrayList<>();
            JSONArray accounts = settleAccountParam.getJSONArray("accounts");
            for (int i = 0; i < accounts.size(); i++) {
                Account account = (Account) JSONObject.toBean(accounts.getJSONObject(i), Account.class);

                accountList.add(account);
            }
            setOffAccountParam.setAccountList(accountList);
            responseData = accountService.setOff(setOffAccountParam);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 查询班次账务
     * @param param
     * @return
     */
    @RequestMapping("searchClassAccount.do")
    @ResponseBody
    public ResponseData searchClassAccount(@RequestBody JSONObject param) {
        return accountService.searchClassAccount(param);
    }

    /**
     * 查询结账统计
     * @param param
     * @return
     */
    @RequestMapping("searchcheckoutPayStatistics.do")
    @ResponseBody
    public ResponseData searchcheckoutPayStatistics(@RequestBody CheckoutPayStatisticsRequest param) {
        return accountService.searchcheckoutPayStatistics(param);
    }


    /**
     * 查询结账明细
     * @param param
     * @return
     */
    @RequestMapping("searchCheckoutPayDetail.do")
    @ResponseBody
    public ResponseData searchCheckoutPayDetail(@RequestBody CheckoutPayStatisticsRequest param) {
        return accountService.searchCheckoutPayDetail(param);
    }


    /**
     * 退款
     * @param param
     * @return
     */
    @RequestMapping("refundMoney.do")
    @ResponseBody
    public ResponseData refundMoney(@RequestBody JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData = accountService.refundMoney(param);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 第三方账务撤销
     * @param param
     * @return
     */
    @RequestMapping("thirdAccoutCancel.do")
    @ResponseBody
    public ResponseData thirdAccoutCancel(@RequestBody JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData = accountService.thirdAccoutCancel(param);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 查询个人账务
     * @param param
     * @return
     */
    @RequestMapping("searchPersonAccount.do")
    @ResponseBody
    public ResponseData searchPersonAccount(@RequestBody RegistParam param) {
        return accountService.searchPersonAccoun(param);
    }

    /**
     * 结束冻结
     * @param finishMemberFreezeRequest
     * @return
     */
    @RequestMapping("finishMemberFreeze.do")
    @ResponseBody
    public ResponseData finishMemberFreeze(@RequestBody FinishMemberFreezeRequest finishMemberFreezeRequest) {
        return accountService.finishMemberFreeze(finishMemberFreezeRequest);
    }

}
