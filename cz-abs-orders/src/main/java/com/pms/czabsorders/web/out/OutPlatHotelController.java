package com.pms.czabsorders.web.out;

import com.pms.czabsorders.bean.out.OutHotelGroupInfo;
import com.pms.czabsorders.service.out.OutPlatHotelService;
import com.pms.czpmsutils.ResponseData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@RestController
public class OutPlatHotelController {

    @Autowired
    private OutPlatHotelService outPlatHotelService;

    /**
     * 外部平台创建集团信息同步
     * */
    @RequestMapping("/hotel/baseinfo/out/groupInfoAdd")
    public ResponseData iotHotelGroupAdd (@RequestBody OutHotelGroupInfo request) throws Exception {
        ResponseData responseData = outPlatHotelService.outCreateHotelGroup(request);
        return responseData;
    }

    /**
     * 外部平台修改集团信息同步
     * */
    @RequestMapping("/hotel/baseinfo/out/groupInfoUpdate")
    public ResponseData iotHotelGroupUpdate(@RequestBody OutHotelGroupInfo request) throws Exception {

        ResponseData responseData = outPlatHotelService.outUpdateGroup(request);
        return responseData;
    }
}
