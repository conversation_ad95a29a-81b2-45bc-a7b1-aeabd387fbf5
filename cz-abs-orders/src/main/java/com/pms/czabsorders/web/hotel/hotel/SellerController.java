package com.pms.czabsorders.web.hotel.hotel;

import com.pms.czhotelfoundation.service.SellerService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.SellerRequest;
import com.pms.czpmsutils.request.SellerSearch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 销售员相关业务
 */
@Controller
@RequestMapping("/hotel/baseinfo/")
@Slf4j
public class SellerController {

    @Autowired
    private SellerService sellerService;

    /**
     * 添加或者删除销售人员
     *
     * @param sellerRequest
     * @return
     */
    @RequestMapping("saveOrUpdateSeller.do")
    @ResponseBody
    public ResponseData saveOrUpdateSeller(@RequestBody SellerRequest sellerRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(sellerService.saveOrUpdateSeller(sellerRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 删除销售人员
     *
     * @param sellerRequest
     * @return
     */
    @RequestMapping("deleteSeller.do")
    @ResponseBody
    public ResponseData deleteSeller(@RequestBody SellerRequest sellerRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            sellerService.deleteSeller(sellerRequest);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 分页查询销售人员
     *
     * @param sellerSearch
     * @return
     */
    @RequestMapping("searchByPage.do")
    @ResponseBody
    public ResponseData searchByPage(@RequestBody SellerSearch sellerSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(sellerService.searchByPage(sellerSearch));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


}
