package com.pms.czabsorders.web.hotel.hotel;


import com.pms.czhotelfoundation.bean.otarep.RepHotel;
import com.pms.czhotelfoundation.bean.otarep.RepRoomType;
import com.pms.czhotelfoundation.bean.otarep.search.RepHotelSearch;
import com.pms.czhotelfoundation.bean.otarep.search.RepRoomTypeSearch;
import com.pms.czhotelfoundation.service.otarep.OtaRepService;
import com.pms.czpmsutils.ResponseData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 爬虫酒店设置
 */
@Controller
@RequestMapping("/hotel/setting/")
public class OtaRepController {

    @Autowired
    private OtaRepService otaRepService;


    @RequestMapping("ollOtaHotel.do")
    @ResponseBody
    public ResponseData ollOtaHotel(@RequestBody RepHotelSearch repHotelSearch){

        return otaRepService.ollOtaHotel(repHotelSearch);

    }

    @RequestMapping("addOrUpaOtaHotel.do")
    @ResponseBody
    public ResponseData addOrUpaOtaHotel(@RequestBody RepHotel repHotel){

        return otaRepService.addOrUpaOtaHotel(repHotel);

    }

    @RequestMapping("allOtaRoomTye.do")
    @ResponseBody
    public ResponseData allOtaRoomTye(@RequestBody RepRoomTypeSearch repRoomTypeSearch){

        return otaRepService.allOtaRoomTye(repRoomTypeSearch);

    }

    @RequestMapping("addOrUpaRoomType.do")
    @ResponseBody
    public ResponseData findAllHotel(@RequestBody RepRoomType repRoomType){

        return otaRepService.addOrUpaRoomType(repRoomType);

    }
}
