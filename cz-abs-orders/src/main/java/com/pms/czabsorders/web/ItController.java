package com.pms.czabsorders.web;

import com.pms.czabsorders.bean.*;
import com.pms.czabsorders.service.order.ItHotelService;
import com.pms.czabsorders.service.order.ItOrderService;
import com.pms.czpmsutils.ResponseData;
import com.pms.pmsorder.bean.request.BookingOrderPageRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 *
 * **/
@Controller
@RequestMapping("/hotel/")
public class ItController {

    @Autowired
    private ItHotelService itHotelService;

    @Autowired
    private ItOrderService itOrderService;

    @RequestMapping("/rate/getPriceCodeByHid")
    @ResponseBody
    public ResponseData getPriceCodeByHid(@RequestBody GetPriceCodeByHidDTO getPriceCodeByHidDTO){
        return itHotelService.getPriceCodeByHid(getPriceCodeByHidDTO);
    }

    @RequestMapping("/rate/getPriceCodeByRoomType")
    @ResponseBody
    public ResponseData getPriceCodeByRoomType(@RequestBody GetPriceCodeByRoomTypeDTO getPriceCodeByRoomTypeDTO){
        return itHotelService.getPriceCodeByRoomType(getPriceCodeByRoomTypeDTO);
    }

    @RequestMapping("/turanAlways/roomTypeAvailableRoom")
    @ResponseBody
    public ResponseData roomTypeAvailableRoom(@RequestBody RoomTypeAvailableRoomDTO roomTypeAvailableRoomDTO){
        return itHotelService.roomTypeAvailableRoom(roomTypeAvailableRoomDTO);
    }

    @RequestMapping("/rate/findRoomDayPrice")
    @ResponseBody
    public ResponseData findRoomDayPrice(@RequestBody FindRoomDayPriceDTO findRoomDayPriceDTO){
        return itHotelService.findRoomDayPrice(findRoomDayPriceDTO);
    }

    @RequestMapping("/absorder/addBookForOtherPlatPlatform")
    @ResponseBody
    public ResponseData addBookForOtherPlatPlatform(@RequestBody AddBookDTO addBookDTO){
        return itOrderService.addBookForOtherPlatPlatform(addBookDTO);
    }

    @RequestMapping("/absorder/searchBooking")
    @ResponseBody
    public ResponseData searchBooking(@RequestBody BookingOrderPageRequest bookingOrderPageRequest){
        return itOrderService.searchBooking(bookingOrderPageRequest);
    }

    @RequestMapping("/absorder/cancelBook")
    @ResponseBody
    public ResponseData cancelBook(@RequestBody CancelBookForOtherPlatPlatformDTO cancelBookForOtherPlatPlatformDTO){
        return itOrderService.cancelBookForOtherPlatPlatform(cancelBookForOtherPlatPlatformDTO);
    }


    @RequestMapping("/room/searchRoomTypeMsg")
    @ResponseBody
    public ResponseData searchRoomTypeMsg(@RequestBody SearchRoomTypeMsgDTO searchRoomTypeMsgDTO){
        return itHotelService.searchRoomTypeMsg(searchRoomTypeMsgDTO);
    }

    @RequestMapping("/room/searchRoomType")
    @ResponseBody
    public ResponseData searchRoomType(@RequestBody SearchRoomTypeMsgDTO searchRoomTypeMsgDTO){
        return itHotelService.searchRoomTypeMsg(searchRoomTypeMsgDTO);
    }

    @RequestMapping("/book/searchBooking")
    @ResponseBody
    public ResponseData searchBooking(@RequestBody BookMsgForOtherPlatformDTO bookMsgForOtherPlatformDTO){
        return itOrderService.searchBookMsgForOtherPlatform(bookMsgForOtherPlatformDTO);
    }

    @RequestMapping("/room/findHotelRoomInfo")
    @ResponseBody
    public ResponseData findHotelRoomInfo(@RequestBody FindHotelRoomInfoDTO findHotelRoomInfoDTO){
        return itHotelService.findHotelRoomInfo(findHotelRoomInfoDTO);
    }

    @RequestMapping("/setting/findBulidFloor")
    @ResponseBody
    public ResponseData findBulidFloor(@RequestBody FindBulidFloorDTO findHotelRoomInfoDTO){
        return itHotelService.findBulidFloor(findHotelRoomInfoDTO);
    }

    @RequestMapping("/room/findHotelRoomNum")
    @ResponseBody
    public ResponseData findBulidFloor(@RequestBody FindHotelRoomNumDTO findHotelRoomInfoDTO){
        return itHotelService.findHotelRoomNum(findHotelRoomInfoDTO);
    }

    @RequestMapping("/room/dayRoomList")
    @ResponseBody
    public ResponseData dayRoomList(@RequestBody FindHotelRoomNumDTO findHotelRoomInfoDTO){
        return itHotelService.dayRoomList(findHotelRoomInfoDTO);
    }

    @RequestMapping("/base/hotelInfo")
    @ResponseBody
    public ResponseData findHotelMsg(@RequestBody FindHotelDTO findHotelRoomInfoDTO){
        return itHotelService.findHotelMsg(findHotelRoomInfoDTO);
    }





}
