package com.pms.czabsorders.web.hotel;


import com.pms.czhotelfoundation.bean.FileInfo;
import com.pms.czhotelfoundation.bean.hotel.HotelOrgPriceImage;
import com.pms.czhotelfoundation.bean.request.FileRequest;
import com.pms.czhotelfoundation.bean.request.UploadBaseToTx;
import com.pms.czhotelfoundation.bean.room.RoomTypeImage;
import com.pms.czhotelfoundation.dao.hotel.HotelOrgPriceImageDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeImageDao;
import com.pms.czhotelfoundation.service.FileService;
import com.pms.czpmsutils.CosFileUtil;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;

@Controller
@RequestMapping("/hotel/baseinfo/")
@Slf4j
public class FileController {

    @Resource
    private FileService fileService;

    @Resource
    private RoomTypeImageDao roomTypeImageDao;

    @Resource
    private HotelOrgPriceImageDao hotelOrgPriceImageDao;

    /**
     * 上传文件到腾讯云对象存储
     *
     * @return
     */
    @RequestMapping("uploadFile")
    @ResponseBody
    public ResponseData uploadFile(@RequestParam("file") MultipartFile file, @RequestParam("sessionToken") String sessionToken, @RequestParam(value = "fileInfoId", required = false) Integer fileInfoId) {
        ResponseData responseData = ResponseData.newSuccessData();
        FileRequest request = new FileRequest();
        request.setSessionToken(sessionToken);
        request.setMultipartFile(file);
        request.setFileInfoId(fileInfoId);

        try {
            FileInfo fileInfo = fileService.uploadFile(request);
            responseData.setData(fileInfo);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 上传文件到腾讯云对象存储•
     *
     * @return
     */
    @RequestMapping("uploadCustomerViewFile")
    @ResponseBody
    public ResponseData uploadCustomerViewFile(@RequestParam("file") MultipartFile file, @RequestParam("hid") Integer hid, @RequestParam(value = "fileInfoId", required = false) Integer fileInfoId) {
        ResponseData responseData = ResponseData.newSuccessData();
        FileRequest request = new FileRequest();
        request.setHid(hid);
        request.setMultipartFile(file);
        request.setFileInfoId(fileInfoId);

        try {
            FileInfo fileInfo = fileService.uploadCustomerViewFile(request);
            responseData.setData(fileInfo);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 上传文件到腾讯云对象存储
     *
     * @return
     */
    @RequestMapping("deleteFile")
    @ResponseBody
    public ResponseData uploadFile(@RequestBody FileRequest fileRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            fileService.deleteFile(fileRequest);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 上传文件到腾讯云对象存储
     *
     * @return
     */
    @RequestMapping("uploadRoomMultiFile")
    @ResponseBody
    public ResponseData uploadRoomMultiFile(@RequestParam("file") MultipartFile file, @RequestParam("sessionToken") String sessionToken, @RequestParam(value = "fileInfoId", required = false) Integer fileInfoId, @RequestParam(value = "bid", required = true) Integer bid, @RequestParam(value = "type", required = true) Integer type) {
        ResponseData responseData = ResponseData.newSuccessData();
        FileRequest request = new FileRequest();
        request.setMutil(true);
        request.setSessionToken(sessionToken);
        request.setMultipartFile(file);
        request.setBid(bid);
        request.setFileInfoId(fileInfoId);
        request.setType(type);

        try {
            FileInfo fileInfo = fileService.uploadRoomMultiFile(request);

            if (1 == request.getType()) {

                RoomTypeImage roomTypeImage = new RoomTypeImage();
                roomTypeImage.setFileId(fileInfo.getId());
                roomTypeImage.setRoomTypeId(bid);
                roomTypeImage.setState(1);
                roomTypeImage.setImages(fileInfo.getMd5());
                roomTypeImage.setUrl(fileInfo.getUrl());
                roomTypeImage.setHid(fileInfo.getHid());

                roomTypeImageDao.saveRoomTypeImage(roomTypeImage);
            } else if (2 == request.getType()) {

            }
            //上传房价牌图片
            else if (3 == request.getType()) {
                HotelOrgPriceImage hotelOrgPriceImage = new HotelOrgPriceImage();
                hotelOrgPriceImage.setHid(request.getHid());
                hotelOrgPriceImage.setImagePath(fileInfo.getUrl());
                hotelOrgPriceImage.setCreateTime(new Date());
                hotelOrgPriceImage.setState(1);
                hotelOrgPriceImage.setImageType(1);
                //设置类型为广告轮播图
                hotelOrgPriceImage.setBusinessType(2);
                Integer insert = hotelOrgPriceImageDao.insert(hotelOrgPriceImage);
            }

            //上传房价牌图片
            else if (4 == request.getType()) {
                HotelOrgPriceImage hotelOrgPriceImage = new HotelOrgPriceImage();
                hotelOrgPriceImage.setHid(request.getHid());
                hotelOrgPriceImage.setImagePath(fileInfo.getUrl());
                hotelOrgPriceImage.setCreateTime(new Date());
                hotelOrgPriceImage.setState(1);
                hotelOrgPriceImage.setImageType(1);
                //设置类型为背景图片
                hotelOrgPriceImage.setBusinessType(1);
                Integer insert = hotelOrgPriceImageDao.insert(hotelOrgPriceImage);
            }
            //酒店房价牌背景图片
            else if (5 == request.getType()) {
                HotelOrgPriceImage hotelOrgPriceImage = new HotelOrgPriceImage();
                hotelOrgPriceImage.setHid(request.getHid());
                hotelOrgPriceImage.setImagePath(fileInfo.getUrl());
                hotelOrgPriceImage.setCreateTime(new Date());
                hotelOrgPriceImage.setState(1);
                hotelOrgPriceImage.setImageType(1);
                hotelOrgPriceImage.setImageName("酒店二维码");
                //设置类型为集团二维码
                hotelOrgPriceImage.setBusinessType(3);
                Integer insert = hotelOrgPriceImageDao.insert(hotelOrgPriceImage);
            }

            responseData.setData(fileInfo);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 上传文件到腾讯云对象存储
     *
     * @return
     */
    @RequestMapping("mutilFileList")
    @ResponseBody
    public ResponseData mutilFileList(@RequestBody FileRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(fileService.mutilFileList(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 上传文件到腾讯云对象存储
     *
     * @return
     */
    @RequestMapping("addOrUpdateFileInfo")
    @ResponseBody
    public ResponseData addOrUpdateFileInfo(@RequestBody FileRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            FileInfo fileInfo = fileService.addOrUpdateFileInfo(request, null);
            responseData.setData(fileInfo);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 上传文件到腾讯云对象存储
     *
     * @return
     */
    @RequestMapping("uploadBaseToTx")
    @ResponseBody
    public ResponseData uploadBaseToTx(@RequestBody UploadBaseToTx request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {


            CosFileUtil.UploadObjectRsp uploadObjectRsp = fileService.uploadBaseToTxFunc(request);

            responseData.setData(uploadObjectRsp.getPath());


        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


}
