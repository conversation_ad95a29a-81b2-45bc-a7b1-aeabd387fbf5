package com.pms.czabsorders.web;


import com.github.pagehelper.Page;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.RegistEventRequest;
import com.pms.czpmsutils.request.RegistInvoiceRequest;
import com.pms.pmsorder.bean.RegistPersonHealthCode;
import com.pms.pmsorder.bean.request.PersonInfoRequest;
import com.pms.pmsorder.bean.request.RegistPageRequest;
import com.pms.pmsorder.bean.request.SearchRegistInfoRequest;
import com.pms.pmsorder.bean.search.*;
import com.pms.pmsorder.service.RegistGroupService;
import com.pms.pmsorder.service.RegistService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Controller
@RequestMapping("/hotel/regist/")
@Slf4j
public class RegistController {

    @Autowired
    private RegistService registService;
    @Autowired
    private RegistGroupService registGroupService;


    @RequestMapping("selectRegistMsgAndPersonName.do")
    @ResponseBody
    public ResponseData selectRegistMsgAndPersonName(@RequestBody JSONObject param) {
        return registService.selectRegistMsgAndPersonName(param);
    }

    @RequestMapping("getRegistMsgOnCheckIn.do")
    @ResponseBody
    public ResponseData getRegistMsgOnCheckIn(@RequestBody JSONObject param) {
        return registService.getRegistMsgOnCheckIn(param);
    }

    /**
     * 查询所有预订单
     *
     * @param request
     * @return
     */
    @RequestMapping("getRegistMsgOnCheckIn2.do")
    @ResponseBody
    public ResponseData getRegistMsgOnCheckIn2(@RequestBody RegistPageRequest request) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            Page<Map<String, Object>> goodsInfoByPage = registService.getRegistMsgOnCheckInPage(request);
            responseData.setData(goodsInfoByPage);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }


    @RequestMapping("getRegistMsgById.do")
    @ResponseBody
    public ResponseData getRegistMsgById(@RequestBody JSONObject param) {
        return registService.getRegistMsgById(param);
    }


    @RequestMapping("findAllTeamByRegistId.do")
    @ResponseBody
    public ResponseData findAllTeamByRegistId(@RequestBody JSONObject param) {
        return registService.findAllTeamByRegistId(param);
    }

    @RequestMapping("getAllGroup.do")
    @ResponseBody
    public ResponseData getAllGroup(@RequestBody JSONObject param) {
        return registGroupService.getAllGroup(param);
    }

    @RequestMapping("findRegistOnTeam.do")
    @ResponseBody
    public ResponseData findRegistOnTeam(@RequestBody RegistPageRequest registPageRequest) {
        return registService.findRegistOnTeam(registPageRequest);
    }

    /**
     * 查询当前客人是否在住
     *
     * @param param
     * @return
     */
    @RequestMapping("guestIsCheckin.do")
    @ResponseBody
    public ResponseData guestIsCheckin(@RequestBody JSONObject param) {
        return registService.guestIsCheckin(param);
    }

    /**
     * 添加或者修改预定人或者登记人信息
     *
     * @param param
     * @return
     */
    @RequestMapping("addOrUpdateGuest.do")
    @ResponseBody
    public ResponseData addOrUpdateGuest(@RequestBody JSONObject param) {
        return registService.addOrUpdateGuest(param);
    }

    /**
     * 添加或者修改预定人或者登记人信息
     *
     * @param param
     * @return
     */
    @RequestMapping("delRegistPerson.do")
    @ResponseBody
    public ResponseData delRegistPerson(@RequestBody JSONObject param) {
        return registService.delRegistPerson(param);
    }

    @RequestMapping("updateRegistPersonHealthCode.do")
    @ResponseBody
    public ResponseData updateRegistPersonHealthCode(@RequestBody RegistPersonHealthCode registPersonHealthCode) {
        return registService.updateRegistPersonHealthCode(registPersonHealthCode);
    }


    @RequestMapping("insertRegistPersonHealthCode.do")
    @ResponseBody
    public ResponseData insertRegistPersonHealthCode(@RequestBody RegistPersonHealthCode registPersonHealthCode) {
        return registService.insertRegistPersonHealthCode(registPersonHealthCode);
    }

    @RequestMapping("searchRegistPersonHealthCode.do")
    @ResponseBody
    public ResponseData searchRegistPersonHealthCode(@RequestBody RegistPersonHealthCodeSearch registPersonHealthCodeSearch) {
        return registService.searchRegistPersonHealthCode(registPersonHealthCodeSearch);
    }

    @RequestMapping("searchHotelRegistInvoiceList.do")
    @ResponseBody
    public ResponseData searchHotelRegistInvoiceList(@RequestBody RegistInvoiceSearch registInvoiceSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(registService.searchHotelRegistInvoiceList(registInvoiceSearch));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("saveOrUpdateRegistInvoice.do")
    @ResponseBody
    public ResponseData saveOrUpdateRegistInvoice(@RequestBody RegistInvoiceRequest registInvoiceRequest) {
        return registService.saveOrUpdateRegistInvoice(registInvoiceRequest);
    }

    @RequestMapping("searchRegistPersonListName.do")
    @ResponseBody
    public ResponseData searchRegistPersonListName(@RequestBody RegistPersonSearch RegistPersonSearch) {
        return registService.searchRegistPersonListName(RegistPersonSearch);
    }


    @RequestMapping("searchRegistPersonList.do")
    @ResponseBody
    public ResponseData searchRegistPersonList(@RequestBody RegistPersonSearch registPersonSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(registService.searchRegistPersonList(registPersonSearch));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 修改预定房型
     *
     * @param param
     * @return
     */
    @RequestMapping("dailyRoomState.do")
    @ResponseBody
    public ResponseData dailyRoomState(@RequestBody JSONObject param) {
        return registService.dailyRoomState(param);
    }


    /**
     * 查询当天登记数据 散客开房，预订入住，取消预订 退房数
     *
     * @param param
     * @return
     */
    @RequestMapping("maDayData.do")
    @ResponseBody
    public ResponseData maDayData(@RequestBody RegistInvoiceRequest param) {
        return registService.maDayData(param);
    }


    @RequestMapping("getRegistEventList.do")
    @ResponseBody
    public ResponseData getRegistEventList(@RequestBody RegistEventSearch registEventSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            return registService.getRegistEvent(registEventSearch);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("updateRegistEvent.do")
    @ResponseBody
    public ResponseData saveOrUpdateRegistInvoice(@RequestBody RegistEventRequest registInvoiceRequest) {
        return registService.updateRegistEvent(registInvoiceRequest);
    }


    @RequestMapping("searchRegistInfo.do")
    @ResponseBody
    public ResponseData searchRegistInfo(@RequestBody SearchRegistInfoRequest searchRegistInfoRequest) {
        return registService.searchRegistInfo(searchRegistInfoRequest);
    }

    @RequestMapping("searchPersonInfo.do")
    @ResponseBody
    public ResponseData searchPersonInfo(@RequestBody PersonInfoSearch personInfoSearch) {
        return registService.searchPersonInfo(personInfoSearch);
    }

    @RequestMapping("updatePersonInfo.do")
    @ResponseBody
    public ResponseData updatePersonInfo(@RequestBody PersonInfoRequest personInfoRequest) {
        return registService.updatePersonInfo(personInfoRequest);
    }
}
