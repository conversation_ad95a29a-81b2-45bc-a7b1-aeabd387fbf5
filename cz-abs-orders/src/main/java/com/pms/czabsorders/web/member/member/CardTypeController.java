package com.pms.czabsorders.web.member.member;


import com.pms.czmembership.bean.member.search.CardLevelUpgradeRuleSearch;
import com.pms.czmembership.service.memeber.CardTypeService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.CardLevelUpgradeRuleRequest;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 会员相关业务接口
 */
@Controller
@RequestMapping("/hotel/cardType/")
public class CardTypeController {

    @Autowired
    private CardTypeService cardTypeService;

    /**
     * 查询会员类型列表
     * @param param
     * @return
     */
    @RequestMapping("findAllCardType.do")
    @ResponseBody
    public ResponseData cardTypeService(@RequestBody JSONObject param){
       return cardTypeService.findAllCardType(param);
    }

    /**
     * 添加或更新会员类型
     * @param param
     * @return
     */
    @RequestMapping("addOrUpdateCardType.do")
    @ResponseBody
    public ResponseData addOrUpdateCardType(@RequestBody JSONObject param){
        return cardTypeService.addOrUpdateCardType(param);
    }

    /**
     * 查询会员等级
     * @param param
     * @return
     */
    @RequestMapping("findCardLevel.do")
    @ResponseBody
    public ResponseData findCardLevel(@RequestBody JSONObject param){
        return cardTypeService.findCardLevel(param);
    }

    /**
     * 添加或更新会员等级
     * @param param
     * @return
     */
    @RequestMapping("addOrUpdateCardLevel.do")
    @ResponseBody
    public ResponseData addOrUpdateCardLevel(@RequestBody JSONObject param){
        return cardTypeService.addOrUpdateCardLevel(param);
    }

    /**
     * 查询升级规则
     * @param cardLevelUpgradeRuleSearch
     * @return
     */
    @RequestMapping("searchCardLevelUpgradeRule.do")
    @ResponseBody
    public ResponseData searchCardLevelUpgradeRule(@RequestBody CardLevelUpgradeRuleSearch cardLevelUpgradeRuleSearch){
       return cardTypeService.searchCardLevelUpgradeRule(cardLevelUpgradeRuleSearch);
    }

    /**
     * 添加或更新升级规则
     * @param cardLevelUpgradeRuleRequest
     * @return
     */
    @RequestMapping("addOrUpdateCardLevelUpgradeRule.do")
    @ResponseBody
    public ResponseData addOrUpdateCardLevelUpgradeRule(@RequestBody CardLevelUpgradeRuleRequest cardLevelUpgradeRuleRequest){
        return cardTypeService.addOrUpdateCardLevelUpgradeRule(cardLevelUpgradeRuleRequest);
    }

}
