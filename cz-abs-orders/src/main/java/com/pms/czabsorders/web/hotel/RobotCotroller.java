package com.pms.czabsorders.web.hotel;

import com.pms.czhotelfoundation.service.smart.AudioService;
import com.pms.czhotelfoundation.service.thirdMachine.RobotHotelService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.RobotRequest;
import com.pms.czpmsutils.request.SendMessageToAudioRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/setting/")
public class RobotCotroller {

    @Autowired
    private RobotHotelService robotHotelService;
    @Autowired
    private AudioService audioService;


    // 送物
    @RequestMapping("customerCall")
    @ResponseBody
    public ResponseData customerCall(@RequestBody RobotRequest robotRequest){
        return robotHotelService.customerCall(robotRequest);

    }

    @RequestMapping("sendMessageToAudio.do")
    @ResponseBody
    public ResponseData sendMessageToAudio(@RequestBody SendMessageToAudioRequest param) {
        return audioService.sendMessageToAudio(param);
    }
}
