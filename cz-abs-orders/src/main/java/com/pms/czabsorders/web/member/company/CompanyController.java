package com.pms.czabsorders.web.member.company;


import com.pms.czmembership.bean.company.HotelCompanyAccount;
import com.pms.czmembership.bean.company.search.*;
import com.pms.czmembership.service.company.CompanyInfoService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.*;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * 协议单位相关接口
 */
@Api(tags = "协议单位相关接口")
@Controller
@RequestMapping("/hotel/company/")
@Slf4j
public class CompanyController {

    @Autowired
    private CompanyInfoService companyInfoService;

    /**
     * 查询所有协议单位
     *
     * @param hotelCompanyInfoSearchRequest
     * @return
     */
    @RequestMapping("getAllCompany.do")
    @ResponseBody
    public ResponseData getAllCompany(@RequestBody HotelCompanyInfoSearchRequest hotelCompanyInfoSearchRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(companyInfoService.getAllCompany(hotelCompanyInfoSearchRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 新增协议单位1
     *
     * @param param
     * @return
     */
    @RequestMapping("companyNameMap.do")
    @ResponseBody
    public ResponseData companyNameMap(@RequestBody HotelCompanyInfoSearchRequest param) {
        return companyInfoService.companyNameMap(param);
    }

    /**
     * 新增协议单位
     *
     * @param param
     * @return
     */
    @RequestMapping("addHotelCompany.do")
    @ResponseBody
    public ResponseData addHotelCompany(@RequestBody JSONObject param) {
        return companyInfoService.addHotelCompany(param);
    }

    /**
     * 新增协议单位账户
     *
     * @param param
     * @return
     */
    @RequestMapping("addHotelCompanyAccount.do")
    @ResponseBody
    public ResponseData addHotelCompanyAccount(@RequestBody JSONObject param) {
        return companyInfoService.addHotelCompanyAccount(param);
    }

    /**
     * 查询协议单位
     *
     * @param hotelCompanyAccountSearch
     * @return
     */
    @RequestMapping("findCompanyAccount.do")
    @ResponseBody
    public ResponseData findCompanyAccount(@RequestBody HotelCompanyAccountSearch hotelCompanyAccountSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(companyInfoService.findCompanyAccount(hotelCompanyAccountSearch));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 查询协议单位账号信息
     *
     * @param hotelCompanyAccountSearch
     * @return
     */
    @RequestMapping("findCompanyAccountForInfo.do")
    @ResponseBody
    public ResponseData findCompanyAccountForInfo(@RequestBody HotelCompanyAccountSearch hotelCompanyAccountSearch) {
        return companyInfoService.selectBySearchForInfo(hotelCompanyAccountSearch);
    }


    /**
     * 新增AR账记录
     *
     * @param param
     * @return
     */
    @RequestMapping("addArRecode.do")
    @ResponseBody
    public ResponseData addArRecode(@RequestBody JSONObject param) {
        return companyInfoService.addArRecode(param);
    }

    /**
     * AR帐冲账
     *
     * @param param
     * @return
     */
    @RequestMapping("strikeArRecode.do")
    @ResponseBody
    public ResponseData strikeArRecode(@RequestBody JSONObject param) {
        return companyInfoService.strikeArRecode(param);
    }


    /**
     * 查询AR记录
     * @param hotelCompanyArRecodeSearch
     * @return
     */
    @RequestMapping("serachHotelCompanyArRecode.do")
    @ResponseBody
    public ResponseData serachHotelCompanyArRecode(@RequestBody HotelCompanyArRecodeSearch hotelCompanyArRecodeSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            return companyInfoService.serachHotelCompanyArRecode(hotelCompanyArRecodeSearch);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 编辑AR记录
     * @param hotelCompanyRequest
     * @return
     */
    @RequestMapping("editHotelCompanyArRecode.do")
    @ResponseBody
    public ResponseData editHotelCompanyArRecode(@RequestBody HotelCompanyRequest hotelCompanyRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(companyInfoService.editHotelCompanyArRecode(hotelCompanyRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 查询支付记录
     * @param hotelCompanyPayRecordSearch
     * @return
     */
    @RequestMapping("searchHotelCompanyPayRecord.do")
    @ResponseBody
    public ResponseData searchHotelCompanyPayRecord(@RequestBody HotelCompanyPayRecordSearch hotelCompanyPayRecordSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            return companyInfoService.searchHotelCompanyPayRecord(hotelCompanyPayRecordSearch);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 新增或编辑支付记录
     * @param hotelCompanyPayRecordRequest
     * @return
     */
    @RequestMapping("addOrUpdateHotelCompanyPayRecord.do")
    @ResponseBody
    public ResponseData addOrUpdateHotelCompanyPayRecord(@RequestBody HotelCompanyPayRecordRequest hotelCompanyPayRecordRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(companyInfoService.addOrUpdateHotelCompanyPayRecord(hotelCompanyPayRecordRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 申请合作
     * @param request
     * @return
     */
    @RequestMapping("applyHotel")
    @ResponseBody
    public ResponseData applyHotel(@RequestBody ApplyHotelRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            companyInfoService.applyHotel(request);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 查询挂账核销记录
     *
     * @param param
     * @return
     */
    @RequestMapping("searchHotelCompanyWriteoff.do")
    @ResponseBody
    public ResponseData searchHotelCompanyWriteoff(@RequestBody HotelCompanyWriteoffSearch param) {

        return companyInfoService.searchHotelCompanyWriteoff(param);
    }

    /**
     * 查询汇总信息
     *
     * @param param
     * @return
     */
    @RequestMapping("searchHotelCompanyAccountInfo.do")
    @ResponseBody
    public ResponseData searchHotelCompanyAccountInfo(@RequestBody HotelCompanyAccountInfoSearch param) {
        return companyInfoService.searchHotelCompanyAccountInfo(param);
    }

    /**
     * 查询汇总信息
     *
     * @param param
     * @return
     */
    @RequestMapping("searchHotelCompanyAccountInfoMap.do")
    @ResponseBody
    public ResponseData searchHotelCompanyAccountInfoMap(@RequestBody HotelCompanyAccountSearch param) {
        return companyInfoService.searchHotelCompanyAccountInfoMap(param);
    }

    /**
     * 查询汇总信息
     *
     * @param param
     * @return
     */
    @RequestMapping("searchHotelCompanyPayArMap.do")
    @ResponseBody
    public ResponseData searchHotelCompanyPayArMap(@RequestBody HotelCompanyArRecodeSearch param) {
        return companyInfoService.searchHotelCompanyPayArMap(param);
    }

    /**
     * 修改账户挂账上线
     *
     * @param hotelCompanyAccount
     * @return
     */
    @RequestMapping("updateCompanyAccountInfo.do")
    @ResponseBody
    public ResponseData updateCompanyAccountInfo(@RequestBody HotelCompanyAccount hotelCompanyAccount) {
        return companyInfoService.updateCompanyAccountInfo(hotelCompanyAccount);
    }

    /**
     * 解约
     * @param hotelCompanyArRecodeRequest
     * @return
     */
    @RequestMapping("breakUpCompanyAccount.do")
    @ResponseBody
    public ResponseData breakUpCompanyAccount(@RequestBody HotelCompanyArRecodeRequest hotelCompanyArRecodeRequest) {
        return companyInfoService.breakUpCompanyAccount(hotelCompanyArRecodeRequest);
    }

}
