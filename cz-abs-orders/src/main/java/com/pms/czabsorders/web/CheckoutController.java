package com.pms.czabsorders.web;

import com.pms.czabsorders.service.checkout.CheckOutService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.CheckoutParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 退房的相关业务
 */
@Controller
@RequestMapping("/hotel/checkout/")
public class CheckoutController {

    @Autowired
    private CheckOutService checkOutService;

    /**
     * 结账
     * @param checkoutParam
     * @return
     */
    @RequestMapping("checkOut.do")
    @ResponseBody
    public ResponseData checkOut(@RequestBody CheckoutParam checkoutParam){
        return checkOutService.checkOut(checkoutParam);
    }

    /**
     * 挂账
     * @param checkoutParam
     * @return
     */
    @RequestMapping("onAccount.do")
    @ResponseBody
    public ResponseData onAccount(@RequestBody CheckoutParam checkoutParam){
        return checkOutService.onAccount(checkoutParam);
    }

    /**
     * 账务单独结算
     * @param checkoutParam
     * @return
     */
    @RequestMapping("checkoutAccount.do")
    @ResponseBody
    public ResponseData checkoutAccount(@RequestBody CheckoutParam checkoutParam){
        return checkOutService.checkoutAccount(checkoutParam);
    }

    /**
     * 快速退房
     * @param checkoutParam
     * @return
     */
    @RequestMapping("fastCheckout.do")
    @ResponseBody
    public ResponseData fastCheckout(@RequestBody CheckoutParam checkoutParam){
        return checkOutService.fastCheckout(checkoutParam);
    }

    /**
     * 取消登记
     * @param checkoutParam
     * @return
     */
    @RequestMapping("cancelRegistFunc.do")
    @ResponseBody
    public ResponseData cancelRegistFunc(@RequestBody CheckoutParam checkoutParam){
        return checkOutService.cancelRegistFunc(checkoutParam);
    }
}
