package com.pms.czabsorders.web.salegoods;


import com.pms.czabsorders.bean.salesgood.AddGoodsForRegistParam;
import com.pms.czabsorders.bean.salesgood.ReturnedGoodsParam;
import com.pms.czabsorders.service.salegoods.SalegoodsService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.pmswarehouse.bean.request.SelectGoodsStockParam;
import com.pms.pmswarehouse.bean.search.GoodsDumbSearch;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/absGoods/")
@Slf4j
public class SalegoodsController {

    @Autowired
    private SalegoodsService salegoodsService;

    @RequestMapping("findAllGoodsDumb.do")
    @ResponseBody
    public ResponseData findAllGoodsDumb(@RequestBody GoodsDumbSearch goodsDumbSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(salegoodsService.findAllGoodsDumb(goodsDumbSearch));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("addOrUpdateGoodsDumb.do")
    @ResponseBody
    public ResponseData addOrUpdateGoodsDumb(@RequestBody JSONObject param) {
        return salegoodsService.addOrUpdateGoodsDumb(param);
    }


    @RequestMapping("cancelGoodsDumb.do")
    @ResponseBody
    public ResponseData cancelGoodsDumb(@RequestBody JSONObject param) {
        return salegoodsService.cancelGoodsDumb(param);
    }

    @RequestMapping("findGoodsDumbInfo.do")
    @ResponseBody
    public ResponseData findGoodsDumbInfo(@RequestBody JSONObject param) {
        return salegoodsService.findGoodsDumbInfo(param);
    }

    @RequestMapping("registAddGoods.do")
    @ResponseBody
    public ResponseData registAddGoods(@RequestBody AddGoodsForRegistParam param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData = salegoodsService.registAddGoods(param);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("returnedGoods.do")
    @ResponseBody
    public ResponseData returnedGoods(@RequestBody ReturnedGoodsParam param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData = salegoodsService.returnedGoods(param);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 查询商品库存统计
     *
     * @param param
     * @return
     */
    @PostMapping("selectGoodsStock")
    @ResponseBody
    public ResponseData selectGoodsStock(@RequestBody SelectGoodsStockParam param) {
        return salegoodsService.selectGoodsStock(param);
    }

//    /**
//     * 导出商品库存统计
//     *
//     * @param param
//     * @return
//     */
//    @PostMapping("exportGoodsStock")
//    @ResponseBody
//    public void exportGoodsStock(@RequestBody SelectGoodsStockParam param, HttpServletResponse response) {
//        salegoodsService.exportGoodsStock(param, response);
//    }


}
