package com.pms.czabsorders.web.hotel.hotel;


import com.pms.czhotelfoundation.bean.request.AddCostCodeRequest;
import com.pms.czhotelfoundation.bean.request.EnableCostCodeRequest;
import com.pms.czhotelfoundation.service.hotelcode.HotelCodeService;
import com.pms.czpmsutils.ResponseData;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 费用码相关查询业务
 */
@Controller
@RequestMapping("/hotel/baseinfo/")
public class HotelCodeController {

    @Autowired
    private HotelCodeService hotelCodeService;

    /**
     * 查询当前客源
     * @param param
     * @return
     */
    @RequestMapping("findAllResource.do")
    @ResponseBody
    public ResponseData findAllHotel(@RequestBody JSONObject param){

        return hotelCodeService.findAllResource(param);

    }

    /**
     * 查询所有费用码
     * @param param
     * @return
     */
    @RequestMapping("findAllCostCode.do")
    @ResponseBody
    public ResponseData findAllCostCode(@RequestBody JSONObject param){

        return hotelCodeService.findAllCostCode(param);

    }

    /**
     * 查询所有启用费用码
     * @param param
     * @return
     */
    @RequestMapping("findAllCostCodeQy.do")
    @ResponseBody
    public ResponseData findAllCostCodeQy(@RequestBody JSONObject param){

        return hotelCodeService.findAllCostCodeQy(param);

    }

    /**
     * 启用停用费用码
     * @param param
     * @return
     */
    @RequestMapping("enableCostCode.do")
    @ResponseBody
    public ResponseData enableCostCode(@RequestBody EnableCostCodeRequest param){

        return hotelCodeService.enableCostCode(param);

    }


    /**
     * 自定义费用码
     * @param addCostCodeRequest
     * @return
     */
    @RequestMapping("addCostCode.do")
    @ResponseBody
    public ResponseData addCostCode(@RequestBody AddCostCodeRequest addCostCodeRequest){

        return hotelCodeService.addCostCode(addCostCodeRequest);

    }

}
