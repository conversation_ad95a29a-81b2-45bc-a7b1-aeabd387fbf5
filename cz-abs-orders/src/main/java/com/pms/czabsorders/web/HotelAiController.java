package com.pms.czabsorders.web;


import com.pms.czabsorders.service.HotelAiService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: 陈星宇
 * @CreateTime: 2025-07-01
 * @Description:
 */
@Controller
@RequestMapping("/pms-price/")
public class HotelAiController {

    @Autowired
    private HotelAiService hotelAiService;

    /**
     * 获取收益概览数据指标
     * @param dataIndicatorsReq
     * @return
     */
    @PostMapping("incomeOverviewIndicator")
    @ResponseBody
    public ResponseData dataIndicators(@RequestBody DataIndicatorsReq dataIndicatorsReq){
        return hotelAiService.dataIndicators(dataIndicatorsReq);
    }

    /**
     * 获取收益概览订单情况
     * @param overviewOrderReq
     * @return
     */
    @PostMapping("incomeOverviewOrder")
    @ResponseBody
    public ResponseData overviewOrder(@RequestBody OverviewOrderReq overviewOrderReq){
        return hotelAiService.overviewOrder(overviewOrderReq);
    }

    /**
     * 房价日历-获取房型价格列表
     * @param priceListReq
     * @return
     */
    @PostMapping("room-type/price-list")
    @ResponseBody
    public ResponseData getPriceList(@RequestBody PriceListReq priceListReq){
        return hotelAiService.getPriceList(priceListReq);
    }

    /**
     * 房价日历-编辑房型价格
     * @param editRoomTypePriceReq
     * @return
     */
    @PostMapping("room-type/price")
    @ResponseBody
    public ResponseData editRoomTypePrice(@RequestBody EditRoomTypePriceReq editRoomTypePriceReq){
        return hotelAiService.editRoomTypePrice(editRoomTypePriceReq);
    }

    /**
     * 房价日历-获取可选房型
     * @param baseRequest
     * @return
     */
    @PostMapping("room-type/optional-list")
    @ResponseBody
    public ResponseData getRoomTypeList(@RequestBody BaseRequest baseRequest){
        return hotelAiService.getRoomTypeList(baseRequest);
    }

    /**
     * 房价日历-获取可选渠道
     * @param baseRequest
     * @return
     */
    @PostMapping("room-type/optional-channel")
    @ResponseBody
    private ResponseData getOptionalChannel(@RequestBody BaseRequest baseRequest){
        return hotelAiService.getOptionalChannel(baseRequest);
    }

    /**
     * 房价日历-同步房型价格至渠道
     */
    @PostMapping("room-type/synchronous-to-channel")
    @ResponseBody
    public ResponseData synchronousToChannel(@RequestBody SynchronousToChannelReq synchronousToChannelReq){
        return hotelAiService.synchronousToChannel(synchronousToChannelReq);
    }

    /**
     * 房价日历-查询房型房价日历
     * @param calenderReq
     * @return
     */
    @PostMapping("room-type/calender")
    @ResponseBody
    public ResponseData calender(@RequestBody CalenderReq calenderReq){
        return hotelAiService.calender(calenderReq);
    }

    /**
     * 房价日历-查询房型日历详细信息
     * @param calenderDetailReq
     * @return
     */
    @PostMapping("room-type/calender-detail")
    @ResponseBody
    public ResponseData calenderDetail(@RequestBody CalenderDetailReq calenderDetailReq){
        return hotelAiService.calenderDetail(calenderDetailReq);
    }


    /**
     * 设置自定义价格
     * @param editPriceReq
     * @return
     */
    @PostMapping("room-type/edit-price")
    @ResponseBody
    public ResponseData editPrice(@RequestBody EditPriceReq editPriceReq){
        return hotelAiService.editPrice(editPriceReq);
    }

    /**
     * 竞对请求-获取酒店位置信息
     * @param sessionToken
     * @return
     */
    @GetMapping("hotel/location-info")
    @ResponseBody
    public ResponseData locationInfo(String sessionToken){
        return hotelAiService.locationInfo(sessionToken);
    }

    @PutMapping("hotel/location-info")
    @ResponseBody
    public ResponseData editLocationInfo(@RequestBody LocationInfoReq locationInfoReq){
        return hotelAiService.editLocationInfo(locationInfoReq);
    }
}
