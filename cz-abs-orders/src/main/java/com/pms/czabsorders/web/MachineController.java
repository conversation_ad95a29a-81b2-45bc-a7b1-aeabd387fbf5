package com.pms.czabsorders.web;


import com.pms.czabsorders.service.machine.MachineMainService;
import com.pms.czabsorders.service.machine.MachineService;
import com.pms.czabsorders.service.machine.MachineUserInfoService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.pmsorder.bean.machine.MachineMain;
import com.pms.pmsorder.bean.machine.model.AvailRoomRequest;
import com.pms.pmsorder.bean.machine.model.ExecuteAssignRoomRequest;
import com.pms.pmsorder.bean.machine.model.HotelOrderListRequest;
import com.pms.pmsorder.bean.machine.search.MachineMainSearch;
import com.pms.pmsorder.bean.machine.search.MachineParamTypeSearch;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Controller
@RequestMapping("/hotel/machine/")
public class MachineController {

    protected final Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private MachineService machineService;

    @Autowired
    private MachineMainService machineMainService;

    @Autowired
    private MachineUserInfoService machineUserInfoService;

    /**
     * 查询酒店下所有自助机
     * @param param
     * @return
     */
    @RequestMapping("findAllMachineByHid.do")
    @ResponseBody
    public ResponseData findAllMachineByHid(@RequestBody MachineMainSearch param){

        return machineService.findAllMachineByHid(param);

    }

    @RequestMapping("findMachineByMac.do")
    @ResponseBody
    public ResponseData findMachineByMac(@RequestBody JSONObject param){

        return machineService.findMachineByMac(param);

    }

    /**
     * 查询酒店下所有自助机
     * @param param
     * @return
     */
    @RequestMapping("hotelMachineExistence.do")
    @ResponseBody
    public ResponseData hotelMachineExistence(@RequestBody MachineMainSearch param){

        return machineService.hotelMachineExistence(param);

    }


    /**
     * 查询当前自助机日志
     * @param param
     * @return
     */
    @RequestMapping("findRecordByMachineUUID.do")
    @ResponseBody
    public ResponseData findRecordByMachineUUID(@RequestBody JSONObject param){

        return machineService.findRecordByMachineUUID(param);

    }

    /**
     * 查询当前自助机日志
     * @param param
     * @return
     */
    @RequestMapping("updateMachineSetting.do")
    @ResponseBody
    public ResponseData updateMachineSetting(@RequestBody JSONObject param){

        return machineService.updateMachineSetting(param);

    }


    /**
     * 查询当前自助机所有设置
     * @param param
     *  machineId 必传
     *  uuid  必传
     * @return
     */
    @RequestMapping("findAllMachineSetting.do")
    @ResponseBody
    public ResponseData findAllMachineSetting(@RequestBody JSONObject param){
        return machineService.findAllMachineSetting(param);
    }

    /**
     * 查询自助机所有公共参数
     * @param machineParamTypeSearch
     * @return
     */
    @RequestMapping("getMachineParam.do")
    @ResponseBody
    public ResponseData getMachineParam(@RequestBody MachineParamTypeSearch machineParamTypeSearch){
        return machineService.getMachineParam(machineParamTypeSearch);
    }

    /**
     * 根据mac地址查询自助机信息
     * @param request
     * @return
     */
    @RequestMapping("findMachineByMacId.do")
    @ResponseBody
    public Map<String,Object> findMachineByMacId(@RequestBody JSONObject request){

        return machineService.findMachineByMacId(request);

    }

    /**
     * 修改或添加自助机
     * @param param
     * @return
     */
    @RequestMapping("addOrUpdateMachine.do")
    @ResponseBody
    public ResponseData addOrUpdateMachine(@RequestBody JSONObject param){
        return machineMainService.addOrUpdateMachine(param);
    }

    @RequestMapping("findAllMaichine.do")
    @ResponseBody
    public Map<String, Object> findAllMaichine(@RequestBody JSONObject map) {

        Map<String, Object> findAllMaichine = machineMainService.findAllMachine(map);

        return findAllMaichine;
    }

    @RequestMapping("copyMachineSetting.do")
    @ResponseBody
    public Map<String, Object> copyMachineSetting(@RequestBody JSONObject map) {

        Map<String, Object> copyMachineSetting = machineService.copyMachineSetting(map);

        return copyMachineSetting;
    }

    @RequestMapping("findMachineSettingByHid.do")
    @ResponseBody
    public ResponseData findMachineSettingByHid(@RequestBody JSONObject param) {
        return machineService.findMachineSettingByHid(param);
    }


    @RequestMapping("findHotelOrderList.do")
    @ResponseBody
    public ResponseData findHotelOrderList(@RequestBody HotelOrderListRequest hotelOrderListRequest) {
        return machineService.findHotelOrderList(hotelOrderListRequest);
    }

    @RequestMapping("getAvailRoomList.do")
    @ResponseBody
    public ResponseData getAvailRoomList(@RequestBody AvailRoomRequest availRoomRequest) {
        return machineService.getAvailRoomList(availRoomRequest);
    }

    @RequestMapping("assignRoom.do")
    @ResponseBody
    public ResponseData assignRoom(@RequestBody ExecuteAssignRoomRequest executeAssignRoomRequest) {
        return machineService.availRoom(executeAssignRoomRequest);
    }

    @RequestMapping("getHotelMachine.do")
    @ResponseBody
    public ResponseData getHotelMachine(@RequestBody MachineMainSearch machineMainSearch) {
        return machineMainService.getHotelMachine(machineMainSearch);
    }

    @RequestMapping("updateHotelMachine.do")
    @ResponseBody
    public ResponseData updateHotelMachine(@RequestBody MachineMain machineMain) {
        return machineMainService.updateHotelMachine(machineMain);
    }
    @RequestMapping("searchRegistInfoByMac.do")
    @ResponseBody
    public ResponseData searchRegistInfoByMac(HttpServletRequest request,@RequestBody MachineMainSearch machineMainSearch) {
        String macUuid = machineMainSearch.getMacUuid();
        String macUuidUpperCase = macUuid.toUpperCase();
        machineMainSearch.setMacUuid(macUuidUpperCase);
        String ipAddress = HotelUtils.getIpAddress(request);
        log.info("searchRegistInfoByMac---ip:{},uid:{}",ipAddress,machineMainSearch.getMacUuid());
        return machineMainService.searchRegistInfoByMac(machineMainSearch);
    }



}
