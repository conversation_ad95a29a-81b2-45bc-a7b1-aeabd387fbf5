package com.pms.czabsorders.web.hotel.price;


import com.pms.czhotelfoundation.bean.price.search.RoomDayPriceSearch;
import com.pms.czhotelfoundation.service.price.RoomDayPriceService;
import com.pms.czhotelfoundation.service.price.RoomPriceCodeService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.BaseRequest;
import com.pms.czpmsutils.request.FindRoomTypeListForRateCodeRequest;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 酒店房价码相关业务
 */
@Controller
@RequestMapping("/hotel/rate/")
@Slf4j
public class RoomRateController {

    @Autowired
    private RoomPriceCodeService roomPriceCodeService;
    @Autowired
    private RoomDayPriceService roomDayPriceService;


    /**
     * 房间房价码列表
     *
     * @param param
     * @return
     */
    @RequestMapping("getPriceCodeByHid.do")
    @ResponseBody
    public ResponseData getPriceCodeByHid(@RequestBody JSONObject param) {
        Object noCache = param.get("noCache");
        if (noCache != null && noCache.equals("noCache")) {
            return roomPriceCodeService.getPriceCodeByHid(param, "noCache");
        } else {
            return roomPriceCodeService.getPriceCodeByHid(param);
        }
    }


    /**
     * 房间房价码
     *
     * @param baseRequest
     * @return
     */
    @RequestMapping("getRoomRateCode.do")
    @ResponseBody
    public ResponseData getRoomRateCode(@RequestBody BaseRequest baseRequest) {
        return roomPriceCodeService.getRoomRateCode(baseRequest);
    }

    /**
     * 暂未使用
     *
     * @param param
     * @return
     */
    @RequestMapping("getMsjPriceCodeByHid.do")
    @ResponseBody
    public ResponseData getMsjPriceCodeByHid(@RequestBody JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Integer msjPriceCodeByHid = roomPriceCodeService.getMsjPriceCodeByHid(param);
            responseData.setData(msjPriceCodeByHid);
            return responseData;
        } catch (Exception e) {
            log.error("",e);
            responseData = ResponseData.newSuccessData();
            responseData.setResult(ER.ERR);
        }

        return responseData;
    }


    /**
     * 房间房价码详情
     *
     * @param param
     * @return
     */
    @RequestMapping("findPriceCodeDetail.do")
    @ResponseBody
    public ResponseData findPriceCodeDetail(@RequestBody JSONObject param) {
        return roomPriceCodeService.findPriceCodeDetail(param);
    }

    /**
     * 房间房价码修改
     *
     * @param param
     * @return
     */
    @RequestMapping("updateRateCodeSpecific.do")
    @ResponseBody
    public ResponseData updateRateCodeSpecific(@RequestBody JSONObject param) {

        return roomPriceCodeService.updateRateCodeSpecific(param);
    }

    /**
     * 房间房价码批量修改
     *
     * @param param
     * @return
     */
    @RequestMapping("updateRateCodeSpecificList.do")
    @ResponseBody
    public ResponseData updateRateCodeSpecificList(@RequestBody JSONObject param) {
        return roomPriceCodeService.updateRateCodeSpecificList(param);
    }

    /**
     * 房间房价码状态修改
     *
     * @param param
     * @return
     */
    @RequestMapping("updateRateCodeState.do")
    @ResponseBody
    public ResponseData updateRateCodeState(@RequestBody JSONObject param) {
        return roomPriceCodeService.updateRateCodeState(param);
    }

    /**
     * 房间房价码新增或修改
     *
     * @param param
     * @return
     */
    @RequestMapping("addOrUpdatRateCode.do")
    @ResponseBody
    public ResponseData addOrUpdatRateCode(@RequestBody JSONObject param) {
        return roomPriceCodeService.addOrUpdatRateCode(param);
    }

    /**
     * 房间房价查询
     *
     * @param param
     * @return
     */
    @RequestMapping("findRoomPrice.do")
    @ResponseBody
    public ResponseData findRoomPrice(@RequestBody JSONObject param) {
        return roomDayPriceService.findRoomPrice(param);
    }


    /**
     * 房间房价码分组查询
     *
     * @param param
     * @return
     */
    @RequestMapping("findPriceCodeByGroupId.do")
    @ResponseBody
    public ResponseData findPriceCodeByGroupId(@RequestBody JSONObject param) {
        ResponseData priceCodeByGroupId = roomPriceCodeService.findPriceCodeByGroupId(param);
        return priceCodeByGroupId;
    }

    /**
     * 获取房型价格
     *
     * @param roomDayPriceSearch
     * @return
     */
    @RequestMapping("getRoomTypePrice.do")
    @ResponseBody
    public ResponseData getRoomTypePrice(@RequestBody RoomDayPriceSearch roomDayPriceSearch) {
        ResponseData priceCodeByGroupId = roomDayPriceService.getRoomTypePrice(roomDayPriceSearch);
        return priceCodeByGroupId;
    }

    @RequestMapping("findRoomTypeListForRateCode.do")
    @ResponseBody
    public ResponseData findRoomTypeListForRateCode(@RequestBody FindRoomTypeListForRateCodeRequest findRoomTypeListForRateCodeRequest) {
        ResponseData roomTypeListForRateCode = roomPriceCodeService.findRoomTypeListForRateCode(findRoomTypeListForRateCodeRequest);
        return roomTypeListForRateCode;
    }

    /**
     * 获取酒店原始价格
     *
     * @param baseRequest
     * @return
     */
    @RequestMapping("getHotelDayOrgPrice.do")
    @ResponseBody
    public ResponseData getHotelDayOrgPrice(@RequestBody BaseRequest baseRequest) {
        ResponseData hotelDayOrgPrice = roomDayPriceService.getHotelDayOrgPrice(baseRequest);
        return hotelDayOrgPrice;
    }

}
