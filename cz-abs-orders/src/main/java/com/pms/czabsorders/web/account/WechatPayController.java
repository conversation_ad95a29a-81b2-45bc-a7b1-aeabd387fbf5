package com.pms.czabsorders.web.account;


import com.pms.czaccount.bean.pay.CreatCode;
import com.pms.czaccount.service.wechat.WeChatPayService;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付
 */
@Controller
@RequestMapping("/hotel/wechatPay/")
public class WechatPayController {

    @Autowired
    private WeChatPayService weChatPayService;

    /**
     * 获取微信二维码信息
     * @param param
     *    参数: money 单位元
     * @return
     */
    @RequestMapping("getWxQrcode.do")
    @ResponseBody
    public Map<String,Object> getWxQrcode(@RequestBody JSONObject param){
       // Map<String, Object> map = JSONObject.fromObject(request.getAttribute(ER.PARAM_MAP));
        return weChatPayService.getWeChatQrcode(param);
    }

    /**
     * 二维码转换
     * @param request
     * @param response
     *   参数:content 二维码内容
     * @throws Exception
     */
    @RequestMapping("transformationQrcode.do")
    @ResponseBody
    public void transformationQrcode(@RequestBody JSONObject map, HttpServletResponse response ) throws Exception {
        HashMap<String, String> map1 = new HashMap<>();
        map1.put("content",map.get("content").toString());
        CreatCode.createCodeStream1(map1,response);
    }

    /**
     * 获取支付成功的结果，最长执行120秒
     * @param request
     *  参数:mainId   getWxQrcode方法返回的mainId
     * @return
     */
    @RequestMapping("handleWeChatPayResult.do")
    @ResponseBody
    public Map<String,Object> handleWeChatPayResult(@RequestBody JSONObject map ){
        return weChatPayService.handleWeChatPayResult(map);
    }

    /**
     * 微信退款
     * @param request
     *  参数:mainId getWxQrcode方法返回的mainId
     *      refundMoney 单位 分
     * @return
     */
    @RequestMapping("wechatRefund.do")
    @ResponseBody
    public Map<String,Object> wechatRefund(@RequestBody JSONObject map ){
        return weChatPayService.wechatRefund(map);
    }

    /**
     * 微信退款
     * @param request
     *  参数:mainId getWxQrcode方法返回的mainId
     *      refundMoney 单位 分
     * @return
     */
    @RequestMapping("wechatMinigroRefund.do")
    @ResponseBody
    public Map<String,Object> wechatMinigroRefund(@RequestBody JSONObject map ){
        return weChatPayService.wechatMinigroRefund(map);
    }


    /**
     * 获取微信支付账号信息
     * @param request
     * @return
     */
    @RequestMapping("getWeChatPayMsg.do")
    @ResponseBody
    public Map<String,Object> getWeChatPayMsg(@RequestBody JSONObject map ){
        return weChatPayService.getWeChatPayMsg(map);
    }

    /**
     * 保存或修改微信账号信息
     * @param request
     * @return
     */
    @RequestMapping("saveOrUpdateWeChatPayMsg.do")
    @ResponseBody
    public Map<String,Object> saveOrUpdateWeChatPayMsg(@RequestBody JSONObject map ){
        return weChatPayService.saveOrUpdateWeChatPayMsg(map);
    }

    /**
     * 关闭微信订单
     * @param request
     * @return
     */
    @RequestMapping("weChatPayClose.do")
    @ResponseBody
    public Map<String,Object> weChatPayClose(@RequestBody JSONObject map ){
        return weChatPayService.weChatPayClose( map);
    }

    /**
     * 保存微信证书
     * @return
     */
    @RequestMapping("addWxCert.do")
    @ResponseBody
    public Map<String,Object> addWxCert(MultipartFile request){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("s",request);
        return weChatPayService.addWxCert(jsonObject);
    }


}
