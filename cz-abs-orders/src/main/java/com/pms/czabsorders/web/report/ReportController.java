package com.pms.czabsorders.web.report;


import com.github.pagehelper.Page;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czreport.bean.*;
import com.pms.czreport.service.impl.ReportService;
import com.pms.czreport.util.ExcelUtils;
import com.pms.czreport.view.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Map;

@RestController
@Slf4j
public class ReportController {

    @Resource
    private ReportService reportService;

    /**
     * 收银员交接表
     *
     * @param reportRequest
     * @return
     */
    @RequestMapping("/hotel/report/account")
    public ResponseData report(@RequestBody ReportRequest reportRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (reportRequest.getExport() != null && reportRequest.getExport().equals(1)) {
                reportRequest.setPageNum(1);
                reportRequest.setPageSize(10000);
                Page<AccountReportView> accountReportViews = reportService.accountReport(reportRequest);
                long t1 = System.currentTimeMillis();

                ExcelUtils.writeExcel(response, accountReportViews, AccountReportView.class);
                long t2 = System.currentTimeMillis();
                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {
                responseData.setData(reportService.accountReport(reportRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 商品销售汇总
     *
     * @param goodsSalesSummaryRequest
     * @return
     */
    @RequestMapping("/hotel/report/goods_sales_summary")
    public ResponseData goodsSalesSummary(@RequestBody GoodsSalesSummaryRequest goodsSalesSummaryRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(reportService.goodsSalesSummary(goodsSalesSummaryRequest));

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 商品销售明细
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/goods_sales_detail")
    public ResponseData goods_sales_detail(@RequestBody GoodsSalesDetailRequest goodsSalesDetailRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (goodsSalesDetailRequest.getExport() != null && goodsSalesDetailRequest.getExport().equals(1)) {
                goodsSalesDetailRequest.setPageNum(1);
                goodsSalesDetailRequest.setPageSize(10000);
                Page<GoodsSalesDetailView> accountReportViews = reportService.goodsSalesDetail(goodsSalesDetailRequest);
                long t1 = System.currentTimeMillis();
                ExcelUtils.writeExcel(response, accountReportViews, GoodsSalesDetailView.class);
                long t2 = System.currentTimeMillis();
                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {
                responseData.setData(reportService.goodsSalesDetail(goodsSalesDetailRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 门店入库清单表
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/storeImportData")
    public ResponseData storeImportData(@RequestBody StoreImportDataRequest request, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (request.getExport() != null && request.getExport().equals(1)) {
                request.setPageNum(1);
                request.setPageSize(10000);
                Page<StoreImportDataView> accountReportViews = reportService.storeImportData(request);
                long t1 = System.currentTimeMillis();
                ExcelUtils.writeExcel(response, accountReportViews, StoreImportDataView.class);
                long t2 = System.currentTimeMillis();
                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {
                responseData.setData(reportService.storeImportData(request));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 门店出库清单表
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/storeExportData")
    public ResponseData storeExportData(@RequestBody StoreImportDataRequest request, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (request.getExport() != null && request.getExport().equals(1)) {
                request.setPageNum(1);
                request.setPageSize(10000);
                Page<StoreImportDataView> accountReportViews = reportService.storeExportData(request);
                long t1 = System.currentTimeMillis();
                ExcelUtils.writeExcel(response, accountReportViews, StoreImportDataView.class);
                long t2 = System.currentTimeMillis();
                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {
                responseData.setData(reportService.storeExportData(request));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 应收记录
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/reversal_record")
    public ResponseData reversalRecord(@RequestBody ReversalRecordRequest reversalRecordRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (reversalRecordRequest.getExport() != null && reversalRecordRequest.getExport().equals(1)) {
                reversalRecordRequest.setPageNum(1);
                reversalRecordRequest.setPageSize(10000);
                Page<ReversalRecordView> accountReportViews = reportService.reversalRecord(reversalRecordRequest);
                long t1 = System.currentTimeMillis();
                ExcelUtils.writeExcel(response, accountReportViews, ReversalRecordView.class);
                long t2 = System.currentTimeMillis();
                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {
                responseData.setData(reportService.reversalRecord(reversalRecordRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 转账记录
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/transfer_record")
    public ResponseData transferRecord(@RequestBody ReversalRecordRequest reversalRecordRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (reversalRecordRequest.getExport() != null && reversalRecordRequest.getExport().equals(1)) {
                reversalRecordRequest.setPageNum(1);
                reversalRecordRequest.setPageSize(10000);
                Page<ReversalRecordView> accountReportViews = reportService.transferRecord(reversalRecordRequest);
                long t1 = System.currentTimeMillis();
                ExcelUtils.writeExcel(response, accountReportViews, ReversalRecordView.class);
                long t2 = System.currentTimeMillis();
                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {
                responseData.setData(reportService.transferRecord(reversalRecordRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 挂账记录
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/open_account")
    public ResponseData openAccount(@RequestBody OpenAccountRequest openAccountRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (openAccountRequest.getExport() != null && openAccountRequest.getExport().equals(1)) {
                openAccountRequest.setPageNum(1);
                openAccountRequest.setPageSize(10000);
                Page<OpenAccountView> openAccountViews = reportService.openAccount(openAccountRequest);
                long t1 = System.currentTimeMillis();
                ExcelUtils.writeExcel(response, openAccountViews, OpenAccountView.class);
                long t2 = System.currentTimeMillis();
                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {
                responseData.setData(reportService.openAccount(openAccountRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * AR账龄分析
     * arAgingAnalysis
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/ar_aging_analysis")
    public ResponseData arAgingAnalysis(@RequestBody ArAgingAnalysisRequest arAgingAnalysisRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (arAgingAnalysisRequest.getExport() != null && arAgingAnalysisRequest.getExport().equals(1)) {
                arAgingAnalysisRequest.setPageNum(1);
                arAgingAnalysisRequest.setPageSize(10000);
                Page<ArAgingAnalysisView> arAgingAnalysisViews = reportService.arAgingAnalysis(arAgingAnalysisRequest);
                long t1 = System.currentTimeMillis();
                ExcelUtils.writeExcel(response, arAgingAnalysisViews, ArAgingAnalysisView.class);
                long t2 = System.currentTimeMillis();
                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {
                responseData.setData(reportService.arAgingAnalysis(arAgingAnalysisRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 收钱吧支付明细报表
     * arAgingAnalysis
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/sqbTransactionReport")
    public ResponseData sqbTransactionReport(@RequestBody SqbTransactionReportRequest sqbTransactionReportRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(reportService.sqbTransactionReport(sqbTransactionReportRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 支付宝支付明细报表
     * arAgingAnalysis
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/alipayFaceTransactionReport")
    public ResponseData alipayFaceTransactionReport(@RequestBody AlipayFaceTransactionReportRequest alipayFaceTransactionReportRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (alipayFaceTransactionReportRequest.getExport() != null && alipayFaceTransactionReportRequest.getExport().equals(1)) {
                alipayFaceTransactionReportRequest.setPageNum(1);
                alipayFaceTransactionReportRequest.setPageSize(10000);
                Page<AlipayFaceTransactionView> alipayFaceTransactionViews = reportService.alipayFaceTransactionReport(alipayFaceTransactionReportRequest);
                long t1 = System.currentTimeMillis();
                ExcelUtils.writeExcel(response, alipayFaceTransactionViews, AlipayFaceTransactionView.class);
                long t2 = System.currentTimeMillis();
                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {
                responseData.setData(reportService.alipayFaceTransactionReport(alipayFaceTransactionReportRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 微信支付明细
     * arAgingAnalysis
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/sysWechatPayReport")
    public ResponseData sysWechatPayReport(@RequestBody SysWechatPayViewReportRequest sysWechatPayViewReportRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (sysWechatPayViewReportRequest.getExport() != null && sysWechatPayViewReportRequest.getExport().equals(1)) {
                sysWechatPayViewReportRequest.setPageNum(1);
                sysWechatPayViewReportRequest.setPageSize(10000);
                Page<SysWechatPayView> sysWechatPayViews = reportService.sysWechatPayReport(sysWechatPayViewReportRequest);
                long t1 = System.currentTimeMillis();
                ExcelUtils.writeExcel(response, sysWechatPayViews, SysWechatPayView.class);
                long t2 = System.currentTimeMillis();
                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {

                responseData.setData(reportService.sysWechatPayReport(sysWechatPayViewReportRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 支付宝退款明细
     * arAgingAnalysis
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/alipayFaceRefundReport")
    public ResponseData alipayFaceRefundReport(@RequestBody AlipayFaceRefundRequest alipayFaceRefundRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (alipayFaceRefundRequest.getExport() != null && alipayFaceRefundRequest.getExport().equals(1)) {
                alipayFaceRefundRequest.setPageNum(1);
                alipayFaceRefundRequest.setPageSize(10000);
                Page<AlipayFaceRefundView> alipayFaceRefundViews = reportService.alipayFaceRefundReport(alipayFaceRefundRequest);
                long t1 = System.currentTimeMillis();
                ExcelUtils.writeExcel(response, alipayFaceRefundViews, AlipayFaceRefundView.class);
                long t2 = System.currentTimeMillis();
                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {
                responseData.setData(reportService.alipayFaceRefundReport(alipayFaceRefundRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/report/sqbRefundReport")
    public ResponseData sqbRefundReport(@RequestBody SqbTransactionReportRequest request, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(reportService.sqbRefundReport(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 微信退款明细
     * arAgingAnalysis
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/sysWechatRefundReport")
    public ResponseData sysWechatRefundReport(@RequestBody SysWechatRefundRequest sysWechatRefundRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (sysWechatRefundRequest.getExport() != null && sysWechatRefundRequest.getExport().equals(1)) {
                sysWechatRefundRequest.setPageNum(1);
                sysWechatRefundRequest.setPageSize(10000);
                Page<SysWechatRefundView> sysWechatRefundViews = reportService.sysWechatRefundReport(sysWechatRefundRequest);
                long t1 = System.currentTimeMillis();
                ExcelUtils.writeExcel(response, sysWechatRefundViews, SysWechatRefundView.class);
                long t2 = System.currentTimeMillis();
                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {
                responseData.setData(reportService.sysWechatRefundReport(sysWechatRefundRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 查询登记表信息
     * arAgingAnalysis
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/getRegistMsgOnCheckInPage")
    public ResponseData getRegistMsgOnCheckInPage(@RequestBody RegistReportRequest registReportRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (registReportRequest.getExport() != null && registReportRequest.getExport().equals(1)) {
                registReportRequest.setPageNum(1);
                registReportRequest.setPageSize(10000);
                Page<Map<String, Object>> registMsgOnCheckInPage = reportService.getRegistMsgOnCheckInPage(registReportRequest);


//                long t1 = System.currentTimeMillis();
//                ExcelUtils.writeExcel(response, sysWechatRefundViews, SysWechatRefundView.class);
//                long t2 = System.currentTimeMillis();
//                log.info(String.format("write over! cost:%sms", (t2 - t1)));
                return null;
            } else {
                responseData.setData(reportService.getRegistMsgOnCheckInPage(registReportRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 查询预订表信息
     * arAgingAnalysis
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/selectBookingOrderReport")
    public ResponseData selectBookingOrderReport(@RequestBody BookingOrderReportRequest bookingOrderReportRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (bookingOrderReportRequest.getExport() != null && bookingOrderReportRequest.getExport().equals(1)) {
                bookingOrderReportRequest.setPageNum(1);
                bookingOrderReportRequest.setPageSize(10000);
                Page<BookingOrderView> bookingOrderViews = reportService.selectBookingOrderReport(bookingOrderReportRequest);
                return null;
            } else {
                responseData.setData(reportService.selectBookingOrderReport(bookingOrderReportRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 查询入住人信息列表
     * arAgingAnalysis
     *
     * @param
     * @return
     */
    @RequestMapping("/hotel/report/selectRegistPersonReport")
    public ResponseData selectRegistPersonReport(@RequestBody RegistPersonReportRequest registPersonReportRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (registPersonReportRequest.getExport() != null && registPersonReportRequest.getExport().equals(1)) {
                registPersonReportRequest.setPageNum(1);
                registPersonReportRequest.setPageSize(10000);
                Page<RegistPersonReportView> registPersonReportViews = reportService.selectRegistPersonReport(registPersonReportRequest);
                return null;
            } else {
                responseData.setData(reportService.selectRegistPersonReport(registPersonReportRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/report/selectCardConsumptionRecordReport")
    public ResponseData selectCardConsumptionRecordReport(@RequestBody CardConsumptionRecordRequest cardConsumptionRecordRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (cardConsumptionRecordRequest.getExport() != null && cardConsumptionRecordRequest.getExport().equals(1)) {
                cardConsumptionRecordRequest.setPageNum(1);
                cardConsumptionRecordRequest.setPageSize(10000);
                Page<CardConsumptionRecordView> cardConsumptionRecordViews = reportService.selectCardConsumptionRecordReport(cardConsumptionRecordRequest);
                return null;
            } else {
                responseData.setData(reportService.selectCardConsumptionRecordReport(cardConsumptionRecordRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/report/selectCardFreezeRecordReport")
    public ResponseData selectCardFreezeRecordReport(@RequestBody CardFreezeRecordRequest cardFreezeRecordRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (cardFreezeRecordRequest.getExport() != null && cardFreezeRecordRequest.getExport().equals(1)) {
                cardFreezeRecordRequest.setPageNum(1);
                cardFreezeRecordRequest.setPageSize(10000);
                Page<CardFreezeRecordView> cardFreezeRecordViews = reportService.selectCardFreezeRecordReport(cardFreezeRecordRequest);
                return null;
            } else {
                responseData.setData(reportService.selectCardFreezeRecordReport(cardFreezeRecordRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/report/selectCardInfoReport")
    public ResponseData selectCardInfoReport(@RequestBody CardInfoRequest cardInfoRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (cardInfoRequest.getExport() != null && cardInfoRequest.getExport().equals(1)) {
                cardInfoRequest.setPageNum(1);
                cardInfoRequest.setPageSize(10000);
                reportService.selectCardInfoReport(cardInfoRequest);
                return null;
            } else {
                responseData.setData(reportService.selectCardInfoReport(cardInfoRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/report/selectCardRechargeReport")
    public ResponseData selectCardRechargeReport(@RequestBody CardRechargeRequest cardRechargeRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (cardRechargeRequest.getExport() != null && cardRechargeRequest.getExport().equals(1)) {
                cardRechargeRequest.setPageNum(1);
                cardRechargeRequest.setPageSize(10000);
                reportService.selectCardRechargeReport(cardRechargeRequest);
                return null;
            } else {
                responseData.setData(reportService.selectCardRechargeReport(cardRechargeRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/report/selectCardRechargeReportSummary")
    public ResponseData selectCardRechargeReportSummary(@RequestBody CardRechargeRequest cardRechargeRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(reportService.selectCardRechargeReportSummary(cardRechargeRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/report/selectCardRechargeCountReport")
    public ResponseData selectCardRechargeCountReport(@RequestBody CardRechargeRequest cardRechargeRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (cardRechargeRequest.getExport() != null && cardRechargeRequest.getExport().equals(1)) {
                cardRechargeRequest.setPageNum(1);
                cardRechargeRequest.setPageSize(10000);
                reportService.selectCardRechargeCountReport(cardRechargeRequest);
                return null;
            } else {
                responseData.setData(reportService.selectCardRechargeCountReport(cardRechargeRequest));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 房型升级或者换房记录表
     *
     * @param registChangeRecordRequest
     * @param response
     * @return
     */
    @RequestMapping("/hotel/report/getRegistChangeRecordReport")
    public ResponseData getRegistChangeRecordReport(@RequestBody RegistChangeRecordRequest registChangeRecordRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (registChangeRecordRequest.getExport() != null && registChangeRecordRequest.getExport().equals(1)) {
                registChangeRecordRequest.setPageNum(1);
                registChangeRecordRequest.setPageSize(10000);
                return null;
            } else {
                responseData.setData(reportService.getRegistChangeRecordReport(registChangeRecordRequest));
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("/hotel/report/getRoomClearRecordReport")
    public ResponseData getRoomClearRecordReport(@RequestBody RoomClearRecordRequest roomClearRecordRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (roomClearRecordRequest.getCreateTimeList() != null) {
                ArrayList<Long> longs = new ArrayList<>();
                roomClearRecordRequest.getCreateTimeList().forEach(t -> {
                    if (t != null) {
                        longs.add(t / 1000);
                    }
                });
                roomClearRecordRequest.setCreateTimeList(longs);
            }
            responseData.setData(reportService.getRoomClearRecordReport(roomClearRecordRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("/hotel/report/getRoomCardRecordReport")
    public ResponseData getRoomCardRecordReport(@RequestBody RoomCardRecordRequest roomCardRecordRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (roomCardRecordRequest.getExport() != null && roomCardRecordRequest.getExport().equals(1)) {
                roomCardRecordRequest.setPageNum(1);
                roomCardRecordRequest.setPageSize(10000);
                return null;
            } else {
                responseData.setData(reportService.getRoomCardRecordReport(roomCardRecordRequest));
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/report/getRoomRepairRecordReport")
    public ResponseData getRoomRepairRecordReport(@RequestBody RoomRepairRecordRequest roomRepairRecordRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (roomRepairRecordRequest.getExport() != null && roomRepairRecordRequest.getExport().equals(1)) {
                roomRepairRecordRequest.setPageNum(1);
                roomRepairRecordRequest.setPageSize(10000);
                return null;
            } else {
                responseData.setData(reportService.getRoomRepairRecordReport(roomRepairRecordRequest));
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/report/getRoomInfoReport")
    public ResponseData getRoomInfoReport(@RequestBody RoomInfoRequest roomInfoRequest, HttpServletResponse response) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (roomInfoRequest.getExport() != null && roomInfoRequest.getExport().equals(1)) {
                roomInfoRequest.setPageNum(1);
                roomInfoRequest.setPageSize(10000);
                return null;
            } else {
                responseData.setData(reportService.getRoomInfoReport(roomInfoRequest));
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/report/selectBySearchAndPeople")
    public ResponseData selectBySearchAndPeople(@RequestBody RegistSearchRequest registSearchRequest) {

        String businessDayMax = registSearchRequest.getBusinessDayMax();
        if (businessDayMax != null && businessDayMax.length() > 1) {
            String replace = businessDayMax.substring(0, 10).replace("-", "");
            registSearchRequest.setBusinessDayMax(replace);
        }

        String businessDayMin = registSearchRequest.getBusinessDayMin();
        if (businessDayMin != null && businessDayMin.length() > 1) {
            String replace = businessDayMin.substring(0, 10).replace("-", "");
            registSearchRequest.setBusinessDayMin(replace);
        }

        String checkoutBusMax = registSearchRequest.getCheckoutBusMax();
        if (checkoutBusMax != null && checkoutBusMax.length() > 1) {
            String replace = checkoutBusMax.substring(0, 10).replace("-", "");
            registSearchRequest.setCheckoutBusMax(replace);
        }

        String checkoutBusMin = registSearchRequest.getCheckoutBusMin();
        if (checkoutBusMin != null && checkoutBusMin.length() > 1) {
            String replace = checkoutBusMin.substring(0, 10).replace("-", "");
            registSearchRequest.setCheckoutBusMin(replace);
        }
        String onCheckBusinessDay = registSearchRequest.getOnCheckBusinessDay();
        if (onCheckBusinessDay != null && onCheckBusinessDay.length() > 1) {
            String replace = onCheckBusinessDay.substring(0, 10).replace("-", "");
            registSearchRequest.setOnCheckBusinessDay(replace);
        }


        return reportService.selectBySearchAndPeople(registSearchRequest);
    }

    @RequestMapping("/hotel/report/selectPeople")
    public ResponseData selectPeople(@RequestBody RegistSearchRequest registSearchRequest) {

        String businessDayMax = registSearchRequest.getBusinessDayMax();
        if (businessDayMax != null && businessDayMax.length() > 1) {
            String replace = businessDayMax.substring(0, 10).replace("-", "");
            registSearchRequest.setBusinessDayMax(replace);
        }

        String businessDayMin = registSearchRequest.getBusinessDayMin();
        if (businessDayMin != null && businessDayMin.length() > 1) {
            String replace = businessDayMin.substring(0, 10).replace("-", "");
            registSearchRequest.setBusinessDayMin(replace);
        }

        String checkoutBusMax = registSearchRequest.getCheckoutBusMax();
        if (checkoutBusMax != null && checkoutBusMax.length() > 1) {
            String replace = checkoutBusMax.substring(0, 10).replace("-", "");
            registSearchRequest.setCheckoutBusMax(replace);
        }

        String checkoutBusMin = registSearchRequest.getCheckoutBusMin();
        if (checkoutBusMin != null && checkoutBusMin.length() > 1) {
            String replace = checkoutBusMin.substring(0, 10).replace("-", "");
            registSearchRequest.setCheckoutBusMin(replace);
        }
        String onCheckBusinessDay = registSearchRequest.getOnCheckBusinessDay();
        if (onCheckBusinessDay != null && onCheckBusinessDay.length() > 1) {
            String replace = onCheckBusinessDay.substring(0, 10).replace("-", "");
            registSearchRequest.setOnCheckBusinessDay(replace);
        }


        return reportService.selectPeople(registSearchRequest);
    }

    @RequestMapping("/hotel/report/selectPeopleAreaCount")
    public ResponseData selectPeopleAreaCount(@RequestBody RegistSearchRequest registSearchRequest) {

        String businessDayMax = registSearchRequest.getBusinessDayMax();
        if (businessDayMax != null && businessDayMax.length() > 1) {
            String replace = businessDayMax.substring(0, 10).replace("-", "");
            registSearchRequest.setBusinessDayMax(replace);
        }

        String businessDayMin = registSearchRequest.getBusinessDayMin();
        if (businessDayMin != null && businessDayMin.length() > 1) {
            String replace = businessDayMin.substring(0, 10).replace("-", "");
            registSearchRequest.setBusinessDayMin(replace);
        }

        String checkoutBusMax = registSearchRequest.getCheckoutBusMax();
        if (checkoutBusMax != null && checkoutBusMax.length() > 1) {
            String replace = checkoutBusMax.substring(0, 10).replace("-", "");
            registSearchRequest.setCheckoutBusMax(replace);
        }

        String checkoutBusMin = registSearchRequest.getCheckoutBusMin();
        if (checkoutBusMin != null && checkoutBusMin.length() > 1) {
            String replace = checkoutBusMin.substring(0, 10).replace("-", "");
            registSearchRequest.setCheckoutBusMin(replace);
        }
        String onCheckBusinessDay = registSearchRequest.getOnCheckBusinessDay();
        if (onCheckBusinessDay != null && onCheckBusinessDay.length() > 1) {
            String replace = onCheckBusinessDay.substring(0, 10).replace("-", "");
            registSearchRequest.setOnCheckBusinessDay(replace);
        }


        return reportService.selectPeopleAreaCount(registSearchRequest);
    }

}
