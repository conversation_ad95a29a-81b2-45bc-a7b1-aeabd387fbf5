package com.pms.czabsorders.web;


import com.pms.czabsorders.bean.RegistParam;
import com.pms.czabsorders.bean.TeamInfo;
import com.pms.czabsorders.bean.WithRoomRequest;
import com.pms.czabsorders.service.checkin.CheckInService;
import com.pms.czabsorders.service.overstay.OverStayService;
import com.pms.czabsorders.service.team.TeamService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;

/**
 * 登记相关业务
 */
@Controller
@Slf4j
@RequestMapping("/hotel/checkin/")
public class CheckInController {



    @Autowired
    private CheckInService checkInService;

    @Autowired
    private TeamService teamService;

    @Autowired
    private OverStayService overStayService;

    /**
     * 散客步入
     * @param param
     * @return
     */
    @RequestMapping("blendCheckIn.do")
    @ResponseBody
    public ResponseData blendCheckIn(@RequestBody JSONObject param) {
        ResponseData responseData = checkInService.blendCheckIn(param);
        return responseData;
    }


    /**
     * 自助机散客步入
     * @param param
     * @return
     */
    @RequestMapping("blendCheckInMachine")
    @ResponseBody
    public JSONObject blendCheckInMachine(@RequestBody JSONObject param) {
        ResponseData responseData = checkInService.blendCheckIn(param);
        return JSONObject.fromObject(responseData);
    }

    /**
     * 设置主客
     * @param param
     * @return
     */
    @RequestMapping("setHostPerson.do")
    @ResponseBody
    public ResponseData setHostPerson(@RequestBody JSONObject param) {
        ResponseData responseData = checkInService.setHostPerson(param);
        return responseData;
    }

    /**
     * 更新登记状态
     * @param param
     * @return
     */
    @RequestMapping("updateRegistState.do")
    @ResponseBody
    public ResponseData updateRegistState(@RequestBody JSONObject param) {
        ResponseData responseData = checkInService.updateRegistState(param);
        return responseData;
    }

    /**
     * 离店
     * @param checkOutRequest
     * @return
     */
    @RequestMapping("checkout.do")
    @ResponseBody
    public ResponseData checkout(@RequestBody CheckOutRequest checkOutRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData = checkInService.checkout(checkOutRequest);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 取消离店
     * @param registParam
     * @return
     */
    @RequestMapping("cancelCheckout.do")
    @ResponseBody
    public ResponseData cancelCheckout(@RequestBody RegistParam registParam) {
        return checkInService.cancelCheckout(registParam);
    }

    /**
     * 更新团队信息
     * @param teamInfo
     * @return
     */
    @RequestMapping("updateTeamInfo.do")
    @ResponseBody
    public ResponseData updateTeamInfo(@RequestBody TeamInfo teamInfo) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData = teamService.updateTeamInfo(teamInfo);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 团队挂账
     * @param teamInfo
     * @return
     */
    @RequestMapping("teamOnAccount.do")
    @ResponseBody
    public ResponseData teamOnAccount(@RequestBody TeamInfo teamInfo) {
        return teamService.teamOnAccount(teamInfo);
    }

    /**
     * 钟点房转日租房
     * @param hourToDayRequest
     * @return
     */
    @RequestMapping("hourToDay.do")
    @ResponseBody
    public ResponseData hourToDay(@RequestBody HourToDayRequest hourToDayRequest) {
        return checkInService.hourToDay(hourToDayRequest);
    }

    /**
     * 更新客源信息
     * @param registParam
     * @return
     */
    @RequestMapping("updateRegistResource.do")
    @ResponseBody
    public ResponseData updateRegistResource(@RequestBody RegistParam registParam) {
        return checkInService.updateRegistResource(registParam);
    }


    /**
     * 查询当前在住的信息
     * @return
     */
    @RequestMapping("searchCheckinDetails.do")
    @ResponseBody
    public Map<String, Object> searchCheckinDetails(@RequestBody JSONObject map) {
        return checkInService.searchCheckinDetails(map);
    }


    /**
     * 预定转入住
     *
     * @param param
     * @return
     */
    @RequestMapping("bookingCheckIn.do")
    @ResponseBody
    public ResponseData bookingCheckIn(@RequestBody JSONObject param) {
        return checkInService.bookingCheckIn(param);
    }


    /**
     * 预定转入住-钟点房
     *
     * @param bookingOrderCheckInRequest
     * @return
     */
    @RequestMapping("bookingOrderCheckIn.do")
    @ResponseBody
    public ResponseData bookingOrderCheckIn(@RequestBody BookingOrderCheckInRequest bookingOrderCheckInRequest) {
        return checkInService.bookingOrderCheckIn(bookingOrderCheckInRequest);
    }


    /**
     * 重新入住
     *
     * @param param
     * @return
     */
    @RequestMapping("aginCheckin.do")
    @ResponseBody
    public ResponseData aginCheckin(@RequestBody JSONObject param) {
        return checkInService.aginCheckin(param);
    }


    /**
     * 查询当前团队在住的信息
     *
     * @param param
     * @return
     */
    @RequestMapping("relieveTeam.do")
    @ResponseBody
    public Map<String, Object> relieveTeam(@RequestBody JSONObject param) {
        return teamService.relieveTeam(param);
    }


    /**
     * 修改在住宾客信息
     *
     * @param param
     * @return
     */
    @RequestMapping("updateGuest.do")
    @ResponseBody
    public Map<String, Object> updateGuest(@RequestBody JSONObject param) {
        return checkInService.updateGuest(param);
    }


    /**
     * 换房业务
     *
     * @param param
     * @return
     */
    @RequestMapping("changeRoom.do")
    @ResponseBody
    public ResponseData changeRoom(@RequestBody JSONObject param) {
        return checkInService.changeRoom(param);
    }

    /**
     * 房型升级
     *
     * @param param
     * @return
     */
    @RequestMapping("updateRoomType.do")
    @ResponseBody
    public ResponseData updateRoomType(@RequestBody JSONObject param) {
        return checkInService.updateRoomType(param);
    }

    /**
     * 续住
     *
     * @param param
     * @return
     */
    @RequestMapping("overStay.do")
    @ResponseBody
    public ResponseData overStay(@RequestBody JSONObject param) {
        return overStayService.overStay(param);
    }

    /**
     * 续住1
     *
     * @param param
     * @return
     */
    @RequestMapping("overStayTwo.do")
    @ResponseBody
    public ResponseData overStayNew(@RequestBody OverStayRequest param) {
        return overStayService.overStayNew(param);
    }

    /**
     * 续住2
     *
     * @param param
     * @return
     */
    @RequestMapping("overHourStay.do")
    @ResponseBody
    public ResponseData overHourStay(@RequestBody OverStayRequest param) {
        return overStayService.overHourStay(param);
    }


    /**
     * 续住3
     *
     * @param param
     * @return
     */
    @RequestMapping("overStayList.do")
    @ResponseBody
    public ResponseData overStayList(@RequestBody OverStayListRequest param) {
        return overStayService.overStayList(param);
    }

    /**
     * 换单续住
     *
     * @param param
     * @return
     */
    @RequestMapping("overStayOrder.do")
    @ResponseBody
    public ResponseData overStayOrder(@RequestBody OverStayOrderRequest param) {
        return overStayService.overStayOrder(param);
    }


    /**
     * 续住-自助机
     *
     * @param param
     * @return
     */
    @RequestMapping("overStayForMachine.do")
    @ResponseBody
    public Map<String, Object> overStayForMachine(@RequestBody JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        resultMap.put(ER.MSG, ER.SUCC);
        ResponseData responseData = overStayService.overStay(param);
        if (responseData.getCode() != 1) {
            resultMap.put(ER.RES, ER.ERR);
        }
        return resultMap;
    }

    /**
     * 修改主单信息
     *
     * @param param
     * @return
     */
    @RequestMapping("updateRegistInfo.do")
    @ResponseBody
    public ResponseData updateRegistInfo(@RequestBody JSONObject param) {
        return checkInService.updateRegistInfo(param);
    }

    /**
     * 联房或者合并
     *
     * @param param
     * @return
     */
    @RequestMapping("teamCombine.do")
    @ResponseBody
    public ResponseData teamCombine(@RequestBody JSONObject param) {
        return checkInService.teamCombine(param);
    }

    /**
     * 设置主账房
     * @param param
     * @return
     */
    @RequestMapping("setMainRoom.do")
    @ResponseBody
    public ResponseData setMainRoom(@RequestBody JSONObject param) {
        return teamService.setMainRoom(param);
    }

    /**
     * 取消入住
     * @param param
     * @return
     */
    @RequestMapping("cancelCheckin.do")
    @ResponseBody
    public ResponseData cancelCheckin(@RequestBody RegistParam param) {
        return checkInService.cancelCheckin(param);
    }

    /**
     * 更新入住类型
     * @param param
     * @return
     */
    @RequestMapping("updateCheckinType.do")
    @ResponseBody
    public ResponseData updateCheckinType(@RequestBody RegistParam param) {
        return checkInService.updateCheckinType(param);
    }


    /**
     * 新的联房的方法
     *
     * @param withRoomRequest
     * @return
     */
    @RequestMapping("withRoom.do")
    @ResponseBody
    public ResponseData withRoom(@RequestBody WithRoomRequest withRoomRequest) {
        return checkInService.withRoom(withRoomRequest);
    }

    /**
     * 上传入住人的现场照片
     *
     * @param upLoadPersonImageRequest
     * @return
     */
    @RequestMapping("upLoadPersonImage.do")
    @ResponseBody
    public ResponseData upLoadPersonImage(@RequestBody UpLoadPersonImageRequest upLoadPersonImageRequest) {
        return checkInService.upLoadPersonImage(upLoadPersonImageRequest);
    }

    /**
     * 获取上一次离店信息
     * @param getLastCheckoutRegistRequest
     * @return
     */
    @RequestMapping("getLastCheckoutRegist.do")
    @ResponseBody
    public ResponseData getLastCheckoutRegist(@RequestBody GetLastCheckoutRegistRequest getLastCheckoutRegistRequest) {
        return checkInService.getLastCheckoutRegist(getLastCheckoutRegistRequest);
    }
}
