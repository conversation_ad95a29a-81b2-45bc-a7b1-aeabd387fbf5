package com.pms.czabsorders.web.account;


import com.pms.czaccount.service.alipay.AliPayService;
import com.pms.czaccount.service.wechat.WeChatPayService;
import com.pms.czpmsutils.GsonUtil;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.WxUtils;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.RefundMoneyRequest;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Controller
@RequestMapping("/hotel/pay/")
@Slf4j
public class PayController {
    public static final String WxNotifyFail =
            "<xml>\n" +
                    "  <return_code><![CDATA[FAIL]]></return_code>\n" +
                    "  <return_msg><![CDATA[]]></return_msg>\n" +
                    "</xml>";


    public static final String WxNotifySuccess =
            "<xml>\n" +
                    "  <return_code><![CDATA[SUCCESS]]></return_code>\n" +
                    "  <return_msg><![CDATA[OK]]></return_msg>\n" +
                    "</xml>";


    @Autowired
    private WeChatPayService weChatPayService;

    private WxUtils wxUtils = new WxUtils();

    @Autowired
    private AliPayService aliPayService;

    @RequestMapping("weChatPayNotify.do")
    @ResponseBody
    public String weChatPayNotify(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 预先设定返回的 response 类型为 xml
            response.setHeader("Content-type", "application/xml");
            // 读取参数，解析Xml为map
            Map<String, String> map = wxUtils.transferXmlToMap(wxUtils.readRequest(request));
            log.info("weChatPayNotify param: " + GsonUtil.bean2Json(map));
            // 签名校验成功，说明是微信服务器发出的数据
            String out_trade_no = map.get("out_trade_no");
            // 可在此持久化微信传回的该 map 数据
            if (map.get("return_code").equals("SUCCESS")) {
                if (map.get("result_code").equals("SUCCESS")) {
                    /**
                     {
                     "hid": 2126,
                     "payType": 1,
                     "money": 0.01,
                     "mainID": 362548,
                     "sessionToken": "7385BABF0CD4417196FE5FA9F26546D0",
                     "mainId": "ALI2126DA9E1F6B879E4D9DA2CB5A9D8",
                     "isQr": "1"
                     }
                     */
                    JSONObject payMsg = new JSONObject();
                    payMsg.put("payType", 2);
                    payMsg.put("mainId", out_trade_no);
                    payMsg.put(ER.SESSION_TOKEN, stringRedisTemplate.opsForValue().get("SESSION_TOKEN:" + out_trade_no));
                    getPayResult(payMsg);
                } else {
                    return WxNotifyFail;
                }
            }
            return WxNotifySuccess;
        } catch (Exception e) {
            log.error("",e);
            // 签名校验失败（可能不是微信服务器发出的数据）
            return WxNotifyFail;
        }
    }


    @RequestMapping("aliPayNotify.do")
    @ResponseBody
    public void aliPayNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        PrintWriter out = response.getWriter();
        try {

            //获取支付宝POST过来反馈信息
            Map<String, String> params = new HashMap<String, String>();
            Map requestParams = request.getParameterMap();
            for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
                String name = (String) iter.next();
                String[] values = (String[]) requestParams.get(name);
                String valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i]
                            : valueStr + values[i] + ",";
                }
                //乱码解决，这段代码在出现乱码时使用。
                //valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
                params.put(name, valueStr);
            }
            log.info("aliPayNotify param: " + GsonUtil.bean2Json(params));
//            if (true){
//                out.print("success");
//                return;
//            }


//异步验签：切记alipaypublickey是支付宝的公钥，请去open.alipay.com对应应用下查看。
//公钥证书模式验签，alipayPublicCertPath是支付宝公钥证书引用路径地址，需在对应应用中下载
//boolean signVerified= AlipaySignature.rsaCertCheckV1(params, alipayPublicCertPath, charset,sign_type);
//普通公钥模式验签，切记alipaypublickey是支付宝的公钥，请去open.alipay.com对应应用下查看。
            //  boolean flag = AlipaySignature.rsaCheckV1(params, alipaypublicKey, charset,"RSA2");
//以字符方式输出


/* 实际验证过程建议商户务必添加以下校验：
	1、需要验证该通知数据中的out_trade_no是否为商户系统中创建的订单号，
	2、判断total_amount是否确实为该订单的实际金额（即商户订单创建时的金额），
	3、校验通知中的seller_id（或者seller_email) 是否为out_trade_no这笔单据的对应的操作方（有的时候，一个商户可能有多个seller_id/seller_email）
	4、验证app_id是否为该商户本身。
	*/

            //商户订单号
            String out_trade_no = new String(request.getParameter("out_trade_no").getBytes("ISO-8859-1"), "UTF-8");

            //支付宝交易号
            //String trade_no = new String(request.getParameter("trade_no").getBytes("ISO-8859-1"), "UTF-8");

            //交易状态
            String trade_status = new String(request.getParameter("trade_status").getBytes("ISO-8859-1"), "UTF-8");

            if (trade_status.equals("TRADE_FINISHED")) {
                //判断该笔订单是否在商户网站中已经做过处理
                //如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
                //如果有做过处理，不执行商户的业务程序

                //注意：
                //退款日期超过可退款期限后（如三个月可退款），支付宝系统发送该交易状态通知
            } else if (trade_status.equals("TRADE_SUCCESS")) {
                //判断该笔订单是否在商户网站中已经做过处理
                //如果没有做过处理，根据订单号（out_trade_no）在商户网站的订单系统中查到该笔订单的详细，并执行商户的业务程序
                //如果有做过处理，不执行商户的业务程序
                /**
                 {
                 "hid": 2126,
                 "payType": 1,
                 "money": 0.01,
                 "mainID": 362548,
                 "sessionToken": "7385BABF0CD4417196FE5FA9F26546D0",
                 "mainId": "ALI2126DA9E1F6B879E4D9DA2CB5A9D8",
                 "isQr": "1"
                 }
                 */

                JSONObject payMsg = new JSONObject();
                payMsg.put("payType", 1);
                payMsg.put("mainId", out_trade_no);
                payMsg.put(ER.SESSION_TOKEN, stringRedisTemplate.opsForValue().get("SESSION_TOKEN:" + out_trade_no));
                getPayResult(payMsg);
                //注意：
                //付款完成后，支付宝系统发送该交易状态通知
            }
            out.print("success");
        } catch (Exception e) {
            log.error("",e);
            out.print("fail");
        }
    }

    @RequestMapping("sqbPayNotify.do")
    @ResponseBody
    public void sqbPayNotify(HttpServletRequest request, HttpServletResponse response) throws Exception {
        PrintWriter out = response.getWriter();
        try {
            String body = FileCopyUtils.copyToString(new InputStreamReader(request.getInputStream()));
            log.info("sqbPayNotify body: " + body);
            JSONObject data = JSONObject.fromObject(body);
            if (Objects.equals("PAID", data.get("order_status")) && Objects.equals("SUCCESS", data.get("status"))) {
                JSONObject payMsg = new JSONObject();
                int payType = 1;
                if (Objects.equals("3", data.get("payway"))) {
                    payType = 2;
                }
                payMsg.put("payType", payType);
                Object out_trade_no = data.getOrDefault("client_sn", "");
                payMsg.put("mainId", out_trade_no);
                payMsg.put("isQr", stringRedisTemplate.opsForValue().get("IS_QR:" + out_trade_no));
                payMsg.put("sn", data.getOrDefault("sn", ""));
                payMsg.put(ER.SESSION_TOKEN, stringRedisTemplate.opsForValue().get("SESSION_TOKEN:" + out_trade_no));
                getPayResult(payMsg);
            }
            out.print("success");
        } catch (Exception e) {
            log.error("",e);
            out.print("fail");
        }
    }

    /**
     * 获取微信二维码信息
     *
     * @param payMsg pub
     *               参数: money 单位元
     * @return
     */
    @RequestMapping("getPayQrcode.do")
    @ResponseBody
    public Map<String, Object> getWxQrcode(@RequestBody JSONObject payMsg) {
        //   JSONObject payMsg = JSONObject.fromObject(request.getAttribute(ER.PARAM_MAP));
        Map rt = null;
        int payType = payMsg.getInt("payType");
        if (payType == 2) {
            rt = weChatPayService.getWeChatQrcode(payMsg);
        } else {
            rt = aliPayService.getAlipayQrCode(payMsg);
        }
        if (rt != null && payMsg.containsKey("addAccountData")) {
            Object mainId = rt.get("mainId");
            if (mainId != null) {
                stringRedisTemplate.opsForValue().set("ADD_ACCOUNT_DATA:" + mainId, payMsg.getString("addAccountData"), 60L, TimeUnit.MINUTES);
             }
        }
        return rt;

    }

    /**
     * 获取微信二维码信息
     *
     * @param payMsg pub
     *               参数: money 单位元
     * @return
     */
    @RequestMapping("getPayToSystemQrcode.do")
    @ResponseBody
    public Map<String, Object> getPayToSystemQrcode(@RequestBody JSONObject payMsg) {
        //   JSONObject payMsg = JSONObject.fromObject(request.getAttribute(ER.PARAM_MAP));
        int payType = payMsg.getInt("payType");
        payMsg.put("isPay2PMSSystem", true);
        if (payType == 2) {
            return weChatPayService.getWeChatQrcode(payMsg);
        } else {
            return aliPayService.getAlipayQrCode(payMsg);
        }
    }


    /**
     * 获取缓存方法的对象
     */
    @Resource(name = "stringRedisTemplate")
    protected StringRedisTemplate stringRedisTemplate;


    /**
     * 获取支付结果
     *
     * @param payMsg 参数: money 单位元
     * @return
     */
    @RequestMapping("getPayToSystemResult.do")
    @ResponseBody
    public Map<String, Object> getPayToSystemResult(@RequestBody JSONObject payMsg) {
        //  JSONObject payMsg = JSONObject.fromObject(request.getAttribute(ER.PARAM_MAP));
        int payType = payMsg.getInt("payType");
        payMsg.put("isPay2PMSSystem", true);
        Map<String, Object> rt = new HashMap<>();
        if (payType == 2) {
            rt = weChatPayService.handleWeChatPayResult(payMsg);
        } else {
            rt = aliPayService.handleAliPayResult(payMsg);
        }

        //支付成功 将mainId放入Redis 半小时
        if (rt.getOrDefault(ER.RES, "").equals(ER.SUCC)) {
            String mainId = rt.getOrDefault("mainId", "").toString();
            String money = rt.getOrDefault("money", "").toString();
            mainId = String.format("payMainId:%s", mainId);
            stringRedisTemplate.opsForValue().set(mainId, money, 30L, TimeUnit.MINUTES);
        }

        return rt;
    }


    /**
     * 获取支付结果
     *
     * @param payMsg 参数: money 单位元
     * @return
     */
    @RequestMapping("getPayResult.do")
    @ResponseBody
    public Map<String, Object> getPayResult(@RequestBody JSONObject payMsg) {
        //  JSONObject payMsg = JSONObject.fromObject(request.getAttribute(ER.PARAM_MAP));
        int payType = payMsg.getInt("payType");
        Map<String, Object> rt = new HashMap<>();
        if (payType == 2) {
            rt = weChatPayService.handleWeChatPayResult(payMsg);
        } else {
            rt = aliPayService.handleAliPayResult(payMsg);
        }

        //支付成功 将mainId放入Redis 半小时
        if (rt.getOrDefault(ER.RES, "").equals(ER.SUCC)) {
            String mainId = rt.getOrDefault("mainId", "").toString();
            String money = rt.getOrDefault("money", "").toString();
            stringRedisTemplate.opsForValue().set(String.format("payMainId:%s", mainId), money, 30L, TimeUnit.MINUTES);

            //支付成功,调用添加账务
            String addAccountData = stringRedisTemplate.opsForValue().get("ADD_ACCOUNT_DATA:" + mainId);
            if (Strings.isNotBlank(addAccountData)) {
                rt = weChatPayService.addAccount(addAccountData, payMsg.getString(ER.SESSION_TOKEN), mainId);
                log.info("addAccount rt:" + rt);
            }
        }

        return rt;
    }


    /**
     * 反扫
     *
     * @param payMsg 参数: money 单位元
     * @return
     */
    @RequestMapping("micropay.do")
    @ResponseBody
    public Map<String, Object> micropay(@RequestBody JSONObject payMsg) {
        // JSONObject payMsg = JSONObject.fromObject(request.getAttribute(ER.PARAM_MAP));
        Map rt = null;
        int payType = payMsg.getInt("payType");
        if (payType == 2) {
            rt = weChatPayService.weChatMicropay(payMsg);
        } else {
            rt = aliPayService.alipayMicropay(payMsg);
        }
        if (rt != null && payMsg.containsKey("addAccountData")) {
            Object mainId = rt.get("mainId");
            if (mainId != null) {
                stringRedisTemplate.opsForValue().set("ADD_ACCOUNT_DATA:" + mainId, payMsg.getString("addAccountData"), 60L, TimeUnit.MINUTES);
            }
        }
        return rt;
    }

    @RequestMapping("refundMoneyFunc")
    @ResponseBody
    public ResponseData refundMoneyFunc(@RequestBody RefundMoneyRequest refundMoneyRequest) {
        Integer payType = refundMoneyRequest.getType();
        HashMap<String, Object> re = new HashMap<>();
        re.put("mainId", refundMoneyRequest.getMainId());
        re.put("refundMoney", refundMoneyRequest.getRefundMoney());
        re.put(ER.SESSION_TOKEN, refundMoneyRequest.getSessionToken());
        Map<String, Object> stringObjectMap = new HashMap<>();
        if (payType == 2) {
            stringObjectMap = weChatPayService.wechatRefund(re);
        } else {
            stringObjectMap = aliPayService.alipayRefund(re);
        }
        ResponseData responseData = new ResponseData();
        responseData.setResult(stringObjectMap.get(ER.RES).toString());
        responseData.setMsg(stringObjectMap.get(ER.MSG).toString());


        return responseData;
    }

    /**
     * 关闭交易
     *
     * @param payMsg 参数: money 单位元
     * @return
     */
    @RequestMapping("payClose.do")
    @ResponseBody
    public Map<String, Object> payClose(@RequestBody JSONObject payMsg) {
        //  JSONObject payMsg = JSONObject.fromObject(request.getAttribute(ER.PARAM_MAP));
        int payType = payMsg.getInt("payType");
        if (payType == 2) {
            return weChatPayService.weChatPayClose(payMsg);
        } else {
            return aliPayService.alipayClose(payMsg);
        }
    }
}
