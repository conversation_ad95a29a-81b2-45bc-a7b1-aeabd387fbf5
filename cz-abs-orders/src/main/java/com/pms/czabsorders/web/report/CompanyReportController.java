package com.pms.czabsorders.web.report;

import com.pms.czpmsutils.ResponseData;
import com.pms.czreport.bean.search.HotelCompanyArRecodeSearch;
import com.pms.czreport.service.impl.CompanyReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/hotel/report/")
public class CompanyReportController {

    @Autowired
    private CompanyReportService companyReportService;

    @RequestMapping("selectArDateAccount.do")
    public ResponseData selectArDateAccount(@RequestBody HotelCompanyArRecodeSearch hotelCompanyArRecodeSearch) {
        return  companyReportService.selectArDateAccount(hotelCompanyArRecodeSearch);
    }


}
