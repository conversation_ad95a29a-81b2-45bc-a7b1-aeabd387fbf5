package com.pms.czabsorders.web.hotel;


import com.pms.czhotelfoundation.bean.search.NoCardHotelBuyRecordSearch;
import com.pms.czhotelfoundation.bean.search.NoCardHotelCountSearch;
import com.pms.czhotelfoundation.bean.search.NoCardHotelSendRecordSearch;
import com.pms.czhotelfoundation.service.NoCardService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.NoCardHotelBuyRecordRequest;
import com.pms.czpmsutils.request.NoCardHotelCountRequest;
import com.pms.czpmsutils.request.NoCardRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;


/**
 * 无证对比相关业务
 */
@Controller
@RequestMapping("/hotel/noCard/")
@Slf4j
public class NoCardController {
    @Resource
    private NoCardService noCardService;

    /**
     * 获取无证购买记录
     * @param request
     * @return
     */
    @RequestMapping("getCardHotelBuyRecord.do")
    @ResponseBody
    public ResponseData getCardHotelBuyRecord(@RequestBody NoCardHotelBuyRecordSearch request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(noCardService.getCardHotelBuyRecord(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 添加无证购买记录
     * @param noCardHotelBuyRecordRequest
     * @return
     */
    @RequestMapping("addNoCardHotelBuyRecord.do")
    @ResponseBody
    public ResponseData addNoCardHotelBuyRecord(@RequestBody NoCardHotelBuyRecordRequest noCardHotelBuyRecordRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(noCardService.addNoCardHotelBuyRecord(noCardHotelBuyRecordRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 获取无证总条数
     * @param request
     * @return
     */
    @RequestMapping("getNoCardHotelCount.do")
    @ResponseBody
    public ResponseData getNoCardHotelCount(@RequestBody NoCardHotelCountSearch request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(noCardService.getNoCardHotelCount(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 更新无证总条数
     * @param noCardHotelCountRequest
     * @return
     */
    @RequestMapping("updateNoCardHotelCount.do")
    @ResponseBody
    public ResponseData updateNoCardHotelCount(@RequestBody NoCardHotelCountRequest noCardHotelCountRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(noCardService.updateNoCardHotelCount(noCardHotelCountRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 获取无证发送记录
     * @param request
     * @return
     */
    @RequestMapping("getNoCardHotelSendRecord.do")
    @ResponseBody
    public ResponseData getNoCardHotelSendRecord(@RequestBody NoCardHotelSendRecordSearch request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(noCardService.getNoCardHotelSendRecord(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 无证操作
     * @param param
     * @return
     */
    @RequestMapping("noCard.do")
    @ResponseBody
    public ResponseData noCard(@RequestBody NoCardRequest param){
        return noCardService.noCard(param);
    }

}
