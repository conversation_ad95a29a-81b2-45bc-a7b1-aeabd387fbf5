package com.pms.czabsorders.web;


import com.pms.czabsorders.service.order.OrderService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.BookingRequest;
import com.pms.pmsorder.bean.request.OtaHotelInfoRequest;
import com.pms.pmsorder.bean.request.OtaRoomTypeRequest;
import com.pms.pmsorder.bean.search.OtaHotelInfoSearch;
import com.pms.pmsorder.bean.search.OtaRoomTypeSearch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/hotel/absorder/")
@Slf4j
public class OrderController2 {

    @Autowired
    private OrderService orderService;

    @RequestMapping("savePersonForBookingRoom.do")
    public ResponseData savePersonForBookingRoom(@RequestBody BookingRequest bookingRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(orderService.addPersonForBookingRoom2(bookingRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("delPersonForBookingRoom.do")
    public ResponseData delPersonForBookingRoom(@RequestBody BookingRequest bookingRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            orderService.delPersonForBookingRoom(bookingRequest);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("updateOtaRoomType.do")
    public ResponseData updateOtaRoomType(@RequestBody OtaRoomTypeRequest otaRoomTypeRequest) {
        return orderService.updateOtaRoomType(otaRoomTypeRequest);
    }

    @RequestMapping("searchOtaRoomType.do")
    public ResponseData searchOtaRoomType(@RequestBody OtaRoomTypeSearch otaRoomTypeSearch) {
        return orderService.searchOtaRoomType(otaRoomTypeSearch);
    }

    @RequestMapping("searchOtaHotelInfo.do")
    public ResponseData searchOtaHotelInfo(@RequestBody OtaHotelInfoSearch otaHotelInfoSearch) {
        return orderService.searchOtaHotelInfo(otaHotelInfoSearch);
    }

    @RequestMapping("updateOtaHotelInfo.do")
    public ResponseData updateOtaHotelInfo(@RequestBody OtaHotelInfoRequest otaHotelInfoRequest) {
        return orderService.updateOtaHotelInfo(otaHotelInfoRequest);
    }


}
