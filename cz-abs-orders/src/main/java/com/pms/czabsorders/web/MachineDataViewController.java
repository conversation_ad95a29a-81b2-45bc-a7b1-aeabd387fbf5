package com.pms.czabsorders.web;


import com.pms.czabsorders.service.machine.MachineDataViewService;
import com.pms.czabsorders.service.machine.MachineHotelService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.pmsorder.bean.machine.Hotel;
import com.pms.pmsorder.bean.machine.view.BookingOrderRequest;
import com.pms.pmsorder.bean.machine.view.GetDayCheckinInfoRequest;
import com.pms.pmsorder.bean.machine.view.MachineUserInfoRequest;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 运维后台相关数据接口
 */
@Controller
@RequestMapping("/hotel/machine/")
@Slf4j
public class MachineDataViewController {


    @Resource
    private MachineDataViewService machineDataViewService;

    @Resource
    private MachineHotelService machineHotelService;

    /**
     * 查询酒店下所有自助机
     *
     * @param request
     * @return
     */
    @RequestMapping("getMachineUserInfo.do")
    @ResponseBody
    public ResponseData getMachineUserInfo(@RequestBody MachineUserInfoRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(machineDataViewService.getMachineUserInfo(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("getMachinePayInfo.do")
    @ResponseBody
    public ResponseData getMachinePayInfo(@RequestBody MachineUserInfoRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(machineDataViewService.getMachinePayInfo(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("getResourceInfo.do")
    @ResponseBody
    public ResponseData getResourceInfo(@RequestBody MachineUserInfoRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(machineDataViewService.getResourceInfo(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("getHotelMachineUserRate.do")
    @ResponseBody
    public ResponseData getHotelMachineUserRate(@RequestBody MachineUserInfoRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(machineDataViewService.getHotelMachineUserRate(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("getHotelMachinePayCountInfo.do")
    @ResponseBody
    public ResponseData getHotelMachinePayCountInfo(@RequestBody MachineUserInfoRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(machineDataViewService.getHotelMachinePayCountInfo(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("getPayCount.do")
    @ResponseBody
    public ResponseData getPayCount(@RequestBody MachineUserInfoRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(machineDataViewService.getPayCount(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("getHotelMachineTotalUseCount.do")
    @ResponseBody
    public ResponseData getHotelMachineTotalUseCount(@RequestBody MachineUserInfoRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(machineDataViewService.getHotelMachineTotalUseCount(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("getHotelMachineTotalPayCount.do")
    @ResponseBody
    public ResponseData getHotelMachineTotalPayCount(@RequestBody MachineUserInfoRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(machineDataViewService.getHotelMachineTotalPayCount(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("getRegistInfo.do")
    @ResponseBody
    public ResponseData getRegistInfo(@RequestBody MachineUserInfoRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(machineDataViewService.getRegistInfo(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("getHotelList.do")
    @ResponseBody
    /**
     * 无参
     */
    public ResponseData getHotelList(@RequestBody JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Map<Integer, Hotel> allHotelCache = machineHotelService.getAllHotelCache();
            responseData.setData(allHotelCache);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("getDayCheckinInfo.do")
    @ResponseBody
    public ResponseData getDayCheckinInfo(@RequestBody GetDayCheckinInfoRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(machineDataViewService.getDayCheckinInfo(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 运维后台查询小程序订单
     * @param request
     * @return
     */
    @RequestMapping("getHotelBookingOrder.do")
    @ResponseBody
    public ResponseData getHotelBookingOrder(@RequestBody BookingOrderRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(machineDataViewService.getHotelBookingOrder(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
