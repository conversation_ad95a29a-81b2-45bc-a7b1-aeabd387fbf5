package com.pms.czabsorders.web.member.report;


import com.pms.czmembership.service.report.MemberReportService;
import com.pms.czpmsutils.ResponseData;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/memberrep/")
public class MemberReportController {

    @Autowired
    private MemberReportService memberReportService;

    /**
     * 查询会员交接班信息
     * @param param
     * @return
     */
    @RequestMapping("searchVipOrCompanyJJBMoney.do")
    @ResponseBody
    public ResponseData searchVipOrCompanyJJ<PERSON><PERSON>(@RequestBody JSONObject param){
        return memberReportService.searchVipOrCompany<PERSON><PERSON><PERSON><PERSON>(param);
    }

}
