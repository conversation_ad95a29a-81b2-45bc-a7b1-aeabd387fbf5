package com.pms.czabsorders.web.oprecord;

import com.pms.czpmsoprecord.bean.OprecordTemplate;
import com.pms.czpmsoprecord.bean.search.OprecordTemplateSearch;
import com.pms.czpmsoprecord.service.OprecordService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.oprecord.search.OprecordSearch;
import com.pms.czpmsutils.constant.user.OprecordInfoRequest;
import com.pms.czpmsutils.request.ApplyHotelRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@Slf4j
public class OprecordController {


    @Autowired
    public OprecordService oprecordService;

    @RequestMapping("/hotel/oprecord/saveOprecord")
    public void saveOprecord(@RequestBody ArrayList<Oprecord> oprecords) {
        oprecordService.saveOprecord(oprecords);
    }

    @RequestMapping("/hotel/oprecord/saveOprecordList")
    public void saveOprecordList(@RequestBody ArrayList<OprecordInfoRequest> oprecords) {
        oprecordService.saveOprecordList(oprecords);
    }

    @RequestMapping("/hotel/oprecord/applyHotel")
    public ResponseData applyHotel(@RequestBody ApplyHotelRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            //4.添加一个表pms-oprecord ,已 oprecard_hid 已名称为表明，复制原有的oprecord表
            oprecordService.applyHotel(request);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }

    @RequestMapping("/hotel/oprecord/searchOprecord")
    public ResponseData searchOprecord(@RequestBody OprecordSearch oprecordSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<Oprecord> oprecords = oprecordService.selectBySearch(oprecordSearch);
            responseData.setData(oprecords);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/oprecord/listOprecordTemplate")
    public ResponseData listOprecordTemplate(@RequestBody OprecordTemplateSearch oprecordTemplateSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(oprecordService.listOprecordTemplate(oprecordTemplateSearch));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/oprecord/addrUpdateTemplate")
    public ResponseData addrUpdateTemplate(@RequestBody OprecordTemplate  oprecordTemplate) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
             oprecordService.addrUpdateTemplate(oprecordTemplate);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/oprecord/addOprecordInfo")
    public ResponseData addOprecordInfo(@RequestBody OprecordInfoRequest req) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            oprecordService.addOprecordInfo(req);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("/hotel/oprecord/addOprecordInfoList")
    public ResponseData addOprecordInfoList(@RequestBody List<OprecordInfoRequest> reqs) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            oprecordService.addOprecordInfoList(reqs);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


}
