package com.pms.czabsorders.web.mini;


import com.pms.czabsorders.bean.mini.UpdateWxMsg;
import com.pms.czabsorders.bean.mini.VipOrderPayRequest;
import com.pms.czabsorders.bean.mini.WechatPhoneParam;
import com.pms.czabsorders.bean.mini.WxShoppingCardRequest;
import com.pms.czabsorders.service.mini.WxMemberService;
import com.pms.czabsorders.service.mini.WxMiniprogramsService;
import com.pms.czabsorders.service.mini.WxOrderService;
import com.pms.czmembership.bean.member.search.CardGroupUrlSearch;
import com.pms.czmembership.bean.member.search.CardRechargePlanDetailsSearch;
import com.pms.czmembership.bean.member.search.CardRechargeSearch;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.*;
import com.pms.pmsorder.bean.search.BookingOrderRoomNumSearch;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

@Controller
@RequestMapping("/hotel/minipro/")
public class MiniprogramsController {

    @Autowired
    private WxMiniprogramsService wxMiniprogramsService;

    @Autowired
    private WxMemberService wxMemberService;

    @Autowired
    private WxOrderService wxOrderService;

    /**
     * 获取openid
     *
     * @param param
     * @return
     */
    @RequestMapping("jscode2session.do")
    @ResponseBody
    public ResponseData jscode2session(@RequestBody JSONObject param) {
        return wxMiniprogramsService.jscode2session(param);
    }

    /**
     * 获取openid
     *
     * @param param
     * @return
     */
    @RequestMapping("getWxUnifiedorder.do")
    @ResponseBody
    public ResponseData getWxUnifiedorder(@RequestBody JSONObject param, HttpServletRequest request) {
        param.put("IP", request.getRemoteAddr());
        return wxMiniprogramsService.getWxUnifiedorder(param);
    }

    /**
     * 获取openid
     *
     * @return
     */
    @RequestMapping("getWeChatPayResult.do")
    @ResponseBody
    public ResponseData getWeChatPayResult(@RequestBody JSONObject param) {
        return wxMiniprogramsService.getWeChatPayResult(param);
    }

    /**
     * 根据openId查询微信信息
     *
     * @param request
     * @return
     */
    @RequestMapping("findWxVip.do")
    @ResponseBody
    public ResponseData findWxVip(@RequestBody JSONObject param, HttpServletRequest servletRequest) {
        String header = servletRequest.getHeader("X-Token");
        param.put(ER.SESSION_TOKEN, header);
        ResponseData wxVip = wxMemberService.findWxVip(param);
        return wxVip;
    }

    /**
     * 微信注册
     *
     * @param request
     * @return
     */
    @RequestMapping("registWxVip.do")
    @ResponseBody
    public ResponseData registWxVip(@RequestBody JSONObject param) {
        return wxMemberService.registWxVip(param);
    }

    /**
     * 微信注册
     *
     * @param param
     * @return
     */
    @RequestMapping("getWxPhone.do")
    @ResponseBody
    public ResponseData getWxPhone(@RequestBody WechatPhoneParam param){
        return wxMemberService.getWxPhone(param);
    }
    /**
     * 微信注册
     * @param param
     * @return
     */
    @RequestMapping("addWxBook.do")
    @ResponseBody
    public ResponseData addBook(@RequestBody JSONObject param) {
        return wxOrderService.addBook(param);
    }

    /**
     * 微信注册
     * @param param
     * @return
     */
    @RequestMapping("stayHours.do")
    @ResponseBody
    public ResponseData stayHours(@RequestBody StayHoursRequest param) {
        return wxOrderService.stayHours(param);
    }

    /**
     * 微信支付
     * @param param
     * @return
     */
    @RequestMapping("vipOrderPay.do")
    @ResponseBody
    public ResponseData vipOrderPay(@RequestBody VipOrderPayRequest param) {
        return wxOrderService.vipOrderPay(param);
    }

    /**
     * 微信注册
     *
     * @param createBookingOrderRequest
     * @return
     */
    @RequestMapping("wxCreateBookingOrderRequest.do")
    @ResponseBody
    public ResponseData createBookingOrder(@RequestBody CreateBookingOrderRequest createBookingOrderRequest) {
        return wxOrderService.createBookingOrder(createBookingOrderRequest);
    }

    /**
     * 微信注册
     *
     * @param param
     * @return
     */
    @RequestMapping("searchWxBook.do")
    @ResponseBody
    public ResponseData searchBook(@RequestBody JSONObject param) {
        return wxOrderService.searchBook(param);
    }

    @RequestMapping("searchOrderInfo.do")
    @ResponseBody
    public ResponseData searchOrderInfo(@RequestBody SearchOrderInfoRequest searchOrderInfoRequest) {
        return wxOrderService.searchOrderInfo(searchOrderInfoRequest);
    }

    @RequestMapping("searchRegistInfoForRoomInfo.do")
    @ResponseBody
    public ResponseData searchRegistInfoForRoomInfo(@RequestBody BaseRequest baseRequest) {
        return wxOrderService.searchRegistInfoForRoomInfo(baseRequest);
    }




    /**
     * 修改会员信息
     *
     * @param param
     * @return
     */
    @RequestMapping("updateWxMsg.do")
    @ResponseBody
    public ResponseData updateWxMsg(@RequestBody UpdateWxMsg param) {
        return wxMemberService.updateWxMsg(param);
    }

    /**
     * 查询会员当前在住信息
     *
     * @param param
     * @return
     */
    @RequestMapping("searchCheckIn.do")
    @ResponseBody
    public ResponseData searchCheckIn(@RequestBody JSONObject param) {
        return wxMemberService.searchCheckIn(param);
    }

    /**
     * 查询会员当前在住信息
     *
     * @param param
     * @return
     */
    @RequestMapping("addWxShoppingCar.do")
    @ResponseBody
    public ResponseData searchCheckIn(@RequestBody WxShoppingCardRequest param) {
        return wxMiniprogramsService.addWxShoppingCar(param);
    }

    /**
     * 查询会员当前在住信息
     *
     * @param param
     * @return
     */
    @RequestMapping("addWxShoppingCar")
    @ResponseBody
    public ResponseData addWxShoppingCarOther(@RequestBody WxShoppingCardRequest param) {
        return wxMiniprogramsService.addWxShoppingCarOther(param);
    }

    /**
     * 查询绑定会员信息
     *
     * @param param
     * @return
     */
    @RequestMapping("getBindVipMsg.do")
    @ResponseBody
    public ResponseData getBindVipMsg(@RequestBody CardGroupUrlSearch param) {
        return wxMemberService.getBindVipMsg(param);
    }

    /**
     * 查询绑定会员信息
     *
     * @param param
     * @return
     */
    @RequestMapping("bindVip.do")
    @ResponseBody
    public ResponseData bindVip(@RequestBody CardGroupUrlSearch param) {
        return wxMemberService.bindVip(param);
    }

    @RequestMapping("selectRechargePlan.do")
    @ResponseBody
    public ResponseData selectRechargePlan(@RequestBody CardRechargePlanDetailsSearch cardRechargePlanDetailsSearch) {
        return wxMemberService.selectRechargePlan(cardRechargePlanDetailsSearch);
    }


    @RequestMapping("addCRecharge.do")
    @ResponseBody
    public ResponseData addCRecharge(@RequestBody AddCRechargeRequest addCRechargeRequest) {
        return wxMemberService.addCRecharge(addCRechargeRequest);
    }

    @RequestMapping("paySuccRecharge.do")
    @ResponseBody
    public ResponseData paySuccRecharge(@RequestBody CardRechargeSearch cardRechargeSearch) {
        return wxMiniprogramsService.paySuccRecharge(cardRechargeSearch);
    }

    /**
     * 查询子订单信息
     *
     * @param param
     * @return
     */
    @RequestMapping("searchBookRoomById.do")
    @ResponseBody
    public ResponseData searchBookRoomById(@RequestBody BookingOrderRoomNumSearch param) {
        return wxOrderService.searchBookRoomById(param);
    }

    /**
     * 查询子订单信息
     *
     * @param param
     * @return
     */
    @RequestMapping("searchWxRegist.do")
    @ResponseBody
    public ResponseData searchWxRegist(@RequestBody JSONObject param) {
        return wxOrderService.searchWxRegist(param);
    }

}
