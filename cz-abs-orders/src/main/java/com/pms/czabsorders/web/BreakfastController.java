package com.pms.czabsorders.web;


import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.BreakfastInRequest;
import com.pms.czpmsutils.request.BreakfastSaleRequest;
import com.pms.czpmsutils.request.UseBreakfastSaleRequest;
import com.pms.pmsorder.bean.BreakfastInfo;
import com.pms.pmsorder.bean.request.RoomCardRecordSearchRequest;
import com.pms.pmsorder.bean.search.*;
import com.pms.pmsorder.service.BreakfastService;
import com.pms.pmsorder.service.FoodsService;
import com.pms.pmsorder.service.RegistService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 早餐券相关业务
 */
@Controller
@RequestMapping("/hotel/regist/")
public class BreakfastController {

    @Autowired
    private BreakfastService breakfastService;

    @Autowired
    private RegistService registService;

    @Autowired
    private FoodsService foodsService;


    /**
     *  查询所有早餐信息
     *
     * @param request
     * @return
     */
    @RequestMapping("allHotelBreakFast.do")
    @ResponseBody
    public ResponseData allHotelBreakFast(@RequestBody BreakfastInfoSearch request) {
        return breakfastService.allHotelBreakFast(request);
    }
    /**
     *  通过早餐Id查询早餐信息
     *
     * @param request
     * @return
     */
    @RequestMapping("getBreakFastById.do")
    @ResponseBody
    public BreakfastInfo addOrUpaBreakFast(@RequestParam Integer breakfastId) {
        return breakfastService.getBreakFastById(breakfastId);
    }

    /**
     *  添加或修改早餐信息
     *
     * @param request
     * @return
     */
    @RequestMapping("addOrUpaBreakFast.do")
    @ResponseBody
    public ResponseData addOrUpaBreakFast(@RequestBody BreakfastInRequest request) {
        return breakfastService.addOrUpaBreakFast(request);
    }


    /**
     *  添加早餐售卖记录
     *
     * @param request
     * @return
     */
    @RequestMapping("addSaleBreakFastRecord.do")
    @ResponseBody
    public ResponseData addSaleBreakFastRecord(@RequestBody BreakfastSaleRequest request) {
        return breakfastService.addSaleBreakFastRecord(request);
    }

    /**
     *  查询早餐券售卖
     *
     * @param request
     * @return
     */
    @RequestMapping("searchBreakfastSaleRecord.do")
    @ResponseBody
    public ResponseData searchBreakfastSaleRecord(@RequestBody BreakfastSaleRecordSearch request) {
        return breakfastService.searchBreakfastSaleRecord(request);
    }


    /**
     *  早餐券使用
     * @param request
     * @return
     */
    @RequestMapping("useBreakfast.do")
    @ResponseBody
    public ResponseData useBreakfast(@RequestBody UseBreakfastSaleRequest request) {
        return breakfastService.useBreakfast(request);
    }
    /**
     *  早餐券使用
     * @param request
     * @return
     */
    @RequestMapping("useBreakfastFace.do")
    @ResponseBody
    public ResponseData useBreakfastFace(@RequestBody UseBreakfastSaleRequest request) {
        return breakfastService.useBreakfastFace(request);
    }

   /* @RequestMapping("faceRep")
    @ResponseBody
    public JSONObject faceRep(@RequestBody JSONObject request) {
        return breakfastService.faceRep(request);
    }*/

    /**
     *  早餐券使用
     * @param request
     * @return
     */
    @RequestMapping("allBreakfastUseRecord.do")
    @ResponseBody
    public ResponseData allBreakfastUseRecord(@RequestBody BreakfastUseRecordSearch request) {
        return breakfastService.allBreakfastUseRecord(request);
    }
    /**
     *  查询所有早餐信息
     *
     * @param request
     * @return
     */
    @RequestMapping("addThirdCardMsg.do")
    @ResponseBody
    public ResponseData addCardMsg(@RequestBody RoomCardRecordSearchRequest request) {
        return foodsService.addCardMsg(request);
    }

    /**
     *  查询所有早餐信息
     *
     * @param request
     * @return
     */
    @RequestMapping("copyFoodsSaleRecord.do")
    @ResponseBody
    public ResponseData copyFoodsSaleRecord(@RequestBody BreSaleRecordSearch request) {
        return foodsService.copyFoodsSaleRecord(request);
    }

    /**
     *  复制餐台信息
     * @param request
     * @return
     */
    @RequestMapping("copyTeather.do")
    @ResponseBody
    public ResponseData copyTeather(@RequestBody BreTeatherSearch request) {
        return foodsService.copyTeather(request);
    }

    /**
     * 查询餐台信息
     * @param request
     * @return
     */
    @RequestMapping("searchTeather.do")
    @ResponseBody
    public ResponseData searchTeather(@RequestBody BreTeatherSearch request) {
        return foodsService.searchTeather(request);
    }

    /**
     * 查询销售记录
     * @param request
     * @return
     */
    @RequestMapping("searchSaleRecord.do")
    @ResponseBody
    public ResponseData searchSaleRecord(@RequestBody BreSaleRecordSearch request) {
        return foodsService.searchSaleRecord(request);
    }

}
