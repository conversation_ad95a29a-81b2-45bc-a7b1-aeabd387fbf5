package com.pms.czabsorders.web.member.member;


import com.pms.czmembership.bean.member.CardRechargePlanRequest;
import com.pms.czmembership.bean.member.search.*;
import com.pms.czmembership.bean.requst.AddOrEditCardInfo;
import com.pms.czmembership.bean.requst.AddOrEditMemberLevelRequest;
import com.pms.czmembership.bean.requst.CommonIdDel;
import com.pms.czmembership.bean.requst.UpdateCardLevelRequest;
import com.pms.czmembership.service.memeber.MemberService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * 会员信息相关业务操作
 */
@Controller
@RequestMapping("/hotel/member/")
@Slf4j
public class MemberController {

    @Autowired
    private MemberService memberService;

    /**
     * 注册会员
     * @param addOrEditCardInfo
     * @return
     */
    @RequestMapping("registMember.do")
    @ResponseBody
    public ResponseData registMember(@RequestBody AddOrEditCardInfo addOrEditCardInfo){
        return memberService.registMember(addOrEditCardInfo);
    }

    /**
     * 获取会员信息
     * @param cardInfoSearchRequest
     * @return
     */
    @RequestMapping("findMember.do")
    @ResponseBody
    public ResponseData findMember(@RequestBody CardInfoSearchRequest cardInfoSearchRequest){

        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(memberService.findMember(cardInfoSearchRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 修改会员基础信息
     * @param addOrEditCardInfo
     * @return
     */
    @RequestMapping("updateMember.do")
    @ResponseBody
    public ResponseData updateMember(@RequestBody AddOrEditCardInfo addOrEditCardInfo){
        return memberService.updateMember(addOrEditCardInfo);
    }

    /**
     * 查询集团会员
     * @param param
     * @return
     */
    @RequestMapping("findGroupMember.do")
    @ResponseBody
    public ResponseData findGroupMember(@RequestBody JSONObject param){
        return memberService.findGroupMember(param);
    }


    @RequestMapping("findGourpLevel.do")
    @ResponseBody
    public ResponseData findGourpLevel(@RequestBody CardGroupLevelSearch param){
        return memberService.findGourpLevel(param);
    }
    /**
     * 会员充值
     * @param param
     * @return
     */
    @RequestMapping("memberTranslate.do")
    @ResponseBody
    public ResponseData memberTranslate(@RequestBody JSONObject param){
        return memberService.memberTranslate(param);
    }

    /**
     * 会员充值撤销
     * @param param
     * @return
     */
    @RequestMapping("memberTranslateCancel.do")
    @ResponseBody
    public ResponseData memberTranslateCancel(@RequestBody JSONObject param){
        return memberService.memberTranslateCancel(param);
    }


    /**
     * 会员消费
     * @param param
     * @return
     */
    @RequestMapping("memberConsumption.do")
    @ResponseBody
    public ResponseData memberConsumption(@RequestBody JSONObject param){
        return memberService.memberConsumption(param);
    }

    /**
     * 会员冲账
     *  aid ： 储值或消费的id
     *  payCostId : 小类费用码
     * @param param
     * @return
     */
    @RequestMapping("strikeMemberConsumption.do")
    @ResponseBody
    public ResponseData strikeMemberConsumption(@RequestBody JSONObject param ){
        return memberService.strikeMemberConsumption(param);
    }

    /**
     * 完成会员预授权
     * @param param
     * @return
     */
    @RequestMapping("finishMemberFreeze.do")
    @ResponseBody
    public ResponseData finishMemberFreeze(@RequestBody JSONObject param){
        return memberService.finishMemberFreeze(param);
    }

    /**
     * 查询会员消费记录
     * @param cardConsumptionRecordSearch
     * @return
     */
    @RequestMapping("searchCardConsumptionRecord.do")
    @ResponseBody
    public ResponseData searchCardConsumptionRecord(@RequestBody CardConsumptionRecordSearch cardConsumptionRecordSearch ){
        return memberService.searchCardConsumptionRecord(cardConsumptionRecordSearch);
    }

    /**
     * 充值记录
     * @param cardRechargeSearch
     * @return
     */
    @RequestMapping("searchCardRecharge.do")
    @ResponseBody
    public ResponseData searchCardRecharge(@RequestBody CardRechargeSearch cardRechargeSearch ){
        return memberService.searchCardRecharge(cardRechargeSearch);
    }

    /**
     * 查询会员冻结记录
     * @param cardFreezeRecordSearch
     * @return
     */
    @RequestMapping("searchCardFreezeRecord.do")
    @ResponseBody
    public ResponseData searchCardFreezeRecord(@RequestBody CardFreezeRecordSearch cardFreezeRecordSearch ){
        return memberService.searchCardFreezeRecord(cardFreezeRecordSearch);
    }


    /**
     * 查询会员入住记录
     * @param cardFreezeRecordSearch
     * @return
     */
    @RequestMapping("searchCardRegistMsg.do")
    @ResponseBody
    public ResponseData searchCardRegistMsg(@RequestBody CardCheckinRecordSearch cardFreezeRecordSearch ){
        return memberService.searchCardRegistMsg(cardFreezeRecordSearch);
    }

    /**
     * 查询会员入住记录
     * @param cardFreezeRecordSearch
     * @return
     */
    @RequestMapping("searchCardCheckinNum.do")
    @ResponseBody
    public ResponseData searchCardCheckinNum(@RequestBody CardCheckinRecordSearch cardFreezeRecordSearch ){
        return memberService.searchCardCheckinNum(cardFreezeRecordSearch);
    }


    /**
     * 注销会员
     * @param memberRequest
     * @return
     */
    @RequestMapping("logoutMember.do")
    @ResponseBody
    public ResponseData logoutMember(@RequestBody MemberRequest memberRequest ){
        return memberService.logoutMember(memberRequest);
    }

    /**
     * 会员积分转换
     * @param memberRequest
     * @return
     */
    @RequestMapping("memberTranslatePoint.do")
    @ResponseBody
    public ResponseData memberTranslatePoint(@RequestBody CardPointRequest memberRequest ){
        return memberService.memberTranslatePoint(memberRequest);
    }

    /**
     * 会员积分消费
     * @param memberRequest
     * @return
     */
    @RequestMapping("memberConsumptionPoint.do")
    @ResponseBody
    public ResponseData memberConsumptionPoint(@RequestBody CardPointUseRequest memberRequest ){
        return memberService.memberConsumptionPoint(memberRequest);
    }

    /**
     * 查询会员积分转换记录
     * @param cardRechargePointSearch
     * @return
     */
    @RequestMapping("searchMemberTranslatePoint.do")
    @ResponseBody
    public ResponseData searchMemberTranslatePoint(@RequestBody CardRechargePointSearch cardRechargePointSearch ){
        return memberService.searchMemberTranslatePoint(cardRechargePointSearch);
    }

    /**
     * 查询会员积分消费记录
     * @param cardConsumptionPointRecordSearch
     * @return
     */
    @RequestMapping("searchMemberConsumptionPoint.do")
    @ResponseBody
    public ResponseData searchMemberConsumptionPoint(@RequestBody CardConsumptionPointRecordSearch cardConsumptionPointRecordSearch ){
        return memberService.searchMemberConsumptionPoint(cardConsumptionPointRecordSearch);
    }


    /**
     * 查询会员付款 消费明细
     * @param searchMemberConMsg
     * @return
     */
    @RequestMapping("searchMemberConMsg.do")
    @ResponseBody
    public ResponseData searchMemberConMsg(@RequestBody SearchMemberConMsg searchMemberConMsg ){
        return memberService.searchMemberConMsg(searchMemberConMsg);
    }

    /**
     * 会员充值计划
     * @param searchMemberConMsg
     * @return
     */
    @RequestMapping("cardRechargePlan.do")
    @ResponseBody
    public ResponseData cardRechargePlan(@RequestBody CardRechargePlanSearch searchMemberConMsg ){
        return memberService.cardRechargePlan(searchMemberConMsg);
    }
    /**
     * 添加或修改会员充值计划
     * @param cardRechargePlanRequest
     * @return
     */
    @RequestMapping("addCardRechargePlan.do")
    @ResponseBody
    public ResponseData addCardRechargePlan(@RequestBody CardRechargePlanRequest cardRechargePlanRequest ){
        return memberService.addCardRechargePlan(cardRechargePlanRequest);
    }

    /**
     * 查询会员充值计划
     * @param cardRechargePlanRequest
     * @return
     */
    @RequestMapping("searchRechargePlanDetails.do")
    @ResponseBody
    public ResponseData searchRechargePlanDetails(@RequestBody CardRechargePlanDetailsSearch cardRechargePlanRequest ){
        return memberService.searchRechargePlanDetails(cardRechargePlanRequest);
    }

    /**
     * 修改会员级别
     * @param updateCardLevelRequest
     * @return
     */
    @RequestMapping("updateMemberLevel.do")
    @ResponseBody
    public ResponseData updateMemberLevel(@RequestBody UpdateCardLevelRequest updateCardLevelRequest ){
        return memberService.updateMemberLevel(updateCardLevelRequest);
    }

    /**
     * 编辑或新增会员等级
     * @param addOrEditMemberLevelRequest
     * @return
     */
    @PostMapping("addOrEditMemberLevel")
    @ResponseBody
    public ResponseData addOrEditMemberLevel(@RequestBody AddOrEditMemberLevelRequest addOrEditMemberLevelRequest){
        return memberService.addOrEditMemberLevel(addOrEditMemberLevelRequest);
    }

    /**
     * 会员等级删除
     * @param levelDel
     * @return
     */
    @PostMapping("levelDel")
    @ResponseBody
    public ResponseData levelDel(@RequestBody CommonIdDel levelDel ){
        return memberService.levelDel(levelDel.getId());
    }

    /**
     * 获取会员等级列表不分页
     * @return
     */
    @GetMapping("getMemberLevelList")
    @ResponseBody
    public ResponseData getMemberLevelList(@RequestParam("sessionToken") String sessionToken) throws Exception {
        return memberService.getMemberLevelList(sessionToken);
    }

    /**
     * 储值设置删除
     * @param prepaidSettingDel
     * @return
     */
    @PostMapping("prepaidSettingDel")
    @ResponseBody
    public ResponseData prepaidSettingDel(@RequestBody CommonIdDel prepaidSettingDel ){
        return memberService.prepaidSettingDel(prepaidSettingDel.getId());
    }


}
