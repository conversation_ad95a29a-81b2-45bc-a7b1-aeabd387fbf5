package com.pms.czabsorders.web.hotel;


import com.github.pagehelper.Page;
import com.pms.czhotelfoundation.bean.SmsHotelCount;
import com.pms.czhotelfoundation.bean.SmsHotelTemplate;
import com.pms.czhotelfoundation.bean.SmsTemplate;
import com.pms.czhotelfoundation.bean.request.SmsHotelBuyRecordRequest;
import com.pms.czhotelfoundation.bean.request.SmsHotelTemplateRequest;
import com.pms.czhotelfoundation.bean.search.SmsHotelCountSearch;
import com.pms.czhotelfoundation.bean.search.SmsHotelSendRecordSearch;
import com.pms.czhotelfoundation.bean.search.SmsHotelTemplateSearch;
import com.pms.czhotelfoundation.bean.search.SmsTemplateSearch;
import com.pms.czhotelfoundation.service.SmsService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.SmsHotelSendRecordRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * 短信
 */
@Controller
@RequestMapping("/hotel/sms/")
@Slf4j
public class SmsController {


    @Resource
    private SmsService smsService;

    /**
     * 或者更新酒店发送短信模板
     *
     * @param request
     * @return
     */
    @RequestMapping("addOrUpdateSmsHotelTemplate.do")
    @ResponseBody
    public ResponseData addOrUpdateSmsHotelTemplate(@RequestBody SmsHotelTemplateRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            smsService.addOrUpdateSmsHotelTemplate(request);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 增加酒店购买短信记录
     *
     * @param request
     * @return
     */
    @RequestMapping("addSmsHotelBuyRecord.do")
    @ResponseBody
    public ResponseData addSmsHotelBuyRecord(@RequestBody SmsHotelBuyRecordRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            smsService.addSmsHotelBuyRecord(request);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("getSmsHotelSendRecord.do")
    @ResponseBody
    public ResponseData getSmsHotelSendRecord(@RequestBody SmsHotelSendRecordSearch request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(smsService.getSmsHotelSendRecord(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 发送短信
     *
     * @param request
     * @return
     */
    @RequestMapping("addSmsHotelSendRecord.do")
    @ResponseBody
    public ResponseData addSmsHotelSendRecord(@RequestBody SmsHotelSendRecordRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            smsService.addSmsHotelSendRecord(request);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("findSmsHotelTemplate.do")
    @ResponseBody
    public ResponseData findSmsHotelTemplate(@RequestBody SmsHotelTemplateSearch request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Page<SmsHotelTemplate> smsHotelTemplate = smsService.findSmsHotelTemplate(request);
            responseData.setData(smsHotelTemplate);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("findSmsTemplate.do")
    @ResponseBody
    public ResponseData findSmsTemplate(@RequestBody SmsTemplateSearch request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Page<SmsTemplate> smsTemplate = smsService.findSmsTemplate(request);
            responseData.setData(smsTemplate);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("findHotelCount.do")
    @ResponseBody
    public ResponseData findHotelCount(@RequestBody SmsHotelCountSearch request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Page<SmsHotelCount> smsHotelCounts = smsService.smsHotelCountSearch(request);
            responseData.setData(smsHotelCounts);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
