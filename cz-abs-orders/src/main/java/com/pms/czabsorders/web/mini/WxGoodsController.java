package com.pms.czabsorders.web.mini;

import com.pms.czabsorders.service.mini.WxGoodsService;
import com.pms.czpmsutils.ResponseData;
import com.pms.pmswarehouse.bean.request.HotelGoodsRequest;
import com.pms.pmswarehouse.bean.search.GoodsShoppingOrderSearch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/minipro/")
public class WxGoodsController {

    @Autowired
    private WxGoodsService wxGoodsService;

    /**
     * 查询微信商品售卖记录
     * @param param
     * @return
     */
    @RequestMapping("searchGoodsRecord.do")
    @ResponseBody
    public ResponseData searchGoodsRecord(@RequestBody GoodsShoppingOrderSearch param){
        return wxGoodsService.searchGoodsRecord(param);
    }

    /**
     * 查询微信商品售卖记录
     * @param param
     * @return
     */
    @RequestMapping("findGoodsByWechat")
    @ResponseBody
    public ResponseData findGoodsByWechat(@RequestBody HotelGoodsRequest param){
        return wxGoodsService.findGoodsByWechat(param);
    }

}
