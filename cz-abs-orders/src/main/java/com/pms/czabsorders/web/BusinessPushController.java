package com.pms.czabsorders.web;

import com.pms.czabsorders.bean.BusinessPushDataRqeust;
import com.pms.czabsorders.service.businessPush.BusinessPushService;
import com.pms.czpmsutils.ResponseData;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@RequestMapping("/hotel/absorder/")
public class BusinessPushController {

    @Resource
    BusinessPushService businessPushService;

    @RequestMapping("businessPushData.do")
    @ResponseBody
    public ResponseData businessPushData(@RequestBody BusinessPushDataRqeust businessPushDataRqeust) {
        ResponseData responseData = ResponseData.newSuccessData();
        businessPushService.businessPushData(businessPushDataRqeust);
        return responseData;
    }
}
