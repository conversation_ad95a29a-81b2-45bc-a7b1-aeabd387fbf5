package com.pms.czabsorders.web.hotel.group;

import com.pms.czhotelfoundation.bean.group.GetHotelListRequest;
import com.pms.czhotelfoundation.service.group.HotelGroupService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.BaseRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 集团业务
 */
@Controller
@RequestMapping("/hotel/group/")
public class HotelGroupController {

    @Autowired
    private HotelGroupService hotelGroupService;

    @RequestMapping("getGroupHotelList.do")
    @ResponseBody
    public ResponseData getGroupHotelList(@RequestBody GetHotelListRequest getHotelListReqest) {
        getHotelListReqest.setSessionToken("DEF623BCAF9847CC9465524E75BB922D");
        return hotelGroupService.getGroupHotelList(getHotelListReqest);
    }


    @RequestMapping("getGroupInfo.do")
    @ResponseBody
    ResponseData getGroupInfo(@RequestBody BaseRequest baseRequest) {
        baseRequest.setSessionToken("DEF623BCAF9847CC9465524E75BB922D");
        ResponseData responseData = hotelGroupService.getGroupInfo(baseRequest);
        return responseData;
    }


    @RequestMapping("getGroupOrgPriceImage.do")
    @ResponseBody
    ResponseData getGroupOrgPriceImage(@RequestBody BaseRequest baseRequest) {
        return hotelGroupService.getGroupOrgPriceImage(baseRequest);
    }

    @RequestMapping("getGroupByUid")
    @ResponseBody
    public Integer getGroupInfo(@RequestBody String uuid) {
        Integer responseData = hotelGroupService.getGroupByUid(uuid);
        return responseData;
    }

}
