package com.pms.czabsorders.web;


import com.pms.czabsorders.service.team.TeamService;
import com.pms.czpmsutils.ResponseData;
import com.pms.pmsorder.bean.request.RegistGroupRequest;
import com.pms.pmsorder.service.RegistGroupService;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/registgroup/")
public class RegistGroupController {

    @Autowired
    private RegistGroupService registGroupService;

    @Autowired
    private TeamService teamService;

    /**
     *  添加团队信息
     * @param request
     * @return
     */
    @RequestMapping("addRegistGroupRecord.do")
    @ResponseBody
    public ResponseData addRegistForOtherPms(@RequestBody JSONObject request) {
        return registGroupService.addRegistGroupRecord(request);
    }

    /**
     *  查询团队信息
     * @param request
     * @return
     */
    @RequestMapping("findRegistGourpRecord.do")
    @ResponseBody
    public ResponseData findRegistGourpRecord(@RequestBody JSONObject request) {
        return registGroupService.findRegistGourpRecord(request);
    }

    @RequestMapping("getGroupRoomInfo.do")
    @ResponseBody
    public ResponseData getGroupRoomInfo(@RequestBody JSONObject request) {
        return registGroupService.getGroupRoomInfo(request);
    }

    @RequestMapping("selectAllTeam.do")
    @ResponseBody
    public ResponseData selectAllTeam(@RequestBody RegistGroupRequest request) {
        return teamService.selectAllTeam(request);
    }

    @RequestMapping("searchTeamDetails.do")
    @ResponseBody
    public ResponseData searchTeamDetails(@RequestBody RegistGroupRequest request) {
        return teamService.searchTeamDetails(request);
    }
}
