package com.pms.czabsorders.web;

import com.pms.czhotelfoundation.bean.docking.PlatDockingSetting;
import com.pms.czpmsutils.GURL;
import com.pms.czpmsutils.Pms;
import com.pms.czpmsutils.enums.PlatTypeEnum;
import com.pms.czpmsutils.request.ThirdAuthReq;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
public class TestController {
    @Autowired
    private RestTemplate restTemplate;


    @RequestMapping("/hotel/order/test")
    public void test(){
        ThirdAuthReq thirdAuthReq = new ThirdAuthReq();
        thirdAuthReq.setAuthType(PlatTypeEnum.JS_SK_PLAT);
        JSONObject authParam = new JSONObject();
        authParam.put("path", Pms.SYNCOPERATEDATA);
        thirdAuthReq.setAuthParam(authParam);
        JSONObject reqParam = new JSONObject();
        reqParam.put("appId","38e11aa5e09e401d");
        List<Map<String ,Object>> operateDataInfos = new ArrayList<>();
        Map<String ,Object> operateDataInfo = new HashMap<>();
        operateDataInfo.put("operateDataId","1");
        reqParam.put("operateDataInfo",operateDataInfo);
        thirdAuthReq.setReqParam(reqParam);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject jsonObject = JSONObject.fromObject(thirdAuthReq);
        HttpEntity<JSONObject> request = new HttpEntity<JSONObject>(jsonObject, headers);
        Map<String, Object> body = restTemplate.postForEntity("http://" + GURL.PMSSOUTH + "/v1/auth/access/post", request, Map.class).getBody();
        System.out.println(body);
    }

}
