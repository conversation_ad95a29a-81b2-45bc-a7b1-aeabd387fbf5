package com.pms.czabsorders.web.member.sales;


import com.pms.czmembership.bean.sales.search.SalesPersonSearch;
import com.pms.czmembership.service.sales.SalesService;
import com.pms.czpmsutils.ResponseData;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/sales/")
public class SalesController {

    @Autowired
    private SalesService salesService;




    @RequestMapping("findAllSalesType.do")
    @ResponseBody
    public ResponseData findAllSalesType(@RequestBody JSONObject param){
        return salesService.findAllSalesType(param);

    }

    @RequestMapping("addOrUpdateSalesType.do")
    @ResponseBody
    public ResponseData addOrUpdateSalesType(@RequestBody JSONObject param){
        return salesService.addOrUpdateSalesType(param);

    }


    @RequestMapping("findSalesPeople.do")
    @ResponseBody
    public ResponseData findSalesPeople(@RequestBody SalesPersonSearch param){
        return salesService.findSalesPeople(param);

    }

    @RequestMapping("addOrUpdateSalesPerson.do")
    @ResponseBody
    public ResponseData addOrUpdateSalesPerson(@RequestBody JSONObject param){
        return salesService.addOrUpdateSalesPerson(param);

    }
}
