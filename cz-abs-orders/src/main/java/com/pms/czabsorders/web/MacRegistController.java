package com.pms.czabsorders.web;

import com.pms.czabsorders.service.machine.MachineRegistService;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/hotel/macregist/")
public class MacRegistController {

    @Autowired
    private MachineRegistService machineRegistService;

    @RequestMapping("machineAddRegistForOtherPms.do")
    @ResponseBody
    public Map<String, Object> checkOut(@RequestBody JSONObject param ){
        return machineRegistService.machineAddRegistForOtherPms(param);
    }

    @RequestMapping("checkOutForOtherPms.do")
    @ResponseBody
    public Map<String, Object> checkOutForOtherPms(@RequestBody JSONObject param ){
        return machineRegistService.checkOutForOtherPms(param);
    }

    @RequestMapping("refundMoneyForOtherPms.do")
    @ResponseBody
    public Map<String, Object> refundmoneyforotherpms(@RequestBody JSONObject param ){
        return machineRegistService.refundMoneyForOtherPmsAndPush(param);
    }

    @RequestMapping("machineAddAccountForOtherPms.do")
    @ResponseBody
    public Map<String, Object> machineAddAccountForOtherPms(@RequestBody JSONObject param){
        return machineRegistService.machineAddAccountForOtherPms(param);
    }

    @RequestMapping("findRegistAndCanRefundAccountByOtherPmsId.do")
    @ResponseBody
    public Map<String, Object> findRegistAndCanRefundAccountByOtherPmsId(@RequestBody JSONObject param){
        return machineRegistService.findRegistAndCanRefundAccountByOtherPmsId(param);
    }
}
