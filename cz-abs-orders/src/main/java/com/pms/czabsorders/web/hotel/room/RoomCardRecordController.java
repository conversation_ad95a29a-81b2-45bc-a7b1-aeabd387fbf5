package com.pms.czabsorders.web.hotel.room;


import com.github.pagehelper.Page;
import com.pms.czhotelfoundation.bean.request.RoomCardRecordRequest;
import com.pms.czhotelfoundation.bean.room.RoomCardRecord;
import com.pms.czhotelfoundation.bean.room.search.RoomCardRecordSearch;
import com.pms.czhotelfoundation.service.room.RoomCardService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/roomcard/")
@Slf4j
public class RoomCardRecordController {

    @Autowired
    private RoomCardService roomCardService;


    @RequestMapping("addRoomCardRecord.do")
    @ResponseBody
    public ResponseData addRoomCardRecord(@RequestBody RoomCardRecordRequest roomCardRecordRequest) {
        return roomCardService.addRoomCardRecord(roomCardRecordRequest);
    }

    @RequestMapping("searchRoomCardRecord.do")
    @ResponseBody
    public ResponseData searchRoomCardRecord(@RequestBody RoomCardRecordSearch roomCardRecordSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Page<RoomCardRecord> roomCardRecords = roomCardService.searchRoomCardRecord(roomCardRecordSearch);
            responseData.setData(roomCardRecords);
        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }

    @RequestMapping("searchRoomCardRecordCount.do")
    @ResponseBody
    public ResponseData searchRoomCardRecordCount(@RequestBody JSONObject param) {
        return roomCardService.searchRoomCardRecordCount(param);
    }
}
