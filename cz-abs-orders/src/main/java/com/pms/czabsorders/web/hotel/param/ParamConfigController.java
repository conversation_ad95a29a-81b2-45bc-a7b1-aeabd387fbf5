package com.pms.czabsorders.web.hotel.param;

import com.pms.czhotelfoundation.bean.room.RoomInfoNum;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoSearch;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.service.param.ParamService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.BaseRequest;
import com.pms.czpmsutils.request.ParamRequest;
import com.pms.czpmsutils.view.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 酒店房价相关设置
 */
@RestController
@RequestMapping("/hotel/baseinfo/")
@Slf4j
public class ParamConfigController {

    @Autowired
    private ParamService paramService;

    @Autowired
    private RoomInfoDao roomInfoDao;

    /**
     * 获取酒店初始化数据
     * @param request
     * @return
     */
    @RequestMapping("hotelInitialDataParam")
    @ResponseBody
    public ResponseData hotelInitialData(@RequestBody BaseRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<HotelInitialDataView> hotelInitialDataList = paramService.getHotelInitialDataList(request);
            Map<Integer, List<HotelInitialDataView>> listMap = hotelInitialDataList.stream().filter(hotelInitialDataView -> {
                return hotelInitialDataView != null && hotelInitialDataView.getValueType() != null;
            }).collect(Collectors.groupingBy(HotelInitialDataView::getValueType));
            //list = list.stream().sorted((front, behind) -> front.[0].compareTo(behind[0])).collect(Collectors.toList());
            listMap.values().stream().sorted(Comparator.comparing(hotelInitialDataViews -> hotelInitialDataViews.get(0).getSort()));
            responseData.setData(listMap);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 获取酒店房型数据
     * @param request
     * @return
     */
    @RequestMapping("getHotelRoomTypeDataParam")
    @ResponseBody
    public ResponseData getHotelRoomTypeList(@RequestBody BaseRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<HotelRoomTypeViewResult> hotelRoomTypeViewList = paramService.getHotelRoomTypeList(request);
            Map<String ,Object> resultMap = new HashMap<>();
            resultMap.put("list",hotelRoomTypeViewList);
            Map<Integer, HotelRoomTypeViewResult> collect = hotelRoomTypeViewList.stream().collect(Collectors.toMap(HotelRoomTypeViewResult::getRoomTypeId, a -> a, (k1, k2) -> k1));
            resultMap.put("map",collect);
            responseData.setData(resultMap);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 获取酒店房间数据
     * @param request
     * @return
     */
    @RequestMapping("getHotelRoomInfoDataParam")
    @ResponseBody
    public ResponseData getHotelRoomInfoList(@RequestBody BaseRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<RoomInfoView> hotelRoomInfoViewList = paramService.getHotelRoomInfoList(request);
            // 自定义排序逻辑，提取 RoomNum 中的数字并比较
            hotelRoomInfoViewList.sort((r1, r2) -> {
                String roomNum1 = r1.getRoomNum();
                String roomNum2 = r2.getRoomNum();

                // 提取数字部分
                Integer num1 = extractNumber(roomNum1);
                Integer num2 = extractNumber(roomNum2);

                // 如果都不存在数字，默认按字符串比较
                if (num1 == null && num2 == null) {
                    return roomNum1.compareToIgnoreCase(roomNum2);
                }

                // 否则按照数字大小排序
                return Integer.compare(num1 != null ? num1 : 0, num2 != null ? num2 : 0);
            });
            Map<String ,Object> resultMap = new HashMap<>();
            resultMap.put("list",hotelRoomInfoViewList);
            Map<Integer, RoomInfoView> collect = hotelRoomInfoViewList.stream().collect(Collectors.toMap(RoomInfoView::getRoomInfoId, a -> a, (k1, k2) -> k1));
            resultMap.put("map",collect);
            responseData.setData(resultMap);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 从字符串中提取整数，如果不存在数字则返回 null
     */
    private Integer extractNumber(String str) {
        if (str == null || str.isEmpty()) {
            return null;
        }

        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\d+");
        java.util.regex.Matcher matcher = pattern.matcher(str);

        StringBuilder digits = new StringBuilder();

        while (matcher.find()) {
            digits.append(matcher.group());
        }

        if (digits.length() == 0) {
            return null; // 没有任何数字
        }

        try {
            return Integer.parseInt(digits.toString());
        } catch (NumberFormatException e) {
            // 如果超出 Integer 范围，可按需处理：抛异常、返回 null 或 Long 类型
            return null;
        }
    }

    /**
     * 获取酒店房价数据
     * @param request
     * @return
     */

    @RequestMapping("getHotelRoomRateCodeList")
    @ResponseBody
    public ResponseData getHotelRoomRateCodeList(@RequestBody BaseRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<RoomRateCodeView> roomRateCodeViews = paramService.getHotelRoomRateCodeList(request);
            Map<String ,Object> resultMap = new HashMap<>();
            resultMap.put("list",roomRateCodeViews);
            responseData.setData(resultMap);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 暂未使用
     * @param request
     * @return
     */
    @RequestMapping("get/map")
    public ResponseData getMap(@RequestBody ParamRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(paramService.getMap(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
    /**
     * 暂未使用
     * @param request
     * @return
     */
    @RequestMapping("get/list")
    public ResponseData getList(@RequestBody ParamRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(paramService.getList(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


}
