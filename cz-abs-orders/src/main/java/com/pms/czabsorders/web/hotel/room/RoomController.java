package com.pms.czabsorders.web.hotel.room;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.Page;
import com.pms.czhotelfoundation.bean.room.RoomImportBean;
import com.pms.czabsorders.listener.RoomImportListener;
import com.pms.czhotelfoundation.bean.request.AddRoomInfoListRequest;
import com.pms.czhotelfoundation.bean.request.BatchAddApplyInfoRecordRequest;
import com.pms.czhotelfoundation.bean.room.RoomCheckRecord;
import com.pms.czhotelfoundation.bean.room.RoomInfoQr;
import com.pms.czhotelfoundation.bean.room.search.RoomCheckRecordSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoQrSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomRateSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoSearch;
import com.pms.czhotelfoundation.service.room.RoomService;
import com.pms.czhotelfoundation.service.room.impl.RoomServiceImpl;
import com.pms.czpmsutils.Checkers;
import com.pms.czpmsutils.HotelFileServerUtil;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.PlatConstant;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.GetRoomInfoForIdRequest;
import com.pms.czpmsutils.request.RoomCheckRecordRequest;
import com.pms.czpmsutils.request.RoomCheckRequest;
import com.pms.czpmsutils.third.IotPlatUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 查询所有房型信息
 */
@Slf4j
@Controller
@RequestMapping("/hotel/room/")
public class RoomController {


    @Autowired
    private RoomService roomService;

    @Autowired
    private IotPlatUtil iotPlatUtil;

    @Resource
    private RoomImportListener<RoomImportBean> roomImportListener;



    @Autowired
    private HotelFileServerUtil hotelFileServerUtil;
    @Autowired
    private RoomServiceImpl roomServiceImpl;

    @RequestMapping("findAllRoom.do")
    @ResponseBody
    public ResponseData findAllRoomType(@RequestBody JSONObject param) {
        return roomService.findAllRoom(param);
    }

    @RequestMapping("findHotelRoomInfo.do")
    @ResponseBody
    public ResponseData findHotelRoomInfo(@RequestBody RoomInfoSearch roomInfoSearch) {
        return roomService.findHotelRoomInfo(roomInfoSearch);
    }

    @RequestMapping("iotFindRoomInfo")
    @ResponseBody
    public ResponseData iotFindRoomInfo(@RequestBody RoomInfoSearch roomInfoSearch) {
        return roomService.iotFindRoomInfo(roomInfoSearch);
    }


    @RequestMapping("findAllHotelRoomInfo.do")
    @ResponseBody
    public ResponseData findAllHotelRoomInfo(@RequestHeader("hotelId") String hotelId) {
        return roomService.findAllHotelRoomInfo(hotelId);
    }


    @RequestMapping("/aizhi/pullingHotelRoomInfo.do")
    @ResponseBody
    public ResponseData aizhiPullingHotelRoomInfo(@RequestHeader("hotelId") String hotelId) {
        //艾智请求拉取酒店房间信息，这里传的是艾智的酒店Id，需要请求iot做一次ID映射转换
        String innerHotelId = iotPlatUtil.getMappingHotelId(PlatConstant.AIZHI_PLAT,
                PlatConstant.IOT_PMS_PLAT,
                hotelId
        );
        log.info("艾智拉取酒店房间映射信息，原始hotelId:{},映射hotelId:{}",hotelId,innerHotelId);
        if (StringUtils.isEmpty(innerHotelId)) {
            return ResponseData.fail("酒店尚未同步！");
        }
        return roomService.findAllHotelRoomInfo(innerHotelId);
    }



    @RequestMapping("findRoomStateNum.do")
    @ResponseBody
    public ResponseData findRoomStateNum(@RequestBody JSONObject param) {
        return roomService.findRoomStateNum(param);
    }

    @RequestMapping("findRoomByRoomType.do")
    @ResponseBody
    public ResponseData findRoomByRoomType(@RequestBody JSONObject param) {
        return roomService.findRoomByRoomType(param);

    }

    @RequestMapping("findRoomFloor.do")
    @ResponseBody
    public ResponseData findRoomFloor(@RequestBody JSONObject param) {
        return roomService.findRoomFloor(param);

    }

    @RequestMapping("addOrUpadteRoom.do")
    @ResponseBody
    public ResponseData addOrUpadteRoom(@RequestBody JSONObject param) {
        return roomService.addOrUpadteRoom(param);
    }

    @RequestMapping("updateRoomInfoRemark.do")
    @ResponseBody
    public ResponseData updateRoomInfoRemark(@RequestBody JSONObject param) {
        return roomService.updateRoomInfoRemark(param);
    }

    @RequestMapping("addRooms.do")
    @ResponseBody
    public ResponseData addRooms(@RequestBody JSONObject param) {
        return roomService.addRooms(param);
    }

    @RequestMapping("findRoomAuxiliaryByHid.do")
    @ResponseBody
    public ResponseData findRoomAuxiliaryByHid(@RequestBody JSONObject param) {
        return roomService.findRoomAuxiliaryByHid(param);
    }

    @RequestMapping("findRoomByNoOrId.do")
    @ResponseBody
    public ResponseData findRoomByNoOrId(@RequestBody JSONObject param) {
        return roomService.findRoomByNoOrId(param);
    }


    @RequestMapping("updateRoomsState.do")
    @ResponseBody
    public ResponseData updateRoomsState(@RequestBody JSONObject param) {

        return roomService.updateRoomsState(param);
    }

    @RequestMapping("searchRoomRepairRecordHistory.do")
    @ResponseBody
    public ResponseData searchRoomRepairRecordHistory(@RequestBody JSONObject param) {

        return roomService.searchRoomRepairRecordHistory(param);
    }

    @RequestMapping("findRoomFeature.do")
    @ResponseBody
    public ResponseData findRoomFeature(@RequestBody JSONObject param) {
        return roomService.findRoomFeature(param);
    }

    @RequestMapping("addOrUpdateRoomCheckRecord.do")
    @ResponseBody
    public ResponseData addOrUpdateRoomCheckRecord(@RequestBody RoomCheckRecordRequest roomCheckRecordRequest) {
        return roomService.addOrUpdateRoomCheckRecord(roomCheckRecordRequest);
    }

    /**
     * 获取查房人列表
     * @param sessionToken
     * @return
     */
    @GetMapping("roomCheckerList")
    @ResponseBody
    public ResponseData roomCheckerList(@RequestParam("sessionToken") String sessionToken) {
        return roomService.roomCheckerList(sessionToken);
    }

    /**
     * 添加或修改查房任务
     * @param roomCheckRequest
     * @return
     */
    @RequestMapping("addOrUpdateRoomCheck")
    @ResponseBody
    public ResponseData addOrUpdateRoomCheck(@RequestBody RoomCheckRequest roomCheckRequest) {
        return roomService.addOrUpdateRoomCheck(roomCheckRequest);
    }

    @RequestMapping("getRoomCheckRecordList.do")
    @ResponseBody
    public ResponseData getRoomCheckRecordList(@RequestBody RoomCheckRecordSearch roomCheckRecordSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Page<RoomCheckRecord> roomCheckRecordList = roomService.getRoomCheckRecordList(roomCheckRecordSearch);
            responseData.setData(roomCheckRecordList);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("findRoomInfoQr.do")
    @ResponseBody
    public ResponseData findRoomInfoQr(@RequestBody RoomInfoQrSearch roomInfoQrSearch) {
        return roomService.findRoomInfoQr(roomInfoQrSearch);
    }

    @RequestMapping("addOrUpdateRoomInfoQr.do")
    @ResponseBody
    public ResponseData addOrUpdateRoomInfoQr(@RequestBody RoomInfoQr roomInfoQr) {
        return roomService.addOrUpdateRoomInfoQr(roomInfoQr);
    }


    @RequestMapping("addRoomInfoList.do")
    @ResponseBody
    public ResponseData addRoomInfoList(@RequestBody AddRoomInfoListRequest roomInfoListRequest) {
        return roomService.addRoomInfoList(roomInfoListRequest);
    }

    @RequestMapping("getRoomInfoForId.do")
    @ResponseBody
    public ResponseData getRoomInfoForId(@RequestBody GetRoomInfoForIdRequest getRoomInfoForIdRequest) {
        return roomService.getRoomInfoForId(getRoomInfoForIdRequest);
    }


    /**
     * 批量导入房间
     * @param request
     * @return
     */
    @RequestMapping("batchImportRoomInfo.do")
    @ResponseBody
    public ResponseData batchImportRoomInfo(@RequestBody BatchAddApplyInfoRecordRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        String downloadFilePath = request.getFilePath();
        if (null == downloadFilePath || downloadFilePath.isEmpty()) {
            responseData.setResult(ER.ERR);
            responseData.setMsg("文件下载地址有误");
            return responseData;
        }
        List<RoomImportBean> roomImportBeanList = new ArrayList<>();
        try {
            final TbUserSession user =  roomServiceImpl.getTbUserSession(request.getSessionToken());
            // 1. 下载模板文件，解析
            File file = hotelFileServerUtil.downloadFile(request.getFilePath());
            // 2. 解析文件，获取酒店，用户数据
            log.info("开始解析excel文件内容");
            ExcelReader excelReader = EasyExcel.read(file, RoomImportBean.class, roomImportListener)
                    .excelType(ExcelTypeEnum.XLSX).build();
            excelReader.readAll();
            if (StringUtils.isNotBlank(RoomImportListener.errorMsg.toString())) {
                log.info("excel文件内容有错误");
                responseData.setResult(ER.ERR);
                responseData.setMsg(RoomImportListener.errorMsg.toString());
                RoomImportListener.errorMsg.setLength(0);
                //清空监听器中容器
                excelReader.finish();
                excelReader.close();
                roomImportListener.setHeaderValidated();
                roomImportListener.clearDataList();
                return responseData;
            }
            roomImportBeanList.addAll(roomImportListener.getDataList());
            log.info("roomImportBeanListL{}",roomImportBeanList);
            if (roomImportBeanList.isEmpty()) {
                log.info("excel文件内容为空");
                responseData.setResult(ER.ERR);
                responseData.setMsg("文件中没有房间数据");
                //清空监听器中容器
                excelReader.finish();
                excelReader.close();
                roomImportListener.setHeaderValidated();
                roomImportListener.clearDataList();
                return responseData;
            }
            //清空监听器中容器
            excelReader.finish();
            excelReader.close();
            roomImportListener.setHeaderValidated();
            roomImportListener.clearDataList();
            ResponseData importResponseData = roomService.addImportRooms(roomImportBeanList, user);
            if(importResponseData.getCode() == -1){
                responseData.setResult(ER.ERR);
                responseData.setMsg(importResponseData.getMsg());
                //清空监听器中容器
                excelReader.finish();
                excelReader.close();
                roomImportListener.setHeaderValidated();
                roomImportListener.clearDataList();
                return responseData;
            }
        }catch (Exception e) {
            log.error("导入房间失败:{}", e.getMessage(), e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            roomImportListener.clearDataList();
        }
        return responseData;
    }
}
