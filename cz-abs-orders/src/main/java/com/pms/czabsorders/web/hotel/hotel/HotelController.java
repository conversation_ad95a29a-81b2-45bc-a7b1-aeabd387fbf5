package com.pms.czabsorders.web.hotel.hotel;


import com.pms.czhotelfoundation.bean.hotel.search.HotelBaseInfoSearch;
import com.pms.czhotelfoundation.bean.hotel.search.HotelGroupInfoSearch;
import com.pms.czhotelfoundation.service.hotel.HotelService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.*;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 房价牌以及酒店相关业务
 */
@Controller
@RequestMapping("/hotel/baseinfo/")
public class HotelController {

    @Autowired
    private HotelService hotelService;


    /**
     * 查询当前自助机日志
     *
     * @param hotelBaseInfoSearch
     * @return
     */
    @RequestMapping("findAllHotel.do")
    @ResponseBody
    public ResponseData findAllHotel(@RequestBody HotelBaseInfoSearch hotelBaseInfoSearch) {
        return hotelService.findAllHotel(hotelBaseInfoSearch);
    }

    /**
     * 获取集团信息
     * @param hotelGroupInfoSearch
     * @return
     */
    @RequestMapping("findHotelGroupInfo.do")
    @ResponseBody
    public ResponseData findHotelGroupInfo(@RequestBody HotelGroupInfoSearch hotelGroupInfoSearch) {
        return hotelService.findHotelGroupInfo(hotelGroupInfoSearch);
    }

    /**
     * 获取当前省市县
     *
     * @param param
     * @return
     */
    @RequestMapping("proviceAndCityAnaArea.do")
    @ResponseBody
    public ResponseData proviceAndCityAnaArea(@RequestBody JSONObject param) {

        return hotelService.proviceAndCityAnaArea(param);

    }

    /**
     * 添加或修改酒店
     *
     * @param request
     * @return
     */
    @RequestMapping("addOrUpdateHotel.do")
    @ResponseBody
    public ResponseData addOrUpdateHotel(@RequestBody JSONObject request) {

        return hotelService.addOrUpdateHotel(request);

    }


    /**
     * 根据酒店 hid 获取酒店信息
     *
     * @param request
     * @return
     */
    @RequestMapping("searchHotelInfo.do")
    @ResponseBody
    public ResponseData searchHotelInfo(@RequestBody JSONObject request) {
        return hotelService.findHotelMsgByHid(request);
    }

    @RequestMapping("findHotelImage.do")
    @ResponseBody
    public ResponseData findHotelImage(@RequestBody BaseRequest baseRequest) {
        return hotelService.findHotelImage(baseRequest);
    }


    /**
     * 根据酒店 hid 获取酒店营业日期
     *
     * @param request
     * @return
     */
    @RequestMapping("findHotelBusinessDay.do")
    @ResponseBody
    public ResponseData findHotelBusinessDay(@RequestBody JSONObject request) {

        return hotelService.findHotelBusinessDay(request);

    }

    /**
     * 根据酒店查询当前酒店的交班模式
     *
     * @param param
     * @return
     */
    @RequestMapping("searchHotelClassInfo.do")
    @ResponseBody
    public ResponseData findHotelClassInfo(@RequestBody JSONObject param) {
        return hotelService.findHotelClassInfo(param);
    }


    /**
     * 根据酒店修改当前酒店的交班模式
     *
     * @param request
     * @return
     */
    @RequestMapping("addOrUpdateHotelClassInfo.do")
    @ResponseBody
    public ResponseData addOrUpdateHotelClassInfo(@RequestBody JSONObject request) {

        return hotelService.addOrUpdateHotelClassInfo(request);

    }


    /**
     * 获取夜审类型数据
     *
     * @param request
     * @return
     */
    @RequestMapping("findHotelNightType.do")
    @ResponseBody
    public ResponseData findHotelNightType(@RequestBody JSONObject request) {

        return hotelService.findHotelNightType(request);

    }


    /**
     * 修改或者添加夜审类型数据
     *
     * @param request
     * @return
     */
    @RequestMapping("addOrUpdateHotelNightType.do")
    @ResponseBody
    public ResponseData addOrUpdateHotelNightType(@RequestBody JSONObject request) {

        return hotelService.addOrUpdateHotelNightType(request);

    }


    /**
     * 查询上次交班的信息
     *
     * @param param
     * @return
     */
    @RequestMapping("saveZhiwen.do")
    @ResponseBody
    public ResponseData saveZhiwen(@RequestBody JSONObject param) {
        return hotelService.saveZhiwen(param);

    }

    /**
     * 查询上次交班的信息
     *
     * @return
     */
    @RequestMapping("searchZhiwen.do")
    @ResponseBody
    public ResponseData searchZhiwen() {
        return hotelService.searchZhiwen();
    }

    /**
     * 查询上次交班的信息
     *
     * @return
     */
    @RequestMapping("dayDiff.do")
    @ResponseBody
    public ResponseData dayDiff(@RequestBody JSONObject param) {
        return hotelService.dayDiff(param);
    }


    /**
     * 修改或者更新集团信息
     * @param request
     * @return
     */
    @RequestMapping("addOrUpdateHotelGroupInfo.do")
    @ResponseBody
    public ResponseData addOrUpdateHotelGroupInfo(@RequestBody HotelGroupInfoRequest request) {

        return hotelService.addOrUpdateHotelGroupInfo(request);

    }

    /**
     * 根据集团id查询集团信息
     * @param getHotelGroupInfoRequest
     * @return
     */
    @RequestMapping("getHotelGroupInfo.do")
    @ResponseBody
    public ResponseData getHotelGroupInfo(@RequestBody GetHotelGroupInfoRequest getHotelGroupInfoRequest) {

        return hotelService.getHotelGroupInfo(getHotelGroupInfoRequest);

    }


    /**
     * 房价牌相关业务处理
     * @param addOrUpdateHotelOrgPriceRequest
     * @return
     */
    @RequestMapping("addOrUpdateHotelOrgPrice.do")
    @ResponseBody
    public ResponseData addOrUpdateHotelOrgPrice(@RequestBody AddOrUpdateHotelOrgPriceRequest addOrUpdateHotelOrgPriceRequest) {

        return hotelService.addOrUpdateHotelOrgPrice(addOrUpdateHotelOrgPriceRequest);

    }


    /**
     * 获取酒店价格牌信息
     * @param baseRequest
     * @return
     */
    @RequestMapping("getHotelOrgPrice.do")
    @ResponseBody
    public ResponseData getHotelOrgPrice(@RequestBody BaseRequest baseRequest) {

        return hotelService.getHotelOrgPrice(baseRequest);

    }

    /**
     * 获取酒店价格牌图片
     * @param getHotelOrgPriceImageRuquest
     * @return
     */
    @RequestMapping("getHotelOrgPriceImageDao.do")
    @ResponseBody
    public ResponseData getHotelOrgPriceImage(@RequestBody GetHotelOrgPriceImageRuquest getHotelOrgPriceImageRuquest) {
        return hotelService.getHotelOrgPriceImage(getHotelOrgPriceImageRuquest);
    }


    /**
     * 获取酒店价格牌展示信息
     * @param baseRequest
     * @return
     */
    @RequestMapping("getHotelOrgPriceShowInfo.do")
    @ResponseBody
    public ResponseData getHotelOrgPriceShowInfo(@RequestBody BaseRequest baseRequest) {
        return hotelService.getHotelOrgPriceShowInfo(baseRequest);
    }


}
