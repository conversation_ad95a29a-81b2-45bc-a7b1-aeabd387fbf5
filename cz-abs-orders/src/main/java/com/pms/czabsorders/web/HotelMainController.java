package com.pms.czabsorders.web;

import com.pms.czabsorders.service.main.HotelMainService;
import com.pms.czhotelfoundation.bean.hotel.CommonFunctionsSettingDto;
import com.pms.czhotelfoundation.bean.hotel.HotelBaseInfo;
import com.pms.czhotelfoundation.bean.hotel.search.HotelShiftRecordSearch;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.BaseRequest;
import com.pms.czpmsutils.request.HotelShiftRecordRequest;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping("/hotel/main/")
public class HotelMainController {

    @Autowired
    private HotelMainService hotelMainService;

    @PostMapping("dataOverview")
    @ResponseBody
    public ResponseData dataOverview(@RequestBody BaseRequest baseRequest) {
        return hotelMainService.dataOverview(baseRequest);
    }

    @PostMapping("realTimeData")
    @ResponseBody
    public ResponseData realTimeData(@RequestBody BaseRequest baseRequest) {
        ResponseData responseData = hotelMainService.realTimeData(baseRequest);
        return responseData;
    }


    @PostMapping("newNote")
    @ResponseBody
    public ResponseData newNote(@RequestBody HotelShiftRecordRequest hotelShiftRecordRequest) {
        ResponseData responseData = hotelMainService.newNote(hotelShiftRecordRequest);
        return responseData;
    }

    @PostMapping("classNoteList")
    @ResponseBody
    public ResponseData classNoteList(@RequestBody HotelShiftRecordSearch hotelShiftRecordSearch) {
        ResponseData responseData = hotelMainService.classNoteList(hotelShiftRecordSearch);
        return responseData;
    }

    @PostMapping("hotelBanner")
    @ResponseBody
    public ResponseData hotelBanner() {
        ResponseData responseData = hotelMainService.hotelBanner();
        return responseData;
    }

    @PostMapping("currentOccupancyRate")
    @ResponseBody
    public ResponseData currentOccupancyRate(@RequestBody HotelBaseInfo hotelBaseInfo) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        responseData.setData(hotelMainService.currentOccupancyRate(hotelBaseInfo.getHid()));
        return responseData;
    }

    @PostMapping("yesterdayOccupancyRate")
    @ResponseBody
    public ResponseData yesterdayOccupancyRate(@RequestBody HotelBaseInfo hotelBaseInfo) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        responseData.setData(hotelMainService.yesterdayOccupancyRate(hotelBaseInfo.getHid()));
        return responseData;
    }

    @PostMapping("roomState")
    @ResponseBody
    public ResponseData roomState(@RequestBody BaseRequest baseRequest) {
        return hotelMainService.roomState(baseRequest);
    }


    @PostMapping("commonFunctions")
    @ResponseBody
    public ResponseData commonFunctions(@RequestBody BaseRequest baseRequest) {
        return hotelMainService.commonFunctions(baseRequest);
    }

    @PostMapping("commonFunctions/setting")
    @ResponseBody
    public ResponseData commonFunctionsSetting(@RequestBody CommonFunctionsSettingDto commonFunctionsSettingDto) {
        return hotelMainService.commonFunctionsSetting(commonFunctionsSettingDto);
    }

}
