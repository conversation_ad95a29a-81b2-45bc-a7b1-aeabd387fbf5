package com.pms.czabsorders.web.hotel.hotel;

import com.pms.czhotelfoundation.bean.jjb.search.HotelChangeShiftsSearch;
import com.pms.czhotelfoundation.service.jjb.ChangeShiftsService;
import com.pms.czpmsutils.ResponseData;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/baseinfo/")
public class ChangeShiftsController {

    @Autowired
    private ChangeShiftsService changeShiftsService;


    /**
     * 查询当前自助机日志
     * @param param
     * @return
     */
    @RequestMapping("addChangeShifts.do")
    @ResponseBody
    public ResponseData addChangeShifts(@RequestBody JSONObject param){
        return changeShiftsService.addChangeShifts(param);

    }

    /**
     * 查询上次交班的信息
     * @param param
     * @return
     */
    @RequestMapping("searchLastShiftsDetails.do")
    @ResponseBody
    public ResponseData searchLastShiftsDetails(@RequestBody JSONObject param){
        return changeShiftsService.searchLastShiftsDetails(param);

    }

    /**
     * 查询上次交班的信息
     * @param param
     * @return
     */
    @RequestMapping("searchShiftsDetails.do")
    @ResponseBody
    public ResponseData searchShiftsDetails(@RequestBody HotelChangeShiftsSearch param){
        return changeShiftsService.searchShiftsDetails(param);

    }

    /**
     * 查询上次交班的信息
     * @param param
     * @return
     */
    @RequestMapping("searchShiftsDetailsMsg.do")
    @ResponseBody
    public ResponseData searchShiftsDetailsMsg(@RequestBody JSONObject param){
        return changeShiftsService.searchShiftsDetailsMsg(param);

    }
}
