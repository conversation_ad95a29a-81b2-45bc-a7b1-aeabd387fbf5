package com.pms.czabsorders.web;


import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/Subscribe/")
@Slf4j
public class FaceMachineController {


    ///Subscribe/Verify

    @RequestMapping("Verify")
    @ResponseBody
    public JSONObject Verify(@RequestBody JSONObject param){
        log.info("param={}",param.toString());
        ResponseData responseData = new ResponseData(ER.SUCC);
        responseData.setData(param);
        String a = "{\n" +
                "    \"operator\": \"Verify\",\n" +
                "    \"code\": 200,\n" +
                "    \"info\": {\n" +
                "        \"Result\": \"Ok\"\n" +
                "    }\n" +
                "}";
        return  JSONObject.fromObject(a);
    }

    @RequestMapping("Snap")
    @ResponseBody
    public ResponseData Snap(@RequestBody JSONObject param){
        ResponseData responseData = new ResponseData(ER.SUCC);
        responseData.setData(param);
        return  responseData;
    }
}
