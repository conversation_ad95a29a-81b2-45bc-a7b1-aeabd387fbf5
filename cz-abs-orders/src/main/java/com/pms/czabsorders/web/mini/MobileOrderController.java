package com.pms.czabsorders.web.mini;

import com.pms.czabsorders.service.mini.MobileOrderService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.GetMobileGroupHotelListRequest;
import com.pms.czpmsutils.request.GetMobileHotelRoomTypeListRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/minipro/")
public class MobileOrderController {

    @Autowired
    MobileOrderService mobileOrderService;

    @RequestMapping("getMobileGroupHotelList.do")
    @ResponseBody
    public ResponseData getMobileGroupHotelList(@RequestBody GetMobileGroupHotelListRequest getMobileGroupHotelListRequest) {
        return mobileOrderService.getMobileGroupHotelList(getMobileGroupHotelListRequest);
    }


    @RequestMapping("getMobileHotelRoomTypeList.do")
    @ResponseBody
    public ResponseData getMobileHotelRoomTypeList(@RequestBody GetMobileHotelRoomTypeListRequest getMobileHotelRoomTypeListRequest) {
        return mobileOrderService.getMobileHotelRoomTypeList(getMobileHotelRoomTypeListRequest);
    }
}
