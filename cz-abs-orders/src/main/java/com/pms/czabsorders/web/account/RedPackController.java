package com.pms.czabsorders.web.account;


import com.pms.czaccount.service.redpack.RedPackService;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Controller
@RequestMapping("/hotel/redpack/")
public class RedPackController {

    @Autowired
    private RedPackService redPackService;


    /**
     *  添加或修改红包设置
     * @param request
     * @return
     */
    @RequestMapping("updateRedPackSetting.do")
    @ResponseBody
    public Map<String,Object> updateRedPackSetting(@RequestBody JSONObject map ){
        return redPackService.updateRedPackSetting(map);
    }

    /**
     *  查询红包设置
     * @param request
     * @return
     */
    @RequestMapping("searchRedPackSetting.do")
    @ResponseBody
    public Map<String,Object> searchRedPackSetting(@RequestBody JSONObject map ){
        return redPackService.searchRedPackSetting(map);
    }

}
