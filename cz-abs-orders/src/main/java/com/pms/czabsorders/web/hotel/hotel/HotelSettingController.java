package com.pms.czabsorders.web.hotel.hotel;

import com.pms.czhotelfoundation.bean.code.DeliveryRobot;
import com.pms.czhotelfoundation.bean.code.HotelGate;
import com.pms.czhotelfoundation.bean.code.search.DeliveryRobotSearch;
import com.pms.czhotelfoundation.bean.code.search.HotelGateSearch;
import com.pms.czhotelfoundation.bean.hotel.search.HotelGoodsManagementSearch;
import com.pms.czhotelfoundation.bean.hotel.search.HotelMiniproSettingSearch;
import com.pms.czhotelfoundation.bean.request.HotelMiniproSettingRequest;
import com.pms.czhotelfoundation.bean.request.HotelPrintSettingRequest;
import com.pms.czhotelfoundation.bean.setting.search.HotelFactoryParamTypeSearch;
import com.pms.czhotelfoundation.bean.setting.search.HotelInitialDataSearch;
import com.pms.czhotelfoundation.bean.setting.search.HotelPrintSettingSearch;
import com.pms.czhotelfoundation.service.hotel.HotelService;
import com.pms.czhotelfoundation.service.hotelsetting.HotelInitialDataService;
import com.pms.czhotelfoundation.service.hotelsetting.HotelSettingService;
import com.pms.czhotelfoundation.service.zimg.ZimgService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 酒店设置
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/hotel/setting/")
@Slf4j
public class HotelSettingController {

    @Autowired
    private HotelSettingService hotelSettingService;

    @Autowired
    private HotelInitialDataService hotelInitialDataService;
    @Autowired
    private HotelService hotelService;

    @Autowired
    private ZimgService zimgService;

    /**
     * 查询所有设置
     *
     * @param param
     * @return
     */
    @RequestMapping("getAllSetting.do")
    @ResponseBody
    public ResponseData getAllSetting(@RequestBody JSONObject param) {
        return hotelSettingService.getAllSetting(param);
    }

    /**
     * 根据id 查询对应的设置信息
     *
     * @param hotelSettingByParamId
     * @return
     */
    @RequestMapping("findHotelSettingByParamId")
    @ResponseBody
    public Object findHotelSettingByParamId(@RequestBody HotelSettingByParamId hotelSettingByParamId) {
        return hotelSettingService.findHotelSettingByParamId(hotelSettingByParamId);

    }

    /**
     * 查询对应的设置信息
     *
     * @param hotelSettingByParamId
     * @return
     */
    @RequestMapping("findHotelPrintSettingMap")
    @ResponseBody
    public JSONObject findHotelPrintSettingMap(@RequestBody HotelSettingByParamId hotelSettingByParamId) {
        return hotelSettingService.findHotelPrintSettingMap(hotelSettingByParamId);

    }


    /**
     * 修改设置的值
     * <br/>1.paramId:设置id
     * <br/>2.paramValue:设置的值
     *
     * @param request
     * @return
     */
    @RequestMapping("updateSetting.do")
    @ResponseBody
    public ResponseData updateSetting(@RequestBody JSONObject request) {
        return hotelSettingService.updateSetting(request);
    }


    /**
     * 查询酒店基础数据
     *
     * @return
     */
    @RequestMapping("getAllHotelInitialData.do")
    @ResponseBody
    public ResponseData getAllHotelInitialData(@RequestBody JSONObject map) {
        return hotelInitialDataService.getAllHotelInitailData(map);

    }

    /**
     * 查询酒店基础数据 分页
     *
     * @param hotelInitialDataSearch
     * @return
     */
    @RequestMapping("getHotelInitailData.do")
    @ResponseBody
    public ResponseData getHotelInitailData(@RequestBody HotelInitialDataSearch hotelInitialDataSearch) {
        return hotelInitialDataService.getHotelInitailData(hotelInitialDataSearch);
    }


    /**
     * 查询楼层楼栋
     *
     * @return
     */
    @RequestMapping("findBulidFloor.do")
    @ResponseBody
    public ResponseData findBulidFloor(@RequestBody JSONObject map) {
        return hotelInitialDataService.findBulidFloor(map);

    }

    /**
     * 修改或添加酒店基础数据
     *
     * @return
     */
    @RequestMapping("addOrUpdataInitailData.do")
    @ResponseBody
    public ResponseData addOrUpdataInitailData(@RequestBody JSONObject map) {

        return hotelInitialDataService.addOrUpdataInitailData(map);

    }

    /**
     * 查询所有接口地址
     * @param param
     * @return
     */
    @RequestMapping("findAllApiUrl.do")
    @ResponseBody
    public ResponseData findAllApiUrl(@RequestBody JSONObject param) {

        return hotelSettingService.findAllApiUrl(param);

    }


    /**
     * 添加或者删除钟点房信息
     *
     * @param hourRoomInfoRequest
     * @return
     */
    @RequestMapping("saveOrUpdateHourRoomInfo.do")
    @ResponseBody
    public ResponseData saveOrUpdateHourRoomInfo(@RequestBody HourRoomInfoRequest hourRoomInfoRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(hotelService.saveOrUpdateHourRoomInfo(hourRoomInfoRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 删除钟点房信息
     *
     * @param hourRoomInfoRequest
     * @return
     */
    @RequestMapping("deleteHourRoomInfo.do")
    @ResponseBody
    public ResponseData deleteHourRoomInfo(@RequestBody HourRoomInfoRequest hourRoomInfoRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            hotelService.deleteHourRoomInfo(hourRoomInfoRequest);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 分页查询钟点房信息
     *
     * @param hourRoomInfoSearch
     * @return
     */
    @RequestMapping("searchHourRoomInfoList.do")
    @ResponseBody
    public ResponseData searchHourRoomInfoList(@RequestBody HourRoomInfoSearch hourRoomInfoSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(hotelService.searchHourRoomInfoList(hourRoomInfoSearch));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 分页查询钟点房信息
     *
     * @param baseRequest
     * @return
     */
    @RequestMapping("searchHotelHourRoomTypeInfoList.do")
    @ResponseBody
    public ResponseData searchHotelHourRoomTypeInfoList(@RequestBody BaseRequest baseRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(hotelService.searchHotelHourRoomTypeInfoList(baseRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 查询用户入住状态
     * @param hotelUserRoomStateRequest
     * @return
     */
    @RequestMapping("searchHotelUserRoomState.do")
    @ResponseBody
    public ResponseData searchHotelUserRoomState(@RequestBody HotelUserRoomStateRequest hotelUserRoomStateRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(hotelService.searchHotelUserRoomState(hotelUserRoomStateRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 添加或者修改用户入住状态
     * @param hotelUserRoomStateRequest
     * @return
     */
    @RequestMapping("saveOrUpdateHotelUserRoomState.do")
    @ResponseBody
    public ResponseData saveOrUpdateHotelUserRoomState(@RequestBody HotelUserRoomStateRequest hotelUserRoomStateRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(hotelService.saveOrUpdateHotelUserRoomState(hotelUserRoomStateRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 分页查询钟点房类型
     * @param hotelHourRoomTypeRequest
     * @return
     */
    @RequestMapping("searchHotelHourRoomTypeList.do")
    @ResponseBody
    public ResponseData searchHotelHourRoomTypeList(@RequestBody HotelHourRoomTypeRequest hotelHourRoomTypeRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(hotelService.searchHotelHourRoomTypeList(hotelHourRoomTypeRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 添加或者修改钟点房类型
     * @param hotelHourRoomTypeRequest
     * @return
     */
    @RequestMapping("saveOrUpdateHotelHourRoomType.do")
    @ResponseBody
    public ResponseData saveOrUpdateHotelHourRoomType(@RequestBody HotelHourRoomTypeRequest hotelHourRoomTypeRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(hotelService.saveOrUpdateHotelHourRoomType(hotelHourRoomTypeRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 分页查询商品管理信息
     * @param hotelGoodsManagementSearch
     * @return
     */
    @RequestMapping("searchHotelGoodsManagementList.do")
    @ResponseBody
    public ResponseData searchHotelGoodsManagementList(@RequestBody HotelGoodsManagementSearch hotelGoodsManagementSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(hotelService.searchHotelGoodsManagementList(hotelGoodsManagementSearch));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 添加或者修改商品管理信息
     * @param hotelGoodsManagementRequest
     * @return
     */
    @RequestMapping("saveOrUpdateHotelGoodsManagement.do")
    @ResponseBody
    public ResponseData saveOrUpdateHotelGoodsManagement(@RequestBody HotelGoodsManagementRequest hotelGoodsManagementRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(hotelService.saveOrUpdateHotelGoodsManagement(hotelGoodsManagementRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 查询酒店打印设置
     * @param hotelPrintSettingSearch
     * @return
     */
    @RequestMapping("findHotelPrintSetting.do")
    @ResponseBody
    public ResponseData findHotelPrintSetting(@RequestBody HotelPrintSettingSearch hotelPrintSettingSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(hotelSettingService.findHotelPrintSetting(hotelPrintSettingSearch));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 修改酒店打印设置
     * @param hotelPrintSettingRequest
     * @return
     */

    @RequestMapping("updateHotelPrintSetting.do")
    @ResponseBody
    public ResponseData updateHotelPrintSetting(@RequestBody HotelPrintSettingRequest hotelPrintSettingRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(hotelSettingService.updateHotelPrintSetting(hotelPrintSettingRequest));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 查询小程序设置
     * @param hotelMiniproSettingSearch
     * @return
     */
    @RequestMapping("findWxHotelSetting.do")
    @ResponseBody
    public ResponseData findWxHotelSetting(@RequestBody HotelMiniproSettingSearch hotelMiniproSettingSearch) {
        return hotelSettingService.findWxHotelSetting(hotelMiniproSettingSearch);
    }

    /**
     * 修改小程序设置
     * @param hotelMiniproSettingRequest
     * @return
     */
    @RequestMapping("updateWxHotelSetting.do")
    @ResponseBody
    public ResponseData updateWxHotelSetting(@RequestBody HotelMiniproSettingRequest hotelMiniproSettingRequest) {
        return hotelSettingService.updateWxHotelSetting(hotelMiniproSettingRequest);
    }


    /**
     * 上传base64 格式 图片
     *
     * @return
     */
    @RequestMapping("uploadBaseImg.do")
    @ResponseBody
    public JSONObject getAlipayQrcode(@RequestBody JSONObject param) {

        String s = zimgService.uplaodImage(param);


        JSONObject jsonObject = new JSONObject();

        jsonObject.put(ER.RES, s == null ? ER.ERR : ER.SUCC);
        jsonObject.put("md5", s);
        return jsonObject;
    }


    /**
     * 查询配送机器人
     * @param deliveryRobotSearch
     * @return
     */
    @RequestMapping("findDeliveryRobot.do")
    @ResponseBody
    public ResponseData findDeliveryRobot(@RequestBody DeliveryRobotSearch deliveryRobotSearch) {
        return hotelService.findDeliveryRobot(deliveryRobotSearch);
    }


    /**
     * 修改配送机器人
     * @param deliveryRobot
     * @return
     */
    @RequestMapping("updateDataDeliveryRobot.do")
    @ResponseBody
    public ResponseData updateDataDeliveryRobot(@RequestBody DeliveryRobot deliveryRobot) {
        return hotelService.updateDataDeliveryRobot(deliveryRobot);
    }


    /**
     * 查询酒店闸机
     * @param hotelGateSearch
     * @return
     */
    @RequestMapping("findHotelGate.do")
    @ResponseBody
    public ResponseData findHotelGate(@RequestBody HotelGateSearch hotelGateSearch) {
        return hotelService.findHotelGate(hotelGateSearch);
    }


    /**
     * 修改酒店闸机
     * @param hotelGate
     * @return
     */
    @RequestMapping("updateHotelGate.do")
    @ResponseBody
    public ResponseData updateHotelGate(@RequestBody HotelGate hotelGate) {
        return hotelService.updateHotelGate(hotelGate);
    }


    /**
     * 查询酒店初始化数据
     * @param hotelFactoryParamTypeSearch
     * @return
     */
    @RequestMapping("getHotelFactoryParam.do")
    @ResponseBody
    public ResponseData getHotelFactoryParam(@RequestBody HotelFactoryParamTypeSearch hotelFactoryParamTypeSearch) {
        return hotelInitialDataService.getHotelFactoryParam(hotelFactoryParamTypeSearch);
    }
}
