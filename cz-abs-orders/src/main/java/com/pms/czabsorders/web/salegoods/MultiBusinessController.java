package com.pms.czabsorders.web.salegoods;


import com.pms.czabsorders.service.salegoods.SalegoodsService;
import com.pms.czpmsutils.ResponseData;
import com.pms.pmswarehouse.bean.search.HotelSellGoodsDto;
import com.pms.pmswarehouse.bean.search.OrderRoomChargeDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @Author: 陈星宇
 * @CreateTime: 2025-05-22
 * @Description:
 */

@Controller
@RequestMapping("/hotel/multi-business/")
@Slf4j
public class MultiBusinessController {


    @Autowired
    private SalegoodsService salegoodsService;

    @RequestMapping("order-room-charge")
    @ResponseBody
    public ResponseData orderRoomCharge(@RequestBody OrderRoomChargeDto orderRoomChargeDto) {
        return salegoodsService.orderRoomCharge(orderRoomChargeDto);
    }


    @RequestMapping("hotSellGoods")
    @ResponseBody
    public ResponseData hotSellGoods(@RequestBody HotelSellGoodsDto hotelSellGoodsDto) {
        return salegoodsService.hotSellGoods(hotelSellGoodsDto);
    }

}
