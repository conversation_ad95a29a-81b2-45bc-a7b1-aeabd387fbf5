package com.pms.czabsorders.web;

import com.pms.czabsorders.bean.FindAvailableHourRoomRequest;
import com.pms.czabsorders.service.turnAlways.TurnAlwaysService;
import com.pms.czhotelfoundation.bean.search.HourRoomDayUseSearch;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.AvailableRoom;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

@Controller
@RequestMapping("/hotel/turanAlways")
public class TurnAlwaysController {

    @Autowired
    private TurnAlwaysService turnAlwaysService;

    @RequestMapping("turnAlways.do")
    @ResponseBody
    public Map<String, Object> turnAlways(@RequestBody JSONObject param) {
        Map<String, Object> login = turnAlwaysService.turnAlways(param);

        return login;
    }

    @RequestMapping("hourTurnAlways.do")
    @ResponseBody
    public ResponseData hourTurnAlways(@RequestBody HourRoomDayUseSearch param) {
        return turnAlwaysService.hourTurnAlways(param);
    }

    @RequestMapping("turnAlwaysCache")
    @ResponseBody
    public void turnAlwaysCache(@RequestBody TbUserSession userSession){
        turnAlwaysService.turnAlwaysCacheFunc(userSession);
    }

    @RequestMapping("findavailableRoom.do")
    @ResponseBody
    public Map<String, Object> findavailableRoom(@RequestBody JSONObject param) {
        Map<String, Object> login = turnAlwaysService.findavailableRoom(param);
        return login;
    }

    @RequestMapping("findavailableRoom")
    @ResponseBody
    public Map<String,Object> findavailableRoom(@RequestBody AvailableRoom availableRoom){
      return   turnAlwaysService.findavailableRoom(JSONObject.fromObject(availableRoom));
    }

    @RequestMapping("findAvailableHourRoom")
    @ResponseBody
    public ResponseData findAvailableHourRoom(@RequestBody FindAvailableHourRoomRequest findAvailableHourRoomRequest){
        return   turnAlwaysService.findAvailableHourRoom(findAvailableHourRoomRequest);
    }

    @RequestMapping("findRoomTypeUseData.do")
    @ResponseBody
    public ResponseData findRoomTypeUseData(@RequestBody AvailableRoom availableRoom){
        return   turnAlwaysService.findRoomTypeUseData(JSONObject.fromObject(availableRoom));
    }



}
