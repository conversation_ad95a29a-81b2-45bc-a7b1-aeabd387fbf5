package com.pms.czabsorders.web.hotel;


import com.pms.czhotelfoundation.bean.request.ConfDataRequest;
import com.pms.czhotelfoundation.bean.request.ConfTemplateRequest;
import com.pms.czhotelfoundation.bean.search.ConfHotelDataSearch;
import com.pms.czhotelfoundation.service.ConfService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * 短信controller
 */
@Controller
@RequestMapping("/hotel/setting/")
@Slf4j
public class ConfController {


    @Resource
    private ConfService confService;

    @RequestMapping("addOrUpdateConfHotelData.do")
    @ResponseBody
    public ResponseData addOrUpdateConfHotelData(@RequestBody ConfDataRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            confService.addOrUpdateConfHotelData(request);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("deleteConfHotelData.do")
    @ResponseBody
    public ResponseData deleteConfHotelData(@RequestBody ConfDataRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            confService.deleteConfHotelData(request);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("listHotelConfTemplate.do")
    @ResponseBody
    public ResponseData listHotelConfTemplate(@RequestBody ConfTemplateRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(confService.listHotelConfTemplate(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("listConfHotelItemData.do")
    @ResponseBody
    public ResponseData listConfHotelItemData(@RequestBody ConfDataRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(confService.listConfHotelItemData(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("listConfTemplateItem.do")
    @ResponseBody
    public ResponseData listConfTemplateItem(@RequestBody ConfTemplateRequest   request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(confService.listConfTemplateItem(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }




    @RequestMapping("listHotelConfData.do")
    @ResponseBody
    public ResponseData listHotelConfData(@RequestBody ConfHotelDataSearch search) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(confService.listHotelConfData(search));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }





    @RequestMapping("getConfData.do")
    @ResponseBody
    public ResponseData listConfTemplateItem(@RequestBody ConfDataRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(confService.getConfData(request));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }







}
