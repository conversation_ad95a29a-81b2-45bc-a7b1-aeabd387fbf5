package com.pms.czabsorders.web.hotel;

import com.pms.czhotelfoundation.bean.request.ControlRoomDeviceRequest;
import com.pms.czhotelfoundation.service.thirdMachine.impl.SmartHotelServiceImpl;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.RoomSmartRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;


@Controller
@RequestMapping("/hotel/setting/")
public class SmartRoomController {

    @Autowired
    private SmartHotelServiceImpl smartHotelService;

    // 获取房间状态
    @RequestMapping("smartRoomData")
    @ResponseBody
    public ResponseData getRoomData(@RequestBody RoomSmartRequest xiaoduRequest){
        return smartHotelService.getRoomData(xiaoduRequest);

    }

    // 入住
    @RequestMapping("smartRoomCheckIn")
    @ResponseBody
    public ResponseData checkIn(@RequestBody List<RoomSmartRequest> xiaoduRequests){
        return  smartHotelService.checkIn(xiaoduRequests);

    }

    // 退房
    @RequestMapping("smartRoomCheckOut")
    @ResponseBody
    public ResponseData checkOut(@RequestBody List<RoomSmartRequest> xiaoduRequests){
        return  smartHotelService.checkOut(xiaoduRequests);
    }

    // 退房
    @RequestMapping("controlRoomDevice")
    @ResponseBody
    public ResponseData controlRoomDevice(@RequestBody ControlRoomDeviceRequest controlRoomDeviceRequest){
        return  smartHotelService.controlRoomDevice(controlRoomDeviceRequest);
    }

}
