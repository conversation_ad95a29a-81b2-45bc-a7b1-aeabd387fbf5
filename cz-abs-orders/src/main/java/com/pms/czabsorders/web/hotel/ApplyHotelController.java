package com.pms.czabsorders.web.hotel;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.Page;
import com.pms.czabsorders.batchapply.BatchApplyInfo;
import com.pms.czhotelfoundation.bean.hotel.HotelBaseInfo;
import com.pms.czhotelfoundation.bean.hotel.HotelGroupInfo;
import com.pms.czhotelfoundation.bean.hotel.search.HotelBaseInfoSearch;
import com.pms.czhotelfoundation.bean.hotel.search.HotelGroupInfoSearch;
import com.pms.czhotelfoundation.bean.request.AddApplyInfoRecordRequest;
import com.pms.czhotelfoundation.bean.request.AuditApplyRequest;
import com.pms.czhotelfoundation.bean.request.BatchAddApplyInfoRecordRequest;
import com.pms.czhotelfoundation.bean.search.ApplyInfoRecordSearch;
import com.pms.czhotelfoundation.bean.user.NewUserHotel;
import com.pms.czhotelfoundation.dao.hotel.HotelBaseInfoDao;
import com.pms.czhotelfoundation.dao.hotel.HotelGroupInfoDao;
import com.pms.czhotelfoundation.dao.user.FounNewUserHotelDao;
import com.pms.czhotelfoundation.dao.user.FounTbUserSessionDao;
import com.pms.czhotelfoundation.service.ApplyInfoRecordService;
import com.pms.czhotelfoundation.service.group.HotelGroupService;
import com.pms.czhotelfoundation.service.out.OutPlatService;
import com.pms.czmembership.service.company.CompanyInfoService;
import com.pms.czpmsoprecord.service.OprecordService;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.conf.HotelIotPlatConfig;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.Iot;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.excel.ExcelDataListener;
import com.pms.czpmsutils.request.ApplyHotelRequest;
import com.pms.czpmsutils.request.IotEnterprise;
import com.pms.czpmsutils.request.IotEnterpriseRequest;
import com.pms.czpmsutils.thirdauth.HotelIotStrategy;
import com.pms.czpmsutils.thread.ThreadPool;
import com.pms.czpmsutils.thread.ThreadPoolFactory;
import com.pms.czpmsutils.thread.ThreadTypeEnum;
import net.sf.json.JSONObject;
import org.apache.poi.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * 酒店申请controller
 */
@Controller
@RequestMapping("/hotel/apply/")
public class ApplyHotelController {

    private static final Logger log = LoggerFactory.getLogger(ApplyHotelController.class);

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private HotelFileServerUtil hotelFileServerUtil;

    @Autowired
    private OutPlatService outPlatService;

    @Resource
    private ApplyInfoRecordService applyInfoRecordService;

    @Resource
    private ExcelDataListener<BatchApplyInfo> excelDataListener;

    @Resource(name = "oprecordServiceImpl")
    public OprecordService oprecordService;

    @Resource(name = "companyInfoServiceImpl")
    private CompanyInfoService companyInfoService;

    @Resource(name = "hotelGroupServiceImpl")
    private HotelGroupService hotelGroupService;

    @Resource
    private HotelBaseInfoDao hotelBaseInfoDao;

    @Resource
    private HotelIotPlatConfig iotPlatConfig;

    @Autowired
    private WebClientUtil webClientUtil;

    @Resource
    private FounNewUserHotelDao newUserHotelDao;

    @Resource
    private FounTbUserSessionDao tbUserSessionDao;

    /**
     * 创建酒店申请记录
     * curl http://127.0.0.1:8102/hotel/apply/addApplyInfoRecord.do -H "Content-Type: application/json"  -d '{"hotelBaseInfo":{"hotelName":"测试添加酒店001","hotelNameEn":"测试添加酒店001","shortName":"测试添加酒店001","shortNameEn":"测试添加酒店001","lon":117.13,"lat":36.66,"poi":"高新区","addr":"上海嘉定区","des":"test add hotel","star":3,"city":310100,"area":310113,"provice":310000,"country":1,"telephone":"021-01100110","contact":"张三","contactPhone":"13664330622","hotelType":1},"hotelGroupInfo":{"chainName":"测试添加集团","leader":"橙色","chainDesc":"测试添加集团","chainDescEn":"test add hotel group","addr":"上海市浦东新区","phone":"13667886709","enable":1},"sessionToken":"BE7CBB9372B0454EBFE7C49420BA34C3"}'
     *
     * @param
     * @return
     */
    @RequestMapping("addApplyInfoRecord.do")
    @ResponseBody
    public ResponseData addApplyInfoRecord(@RequestBody AddApplyInfoRecordRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            applyInfoRecordService.addApplyInfoRecord(request);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("testVoid.do")
    @ResponseBody
    public ResponseData voidGet() {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Map<String, String> authParamMap = new HashMap<>();
            authParamMap.put("eid",3095 + "");
            authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
            authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
            authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
            HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
            hotelIotStrategy.initStrategy(authParamMap);
            JSONObject res = webClientUtil.sendPostByJson(
                    iotPlatConfig.getAddress() + "/v1/config/test/void" ,
                    "0001",
                    hotelIotStrategy,
                    JSONObject.class);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 创建酒店申请记录列表
     *
     * @param
     * @return
     */
    @RequestMapping("findApplyInfoRecord.do")
    @ResponseBody
    public ResponseData findApplyInfoRecord(@RequestBody ApplyInfoRecordSearch search) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(applyInfoRecordService.findApplyInfoRecord(search));
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 审核
     * curl http://127.0.0.1:8102/hotel/apply/auditApply.do -H "Content-Type: application/json"  -d '{"uuid":"715B7D0D09FF4394B75DE50213ED5529","status":2,"auditRemark":"测试增加酒店 审核备注","auditReason":"","auditUserName":"王旭","sessionToken":"BE7CBB9372B0454EBFE7C49420BA34C3"}'
     *
     * @param
     * @return
     */
    @RequestMapping("auditApply.do")
    @ResponseBody
    public ResponseData auditApply(@RequestBody AuditApplyRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            applyInfoRecordService.auditApply(request);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @RequestMapping("batchAddApplyInfoRecord.do")
    @ResponseBody
    public ResponseData batchAddApplyInfoRecord(@RequestBody BatchAddApplyInfoRecordRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        String downloadFilePath = request.getFilePath();
        if (null == downloadFilePath || downloadFilePath.isEmpty()) {
            responseData.setResult(ER.ERR);
            responseData.setMsg("文件下载地址有误");
            return responseData;
        }
        List<BatchApplyInfo> batchApplyInfoList = new ArrayList<>();
        try {
            // 1. 下载模板文件，解析企业和用户信息
            File file = hotelFileServerUtil.downloadFile(request.getFilePath());
            // 2. 解析文件，获取酒店，用户数据
            log.info("开始解析excel文件内容");
            ExcelReader excelReader = EasyExcel.read(file, BatchApplyInfo.class, excelDataListener)
                    .excelType(ExcelTypeEnum.XLSX).build();
            excelReader.readAll();
            if (StringUtils.isNotBlank(ExcelDataListener.errorMsg.toString())) {
                log.info("excel文件内容有错误");
                responseData.setResult(ER.ERR);
                responseData.setMsg(ExcelDataListener.errorMsg.toString());
                ExcelDataListener.errorMsg.setLength(0);
                //清空监听器中容器
                excelReader.finish();
                excelReader.close();
                excelDataListener.setHeaderValidated();
                excelDataListener.clearDataList();
                return responseData;
            }
            //这里是方便下面清除导入监听器中的list对象后，新list的值还保存
            batchApplyInfoList.addAll(excelDataListener.getDataList());
            if (batchApplyInfoList.isEmpty()) {
                log.info("excel文件内容为空");
                responseData.setResult(ER.ERR);
                responseData.setMsg("文件中没有酒店用户数据");
                //清空监听器中容器
                excelReader.finish();
                excelReader.close();
                excelDataListener.setHeaderValidated();
                excelDataListener.clearDataList();
                return responseData;
            }
            //清空监听器中容器
            excelReader.finish();
            excelReader.close();
            excelDataListener.setHeaderValidated();
            excelDataListener.clearDataList();
            // 校验酒店名称
            List<String> hotelNameList = new ArrayList<>();
            for(BatchApplyInfo batchApplyInfo : batchApplyInfoList){
                hotelNameList.add(batchApplyInfo.getHotelName());
            }
            List<String> dbHotelNameList = hotelBaseInfoDao.getHotelNameList(hotelNameList);
            if(null != dbHotelNameList && !dbHotelNameList.isEmpty()){
                responseData.setResult(ER.ERR);
                String existingHotelNames = String.join(",", dbHotelNameList);
                responseData.setMsg("以下酒店名称已存在：" + existingHotelNames);
                //清空监听器中容器
                excelReader.finish();
                excelReader.close();
                excelDataListener.setHeaderValidated();
                excelDataListener.clearDataList();
                return responseData;
            }

            // 循环创建酒店，并开通酒店账号
            asyncApplyHotel(batchApplyInfoList, request.getSessionToken());
        } catch (Exception e) {
            log.error("批量创建酒店失败:{}", e.getMessage(), e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public void asyncApplyHotel(List<BatchApplyInfo> batchApplyInfoList, String sessionToken) {
        ThreadPool threadPool = ThreadPoolFactory.getInstance().getThreadPool(ThreadTypeEnum.EXECUTE_HOTEL_IMPORT_TASK);
        threadPool.monitor(ThreadTypeEnum.EXECUTE_HOTEL_IMPORT_TASK.getMessage());
        threadPool.execute(() -> {
            log.info("异步批量创建酒店开始");
            long startTime = System.currentTimeMillis();
            for (BatchApplyInfo batchApplyInfo : batchApplyInfoList) {
                log.info("创建酒店{}", batchApplyInfo.getHotelName());
                try {
                    Integer hotelGroupId = hotelGroupService.getGroupByUid(batchApplyInfo.getHotelGroupCode());
                    // 3. 添加酒店，hotel/apply/addApplyInfoRecord.do
                    AddApplyInfoRecordRequest addApplyInfoRecordRequest = new AddApplyInfoRecordRequest();
                    HotelBaseInfo hotelBaseInfo = new HotelBaseInfo();
                    // 酒店联系人
                    hotelBaseInfo.setContact(batchApplyInfo.getName());
                    // 酒店联系人电话
                    hotelBaseInfo.setContactPhone(batchApplyInfo.getPhone());
                    // 环境状态 1.正式营业 2-暂停营业 3-试营业 4-筹备中 5-建设中
                    hotelBaseInfo.setEnvironmentalState(1);
                    // 过期时间:以导入时间为准，+1年
                    hotelBaseInfo.setExprieDate(DateUtil.addYear(new Date()));
                    hotelBaseInfo.setHotelName(batchApplyInfo.getHotelName());
                    hotelBaseInfo.setHotelGroupId(hotelGroupId);
                    // 状态（默认为0  审核不通过   1  审核通过)
                    hotelBaseInfo.setHotelStatus(0);
                    // 酒店类型:酒店类型 1酒店 2公寓 3民宿 4客栈 5主题酒店
                    hotelBaseInfo.setHotelType(1);
                    // 酒店编号
                    hotelBaseInfo.setShortName(batchApplyInfo.getHotelCode());
                    // 酒店星级
                    hotelBaseInfo.setStar(1);
                    hotelBaseInfo.setAccountManager(batchApplyInfo.getAccountManager());
                    hotelBaseInfo.setAccountManagerPhone(batchApplyInfo.getAccountManagerPhone());
                    addApplyInfoRecordRequest.setHotelBaseInfo(hotelBaseInfo);
                    addApplyInfoRecordRequest.setSessionToken(sessionToken);
                    //创建申请记录
                    //这个方法只创建酒店基本信息和申请记录，如果集团id为空，后续方法会将祺更新为失败
                    applyInfoRecordService.onlyAddApplyInfoRecord(addApplyInfoRecordRequest);
                    // 集团编码不能重复
                    // 查询酒店信息
                    HotelBaseInfoSearch search = new HotelBaseInfoSearch();
                    search.setHotelGroupId(hotelGroupId);
                    search.setHotelName(batchApplyInfo.getHotelName());
                    Page<HotelBaseInfo> page = hotelBaseInfoDao.selectBySearch(search);
                    Integer hid = null;
                    if (null != page && page.size() >= 1) {
                        hid = page.get(0).getHid();
                    }
                    //自动审核每个酒店
                    AuditApplyRequest auditApplyRequest = new AuditApplyRequest();
                    auditApplyRequest.setHid(hid);
                    auditApplyRequest.setSessionToken(sessionToken);
                    if (hotelGroupId == null) {
                        auditApplyRequest.setAuditReason("所属集团编码在平台中不存在");
                        auditApplyRequest.setAuditRemark("批量导入酒店，所属集团编码必须存在");
                        auditApplyRequest.setAuditUserName("system");
                        auditApplyRequest.setStatus(0);
                    } else {
                        auditApplyRequest.setAuditReason("批量导入，自动审核通过");
                        auditApplyRequest.setAuditRemark("无备注信息");
                        auditApplyRequest.setAuditUserName("system");
                        auditApplyRequest.setStatus(2);
                    }
                    applyInfoRecordService.auditApply(auditApplyRequest);

                    ApplyHotelRequest applyHotelRequest = new ApplyHotelRequest();
                    applyHotelRequest.setHotelId(hid);
                    applyHotelRequest.setHotelGroupId(hotelGroupId);
                    applyHotelRequest.setName(batchApplyInfo.getName());
                    // 用手机号
                    String phone = batchApplyInfo.getPhone();
                    applyHotelRequest.setUid(phone);
                    applyHotelRequest.setPassword("Hotel" + phone.substring(5));
                    applyHotelRequest.setPhone(phone);
                    applyHotelRequest.setHotelName(batchApplyInfo.getHotelName());
                    applyHotelRequest.setSessionToken(sessionToken);
                    applyHotelRequest.setHid(hid);
                    applyHotelRequest.setAccountManager(batchApplyInfo.getAccountManager());
                    applyHotelRequest.setAccountManagerPhone(batchApplyInfo.getAccountManagerPhone());
                    // 4. 创建创建oprecord表，/hotel/oprecord/applyHotel
                    oprecordService.applyHotel(applyHotelRequest);
                    // 5. 创建用户权限，调用CZ-USER模块的服务创建用户权限
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    //为新酒店添加权限组别，并发送相关短信
                    HttpEntity<ApplyHotelRequest> httpEntity = new HttpEntity<>(applyHotelRequest, headers);
                    restTemplate.postForEntity
                                    ("http://" + GURL.USER + "/hotel/role/applyHotel", httpEntity, ResponseData.class)
                            .getBody();
                    // 6. 创建协议单位，/hotel/company/applyHotel
                    companyInfoService.applyHotel(applyHotelRequest);
                } catch (Exception e) {
                    log.error("酒店{}创建失败，异常：{}", "", e.getMessage(), e);
                }
            }
            long endTime = System.currentTimeMillis();
            log.info("异步批量创建酒店结束,耗时{}", endTime - startTime);
        });
    }

    @RequestMapping("iotHotelAdd")
    @ResponseBody
    public ResponseData iotHotelAdd(@RequestBody IotEnterpriseRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            responseData.setData(creatHotelByIot(request));
        } catch (Exception e) {
            log.error("iot创建酒店失败:{}", e.getMessage(), e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("创建酒店异常");
        }
        return responseData;
    }

    @RequestMapping("iotHotelUpdate")
    @ResponseBody
    public ResponseData iotHotelUpdate(@RequestBody IotEnterpriseRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            updateHotelByIot(request);
        } catch (Exception e) {
            log.error("iot修改酒店失败:{}", e.getMessage(), e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("修改酒店异常");
        }
        return responseData;
    }

    public void updateHotelByIot(IotEnterpriseRequest iotEnterpriseRequest) throws Exception {
        HotelBaseInfo hotelBaseInfo = new HotelBaseInfo();
        hotelBaseInfo.setHid(Integer.parseInt(iotEnterpriseRequest.getHotelId()));
        hotelBaseInfo.setContact(iotEnterpriseRequest.getHotelContactName());
        hotelBaseInfo.setContactPhone(iotEnterpriseRequest.getHotelContactPhone());
        hotelBaseInfo.setHotelName(iotEnterpriseRequest.getEnterpriseName());
        hotelBaseInfo.setAccountManager(iotEnterpriseRequest.getCustManagerName());
        hotelBaseInfo.setAccountManagerPhone(iotEnterpriseRequest.getCustManagerNumber());
        hotelBaseInfo.setUpdateUserName("admin");
        hotelBaseInfo.setUpdateTime(new Date());
        hotelBaseInfo.setUpdateUserId("0");
        hotelBaseInfo.setExprieDate(HotelUtils.parseStr2Date(iotEnterpriseRequest.getExpireTime()));
        hotelBaseInfo.setDes(iotEnterpriseRequest.getMark());
        hotelBaseInfoDao.editHotelBaseInfo(hotelBaseInfo);

        TbUserSession tbUserSession = new TbUserSession();
        tbUserSession.setHotelName(hotelBaseInfo.getHotelName());
        tbUserSession.setHid(hotelBaseInfo.getHid());
        tbUserSessionDao.updateSessionHotelName(tbUserSession);

        NewUserHotel newUserHotel = new NewUserHotel();
        newUserHotel.setHotelName(hotelBaseInfo.getHotelName());
        newUserHotel.setHid(hotelBaseInfo.getHid());
        newUserHotelDao.updateHotelName(newUserHotel);
    }

    public Integer creatHotelByIot(IotEnterpriseRequest iotEnterpriseRequest) throws Exception {
        log.info("iot创建酒店开始");
        long startTime = System.currentTimeMillis();
        log.info("创建酒店{}", iotEnterpriseRequest.getEnterpriseName());
        try {
            AddApplyInfoRecordRequest addApplyInfoRecordRequest = new AddApplyInfoRecordRequest();
            HotelBaseInfo hotelBaseInfo = new HotelBaseInfo();
            // 酒店联系人
            hotelBaseInfo.setContact(iotEnterpriseRequest.getHotelContactName());
            // 酒店联系人电话
            hotelBaseInfo.setContactPhone(iotEnterpriseRequest.getHotelContactPhone());
            // 环境状态 1.正式营业 2-暂停营业 3-试营业 4-筹备中 5-建设中
            hotelBaseInfo.setEnvironmentalState(1);
            //设置酒店过期时间
            hotelBaseInfo.setExprieDate(DateUtil.strToDate(iotEnterpriseRequest.getExpireTime(), DateUtil.DATE_S));
            hotelBaseInfo.setHotelName(iotEnterpriseRequest.getEnterpriseName());
            // 状态（默认为0  审核不通过   1  审核通过)
            hotelBaseInfo.setHotelStatus(0);
            // 酒店类型:酒店类型 1酒店 2公寓 3民宿 4客栈 5主题酒店
            hotelBaseInfo.setHotelType(1);
            // 酒店编号
            hotelBaseInfo.setShortName(iotEnterpriseRequest.getHotelId());
            // 酒店星级
            hotelBaseInfo.setStar(1);
            hotelBaseInfo.setAccountManager(iotEnterpriseRequest.getCustManagerName());
            hotelBaseInfo.setAccountManagerPhone(iotEnterpriseRequest.getCustManagerNumber());
            try{
                if(StringUtils.isNotBlank(iotEnterpriseRequest.getProvinceCode())){
                    hotelBaseInfo.setProvice(Integer.parseInt(iotEnterpriseRequest.getProvinceCode()));
                }
//                if(StringUtils.isNotBlank(iotEnterpriseRequest.getCityCode())){
//                    hotelBaseInfo.setCity(Integer.parseInt(iotEnterpriseRequest.getCityCode()));
//                }
//                if(StringUtils.isNotBlank(iotEnterpriseRequest.getAreaCode())){
//                    hotelBaseInfo.setArea(Integer.parseInt(iotEnterpriseRequest.getAreaCode()));
//                }
            }catch (Exception e){
                log.info("设置酒店省市区编码失败，异常信息为 {}",e );
            }
            Integer hotelGroupId = null;
            if (iotEnterpriseRequest.getHotelGroup() != null) {
                try{
                    hotelGroupId = Integer.parseInt(iotEnterpriseRequest.getHotelGroup());
                }catch (Exception e){
                    hotelGroupId = hotelGroupService.getGroupByUid(iotEnterpriseRequest.getHotelGroup());
                }
            }
            hotelBaseInfo.setHotelGroupId(hotelGroupId);
            addApplyInfoRecordRequest.setHotelBaseInfo(hotelBaseInfo);
            //创建申请记录
            hotelBaseInfo = applyInfoRecordService.onlyAddApplyInfoRecordByIot(addApplyInfoRecordRequest);
            //自动审核每个酒店
            AuditApplyRequest auditApplyRequest = new AuditApplyRequest();
            auditApplyRequest.setHid(hotelBaseInfo.getHid());
            auditApplyRequest.setAuditReason("iot创建酒店，自动审核通过");
            auditApplyRequest.setAuditRemark("无备注信息");
            auditApplyRequest.setAuditUserName("system");
            auditApplyRequest.setStatus(2);
            applyInfoRecordService.auditApply(auditApplyRequest);
            ApplyHotelRequest applyHotelRequest = new ApplyHotelRequest();
            applyHotelRequest.setHotelId(hotelBaseInfo.getHid());
            applyHotelRequest.setHotelGroupId(hotelBaseInfo.getHotelGroupId());
            applyHotelRequest.setName("");
            // 用手机号
            String phone = iotEnterpriseRequest.getHotelContactPhone();
            applyHotelRequest.setUid(phone);
            applyHotelRequest.setPassword("Hotel" + phone.substring(5));
            applyHotelRequest.setPhone(phone);
            applyHotelRequest.setHotelName(iotEnterpriseRequest.getEnterpriseName());
            applyHotelRequest.setHid(hotelBaseInfo.getHid());
            applyHotelRequest.setAccountManager(iotEnterpriseRequest.getCustManagerName());
            applyHotelRequest.setAccountManagerPhone(iotEnterpriseRequest.getCustManagerNumber());
            // 4. 创建创建oprecord表，/hotel/oprecord/applyHotel
            oprecordService.applyHotel(applyHotelRequest);
            // 6. 创建协议单位，/hotel/company/applyHotel
            companyInfoService.applyHotelByIot(applyHotelRequest);
            long endTime = System.currentTimeMillis();
            log.info("iot创建酒店结束,耗时{}", endTime - startTime);
            return hotelBaseInfo.getHid();
        } catch (Exception e) {
            log.error("酒店{}创建失败，异常：{}", "", e.getMessage(), e);
            throw new Exception(e.getMessage());
        }
    }
}
