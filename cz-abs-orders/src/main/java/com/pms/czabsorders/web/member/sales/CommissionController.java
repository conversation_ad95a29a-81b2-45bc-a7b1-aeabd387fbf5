package com.pms.czabsorders.web.member.sales;

import com.pms.czmembership.bean.requst.SalesPersonCommissionRequest;
import com.pms.czmembership.bean.requst.SeachPersonComssion;
import com.pms.czmembership.bean.sales.search.SalesHotelCommissionTypeSearch;
import com.pms.czmembership.service.sales.CommissionHotelTypeService;
import com.pms.czmembership.service.sales.CommissionService;
import com.pms.czpmsutils.ResponseData;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/commission/")
public class CommissionController {

    @Autowired
    private CommissionService commissionService;

    @Autowired
    private CommissionHotelTypeService commissionHotelTypeService;

    @RequestMapping("findAllCommissions.do")
    @ResponseBody
    public ResponseData findAllCommissions(@RequestBody JSONObject param){
        return commissionService.findAllCommissions(param);
    }

    @RequestMapping("addOrUpdateCommission.do")
    @ResponseBody
    public ResponseData addOrUpdateCommission(@RequestBody JSONObject param){
        return commissionService.addOrUpdateCommission(param);
    }

    @RequestMapping("allCommissionType.do")
    @ResponseBody
    public ResponseData allCommissionType(@RequestBody JSONObject param){
        return commissionService.allCommissionType(param);
    }


    @RequestMapping("findAllHotelCommissionType.do")
    @ResponseBody
    public ResponseData findAllHotelCommissionType(@RequestBody SalesHotelCommissionTypeSearch param){
        return commissionHotelTypeService.allCommissionHotelType(param);
    }

    @RequestMapping("addOrUpdateCommissionHotelType.do")
    @ResponseBody
    public ResponseData addOrUpdateCommissionHotelType(@RequestBody JSONObject param){
        return commissionHotelTypeService.addOrUpdateCommissionHotelType(param);
    }

    @RequestMapping("searchPersonComission.do")
    @ResponseBody
    public ResponseData searchPersonComission(@RequestBody SeachPersonComssion param){
        return commissionService.searchPersonComission(param);
    }

    @RequestMapping("addOrUpdatePersonComission.do")
    @ResponseBody
    public ResponseData addOrUpdatePersonComission(@RequestBody SalesPersonCommissionRequest param){
        return commissionService.addOrUpdatePersonComission(param);
    }

}
