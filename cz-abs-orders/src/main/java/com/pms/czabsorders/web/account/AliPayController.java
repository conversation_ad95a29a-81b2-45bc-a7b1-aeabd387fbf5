package com.pms.czabsorders.web.account;




import com.pms.czaccount.service.alipay.AliPayService;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

/**
 * 支付宝支付
 */
@Controller
@RequestMapping("/hotel/alipay/")
public class AliPayController {

    @Autowired
    private AliPayService aliPayService;

    /**
     * 获取微信二维码信息
     * @param request
     *    参数: money 单位元
     *         memo  说明
     * @return
     */
    @RequestMapping("getAlipayQrcode.do")
    @ResponseBody
    public Map<String,Object> getAlipayQrcode(@RequestBody JSONObject param){
        return aliPayService.getAlipayQrCode(param);
    }

    /**
     * 获取支付成功的结果，最长执行120秒
     * @param request
     *  参数:mainId   getWxQrcode方法返回的mainId
     * @return
     */
    @RequestMapping("handleAliPayResult.do")
    @ResponseBody
    public Map<String,Object> handleAliPayResult(@RequestBody JSONObject map ){
        return aliPayService.handleAliPayResult(map);
    }

    /**
     * 关闭支付宝交易
     * @param request
     * @return
     */
    @RequestMapping("alipayClose.do")
    @ResponseBody
    public Map<String,Object> alipayClose(@RequestBody JSONObject map ){
        return aliPayService.alipayClose(map);
    }

    /**
     * 获取支付成功的结果，最长执行120秒
     * @param request
     *  参数:mainId   getWxQrcode方法返回的mainId
     *      refundMoney 单位 分
     * @return
     */
    @RequestMapping("alipayRefund.do")
    @ResponseBody
    public Map<String,Object> alipayRefund(@RequestBody JSONObject map ){
        return aliPayService.alipayRefund(map);
    }

    /**
     * 获取支付成功的结果，最长执行120秒
     * @param request
     *  参数:mainId   getWxQrcode方法返回的mainId
     *      refundMoney 单位 分
     * @return
     */
    @RequestMapping("getAlipayMsg.do")
    @ResponseBody
    public Map<String,Object> getAlipayMsg(@RequestBody JSONObject map ){
        return aliPayService.getAlipayMsg(map);
    }


    /**
     * 获取支付成功的结果，最长执行120秒
     * @param request
     *  参数:mainId   getWxQrcode方法返回的mainId
     *      refundMoney 单位 分
     * @return
     */
    @RequestMapping("saveOrUpdateAlipayMsg.do")
    @ResponseBody
    public Map<String,Object> saveOrUpdateAlipayMsg(@RequestBody JSONObject map ){
        return aliPayService.saveOrUpdateAlipayMsg(map);
    }

}
