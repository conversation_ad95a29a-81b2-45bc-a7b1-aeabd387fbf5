package com.pms.czabsorders.web.hotel.chess;

import com.pms.czhotelfoundation.bean.chess.GetHotelHourRoomTypePriceRequest;
import com.pms.czhotelfoundation.bean.chess.GetHotelRoomListRequest;
import com.pms.czhotelfoundation.service.chess.ChessService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.BaseRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 查询酒店所有的棋牌室
 */
@Controller
@RequestMapping("/hotel/room/")
public class ChessController {
    @Autowired
    private ChessService chessService;



    @RequestMapping("getHotelList")
    @ResponseBody
    public ResponseData getHotelList(@RequestBody BaseRequest baseRequest) {
        ResponseData responseData = chessService.getHotelList(baseRequest);
        return responseData;
    }

    @RequestMapping("getHotelBaseInfo")
    @ResponseBody
    public ResponseData getHotelBaseInfo(@RequestBody BaseRequest baseRequest) {
        ResponseData responseData = chessService.getHotelBaseInfo(baseRequest);
        return responseData;
    }


    @RequestMapping("getHotelRoomList")
    @ResponseBody
    public ResponseData getHotelRoomList(@RequestBody GetHotelRoomListRequest getHotelRoomListRequest) {
        ResponseData responseData = chessService.getHotelRoomList(getHotelRoomListRequest);
        return responseData;
    }

    @RequestMapping("getHotelHourRateCodeList")
    @ResponseBody
    public ResponseData getHotelHourRateCodeList(@RequestBody BaseRequest baseRequest) {
        ResponseData responseData = chessService.getHotelHourRateCodeList(baseRequest);
        return responseData;
    }

    @RequestMapping("getHotelHourRoomTypePrice")
    @ResponseBody
    public ResponseData getHotelHourRoomTypePrice(@RequestBody GetHotelHourRoomTypePriceRequest getHotelHourRoomTypePriceRequest) {
        ResponseData responseData = chessService.getHotelHourRoomTypePrice(getHotelHourRoomTypePriceRequest);
        return responseData;
    }



}
