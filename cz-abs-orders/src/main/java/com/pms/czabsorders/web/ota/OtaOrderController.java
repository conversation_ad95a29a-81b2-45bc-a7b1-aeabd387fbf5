package com.pms.czabsorders.web.ota;

import com.pms.czabsorders.service.ota.OtaOrderService;
import com.pms.czhotelfoundation.bean.ota.OtaOrderDailyPrice;
import com.pms.czhotelfoundation.bean.ota.OtaPmsOrderInfo;
import com.pms.czhotelfoundation.bean.ota.OtaOrderRoomUpdateInfo;
import com.pms.czhotelfoundation.bean.ota.OtaOrderStatusUpdate;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping("/hotel/order/ota/")
public class OtaOrderController {

    @Autowired
    private OtaOrderService otaOrderService;

    @RequestMapping("getOtaOrderInfo")
    @ResponseBody
    public OtaPmsOrderInfo getOtaOrderInfo(String orderSn){
        return  otaOrderService.getOtaOrderInfoBySn(orderSn);
    }

    /**
     * Ota创建预订单
     * **/
    @RequestMapping("createOrder")
    @ResponseBody
    public ResponseData getOtaOrderInfo(OtaPmsOrderInfo otaOrderInfo){
        return  otaOrderService.createOtaOrder(otaOrderInfo);
    }

    /**
     * 修改预订单状态
     * **/
    @RequestMapping("updateOrderOtaStatus")
    @ResponseBody
    public ResponseData updateOrderOtaStatus(OtaOrderStatusUpdate statusUpdate){
        return  otaOrderService.updateOtaOrderStatus(statusUpdate);
    }

    
    //修改预定的间夜信息
    @RequestMapping("upaBookRoomNightAndNumFunc")
    @ResponseBody
    public ResponseData upaBookRoomNightAndNumFunc(@RequestBody OtaOrderRoomUpdateInfo param) {
        return otaOrderService.upaBookRoomNightAndNumFunc(param);
    }


    @RequestMapping("getDailyPriceListByOrderList")
    @ResponseBody
    public ResponseData getDailyPriceListByOrderList(@RequestBody List<Integer> orderIdList) {
        ResponseData responseData = new ResponseData();
        responseData.setData(otaOrderService.getDailyPriceListByOrderList(orderIdList));
        responseData.setResult(ER.SUCC);
        return responseData;
    }
}
