package com.pms.czabsorders.web;

import com.pms.czabsorders.service.hotelBusinessExecute.HotelBuinessExecuteService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.HotelDataClearRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/absorder/")
public class HotelBuinessExecute {

    @Autowired
    private HotelBuinessExecuteService hotelBuinessExecuteService;

    /**
     * 酒店数据清理
     * @param hotelDataClearRequest
     * @return
     */
    @RequestMapping("hotelDataClear")
    @ResponseBody
    public ResponseData hotelDataClear(@RequestBody HotelDataClearRequest hotelDataClearRequest) {
        return hotelBuinessExecuteService.hotelDataClear(hotelDataClearRequest);
    }
}
