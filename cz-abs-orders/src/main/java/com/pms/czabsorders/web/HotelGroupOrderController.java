package com.pms.czabsorders.web;


import com.pms.czabsorders.bean.CardGroupInfoSearchRequest;
import com.pms.czabsorders.bean.CardGroupLevelSearchRequest;
import com.pms.czabsorders.bean.CardGroupTypeSearchRequest;
import com.pms.czabsorders.service.group.HotelGroupOrderService;
import com.pms.czmembership.bean.company.search.HotelCompanyAccountInfoSearch;
import com.pms.czmembership.bean.company.search.HotelCompanyInfoSearch;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.BaseRequest;
import com.pms.pmsorder.bean.request.BookingOrderPageRequest;
import com.pms.pmsorder.bean.request.GetHotelGroupAvailableRoomRequest;
import com.pms.pmsorder.bean.search.RegistPersonSearch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/absorder/group")
public class HotelGroupOrderController {
    @Autowired
    HotelGroupOrderService hotelGroupOrderService;

    @RequestMapping("getHotelGroupOrderList.do")
    @ResponseBody
    ResponseData getHotelGroupOrderList(@RequestBody BookingOrderPageRequest bookingOrderPageRequest) {
        bookingOrderPageRequest.setSessionToken("DEF623BCAF9847CC9465524E75BB922D");
        return hotelGroupOrderService.getHotelGroupOrderList(bookingOrderPageRequest);
    }

    @RequestMapping("getHotelGroupAvailableRoom.do")
    @ResponseBody
    ResponseData getHotelGroupAvailableRoom(@RequestBody GetHotelGroupAvailableRoomRequest getHotelGroupAvailableRoomRequest) {
        ResponseData hotelGroupAvailableRoom = hotelGroupOrderService.getHotelGroupAvailableRoom(getHotelGroupAvailableRoomRequest);
        return hotelGroupAvailableRoom;
    }

    @RequestMapping("getHotelGroupRoomTypeNum.do")
    @ResponseBody
    ResponseData getHotelGroupRoomTypeNum(@RequestBody BaseRequest baseRequest) {
        baseRequest.setSessionToken("DEF623BCAF9847CC9465524E75BB922D");
        ResponseData getHotelGroupRoomTypeNum = hotelGroupOrderService.getHotelGroupRoomTypeNum(baseRequest);
        return getHotelGroupRoomTypeNum;
    }

    @RequestMapping("getHotelGroupMember.do")
    @ResponseBody
    ResponseData getHotelGroupMember(@RequestBody CardGroupInfoSearchRequest cardGroupInfoSearch) {
        cardGroupInfoSearch.setSessionToken("DEF623BCAF9847CC9465524E75BB922D");
        ResponseData hotelGroupMember = hotelGroupOrderService.getHotelGroupMember(cardGroupInfoSearch);
        return hotelGroupMember;
    }

    @RequestMapping("getHotelGroupLevel.do")
    @ResponseBody
    ResponseData getHotelGroupLevel(@RequestBody CardGroupLevelSearchRequest cardGroupLevelSearchRequest) {
        cardGroupLevelSearchRequest.setSessionToken("DEF623BCAF9847CC9465524E75BB922D");
        ResponseData responseData = hotelGroupOrderService.getHotelGroupLevel(cardGroupLevelSearchRequest);
        return responseData;
    }

    @RequestMapping("getHotelGroupType.do")
    @ResponseBody
    ResponseData getHotelGroupType(@RequestBody CardGroupTypeSearchRequest cardGroupTypeSearchRequest) {
        cardGroupTypeSearchRequest.setSessionToken("DEF623BCAF9847CC9465524E75BB922D");
        ResponseData responseData = hotelGroupOrderService.getHotelGroupType(cardGroupTypeSearchRequest);
        return responseData;
    }

    @RequestMapping("getHotelGroupRegistPerson.do")
    @ResponseBody
    ResponseData getHotelGroupType(@RequestBody RegistPersonSearch registPersonSearch) {
        registPersonSearch.setSessionToken("DEF623BCAF9847CC9465524E75BB922D");
        ResponseData responseData = hotelGroupOrderService.getHotelGroupRegistPerson(registPersonSearch);
        return responseData;
    }

    @RequestMapping("getHotelGroupCompany.do")
    @ResponseBody
    ResponseData getHotelGroupCompany(@RequestBody HotelCompanyInfoSearch hotelCompanyInfoSearch) {
        hotelCompanyInfoSearch.setSessionToken("DEF623BCAF9847CC9465524E75BB922D");
        ResponseData responseData = hotelGroupOrderService.getHotelGroupCompany(hotelCompanyInfoSearch);
        return responseData;
    }

    @RequestMapping("getHotelGroupCompanyAccount.do")
    @ResponseBody
    ResponseData getHotelGroupCompanyAccount(@RequestBody HotelCompanyAccountInfoSearch hotelCompanyAccountInfoSearch) {
        hotelCompanyAccountInfoSearch.setSessionToken("DEF623BCAF9847CC9465524E75BB922D");
        ResponseData responseData = hotelGroupOrderService.getHotelGroupCompanyAccount(hotelCompanyAccountInfoSearch);
        return responseData;
    }


}
