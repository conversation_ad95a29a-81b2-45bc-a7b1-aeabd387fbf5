package com.pms.czabsorders.listener;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.StringUtils;
import com.pms.czhotelfoundation.bean.room.RoomImportBean;
import com.pms.czhotelfoundation.service.room.RoomService;
import com.pms.czpmsutils.ValidationError;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.excel.BatchApplyInfoTypeEnum;
import com.pms.czpmsutils.excel.ExcelValidationException;
import com.pms.czpmsutils.excel.RoomImportTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: 陈星宇
 * @CreateTime: 2025-04-07
 * @Description:
 */
@Slf4j
@Component
public class RoomImportListener<RoomImportBean> extends AnalysisEventListener<RoomImportBean> implements ReadListener<RoomImportBean> {




    private List<RoomImportBean> dataList = new ArrayList<>();

    public static Map<String,String> headErrorMap = new HashMap<>();

    public static final List<ValidationError> errors = new ArrayList<>();

    public static StringBuilder errorMsg = new StringBuilder();

    private static boolean headerValidated = false;

    /**
     * 表头
     */
    private static final List<String> EXPECTED_HEADER = Arrays.asList("房号","房型","价格/元","楼层","楼栋","电话");





    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        //实际的表头
        List<String> actualHeader = headMap.values().stream().map(CellData::getStringValue).collect(Collectors.toList());
        //校验表头数量
        if(actualHeader.size() != EXPECTED_HEADER.size()){
            errorMsg.append("表头不匹配,正确应该为第一列房号,第二列房型,第三列价格/元,第四列楼层,第五列楼栋,第六列电话");
            headerValidated = true;
        }


        if(errorMsg.toString().isEmpty()){
            //逐列校验表头内容
            for (int i = 0; i < EXPECTED_HEADER.size(); i++) {
                if(!EXPECTED_HEADER.get(i).equals(actualHeader.get(i))){
                    if(StringUtils.isNotBlank(errorMsg.toString())){
                        errorMsg.append(",").append(String.format("第%d列表头错误，应为【%s】，实际为【%s】%n",i+1,EXPECTED_HEADER.get(i),actualHeader.get(i)));
                    }else {
                        errorMsg.append(String.format("第%d列表头错误，应为【%s】，实际为【%s】%n",i+1,EXPECTED_HEADER.get(i),actualHeader.get(i)));
                    }

                }
            }
        }

    }


    @Override
    public void invoke(RoomImportBean roomImportBean, AnalysisContext analysisContext) {
        if(!headerValidated){
            int rowIndex = analysisContext.readRowHolder().getRowIndex()+ 1;
            ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();
            Validator validator = validatorFactory.getValidator();
            Set<ConstraintViolation<RoomImportBean>> violations = validator.validate(roomImportBean);
            violations.forEach(violation -> {
                String fieldName  = violation.getPropertyPath().toString();
                String explain = RoomImportTypeEnum.getByFieldName(fieldName);
                String msg = violation.getMessage();
                if(StringUtils.isNotBlank(errorMsg.toString())){
                    errorMsg.append(",").append(String.format("【错误】第%d行 %s → %s%n",rowIndex,explain,msg));
                }else {
                    errorMsg.append(String.format("【错误】第%d行 %s → %s%n",rowIndex,explain,msg));
                }
            });
            dataList.add(roomImportBean);
            if(dataList.size() >100){
                dataList.clear();
                if(StringUtils.isNotBlank(errorMsg.toString())){
                    errorMsg.append(",").append("默认支持最大处理行数为100");
                }else {
                    errorMsg.append("默认支持最大处理行数为100");
                }
            }
        }

    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("excel文件解析完成");
        headErrorMap.put("errorMsg",errorMsg.toString());
        if(!errors.isEmpty()){
            throw new ExcelValidationException("Excel校验失败",errors);
        }
    }


    // 获取读取到的数据
    public List<RoomImportBean> getDataList() {
        return dataList;
    }

    public void setDataList(List<RoomImportBean> dataList) {
        this.dataList = dataList;
    }

    public void clearDataList() {
        this.dataList.clear();
    }

   public void setHeaderValidated(){
        headerValidated = false;
   }
}
