package com.pms.czabsorders.service.main;

import com.pms.czhotelfoundation.bean.hotel.CommonFunctionsSettingDto;
import com.pms.czhotelfoundation.bean.hotel.search.HotelShiftRecordSearch;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.BaseRequest;
import net.sf.json.JSONObject;
import com.pms.czpmsutils.request.HotelShiftRecordRequest;

/**
 * <AUTHOR>
 */
public interface HotelMainService {
    /**
     * 数据概览
     * @return
     */
    ResponseData dataOverview(BaseRequest baseRequest);

    /**
     * 实时数据
     * @param baseRequest
     * @return
     */
    ResponseData realTimeData(BaseRequest baseRequest);

    /**
     * 计算昨天入住率
     * @param hid
     */
    double yesterdayOccupancyRate(Integer hid);

    /**
     * 计算当前入住率
     * @param hid
     */
    double currentOccupancyRate(Integer hid);

    /**
     * 之前7天订出率
     * @param hid
     * @param result
     */
    void preSevenBookRate(Integer hid, JSONObject result);

    /**
     * 未来7天订出率
     * @param hid
     * @param result
     */
    void futureSevenBookRate(Integer hid,JSONObject result,String sessionToken);


    /**
     * 交班留言
     * @param hotelShiftRecordRequest
     * @return
     */
    ResponseData newNote(HotelShiftRecordRequest hotelShiftRecordRequest);

    /**
     * 交班留言分页接口
     * @param hotelShiftRecordSearch
     * @return
     */
    ResponseData classNoteList(HotelShiftRecordSearch hotelShiftRecordSearch);


    /**
     * 广告位模块
     * @return
     */
    ResponseData hotelBanner();

    /**
     * 房间统计
     * @return
     */
    ResponseData roomState(BaseRequest baseRequest);

    /**
     * 常用功能
     * @param baseRequest
     * @return
     */
    ResponseData commonFunctions(BaseRequest baseRequest);

    /**
     * 常用功能修改
     * @param commonFunctionsSettingDto
     * @return
     */
    ResponseData commonFunctionsSetting(CommonFunctionsSettingDto commonFunctionsSettingDto);
}
