package com.pms.czabsorders.service.machine.impl;

import com.pms.czabsorders.service.machine.MachineUserInfoService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.dao.RegistDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.List;


@Service
@Primary
@Slf4j
public class MachineUserInfoServiceImpl extends BaseService implements MachineUserInfoService {

    @Autowired
    RegistDao registDao;

    @Override
    public ResponseData getHotelMachineUserRate(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Calendar rightNow = Calendar.getInstance();
            Integer year = rightNow.get(Calendar.YEAR);
            Integer month = rightNow.get(Calendar.MONTH)+1; //第一个月从0开始，所以得到月份＋1
            Integer day = rightNow.get(rightNow.DAY_OF_MONTH);
            RegistSearch registSearch = new RegistSearch();
            int mode = param.getInt("mode");
            if (mode == 1){
                registSearch.setRegistYear(year);
            }
            else if (mode == 2){
                registSearch.setRegistYear(year);
                registSearch.setRegistYearMonth(month);
            }
            else if (mode == 3){
                String value = year.toString() +  (month.toString().length() ==2 ? month.toString() : "0" + month.toString()) + (day.toString().length() ==2 ? day.toString() : "0" + day.toString());
                registSearch.setBusinessDay(Integer.parseInt(value));
            }
            registSearch.setCheckinMode(1);

            List<Regist> regists = registDao.selectBySearch(registSearch);



            log.info("regists.size()={}",regists.size());

            JSONObject data = new JSONObject();
            data.put("userCount", 100);
            data.put("checkin",80);
            data.put("checkout",18);
            data.put("stayover",2);

            data.put("payMoneyCount" , 1494100.08);
            data.put("payCount" , 4124);

            JSONArray userDataList = new JSONArray();

            JSONArray payDataList = new JSONArray();

            JSONArray payCountDataList = new JSONArray();

            for (int i = 0; i < 12; i++) {
                JSONObject dataInfo = new JSONObject();
                dataInfo.put("x",(i + 1) + "月");
                dataInfo.put("y", (Math.random() * 1000) + 200);
                userDataList.add(dataInfo);
            }

            for (int i = 0; i < 12; i++) {
                JSONObject dataInfo = new JSONObject();
                dataInfo.put("x",(i + 1) + "月");
                dataInfo.put("y", (Math.random() * 1000) + 200);
                payDataList.add(dataInfo);
            }

            for (int i = 0; i < 12; i++) {
                JSONObject dataInfo = new JSONObject();
                dataInfo.put("x",(i + 1) + "月");
                dataInfo.put("y", Math.random());
                payCountDataList.add(dataInfo);
            }

            data.put("userDataList" ,userDataList);
            data.put("payDataList" ,payDataList);
            data.put("payCountDataList", payCountDataList);

            responseData.setData(data);

        }catch (Exception e){
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }

    @Override
    public ResponseData getMachineUserInfo(JSONObject param) {
        return null;
    }

    @Override
    public ResponseData getMachinePayInfo(JSONObject param) {
        return null;
    }
    
}
