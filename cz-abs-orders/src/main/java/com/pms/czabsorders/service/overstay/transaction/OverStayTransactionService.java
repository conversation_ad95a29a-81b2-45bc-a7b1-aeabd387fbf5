package com.pms.czabsorders.service.overstay.transaction;

import com.pms.czabsorders.bean.AddArAccount;
import com.pms.czaccount.bean.account.Account;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czmembership.bean.company.HotelCompanyAccountInfo;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.*;

import java.util.ArrayList;
import java.util.List;

public interface OverStayTransactionService {
    public void overStayTransaction(Regist regist, List<BookingOrderDailyPrice> bookingOrderDailyPrices, List<Integer> delBookingOrderDailyPriceids, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, TbUserSession user, ArrayList<Oprecord> oprecords) throws Exception;


    public void overStayTransactionTwo(Regist regist, List<BookingOrderDailyPrice> bookingOrderDailyPrices, List<BookingOrderDailyPrice> deletePrices, AddArAccount addArAccount, HotelCompanyAccountInfo hotelCompanyAccountInfo, BookingOrderConfig bookingOrderConfig, RegistStayover rs) throws Exception;


    public void overStayHourTransaction(Regist regist, Account account, RegistStayover rs, ArrayList<HourRoomDayUse> addHourUse, ArrayList<HourRoomDayUse> upaHourUse) throws Exception;


    //换单续住
    public void overStayTransactionOrder(Regist regist, List<BookingOrderDailyPrice> bookingOrderDailyPrices, BookingOrder oldBook, BookingOrder newBook, BookingOrderRoomNum oldBookRoom, BookingOrderRoomNum newBookRoom, BookingOrderConfig bookingOrderConfig, RegistStayover rs, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, List<RegistPerson> registPeople, RegistChangeRecord registChangeRecord, RoomInfo roomInfo, RoomInfo oldRoomInfo) throws Exception;


    public void overStayListTransaction(List<Regist> regist, List<BookingOrderDailyPrice> addPrices, List<BookingOrderDailyPrice> delPrices) throws Exception;

}
