package com.pms.czabsorders.service.team.transaction.impl;

import com.pms.czabsorders.service.team.transaction.TeamTransactionService;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomRepairRecordHistory;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomRepairRecordHistoryDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.room.ROOM_AUXILIARY;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistGroup;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.dao.RegistDao;
import com.pms.pmsorder.dao.RegistGroupDao;
import com.pms.pmsorder.dao.RegistPersonDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Primary
public class TeamTransactionServiceImpl extends BaseService implements TeamTransactionService {

    @Autowired
    private RegistDao registDao;

    @Autowired
    private RegistGroupDao registGroupDao;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private RoomRepairRecordHistoryDao roomRepairRecordHistoryDao;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void relieveTeam(int reliveType, List<Regist> registList, RegistGroup registGroup, TbUserSession user, List<RoomAuxiliaryRelation> relationList) throws Exception {

        //操作日志
        ArrayList<Oprecord> oprecords = new ArrayList<>();
        Oprecord oprecord = new Oprecord(user);
        for (Regist r : registList){

//            if(r.getState()!=0){
//                throw new Exception("当前房间不支持拆分,房间号:"+r.getRoomNum());
//            }
            r.setIsMainRoom(0);
            r.setRegistGroupId(0);
            r.setTeamCodeId(0);
            r.setTeamCodeName("");

            Regist registInfo = Regist.CreateRegist(r.getRegistId());
            registInfo.setIsMainRoom(0);
            registInfo.setRegistGroupId(0);
            registInfo.setTeamCodeId(0);
            registInfo.setTeamCodeName("");

            Integer update = registDao.update(registInfo);
            if(update<1){
                throw new Exception("拆分团队失败:"+r.getRegistId());
            }
            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setRegistId(r.getRegistId());
            oprecord.setSourceValue(r.getTeamCodeId().toString());
            oprecord.setBcodeO(r.getTeamCodeName());
            oprecord.setDescription("脱离团队:"+r.getTeamCodeName());
            oprecords.add(oprecord);
        }

        Integer update = registGroupDao.update(registGroup);
        if(update<1){
            throw new Exception("撤销团队信息失败:"+registGroup.getSn());
        }
        oprecord = new Oprecord(user);
        oprecord.setMainId(registGroup.getSn());
        oprecord.setRegistId(registGroup.getRegistGroupId());
        oprecord.setOccurTime(HotelUtils.currentTime());
        oprecords.add(oprecord);
        for (RoomAuxiliaryRelation rar:relationList) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(rar.getRelationId());
        }
        this.addOprecords(oprecords);
    }

    /**
     * 团队合并
     * @param mergeType   团队类型  1.单个房间合并   2.单房间并入团队  3.团队和团队合并
     * @param registList       入住单集合
     * @param registGroup      主团队信息
     * @param registGroupList  合并的团队
     * @param user
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void mergeTeam(int mergeType, List<Regist> registList, RegistGroup registGroup, List<RegistGroup> registGroupList, TbUserSession user) throws Exception {

        //是否创建主账房
        boolean isMain = false;

        //操作日志
        ArrayList<Oprecord> oprecords = new ArrayList<>();
        Oprecord oprecord = new Oprecord(user);

        //都是单房间合并，则重添加团队信息
        if(mergeType==1){
            Integer insert = registGroupDao.insert(registGroup);
            if(insert<1){
                throw new Exception("创建团队失败");
            }

            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setRegistId(registGroup.getRegistGroupId());
            oprecord.setMainId(registGroup.getSn());
            oprecord.setDescription("创建团队，编号为:"+registGroup.getSn());

            oprecords.add(oprecord);

            isMain = true;
        }

        RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
        roomAuxiliaryRelation.setHid(user.getHid());
        roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());

        //对入住信息进行合并
        for (Regist regist:registList){
            Regist registInfo = Regist.CreateRegist(regist.getRegistId());
            regist.setRegistGroupId(registGroup.getRegistGroupId());
            regist.setTeamCodeId(registGroup.getRegistGroupId());
            regist.setTeamCodeName(registGroup.getGroupName());
            registInfo.setRegistGroupId(registGroup.getRegistGroupId());
            registInfo.setTeamCodeId(registGroup.getRegistGroupId());
            registInfo.setTeamCodeName(registGroup.getGroupName());
            if(isMain){
                regist.setIsMainRoom(1);
                registInfo.setIsMainRoom(1);
                isMain = false;
            }
            //修改登记单信息
            Integer update = registDao.update(registInfo);
            if(update<1){
                throw new Exception("合并 : "+regist.getRoomNum()+" 失败。编号:"+regist.getRegistId());
            }

            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setMainId(regist.getSn());

            oprecord.setDescription("将房间合并到:"+registGroup.getSn()+" 团队");
            oprecords.add(oprecord);

            //添加辅助房态
            if(mergeType<3){

                roomAuxiliaryRelation.setRelationId(null);
                roomAuxiliaryRelation.setRoomId(regist.getRoomNumId());
                roomAuxiliaryRelation.setRegistId(regist.getRegistId());
                roomAuxiliaryRelation.setRoomNum(regist.getRoomNum());

                roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.TEAM);
                roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.TEAM);

                roomAuxiliaryRelationDao.saveRoomAuxiliaryRelation(roomAuxiliaryRelation);

            }

        }

        //对团第信息进行合并
        for (RegistGroup rg:registGroupList){

            if(rg.getRegistGroupId().equals(registGroup.getRegistGroupId())){
                continue;
            }

            rg.setState(0);
            rg.setFormerGroupId(registGroup.getRegistGroupId());

            Integer update = registGroupDao.update(rg);

            if(update<1){
                throw new Exception("合并团队失败,编号:"+registGroup.getSn());
            }

            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setRegistId(registGroup.getRegistGroupId());
            oprecord.setMainId(registGroup.getSn());
            oprecord.setChangedValue(registGroup.getRegistGroupId()+"");
            oprecord.setDescription("合并到编号为:"+registGroup.getSn()+"的团队");

            oprecords.add(oprecord);

        }

        this.addOprecords(oprecords);

    }

    /**
     *
     * @param registGroup       团队信息
     * @param registList        登记信息
     * @param updaRegistPerson  登记人
     * @param deleteRelations   辅助房态
     * @param roomInfos         房间信息
     * @param roomRepairRecordHistories 房态修改记录
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void teamOnAccount(RegistGroup registGroup, List<Regist> registList, List<RegistPerson> updaRegistPerson, List<RoomAuxiliaryRelation> deleteRelations, List<RoomInfo> roomInfos,List<RoomRepairRecordHistory> roomRepairRecordHistories) throws Exception {

        // 1.团队改为挂账状态
        registGroup.setState(3);
        Integer update = registGroupDao.update(registGroup);

        if(update<1){
            throw new Exception("修改团队信息失败");
        }

        // 订单信息
        registDao.updateRegistList(registList);

        registPersonDao.updatePeople(updaRegistPerson);

        // 辅助房态
        if(deleteRelations.size()>0){

            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(deleteRelations);

        }

        roomInfoDao.updateRoomList(roomInfos);

        roomRepairRecordHistoryDao.addRoomRepairRecordHistoryList(roomRepairRecordHistories);



    }
}
