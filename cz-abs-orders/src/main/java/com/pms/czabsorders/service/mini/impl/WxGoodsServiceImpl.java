package com.pms.czabsorders.service.mini.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.mini.AddGoodsParam;
import com.pms.czabsorders.service.mini.WxGoodsService;
import com.pms.czabsorders.service.mini.transaction.WxGoodsServiceTransaction;
import com.pms.czabsorders.service.mini.transaction.WxServiceTransaction;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.code.HotelBusinessDay;
import com.pms.czhotelfoundation.bean.code.search.HotelBusinessDaySearch;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.search.RoomAuxiliaryRelationSearch;
import com.pms.czhotelfoundation.dao.code.HotelBusinessDayDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.OrderNumUtils;
import com.pms.czpmsutils.RSAUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.search.BookingOrderRoomNumSearch;
import com.pms.pmsorder.bean.search.BookingOrderRoomTypeSearch;
import com.pms.pmsorder.bean.search.RegistPersonSearch;
import com.pms.pmsorder.dao.*;
import com.pms.pmswarehouse.bean.*;
import com.pms.pmswarehouse.bean.request.HotelGoodsRequest;
import com.pms.pmswarehouse.bean.search.GoodsClassSearch;
import com.pms.pmswarehouse.bean.search.GoodsShoppingOrderSearch;
import com.pms.pmswarehouse.bean.search.GoodsShoppingcarDetailsSearch;
import com.pms.pmswarehouse.bean.search.GoodsStockGoodsNumSearch;
import com.pms.pmswarehouse.dao.*;
import com.pms.pmswarehouse.service.GoodsStockService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WxGoodsServiceImpl extends BaseService implements WxGoodsService {

    @Autowired
    private GoodsShoppingcarDao goodsShoppingcarDao;

    @Autowired
    private GoodsShoppingcarDetailsDao goodsShoppingcarDetailsDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private HotelBusinessDayDao hotelBusinessDayDao;

    @Autowired
    private WxGoodsServiceTransaction wxGoodsServiceTransaction;

    private BaseService baseService = this;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private GoodsStockService goodsStockService;

    @Autowired
    private GoodsShoppingOrderDao goodsShoppingOrderDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private WxServiceTransaction wxServiceTransaction;

    @Autowired
    private GoodsClassDao goodsClassDao;

    @Autowired
    private GoodsDao goodsDao;

    @Resource
    private GoodsInfoDao goodsInfoDao;


    @Override
    public ResponseData addGoodsToRegist(AddGoodsParam addGoodsParam, TbUserSession user) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            GoodsShoppingcar goodsShoppingcar = goodsShoppingcarDao.selectById(addGoodsParam.getId());

            if (goodsShoppingcar == null) {
                throw new Exception("查询商品信息有误");
            }

            String s = "WX-" + goodsShoppingcar.getId() + "-" + goodsShoppingcar.getSn();


            // 查询营业日期
            HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
            hotelBusinessDaySearch.setHid(user.getHid());
            HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);

            Integer businessDay = hotelBusinessDay.getBusinessDay();

            user.setBusinessDay(businessDay);

            if (!s.equals(addGoodsParam.getSn())) {
                throw new Exception("商品订单不匹配");
            }

            // 验证是否已添加
            Boolean ok = stringRedisTemplate.opsForValue().setIfAbsent(s, "", 3, TimeUnit.HOURS);
            if (Boolean.FALSE.equals(ok)) {
                return responseData;
            }

            if (goodsShoppingcar.getGoodType() == 2) {
                return this.addGoodsForLs(addGoodsParam, goodsShoppingcar, user);
            }


            final Regist regist = registDao.selectById(goodsShoppingcar.getRegistId());

            regist.setSumPay(regist.getSumPay() + goodsShoppingcar.getSumPrice());
            regist.setSumSale(regist.getSumSale() + goodsShoppingcar.getSumPrice());

            StringBuilder goodSummary = new StringBuilder();

            GoodsShoppingcarDetailsSearch goodsShoppingcarDetailsSearch = new GoodsShoppingcarDetailsSearch();
            goodsShoppingcarDetailsSearch.setGoodsShoppingId(goodsShoppingcar.getId());
            Page<GoodsShoppingcarDetails> goodsShoppingcarDetails = goodsShoppingcarDetailsDao.selectBySearch(goodsShoppingcarDetailsSearch);

            // 账务信息
            ArrayList<Account> addAccounts = new ArrayList<>();

            // 商品出售记录
            ArrayList<GoodsSaleRecord> goodsSaleRecords = new ArrayList<>();

            RegistPerson registPerson = new RegistPerson();
            registPerson.setRegistPersonId(0);
            registPerson.setPersonName("  ");


            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(regist.getRegistId());
            registPersonSearch.setIsOther(0);
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
            if (registPeople != null && registPeople.size() > 0) {
                registPerson = registPeople.get(0);
            }


            Date date = new Date();

            for (GoodsShoppingcarDetails gsd : goodsShoppingcarDetails) {

                // 账务信息
                String no = OrderNumUtils.getNo(goodsShoppingcar.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);

                goodSummary.append(gsd.getGoodsInfoName());
                goodSummary.append("*");
                goodSummary.append(gsd.getGoodsNum());
                goodSummary.append("  ");

                Account account = new Account();

                account.setAccountId(no);
                account.setRegistId(regist.getRegistId());
                account.setTeamCodeId(regist.getTeamCodeId());
                account.setPrice(gsd.getSumPrice());
                account.setPayType(1);

                account.setHid(regist.getHid());
                account.setHotelGroupId(regist.getHotelGroupId());

                account.setPayClassId(10);
                account.setPayClassName("客房");
                account.setPayCodeId("3100");
                account.setPayCodeName("小商品");

                account.setIsSale(1);

                account.setGoodId(gsd.getGoodsInfoId());
                account.setGoodName(gsd.getGoodsInfoName());
                account.setUintPrice(gsd.getPrice());
                account.setSaleNum(gsd.getGoodsNum());

                account.setRegistState(regist.getState());
                account.setIsCancel(0);

                account.setRoomInfoId(regist.getRoomNumId());
                account.setRoomTypeId(regist.getRoomTypeId());
                account.setRoomNum(regist.getRoomNum());

                //用户信息
                account.setCreateTime(date);
                account.setCreateUserId(user.getUserId());
                account.setCreateUserName(user.getUserName());
                account.setUpdateTime(date);
                account.setUpdateUserId(user.getUserId());
                account.setUpdateUserName(user.getUserName());

                account.setClassId(user.getClassId());
                account.setUpdateCalssId(user.getClassId());

                account.setRemark(gsd.getGoodsInfoName() + "*" + gsd.getGoodsNum());

                // 营业日期
                account.setAccountYearMonth(user.getBusinessMonth());
                account.setAccountYear(user.getBusinessYear());
                account.setBusinessDay(businessDay);
                account.setRegistPersonId(registPerson.getRegistPersonId());
                account.setRegistPersonName(registPerson.getPersonName());
                account.setAccountType(2);
                account.setGroupAccount(0);
                addAccounts.add(account);

                // 商品销售记录
                //2.增加销售记录
                GoodsSaleRecord record = new GoodsSaleRecord();
                record.setHid(user.getHid());
                record.setHotelGroupId(user.getHotelGroupId());
                //  record.setGoodsStockId(goodsStock.getGoodsStockId());
               // record.setGoodsStockName(goodsStock.getGoodsStockName());


                record.setGoodsInfoId(gsd.getGoodsInfoId());
                record.setGoodsInfoName(gsd.getGoodsInfoName());
                record.setGoodsClassId(gsd.getGoodsClassId());
                record.setGoodsClassName(gsd.getGoodsClassName());


                record.setPrice(gsd.getPrice());
                record.setTotalMoney(gsd.getSumPrice());
                record.setNum(gsd.getGoodsNum());
                record.setRemark(regist.getRoomNum()+"通过小程序下单");

                //regist信息
                record.setRegistId(regist.getRegistId());
                record.setRoomNum(regist.getRoomNum());
                record.setRoomNumId(regist.getRoomNumId());
                record.setRegistPersionId(registPerson.getRegistPersonId());
                record.setRegistPersionName(registPerson.getPersonName());


                //业务信息
                record.setSourceTypeId(4);
                record.setSourceTypeName("小程序");
                record.setAccountId(no);

                //bussiness信息
                record.setBusinessDay(businessDay);
                record.setBusinessYear(HotelUtils.getNowyyyyInt());
                record.setBusinessMonth(HotelUtils.getNowyyyyMMInt());

                //user信息
                record.setHid(user.getHid());
                record.setHotelGroupId(user.getHotelGroupId());
                record.setClassId(user.getClassId());
                record.setCreateUserId(user.getUserId());
                record.setCreateUserName(user.getUserName());
                record.setUpdateUserId(user.getUserId());
                record.setUpdateUserName(user.getUserName());

                //出售
                record.setSaleType(1);
                record.setHotelIsCheckGoodsNum(goodsShoppingcar.getStcokCheck());
                goodsSaleRecords.add(record);

            }

            // 添加支付信息
            Account payAccount = new Account(user);
            String no = OrderNumUtils.getNo(goodsShoppingcar.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
            payAccount.setIsCancel(0);
            payAccount.setAccountYear(user.getBusinessYear());
            payAccount.setAccountYearMonth(user.getBusinessMonth());
            payAccount.setBusinessDay(user.getBusinessDay());
            payAccount.setClassId(user.getClassId());
            payAccount.setSettleAccountTime(new Date());
            payAccount.setHid(regist.getHid());
            payAccount.setHotelGroupId(regist.getHotelGroupId());
            //设置账务关联人id为0
            payAccount.setRegistPersonId(0);
            payAccount.setRegistPersonName("");
            payAccount.setThirdRefundState(0);
            payAccount.setAccountId(no);
            payAccount.setPrice(goodsShoppingcar.getSumPrice());
            payAccount.setPayType(2);
            payAccount.setPayClassId(12);
            payAccount.setPayClassName("微信支付");
            payAccount.setPayCodeId("9340");
            payAccount.setPayCodeName("微信公众号支付");
            payAccount.setIsSale(1);
            payAccount.setUintPrice(goodsShoppingcar.getSumPrice());
            payAccount.setSaleNum(1);
            payAccount.setRegistState(0);
            payAccount.setSettleAccountTime(new Date());
            payAccount.setThirdAccoutId(addGoodsParam.getMainId());
            payAccount.setRefundPrice(0);
            payAccount.setThirdRefundState(0);
            payAccount.setAccountType(1);
            payAccount.setBusinessDay(businessDay);
            payAccount.setRemark("小程序下单：" + goodSummary.toString());
            payAccount.setRoomInfoId(regist.getRoomNumId());
            payAccount.setRoomTypeId(regist.getRoomTypeId());
            payAccount.setRoomNum(regist.getRoomNum());
            payAccount.setRegistPersonId(registPerson.getRegistPersonId());
            payAccount.setRegistPersonName(registPerson.getPersonName());
            payAccount.setRegistId(regist.getRegistId());
            payAccount.setGroupAccount(0);
            if(goodsShoppingcar.getSumPrice()>0){
                addAccounts.add(payAccount);
            }

            // 添加商品推送数据
            GoodsShoppingOrder goodsShoppingOrder = new GoodsShoppingOrder();
            goodsShoppingOrder.setHid(regist.getHid());
            goodsShoppingOrder.setHotelGroupId(regist.getHotelGroupId());
            goodsShoppingOrder.setMoney(goodsShoppingcar.getSumPrice());
            goodsShoppingOrder.setPayTypeName(payAccount.getPayClassName());
            goodsShoppingOrder.setState(2);
            goodsShoppingOrder.setSn(goodsShoppingcar.getSn());
            goodsShoppingOrder.setRemark("微信小程序下单");
            goodsShoppingOrder.setInvoiceId(0);
            goodsShoppingOrder.setShoppingYear(user.getBusinessYear());
            goodsShoppingOrder.setShoppingYearMonth(user.getBusinessMonth());
            goodsShoppingOrder.setBusinessDay(businessDay);
            goodsShoppingOrder.setWechatAccount(user.getSessionId());
            goodsShoppingOrder.setOrderType(2);
            goodsShoppingOrder.setPayTime(date);
            goodsShoppingOrder.setWechatName(user.getUserName());
            goodsShoppingOrder.setAddress(regist.getRoomNum());
            goodsShoppingOrder.setRoomInfoId(regist.getRoomNumId());
            goodsShoppingOrder.setRoomTypeId(regist.getRoomTypeId());
            goodsShoppingOrder.setRoomTypeName(regist.getRoomTypeName());
            goodsShoppingOrder.setDiscountAmount(0);
            goodsShoppingOrder.setFreightAmount(0);
            goodsShoppingOrder.setGoodsSummary(goodSummary.toString());
            goodsShoppingOrder.setWechatId(goodsShoppingcar.getCardGroupId());
            goodsShoppingOrder.setRoomInfoId(regist.getRoomNumId());
            goodsShoppingOrder.setRoomNo(regist.getRoomNum());
            goodsShoppingOrder.setRoomTypeId(regist.getRoomTypeId());
            goodsShoppingOrder.setRoomTypeName(regist.getRoomTypeName());

            //用户信息
            goodsShoppingOrder.setCreateTime(date);
            goodsShoppingOrder.setClassId(user.getClassId());
            goodsShoppingOrder.setCreateUserId(user.getUserId());
            goodsShoppingOrder.setCreateUserName(user.getUserName());
            goodsShoppingOrder.setUpdateTime(date);
            goodsShoppingOrder.setUpdateUserId(user.getUserId());
            goodsShoppingOrder.setUpdateUserName(user.getUserName());
            goodsShoppingOrder.setUpdateClassId(user.getClassId() + "");

            final Integer id =  wxGoodsServiceTransaction.addGoodsToRegist(regist, addAccounts, goodsShoppingOrder,goodsSaleRecords);

            final String s1 = goodSummary.toString();

            //调用商品销售的服务
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {

                        HashMap<String, String> filed = new HashMap<>();
                        filed.put("roomNo", regist.getRoomNum());
                        filed.put("goodsSummary", s1);
                        HashMap<String, String> dataMap = new HashMap<>();
                        dataMap.put("shoppingId", id + "");
                        dataMap.put("registId", regist.getRegistId() + "");
                        dataMap.put("type","1");
                        HashMap<String, String> onClickCbData = new HashMap<>();
                        onClickCbData.put("shoppingId", id + "");
                        onClickCbData.put("registId", regist.getRegistId() + "");
                        onClickCbData.put("type","1");
                        baseService.push(user.getHotelGroupId(), user.getHid(), 28, filed, dataMap, true, true, onClickCbData);

                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 房间零售
     *
     * @param goodsShoppingcar
     * @param user
     * @return
     */
    public ResponseData addGoodsForLs(AddGoodsParam addGoodsParam, GoodsShoppingcar goodsShoppingcar, TbUserSession user) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {


            // 查询营业日期
            HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
            hotelBusinessDaySearch.setHid(goodsShoppingcar.getHid());
            HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);

            Integer businessDay = hotelBusinessDay.getBusinessDay();

            RoomInfo roomInfo = roomInfoDao.selectById(goodsShoppingcar.getRoomNumId());


            StringBuilder goodSummary = new StringBuilder();

            GoodsShoppingcarDetailsSearch goodsShoppingcarDetailsSearch = new GoodsShoppingcarDetailsSearch();
            goodsShoppingcarDetailsSearch.setGoodsShoppingId(goodsShoppingcar.getId());
            Page<GoodsShoppingcarDetails> goodsShoppingcarDetails = goodsShoppingcarDetailsDao.selectBySearch(goodsShoppingcarDetailsSearch);

            ArrayList<Account> addAccounts = new ArrayList<>();


            // 商品出售记录
            ArrayList<GoodsSaleRecord> goodsSaleRecords = new ArrayList<>();

            Date date = new Date();

            for (GoodsShoppingcarDetails gsd : goodsShoppingcarDetails) {

                String no = OrderNumUtils.getNo(goodsShoppingcar.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);

                goodSummary.append(gsd.getGoodsInfoName());
                goodSummary.append("*");
                goodSummary.append(gsd.getGoodsNum());
                goodSummary.append("  ");

                Account account = new Account();

                account.setAccountId(no);
                account.setPrice(gsd.getSumPrice());
                account.setPayType(1);
                account.setIsSale(0);

                account.setHid(goodsShoppingcar.getHid());
                account.setHotelGroupId(goodsShoppingcar.getHotelGroupId());

                account.setPayClassId(10);
                account.setPayClassName("客房");
                account.setPayCodeId("3100");
                account.setPayCodeName("小商品");


                account.setGoodId(gsd.getGoodsInfoId());
                account.setGoodName(gsd.getGoodsInfoName());
                account.setUintPrice(gsd.getPrice());
                account.setSaleNum(gsd.getGoodsNum());

                account.setRegistState(1);
                account.setIsCancel(0);

                account.setRoomInfoId(goodsShoppingcar.getRoomNumId());
                account.setRoomTypeId(roomInfo.getRoomTypeId());
                account.setRoomNum(goodsShoppingcar.getRoomNum());

                //用户信息
                account.setCreateTime(date);
                account.setCreateUserId(user.getUserId());
                account.setCreateUserName(user.getUserName());
                account.setUpdateTime(date);
                account.setUpdateUserId(user.getUserId());
                account.setUpdateUserName(user.getUserName());

                account.setClassId(user.getClassId());
                account.setUpdateCalssId(user.getClassId());

                account.setRemark(gsd.getGoodsInfoName() + "*" + gsd.getGoodsNum());

                // 营业日期
                account.setAccountYearMonth(user.getBusinessMonth());
                account.setAccountYear(user.getBusinessYear());
                account.setBusinessDay(businessDay);
                account.setAccountType(2);
                addAccounts.add(account);


                //2.增加销售记录
                GoodsSaleRecord record = new GoodsSaleRecord();
                record.setHid(user.getHid());
                record.setHotelGroupId(user.getHotelGroupId());
                //  record.setGoodsStockId(goodsStock.getGoodsStockId());
                // record.setGoodsStockName(goodsStock.getGoodsStockName());


                record.setGoodsInfoId(gsd.getGoodsInfoId());
                record.setGoodsInfoName(gsd.getGoodsInfoName());
                record.setGoodsClassId(gsd.getGoodsClassId());
                record.setGoodsClassName(gsd.getGoodsClassName());


                record.setPrice(gsd.getPrice());
                record.setTotalMoney(gsd.getSumPrice());
                record.setNum(gsd.getGoodsNum());
                record.setRemark(roomInfo.getRoomNum()+"通过房间扫码下单");

                //regist信息
                //record.setRegistId(regist.getRegistId());
                record.setRoomNum(roomInfo.getRoomNum());
                record.setRoomNumId(roomInfo.getRoomInfoId());
                // record.setRegistPersionId(registPerson.getRegistPersonId());
                //record.setRegistPersionName(registPerson.getPersonName());


                //业务信息
                record.setSourceTypeId(5);
                record.setSourceTypeName("小程序零售");
                record.setAccountId(no);

                //bussiness信息
                record.setBusinessDay(businessDay);
                record.setBusinessYear(HotelUtils.getNowyyyyInt());
                record.setBusinessMonth(HotelUtils.getNowyyyyMMInt());

                //user信息
                record.setHid(user.getHid());
                record.setHotelGroupId(user.getHotelGroupId());
                record.setClassId(user.getClassId());
                record.setCreateUserId(user.getUserId());
                record.setCreateUserName(user.getUserName());
                record.setUpdateUserId(user.getUserId());
                record.setUpdateUserName(user.getUserName());

                //出售
                record.setSaleType(1);
                record.setHotelIsCheckGoodsNum(goodsShoppingcar.getStcokCheck());
                goodsSaleRecords.add(record);
            }

            // 添加支付信息
            Account payAccount = new Account(user);
            String no = OrderNumUtils.getNo(goodsShoppingcar.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
            payAccount.setIsCancel(0);
            payAccount.setAccountYear(user.getBusinessYear());
            payAccount.setAccountYearMonth(user.getBusinessMonth());
            payAccount.setBusinessDay(user.getBusinessDay());
            payAccount.setClassId(user.getClassId());
            payAccount.setSettleAccountTime(new Date());
            payAccount.setHid(goodsShoppingcar.getHid());
            payAccount.setHotelGroupId(goodsShoppingcar.getHotelGroupId());
            //设置账务关联人id为0
            payAccount.setRegistPersonId(0);
            payAccount.setRegistPersonName("");
            payAccount.setThirdRefundState(0);
            payAccount.setAccountId(no);
            payAccount.setPrice(goodsShoppingcar.getSumPrice());
            payAccount.setPayType(2);
            payAccount.setPayClassId(12);
            payAccount.setPayClassName("微信支付");
            payAccount.setPayCodeId("9340");
            payAccount.setPayCodeName("微信公众号支付");
            payAccount.setIsSale(0);
            payAccount.setUintPrice(goodsShoppingcar.getSumPrice());
            payAccount.setSaleNum(1);
            payAccount.setRegistState(0);
            payAccount.setSettleAccountTime(new Date());
            payAccount.setThirdAccoutId(addGoodsParam.getMainId());
            payAccount.setRefundPrice(0);
            payAccount.setThirdRefundState(0);
            payAccount.setAccountType(1);
            payAccount.setBusinessDay(businessDay);
            payAccount.setRemark("小程序下单：" + goodSummary.toString());
            payAccount.setRoomInfoId(roomInfo.getRoomInfoId());
            payAccount.setRoomTypeId(roomInfo.getRoomTypeId());
            payAccount.setRoomNum(roomInfo.getRoomNum());
            addAccounts.add(payAccount);

            // 添加商品推送数据
            GoodsShoppingOrder goodsShoppingOrder = new GoodsShoppingOrder();
            goodsShoppingOrder.setHid(roomInfo.getHid());
            goodsShoppingOrder.setHotelGroupId(roomInfo.getHotelGroupId());
            goodsShoppingOrder.setMoney(goodsShoppingcar.getSumPrice());
            goodsShoppingOrder.setPayTypeName(payAccount.getPayClassName());
            goodsShoppingOrder.setState(2);
            goodsShoppingOrder.setSn(goodsShoppingcar.getSn());
            goodsShoppingOrder.setRemark("微信小程序下单");
            goodsShoppingOrder.setInvoiceId(0);
            goodsShoppingOrder.setShoppingYear(user.getBusinessYear());
            goodsShoppingOrder.setShoppingYearMonth(user.getBusinessMonth());
            goodsShoppingOrder.setBusinessDay(businessDay);
            goodsShoppingOrder.setWechatAccount(user.getSessionId());
            goodsShoppingOrder.setOrderType(2);
            goodsShoppingOrder.setPayTime(date);
            goodsShoppingOrder.setWechatId(goodsShoppingcar.getCardGroupId());
            goodsShoppingOrder.setWechatName(user.getUserName());
            goodsShoppingOrder.setAddress(roomInfo.getRoomNum());
            goodsShoppingOrder.setRoomInfoId(roomInfo.getRoomInfoId());
            goodsShoppingOrder.setRoomTypeId(roomInfo.getRoomTypeId());
            goodsShoppingOrder.setRoomTypeName(roomInfo.getRoomTypeName());
            goodsShoppingOrder.setDiscountAmount(0);
            goodsShoppingOrder.setFreightAmount(0);
            goodsShoppingOrder.setGoodsSummary(goodSummary.toString());
            goodsShoppingOrder.setRoomInfoId(roomInfo.getRoomInfoId());
            goodsShoppingOrder.setRoomNo(roomInfo.getRoomNum());
            goodsShoppingOrder.setRoomTypeId(roomInfo.getRoomTypeId());
            goodsShoppingOrder.setRoomTypeName(roomInfo.getRoomTypeName());

            //用户信息
            goodsShoppingOrder.setCreateTime(date);
            goodsShoppingOrder.setClassId(user.getClassId());
            goodsShoppingOrder.setCreateUserId(user.getUserId());
            goodsShoppingOrder.setCreateUserName(user.getUserName());
            goodsShoppingOrder.setUpdateTime(date);
            goodsShoppingOrder.setUpdateUserId(user.getUserId());
            goodsShoppingOrder.setUpdateUserName(user.getUserName());
            goodsShoppingOrder.setUpdateClassId(user.getClassId() + "");

            // 新增哑房账信息
            GoodsDumb goodsDumb = new GoodsDumb();
            goodsDumb.setUpdateTime(date);
            goodsDumb.setUpdateUserName(user.getUserName());
            goodsDumb.setUpdateUserId(user.getUserId());

            goodsDumb.setCardInfoId(goodsShoppingcar.getCardId());
            goodsDumb.setCardName(goodsShoppingcar.getCreateUserName());
            goodsDumb.setRemark(goodSummary.toString());
            goodsDumb.setAccountType(1);
            goodsDumb.setPayType(payAccount.getPayCodeName());
            goodsDumb.setSumPrice(goodsShoppingcar.getSumPrice());
            goodsDumb.setState(1);

            /**
             * id 为空说明是添加
             *    不为空说明是修改
             */
            goodsDumb.setHid(user.getHid());
            goodsDumb.setHotelGroupId(user.getHotelGroupId());
            goodsDumb.setCreateTime(date);
            goodsDumb.setCreateUserName(user.getUserName());
            goodsDumb.setCreateUserId(user.getUserId());
            goodsDumb.setState(1);
            goodsDumb.setBusinessDay(user.getBusinessDay());
            /**
             * 冗余字段，营业日期 年
             */
            goodsDumb.setDumbYear(user.getBusinessYear());

            /**
             * 冗余字段，营业日子 年月
             */
            goodsDumb.setDumbYearMonth(user.getBusinessMonth());
            goodsDumb.setSn(addGoodsParam.getMainId());

            /**
             * 班次 id
             */
            goodsDumb.setClassId(user.getClassId());

            final  Integer id =  wxGoodsServiceTransaction.addGoodsToLs(goodsDumb, addAccounts, goodsShoppingOrder,goodsSaleRecords);

            final String s1 = goodSummary.toString();
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {

                        HashMap<String, String> filed = new HashMap<>();
                        filed.put("roomNo", roomInfo.getRoomNum());
                        filed.put("goodsSummary", s1);
                        HashMap<String, String> dataMap = new HashMap<>();
                        HashMap<String, String> onClickCbData = new HashMap<>();
                        dataMap.put("shoppingId", id + "");
                        dataMap.put("registId",  "-1");
                        dataMap.put("type","2"); // 在房间中
                        onClickCbData.put("shoppingId", id + "");
                        onClickCbData.put("registId", "-1");
                        onClickCbData.put("type","2"); // 在房间中
                        baseService.push(user.getHotelGroupId(), user.getHid(), 28, filed, dataMap, true, true, onClickCbData);

                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData searchGoodsRecord(GoodsShoppingOrderSearch goodsShoppingOrderSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken =goodsShoppingOrderSearch.getSessionToken();
            TbUserSession user = this.getTbUserSession(sessionToken);

            Page<GoodsShoppingOrder> goodsShoppingOrders = goodsShoppingOrderDao.selectBySearch(goodsShoppingOrderSearch);
            responseData.setData(goodsShoppingOrders);

        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }

    @Override
    public ResponseData cancelWxSaleGoods(GoodsShoppingOrderSearch goodsShoppingOrderSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken =goodsShoppingOrderSearch.getSessionToken();
            TbUserSession user = this.getTbUserSession(sessionToken);

            GoodsShoppingOrder goodsShoppingOrder = goodsShoppingOrderDao.selectById(goodsShoppingOrderSearch.getId());


        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }

    /**
     * 取消预订单
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData cancelBook(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            int bookingOrderId = param.getInt("bookingOrderId");

            //1.查询预订单
            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderId);
            if (!user.getHid().equals(bookingOrder.getHid())) {
                throw new Exception("此订单有误");
            }

            if (bookingOrder.getOrderStatus() != BOOK.STA_YX && bookingOrder.getOrderStatus() != BOOK.STA_NS) {
                throw new Exception("当前订单状态不允许取消");
            }

            if (!param.containsKey("reason")) {
                throw new Exception("取消理由不能空");
            }
            bookingOrder.setCancelBookingLation(param.getString("reason"));
            //查询当前订单的账务信息，如果账不平则不允许取消
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setBookingId(bookingOrderId);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            int cash = 0;
            int cost = 0;
            for (int i = 0; i < accounts.size(); i++) {
                Account account = accounts.get(i);
                if (account.getRegistState() != 0 || account.getIsCancel() != 0) {
                    continue;
                }
                if (account.getPayType() == 1) {
                    cost += account.getPrice();
                }
                if (account.getPayType() == 2) {
                    cash += account.getPrice();
                }
            }

            if (cash != cost) {
                throw new Exception("请先处理账务信息");
            }
            //2.查询预订房型
            BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
            bookingOrderRoomTypeSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            List<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);

            //3.查询预订房间
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingOrderId);
            List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            //4.查询当前预订单的辅助房态
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setBookingOrderId(bookingOrderId);
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

            ArrayList<BookingOrder> bookingOrders = new ArrayList<>();
            bookingOrders.add(bookingOrder);
            wxServiceTransaction.cancelOrderService(bookingOrders, bookingOrderRoomTypes, bookingOrderRoomNums, user, roomAuxiliaryRelations);


        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData findGoodsByWechat(HotelGoodsRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken = RSAUtils.getStringDecrypt(request.getHotelId());
            ;
            final TbUserSession user = this.getTbUserSession(sessionToken);
            request.setHid(user.getHid());
            GoodsClassSearch goodsClassSearch = new GoodsClassSearch();
            goodsClassSearch.setHid(request.getHid());
            // 小程序 且  传 hid
            Page<GoodsClass> goodsClasses = goodsClassDao.selectBySearch(goodsClassSearch);

            //  查询商品小类
            List<ShowGoodsView> showGoodsViews = new ArrayList<>();
            //核算库存查询,goodsStockNum
            request.setState(1);
            if (request.getHotelIsCheckGoodsNum() != null && request.getHotelIsCheckGoodsNum().equals(1)) {
                GoodsStockGoodsNumSearch search = new GoodsStockGoodsNumSearch();
                search.setHid(request.getHid());
                search.setSearchValue(request.getSearchValue());
                showGoodsViews = goodsDao.findShowGoodsNumView(request);
                for (ShowGoodsView showGoodsView : showGoodsViews) {
                    GoodsInfo goodsInfo = goodsInfoDao.selectById(showGoodsView.getGoodsInfoId());
                    showGoodsView.setGoodsClassId(goodsInfo.getGoodsClassId());
                    showGoodsView.setGoodsClassName(goodsInfo.getGoodsClassName());
                    showGoodsView.setPrice(goodsInfo.getPrice());
                    showGoodsView.setVipPrice(goodsInfo.getVipPrice());
                    showGoodsView.setUnit(goodsInfo.getUnit());
                    showGoodsView.setGoodsImg(goodsInfo.getImageFileUrl());
                }
            } else { //不核算库存查询所有的商品
                showGoodsViews = goodsDao.findShowGoodsView(request);
            }

            Map<Integer, List<ShowGoodsView>> goodsMap = showGoodsViews.stream().collect(Collectors.groupingBy(ShowGoodsView::getGoodsClassId));

            JSONArray resClass = new JSONArray();

            for (GoodsClass goodsClass : goodsClasses) {

                JSONObject jo = new JSONObject();
                jo.put("id", goodsClass.getGoodsClassId());
                jo.put("name", goodsClass.getClassInfoName());

                List<ShowGoodsView> showGoodsViews1 = goodsMap.get(goodsClass.getGoodsClassId());

                jo.put("children", showGoodsViews1);

                resClass.add(jo);
            }

            responseData.setData(resClass);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    public ResponseData findGoodsRecord(GoodsShoppingOrderSearch request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken = RSAUtils.getStringDecrypt(request.getHotelId());
            final TbUserSession user = this.getTbUserSession(sessionToken);
            request.setHid(user.getHid());
            Page<GoodsShoppingOrder> goodsShoppingOrders = goodsShoppingOrderDao.selectBySearch(request);

            if(goodsShoppingOrders==null||goodsShoppingOrders.size()<1){
                return responseData;
            }

            String ids = "";

            for(GoodsShoppingOrder  gds:goodsShoppingOrders){
                ids+=gds.getId()+",";
            }
            ids+="0";

            GoodsShoppingcarDetailsSearch goodsShoppingcarDetailsSearch = new GoodsShoppingcarDetailsSearch();
            goodsShoppingcarDetailsSearch.setHid(user.getHid());
            goodsShoppingcarDetailsSearch.setGoodsShoppingIds(ids);
            Page<GoodsShoppingcarDetails> goodsShoppingcarDetails = goodsShoppingcarDetailsDao.selectBySearch(goodsShoppingcarDetailsSearch);


            Map<Integer, List<GoodsShoppingcarDetails>> collect = goodsShoppingcarDetails.stream().collect(Collectors.groupingBy(GoodsShoppingcarDetails::getGoodsInfoId));



            Page<JSONObject> baseList = new Page<JSONObject>();

            for(GoodsShoppingOrder gso:goodsShoppingOrders){

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("sn",gso.getSn());
                jsonObject.put("id",gso.getId());
                jsonObject.put("createTimeStr",HotelUtils.parseDate2Str(gso.getCreateTime()));
                jsonObject.put("goodsSummary",gso.getGoodsSummary());
                jsonObject.put("totalPrice",gso.getMoney());
                jsonObject.put("status",gso.getState());
                jsonObject.put("roomNo",gso.getRoomNo());

                List<GoodsShoppingcarDetails> gsds = collect.get(gso.getId());

                JSONArray array = new JSONArray();

                for(GoodsShoppingcarDetails gsd:gsds){

                    JSONObject jo = new JSONObject();
                    jo.put("name",gsd.getGoodsInfoName());
                    jo.put("num",gsd.getGoodsNum());
                    jo.put("img",gsd.getId());
                    jo.put("price",gsd.getPrice());
                }


            }



        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
