package com.pms.czabsorders.service.account.transaction.impl;

import com.pms.czabsorders.service.account.transaction.AccountTransaction;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountCancel;
import com.pms.czaccount.bean.account.AccountThirdPayRecode;
import com.pms.czaccount.dao.account.AccountCancelDao;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czaccount.dao.account.AccountThirdPayRecodeDao;
import com.pms.czmembership.bean.company.HotelCompanyAccount;
import com.pms.czmembership.bean.company.HotelCompanyAccountInfo;
import com.pms.czmembership.bean.company.HotelCompanyArRecode;
import com.pms.czmembership.bean.member.*;
import com.pms.czmembership.dao.company.HotelCompanyAccountDao;
import com.pms.czmembership.dao.company.HotelCompanyAccountInfoDao;
import com.pms.czmembership.dao.company.HotelCompanyArRecodeDao;
import com.pms.czmembership.dao.member.*;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.OrderNumUtils;
import com.pms.czpmsutils.StringUtil;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.regist.CHECK_IN_TYPE;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.dao.BookingOrderDailyPriceDao;
import com.pms.pmsorder.dao.BookingOrderDao;
import com.pms.pmsorder.dao.RegistDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Service
@Primary
public class AccountTransactionImpl extends BaseService implements AccountTransaction {

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private AccountCancelDao accountCancelDao;

    @Autowired
    private AccountThirdPayRecodeDao accountThirdPayRecodeDao;

    @Autowired
    private HotelCompanyArRecodeDao hotelCompanyArRecodeDao;

    @Autowired
    private CardConsumptionRecordDao cardConsumptionRecordDao;
    @Autowired
    private RegistDao registDao;
    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private HotelCompanyAccountInfoDao hotelCompanyAccountInfoDao;

    @Autowired
    private HotelCompanyAccountDao hotelCompanyAccountDao;

    @Autowired
    private CardFreezeRecordDao cardFreezeRecordDao;

    @Autowired
    private CardInfoDao cardInfoDao;

    @Autowired
    private CardGroupInfoDao cardGroupInfoDao;

    @Autowired
    private CardOperationRecordDao cardOperationRecordDao;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeFinishMemberFreeze(CardFreezeRecord cardFreezeRecord, CardInfo cardInfo, CardGroupInfo cardGroupInfo, CardOperationRecord cardOperationRecord, Account account) throws Exception {
        Integer result = 0;
        if (null != cardFreezeRecord) {
            result = cardFreezeRecordDao.update(cardFreezeRecord);
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        if (null != cardInfo) {
            result = cardInfoDao.update(cardInfo);
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        if (null != cardGroupInfo) {
            result = cardGroupInfoDao.update(cardGroupInfo);
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        if (null != cardOperationRecord) {
            result = cardOperationRecordDao.saveCardOperationRecord(cardOperationRecord);
            if (result < 1) {
                throw new Exception(HOTEL_CONST.INSERTERR);
            }
        }

        if (null != account) {
            result = accountDao.editAccount(account);
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void addAccountTransaction(Account account, AccountThirdPayRecode accountThirdPayRecode, ArrayList<Oprecord> oprecords, Regist regist, BookingOrderDailyPrice bookingOrderDailyPrice) throws Exception {
        Integer integer = 0;
        integer = accountDao.saveAccount(account);
        String thirdAccoutId = account.getThirdAccoutId();
        if (!StringUtil.isEmpty(thirdAccoutId)) {
            accountDao.insertAccountInfo(account.getThirdAccoutId());
        }

        if (integer < 1) {
            throw new Exception("插入账务信息失败");
        }
        if (accountThirdPayRecode != null) {
            Integer update = accountThirdPayRecodeDao.insert(accountThirdPayRecode);
            if (update < 1) {
                throw new Exception("插入账务信息失败");
            }
        }
        if (regist != null) {
            Regist registInfo = Regist.CreateRegist(regist.getRegistId());
            Integer price = account.getPrice();
            if (account.getPayType() == 1) {
                // regist.setSumSale(price + regist.getSumSale());
                registInfo.setSumSale(price + regist.getSumSale());
            } else if (account.getPayType() == 2) {
                // regist.setSumPay(price + regist.getSumPay());
                registInfo.setSumPay(price + regist.getSumPay());
            }
            Integer update = registDao.update(registInfo);
            if (update < 1) {
                throw new Exception("更新主单状态失败");
            }
        }
        if (bookingOrderDailyPrice != null) {
            bookingOrderDailyPrice.setDailyState(0);
            bookingOrderDailyPriceDao.editBookingOrderDailyPrice(bookingOrderDailyPrice);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void balanceAccountTransaction(Account account, AccountThirdPayRecode accountThirdPayRecode, Regist regist, BookingOrder bookingOrder, ArrayList<Oprecord> oprecords) throws Exception {
        Integer update = 0;
        if (accountThirdPayRecode != null) {
            update = accountThirdPayRecodeDao.update(accountThirdPayRecode);
            if (update < 1) {
                throw new Exception("更新账务信息失败");
            }
        }
        if (regist != null) {
            update = registDao.update(regist);
            if (update < 1) {
                throw new Exception("更新主单信息失败");
            }
        }

        if (bookingOrder != null) {
            update = bookingOrderDao.editBookingOrder(bookingOrder);
            if (update < 1) {
                throw new Exception("更新预订单信息失败");
            }
        }

        update = accountDao.editAccount(account);
        if (update < 1) {
            throw new Exception("更新账务信息失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void setOffTransaction(ArrayList<Account> accounts, ArrayList<AccountThirdPayRecode> accountThirdPayRecodes, ArrayList<HotelCompanyArRecode> hotelCompanyArRecodes, HotelCompanyAccountInfo hotelCompanyAccountInfo, ArrayList<CardConsumptionRecord> cardConsumptionRecords, ArrayList<CardFreezeRecord> cardFreezeRecords, ArrayList<Oprecord> oprecords, TbUserSession user, Regist regist) throws Exception {

        Date date = new Date();
        Integer cash = 0;
        Integer cost = 0;
        for (int i = 0; i < accounts.size(); i++) {
            Account account = accounts.get(i);
            if (account.getPayType() == 1) {
                cost += account.getPrice();
            } else if (account.getPayType() == 2) {
                cash += account.getPrice();
            }
            Integer integer = accountDao.editAccount(account);
            if (integer < 1) {
                throw new Exception("冲账失败");
            }
            AccountCancel accountCancel = new AccountCancel();
            accountCancel.setHid(account.getHid());
            accountCancel.setHotelGroupId(account.getHotelGroupId());
            accountCancel.setAccountId(account.getAccountId());
            accountCancel.setPrice(account.getPrice());
            accountCancel.setPayType(account.getPayType());
            accountCancel.setPayClassName(account.getPayClassName());
            accountCancel.setPayCodeId(Integer.parseInt(account.getPayCodeId()));
            accountCancel.setPayCodeName(account.getPayCodeName());
            accountCancel.setRoomInfoId(account.getRoomInfoId());
            accountCancel.setRoomNum(account.getRoomNum());
            accountCancel.setAccountCode(account.getAccountCode());
            accountCancel.setAccountCreateUserName(account.getCreateUserName());
            accountCancel.setIsSale(account.getIsSale());
            accountCancel.setBusinessDay(account.getBusinessDay());
            accountCancel.setClassId(account.getClassId());
            accountCancel.setCreateTime(date);
            accountCancel.setCreateUserId(user.getUserId());
            accountCancel.setCreateUserName(user.getUserName());
            accountCancel.setRegistId(account.getRegistId());
            accountCancel.setBookingId(account.getBookingId());
            accountCancel.setCancelType(2);

            Oprecord oprecord = new Oprecord(user);
            oprecord.setHid(user.getHid());
            oprecord.setDescription(account.getPayCodeName() + "-冲账，金额-" + account.getPrice() / 100.00 + "元");
            if (account.getRegistId() != null) {
                oprecord.setRegistId(account.getRegistId());
            }
            if (account.getBookingId() != null) {
                oprecord.setBookingOrderId(account.getBookingId());
            }
            integer = accountCancelDao.insert(accountCancel);
            if (integer < 1) {
                throw new Exception("冲账失败");
            }
            oprecords.add(oprecord);
        }

        for (int i = 0; i < accountThirdPayRecodes.size(); i++) {
            AccountThirdPayRecode accountThirdPayRecode = accountThirdPayRecodes.get(i);
            Integer update = accountThirdPayRecodeDao.update(accountThirdPayRecode);
            if (update < 1) {
                throw new Exception("修改第三方支付记录失败");
            }
        }


        for (int i = 0; i < hotelCompanyArRecodes.size(); i++) {
            HotelCompanyArRecode hotelCompanyArRecode = hotelCompanyArRecodes.get(i);
            Integer update = hotelCompanyArRecodeDao.update(hotelCompanyArRecode);
            if (update < 1) {
                throw new Exception("修改AR账务信息失败");
            }
        }

        if (hotelCompanyAccountInfo != null) {
            Integer update = hotelCompanyAccountInfoDao.update(hotelCompanyAccountInfo);
            if (update < 1) {
                throw new Exception("修改AR账务信息失败");
            }
        }

        for (int i = 0; i < cardConsumptionRecords.size(); i++) {
            CardConsumptionRecord cardConsumptionRecord = cardConsumptionRecords.get(i);
        }

        for (int i = 0; i < cardFreezeRecords.size(); i++) {
            CardFreezeRecord cardFreezeRecord = cardFreezeRecords.get(i);
        }
//        if (regist != null) {
//            regist.setSumPay(regist.getSumPay() - cash);
//            regist.setSumSale(regist.getSumSale() - cost);
//            Integer update = registDao.update(regist);
//            if (update < 1) {
//                throw new Exception("修改订单信息失败");
//            }
//        }
        this.addOprecords(oprecords);

    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void refundMoneyTransaction(Account account, Account refundAccount, ArrayList<Oprecord> oprecords, TbUserSession user, Regist regist, BookingOrder bookingOrder) throws Exception {

        Integer result = accountDao.editAccount(account);
        if (result < 1) {
            throw new Exception("修改帐务信息失败");
        }
        result = accountDao.saveAccount(refundAccount);
        if (result < 1) {
            throw new Exception("插入帐务信息失败");
        }
        if (regist != null && regist.getRegistId() > 0) {
            result = registDao.update(regist);
            if (result < 1) {
                throw new Exception("修改主单信息失败");
            }
        }
        if (bookingOrder != null && bookingOrder.getBookingOrderId() > 0) {
            result = bookingOrderDao.editBookingOrder(bookingOrder);
            if (result < 1) {
                throw new Exception("修改订单信息失败");
            }
        }
        this.addOprecords(oprecords);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void autoAddRoomPrice(BookingOrderDailyPrice bookingOrderDailyPrice, Regist regist, RegistPerson registPerson, TbUserSession user) throws Exception {

        String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
        // 1.修改价格信息改为已结
        bookingOrderDailyPrice.setDailyState(0);
        bookingOrderDailyPriceDao.editBookingOrderDailyPrice(bookingOrderDailyPrice);
        // 2.添加账务信息
        Account account = new Account();
        account.setAccountId(accountId);
        account.setHid(user.getHid());
        account.setHotelGroupId(user.getHotelGroupId());
        account.setCreateUserId(user.getUserId());
        account.setCreateUserName(user.getUserName());
        account.setCreateTime(new Date());
        account.setIsCancel(0);
        account.setAccountYear(user.getBusinessYear());
        account.setAccountYearMonth(user.getBusinessMonth());
        account.setBusinessDay(user.getBusinessDay());
        account.setClassId(user.getClassId());
        account.setRegistId(regist.getRegistId());
        account.setBookingId(regist.getBookingOrderId());
        account.setTeamCodeId(regist.getTeamCodeId());
        account.setPrice(bookingOrderDailyPrice.getPrice());
        account.setPayType(1);

        account.setPayClassId(10);
        account.setPayClassName("客房");
        account.setPayCodeId("0002");
        account.setPayCodeName("全天房费");
        if (regist.getCheckinType() == CHECK_IN_TYPE.CIT_HOURDAY) {
            account.setPayCodeId("0007");
            account.setPayCodeName("钟点房费");
        }

        account.setRoomInfoId(regist.getRoomNumId());
        account.setRoomTypeId(regist.getRoomTypeId());
        account.setRoomNum(regist.getRoomNum());
        account.setIsSale(1);

        account.setUintPrice(bookingOrderDailyPrice.getPrice());
        account.setSaleNum(1);
        account.setRegistState(0);
        account.setRemark("开房自动产生房费");
        account.setSettleAccountTime(new Date());
        account.setRefundPrice(0);
        account.setThirdRefundState(0);
        account.setAccountType(1);

        account.setThirdAccoutId(bookingOrderDailyPrice.getId() + "");

        account.setRegistPersonId(registPerson.getRegistPersonId());
        account.setRegistPersonName(registPerson.getPersonName());

        Integer integer = 0;
        try {
            integer = accountDao.saveAccount(account);
        } catch (Exception e) {
            String uuid = HotelUtils.getUUID();
            account.setAccountId(uuid);
            integer = accountDao.saveAccount(account);
        }
        if (integer < 1) {
            throw new Exception("自动添加房费信息失败");
        }

        regist.setSumSale(regist.getSumSale() + bookingOrderDailyPrice.getPrice());

        Integer update = registDao.update(regist);

        if (update < 1) {
            throw new Exception("自动添加房费信息失败");
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void tranAccountFunc(ArrayList<Account> addAccounts, ArrayList<Account> upaAccounts, ArrayList<AccountCancel> addAccountCancels) throws Exception {

        if (addAccounts.size() > 0) {
            accountDao.saveAccountList(addAccounts);
        }
        if (upaAccounts.size() > 0) {
            accountDao.editAccountList(upaAccounts);
        }
        if (addAccountCancels.size() > 0) {
            accountCancelDao.insertList(addAccountCancels);
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void addAccountForArTransaction(Account account, HotelCompanyArRecode hotelCompanyArRecode, HotelCompanyAccount hotelCompanyAccount, HotelCompanyAccountInfo hotelCompanyAccountInfo, List<Oprecord> oprecords) throws Exception {
        Integer result = 0;
        result = hotelCompanyArRecodeDao.insert(hotelCompanyArRecode);
        if (result < 1) {
            throw new Exception(HOTEL_CONST.INSERTERR);
        }
        account.setThirdAccoutId(hotelCompanyArRecode.getId().toString());
        result = accountDao.saveAccount(account);
        if (result < 1) {
            throw new Exception(HOTEL_CONST.INSERTERR);
        }
        hotelCompanyAccount.setNoSettleMoney(hotelCompanyAccount.getNoSettleMoney() + hotelCompanyArRecode.getMoney());
        hotelCompanyAccountInfo.setNoOffWriteMoney(hotelCompanyAccountInfo.getNoOffWriteMoney() + hotelCompanyArRecode.getMoney());
        hotelCompanyAccountInfo.setMaxLimit(hotelCompanyAccountInfo.getMaxLimit() - hotelCompanyArRecode.getMoney());
        result = hotelCompanyAccountDao.update(hotelCompanyAccount);
        if (result < 1) {
            throw new Exception(HOTEL_CONST.UPDATEERR);
        }
        result = hotelCompanyAccountInfoDao.update(hotelCompanyAccountInfo);
        if (result < 1) {
            throw new Exception(HOTEL_CONST.UPDATEERR);
        }
    }
}
