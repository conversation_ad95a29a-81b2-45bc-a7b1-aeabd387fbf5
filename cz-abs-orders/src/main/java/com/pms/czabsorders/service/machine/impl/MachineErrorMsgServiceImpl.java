package com.pms.czabsorders.service.machine.impl;

import com.pms.czabsorders.service.machine.MachineErrorMsgService;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.machine.MachineErrorMsg;
import com.pms.pmsorder.dao.MachineErrorMsgDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Primary
@Slf4j
public class MachineErrorMsgServiceImpl extends BaseService implements MachineErrorMsgService {

    @Autowired
    private MachineErrorMsgDao machineErrorMsgDao;

    @Override
    public JSONObject addMachineErrorMsg(JSONObject param) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            MachineErrorMsg machineErrorMsg = new MachineErrorMsg();
            machineErrorMsg.setHid(user.getHid());
            machineErrorMsg.setHotelGroupId(user.getHotelGroupId());
            machineErrorMsg.setPmsType(param.getInt("pmsType"));
            machineErrorMsg.setParam(param.getString("param"));
            machineErrorMsg.setUrl(param.getString("url"));
            machineErrorMsg.setRemark(param.getString("result"));

            if(param.get("errorType")!=null&&!"".equals(param.getString("errorType"))){
                machineErrorMsg.setErrorType(Integer.parseInt(param.getString("errorType")));
            }else {
                machineErrorMsg.setErrorType(0);
            }

            machineErrorMsg.setCreateUserId(user.getUserId());
            machineErrorMsg.setCreateUserName(user.getUserName());
            machineErrorMsg.setCreateTime(new Date());

            machineErrorMsgDao.insert(machineErrorMsg);

            return jsonObject;

        } catch (Exception e) {
            jsonObject.put(ER.RES, ER.ERR);
            jsonObject.put(ER.MSG, e.getMessage());
            log.error("",e);

        }

        return jsonObject;
    }
}
