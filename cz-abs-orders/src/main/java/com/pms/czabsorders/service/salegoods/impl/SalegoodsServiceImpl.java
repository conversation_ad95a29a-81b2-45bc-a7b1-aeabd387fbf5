package com.pms.czabsorders.service.salegoods.impl;

import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.salesgood.AddGoodsForRegistParam;
import com.pms.czabsorders.bean.salesgood.ReturnedGoodsParam;
import com.pms.czabsorders.service.salegoods.SalegoodsService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountCancel;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountCancelDao;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czaccount.service.alipay.AliPayService;
import com.pms.czaccount.service.wechat.WeChatPayService;
import com.pms.czmembership.service.memeber.MemberService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.OrderNumUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.ERROR_MSG;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.dao.BookingOrderDao;
import com.pms.pmsorder.dao.RegistDao;
import com.pms.pmswarehouse.bean.*;
import com.pms.pmswarehouse.bean.request.GoodsSaleRecordRequest;
import com.pms.pmswarehouse.bean.request.SelectGoodsStockCountParam;
import com.pms.pmswarehouse.bean.request.SelectGoodsStockParam;
import com.pms.pmswarehouse.bean.search.*;
import com.pms.pmswarehouse.dao.GoodsDumbDao;
import com.pms.pmswarehouse.dao.GoodsSaleRecordDao;
import com.pms.pmswarehouse.dao.GoodsShoppingOrderDao;
import com.pms.pmswarehouse.dao.GoodsStockDetailDao;
import com.pms.pmswarehouse.service.GoodsStockService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Primary
@Slf4j
public class SalegoodsServiceImpl extends BaseService implements SalegoodsService {




    @Autowired
    private GoodsDumbDao goodsDumbDao;

    @Autowired
    private GoodsStockDetailDao goodsStockDetailDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private GoodsStockService goodsStockService;


    @Autowired
    private GoodsSaleRecordDao goodsSaleRecordDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    AliPayService aliPayService;

    @Autowired
    private WeChatPayService weChatPayService;

    @Autowired
    private GoodsShoppingOrderDao goodsShoppingOrderDao;

    @Autowired
    private MemberService memberService;

    @Autowired
    private AccountCancelDao accountCancelDao;


    @Override
    public Page<GoodsDumb> findAllGoodsDumb(GoodsDumbSearch goodsDumbSearch) throws Exception {
        TbUserSession userSession = getTbUserSession(goodsDumbSearch);
        if (userSession == null) {
            throw new Exception(ERROR_MSG.INVALID_SESSION);
        }
        goodsDumbSearch.setHid(userSession.getHid());
        Page<GoodsDumb> goodsDumbs = goodsDumbDao.selectBySearch(goodsDumbSearch);
        //处理商超订单分页
        if(!CollectionUtils.isEmpty(goodsDumbs.getResult())){
            for (GoodsDumb goodsDumb : goodsDumbs.getResult()) {
                if(Objects.nonNull(goodsDumb.getAccountId())){
                    Account account = accountDao.selectById(goodsDumb.getAccountId());
                    //todo
                    goodsDumb.setRegistState(account.getRegistState());
                    goodsDumb.setRoomNum(account.getRoomNum());
                    goodsDumb.setRoomInfoId(account.getRoomInfoId());
                }
            }
        }
        return goodsDumbs;
    }


    public JSONObject pushData(JSONObject jsonData, TbUserSession user, String jsonStr) throws Exception {
        JSONObject jsonObject = JSONObject.fromObject(URLDecoder.decode(jsonData.getString(jsonStr), "utf-8"));
        jsonObject.put("hid", user.getHid());
        //验证是否分页
        Object pageObj = jsonObject.get("page");
        Object pageSizeObj = jsonObject.get("pageSize");
        if (pageObj != null && pageSizeObj != null) {
            int page = Integer.parseInt(pageObj.toString());
            int pageSize = Integer.parseInt(pageSizeObj.toString());
            jsonObject.put("page", (page - 1) * pageSize);
        }
        return jsonObject;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData addOrUpdateGoodsDumb(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        boolean onlinePay = false;
        String mainId = "";
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            //新增零售使用线上支付的业务
            onlinePay = param.containsKey("mainId") && !param.getString("mainId").equals("");
            if (onlinePay) {
                mainId = param.getString("mainId");
                //setIfAbsent  存有的话,会返回失败说明失败
                Boolean aBoolean = stringRedisTemplate.opsForValue().setIfAbsent(mainId, "", 3, TimeUnit.HOURS);
                if (Boolean.FALSE.equals(aBoolean)) {
                    responseData.setResult(ER.ERR);
                    responseData.setMsg(ER.EXECUTE_SUCCESS);
                    return responseData;
                }
            }
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setOperator(user.getUserName());

            if (param.get("goodsDumbData") == null) {
                throw new Exception("先付帐数据不能空");
            }
            JSONObject goodsDumbData = JSONObject.fromObject(URLDecoder.decode(param.getString("goodsDumbData"), "utf-8"));
            GoodsDumb goodsDumb = new GoodsDumb();
            Date date = new Date();
            goodsDumb.setUpdateTime(date);
            goodsDumb.setUpdateUserName(user.getUserName());
            goodsDumb.setUpdateUserId(user.getUserId());
            int updateValue = 0;

            if (goodsDumbData.get("cardInfoId") != null) {
                goodsDumb.setCardInfoId(goodsDumbData.getInt("cardInfoId"));
            }
            if (goodsDumbData.get("cardName") != null) {
                goodsDumb.setCardName(goodsDumbData.getString("cardName"));
            }
            if (goodsDumbData.get("companyId") != null) {
                goodsDumb.setCompanyId(goodsDumbData.getInt("companyId"));
            }
            if (goodsDumbData.get("compayName") != null) {
                goodsDumb.setCompayName(goodsDumbData.getString("compayName"));
            }
            if (goodsDumbData.get("roomRateCodeId") != null) {
                goodsDumb.setRoomRateCodeId(goodsDumbData.getInt("roomRateCodeId"));
            }
            if (goodsDumbData.get("roomRateCodeName") != null) {
                goodsDumb.setRoomRateCodeName(goodsDumbData.getString("roomRateCodeName"));
            }
            if (goodsDumbData.get("remark") != null) {
                goodsDumb.setRemark(goodsDumbData.getString("remark"));
            }
            if (goodsDumbData.get("saleId") != null) {
                goodsDumb.setSaleId(goodsDumbData.getInt("saleId"));
            }
            if (goodsDumbData.get("invoiceId") != null) {
                goodsDumb.setInvoiceId(goodsDumbData.getInt("invoiceId"));
            }
            if (goodsDumbData.get("accountType") != null) {
                goodsDumb.setAccountType(goodsDumbData.getInt("accountType"));
            }

            if (goodsDumbData.get("payType") == null || goodsDumbData.getString("payType").equals("")) {
                throw new Exception("支付方式不能空");
            }
            goodsDumb.setPayType(goodsDumbData.getString("payType"));
            if (goodsDumbData.get("sumPrice") == null) {
                throw new Exception("支付金额不能空");
            }
            goodsDumb.setSumPrice(goodsDumbData.getInt("sumPrice"));


            if (goodsDumbData.get("state") != null) {
                goodsDumb.setState(goodsDumbData.getInt("state"));
            }

            /**
             * id 为空说明是添加
             *    不为空说明是修改
             */
            if (goodsDumbData.get("goodsDumbId") == null) {
                goodsDumb.setHid(user.getHid());
                goodsDumb.setHotelGroupId(user.getHotelGroupId());
                goodsDumb.setCreateTime(date);
                goodsDumb.setCreateUserName(user.getUserName());
                goodsDumb.setCreateUserId(user.getUserId());
                goodsDumb.setState(1);
                goodsDumb.setBusinessDay(user.getBusinessDay());
                /**
                 * 冗余字段，营业日期 年
                 */
                goodsDumb.setDumbYear(user.getBusinessYear());

                /**
                 * 冗余字段，营业日子 年月
                 */
                goodsDumb.setDumbYearMonth(user.getBusinessMonth());

                /**
                 * 班次 id
                 */
                goodsDumb.setClassId(user.getClassId());
                updateValue = goodsDumbDao.insert(goodsDumb);
            } else {

                updateValue = goodsDumbDao.update(goodsDumb);
            }

            if (updateValue < 1) {
                throw new Exception("修改失败，请稍后重试");
            }
            /**
             * 添加哑房账记录后，获取goodsDumbId
             */
            Integer goodsDumbId = goodsDumb.getGoodsDumbId();
            oprecord.setDescription(user.getUserName() + ":" + goodsDumb.getGoodsDumbId() == null ? "添加先付账记录:" : "更新先付账记录:");
            oprecord.setHid(user.getHid());
            oprecord.setBusinessShiftName(user.getClassId().toString());
            oprecord.setBusinessShiftId(user.getClassId());
            oprecord.setBusinessDay(user.getBusinessDay());
            oprecords.add(oprecord);

            GoodsSaleRecordRequest goodsSaleRecordRequest = new GoodsSaleRecordRequest();
            goodsSaleRecordRequest.setSourceTypeId(1);
            goodsSaleRecordRequest.setRegistId(goodsDumbId);
            goodsSaleRecordRequest.setSourceTypeName("零售");
            goodsSaleRecordRequest.setRemark(goodsDumbData.getString("remark"));
            goodsSaleRecordRequest.setSessionToken(sessionToken);
            goodsSaleRecordRequest.setHotelIsCheckGoodsNum(goodsDumbData.getInt("hotelIsCheckGoodsNum"));
            if (goodsSaleRecordRequest.getHotelIsCheckGoodsNum() == 1) {
                goodsSaleRecordRequest.setGoodsStockId(goodsDumbData.getInt("goodsStockId"));
            }
            List<GoodsSaleRecordRequest.GoodsInfo> goodsInfoList = new ArrayList<>();
            goodsSaleRecordRequest.setGoodsInfoList(goodsInfoList);
            if (goodsDumbData.get("goodsStockDetailList") != null) {
                JSONArray goodsStockDetailList = goodsDumbData.getJSONArray("goodsStockDetailList");
                for (int i = 0; i < goodsStockDetailList.size(); i++) {
                    JSONObject goodsStockDetail = goodsStockDetailList.getJSONObject(i);
                    GoodsSaleRecordRequest.GoodsInfo goodsInfo = new GoodsSaleRecordRequest.GoodsInfo();
                    goodsInfo.setId(goodsStockDetail.getInt("goodsInfoId"));
                    goodsInfo.setNum(goodsStockDetail.getInt("amount"));
                    goodsInfo.setPrice(goodsStockDetail.getInt("price"));
                    goodsSaleRecordRequest.getGoodsInfoList().add(goodsInfo);
                }

                //调用商品销售的服务
                try {
                    goodsStockService.addGoodsSaleRecord(goodsSaleRecordRequest);
                } catch (Exception e) {
                    log.error("",e);
                    responseData.setMsg("业务处理异常");
                    responseData.setCode(-1);
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                }
                /**
                 * 添加账务支付信息
                 */
                if (goodsDumbData.get("accountList") != null) {
                    JSONArray accountList = goodsDumbData.getJSONArray("accountList");
                    Account accountBean = new Account();
                    for (int i = 0; i < accountList.size(); i++) {
                        JSONObject account = accountList.getJSONObject(i);
                        accountBean.setGoodDumbId(goodsDumbId);
                        accountBean.setPrice(account.getInt("price"));
                        accountBean.setPayType(1);
                        accountBean.setIsSale(0);
                        accountBean.setRegistPersonId(0);
                        accountBean.setPayClassName("客房");
                        accountBean.setPayClassId(10);
                        accountBean.setPayCodeId("3100");
                        accountBean.setPayCodeName("小商品");
                        accountBean.setRegistState(1);
                        accountBean.setAccountType(2);
                        if (account.get("goodId") == null) {
                            throw new Exception("商品编号不能空");
                        }
                        accountBean.setGoodId(account.getInt("goodId"));
                        if (account.get("goodName") == null || account.getString("goodName").equals("")) {
                            throw new Exception("商品名称不能空");
                        }
                        accountBean.setGoodName(account.getString("goodName"));
                        if (account.get("uintPrice") == null) {
                            throw new Exception("商品单价不能空");
                        }
                        accountBean.setUintPrice(account.getInt("uintPrice"));
                        if (account.get("saleNum") == null) {
                            throw new Exception("销售数量不能空");
                        }
                        accountBean.setSaleNum(account.getInt("saleNum"));
                        if (account.get("unit") != null && !account.getString("unit").equals("")) {
                            accountBean.setUnit(account.getString("unit"));
                        }
                        if (account.get("remark") != null && !account.getString("remark").equals("")) {
                            accountBean.setRemark(account.getString("remark"));
                        }
                        if (account.get("isCancel") != null) {
                            accountBean.setIsCancel(account.getInt("isCancel"));
                        }

                        if (account.get("accountId") == null) {
                            accountBean.setAccountId(HotelUtils.getHIDUUID32("A", user.getHid()));
                            accountBean.setIsCancel(0);
                            accountBean.setHid(user.getHid());
                            accountBean.setHotelGroupId(user.getHotelGroupId());
                            accountBean.setCreateTime(date);
                            accountBean.setCreateUserName(user.getUserName());
                            accountBean.setCreateUserId(user.getUserId());
                            accountBean.setClassId(user.getClassId());
                            accountBean.setBusinessDay(user.getBusinessDay());
                            accountBean.setAccountYear(user.getBusinessYear());
                            accountBean.setAccountYearMonth(user.getBusinessMonth());
                            updateValue = accountDao.saveAccount(accountBean);
                        } else {
                            accountBean.setUpdateUserName(user.getUserName());
                            accountBean.setUpdateTime(date);
                            accountBean.setUpdateUserId(user.getUserId());
                            updateValue = accountDao.editAccount(accountBean);
                        }
                        if (updateValue < 1) {
                            throw new Exception("添加账务消费信息失败");
                        }
                    }
                }


                if (goodsDumbData.get("payInfoList") != null) {
                    JSONArray payInfoList = goodsDumbData.getJSONArray("payInfoList");

                    Account payAccount = new Account();

                    for (int i = 0; i < payInfoList.size(); i++) {
                        JSONObject payInfo = payInfoList.getJSONObject(i);

                        payAccount.setGoodDumbId(goodsDumbId);
                        payAccount.setPrice(payInfo.getInt("price"));
                        payAccount.setPayType(2);
                        payAccount.setIsSale(0);
                        payAccount.setRegistState(1);

                        if (payInfo.get("payClassId") == null) {
                            throw new Exception("支付类别id不能空");
                        }
                        payAccount.setPayClassId(payInfo.getInt("payClassId"));
                        if (payInfo.get("payClassName") == null || payInfo.getString("payClassName").equals("")) {
                            throw new Exception("支付类别名称不能空");
                        }
                        payAccount.setPayClassName(payInfo.getString("payClassName"));
                        if (payInfo.get("payCodeId") == null) {
                            throw new Exception("支付小类id不能空 ");
                        }
                        payAccount.setPayCodeId(payInfo.getString("payCodeId"));
                        if (payInfo.get("payCodeName") == null || payInfo.getString("payCodeName").equals("")) {
                            throw new Exception("支付小类名称不能空 ");
                        }
                        payAccount.setPayCodeName(payInfo.getString("payCodeName"));

                        if (payInfo.get("thirdAccoutId") != null) {
                            payAccount.setThirdAccoutId(payInfo.getString("thirdAccoutId"));
                            payAccount.setThirdRefundState(0);
                            payAccount.setRefundPrice(0);
                        }
                        payAccount.setSettleAccountTime(date);

                        if (payInfo.get("accountId") == null) {
                            payAccount.setAccountId(HotelUtils.getHIDUUID32("A", user.getHid()));
                            payAccount.setHid(user.getHid());
                            payAccount.setHotelGroupId(user.getHotelGroupId());
                            payAccount.setCreateTime(date);
                            payAccount.setCreateUserName(user.getUserName());
                            payAccount.setCreateUserId(user.getUserId());
                            payAccount.setClassId(user.getClassId());
                            payAccount.setBusinessDay(user.getBusinessDay());
                            payAccount.setAccountYear(user.getBusinessYear());
                            payAccount.setAccountYearMonth(user.getBusinessMonth());
                            payAccount.setIsCancel(0);
                            accountDao.saveAccount(payAccount);

                        } else {
                            payAccount.setUpdateUserName(user.getUserName());
                            payAccount.setUpdateTime(date);
                            payAccount.setUpdateUserId(user.getUserId());
                            accountDao.editAccount(payAccount);
                        }

                        if (updateValue < 1) {
                            throw new Exception("添加账务支付信息失败");
                        }

                    }
                }
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setCode(-1);
            if (onlinePay) {
                stringRedisTemplate.delete(mainId);
            }
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return responseData;
    }

    @Override
    public ResponseData cancelGoodsDumb(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (param.get("goodsDumbData") == null) {
                throw new Exception("查询条件不能空");
            }
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);


            Integer businessDay = user.getBusinessDay();
            Integer classId = user.getClassId();

            JSONObject goodsDumbData = param.getJSONObject("goodsDumbData");
            Integer goodsDumbId = goodsDumbData.getInt("goodsDumbId");
            /**
             * 查询现付账表
             */
            GoodsDumb goodsDumb = goodsDumbDao.selectById(goodsDumbId);
            /**
             * 营业日期是否一致 ，不一致则不允许冲账
             */
            if (!goodsDumb.getBusinessDay().equals(businessDay)) {
                throw new Exception("营业日期或班次不一致，不允许冲账操作");
            }

            /**
             * 查询出库记录表
             */
            GoodsSaleRecordSearch goodsSaleRecordSearch = new GoodsSaleRecordSearch();
            goodsSaleRecordSearch.setRegistId(goodsDumbId);

            Page<GoodsSaleRecord> goodsSaleRecords = goodsSaleRecordDao.selectBySearch(goodsSaleRecordSearch);
//            GoodsStockDetailSearch goodsStockDetailSearch = new GoodsStockDetailSearch();
//            goodsStockDetailSearch.setGoodDumbId(goodsDumbId);
//            goodsStockDetailSearch.setState(1);
//
//
//            List<GoodsStockDetail> goodsStockDetails = goodsStockDetailDao.selectBySearch(goodsStockDetailSearch);

            List<Integer> ids = new ArrayList<>();
            for (int i = 0; i < goodsSaleRecords.size(); i++) {
                ids.add(goodsSaleRecords.get(i).getId());
            }
            GoodsSaleRecordRequest request = new GoodsSaleRecordRequest();
            request.setGoodsSaleRecordIdList(ids);
            request.setSessionToken(sessionToken);
            goodsStockService.addGoodsReturnRecord(request);

            /**
             * 查询账务表
             */
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setGoodDumbId(goodsDumbId);
            accountSearch.setHid(user.getHid());
            accountSearch.setIsCancel(0);
            accountSearch.setIsDump(0);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            GoodsShoppingOrder goodsShoppingOrder = null;

            GoodsShoppingOrderSearch goodsShoppingOrderSearch = new GoodsShoppingOrderSearch();
            goodsShoppingOrderSearch.setDumpId(goodsDumbId);
            Page<GoodsShoppingOrder> goodsShoppingOrders = goodsShoppingOrderDao.selectBySearch(goodsShoppingOrderSearch);
            if (goodsShoppingOrders != null && goodsShoppingOrders.size() > 0) {
                goodsShoppingOrder = goodsShoppingOrders.get(0);
            }

            boolean success = executeCancelGoodsDumb(goodsDumb, accounts, new ArrayList<>(), user, goodsShoppingOrder);
            if (!success) {
                throw new Exception("执行失败");
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setCode(-1);
        }
        return responseData;
    }


    @Transactional(rollbackFor = Exception.class)
    public boolean executeCancelGoodsDumb(GoodsDumb goodsDumb, List<Account> accounts, List<GoodsStockDetail> goodsStockDetails, TbUserSession user, GoodsShoppingOrder goodsShoppingOrder) {
        try {
            goodsDumb.setState(0);
            Integer update = goodsDumbDao.update(goodsDumb);
            if (update < 1) {
                throw new Exception("修改失败");
            }

            for (int i = 0; i < accounts.size(); i++) {
                Account account = accounts.get(i);
                account.setIsCancel(1);
                //需要退款
                if (account.getThirdAccoutId() != null && account.getThirdRefundState() == 0 && account.getRefundPrice() == 0) {
                    //插入一条退款记录
                    Account refundAccount = new Account();
                    BeanUtils.copyProperties(account, refundAccount);

                    String id = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
                    //修改
                    refundAccount.setAccountId(id);
                    refundAccount.setCreateUserName(user.getUserName());
                    refundAccount.setClassId(user.getClassId());
                    refundAccount.setPrice(0 - account.getPrice());
                    refundAccount.setUpdateTime(new Date());
                    refundAccount.setUpdateUserId(user.getUserId());
                    refundAccount.setUpdateCalssId(user.getClassId());
                    refundAccount.setUpdateUserName(user.getUserName());
                    refundAccount.setClassId(user.getClassId());
                    refundAccount.setBusinessDay(user.getBusinessDay());
                    refundAccount.setIsCancel(0);
                    refundAccount.setAccountYear(user.getBusinessYear());
                    refundAccount.setAccountYearMonth(user.getBusinessMonth());
                    refundAccount.setSettleAccountTime(new Date());
                    Map<String, Object> postData = new HashMap<>();
                    postData.put(ER.SESSION_TOKEN, user.getSessionId());
                    postData.put("mainId", account.getThirdAccoutId());
                    postData.put("refundMoney", account.getPrice());
                    Map<String, Object> resultMap = new HashMap<>();

                    //支付宝在线支付
                    if (account.getPayCodeId().equals("9300")) {
                        resultMap = aliPayService.alipayRefund(postData);
                        refundAccount.setPayClassId(11);
                        refundAccount.setPayClassName("支付宝");
                        refundAccount.setPayCodeId("9309");
                        refundAccount.setPayCodeName("支付宝退款");
                    }
                    //微信在线支付
                    else if (account.getPayCodeId().equals("9320")) {
                        resultMap = weChatPayService.wechatRefund(postData);
                        refundAccount.setPayClassId(12);
                        refundAccount.setPayClassName("微信支付");
                        refundAccount.setPayCodeId("9329");
                        refundAccount.setPayCodeName("微信扫码支付退款");
                    }
                    //微信在线支付
                    else if (account.getPayCodeId().equals("9340")) {
                        resultMap = weChatPayService.wechatMinigroRefund(postData);
                        refundAccount.setPayClassId(12);
                        refundAccount.setPayClassName("微信支付");
                        refundAccount.setPayCodeId("9341");
                        refundAccount.setPayCodeName("微信公众号支付退款");
                    }
                    //会员相关业务
                    else if (account.getPayCodeId().equals("9600") || account.getPayCodeId().equals("9620") || account.getPayCodeId().equals("9630")) {
                        JSONObject param = new JSONObject();
                        param.put("aid", account.getThirdAccoutId());
                        param.put("payCostId", account.getPayCodeId());
                        param.put("sessionToken", user.getSessionId());
                        ResponseData res = memberService.strikeMemberConsumption(param);
                        if (res.getCode() != 1) {
                            throw new Exception("会员冲账失败");
                        }
                        resultMap.put(ER.RES, ER.SUCC);
                    }
                    if (!resultMap.get(ER.RES).toString().equals(ER.SUCC)) {
                        throw new Exception(resultMap.get(ER.MSG).toString());
                    }
                    //退款成功修改帐务信息
                    account.setRefundPrice(account.getPrice());
                    account.setThirdRefundState(1);
                    refundAccount.setRefundPrice(account.getPrice());
                    refundAccount.setThirdRefundState(1);
                }

                /**
                 * 插入第三方 冲账记录表中
                 */
                AccountCancel accountCancel = new AccountCancel();
                accountCancel.setAccountId(account.getAccountId());
                accountCancel.setPrice(account.getPrice());
                accountCancel.setPayType(account.getPayType());
                accountCancel.setHid(account.getHid());
                accountCancel.setHotelGroupId(account.getHotelGroupId());
                accountCancel.setPayClassId(account.getPayClassId());
                accountCancel.setPayClassName(account.getPayClassName());
                accountCancel.setPayCodeId(Integer.parseInt(account.getPayCodeId()));
                accountCancel.setPayCodeName(account.getPayCodeName());
                accountCancel.setRoomInfoId(account.getRoomInfoId());
                accountCancel.setRoomNum(account.getRoomNum());
                accountCancel.setAccountCode(account.getAccountCode());
                accountCancel.setAccountCreateUserName(account.getCreateUserName());
                accountCancel.setIsSale(account.getIsSale());
                accountCancel.setBusinessDay(user.getBusinessDay());
                accountCancel.setClassId(user.getClassId());
                accountCancel.setCreateTime(new Date());
                accountCancel.setCreateUserId(user.getUserId());
                accountCancel.setBookingId(account.getBookingId());
                accountCancel.setRegistId(account.getRegistId());
                accountCancel.setTeamCodeId(account.getTeamCodeId());
                accountCancel.setCreateUserName(user.getUserName());
                accountCancel.setCancelType(2);
                Integer insert = accountCancelDao.insert(accountCancel);
                if (insert < 1) {
                    throw new Exception("冲账失败");
                }

                update = accountDao.editAccount(account);
                if (update < 1) {
                    throw new Exception("修改失败");
                }
            }

            for (int i = 0; i < goodsStockDetails.size(); i++) {
                goodsStockDetails.get(i).setState(0);
                update = goodsStockDetailDao.update(goodsStockDetails.get(i));
                if (update < 1) {
                    throw new Exception("修改失败");
                }
            }

            if (goodsShoppingOrder != null) {
                goodsShoppingOrder.setState(5);
                goodsShoppingOrderDao.update(goodsShoppingOrder);
            }

            return true;
        } catch (Exception e) {
            log.error("",e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return false;
    }

    @Override
    public ResponseData findGoodsDumbInfo(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (param.get("goodsDumbData") == null) {
                throw new Exception("查询条件不能空");
            }

            JSONObject goodsDumbData = param.getJSONObject("goodsDumbData");
            Integer goodsDumbId = goodsDumbData.getInt("goodsDumbId");
            GoodsSaleRecordSearch goodsSaleRecordSearch = new GoodsSaleRecordSearch();
            goodsSaleRecordSearch.setRegistId(goodsDumbId);
            goodsSaleRecordSearch.setHid(user.getHid());
            Page<GoodsSaleRecord> goodsSaleRecords = goodsSaleRecordDao.selectBySearch(goodsSaleRecordSearch);
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setGoodDumbId(goodsDumbId);
            accountSearch.setHid(user.getHid());
//            accountSearch.setIsCancel(0);
            accountSearch.setIsDump(0);
            ArrayList<Account> costs = new ArrayList<>();
            ArrayList<Account> cashs = new ArrayList<>();
            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            if (accounts != null && accounts.size() > 0) {
                for (int i = 0; i < accounts.size(); i++) {
                    Account account = accounts.get(i);
                    if (account.getPayType() == 1) {
                        costs.add(account);
                    } else if (account.getPayType() == 2) {
                        cashs.add(account);
                    }
                }
            }
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("costs", costs);
            resultMap.put("cashs", cashs);
            resultMap.put("goodsStockDetails", goodsSaleRecords);
            responseData.setData(resultMap);

        } catch (Exception e) {
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setCode(-1);
        }
        return responseData;
    }

    @Override
    @Transactional("transactionManager")
    public ResponseData registAddGoods(AddGoodsForRegistParam param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (param.getBookingOrderId() == null && param.getRegistId() == null) {
                throw new Exception("缺少必要参数");
            }

            if (param.getGoodInfoList() == null && param.getGoodInfoList().size() < 1) {
                throw new Exception("缺少必要参数");
            }
            final TbUserSession user = this.getTbUserSession(param.getSessionToken());

            Regist regist = null;
            BookingOrder bookingOrder = null;

            Date date = new Date();




            //日志记录
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            String accountId = HotelUtils.getHIDUUID32("A", user.getHid());
            Account account = new Account();
            account.setAccountId(accountId);
            account.setHid(user.getHid());
            account.setHotelGroupId(user.getHotelGroupId());
            account.setCreateUserId(user.getUserId());
            account.setCreateUserName(user.getUserName());
            account.setCreateTime(new Date());
            account.setIsCancel(0);
            account.setAccountYear(user.getBusinessYear());
            account.setAccountYearMonth(user.getBusinessMonth());
            account.setBusinessDay(user.getBusinessDay());
            account.setClassId(user.getClassId());
            account.setSettleAccountTime(new Date());
            account.setRegistPersonId(0);
            //消费-付款
            account.setPayType(1);
            account.setSaleNum(1);
            account.setUintPrice(param.getMoney());
            account.setPayClassId(10);
            account.setPayClassName("客房");
            account.setPayCodeId("3100");
            account.setPayCodeName("小商品");
            account.setAccountType(2);
            account.setRemark(param.getRemark());
            account.setReason("");
            account.setRefundPrice(0);
            account.setPrice(param.getMoney());
            if (param.getRegistId() != null) {
                account.setRegistId(param.getRegistId());
                account.setRegistPersonId(param.getRegistPersonId());
                account.setRegistPersonName(param.getRegistPersonName());
                account.setRoomNum(param.getRoomNum());
                account.setRoomInfoId(param.getRoomNumId());
                account.setRoomTypeId(param.getRoomTypeId());
                account.setRegistState(0);
            } else if (param.getBookingOrderId() != null) {
                account.setBookingId(param.getBookingOrderId());
                account.setRegistState(0);
                account.setRegistPersonId(param.getRegistPersonId());
                account.setRegistPersonName(param.getRegistPersonName());
            }
            Integer integer = accountDao.saveAccount(account);
            if (integer < 1) {
                throw new Exception("入账失败");
            }


            GoodsDumb goodsDumb = new GoodsDumb();
            goodsDumb.setUpdateTime(date);
            goodsDumb.setUpdateUserName(user.getUserName());
            goodsDumb.setUpdateUserId(user.getUserId());
            int updateValue = 0;


            //支付方式
            goodsDumb.setPayType(StringUtils.isNotBlank(param.getSelectedPayWay()) ? param.getSelectedPayWay() : "");
            //金额
            goodsDumb.setSumPrice(param.getMoney());
            goodsDumb.setState(1);


            /**
             * id 为空说明是添加
             *    不为空说明是修改
             */

            goodsDumb.setHid(user.getHid());
            goodsDumb.setHotelGroupId(user.getHotelGroupId());
            goodsDumb.setCreateTime(date);
            goodsDumb.setCreateUserName(user.getUserName());
            goodsDumb.setCreateUserId(user.getUserId());
            goodsDumb.setInvoiceId(0);
            goodsDumb.setState(1);
            goodsDumb.setAccountId(accountId);
            goodsDumb.setBusinessDay(user.getBusinessDay());
            /**
             * 冗余字段，营业日期 年
             */
            goodsDumb.setDumbYear(user.getBusinessYear());
            /**
             * 冗余字段，营业日子 年月
             */
            goodsDumb.setDumbYearMonth(user.getBusinessMonth());

            /**
             * 班次 id
             */
            goodsDumb.setClassId(user.getClassId());
            updateValue = goodsDumbDao.insert(goodsDumb);
            if (updateValue < 1) {
                throw new Exception("修改失败，请稍后重试");
            }


            //为了迎合零售列表的商品详情
            for (AddGoodsForRegistParam.GoodInfo goodInfo : param.getGoodInfoList()) {
                String saleAccountId = HotelUtils.getHIDUUID32("A", user.getHid());
                Account saleAccount = new Account();
                saleAccount.setAccountId(saleAccountId);
                saleAccount.setHid(user.getHid());
                saleAccount.setHotelGroupId(user.getHotelGroupId());
                saleAccount.setCreateUserId(user.getUserId());
                saleAccount.setCreateUserName(user.getUserName());
                saleAccount.setCreateTime(new Date());
                saleAccount.setIsCancel(0);
                saleAccount.setAccountYear(user.getBusinessYear());
                saleAccount.setAccountYearMonth(user.getBusinessMonth());
                saleAccount.setBusinessDay(user.getBusinessDay());
                saleAccount.setClassId(user.getClassId());
                saleAccount.setSettleAccountTime(new Date());
                saleAccount.setRegistPersonId(0);

                saleAccount.setGoodDumbId(goodsDumb.getGoodsDumbId());
                saleAccount.setGoodId(goodInfo.getGoodsInfoId());
                saleAccount.setGoodName(goodInfo.getGoodsInfoName());


                //消费-付款
                saleAccount.setPayType(1);
                saleAccount.setSaleNum(goodInfo.getAmount());
                //单价
                saleAccount.setUintPrice(goodInfo.getPrice());
                saleAccount.setPayClassId(10);
                saleAccount.setPayClassName("客房");
                saleAccount.setPayCodeId("3100");
                saleAccount.setPayCodeName("小商品");
                saleAccount.setAccountType(2);
                saleAccount.setRemark(goodInfo.getDirection());
                saleAccount.setReason("");
                saleAccount.setRefundPrice(0);
                //账单金额
                saleAccount.setPrice(goodInfo.getMoney());
                if (param.getRegistId() != null) {
                    //saleAccount.setRegistId(param.getRegistId());
                    saleAccount.setRegistPersonId(param.getRegistPersonId());
                    saleAccount.setRegistPersonName(param.getRegistPersonName());
                    saleAccount.setRoomNum(param.getRoomNum());
                    saleAccount.setRoomInfoId(param.getRoomNumId());
                    saleAccount.setRoomTypeId(param.getRoomTypeId());
                    saleAccount.setRegistState(0);
                } else if (param.getBookingOrderId() != null) {
                    saleAccount.setBookingId(param.getBookingOrderId());
                    saleAccount.setRegistState(0);
                    saleAccount.setRegistPersonId(param.getRegistPersonId());
                    saleAccount.setRegistPersonName(param.getRegistPersonName());
                }
                Integer saleInteger = accountDao.saveAccount(saleAccount);
                if (saleInteger < 1) {
                    throw new Exception("入账失败");
                }
                goodInfo.setAccountId(saleAccount.getAccountId());
            }



            List<AddGoodsForRegistParam.GoodInfo> goodInfoList = param.getGoodInfoList();

            GoodsSaleRecordRequest goodsSaleRecordRequest = new GoodsSaleRecordRequest();
            goodsSaleRecordRequest.setSessionToken(param.getSessionToken());
            goodsSaleRecordRequest.setRegistId(param.getRegistId());
            //帐务ID
            goodsSaleRecordRequest.setAccountId(account.getAccountId());
            goodsSaleRecordRequest.setRoomNum(param.getRoomNum());
            goodsSaleRecordRequest.setRoomNumId(param.getRoomNumId());
            goodsSaleRecordRequest.setRegistPersionId(param.getRegistPersonId());
            goodsSaleRecordRequest.setRegistPersionName(param.getRegistPersonName());
            goodsSaleRecordRequest.setSourceTypeId(2);
            goodsSaleRecordRequest.setSourceTypeName("房间销售小商品");
            goodsSaleRecordRequest.setHotelIsCheckGoodsNum(param.getHotelIsCheckGoodsNum());
            List<GoodsSaleRecordRequest.GoodsInfo> goodsInfos = new ArrayList<>();
            //没有库存的概念
            /*if (goodsSaleRecordRequest.getHotelIsCheckGoodsNum() == 1) {
                if(Objects.nonNull(param.getGoodsStockId())){
                    goodsSaleRecordRequest.setGoodsStockId(param.getGoodsStockId());
                }
            }*/
            goodsSaleRecordRequest.setGoodsInfoList(goodsInfos);
            for (int i = 0; i < goodInfoList.size(); i++) {
                GoodsSaleRecordRequest.GoodsInfo goodsInfo = new GoodsSaleRecordRequest.GoodsInfo();
                goodsInfo.setPrice(goodInfoList.get(i).getPrice());
                goodsInfo.setNum(goodInfoList.get(i).getAmount());
                goodsInfo.setId(goodInfoList.get(i).getGoodsInfoId());
                goodsInfo.setRemark(goodInfoList.get(i).getDirection());
                goodsInfo.setGoodsDumbId(goodsDumb.getGoodsDumbId());
                goodsSaleRecordRequest.getGoodsInfoList().add(goodsInfo);
            }

//            if (account.getRegistId() != null && account.getRegistId() > 0) {
//                regist = registDao.selectById(account.getRegistId());
//
//                regist.setSumSale(regist.getSumSale() + param.getMoney());
//                Integer update = registDao.update(regist);
//                if (update < 1) {
//                    throw new Exception("更新主单失败");
//                }
//            }
            //执行商品销售
            goodsStockService.addGoodsSaleRecord(goodsSaleRecordRequest);
        } catch (Exception e) {
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setCode(-1);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return responseData;
    }

    @Override
    @Transactional("transactionManager")
    public ResponseData returnedGoods(ReturnedGoodsParam param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            final TbUserSession user = this.getTbUserSession(param.getSessionToken());
            Regist regist = null;
            BookingOrder bookingOrder = null;
            String accountId = param.getAccountId();
            Account account = accountDao.selectById(accountId);
            account.setIsCancel(1);
            account.setRegistState(3);// 冲账
            Integer update = 0;
            update = accountDao.editAccount(account);
            if (update < 1) {
                throw new Exception("修改失败");
            }
            /**
             * 查询出库记录表
             */
            GoodsSaleRecordSearch goodsSaleRecordSearch = new GoodsSaleRecordSearch();
            //通过帐务ID 去判断
            goodsSaleRecordSearch.setAccountId(param.getAccountId());
            Page<GoodsSaleRecord> goodsSaleRecords = goodsSaleRecordDao.selectBySearch(goodsSaleRecordSearch);
            List<Integer> ids = new ArrayList<>();
            for (int i = 0; i < goodsSaleRecords.size(); i++) {
                ids.add(goodsSaleRecords.get(i).getId());
            }
            GoodsSaleRecordRequest request = new GoodsSaleRecordRequest();
            request.setGoodsSaleRecordIdList(ids);
            request.setSessionToken(param.getSessionToken());

//            if (account.getRegistId() != null && account.getRegistId() > 0) {
//                regist = registDao.selectById(account.getRegistId());
//                regist.setSumSale(regist.getSumSale() - account.getPrice());
//                update = registDao.update(regist);
//                if (update < 1) {
//                    throw new Exception("更新主单失败");
//                }
//            }

            /**
             * 插入第三方 冲账记录表中
             */
            AccountCancel accountCancel = new AccountCancel();
            accountCancel.setAccountId(account.getAccountId());
            accountCancel.setPrice(account.getPrice());
            accountCancel.setPayType(account.getPayType());
            accountCancel.setHid(account.getHid());
            accountCancel.setHotelGroupId(account.getHotelGroupId());
            accountCancel.setPayClassId(account.getPayClassId());
            accountCancel.setPayClassName(account.getPayClassName());
            accountCancel.setPayCodeId(Integer.parseInt(account.getPayCodeId()));
            accountCancel.setPayCodeName(account.getPayCodeName());
            accountCancel.setRoomInfoId(account.getRoomInfoId());
            accountCancel.setRoomNum(account.getRoomNum());
            accountCancel.setAccountCode(account.getAccountCode());
            accountCancel.setAccountCreateUserName(account.getCreateUserName());
            accountCancel.setIsSale(account.getIsSale());
            accountCancel.setBusinessDay(user.getBusinessDay());
            accountCancel.setClassId(user.getClassId());
            accountCancel.setCreateTime(new Date());
            accountCancel.setCreateUserId(user.getUserId());
            accountCancel.setBookingId(account.getBookingId());
            accountCancel.setRegistId(account.getRegistId());
            accountCancel.setTeamCodeId(account.getTeamCodeId());
            accountCancel.setCreateUserName(user.getUserName());
            accountCancel.setCancelType(2);
            Integer insert = accountCancelDao.insert(accountCancel);
            if (insert < 1){
                throw new Exception("退货失败");
            }
            goodsStockService.addGoodsReturnRecord(request);
        } catch (Exception e) {
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setCode(-1);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return responseData;
    }

    /**
     * 查询商品库存统计
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData selectGoodsStock(SelectGoodsStockParam param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            final TbUserSession user = this.getTbUserSession(param.getSessionToken());
            Integer hid = user.getHid();
            param.setHid(hid);
            Page<SelectGoodsStockData> page = goodsStockDetailDao.selectGoodsStock(param);
            SelectGoodsStockCountParam countParam = new SelectGoodsStockCountParam();
            BeanUtils.copyProperties(param, countParam);
            List<SelectGoodsStockData> totalList = goodsStockDetailDao.selectGoodsStockCount(countParam);
            //合计 库存数量：，销售数量：，入库数量：
            Integer totalStockNum = totalList.stream().collect(Collectors.summingInt(SelectGoodsStockData::getStockNum));
            Integer totalSaleNum = totalList.stream().collect(Collectors.summingInt(SelectGoodsStockData::getSaleNum));
            Integer totalInventoryQuantity = totalList.stream().collect(Collectors.summingInt(SelectGoodsStockData::getInventoryQuantity));
            JSONObject res = new JSONObject();
            res.put("goodsList", page.getResult());
            res.put("pageNum", page.getPageNum());
            res.put("pageSize", page.getPageSize());
            res.put("totalPage", page.getPages());
            res.put("totalStockNum", totalStockNum);
            res.put("totalSaleNum", totalSaleNum);
            res.put("totalInventoryQuantity", totalInventoryQuantity);
            res.put("totalSize", page.getTotal());
            responseData.setData(res);
        } catch (Exception e) {
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setCode(-1);
        }
        return responseData;
    }

    @Override
    public ResponseData orderRoomCharge(OrderRoomChargeDto orderRoomChargeDto) {
        /*ResponseData responseData = ResponseData.newSuccessData();
        try {
            final TbUserSession user = this.getTbUserSession(orderRoomChargeDto.getSessionToken());

            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setOperator(user.getUserName());

            GoodsDumb goodsDumb = new GoodsDumb();
            Date date = new Date();
            goodsDumb.setUpdateTime(date);
            goodsDumb.setUpdateUserName(user.getUserName());
            goodsDumb.setUpdateUserId(user.getUserId());
            int updateValue = 0;


            //支付方式
            goodsDumb.setPayType(orderRoomChargeDto.getSelectedPayWay());
            //金额
            goodsDumb.setSumPrice(orderRoomChargeDto.getTotalAmount());
            goodsDumb.setState(1);


            *//**
             * id 为空说明是添加
             *    不为空说明是修改
             *//*

            goodsDumb.setHid(user.getHid());
            goodsDumb.setHotelGroupId(user.getHotelGroupId());
            goodsDumb.setCreateTime(date);
            goodsDumb.setCreateUserName(user.getUserName());
            goodsDumb.setCreateUserId(user.getUserId());
            goodsDumb.setInvoiceId(0);
            goodsDumb.setState(1);
            goodsDumb.setBusinessDay(user.getBusinessDay());
                *//**
                 * 冗余字段，营业日期 年
                 *//*
             goodsDumb.setDumbYear(user.getBusinessYear());
                *//**
                 * 冗余字段，营业日子 年月
                 *//*
             goodsDumb.setDumbYearMonth(user.getBusinessMonth());

                *//**
                 * 班次 id
                 *//*
             goodsDumb.setClassId(user.getClassId());
             updateValue = goodsDumbDao.insert(goodsDumb);
            if (updateValue < 1) {
                throw new Exception("修改失败，请稍后重试");
            }


            *//**
             * 添加哑房账记录后，获取goodsDumbId
             *//*
            Integer goodsDumbId = goodsDumb.getGoodsDumbId();
            oprecord.setDescription(user.getUserName() + ":" + goodsDumb.getGoodsDumbId() == null ? "添加先付账记录:" : "更新先付账记录:");
            oprecord.setHid(user.getHid());
            oprecord.setBusinessShiftName(user.getClassId().toString());
            oprecord.setBusinessShiftId(user.getClassId());
            oprecord.setBusinessDay(user.getBusinessDay());
            oprecords.add(oprecord);

            GoodsSaleRecordRequest goodsSaleRecordRequest = new GoodsSaleRecordRequest();
            goodsSaleRecordRequest.setSourceTypeId(1);
            goodsSaleRecordRequest.setRegistId(goodsDumbId);
            goodsSaleRecordRequest.setSourceTypeName("零售");
            goodsSaleRecordRequest.setRoomNum(orderRoomChargeDto.getRoomNum());
            goodsSaleRecordRequest.setRoomNumId(orderRoomChargeDto.getRoomInfoId());
            //todo备注
            //goodsSaleRecordRequest.setRemark("");
            goodsSaleRecordRequest.setSessionToken(orderRoomChargeDto.getSessionToken());
            //todo 是否核算库存
            goodsSaleRecordRequest.setHotelIsCheckGoodsNum(1);
            *//*if (goodsSaleRecordRequest.getHotelIsCheckGoodsNum() == 1) {
                goodsSaleRecordRequest.setGoodsStockId(goodsDumbData.getInt("goodsStockId"));
            }*//*
            List<GoodsSaleRecordRequest.GoodsInfo> goodsInfoList = new ArrayList<>();



            for (GoodInfoDto goodInfoDto : orderRoomChargeDto.getList()) {
                GoodsSaleRecordRequest.GoodsInfo goodsInfo = new GoodsSaleRecordRequest.GoodsInfo();
                goodsInfo.setId(goodInfoDto.getGoodsInfoId());
                goodsInfo.setNum(goodInfoDto.getNum());
                goodsInfo.setPrice(Integer.parseInt(goodInfoDto.getPrice()));
                if(StringUtils.isNotBlank(goodInfoDto.getDesc())){
                    goodsInfo.setRemark(goodInfoDto.getDesc());
                }
                goodsInfoList.add(goodsInfo);
            }
            goodsSaleRecordRequest.setGoodsInfoList(goodsInfoList);
                //调用商品销售的服务

            goodsStockService.addGoodsSaleRecord(goodsSaleRecordRequest);

        }catch (Exception e){
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setCode(-1);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return responseData;*/

        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (orderRoomChargeDto.getBookingOrderId() == null && orderRoomChargeDto.getRegistId() == null) {
                throw new Exception("缺少必要参数");
            }

            if (orderRoomChargeDto.getGoodInfoList() == null && orderRoomChargeDto.getGoodInfoList().size() < 1) {
                throw new Exception("缺少必要参数");
            }
            final TbUserSession user = this.getTbUserSession(orderRoomChargeDto.getSessionToken());

            Regist regist = null;
            BookingOrder bookingOrder = null;

            Date date = new Date();


            //日志记录
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            String accountId = HotelUtils.getHIDUUID32("A", user.getHid());
            Account account = new Account();
            account.setAccountId(accountId);
            account.setHid(user.getHid());
            account.setHotelGroupId(user.getHotelGroupId());
            account.setCreateUserId(user.getUserId());
            account.setCreateUserName(user.getUserName());
            account.setCreateTime(new Date());
            account.setIsCancel(0);
            account.setAccountYear(user.getBusinessYear());
            account.setAccountYearMonth(user.getBusinessMonth());
            account.setBusinessDay(user.getBusinessDay());
            account.setClassId(user.getClassId());
            account.setSettleAccountTime(new Date());
            account.setRegistPersonId(0);
            //消费-付款
            account.setPayType(1);
            account.setSaleNum(1);
            account.setUintPrice(orderRoomChargeDto.getMoney());
            account.setPayClassId(10);
            account.setPayClassName("客房");
            account.setPayCodeId("3100");
            account.setPayCodeName("小商品");
            account.setAccountType(2);
            account.setRemark(StringUtils.isNotBlank(orderRoomChargeDto.getRemark()) ? orderRoomChargeDto.getRemark() : "");
            account.setReason("");
            account.setRefundPrice(0);
            account.setPrice(orderRoomChargeDto.getMoney());
            if (orderRoomChargeDto.getRegistId() != null) {
                account.setRegistId(orderRoomChargeDto.getRegistId());
                account.setRegistPersonId(orderRoomChargeDto.getRegistPersonId());
                account.setRegistPersonName(orderRoomChargeDto.getRegistPersonName());
                account.setRoomNum(orderRoomChargeDto.getRoomNum());
                account.setRoomInfoId(orderRoomChargeDto.getRoomNumId());
                account.setRoomTypeId(orderRoomChargeDto.getRoomTypeId());
                account.setRegistState(0);
            } else if (orderRoomChargeDto.getBookingOrderId() != null) {
                account.setBookingId(orderRoomChargeDto.getBookingOrderId());
                account.setRegistState(0);
                account.setRegistPersonId(orderRoomChargeDto.getRegistPersonId());
                account.setRegistPersonName(orderRoomChargeDto.getRegistPersonName());
            }
            Integer integer = accountDao.saveAccount(account);
            if (integer < 1) {
                throw new Exception("入账失败");
            }

            /**
             * 新加逻辑前台下单
             */


                GoodsDumb goodsDumb = new GoodsDumb();
                goodsDumb.setUpdateTime(date);
                goodsDumb.setUpdateUserName(user.getUserName());
                goodsDumb.setUpdateUserId(user.getUserId());
                int updateValue = 0;


                //支付方式
                goodsDumb.setPayType(orderRoomChargeDto.getSelectedPayWay());
                //金额
                goodsDumb.setSumPrice(orderRoomChargeDto.getMoney());
                goodsDumb.setState(1);


                /**
                 * id 为空说明是添加
                 *    不为空说明是修改
                 */

                goodsDumb.setHid(user.getHid());
                goodsDumb.setHotelGroupId(user.getHotelGroupId());
                goodsDumb.setCreateTime(date);
                goodsDumb.setCreateUserName(user.getUserName());
                goodsDumb.setCreateUserId(user.getUserId());
                goodsDumb.setInvoiceId(0);
                goodsDumb.setState(1);
                goodsDumb.setAccountId(accountId);
                goodsDumb.setBusinessDay(user.getBusinessDay());
                /**
                 * 冗余字段，营业日期 年
                 */
                goodsDumb.setDumbYear(user.getBusinessYear());
                /**
                 * 冗余字段，营业日子 年月
                 */
                goodsDumb.setDumbYearMonth(user.getBusinessMonth());

                /**
                 * 班次 id
                 */
                goodsDumb.setClassId(user.getClassId());
                updateValue = goodsDumbDao.insert(goodsDumb);
                if (updateValue < 1) {
                    throw new Exception("修改失败，请稍后重试");
                }

            //为了迎合零售列表的商品详情
            for (OrderRoomChargeDto.GoodInfo goodInfo : orderRoomChargeDto.getGoodInfoList()) {
                String saleAccountId = HotelUtils.getHIDUUID32("A", user.getHid());
                Account saleAccount = new Account();
                saleAccount.setAccountId(saleAccountId);
                saleAccount.setHid(user.getHid());
                saleAccount.setHotelGroupId(user.getHotelGroupId());
                saleAccount.setCreateUserId(user.getUserId());
                saleAccount.setCreateUserName(user.getUserName());
                saleAccount.setCreateTime(new Date());
                saleAccount.setIsCancel(0);
                saleAccount.setAccountYear(user.getBusinessYear());
                saleAccount.setAccountYearMonth(user.getBusinessMonth());
                saleAccount.setBusinessDay(user.getBusinessDay());
                saleAccount.setClassId(user.getClassId());
                saleAccount.setSettleAccountTime(new Date());
                saleAccount.setRegistPersonId(0);

                saleAccount.setGoodDumbId(goodsDumb.getGoodsDumbId());
                saleAccount.setGoodId(goodInfo.getGoodsInfoId());
                saleAccount.setGoodName(goodInfo.getGoodsInfoName());


                //消费-付款
                saleAccount.setPayType(1);
                saleAccount.setSaleNum(goodInfo.getAmount());
                //单价
                saleAccount.setUintPrice(goodInfo.getPrice());
                saleAccount.setPayClassId(10);
                saleAccount.setPayClassName("客房");
                saleAccount.setPayCodeId("3100");
                saleAccount.setPayCodeName("小商品");
                saleAccount.setAccountType(2);
                saleAccount.setRemark(goodInfo.getDirection());
                saleAccount.setReason("");
                saleAccount.setRefundPrice(0);
                //账单金额
                saleAccount.setPrice(goodInfo.getMoney());
                if (orderRoomChargeDto.getRegistId() != null) {
                    //saleAccount.setRegistId(orderRoomChargeDto.getRegistId());
                    saleAccount.setRegistPersonId(orderRoomChargeDto.getRegistPersonId());
                    saleAccount.setRegistPersonName(orderRoomChargeDto.getRegistPersonName());
                    saleAccount.setRoomNum(orderRoomChargeDto.getRoomNum());
                    saleAccount.setRoomInfoId(orderRoomChargeDto.getRoomNumId());
                    saleAccount.setRoomTypeId(orderRoomChargeDto.getRoomTypeId());
                    saleAccount.setRegistState(0);
                } else if (orderRoomChargeDto.getBookingOrderId() != null) {
                    //saleAccount.setBookingId(orderRoomChargeDto.getBookingOrderId());
                    saleAccount.setRegistState(0);
                    saleAccount.setRegistPersonId(orderRoomChargeDto.getRegistPersonId());
                    saleAccount.setRegistPersonName(orderRoomChargeDto.getRegistPersonName());
                }
                Integer saleInteger = accountDao.saveAccount(saleAccount);
                if (saleInteger < 1) {
                    throw new Exception("入账失败");
                }
                goodInfo.setAccountId(saleAccount.getAccountId());
            }






            List<OrderRoomChargeDto.GoodInfo> goodInfoList = orderRoomChargeDto.getGoodInfoList();

            GoodsSaleRecordRequest goodsSaleRecordRequest = new GoodsSaleRecordRequest();
            goodsSaleRecordRequest.setSessionToken(orderRoomChargeDto.getSessionToken());
            goodsSaleRecordRequest.setRegistId(orderRoomChargeDto.getRegistId());
            //帐务ID
            //goodsSaleRecordRequest.setAccountId(account.getAccountId());
            goodsSaleRecordRequest.setRoomNum(orderRoomChargeDto.getRoomNum());
            goodsSaleRecordRequest.setRoomNumId(orderRoomChargeDto.getRoomNumId());
            goodsSaleRecordRequest.setRegistPersionId(orderRoomChargeDto.getRegistPersonId());
            goodsSaleRecordRequest.setRegistPersionName(orderRoomChargeDto.getRegistPersonName());
            goodsSaleRecordRequest.setSourceTypeId(2);
            goodsSaleRecordRequest.setSourceTypeName("房间销售小商品");
            goodsSaleRecordRequest.setHotelIsCheckGoodsNum(1);
            List<GoodsSaleRecordRequest.GoodsInfo> goodsInfos = new ArrayList<>();
            //没有仓库的概念
            /*if (goodsSaleRecordRequest.getHotelIsCheckGoodsNum() == 1) {
                if(Objects.nonNull(orderRoomChargeDto.getGoodsStockId())){
                    goodsSaleRecordRequest.setGoodsStockId(orderRoomChargeDto.getGoodsStockId());
                }
            }*/
            goodsSaleRecordRequest.setGoodsInfoList(goodsInfos);
            for (int i = 0; i < goodInfoList.size(); i++) {
                GoodsSaleRecordRequest.GoodsInfo goodsInfo = new GoodsSaleRecordRequest.GoodsInfo();
                goodsInfo.setPrice(goodInfoList.get(i).getPrice());
                goodsInfo.setNum(goodInfoList.get(i).getAmount());
                goodsInfo.setId(goodInfoList.get(i).getGoodsInfoId());
                goodsInfo.setRemark(goodInfoList.get(i).getDirection());
                goodsInfo.setAccountId(goodInfoList.get(i).getAccountId());
                goodsInfo.setGoodsDumbId(goodsDumb.getGoodsDumbId());
                goodsSaleRecordRequest.getGoodsInfoList().add(goodsInfo);
            }

//            if (account.getRegistId() != null && account.getRegistId() > 0) {
//                regist = registDao.selectById(account.getRegistId());
//
//                regist.setSumSale(regist.getSumSale() + param.getMoney());
//                Integer update = registDao.update(regist);
//                if (update < 1) {
//                    throw new Exception("更新主单失败");
//                }
//            }
            //执行商品销售
            goodsStockService.addGoodsSaleRecord(goodsSaleRecordRequest);
        } catch (Exception e) {
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setCode(-1);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return responseData;



    }

    @Override
    public ResponseData hotSellGoods(HotelSellGoodsDto hotelSellGoodsDto) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            final TbUserSession user = this.getTbUserSession(hotelSellGoodsDto.getSessionToken());
            List<GoodsSaleRecordDto> goodsSaleRecordDtos = goodsSaleRecordDao.selectByCondition(hotelSellGoodsDto.getStartTime(), hotelSellGoodsDto.getEndTime(), user.getHid());
            //总金额
            Integer allSellAmount = goodsSaleRecordDao.selectAllSellAmount(hotelSellGoodsDto.getStartTime(), hotelSellGoodsDto.getEndTime(), user.getHid());
            if(!CollectionUtils.isEmpty(goodsSaleRecordDtos)){
                for (GoodsSaleRecordDto goodsSaleRecordDto : goodsSaleRecordDtos) {
                    goodsSaleRecordDto.setRatio(calculateSalesPercentage(goodsSaleRecordDto.getTotalAmount(),allSellAmount));
                }
            }
            responseData.setData(goodsSaleRecordDtos);
        }catch (Exception e) {
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setCode(-1);
        }
        return responseData;
    }


    /**
     * 计算销售额占比（百分比形式）
     * @param part 部分销售额（整数）
     * @param total 总销售额（整数）
     * @return 保留两位小数的百分比字符串
     */
    public static String calculateSalesPercentage(Integer part, Integer total) {
        // 1. 处理边界情况
        if (part == null || total == null) {
            throw new IllegalArgumentException("销售额不能为null");
        }

        if (total == 0) {
            return "0.00%"; // 避免除零错误
        }

        if (part < 0 || total < 0) {
            throw new IllegalArgumentException("销售额不能为负数");
        }

        // 2. 使用BigDecimal进行精确计算
        BigDecimal partBD = new BigDecimal(part);
        BigDecimal totalBD = new BigDecimal(total);

        // 3. 计算百分比 (part/total * 100)
        BigDecimal percentage = partBD
                .divide(totalBD, 4, RoundingMode.HALF_UP) // 保留4位小数中间结果
                .multiply(BigDecimal.valueOf(100))
                .setScale(2, RoundingMode.HALF_UP); // 最终保留2位小数

        // 4. 格式化输出
        return String.format("%.2f%%", percentage);
    }

//    /**
//     * 导出商品库存统计
//     *
//     * @param param
//     * @param response
//     * @return
//     */
//    @Override
//    public void exportGoodsStock(SelectGoodsStockParam param, HttpServletResponse response) {
//        try {
//            final TbUserSession user = this.getTbUserSession(param.getSessionToken());
//            Integer hid = user.getHid();
//            param.setHid(hid);
//            SelectGoodsStockCountParam countParam = new SelectGoodsStockCountParam();
//            BeanUtils.copyProperties(param, countParam);
//            List<SelectGoodsStockData> totalList = goodsStockDetailDao.selectGoodsStockCount(countParam);
//            //合计 库存数量：，销售数量：，入库数量：
//            Integer totalStockNum = totalList.stream().collect(Collectors.summingInt(SelectGoodsStockData::getStockNum));
//            Integer totalSaleNum = totalList.stream().collect(Collectors.summingInt(SelectGoodsStockData::getSaleNum));
//            Integer totalInventoryQuantity = totalList.stream().collect(Collectors.summingInt(SelectGoodsStockData::getInventoryQuantity));
//            ExcelWriter writer = new ExcelWriter(false);
//            writer.addHeaderAlias("goodsClassName", "商品分类");
//            writer.addHeaderAlias("goodsName", "商品名称");
//            writer.addHeaderAlias("stockNum", "库存数量");
//            writer.addHeaderAlias("saleNum", "销售数量");
//            writer.addHeaderAlias("inventoryQuantity", "入库数量");
//
//            writer.setOnlyAlias(true);
//
//            // 设置列宽（Colum）
//            for (int i = 0; i < 9; i++) {
//                writer.setColumnWidth(i, 20);
//            }
//            writer.write(totalList, true);
//            writer.passRows(1);
//            writer.clearHeaderAlias();
//            Map<String, Integer> row = new HashMap<>();
//            row.put("合计库存数量", totalStockNum);
//            row.put("合计销售数量", totalSaleNum);
//            row.put("合计入库数量", totalInventoryQuantity);
//            writer.writeRow(row, true);
//            String fileName = URLEncoder.encode("商品库存统计", "utf-8");
//            response.setContentType("application/vnd.ms-excel;charset=utf-8");
//            response.setHeader("Access-Control-Expose-Headers", "fileName");
//            response.setHeader("fileName", fileName + ".xls");
//            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xls");
//            ServletOutputStream out = null;
//            out = response.getOutputStream();
//            writer.flush(out, true);
//            writer.close();
//            IoUtil.close(out);
//        } catch (Exception e) {
//            log.error("",e);
//        }
//    }
}
