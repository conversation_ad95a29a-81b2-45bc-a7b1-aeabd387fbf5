package com.pms.czabsorders.service.mini.transaction.impl;

import com.pms.czabsorders.bean.mini.WxShoppingCardRequest;
import com.pms.czabsorders.service.mini.transaction.WxServiceTransaction;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.dao.HourRoomDayUseDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czmembership.bean.member.CardGroupInfo;
import com.pms.czmembership.bean.member.CardInfo;
import com.pms.czmembership.bean.member.CardOperationRecord;
import com.pms.czmembership.dao.member.*;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.dao.*;
import com.pms.pmswarehouse.bean.GoodsShoppingcar;
import com.pms.pmswarehouse.bean.GoodsShoppingcarDetails;
import com.pms.pmswarehouse.dao.GoodsShoppingcarDao;
import com.pms.pmswarehouse.dao.GoodsShoppingcarDetailsDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
public class WxServiceTransactionImpl extends BaseService implements WxServiceTransaction {
    @Autowired
    private CardGroupInfoDao cardGroupInfoDao;

    @Autowired
    private CardInfoDao cardInfoDao;

    @Autowired
    private CardGroupLevelDao cardGroupLevelDao;

    @Autowired
    private CardTypeDao cardTypeDao;

    @Autowired
    private CardRechargeDao cardRechargeDao;

    @Autowired
    private CardLevelDao cardLevelDao;

    @Autowired
    private CardOperationRecordDao cardOperationRecordDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private RegistGroupDao registGroupDao;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private GoodsShoppingcarDao goodsShoppingcarDao;

    @Autowired
    private GoodsShoppingcarDetailsDao goodsShoppingcarDetailsDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private HourRoomDayUseDao hourRoomDayUseDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void registVxVip(CardGroupInfo cardGroupInfo, List<CardInfo> cardInfoList, ArrayList<CardOperationRecord> cardOperationRecords, TbUserSession session) throws Exception {

        // 添加授权
        session.setUserId(session.getSessionId());
        this.addTbUserSession(session);

        Integer insert = cardGroupInfoDao.insert(cardGroupInfo);

        if(insert<1){
            throw new Exception("添加会员信息失败-G");
        }

        HashMap<Integer, Integer> cardIdMap = new HashMap<>();

        // 批量添加酒店会员
        for(CardInfo cardInfo:cardInfoList){

            cardInfo.setCardGroupId(cardGroupInfo.getId());

            Integer insert1 = cardInfoDao.insert(cardInfo);

            cardIdMap.put(cardInfo.getHid(),cardInfo.getId());

        }

        // 添加会员日志
        for (CardOperationRecord cardOperationRecord:cardOperationRecords){
            cardOperationRecord.setCardId(cardIdMap.get(cardOperationRecord.getHid()));
            cardOperationRecordDao.saveCardOperationRecord(cardOperationRecord);
        }

    }

    /**
     * 添加预订单
     * @param bookingOrder
     * @param registGroup
     * @param bookingOrderRoomTypes
     * @param roomAuxiliaryRelations
     * @param oprecords
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public void addBookService(BookingOrder bookingOrder, RegistGroup registGroup, List<BookingOrderRoomType> bookingOrderRoomTypes, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, ArrayList<Oprecord> oprecords, BookingOrderConfig bookingOrderConfig, Account account) throws Exception{

        // 1.添加预订单
        bookingOrder.setBookingNamePyjp(HotelUtils.hypyToFirstChar(bookingOrder.getBookingName()));
        Integer integer = bookingOrderDao.saveBookingOrder(bookingOrder);

        if(integer<1){
            throw new Exception("添加预订单失败");
        }

        // 添加团队
        if(registGroup!=null){
            registGroup.setBookingOrderId(bookingOrder.getBookingOrderId());
            Integer insert = registGroupDao.insert(registGroup);
            if(insert<1){
                throw new Exception("添加团队信息失败");
            }
        }

        // 添加预订房型
        for(BookingOrderRoomType bort:bookingOrderRoomTypes){

            bort.setBookingOrderId(bookingOrder.getBookingOrderId());
            Integer integer1 = bookingOrderRoomTypeDao.saveBookingOrderRoomType(bort);

            if(integer1<1){
                throw new Exception("添加预订房型："+bort.getRoomTypeId()+"失败");
            }

            // 添加预订房型价格
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bort.getBookingOrderDailyPrices();

            for(BookingOrderDailyPrice bookingOrderDailyPrice:bookingOrderDailyPrices){

                bookingOrderDailyPrice.setBookingOrderId(bookingOrder.getBookingOrderId());

                Integer integer2 = bookingOrderDailyPriceDao.saveBookingOrderDailyPrice(bookingOrderDailyPrice);

                if(integer2<1){
                    throw new Exception("添加预订房型价格："+bort.getRoomTypeId()+"---"+bookingOrderDailyPrice.getDailyTime()+"失败");
                }

            }

            // 添加预订房间
            List<BookingOrderRoomNum> bookingOrderRoomNums = bort.getBookingOrderRoomNums();

            for(BookingOrderRoomNum born:bookingOrderRoomNums){

                born.setBookingOrderId(bookingOrder.getBookingOrderId());
                born.setBookingOrderRoomTypeId(bort.getId());

                Integer integer2 = bookingOrderRoomNumDao.saveBookingOrderRoomNum(born);

                if(integer2<1){
                    throw new Exception("添加预订房房间："+born.getRoomNum()+"失败");
                }

                ArrayList<BookingOrderDailyPrice> bodpRoom = born.getBookingOrderDailyPrices();

                for(BookingOrderDailyPrice bookingOrderDailyPrice:bodpRoom){

                    bookingOrderDailyPrice.setBookingOrderId(bookingOrder.getBookingOrderId());
                    bookingOrderDailyPrice.setBookingOrderRoomNumId(born.getId());
                    bookingOrderDailyPrice.setRoomNumId(born.getRoomNumId());
                    Integer integer3 = bookingOrderDailyPriceDao.saveBookingOrderDailyPrice(bookingOrderDailyPrice);

                    if(integer3<1){
                        throw new Exception("添加预订房间价格："+born.getRoomNum()+"---"+bookingOrderDailyPrice.getDailyTime()+"失败");
                    }

                }

            }



        }

        // 添加辅助房态
        for (RoomAuxiliaryRelation roomAuxiliary:roomAuxiliaryRelations){
            roomAuxiliary.setBookingOrderId(bookingOrder.getBookingOrderId());
            roomAuxiliaryRelationDao.saveRoomAuxiliaryRelation(roomAuxiliary);
        }

        bookingOrderConfig.setBookingOrderId(bookingOrder.getBookingOrderId());
        Integer saveBookingOrderConfig = bookingOrderConfigDao.saveBookingOrderConfig(bookingOrderConfig);
        if(saveBookingOrderConfig<1){
            throw new Exception("添加预订设置失败");
        }

        if(account!=null){

            account.setBookingId(bookingOrder.getBookingOrderId());

            accountDao.saveAccount(account);

        }
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public void upaWxMemberMsg(CardGroupInfo cardGroupInfo, List<CardInfo> cardInfos) throws Exception {

        Integer update = cardGroupInfoDao.update(cardGroupInfo);

        if(update<1){
            throw new Exception("修改会员信息失败");
        }

        cardInfoDao.updateList(cardInfos);

    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public void addWxShoppingCar(GoodsShoppingcar delGoodshoppingcar, List<GoodsShoppingcarDetails> delDetails,
                                 GoodsShoppingcar goodsShoppingcar, WxShoppingCardRequest wxShoppingCardRequest) {

        if(delGoodshoppingcar!=null){
            goodsShoppingcarDao.delete(delGoodshoppingcar.getId());
        }

        if(delDetails.size()>0){
            goodsShoppingcarDetailsDao.deleteList(delDetails);
        }

        Integer insert = goodsShoppingcarDao.insert(goodsShoppingcar);


        List<WxShoppingCardRequest.GoodInfo> goodList = wxShoppingCardRequest.getGoodList();

        ArrayList<GoodsShoppingcarDetails> goodsShoppingcarDetails = new ArrayList<>();

        for(WxShoppingCardRequest.GoodInfo gis:goodList){

            GoodsShoppingcarDetails gsd = new GoodsShoppingcarDetails();

            gsd.setHid(goodsShoppingcar.getHid());
            gsd.setHotelGroupId(goodsShoppingcar.getHotelGroupId());
            gsd.setGoodsClassId(gis.getGoodsClassId());
            gsd.setGoodsClassName(gis.getGoodsClassName());
            gsd.setGoodsInfoId(gis.getGoodsInfoId());
            gsd.setGoodsInfoName(gis.getGoodsInfoName());
            gsd.setGoodsShoppingId(goodsShoppingcar.getId());

            gsd.setGoodsNum(gis.getGoodsNum());
            gsd.setPrice(gis.getPrice());
            gsd.setSumPrice(gis.getGoodsNum()*gis.getPrice());

            goodsShoppingcarDetails.add(gsd);
        }

        goodsShoppingcarDetailsDao.insertList(goodsShoppingcarDetails);


    }

    /**
     * 取消订单
     *
     * @param bookingOrders
     * @param bookingOrderRoomTypes
     * @param bookingOrderRoomNums
     * @param user
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public void cancelOrderService(List<BookingOrder> bookingOrders, List<BookingOrderRoomType> bookingOrderRoomTypes, List<BookingOrderRoomNum> bookingOrderRoomNums, TbUserSession user, List<RoomAuxiliaryRelation> roomAuxiliaryRelations) throws Exception {

        ArrayList<Oprecord> oprecords = new ArrayList<>();

        for (BookingOrder bookingOrder : bookingOrders) {
            Oprecord oprecord = new Oprecord(user);
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setSourceValue(bookingOrder.getOrderStatus() + "");
            oprecord.setChangedValue(BOOK.STA_YQX + "");
            oprecord.setMainId(bookingOrder.getSn());
            bookingOrder.setOrderStatus(BOOK.STA_YQX);

            //1.取消预订主单
            Integer boState = bookingOrderDao.editBookingOrder(bookingOrder);
            if (boState < 1) {
                throw new Exception("取消预订失败。bid:" + bookingOrder.getBookingOrderId());
            }

            oprecord.setDescription("将订单:" + bookingOrder.getSn() + " 改为取消状态");
            oprecords.add(oprecord);
        }

        //2.取消预订房型
        for (BookingOrderRoomType bookingOrderRoomType : bookingOrderRoomTypes) {
            bookingOrderRoomType.setOrderState(BOOK.STA_YQX);
            Integer brtState = bookingOrderRoomTypeDao.editBookingOrderRoomType(bookingOrderRoomType);
            if (brtState < 1) {
                throw new Exception("取消预定房型失败。bid:" + bookingOrderRoomType.getBookingOrderId() + ",brid:" + bookingOrderRoomType.getId());
            }
        }

        //3.取消预订房间
        for (BookingOrderRoomNum bookingOrderRoomNum : bookingOrderRoomNums) {
            bookingOrderRoomNum.setOrderState(BOOK.STA_YQX);
            Integer brnState = bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNum);
            if (brnState < 1) {
                throw new Exception("取消预定房间失败。bid:" + bookingOrderRoomNum.getBookingOrderId() + ",brid:" + bookingOrderRoomNum.getId() + ",brnid:" + bookingOrderRoomNum.getId());
            }
        }

        //4.取消辅助房态
        for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {
            Integer integer = roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());
            if (integer < 1) {
                throw new Exception("取消辅助房态失败:" + roomAuxiliaryRelation.getRelationId());
            }
        }


        this.addOprecords(oprecords);

    }

    /**
     * 钟点房续住
     * @param regist
     * @param account
     * @param addHourUse
     * @param upaHourUse
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void stayHoursTranSaction(Regist regist, Account account, List<HourRoomDayUse> addHourUse, List<HourRoomDayUse> upaHourUse,BookingOrderRoomNum upaBookRoom,BookingOrder upaBookOrder) {

        registDao.update(regist);

        if(account!=null){
            accountDao.saveAccount(account);

            // 添加对应的钟点房费
            account.setAccountId(account.getAccountId()+"fs");
            account.setPayType(1);
            account.setPayClassId(1);
            account.setPayClassName("客房");
            account.setPayCodeId("0007");
            account.setPayCodeName("钟点房费");

            accountDao.saveAccount(account);
        }

        if(addHourUse.size()>0){
            for(HourRoomDayUse hourRoomDayUse:addHourUse){
                hourRoomDayUseDao.insert(hourRoomDayUse);
            }
        }
        if(addHourUse.size()>0){
            for(HourRoomDayUse hourRoomDayUse:upaHourUse){
                hourRoomDayUseDao.update(hourRoomDayUse);
            }
        }
        if(null!=upaBookRoom){
            bookingOrderRoomNumDao.editBookingOrderRoomNum(upaBookRoom);
        }

        if(null!=upaBookOrder){
            bookingOrderDao.editBookingOrder(upaBookOrder);
        }
    }
}
