package com.pms.czabsorders.service.mini.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.mini.FindHotelMsg;
import com.pms.czabsorders.bean.mini.HotelView;
import com.pms.czabsorders.bean.mini.ShoppingOrderDelivery;
import com.pms.czabsorders.service.mini.WxHotelService;
import com.pms.czaccount.bean.pay.WechatMiniprograms;
import com.pms.czaccount.bean.pay.WechatSubscribeMsg;
import com.pms.czaccount.bean.pay.search.WechatMiniprogramsSearch;
import com.pms.czaccount.bean.pay.search.WechatSubscribeMsgSearch;
import com.pms.czaccount.dao.pay.WechatMiniprogramsDao;
import com.pms.czaccount.dao.pay.WechatSubscribeMsgDao;
import com.pms.czhotelfoundation.bean.hotel.HotelBaseInfo;
import com.pms.czhotelfoundation.bean.hotel.HotelMiniproSetting;
import com.pms.czhotelfoundation.bean.hotel.search.HotelBaseInfoSearch;
import com.pms.czhotelfoundation.bean.hotel.search.HotelMiniproSettingSearch;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.dao.hotel.HotelBaseInfoDao;
import com.pms.czhotelfoundation.dao.hotel.HotelMiniproSettingDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czmembership.bean.member.CardGroupInfo;
import com.pms.czmembership.bean.member.CardGroupUrl;
import com.pms.czmembership.bean.member.search.CardGroupUrlSearch;
import com.pms.czmembership.dao.member.CardGroupInfoDao;
import com.pms.czmembership.dao.member.CardGroupUrlDao;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.conf.CustomQrCodeUtil;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.hotelsetting.HOTEL_SETTING;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.HotelSettingByParamId;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.dao.RegistDao;
import com.pms.pmswarehouse.bean.GoodsShoppingOrder;
import com.pms.pmswarehouse.dao.GoodsShoppingOrderDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Ellipse2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Primary
@Slf4j
public class WxHotelServiceImpl extends BaseService implements WxHotelService {


    @Autowired
    private WechatMiniprogramsDao miniprogramsDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private HotelBaseInfoDao hotelBaseInfoDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private GoodsShoppingOrderDao goodsShoppingOrderDao;

    @Autowired
    private WechatSubscribeMsgDao wechatSubscribeMsgDao;

    String subscribeSend = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=";

    @Autowired
    private HotelMiniproSettingDao hotelMiniproSettingDao;

    //  @Autowired
    // private WechatSubscribeMsgDao wechatSubscribeMsgDao;

    @Autowired
    private WechatMiniprogramsDao wechatMiniprogramsDao;

    @Autowired
    private CardGroupUrlDao cardGroupUrlDao;

    @Autowired
    private CardGroupInfoDao cardGroupInfoDao;


    /**
     * 获取微信二维码
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData createHotelWxQrCode(WechatMiniprogramsSearch param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getSessionToken();
            TbUserSession user = this.getTbUserSession(sessionToken);

            WechatMiniprograms wechatMiniprograms = this.getAccessToken(user.getHotelGroupId());

            if (param.getCardLevelId() == null) {
                param.setCardLevelId(wechatMiniprograms.getCardLevelId());
                param.setCardTypeId(wechatMiniprograms.getCardTypeId());
            }

            if (param.getCardLevelId() == null) {
                throw new Exception("请设置正确的关注会员");
            }


            String url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + wechatMiniprograms.getAccessToken();

            JSONObject qrCodeParam = new JSONObject();
            qrCodeParam.put("scene", "scene=" + param.getScene());
            qrCodeParam.put("page", param.getPage());
            qrCodeParam.put("width", param.getWidth());
            qrCodeParam.put("auto_color", param.getAutoColor());
            qrCodeParam.put("line_color", param.getLineColor());
            qrCodeParam.put("is_hyaline", param.getHyaline());
            byte[] post = HttpRequest.post(url, qrCodeParam.toString());
            String res = Base64.getEncoder().encodeToString(post);
            InputStream input = new ByteArrayInputStream(post);
            wechatMiniprograms.setQrcode(res);
            wechatMiniprograms.setCardLevelId(param.getCardLevelId());
            wechatMiniprograms.setCardTypeId(param.getCardTypeId());

            BufferedImage bi = ImageIO.read(input);
            if(param.getNotifyUrl()!=null&&param.getNotifyUrl().length()>0){
                BufferedImage bufferedImage = headYuan(param.getNotifyUrl());

                byte[] bytes1 = CustomQrCodeUtil.changLogo(post, bufferedImage);

                InputStream input1 = new ByteArrayInputStream(bytes1);
                bi = ImageIO.read(input1);

                BufferedImage newPic = this.createNewPic(user.getHotelName(), bi, param);
                ByteArrayOutputStream out = new ByteArrayOutputStream();
                ImageIO.write(newPic, "jpeg", out);
                byte[] bytes = out.toByteArray();
                wechatMiniprograms.setQrcode(Base64.getEncoder().encodeToString(bytes));
                responseData.setData(bytes);
            }else {
                wechatMiniprograms.setQrcode(res);
                responseData.setData(post);
            }

            miniprogramsDao.update(wechatMiniprograms);





        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }


    /**
     * 获取微信二维码
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData createHotelWxQrCodeOther(WechatMiniprogramsSearch param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getSessionToken();
            TbUserSession user = this.getTbUserSession(sessionToken);

            WechatMiniprograms wechatMiniprograms = this.getAccessToken(user.getHotelGroupId());


            String url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + wechatMiniprograms.getAccessToken();

            JSONObject qrCodeParam = new JSONObject();
            qrCodeParam.put("scene", param.getScene());
            qrCodeParam.put("page", param.getPage());
            qrCodeParam.put("width", param.getWidth());
            qrCodeParam.put("auto_color", param.getAutoColor());
            qrCodeParam.put("line_color", param.getLineColor());
            qrCodeParam.put("is_hyaline", param.getHyaline());
            byte[] post = HttpRequest.post(url, qrCodeParam.toString());
            String scene = param.getScene();
            if (param.getCreateType() == 1) {
                scene = param.getRoomNo();
            }

            InputStream input = new ByteArrayInputStream(post);

            BufferedImage bi = ImageIO.read(input);


            if (param.getNotifyUrl() != null && param.getNotifyUrl().length() > 0) {
                BufferedImage bufferedImage = headYuan(param.getNotifyUrl());

                byte[] bytes1 = CustomQrCodeUtil.changLogo(post, bufferedImage);

                InputStream input1 = new ByteArrayInputStream(bytes1);
                bi = ImageIO.read(input1);

            }


            BufferedImage newPic = this.createNewPic(param.getRoomNo(), bi, param);
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            ImageIO.write(newPic, "jpeg", out);
            byte[] bytes = out.toByteArray();


            CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObject(bytes, user.getHid(), "room-qr-images", "image/jpeg", scene + ".jpeg");
            responseData.setData(uploadObjectRsp);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }


    public BufferedImage createNewPic(String title, BufferedImage logo, WechatMiniprogramsSearch wechatMiniprogramsSearch) {

        BufferedImage image = new BufferedImage(wechatMiniprogramsSearch.getWidth() + 100, wechatMiniprogramsSearch.getWidth() + 100, BufferedImage.TYPE_INT_RGB);

        //设置图片的背景色
        Graphics2D main = image.createGraphics();
        main.setColor(Color.white);
        main.fillRect(0, 0, wechatMiniprogramsSearch.getWidth() + 120, wechatMiniprogramsSearch.getWidth() + 120);

        //***********************插入中间广告图
        Graphics mainPic = image.getGraphics();

        if (logo != null) {
            mainPic.drawImage(logo, 40, 40, wechatMiniprogramsSearch.getWidth(), wechatMiniprogramsSearch.getWidth(), null);
            mainPic.dispose();
        }

        //***********************页面头部
        Graphics titleG = image.createGraphics();
        //设置区域颜色
        //titleG.setColor(Color.white);
        //填充区域并确定区域大小位置
        //titleG.fillRect(450, 50, 450, 50);
        //设置字体颜色，先设置颜色，再填充内容
        titleG.setColor(Color.BLACK);
        //设置字体
        Font titleFont = new Font("宋体", Font.BOLD, 36);
        titleG.setFont(titleFont);
        titleG.drawString(title, 100, wechatMiniprogramsSearch.getWidth() + 70);

        return image;
    }


    public BufferedImage headYuan(String url) {
        BufferedImage resultImg = null;

        try {
            // 图片缩放 -----------stat
            BufferedImage buffImg1 = ImageIO.read(new URL(url));
            // 图片宽度
            double width = buffImg1.getWidth();
            // 图片高度
            double height = buffImg1.getHeight();
            //设置大小
            if (width > 188 && height > 188) {
                width = 188 / (double) buffImg1.getWidth() * (double) buffImg1.getWidth();
                height = 188 / (double) buffImg1.getHeight() * (double) buffImg1.getHeight();
            }
            //  ------------end
            //缩放图片并转换成 BufferedImage
            BufferedImage tag = ImageUtils.toBufferedImage(buffImg1.getScaledInstance((int) width, (int) height, Image.SCALE_DEFAULT));
            if (StringUtils.isBlank(url)) {
                return null;
            }

            // 图片转圆形 --------stat
            resultImg = new BufferedImage(tag.getWidth(), tag.getHeight(), BufferedImage.TYPE_INT_RGB);
            Graphics2D g = resultImg.createGraphics();
            Ellipse2D.Double shape = new Ellipse2D.Double(0, 0, tag.getWidth(), tag.getHeight());
            // 使用 setRenderingHint 设置抗锯齿
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            resultImg = g.getDeviceConfiguration().createCompatibleImage(tag.getWidth(), tag.getHeight(), Transparency.TRANSLUCENT);
            g = resultImg.createGraphics();
            // 使用 setRenderingHint 设置抗锯齿
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g.setClip(shape);
            g.drawImage(tag, 0, 0, null);
            g.dispose();
        } catch (MalformedURLException e) {
            log.error("",e);
            log.info("URL格式异常" + e.getMessage());
        } catch (IOException e) {
            log.error("",e);
            log.info("读取图片异常" + e.getMessage());
        }
        return resultImg;

    }

    public BufferedImage toEsas(String url, BufferedImage tag) {
        BufferedImage resultImg = null;
        try {
            if (StringUtils.isBlank(url)) {
                return null;
            }
            // 图片转圆形 --------stat
            resultImg = new BufferedImage(tag.getWidth(), tag.getHeight(), BufferedImage.TYPE_INT_RGB);
            Graphics2D g = resultImg.createGraphics();
            Ellipse2D.Double shape = new Ellipse2D.Double(0, 0, tag.getWidth(), tag.getHeight());
            // 使用 setRenderingHint 设置抗锯齿
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            resultImg = g.getDeviceConfiguration().createCompatibleImage(tag.getWidth(), tag.getHeight(), Transparency.TRANSLUCENT);
            g = resultImg.createGraphics();
            // 使用 setRenderingHint 设置抗锯齿
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g.setClip(shape);
            g.drawImage(tag, 0, 0, null);
            g.dispose();
        } catch (Exception e) {
            log.error("",e);
        }
        return resultImg;


    }


    @Override
    public ResponseData findHotelWxMinipro(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            TbUserSession tbUserSession = this.getTbUserSession(sessionToken);
            WechatMiniprograms accessToken = this.getAccessToken(tbUserSession.getHotelGroupId());

            responseData.setData(accessToken);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }

    public WechatMiniprograms getAccessToken(int hotelGroupId) throws Exception {

        WechatMiniprogramsSearch wechatMiniprogramsSearch = new WechatMiniprogramsSearch();
        wechatMiniprogramsSearch.setHotelGroupId(hotelGroupId);
        List<WechatMiniprograms> wechatMiniprogramsList = miniprogramsDao.selectBySearch(wechatMiniprogramsSearch);

        if (wechatMiniprogramsList.size() < 1) {
            throw new Exception("未查询到小程序配置信息");
        }
        WechatMiniprograms wechatMiniprograms = wechatMiniprogramsList.get(0);

        String accUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + wechatMiniprograms.getAppid()
                + "&secret=" + wechatMiniprograms.getAppsecret();
        String accessToken = wechatMiniprograms.getAccessToken();

        Date date = new Date();

        long time = wechatMiniprograms.getAccessTokenTime().getTime();

        Long diff = date.getTime() - time;

        int i = diff.intValue();

        if (accessToken == null || "".equals(accessToken) || i >= wechatMiniprograms.getAccessTokenValidityTime()) {

            String s = HttpRequest.sendGet(accUrl, "");

            JSONObject jsonObject = JSONObject.fromObject(s);

            if (jsonObject.get("errcode") != null && jsonObject.getInt("errcode") != 0) {
                throw new Exception(jsonObject.getString("errmsg"));
            }

            wechatMiniprograms.setAccessToken(jsonObject.getString("access_token"));
            wechatMiniprograms.setAccessTokenTime(date);

        } else {
            return wechatMiniprograms;
        }

        WechatMiniprogramsSearch wechatMiniprogramsSearch1 = new WechatMiniprogramsSearch();
        wechatMiniprogramsSearch1.setAppid(wechatMiniprograms.getAppid());
        List<WechatMiniprograms> wechatMiniprograms1 = miniprogramsDao.selectBySearch(wechatMiniprogramsSearch1);

        Date date1 = new Date();
        for (WechatMiniprograms miniprograms : wechatMiniprograms1) {
            WechatMiniprograms wxs = new WechatMiniprograms();
            wxs.setAccessTokenTime(date1);
            wxs.setId(miniprograms.getId());
            wxs.setAccessToken(wechatMiniprograms.getAccessToken());
            miniprogramsDao.update(miniprograms);
        }

        return wechatMiniprograms;
    }

    @Override
    public ResponseData findHotelJson(FindHotelMsg findHotelMsg) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            WechatMiniprogramsSearch wechatAccountsSearch = new WechatMiniprogramsSearch();
            wechatAccountsSearch.setHotelGroupId(findHotelMsg.getHotelGroupId());

            List<WechatMiniprograms> wechatMiniprograms = miniprogramsDao.selectBySearch(wechatAccountsSearch);

            if (wechatMiniprograms.size() < 1) {
                throw new Exception("查询微信记录失败");
            }
            WechatMiniprograms miniprograms = wechatMiniprograms.get(0);

            RoomInfo roomInfo = roomInfoDao.selectById(findHotelMsg.getRoomId());

            if (roomInfo == null) {
                throw new Exception("查询房间信息失败");
            }

            HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(roomInfo.getHid());

            if (!hotelBaseInfo.getHotelGroupId().equals(miniprograms.getHotelGroupId())) {
                throw new Exception("查询房间信息与酒店信息不一致");
            }

            HotelMiniproSettingSearch hotelMiniproSettingSearch = new HotelMiniproSettingSearch();
            hotelMiniproSettingSearch.setHid(roomInfo.getHid());
            Page<HotelMiniproSetting> hotelMiniproSettings = hotelMiniproSettingDao.selectBySearch(hotelMiniproSettingSearch);

            if (hotelMiniproSettings == null || hotelMiniproSettings.size() < 1) {
                throw new Exception("当前时间不在商品售卖时间内");
            }

            HotelMiniproSetting hotelMiniproSetting = hotelMiniproSettings.get(0);

            Integer tabW = hotelMiniproSetting.getTabW();
            if (tabW == 0) {
                throw new Exception("当前时间不在商品售卖时间内");
            }

            Regist regist = null;

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(hotelBaseInfo.getHid());
            registSearch.setRoomNumId(roomInfo.getRoomInfoId());
            registSearch.setState(0);

            List<Regist> regists = registDao.selectBySearch(registSearch);

            if (regists != null && regists.size() > 0) {
                regist = regists.get(0);
            }

            JSONObject data = new JSONObject();
            data.put("hid", roomInfo.getHid());
            data.put("hotelGroupId", miniprograms.getHotelGroupId());
            data.put("roomInfoId", roomInfo.getRoomInfoId());
            data.put("roomNum", roomInfo.getRoomNum());
            data.put("cardLevelId", miniprograms.getCardLevelId());
            data.put("hotelName", hotelBaseInfo.getHotelName());
            data.put("reType", 2);

            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setHid(roomInfo.getHid());
            hotelSettingByParamId.setParamId(HOTEL_SETTING.STOCK_CALSTOCK);

            Object hotelSettingByParamId1 = 0;


            data.put("hotelIsCheckNum", hotelSettingByParamId1);

            if (regist != null) {
                data.put("reType", 1);
                data.put("registId", regist.getRegistId());
            }

            responseData.setData(data);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData findHotelJsonOther(FindHotelMsg findHotelMsg) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken =    RSAUtils.getStringDecrypt(findHotelMsg.getHotelId()); ;
            final TbUserSession user = this.getTbUserSession(sessionToken);

            WechatMiniprogramsSearch wechatAccountsSearch = new WechatMiniprogramsSearch();
            wechatAccountsSearch.setHotelGroupId(user.getHotelGroupId());

            List<WechatMiniprograms> wechatMiniprograms = miniprogramsDao.selectBySearch(wechatAccountsSearch);

            if (wechatMiniprograms.size() < 1) {
                throw new Exception("查询微信记录失败");
            }
            WechatMiniprograms miniprograms = wechatMiniprograms.get(0);

            RoomInfo roomInfo = roomInfoDao.selectById(findHotelMsg.getRoomId());

            if (roomInfo == null) {
                throw new Exception("查询房间信息失败");
            }

            HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(roomInfo.getHid());

            if (!hotelBaseInfo.getHotelGroupId().equals(miniprograms.getHotelGroupId())) {
                throw new Exception("查询房间信息与酒店信息不一致");
            }

            HotelMiniproSettingSearch hotelMiniproSettingSearch = new HotelMiniproSettingSearch();
            hotelMiniproSettingSearch.setHid(roomInfo.getHid());
            Page<HotelMiniproSetting> hotelMiniproSettings = hotelMiniproSettingDao.selectBySearch(hotelMiniproSettingSearch);

            if (hotelMiniproSettings == null || hotelMiniproSettings.size() < 1) {
                throw new Exception("当前时间不在商品售卖时间内");
            }

            HotelMiniproSetting hotelMiniproSetting = hotelMiniproSettings.get(0);

            Integer tabW = hotelMiniproSetting.getTabW();
            if (tabW == 0) {
                throw new Exception("当前时间不在商品售卖时间内");
            }

            Regist regist = null;

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(hotelBaseInfo.getHid());
            registSearch.setRoomNumId(roomInfo.getRoomInfoId());
            registSearch.setState(0);

            List<Regist> regists = registDao.selectBySearch(registSearch);

            if (regists != null && regists.size() > 0) {
                regist = regists.get(0);
            }

            JSONObject data = new JSONObject();
            data.put("hid", roomInfo.getHid());
            data.put("hotelGroupId", miniprograms.getHotelGroupId());
            data.put("roomInfoId", roomInfo.getRoomInfoId());
            data.put("roomNum", roomInfo.getRoomNum());
            data.put("cardLevelId", miniprograms.getCardLevelId());
            data.put("hotelName", hotelBaseInfo.getHotelName());
            data.put("reType", 2);
            data.put("goodType", 2);

            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setHid(roomInfo.getHid());
            hotelSettingByParamId.setParamId(HOTEL_SETTING.STOCK_CALSTOCK);
            Object hotelSettingByParamId1 = findHotelSettingByParamId(hotelSettingByParamId);
            if(hotelSettingByParamId1==null){
                hotelSettingByParamId1 = 0;
            }

            data.put("hotelIsCheckNum", hotelSettingByParamId1);

            if (regist != null) {
                data.put("reType", 1);
                data.put("registId", regist.getRegistId());
            }

            responseData.setData(data);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 修改订单状态
     *
     * @param shoppingOrderDelivery
     * @return
     */
    @Override
    public ResponseData ShoppingOrderDeliveryFunc(ShoppingOrderDelivery shoppingOrderDelivery) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            TbUserSession user = this.getTbUserSession(shoppingOrderDelivery.getSessionToken());

            GoodsShoppingOrder goodsShoppingOrder = goodsShoppingOrderDao.selectById(shoppingOrderDelivery.getId());

            if (!user.getHid().equals(user.getHid())) {
                throw new Exception("未查到对应的商品信息-H");
            }

            // 老状态
            Integer orderType = goodsShoppingOrder.getOrderType();

            // 新状态
            Integer nowType = shoppingOrderDelivery.getType();

            goodsShoppingOrder.setState(nowType);
            goodsShoppingOrder.setSaleRemark(">" + nowType);

            // 判断是否是待收货，待收货则

            if (orderType == 2 && nowType == 3) {

                // 查询模板id
                WechatSubscribeMsgSearch wechatSubscribeMsgSearch = new WechatSubscribeMsgSearch();
                wechatSubscribeMsgSearch.setHotelGroupId(shoppingOrderDelivery.getHotelGroupId());
                wechatSubscribeMsgSearch.setMsgType(1);
                Page<WechatSubscribeMsg> wechatSubscribeMsgs = wechatSubscribeMsgDao.selectBySearch(wechatSubscribeMsgSearch);

                if (wechatSubscribeMsgs != null && wechatSubscribeMsgs.size() > 0) {

                    WechatSubscribeMsg wechatSubscribeMsg = wechatSubscribeMsgs.get(0);

                    String createUserId = goodsShoppingOrder.getCreateUserId();

                    String[] split = createUserId.split("-");

                    String openId = split[0];

                    for (int i = 1; i < split.length - 1; i++) {
                        openId += "-";
                        openId += split[i];
                    }

                    WechatMiniprograms accessToken = this.getAccessToken(user.getHotelGroupId());

                    JSONObject sendMsg = new JSONObject();
                    sendMsg.put("touser", openId);
                    sendMsg.put("template_id", wechatSubscribeMsg.getMsgId());

                    HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(goodsShoppingOrder.getHid());


                    JSONObject sendData = new JSONObject();

                    JSONObject thing2 = new JSONObject();
                    thing2.put("value", hotelBaseInfo.getHotelName());
                    sendData.put("thing2", thing2);
                    JSONObject thing8 = new JSONObject();
                    thing2.put("value", goodsShoppingOrder.getGoodsSummary());
                    sendData.put("thing8", thing8);
                    JSONObject number13 = new JSONObject();
                    thing2.put("value", goodsShoppingOrder.getAddress());
                    sendData.put("number13", number13);
                    JSONObject time22 = new JSONObject();
                    thing2.put("value", HotelUtils.currentTime());
                    sendData.put("time22", time22);
                    JSONObject thing18 = new JSONObject();
                    thing2.put("value", shoppingOrderDelivery.getSendType() == 1 ? "人工" : "机器人");
                    sendData.put("thing18", thing18);

                    sendMsg.put("data", sendData);

                    String url = subscribeSend + accessToken.getAccessToken();

                    String s = HttpRequest.sendPost(url, sendMsg);

                    log.info("s={}",s);

                }


            }


            goodsShoppingOrderDao.update(goodsShoppingOrder);


        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData hotelMap(WechatMiniprogramsSearch wechatMiniprogramsSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = wechatMiniprogramsSearch.getSessionToken();
            TbUserSession tbUserSession = this.getTbUserSession(sessionToken);

            HotelBaseInfoSearch hotelBaseInfoSearch = new HotelBaseInfoSearch();

            hotelBaseInfoSearch.setHotelGroupId(tbUserSession.getHotelGroupId());

            Page<HotelBaseInfo> hotelBaseInfos = hotelBaseInfoDao.selectBySearch(hotelBaseInfoSearch);

            HashMap<String, JSONObject> hMap = new HashMap<>();

            for (HotelBaseInfo hb : hotelBaseInfos) {

                JSONObject jo = new JSONObject();
                jo.put("hotelName", hb.getHotelName());
                jo.put("hid", hb.getHid());
                jo.put("hotelGroupId", hb.getHotelGroupId());
                jo.put("addr", hb.getAddr());
                jo.put("lat", hb.getLat());
                jo.put("lon", hb.getLon());
                jo.put("hotelNameEn", hb.getHotelNameEn());

                hMap.put(hb.getHid() + "", jo);

            }

            responseData.setData(hMap);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }

    @Override
    public ResponseData allWxHotel(HotelBaseInfoSearch param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/

            HotelBaseInfoSearch hotelBaseInfoSearch = new HotelBaseInfoSearch();

            Page<HotelBaseInfo> hotelBaseInfos = hotelBaseInfoDao.selectBySearch(hotelBaseInfoSearch);

            WechatMiniprogramsSearch wechatMiniprogramsSearch1 = new WechatMiniprogramsSearch();
            List<WechatMiniprograms> wechatMiniprograms = wechatMiniprogramsDao.selectBySearch(wechatMiniprogramsSearch1);
            Map<Integer, WechatMiniprograms> collect = wechatMiniprograms.stream().collect(Collectors.toMap(WechatMiniprograms::getHotelGroupId, a -> a, (k1, k2) -> k2));

            ArrayList<HotelView> hotelViews = new ArrayList<>();

            ArrayList<HotelView> hotelViewsNs = new ArrayList<>();
            Double lat = param.getLat();
            Double lon = param.getLon();

            for (HotelBaseInfo hb : hotelBaseInfos) {
                Integer hotelGroupId = hb.getHotelGroupId();
                if (hotelGroupId == null) {
                    continue;
                }
                WechatMiniprograms miniprograms = collect.get(hotelGroupId);
                if (miniprograms == null) {
                    continue;
                }

                HotelView hotelView = new HotelView();
                hotelView.setHid(hb.getHid());
                hotelView.setHotelGroupId(hotelGroupId);
                hotelView.setImg(hb.getOpenId());
                hotelView.setHotelName(hb.getHotelName());
                hotelView.setPrice(hb.getCheckLimit());
                hotelView.setHotelPhone(hb.getContactPhone());
                hotelView.setCardLevelId(miniprograms.getCardLevelId());
                hotelView.setCardGroupId(miniprograms.getCardTypeId());
                hotelView.setAddr(hb.getAddr());

                Double lat1 = hb.getLat();
                Double lon1 = hb.getLon();
                if (lat1 != null && lon1 != null) {
                    Double distance = HotelUtils.getDistance(lat, lon, hb.getLat(), hb.getLon());
                    Double v = distance.intValue() / 1000.0;
                    hotelView.setArea(v.toString());
                    hotelView.setKs(v);
                    hotelViews.add(hotelView);
                } else {
                    hotelView.setAddr("");
                    hotelView.setKs(1000000000.0);
                    hotelViewsNs.add(hotelView);
                }
            }
            hotelViews.addAll(hotelViewsNs);
            hotelViews.sort(Comparator.comparing(HotelView::getKs));
            responseData.setData(hotelViews);


        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }

    /**
     * 查询会员绑定二维码
     * @param cardGroupUrlSearch
     * @return
     */
    @Override
    public ResponseData searchVipUrl(CardGroupUrlSearch cardGroupUrlSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            TbUserSession user = this.getTbUserSession(cardGroupUrlSearch);


            CardGroupInfo cardGroupInfo = cardGroupInfoDao.selectById(cardGroupUrlSearch.getCardGroupId());

            if(cardGroupInfo==null){
                throw new Exception("未查询到对应的会员信息");
            }

            CardGroupUrlSearch cgu = new CardGroupUrlSearch();
            cgu.setCardGroupId(cardGroupUrlSearch.getCardGroupId());
            cgu.setHid(cardGroupUrlSearch.getHid());

            Page<CardGroupUrl> cardGroupUrls = cardGroupUrlDao.selectBySearch(cgu);

            // 存在则返回，不存在重新生成
            if(cardGroupUrls.size()>0){
                responseData.setData(cardGroupUrls.get(0));
                return responseData;
            }


            WechatMiniprograms wechatMiniprograms = this.getAccessToken(user.getHotelGroupId());


            String url = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + wechatMiniprograms.getAccessToken();
            WechatMiniprogramsSearch wechatMiniprogramsSearch = new WechatMiniprogramsSearch();
            JSONObject qrCodeParam = new JSONObject();
            String scene =   user.getHotelGroupId() + "&" + wechatMiniprograms.getCardLevelId()+"&"+cardGroupInfo.getId();
            qrCodeParam.put("scene", "scene="+ scene);
            qrCodeParam.put("page", "pages/login/login");
            qrCodeParam.put("width", wechatMiniprogramsSearch.getWidth());
            qrCodeParam.put("auto_color", wechatMiniprogramsSearch.getAutoColor());
            qrCodeParam.put("line_color", wechatMiniprogramsSearch.getLineColor());
            qrCodeParam.put("is_hyaline", wechatMiniprogramsSearch.getHyaline());
            byte[] post = HttpRequest.post(url, qrCodeParam.toString());


            InputStream input = new ByteArrayInputStream(post);

            BufferedImage bi = ImageIO.read(input);


            if (wechatMiniprograms.getNotifyUrl() != null && wechatMiniprograms.getNotifyUrl().length() > 0) {
                BufferedImage bufferedImage = headYuan(wechatMiniprograms.getNotifyUrl());

                byte[] bytes1 = CustomQrCodeUtil.changLogo(post, bufferedImage);

                InputStream input1 = new ByteArrayInputStream(bytes1);
                bi = ImageIO.read(input1);

            }

            BufferedImage newPic = this.createNewPic(cardGroupInfo.getCardName(), bi, wechatMiniprogramsSearch);
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            ImageIO.write(newPic, "jpeg", out);
            byte[] bytes = out.toByteArray();

            CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObject(bytes, user.getHid(), "vipbind-qr-images", "image/jpeg", MD5Util.MD5Encode(scene,"utf-8") + ".jpeg");

            CardGroupUrl cardGroupUrl = new CardGroupUrl();
            cardGroupUrl.setBindUrl(uploadObjectRsp.getUrl());
            cardGroupUrl.setHid(user.getHid());
            cardGroupUrl.setHotelGroupId(user.getHotelGroupId());
            cardGroupUrl.setCardGroupId(cardGroupUrlSearch.getCardGroupId());

            Integer insert = cardGroupUrlDao.insert(cardGroupUrl);

            responseData.setData(cardGroupUrl);

        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }
}
