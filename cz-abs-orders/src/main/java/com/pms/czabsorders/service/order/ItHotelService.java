package com.pms.czabsorders.service.order;


import com.pms.czabsorders.bean.*;
import com.pms.czpmsutils.ResponseData;

// 酒店基础信息相关接口
public interface ItHotelService {

    public ResponseData getPriceCodeByHid(GetPriceCodeByHidDTO getPriceCodeByHidDTO);

    public ResponseData getPriceCodeByRoomType(GetPriceCodeByRoomTypeDTO getPriceCodeByRoomTypeDTO);

    public ResponseData roomTypeAvailableRoom(RoomTypeAvailableRoomDTO roomTypeAvailableRoomDTO);

    public ResponseData findRoomDayPrice(FindRoomDayPriceDTO findRoomDayPriceDTO);

    public ResponseData searchRoomTypeMsg(SearchRoomTypeMsgDTO searchRoomTypeMsgDTO);

    public ResponseData findBulidFloor(FindBulidFloorDTO findBulidFloorDTO);

    public ResponseData findHotelRoomInfo(FindHotelRoomInfoDTO findHotelRoomInfoDTO);

    public ResponseData searchRoomType(SearchRoomTypeMsgDTO searchRoomTypeMsgDTO);

    public ResponseData findHotelRoomNum(FindHotelRoomNumDTO findHotelRoomNumDTO);

    public ResponseData dayRoomList(FindHotelRoomNumDTO findHotelRoomNumDTO);

    public ResponseData findHotelMsg(FindHotelDTO findHotelDTO);


}
