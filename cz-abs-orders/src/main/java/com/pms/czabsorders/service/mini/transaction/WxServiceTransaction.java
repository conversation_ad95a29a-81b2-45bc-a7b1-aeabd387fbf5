package com.pms.czabsorders.service.mini.transaction;

import com.pms.czabsorders.bean.mini.WxShoppingCardRequest;
import com.pms.czaccount.bean.account.Account;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czmembership.bean.member.CardGroupInfo;
import com.pms.czmembership.bean.member.CardInfo;
import com.pms.czmembership.bean.member.CardOperationRecord;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.*;
import com.pms.pmswarehouse.bean.GoodsShoppingcar;
import com.pms.pmswarehouse.bean.GoodsShoppingcarDetails;

import java.util.ArrayList;
import java.util.List;

public interface WxServiceTransaction {

    public void registVxVip(CardGroupInfo cardGroupInfo, List<CardInfo> cardInfoList, ArrayList<CardOperationRecord> cardOperationRecords, TbUserSession session) throws Exception;

    public void addBookService(BookingOrder bookingOrder, RegistGroup registGroup, List<BookingOrderRoomType> bookingOrderRoomTypes, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, ArrayList<Oprecord> oprecords, BookingOrderConfig bookingOrderConfig, Account account) throws Exception;


    public void upaWxMemberMsg(CardGroupInfo cardGroupInfo,List<CardInfo> cardInfos) throws Exception;

    public void addWxShoppingCar(GoodsShoppingcar delGoodshoppingcar, List<GoodsShoppingcarDetails> delDetails,
                                 GoodsShoppingcar goodsShoppingcar, WxShoppingCardRequest wxShoppingCardRequest);

    public void cancelOrderService(List<BookingOrder> bookingOrders, List<BookingOrderRoomType> bookingOrderRoomTypes, List<BookingOrderRoomNum> bookingOrderRoomNums, TbUserSession user, List<RoomAuxiliaryRelation> roomAuxiliaryRelations) throws Exception;

    // 钟点房续住
    public void stayHoursTranSaction(Regist regist,Account account,List<HourRoomDayUse> addHourUse,List<HourRoomDayUse> upaHourUse,BookingOrderRoomNum upaBookRoom,BookingOrder upaBookOrder);

}
