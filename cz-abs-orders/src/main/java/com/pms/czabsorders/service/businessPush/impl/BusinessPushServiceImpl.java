package com.pms.czabsorders.service.businessPush.impl;

import com.pms.czabsorders.bean.BusinessPushDataRqeust;
import com.pms.czabsorders.service.businessPush.BusinessPushService;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.UUID;


@Service
@Primary
@Slf4j
public class BusinessPushServiceImpl extends BaseService implements BusinessPushService {


    @Override
    public ResponseData businessPushData(BusinessPushDataRqeust businessPushDataRqeust) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = businessPushDataRqeust.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Integer hid = user.getHid();
            HashMap<String, String> map = new HashMap<>();
            map.put("type", businessPushDataRqeust.getType().toString());
            map.put("termType", businessPushDataRqeust.getTermType().toString());
            map.put("hid", hid.toString());
            map.put("uuid", UUID.randomUUID().toString().replace("-", ""));
            //自助机写卡
            if (businessPushDataRqeust.getType() == 1) {
                map.put("startTime", businessPushDataRqeust.getStartTime());
                map.put("endTime", businessPushDataRqeust.getEndTime());
                map.put("roomNo", businessPushDataRqeust.getRoomNo());
                map.put("roomInfoId", businessPushDataRqeust.getRoomInfoId());
                map.put("guestName", businessPushDataRqeust.getGuestName());
            }
            //无证比对
            else if (businessPushDataRqeust.getType() == 5){
                map.put("guestName", businessPushDataRqeust.getGuestName());
                map.put("idCode", businessPushDataRqeust.getIdCode());
                map.put("orderId" , businessPushDataRqeust.getOrderId());
                map.put("phone", businessPushDataRqeust.getPhone());
            }

            String rt = machinePush(hid, hid, businessPushDataRqeust.getMacToken(), map);
            log.info("rt={}",rt);
            return null;
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }
}
