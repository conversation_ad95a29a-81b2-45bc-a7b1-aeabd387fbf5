package com.pms.czabsorders.service.turnAlways.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.FindAvailableHourRoomRequest;
import com.pms.czabsorders.bean.HourTrunAlwaysResponse;
import com.pms.czabsorders.service.turnAlways.TurnAlwaysService;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.room.*;
import com.pms.czhotelfoundation.bean.room.search.RoomCheckRecordSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomRepairRecordHistorySearch;
import com.pms.czhotelfoundation.bean.search.HourRoomDayUseSearch;
import com.pms.czhotelfoundation.dao.HourRoomDayUseDao;
import com.pms.czhotelfoundation.dao.room.RoomCheckRecordDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomRepairRecordHistoryDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.JsonDateValueProcessor;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ECache;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.room.BC_ROOM_STATUS;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.BookingOrderRoomNum;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.search.BookingOrderRoomNumSearch;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.dao.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Primary
@Slf4j
public class TurnAlwaysServiceImpl extends BaseService implements TurnAlwaysService {


    /**
     * 登记信息表
     */
    @Autowired
    private RegistDao registDao;

    /**
     * 预定分房表
     */
    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    /**
     * 维修房
     */
    @Autowired
    private RoomRepairRecordHistoryDao roomRepairRecordHistoryDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;
    /**
     * 在住人
     */
    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    // 房型
    @Autowired
    private RoomTypeDao roomTypeDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private HourRoomDayUseDao hourRoomDayUseDao;

    @Autowired
    private RoomCheckRecordDao roomCheckRecordDao;

    /**
     * 展示未来房价
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> turnAlways(JSONObject param) {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(ER.RES, ER.SUCC);
        try {

            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();

            // 每日占用情况
            String drms = ECache.TURNALWAYS_DATEROOMSTATE + "_" + user.getHid();
            Object drmsObj = userCahe.get(ECache.TURNALWAYS_DATEROOMSTATE, drms);

            // 预定未分房的数据
            String nrrb = ECache.TURNALWAYS_NOROWROOMBOOK + "_" + user.getHid();
            Object nrrbObj = userCahe.get(ECache.TURNALWAYS_NOROWROOMBOOK, nrrb);

            // 每天的使用数据
            String tadn = ECache.TURNALWAYS_TURNALWATSDATENUM + "_" + user.getHid();
            Object tadnObj = userCahe.get(ECache.TURNALWAYS_TURNALWATSDATENUM, tadn);


            // 判断缓存中的数据是否正确
            boolean b = drmsObj == null || nrrbObj == null || tadnObj == null || "{}".equals(drmsObj.toString()) || "{}".equals(nrrbObj.toString()) || "{}".equals(tadnObj.toString());

            if (b) {
                turnAlwaysCache(user, resultMap, userCahe);
            } else {
                resultMap.put("dateRoomStateMsg", JSONObject.fromObject(drmsObj));
                resultMap.put("noRowRoomBookMsg", JSONObject.fromObject(nrrbObj));
                resultMap.put("turnAlwaysDateNum", JSONObject.fromObject(tadnObj));
            }


        } catch (Exception e) {
            log.error("", e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }


    @Override
    public void turnAlwaysCacheFunc(TbUserSession user) {
        try {
            HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
            this.turnAlwaysCache(user, new HashMap<>(), stringObjectObjectHashOperations);
        } catch (Exception e) {
            log.error("", e);
        }

    }

    /**
     * 更新缓存中每日房情的数据
     *
     * @param user
     * @param resultMap
     * @throws Exception
     */
    @Override
    public void turnAlwaysCache(TbUserSession user, Map<String, Object> resultMap, HashOperations<String, Object, Object> userCahe) throws Exception {

        Date date1 = HotelUtils.addDayGetNewDate(new Date(), -2);
        int date2Int = HotelUtils.parseDate2Int(date1);

        // 最底层 展示信息，已状态为key，value 未最基础信息
        // 状态 1.入住信息  2.维修/停用 3.预定信息(已排房) 4.预定信息(未排房)
        Map<Integer, JSONObject> stateMsg = new HashMap<>();

        // 第二层 已房间id为key，value 为 stateMsg
        HashMap<Integer, HashMap<Integer, JSONObject>> roomForStateMsg = new HashMap<>();

        // 最外层 已日期为key，value 为 roomForStateMsg
        // 上面两个变量只为注释，并没有实际意义
        HashMap<String, HashMap<String, HashMap<String, JSONObject>>> dateRoomStateMsg = new HashMap<>();

        // 房间使用情况
        //   key  roomId+yyyy-MM-dd
        HashMap<String, Integer> roomUseDetail = new HashMap<>();

        // 每日使用数据明细
        JSONObject dateNum = new JSONObject();

        //将所有查询数据库的数据 处理后添加到当前集合
        List<JSONObject> baseData = new ArrayList<>();

        // 记录 最小的开始时间和 最大的结束时间
        // 已这两个时间为准则遍历，减少循环次数
        Date minStartTime = new Date();

        Date maxEndTime = new Date();

        Date dayRoomStartDate = new Date();

        // 1.查询所有在住信息
        //   在住信息的状态为 1
        RegistSearch registSearch = new RegistSearch();
        registSearch.setHid(user.getHid());
        registSearch.setState(0);

        List<Regist> regists = registDao.selectBySearch(registSearch);

        //查询在住人
        HashMap<String, Object> map = new HashMap<>();
        map.put("hid", user.getHid());
        map.put("registState", 0);
        List<RegistPerson> registPeople = registPersonDao.searchCheckinPeople(map);
        Map<Integer, List<RegistPerson>> collect = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));
        JSONObject peoObj = new JSONObject();
        for (Regist regist : regists) {
            Date checkinTime = regist.getCheckinTime();
            // 凌晨6点前开的房，算凌晨房
            int hours = checkinTime.getHours();
            if (hours <= 6) {
                // 入住时间 往前推 6个小时
                checkinTime = new Date(checkinTime.getTime() - 21600000);
            }
            Date checkoutTime = regist.getCheckoutTime();


            JSONObject registObj = new JSONObject();
            registObj.put("registId", regist.getRegistId());
            registObj.put("roomNum", regist.getRoomNum());
            registObj.put("roomNumId", regist.getRoomNumId());
            registObj.put("resourceId", regist.getResourceId());
            registObj.put("resourceName", regist.getResourceName());
            registObj.put("registGroupId", regist.getRegistGroupId());
            registObj.put("roomTypeId", regist.getRoomTypeId());
            registObj.put("roomTypeName", regist.getRoomTypeName());
            if (null != collect.get(regist.getRegistId())) {
                JSONArray people = new JSONArray();
                for (RegistPerson registPerson : collect.get(regist.getRegistId())) {
                    peoObj = new JSONObject();
                    peoObj.put("personName", registPerson.getPersonName());
                    peoObj.put("registPersonId", registPerson.getRegistPersonId());
                    people.add(peoObj);
                }
                registObj.put("people", people);
            }

            baseData = dateBaseHandler(1, baseData, regist.getRoomNumId(), checkinTime, checkoutTime, minStartTime, maxEndTime, registObj);
        }

        // 2.查询当前有效的维修信息
        RoomRepairRecordHistorySearch roomRepairRecordSearch = new RoomRepairRecordHistorySearch();
        roomRepairRecordSearch.setHid(user.getHid());
        roomRepairRecordSearch.setState(0);
        List<RoomRepairRecordHistory> roomRepairRecords = roomRepairRecordHistoryDao.selectBySearch(roomRepairRecordSearch);

        for (RoomRepairRecordHistory roomRepairRecord : roomRepairRecords) {

            Date begintime = roomRepairRecord.getBegintime();
            Date endtime = roomRepairRecord.getEndtime();

            JSONObject registObj = new JSONObject();
            registObj.put("repairCheckRoomRecordId", roomRepairRecord.getRepairCheckRoomRecordId());
            registObj.put("des", roomRepairRecord.getDes());
            registObj.put("roomNum", roomRepairRecord.getRoomNum());
            registObj.put("roomTypeId", roomRepairRecord.getRoomTypeId());
            registObj.put("roomNumId", roomRepairRecord.getRoomId());
            baseData = dateBaseHandler(2, baseData, roomRepairRecord.getRoomId(), begintime, endtime, minStartTime, maxEndTime, registObj);

        }

        // 3.查询当前预定信息
        BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
        bookingOrderRoomNumSearch.setHid(user.getHid());
        bookingOrderRoomNumSearch.setOrderState(BOOK.STA_YX);
        bookingOrderRoomNumSearch.setIsCheckin(0);

        List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

        // 对预定信息进行分组
        //  已排房/未排房

        // 未排房信息
        HashMap<String, JSONArray> noRowRoomBookMsg = new HashMap<>();

        for (BookingOrderRoomNum bookingOrderRoomNum : bookingOrderRoomNums) {
            Date checkinTime = bookingOrderRoomNum.getCheckinTime();
            Date checkoutTime = bookingOrderRoomNum.getCheckoutTime();

            // 0 未分房  1 已分房
            Integer rowRoom = bookingOrderRoomNum.getRowRoom();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("bookingOrderId", bookingOrderRoomNum.getBookingOrderId());
            jsonObject.put("roomNumId", bookingOrderRoomNum.getRoomNumId());
            jsonObject.put("roomNum", bookingOrderRoomNum.getRoomNum());
            jsonObject.put("id", bookingOrderRoomNum.getId());
            jsonObject.put("roomTypeId", bookingOrderRoomNum.getRoomTypeId());
            int type = 3;
            //未分房
            if (rowRoom == 0) {
                jsonObject.put("roomNumId", 0);
                type = 4;
            }

            baseData = dateBaseHandler(type, baseData, bookingOrderRoomNum.getRoomNumId(), checkinTime, checkoutTime, minStartTime, maxEndTime, jsonObject);

        }


        Date bianliDate = new Date();
        // 如果遍历的最早时间，小于当前时间减 2 天，则最小遍历时间 未 前天
        Integer minInt = HotelUtils.parseDate2Int(minStartTime);
        if (minInt < date2Int) {
            minStartTime = date1;
        }
        // 4.对已经处理的baseData做处理
        // 对最大最小时间差做分组
        List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(minStartTime).substring(0, 10), HotelUtils.getDay(HotelUtils.parseDate2Str(maxEndTime), 1));

        HashMap<String, JSONObject> rtJson = new HashMap<>();

        for (String date : allDayListBetweenDate) {

            //日期格式 int类型  yyyyMMdd
            int dateInt = Integer.parseInt(date.replace("-", ""));

            // 每日房情 ，精确到房间
            HashMap<String, HashMap<String, JSONObject>> drsm = dateRoomStateMsg.get(date);
            if (drsm == null) {
                drsm = new HashMap<String, HashMap<String, JSONObject>>();
            }

            JSONArray noRoomList = noRowRoomBookMsg.get(date);
            if (noRoomList == null) {
                noRoomList = new JSONArray();
            }

            int checkInNum = 0;     //入住数
            int repairdNum = 0;     //维修数
            int bookRoomNum = 0;   //预定数
            int bookNoRoomNum = 0;  //预定未分房数

            for (JSONObject obj : baseData) {
                // 开始时间
                // 如果当前时间 小于 数据的开始时间，则说明用户入住的开始时间大于当前时间，不在当前时间内，则进行下一条数据
                int beginTimeInt = obj.getInt("beginTimeInt");
                if (dateInt < beginTimeInt) {
                    continue;
                }

                // 结束时间
                // 如果结束时间 大于 数据的结束时间，则进行下一条数据
                int endTimeInt = obj.getInt("endTimeInt");
                if (dateInt >= endTimeInt) {
                    continue;
                }

                int roomNumId = 0;
                try {
                    roomNumId = obj.getInt("roomNumId");
                } catch (Exception e) {
                    log.info("obj={}", obj.toString());
                }
                int dataType = obj.getInt("dataType");

                if (obj.get("roomTypeId") == null) {
                    log.info("obj={}", obj.toString());
                }

                int roomTypeId = obj.getInt("roomTypeId");

                // 房型key
                String roomTypeKey = dateInt + "" + roomTypeId;
                JSONObject rtRoomType = rtJson.get(roomTypeKey);
                if (rtRoomType == null) {
                    rtRoomType = new JSONObject();
                }
                Integer rtRoomTypeInt = 0;
                Object rtRoomTypeIntObj = rtRoomType.get(dataType + "");
                if (rtRoomTypeIntObj != null) {
                    rtRoomTypeInt = Integer.parseInt(rtRoomTypeIntObj.toString());
                }

                rtRoomTypeInt++;
                rtRoomType.put(dataType, rtRoomTypeInt);

                rtJson.put(roomTypeKey, rtRoomType);

                //预定未分房
                if (dataType == 4) {
                    noRoomList.add(obj);
                    bookNoRoomNum++;
                    continue;
                }

                switch (dataType) {
                    case 1:
                        checkInNum++;
                        break;
                    case 2:
                        repairdNum++;
                        break;
                    case 3:
                        bookRoomNum++;
                        break;
                }

                //每日的使用情况
                String roomUseDetailKey = roomNumId + date;
                roomUseDetail.put(roomUseDetailKey, dataType);

                String roomNumIdStr = roomNumId + "";
                String dataTypeStr = dataType + "";
                HashMap<String, JSONObject> rfms = drsm.get(roomNumId);
                if (rfms == null) {
                    rfms = new HashMap<>();
                }
                rfms.put(dataTypeStr, JSONObject.fromObject(obj.toString()));
                rfms.get(dataTypeStr).put("isEndMark", 0);
                // 如果当前时间和离店时间一直，则添加预计结束标示
                if (dateInt == endTimeInt) {
                    rfms.get(dataTypeStr).put("isEndMark", 1);
                }
                drsm.put(roomNumIdStr, rfms);

            }

            JSONObject dayNum = new JSONObject();
            dayNum.put("checkInNum", checkInNum);
            dayNum.put("repairdNum", repairdNum);
            dayNum.put("bookRoomNum", bookRoomNum);
            dayNum.put("bookNoRoomNum", bookNoRoomNum);
            dateNum.put(date, dayNum);


            dateRoomStateMsg.put(date, drsm);
            noRowRoomBookMsg.put(date, noRoomList);

        }


        Date upaCacheDate = new Date();
        resultMap.put("dateRoomStateMsg", dateRoomStateMsg);
        resultMap.put("noRowRoomBookMsg", noRowRoomBookMsg);
        resultMap.put("turnAlwaysDateNum", dateNum);
        resultMap.put("roomUseDetail", roomUseDetail);

        //酒店未来房情每日占用情况
        String drms = ECache.TURNALWAYS_DATEROOMSTATE + "_" + user.getHid();
        userCahe.put(ECache.TURNALWAYS_DATEROOMSTATE, drms, JSONObject.fromObject(dateRoomStateMsg).toString());

        //酒店未来房情预定未分房的数据
        String nrrb = ECache.TURNALWAYS_NOROWROOMBOOK + "_" + user.getHid();
        userCahe.put(ECache.TURNALWAYS_NOROWROOMBOOK, nrrb, JSONObject.fromObject(noRowRoomBookMsg).toString());

        //酒店未来房情预定每天的使用数据
        String tadn = ECache.TURNALWAYS_TURNALWATSDATENUM + "_" + user.getHid();
        userCahe.put(ECache.TURNALWAYS_TURNALWATSDATENUM, tadn, JSONObject.fromObject(dateNum).toString());

        // 每天的使用详情
        String tdsd = ECache.TURNALWAYS_DAYUSEDETAIL + "_" + user.getHid();
        userCahe.put(ECache.TURNALWAYS_DAYUSEDETAIL, tdsd, JSONObject.fromObject(roomUseDetail).toString());

        // 房型的使用详情
        String tdrt = ECache.TURNALWAYS_ROOMTYPE + "_" + user.getHid();
        userCahe.put(ECache.TURNALWAYS_ROOMTYPE, tdrt, JSONObject.fromObject(rtJson).toString());

    }


    /**
     * 更新缓存中每日房情的数据
     *
     * @param hid
     * @param resultMap
     * @throws Exception
     */
    public void turnAlwaysCacheForGroup(Integer hid, Map<String, Object> resultMap, HashOperations<String, Object, Object> userCahe) throws Exception {
        Date date1 = HotelUtils.addDayGetNewDate(new Date(), -2);
        int date2Int = HotelUtils.parseDate2Int(date1);
        // 最底层 展示信息，已状态为key，value 未最基础信息
        // 状态 1.入住信息  2.维修/停用 3.预定信息(已排房) 4.预定信息(未排房)
        Map<Integer, JSONObject> stateMsg = new HashMap<>();

        // 第二层 已房间id为key，value 为 stateMsg
        HashMap<Integer, HashMap<Integer, JSONObject>> roomForStateMsg = new HashMap<>();

        // 最外层 已日期为key，value 为 roomForStateMsg
        // 上面两个变量只为注释，并没有实际意义
        HashMap<String, HashMap<String, HashMap<String, JSONObject>>> dateRoomStateMsg = new HashMap<>();

        // 房间使用情况
        //   key  roomId+yyyy-MM-dd
        HashMap<String, Integer> roomUseDetail = new HashMap<>();

        // 每日使用数据明细
        JSONObject dateNum = new JSONObject();

        //将所有查询数据库的数据 处理后添加到当前集合
        List<JSONObject> baseData = new ArrayList<>();

        // 记录 最小的开始时间和 最大的结束时间
        // 已这两个时间为准则遍历，减少循环次数
        Date minStartTime = new Date();

        Date maxEndTime = new Date();

        Date dayRoomStartDate = new Date();

        // 1.查询所有在住信息
        //   在住信息的状态为 1
        RegistSearch registSearch = new RegistSearch();
        registSearch.setHid(hid);
        registSearch.setState(0);

        List<Regist> regists = registDao.selectBySearch(registSearch);

        JSONObject peoObj = new JSONObject();
        //查询在住人
        HashMap<String, Object> map = new HashMap<>();
        map.put("hid", hid);
        map.put("registState", 0);
        List<RegistPerson> registPeople = registPersonDao.searchCheckinPeople(map);
        Map<Integer, List<RegistPerson>> collect = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));

        for (Regist regist : regists) {
            Date checkinTime = regist.getCheckinTime();
            Date checkoutTime = regist.getCheckoutTime();


            JSONObject registObj = new JSONObject();
            registObj.put("registId", regist.getRegistId());
            registObj.put("roomNum", regist.getRoomNum());
            registObj.put("roomNumId", regist.getRoomNumId());
            registObj.put("resourceId", regist.getResourceId());
            registObj.put("resourceName", regist.getResourceName());
            registObj.put("registGroupId", regist.getRegistGroupId());
            registObj.put("roomTypeId", regist.getRoomTypeId());
            registObj.put("roomTypeName", regist.getRoomTypeName());
            if (null != collect.get(regist.getRegistId())) {
                JSONArray people = new JSONArray();
                for (RegistPerson registPerson : collect.get(regist.getRegistId())) {
                    peoObj = new JSONObject();
                    peoObj.put("personName", registPerson.getPersonName());
                    peoObj.put("registPersonId", registPerson.getRegistPersonId());
                    people.add(peoObj);
                }
                registObj.put("people", people);
            }
            baseData = dateBaseHandler(1, baseData, regist.getRoomNumId(), checkinTime, checkoutTime, minStartTime, maxEndTime, registObj);
        }

        // 2.查询当前有效的维修信息
        RoomRepairRecordHistorySearch roomRepairRecordSearch = new RoomRepairRecordHistorySearch();
        roomRepairRecordSearch.setHid(hid);
        roomRepairRecordSearch.setState(0);
        List<RoomRepairRecordHistory> roomRepairRecords = roomRepairRecordHistoryDao.selectBySearch(roomRepairRecordSearch);

        for (RoomRepairRecordHistory roomRepairRecord : roomRepairRecords) {

            Date begintime = roomRepairRecord.getBegintime();
            Date endtime = roomRepairRecord.getEndtime();

            JSONObject registObj = new JSONObject();
            registObj.put("repairCheckRoomRecordId", roomRepairRecord.getRepairCheckRoomRecordId());
            registObj.put("des", roomRepairRecord.getDes());
            registObj.put("roomNum", roomRepairRecord.getRoomNum());
            registObj.put("roomTypeId", roomRepairRecord.getRoomTypeId());
            registObj.put("roomNumId", roomRepairRecord.getRoomId());
            baseData = dateBaseHandler(2, baseData, roomRepairRecord.getRoomId(), begintime, endtime, minStartTime, maxEndTime, registObj);

        }

        // 3.查询当前预定信息
        BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
        bookingOrderRoomNumSearch.setHid(hid);
        bookingOrderRoomNumSearch.setOrderState(BOOK.STA_YX);
        bookingOrderRoomNumSearch.setIsCheckin(0);

        List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

        // 对预定信息进行分组
        //  已排房/未排房

        // 未排房信息
        HashMap<String, JSONArray> noRowRoomBookMsg = new HashMap<>();

        for (BookingOrderRoomNum bookingOrderRoomNum : bookingOrderRoomNums) {
            Date checkinTime = bookingOrderRoomNum.getCheckinTime();
            Date checkoutTime = bookingOrderRoomNum.getCheckoutTime();

            // 0 未分房  1 已分房
            Integer rowRoom = bookingOrderRoomNum.getRowRoom();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("bookingOrderId", bookingOrderRoomNum.getBookingOrderId());
            jsonObject.put("roomNumId", bookingOrderRoomNum.getRoomNumId());
            jsonObject.put("roomNum", bookingOrderRoomNum.getRoomNum());
            jsonObject.put("id", bookingOrderRoomNum.getId());
            jsonObject.put("roomTypeId", bookingOrderRoomNum.getRoomTypeId());
            int type = 3;

            if (rowRoom == 0) {
                jsonObject.put("roomNumId", 0);
                type = 4;
            }

            baseData = dateBaseHandler(type, baseData, bookingOrderRoomNum.getRoomNumId(), checkinTime, checkoutTime, minStartTime, maxEndTime, jsonObject);

        }


        Date bianliDate = new Date();
        // 4.对已经处理的baseData做处理
        // 如果遍历的最早时间，小于当前时间减 2 天，则最小遍历时间 未 前天
        Integer minInt = HotelUtils.parseDate2Int(minStartTime);
        if (minInt < date2Int) {
            minStartTime = date1;
        }
        // 对最大最小时间差做分组
        List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(minStartTime).substring(0, 10), HotelUtils.getDay(HotelUtils.parseDate2Str(maxEndTime), 1));

        HashMap<String, JSONObject> rtJson = new HashMap<>();

        for (String date : allDayListBetweenDate) {

            //日期格式 int类型  yyyyMMdd
            int dateInt = Integer.parseInt(date.replace("-", ""));

            // 每日房情 ，精确到房间
            HashMap<String, HashMap<String, JSONObject>> drsm = dateRoomStateMsg.get(date);
            if (drsm == null) {
                drsm = new HashMap<String, HashMap<String, JSONObject>>();
            }

            JSONArray noRoomList = noRowRoomBookMsg.get(date);
            if (noRoomList == null) {
                noRoomList = new JSONArray();
            }

            int checkInNum = 0;     //入住数
            int repairdNum = 0;     //维修数
            int bookRoomNum = 0;   //预定数
            int bookNoRoomNum = 0;  //预定未分房数

            for (JSONObject obj : baseData) {
                // 开始时间
                // 如果当前时间 小于 数据的开始时间，则进行下一条数据
                int beginTimeInt = obj.getInt("beginTimeInt");
                if (dateInt < beginTimeInt) {
                    continue;
                }

                // 结束时间
                // 如果结束时间 大于 数据的结束时间，则进行下一条数据
                int endTimeInt = obj.getInt("endTimeInt");
                if (dateInt >= endTimeInt) {
                    continue;
                }

                int roomNumId = 0;
                try {
                    roomNumId = obj.getInt("roomNumId");
                } catch (Exception e) {
                    log.info("obj={}", obj.toString());
                }
                int dataType = obj.getInt("dataType");

                if (obj.get("roomTypeId") == null) {
                    log.info("obj={}", obj.toString());
                }

                int roomTypeId = obj.getInt("roomTypeId");

                // 房型key
                String roomTypeKey = dateInt + "" + roomTypeId;
                JSONObject rtRoomType = rtJson.get(roomTypeKey);
                if (rtRoomType == null) {
                    rtRoomType = new JSONObject();
                }
                Integer rtRoomTypeInt = 0;
                Object rtRoomTypeIntObj = rtRoomType.get(dataType + "");
                if (rtRoomTypeIntObj != null) {
                    rtRoomTypeInt = Integer.parseInt(rtRoomTypeIntObj.toString());
                }

                rtRoomTypeInt++;
                rtRoomType.put(dataType, rtRoomTypeInt);

                rtJson.put(roomTypeKey, rtRoomType);

                //预定未分房
                if (dataType == 4) {
                    noRoomList.add(obj);
                    bookNoRoomNum++;
                    continue;
                }

                switch (dataType) {
                    case 1:
                        checkInNum++;
                        break;
                    case 2:
                        repairdNum++;
                        break;
                    case 3:
                        bookRoomNum++;
                        break;
                }

                //每日的使用情况
                String roomUseDetailKey = roomNumId + date;
                roomUseDetail.put(roomUseDetailKey, dataType);

                String roomNumIdStr = roomNumId + "";
                String dataTypeStr = dataType + "";
                HashMap<String, JSONObject> rfms = drsm.get(roomNumId);
                if (rfms == null) {
                    rfms = new HashMap<>();
                }
                rfms.put(dataTypeStr, JSONObject.fromObject(obj.toString()));
                rfms.get(dataTypeStr).put("isEndMark", 0);
                // 如果当前时间和离店时间一直，则添加预计结束标示
                if (dateInt == endTimeInt) {
                    rfms.get(dataTypeStr).put("isEndMark", 1);
                }
                drsm.put(roomNumIdStr, rfms);

            }

            JSONObject dayNum = new JSONObject();
            dayNum.put("checkInNum", checkInNum);
            dayNum.put("repairdNum", repairdNum);
            dayNum.put("bookRoomNum", bookRoomNum);
            dayNum.put("bookNoRoomNum", bookNoRoomNum);
            dateNum.put(date, dayNum);


            dateRoomStateMsg.put(date, drsm);
            noRowRoomBookMsg.put(date, noRoomList);

        }


        Date upaCacheDate = new Date();
        resultMap.put("dateRoomStateMsg", dateRoomStateMsg);
        resultMap.put("noRowRoomBookMsg", noRowRoomBookMsg);
        resultMap.put("turnAlwaysDateNum", dateNum);
        resultMap.put("roomUseDetail", roomUseDetail);

        String drms = ECache.TURNALWAYS_DATEROOMSTATE + "_" + hid;
        userCahe.put(ECache.TURNALWAYS_DATEROOMSTATE, drms, JSONObject.fromObject(dateRoomStateMsg).toString());

        String nrrb = ECache.TURNALWAYS_NOROWROOMBOOK + "_" + hid;
        userCahe.put(ECache.TURNALWAYS_NOROWROOMBOOK, nrrb, JSONObject.fromObject(noRowRoomBookMsg).toString());

        String tadn = ECache.TURNALWAYS_TURNALWATSDATENUM + "_" + hid;
        userCahe.put(ECache.TURNALWAYS_TURNALWATSDATENUM, tadn, JSONObject.fromObject(dateNum).toString());

        // 每天的使用详情
        String tdsd = ECache.TURNALWAYS_DAYUSEDETAIL + "_" + hid;
        userCahe.put(ECache.TURNALWAYS_DAYUSEDETAIL, tdsd, JSONObject.fromObject(roomUseDetail).toString());

        // 房型的使用详情
        String tdrt = ECache.TURNALWAYS_ROOMTYPE + "_" + hid;
        userCahe.put(ECache.TURNALWAYS_ROOMTYPE, tdrt, JSONObject.fromObject(rtJson).toString());

    }

    /**
     * 查询日期段之内的可用的房间信息
     * type
     * 1 可用房间 9 可查房房间 10 入住房间
     *
     * @param param
     * @return
     */
    @Override
    public List<RoomInfoResult> canUseRoom(TbUserSession user, JSONObject param) throws Exception {

        // 查询类型
        int type = param.getInt("type");

        // 2.查询当前所有的房间
        RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
        roomInfoSearch.setHid(user.getHid());
        roomInfoSearch.setState(1);

        // 房型id
        Object roomTypeIdObj = param.get("roomTypeId");
        if (roomTypeIdObj != null) {
            roomInfoSearch.setRoomTypeId(Integer.parseInt(roomTypeIdObj.toString()));
        }

        List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
        //9 可查房房间 10 入住房间
        if (type == 9) {
            //查询可查房房间
            RoomCheckRecordSearch roomCheckRecordSearch = new RoomCheckRecordSearch();
            roomCheckRecordSearch.setHid(user.getHid());
            roomCheckRecordSearch.setHotelGroupId(user.getHotelGroupId());
            List<RoomCheckRecord> list = roomCheckRecordDao.selectEndCheckRoom(roomCheckRecordSearch);
            List<Integer> endCheckRoomIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(list)) {
                endCheckRoomIds = list.stream().map(RoomCheckRecord::getRoomInfoId).collect(Collectors.toList());
            }
            List<Integer> finalEndCheckRoomIds = endCheckRoomIds;
            return roomInfos
                    .stream()
                    .filter(roomInfo -> (3 == roomInfo.getRoomNumState() || 4 == roomInfo.getRoomNumState()) && !finalEndCheckRoomIds.contains(roomInfo.getRoomInfoId()))
                    .map(roomInfo -> {
                        RoomInfoResult roomInfoResult = new RoomInfoResult();
                        BeanUtils.copyProperties(roomInfo, roomInfoResult);
                        return roomInfoResult;
                    }).collect(Collectors.toList());
        } else if (type == 10) {
            //查询在住人房间
            HashMap<String, Object> map = new HashMap<>();
            map.put("hid", user.getHid());
            map.put("registState", 0);
            List<RegistPerson> registPeople = registPersonDao.searchCheckinPeople(map);
            if (CollectionUtils.isEmpty(registPeople)) {
                log.warn("未查询到在住单");
                return new ArrayList<>();
            }
            Map<Integer, List<RegistPerson>> collect = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRoomNumId));
            List<RoomInfo> list = roomInfoDao.findByIds(new ArrayList<>(collect.keySet()));
            return list.stream().map(roomInfo -> {
                RoomInfoResult roomInfoResult = new RoomInfoResult();
                BeanUtils.copyProperties(roomInfo, roomInfoResult);
                List<RegistPerson> registPersonList = collect.get(roomInfo.getRoomInfoId());
                roomInfoResult.setPersonName(String.join("、", registPersonList.stream().map(RegistPerson::getPersonName).collect(Collectors.toList())));
                roomInfoResult.setRegistId(registPersonList.get(0).getRegistId());
                return roomInfoResult;
            }).collect(Collectors.toList());
        }else if(type == 11){
            return roomInfos
                    .stream()
                    .filter(roomInfo -> 5!= roomInfo.getRoomNumState() && 6 != roomInfo.getRoomNumState())
                    .map(roomInfo -> {
                        RoomInfoResult roomInfoResult = new RoomInfoResult();
                        BeanUtils.copyProperties(roomInfo, roomInfoResult);
                        return roomInfoResult;
                    }).collect(Collectors.toList());
        }
        // 1 可用房间
        if (type == 1) {
            roomInfoSearch.setRoomNumState(1);
        }
        // 可用的房间
        ArrayList<RoomInfoResult> availableRoom = new ArrayList<>();
        // 3.查询缓存里每天的房间使用情况
        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
        String tdsd = ECache.TURNALWAYS_DAYUSEDETAIL + "_" + user.getHid();
        Object tdsdStr = userCahe.get(ECache.TURNALWAYS_DAYUSEDETAIL, tdsd);

        JSONObject tdsdObj = new JSONObject();


        if (tdsdStr == null || "{}".equals(tdsdStr.toString())) {

            HashMap<String, Object> map = new HashMap<>();
            turnAlwaysCache(user, map, userCahe);
            tdsdObj = JSONObject.fromObject(map.get("roomUseDetail"));
        } else {
            tdsdObj = JSONObject.fromObject(tdsdStr);

        }

        // 4.获取当前 开始时间 结束时间
        String startTime = param.getString("startTime");
        String endTime = param.getString("endTime");

        List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(startTime, endTime);

        // 5.遍历所有的房间信息
        // 每天的使用情况，key=房型id+日期，value=可用房间
        HashMap<String, Integer> dayNum = new HashMap<>();
        String key = "";
        boolean availability = true;
        for (RoomInfo roomInfo : roomInfos) {

            Integer roomInfoId = roomInfo.getRoomInfoId();

            availability = true;

            for (int i = 0; i < allDayListBetweenDate.size(); i++) {
                String date = allDayListBetweenDate.get(i);
                key = roomInfoId + date;
                availability = tdsdObj.get(key) == null;
                if (!availability) {
                    break;
                }

            }
            //todo 待确认，排除掉房态为锁房的数据
//            if(roomInfo.getRoomNumState() == ROOM_STATUS.OS){
//                availability = false;
//            }

            if (availability) {
                RoomInfoResult roomInfoResult = new RoomInfoResult();
                BeanUtils.copyProperties(roomInfo, roomInfoResult);
                availableRoom.add(roomInfoResult);

            }
        }

        return availableRoom;

    }

    private List<RoomInfoResult> PlannedRooms(TbUserSession user, JSONObject param, List<Integer> selectedRoomIds) throws Exception {

        // 查询类型
        int type = param.getInt("type");

        // 2.查询当前所有的房间
        RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
        roomInfoSearch.setHid(user.getHid());
        roomInfoSearch.setState(1);

        // 房型id
        Object roomTypeIdObj = param.get("roomTypeId");
        if (roomTypeIdObj != null) {
            roomInfoSearch.setRoomTypeId(Integer.parseInt(roomTypeIdObj.toString()));
        }
        roomInfoSearch.setRoomInfoIds(selectedRoomIds);

        List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
        if(type == 9){
            //查询可查房房间
            RoomCheckRecordSearch roomCheckRecordSearch = new RoomCheckRecordSearch();
            roomCheckRecordSearch.setHid(user.getHid());
            roomCheckRecordSearch.setHotelGroupId(user.getHotelGroupId());
            List<RoomCheckRecord> list = roomCheckRecordDao.selectEndCheckRoom(roomCheckRecordSearch);
            List<Integer> endCheckRoomIds = new ArrayList<>();
            if(!CollectionUtils.isEmpty(list)){
                endCheckRoomIds = list.stream().map(RoomCheckRecord::getRoomInfoId).collect(Collectors.toList());
            }
            List<Integer> finalEndCheckRoomIds = endCheckRoomIds;
            return roomInfos
                    .stream()
                    .filter(roomInfo -> (3 == roomInfo.getRoomNumState() || 4 == roomInfo.getRoomNumState()) && !finalEndCheckRoomIds.contains(roomInfo.getRoomInfoId()))
                    .map(roomInfo -> {
                        RoomInfoResult roomInfoResult = new RoomInfoResult();
                        BeanUtils.copyProperties(roomInfo,roomInfoResult);
                        return roomInfoResult;
                    }).collect(Collectors.toList());
        }else if(type ==10){
            //查询在住人房间
            HashMap<String, Object> map = new HashMap<>();
            map.put("hid", user.getHid());
            map.put("registState", 0);
            List<RegistPerson> registPeople = registPersonDao.searchCheckinPeople(map);
            if(CollectionUtils.isEmpty(registPeople)){
                log.warn("未查询到在住单");
                return new ArrayList<>();
            }
            Map<Integer, List<RegistPerson>> collect = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRoomNumId));
            List<RoomInfo> list = roomInfoDao.findByIds(new ArrayList<>(collect.keySet()));
            return list.stream().map(roomInfo -> {
                RoomInfoResult roomInfoResult = new RoomInfoResult();
                BeanUtils.copyProperties(roomInfo,roomInfoResult);
                List<RegistPerson> registPersonList = collect.get(roomInfo.getRoomInfoId());
                roomInfoResult.setPersonName(String.join("、", registPersonList.stream().map(RegistPerson::getPersonName).collect(Collectors.toList())));
                roomInfoResult.setRegistId(registPersonList.get(0).getRegistId());
                return roomInfoResult;
            }).collect(Collectors.toList());
        }
// 1 可用房间 9 可查房房间 10 入住房间
        if (type == 1) {
            roomInfoSearch.setRoomNumState(1);
        }

        // 可用的房间
        ArrayList<RoomInfoResult> plannedRoomList = new ArrayList<>();
        if (null != roomInfos && roomInfos.size() > 0){
            for (RoomInfo roomInfo : roomInfos) {
                RoomInfoResult roomInfoResult = new RoomInfoResult();
                BeanUtils.copyProperties(roomInfo,roomInfoResult);
                plannedRoomList.add(roomInfoResult);
            }
        }


        return plannedRoomList;

    }


    public List<RoomInfo> canUseRoomForGroup(Integer hid, JSONObject param) throws Exception {

        // 查询类型
        int type = param.getInt("type");

        List<RoomInfo> roomInfos = new ArrayList<>();


        // 2.查询当前所有的房间
        RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
        roomInfoSearch.setHid(hid);
        roomInfoSearch.setState(1);
        if (type == 1) {
            roomInfoSearch.setRoomNumState(1);
        }

        // 房型id
        Object roomTypeIdObj = param.get("roomTypeId");
        if (roomTypeIdObj != null) {
            roomInfoSearch.setRoomTypeId(Integer.parseInt(roomTypeIdObj.toString()));
        }

        roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);


        // 可用的房间
        ArrayList<RoomInfo> availableRoom = new ArrayList<>();

        // 3.查询缓存里每天的房间使用情况
        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
        String tdsd = ECache.TURNALWAYS_DAYUSEDETAIL + "_" + hid;
        Object tdsdStr = userCahe.get(ECache.TURNALWAYS_DAYUSEDETAIL, tdsd);

        JSONObject tdsdObj = new JSONObject();


        if (tdsdStr == null || "{}".equals(tdsdStr.toString())) {

            HashMap<String, Object> map = new HashMap<>();
            turnAlwaysCacheForGroup(hid, map, userCahe);
            tdsdObj = JSONObject.fromObject(map.get("roomUseDetail"));
        } else {
            tdsdObj = JSONObject.fromObject(tdsdStr);

        }

        // 4.获取当前 开始时间 结束时间
        String startTime = param.getString("startTime");
        String endTime = param.getString("endTime");

        List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(startTime, endTime);

        // 5.遍历所有的房间信息
        // 每天的使用情况，key=房型id+日期，value=可用房间
        HashMap<String, Integer> dayNum = new HashMap<>();
        String key = "";
        boolean availability = true;
        for (RoomInfo roomInfo : roomInfos) {

            Integer roomInfoId = roomInfo.getRoomInfoId();

            availability = true;

            for (int i = 0; i < allDayListBetweenDate.size(); i++) {
                String date = allDayListBetweenDate.get(i);
                key = roomInfoId + date;
                availability = tdsdObj.get(key) == null;
                if (!availability) {
                    break;
                }

            }

            if (availability) {

                availableRoom.add(roomInfo);

            }
        }

        return availableRoom;

    }

    @Override
    public ResponseData findRoomTypeUseData(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            TbUserSession user = getTbUserSession(sessionToken);

            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();

            // 房型的使用详情
            String tdrt = ECache.TURNALWAYS_ROOMTYPE + "_" + user.getHid();
            Object o = userCahe.get(ECache.TURNALWAYS_ROOMTYPE, tdrt);

            if (o == null) {
                this.turnAlwaysCacheFunc(user);
                o = userCahe.get(ECache.TURNALWAYS_ROOMTYPE, tdrt);
            }

            JSONObject jsonObject = JSONObject.fromObject(o);
            String s = ECache.TURNALWAYS_TURNALWATSDATENUM + "_" + user.getHid();

            Object o1 = userCahe.get(ECache.TURNALWAYS_TURNALWATSDATENUM, s);

            JSONObject tadn = JSONObject.fromObject(o1);

            Set<String> keys = tadn.keySet();

            JSONObject jsonObject1 = new JSONObject();

            for (String key : keys) {

                JSONObject tor = tadn.getJSONObject(key);
                jsonObject1 = new JSONObject();

                jsonObject1.put("1", tor.get("checkInNum"));
                jsonObject1.put("2", tor.get("repairdNum"));
                jsonObject1.put("3", tor.get("bookRoomNum"));
                jsonObject1.put("4", tor.get("bookNoRoomNum"));

                jsonObject.put(key.replace("-", ""), jsonObject1);

            }

            responseData.setData(jsonObject);

        } catch (Exception e) {
            log.error("", e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    /**
     * 验证某个时段的可用房间
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> findavailableRoom(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if (user.getSessionType() == 3) {
                user.setHid(param.getInt("hid"));
            }
            int type = param.getInt("type");
            if (type == 9 || type == 10 || type == 11) {
                resultMap.put("list", this.canUseRoom(user, param));
                resultMap.put("canUseRoomMap", new HashMap<>());
                resultMap.put("trrb", new JSONObject());
                resultMap.put("canUseRoomNum", 0);
                return resultMap;
            }
            String startTime = param.getString("startTime").substring(0, 10);
            String endTime = param.getString("endTime").substring(0, 10);

            Date startDate = HotelUtils.parseStr2Date(startTime + " 00:00:00");

            Date date1 = new Date();
            Integer integer1 = HotelUtils.parseDate2Int(date1);
            Integer integer2 = HotelUtils.parseDate2Int(startDate);

            // 如果日期一致  且小于3点 未来房情开始时间减一天。
            if (integer1.equals(integer2)) {
                Calendar cal = Calendar.getInstance();
                int hours = cal.get(Calendar.HOUR_OF_DAY);
                if (hours < 3) {
                    startDate = HotelUtils.addDayGetNewDate(startDate, -1);
                }

            }


            Date endDate = HotelUtils.parseStr2Date(endTime + " 00:00:00");

            // 是否删除原预订数量
            Boolean addBookNumsTag = false;

            Map<Integer, List<BookingOrderRoomNum>> bookRoomNum = new HashMap<>();

            Object bookId = param.get("bookingOrderId");
            if (bookId != null && !"".equals(bookId.toString()) && !"0".equals(bookId.toString())) {
                BookingOrder bookingOrder = bookingOrderDao.selectById(Integer.parseInt(bookId.toString()));
                if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())) {
                    throw new Exception("预订编号有误");
                }

                Date checkinTime = bookingOrder.getCheckinTime();
                Date checkoutTime = bookingOrder.getCheckoutTime();


                // 预订的开始、结束时间 包含在预订时间内，则把当前订单的信息加到房情中
                if (checkinTime.getTime() >= startDate.getTime() && checkinTime.getTime() <= endDate.getTime() || checkoutTime.getTime() >= startDate.getTime() && checkoutTime.getTime() <= endDate.getTime()) {

                    BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                    bookingOrderRoomNumSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
                    bookingOrderRoomNumSearch.setOrderState(1);
                    bookingOrderRoomNumSearch.setIsCheckin(0);

                    List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

                    bookRoomNum = bookingOrderRoomNums.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getRoomTypeId));

                    addBookNumsTag = true;

                }

            }

            // 1.获取可用房间

            List<RoomInfoResult> roomInfos = this.canUseRoom(user, param);

            Map<Integer, List<RoomInfo>> canUserRoomMap = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomTypeId));


            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(startTime, endTime);
            // 获取缓存中预定未分房的数据
            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            String trrb = ECache.TURNALWAYS_NOROWROOMBOOK + "_" + user.getHid();
            Object trrbStr = userCahe.get(ECache.TURNALWAYS_NOROWROOMBOOK, trrb);

            // 对房间按照房间号中的数字的大小进行排序
            // 自定义排序逻辑，提取 RoomNum 中的数字并比较
            roomInfos.sort((r1, r2) -> {
                String roomNum1 = r1.getRoomNum();
                String roomNum2 = r2.getRoomNum();

                // 提取数字部分
                Integer num1 = extractNumber(roomNum1);
                Integer num2 = extractNumber(roomNum2);

                // 如果都不存在数字，默认按字符串比较
                if (num1 == null && num2 == null) {
                    return roomNum1.compareToIgnoreCase(roomNum2);
                }

                // 否则按照数字大小排序
                return Integer.compare(num1 != null ? num1 : 0, num2 != null ? num2 : 0);
            });

            resultMap.put("list", roomInfos);
            resultMap.put("canUseRoomMap", canUserRoomMap);


            // 返回已排房间的房间列表，用于前端对已选房间进行排序
            if(param.containsKey("selectedRoomIds") && null != param.getJSONArray("selectedRoomIds")
                    && !param.getJSONArray("selectedRoomIds").isEmpty()){
                JSONArray selectedRoomIdsArray = param.getJSONArray("selectedRoomIds");
                List<Integer> selectedRoomIds = new ArrayList<>();
                for (int i = 0; i < selectedRoomIdsArray.size(); i++) {
                    selectedRoomIds.add(selectedRoomIdsArray.getInt(i));
                }
                List<RoomInfoResult> selectedRoomIdsRoomInfos = this.PlannedRooms(user, param, selectedRoomIds);
                if(null != selectedRoomIdsRoomInfos && !selectedRoomIdsRoomInfos.isEmpty()){
                    selectedRoomIdsRoomInfos.addAll(roomInfos);
                }
                resultMap.put("selectedRoomIdsRoomInfos", selectedRoomIdsRoomInfos);
            }


            // 2.对预定未分房数做处理

            // 查询每个房型的可用数量
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            List<RoomInfoNum> roomNumByRoomType = roomInfoDao.findRoomNumByRoomType(roomInfoSearch);
            //这里查询可用房间数量排除锁房状态
//            List<RoomInfoNum> roomNumByRoomType = roomInfoDao.findRoomNumByRoomTypeWithoutLock(roomInfoSearch);


            // 预订未排房
            JSONObject trrbObj = JSONObject.fromObject(trrbStr);

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setOrderState(1);
            bookingOrderRoomNumSearch.setIsCheckin(0);
            bookingOrderRoomNumSearch.setHid(user.getHid());
            List<Long> checkinTime = new ArrayList<>();
            checkinTime.add(startDate.getTime() / 1000);
            checkinTime.add(endDate.getTime() / 1000 - 5000);
            bookingOrderRoomNumSearch.setCheckinTime(checkinTime);
            List<BookingOrderRoomNum> bookingOrderRoomNums2 = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            Map<Integer, List<BookingOrderRoomNum>> collect = bookingOrderRoomNums2.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getRoomTypeId));
            // 每个房型在查询期间的最大使用数量
            HashMap<Integer, Integer> maxUseNum = new HashMap<>();

            // 数据汇总
            Map<Object, Object> dateNumSummary = new HashMap<>();
            JSONObject trrbJson = new JSONObject();

            for (RoomInfoNum m : roomNumByRoomType) {
                Integer roomTypeId = m.getRoomTypeId();
                maxUseNum.put(roomTypeId, 0);
                trrbJson.put(roomTypeId, 0);
                List<BookingOrderRoomNum> bookingOrderRoomNums1 = collect.get(roomTypeId);
                if (bookingOrderRoomNums1 != null) {
                    trrbJson.put(roomTypeId, bookingOrderRoomNums1.size());
                }
            }
            // 房型的使用详情
            String tdrt = ECache.TURNALWAYS_ROOMTYPE + "_" + user.getHid();
            Object o = userCahe.get(ECache.TURNALWAYS_ROOMTYPE, tdrt);

            JSONObject jsonObject = JSONObject.fromObject(o);

            String s = ECache.TURNALWAYS_TURNALWATSDATENUM + "_" + user.getHid();

            Object o1 = userCahe.get(ECache.TURNALWAYS_TURNALWATSDATENUM, s);

            JSONObject tadn = JSONObject.fromObject(o);


            for (String date : allDayListBetweenDate) {

                String replace = date.replace("-", "");
                for (RoomInfoNum m : roomNumByRoomType) {

                    Integer roomTypeId = m.getRoomTypeId();

                    String dk = replace + roomTypeId;
                    Object o2 = tadn.get(dk);
                    if (o2 == null) {
                        continue;
                    }
                    JSONObject jsonObject1 = tadn.getJSONObject(dk);

                    Set<String> set = jsonObject1.keySet();

                    int maxNum = 0;

                    for (String dkey : set) {

                        int anInt = jsonObject1.getInt(dkey);
                        maxNum += anInt;

                    }
                    Integer integer = maxUseNum.get(roomTypeId);
                    if (integer == null) {
                        maxUseNum.put(roomTypeId, maxNum);
                        continue;
                    }
                    if (integer < maxNum) {
                        maxUseNum.put(roomTypeId, maxNum);
                    }

                }

            }


            Set<Integer> integers = maxUseNum.keySet();
            if (addBookNumsTag) {

                if (integers.size() < 1) {
                    Set<Integer> integers1 = bookRoomNum.keySet();
                    for (Integer is : integers1) {
                        List<BookingOrderRoomNum> bookingOrderRoomNums = bookRoomNum.get(is);
                        maxUseNum.put(is, 0 - bookingOrderRoomNums.size());
                    }
                } else {
                    for (Integer is : integers) {
                        List<BookingOrderRoomNum> bookingOrderRoomNums = bookRoomNum.get(is);
                        Integer integer = maxUseNum.get(is);
                        if (bookingOrderRoomNums != null) {
                            maxUseNum.put(is, integer - bookingOrderRoomNums.size());
                        }
                    }

                }
            }

            // 可用数量
            HashMap<Integer, Integer> canUseRoomNum = new HashMap<>();

            for (RoomInfoNum m : roomNumByRoomType) {
                canUseRoomNum.put(m.getRoomTypeId(), m.getSumCount() - maxUseNum.get(m.getRoomTypeId()));
            }

            resultMap.put("trrb", trrbJson);
            resultMap.put("canUseRoomNum", canUseRoomNum);

        } catch (Exception e) {
            log.error("", e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }


    /**
     * 查询指定日期范围酒店的房态信息，包含如下内容,OTA传过来的信息
     * 1、可用房间信息（在日期范围内，每天都满足预定的房间信息）
     * 2、每日房间使用信息（在日期范围内，每天房间使用信息，为map，key为时间YYYYMMDD+房型Id，value为{"canUseNum":"","overSaleNum"}）
     * 返回信息如下:{"canUseRoomMap":[],"tdrt":[],list}
     * param:{"startTime":"","endTime":"","roomTypeId":"","bookingOrderId":""}
     */
    public Map<String, Object> findAvailableRoomByOta(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            // 解析参数中的会话令牌
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if (user.getSessionType() == 3) {
                user.setHid(param.getInt("hid"));
            }
            //统一将param中type变为2，查询可用房间信息，以及房型每日的使用情况
            param.put("type", 2);
            //1、解析开始和结束时间
            String startTime = param.getString("startTime").substring(0, 10);
            Date startDate = HotelUtils.parseStr2Date(startTime + " 00:00:00");
            Integer startDateInt = HotelUtils.parseDate2Int(startDate);
            Date nowDate = new Date();
            Integer nowDateInt = HotelUtils.parseDate2Int(nowDate);
            // 如果日期一致  且小于3点 未来房情开始时间减一天。
            if (nowDateInt.equals(startDateInt)) {
                Calendar cal = Calendar.getInstance();
                int hours = cal.get(Calendar.HOUR_OF_DAY);
                if (hours < 3) {
                    startDate = HotelUtils.addDayGetNewDate(startDate, -1);
                }
            }
            String endTime = param.getString("endTime").substring(0, 10);
            Date endDate = HotelUtils.parseStr2Date(endTime + " 00:00:00");

            //2、原订单处理
            //是否删除原预订数量
            Boolean addBookNumsTag = false;
            // 初始化用于存储预订房间数量的映射
            Map<Integer, List<BookingOrderRoomNum>> bookRoomNum = new HashMap<>();
            // 处理预订订单ID参数
            Object bookId = param.get("bookingOrderId");
            if (bookId != null && !"".equals(bookId.toString()) && !"0".equals(bookId.toString())) {
                BookingOrder bookingOrder = bookingOrderDao.selectById(Integer.parseInt(bookId.toString()));
                if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())) {
                    throw new Exception("预订编号有误");
                }
                Date checkinTime = bookingOrder.getCheckinTime();
                Date checkoutTime = bookingOrder.getCheckoutTime();
                // 预订的开始、结束时间 包含在预订时间内，则把当前订单的信息加到房情中
                if (checkinTime.getTime() >= startDate.getTime()
                        && checkinTime.getTime() <= endDate.getTime()
                        || checkoutTime.getTime() >= startDate.getTime()
                        && checkoutTime.getTime() <= endDate.getTime()
                ) {
                    //查询房间预定状态表中，当前订单Id中状态为有效且入住状态为未入住的订单信息
                    BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                    bookingOrderRoomNumSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
                    bookingOrderRoomNumSearch.setOrderState(1);
                    bookingOrderRoomNumSearch.setIsCheckin(0);
                    List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
                    bookRoomNum = bookingOrderRoomNums.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getRoomTypeId));
                    //将是否删除原订单数量标记为true
                    addBookNumsTag = true;
                }
            }

            //3、获取可用房间，这里的可用房间指的是在时间周期中，每天均可用的房间（去除预定分房，维修，入住的房间）
            List<RoomInfoResult> roomInfos = this.canUseRoom(user, param);
            //按照房型将可用房间分组放入list中
            Map<Integer, List<RoomInfo>> canUserRoomMap = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomTypeId));
            resultMap.put("canUseRoomMap",canUserRoomMap);
            //2.查询每个房型的可用数量
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            List<RoomInfoNum> roomNumByRoomType = roomInfoDao.findRoomNumByRoomType(roomInfoSearch);
            //这里查询可用房间数量排除锁房状态
//            List<RoomInfoNum> roomNumByRoomType = roomInfoDao.findRoomNumByRoomTypeWithoutLock(roomInfoSearch);

            //如果入参中有房型Id,则过滤出该房型Id的可用数量信息
            if( !"".equals(param.get("roomTypeId")) && null !=param.get("roomTypeId")){
                roomNumByRoomType = roomNumByRoomType.stream().filter(m -> m.getRoomTypeId()
                                .equals(Integer.parseInt(param.get("roomTypeId")
                                .toString()))).collect(Collectors.toList());
            }
            //初始化每天的最大使用数量
            HashMap<Integer, Integer> maxUseNumMap = new HashMap<>();
            for (RoomInfoNum m : roomNumByRoomType) {
                Integer roomTypeId = m.getRoomTypeId();
                maxUseNumMap.put(roomTypeId, 0);
            }
            //缓存查询初始化
            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            //获取酒店未来房情 房型每天的使用情况,key为日期YYYYMMDD+房型Id,value为以下每个状态的数量 "202507012354": {"4": 1}
            // 状态 1.入住信息  2.维修/停用 3.预定信息(已排房) 4.预定信息(未排房)
            String tdrt = ECache.TURNALWAYS_ROOMTYPE + "_" + user.getHid();
            Object tdrtObject = userCahe.get(ECache.TURNALWAYS_ROOMTYPE, tdrt);
            JSONObject tdrtJsonObject = JSONObject.fromObject(tdrtObject);
            //3.获取请求时间周期内的所有天数
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(startTime, endTime);
            //定义房型在时间周期内的每日使用情况
            JSONObject roomTypeUsage = new JSONObject();
            for (String date : allDayListBetweenDate) {
                String dateYYYYMMDD  = date.replace("-", "");
                //遍历房型信息
                for (RoomInfoNum m : roomNumByRoomType) {
                    //房型每日使用情况
                    JSONObject roomTypeDayUseDetail = new JSONObject();
                    Integer roomTypeId = m.getRoomTypeId();
                    String dk = dateYYYYMMDD + roomTypeId;
                    Object o = tdrtJsonObject.get(dk);
                    if (o == null) {
                        continue;
                    }
                    JSONObject roomTypeTdrt = tdrtJsonObject.getJSONObject(dk);
                    List<String> set = BC_ROOM_STATUS.getAllState();
                    int maxNum = 0;
                    for (String dkey : set) {
                        if (roomTypeTdrt.get(dkey) == null) {
                            roomTypeDayUseDetail.put(dk, 0);
                            continue;
                        }else {
                            int anInt = roomTypeTdrt.getInt(dkey);
                            maxNum += anInt;
                            roomTypeDayUseDetail.put(dk, anInt);
                        }
                    }
                    Integer integer = maxUseNumMap.get(roomTypeId);
                    if (integer == null) {
                        maxUseNumMap.put(roomTypeId, maxNum);
                        continue;
                    }
                    if (integer < maxNum) {
                        maxUseNumMap.put(roomTypeId, maxNum);
                    }

                }
            }

            Set<Integer> integers = maxUseNumMap.keySet();
            if (addBookNumsTag) {
                if (integers.size() < 1) {
                    Set<Integer> integers1 = bookRoomNum.keySet();
                    for (Integer is : integers1) {
                        List<BookingOrderRoomNum> bookingOrderRoomNums = bookRoomNum.get(is);
                        maxUseNumMap.put(is, 0 - bookingOrderRoomNums.size());
                    }
                } else {
                    for (Integer is : integers) {
                        List<BookingOrderRoomNum> bookingOrderRoomNums = bookRoomNum.get(is);
                        Integer integer = maxUseNumMap.get(is);
                        if (bookingOrderRoomNums != null) {
                            maxUseNumMap.put(is, integer - bookingOrderRoomNums.size());
                        }
                    }

                }
            }
            // 可用数量
            HashMap<Integer, Integer> canUseRoomNum = new HashMap<>();
            for (RoomInfoNum m : roomNumByRoomType) {
                canUseRoomNum.put(m.getRoomTypeId(), m.getSumCount() - maxUseNumMap.get(m.getRoomTypeId()));
            }
            resultMap.put("canUseRoomNum", canUseRoomNum);
        } catch (Exception e) {
            log.error("", e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    /**
     * 从字符串中提取整数，如果不存在数字则返回 null
     */
    private Integer extractNumber(String str) {
        if (str == null || str.isEmpty()) {
            return null;
        }

        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\d+");
        java.util.regex.Matcher matcher = pattern.matcher(str);

        StringBuilder digits = new StringBuilder();

        while (matcher.find()) {
            digits.append(matcher.group());
        }

        if (digits.length() == 0) {
            return null; // 没有任何数字
        }

        try {
            return Integer.parseInt(digits.toString());
        } catch (NumberFormatException e) {
            // 如果超出 Integer 范围，可按需处理：抛异常、返回 null 或 Long 类型
            return null;
        }
    }

    @Override
    public ResponseData findAvailableHourRoom(FindAvailableHourRoomRequest findAvailableHourRoomRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            String sessionToken = findAvailableHourRoomRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if (user.getSessionType() == 3) {
                user.setHid(findAvailableHourRoomRequest.getHid());
            }
            String startTime = findAvailableHourRoomRequest.getStartTime();
            String endTime = findAvailableHourRoomRequest.getEndTime();
            Date startDate = HotelUtils.parseStr2Date(startTime + " 00:00:00");
            Date endDate = HotelUtils.parseStr2Date(endTime + " 00:00:00");
            // 是否删除原预订数量
            Boolean addBookNumsTag = false;
            Map<Integer, List<BookingOrderRoomNum>> bookRoomNum = new HashMap<>();
            Object bookId = findAvailableHourRoomRequest.getBookingOrderId();
            if (bookId != null && !"".equals(bookId.toString()) && !"0".equals(bookId.toString())) {
                BookingOrder bookingOrder = bookingOrderDao.selectById(Integer.parseInt(bookId.toString()));
                if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())) {
                    throw new Exception("预订编号有误");
                }
                Date checkinTime = bookingOrder.getCheckinTime();
                Date checkoutTime = bookingOrder.getCheckoutTime();
                // 预订的开始、结束时间 包含在预订时间内，则把当前订单的信息加到房情中
                if (checkinTime.getTime() >= startDate.getTime() && checkinTime.getTime() <= endDate.getTime() || checkoutTime.getTime() >= startDate.getTime() && checkoutTime.getTime() <= endDate.getTime()) {
                    BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                    bookingOrderRoomNumSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
                    bookingOrderRoomNumSearch.setOrderState(1);
                    bookingOrderRoomNumSearch.setIsCheckin(0);
                    List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
                    bookRoomNum = bookingOrderRoomNums.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getRoomTypeId));
                    addBookNumsTag = true;
                }
            }

            // 1.获取可用房间

            JSONObject param = new JSONObject();
            param.put("endTime", findAvailableHourRoomRequest.getEndTime());
            param.put("startTime", findAvailableHourRoomRequest.getStartTime());
            param.put("sessionToken", findAvailableHourRoomRequest.getSessionToken());
            param.put("type", findAvailableHourRoomRequest.getType());

            List<RoomInfoResult> roomInfos = this.canUseRoom(user, param);

            Map<Integer, List<RoomInfo>> canUserRoomMap = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomTypeId));


            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(startTime, endTime);
            // 获取缓存中预定未分房的数据
            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            String trrb = ECache.TURNALWAYS_NOROWROOMBOOK + "_" + user.getHid();
            Object trrbStr = userCahe.get(ECache.TURNALWAYS_NOROWROOMBOOK, trrb);

            resultMap.put("list", roomInfos);
            resultMap.put("canUseRoomMap", canUserRoomMap);


            // 2.对预定未分房数做处理

            // 查询每个房型的可用数量
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            List<RoomInfoNum> roomNumByRoomType = roomInfoDao.findRoomNumByRoomType(roomInfoSearch);
            //这里查询可用房间数量排除锁房状态
//            List<RoomInfoNum> roomNumByRoomType = roomInfoDao.findRoomNumByRoomTypeWithoutLock(roomInfoSearch);


            // 预订未排房
            JSONObject trrbObj = JSONObject.fromObject(trrbStr);

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setOrderState(1);
            bookingOrderRoomNumSearch.setIsCheckin(0);
            bookingOrderRoomNumSearch.setHid(user.getHid());
            List<Long> checkinTime = new ArrayList<>();
            checkinTime.add(startDate.getTime() / 1000);
            checkinTime.add(endDate.getTime() / 1000 - 5000);
            bookingOrderRoomNumSearch.setCheckinTime(checkinTime);
            List<BookingOrderRoomNum> bookingOrderRoomNums2 = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            Map<Integer, List<BookingOrderRoomNum>> collect = bookingOrderRoomNums2.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getRoomTypeId));
            // 每天的最大使用数量
            HashMap<Integer, Integer> maxUseNum = new HashMap<>();

            // 数据汇总
            Map<Object, Object> dateNumSummary = new HashMap<>();
            JSONObject trrbJson = new JSONObject();

            for (RoomInfoNum m : roomNumByRoomType) {
                Integer roomTypeId = m.getRoomTypeId();
                maxUseNum.put(roomTypeId, 0);
                trrbJson.put(roomTypeId, 0);
                List<BookingOrderRoomNum> bookingOrderRoomNums1 = collect.get(roomTypeId);
                if (bookingOrderRoomNums1 != null) {
                    trrbJson.put(roomTypeId, bookingOrderRoomNums1.size());
                }
            }
            // 房型的使用详情
            String tdrt = ECache.TURNALWAYS_ROOMTYPE + "_" + user.getHid();
            Object o = userCahe.get(ECache.TURNALWAYS_ROOMTYPE, tdrt);

            JSONObject jsonObject = JSONObject.fromObject(o);

            String s = ECache.TURNALWAYS_TURNALWATSDATENUM + "_" + user.getHid();

            Object o1 = userCahe.get(ECache.TURNALWAYS_TURNALWATSDATENUM, s);

            JSONObject tadn = JSONObject.fromObject(o);


            for (String date : allDayListBetweenDate) {

                String replace = date.replace("-", "");
                for (RoomInfoNum m : roomNumByRoomType) {

                    Integer roomTypeId = m.getRoomTypeId();

                    String dk = replace + roomTypeId;
                    Object o2 = tadn.get(dk);
                    if (o2 == null) {
                        continue;
                    }
                    JSONObject jsonObject1 = tadn.getJSONObject(dk);

                    Set<String> set = jsonObject1.keySet();

                    int maxNum = 0;

                    for (String dkey : set) {

                        int anInt = jsonObject1.getInt(dkey);
                        maxNum += anInt;

                    }
                    Integer integer = maxUseNum.get(roomTypeId);
                    if (integer == null) {
                        maxUseNum.put(roomTypeId, maxNum);
                        continue;
                    }
                    if (integer < maxNum) {
                        maxUseNum.put(roomTypeId, maxNum);
                    }

                }

            }
            Set<Integer> integers = maxUseNum.keySet();
            if (addBookNumsTag) {
                if (integers.size() < 1) {
                    Set<Integer> integers1 = bookRoomNum.keySet();
                    for (Integer is : integers1) {
                        List<BookingOrderRoomNum> bookingOrderRoomNums = bookRoomNum.get(is);
                        maxUseNum.put(is, 0 - bookingOrderRoomNums.size());
                    }
                } else {
                    for (Integer is : integers) {
                        List<BookingOrderRoomNum> bookingOrderRoomNums = bookRoomNum.get(is);
                        Integer integer = maxUseNum.get(is);
                        if (bookingOrderRoomNums != null) {
                            maxUseNum.put(is, integer - bookingOrderRoomNums.size());
                        }
                    }

                }
            }
            // 可用数量
            HashMap<Integer, Integer> canUseRoomNum = new HashMap<>();
            for (RoomInfoNum m : roomNumByRoomType) {
                canUseRoomNum.put(m.getRoomTypeId(), m.getSumCount() - maxUseNum.get(m.getRoomTypeId()));
            }
            resultMap.put("trrb", trrbJson);
            resultMap.put("canUseRoomNum", canUseRoomNum);
            responseData.setData(resultMap);
        } catch (Exception e) {
            log.error("", e);
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    public Map<String, Object> findGroupHotelRoom(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            int hid = param.getInt("hid");
            String startTime = param.getString("startTime");
            String endTime = param.getString("endTime");

            // 是否删除原预订数量
            Boolean addBookNumsTag = false;
            Map<Integer, List<BookingOrderRoomNum>> bookRoomNum = new HashMap<>();
            Object bookId = param.get("bookingOrderId");
            if (bookId != null && !"".equals(bookId.toString()) && !"0".equals(bookId.toString())) {
                BookingOrder bookingOrder = bookingOrderDao.selectById(Integer.parseInt(bookId.toString()));
                if (bookingOrder == null || !bookingOrder.getHid().equals(hid)) {
                    throw new Exception("预订编号有误");
                }

                Date checkinTime = bookingOrder.getCheckinTime();
                Date checkoutTime = bookingOrder.getCheckoutTime();

                Date startDate = HotelUtils.parseStr2Date(startTime + " 00:00:00");
                Date endDate = HotelUtils.parseStr2Date(endTime + " 00:00:00");

                // 预订的开始、结束时间 包含在预订时间内，则把当前订单的信息加到房情中
                if (checkinTime.getTime() >= startDate.getTime() && checkinTime.getTime() <= endDate.getTime() || checkoutTime.getTime() >= startDate.getTime() && checkoutTime.getTime() <= endDate.getTime()) {

                    BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                    bookingOrderRoomNumSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
                    bookingOrderRoomNumSearch.setOrderState(1);
                    bookingOrderRoomNumSearch.setIsCheckin(0);

                    List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

                    bookRoomNum = bookingOrderRoomNums.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getRoomTypeId));

                    addBookNumsTag = true;

                }

            }

            // 1.获取可用房间
            List<RoomInfo> roomInfos = this.canUseRoomForGroup(hid, param);

            Map<Integer, List<RoomInfo>> canUserRoomMap = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomTypeId));


            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(startTime, endTime);
            // 获取缓存中预定未分房的数据
            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            String trrb = ECache.TURNALWAYS_NOROWROOMBOOK + "_" + hid;
            Object trrbStr = userCahe.get(ECache.TURNALWAYS_NOROWROOMBOOK, trrb);

            resultMap.put("list", roomInfos);
            resultMap.put("canUseRoomMap", canUserRoomMap);


            // 2.对预定未分房数做处理

            // 查询每个房型的可用数量
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(hid);
            List<RoomInfoNum> roomNumByRoomType = roomInfoDao.findRoomNumByRoomType(roomInfoSearch);
            //这里查询可用房间数量排除锁房状态
//            List<RoomInfoNum> roomNumByRoomType = roomInfoDao.findRoomNumByRoomTypeWithoutLock(roomInfoSearch);

            JSONObject trrbObj = JSONObject.fromObject(trrbStr);

            // 每天的最大使用数量
            HashMap<Integer, Integer> maxUseNum = new HashMap<>();

            // 数据汇总
            Map<Object, Object> dateNumSummary = new HashMap<>();

            if (trrbStr == null) {
                for (RoomInfoNum m : roomNumByRoomType) {
                    Integer roomTypeId = m.getRoomTypeId();
                    maxUseNum.put(roomTypeId, 0);
                }
                resultMap.put("trrb", maxUseNum);
            } else {
                //  key 为 日期   value 为各种房型数量的 json对象

                for (String date : allDayListBetweenDate) {

                    Map<Object, Object> dayNum = new HashMap<>();

                    // 如果当日没有预定未分房数据，则跳出循环
                    Object d = trrbObj.get(date);
                    if (d == null) {
                        d = new JSONArray();
                    }
                    // 取出预定未分房的集合
                    List<BookingOrderRoomNum> list = JSONArray.toList(JSONArray.fromObject(d), BookingOrderRoomNum.class);
                    Map<Integer, List<BookingOrderRoomNum>> collect = list.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getRoomTypeId));

                    for (RoomInfoNum m : roomNumByRoomType) {

                        Integer roomTypeId = m.getRoomTypeId();

                        Integer integer = maxUseNum.get(roomTypeId);
                        if (integer == null) {
                            integer = 0;
                        }
                        List<BookingOrderRoomNum> bookingOrderRoomNums = collect.get(roomTypeId);
                        if (bookingOrderRoomNums != null && bookingOrderRoomNums.size() > integer) {
                            integer = bookingOrderRoomNums.size();
                        }
                        maxUseNum.put(roomTypeId, integer);

                    }

                }
            }


            Set<Integer> integers = maxUseNum.keySet();
            if (addBookNumsTag) {

                if (integers.size() < 1) {
                    Set<Integer> integers1 = bookRoomNum.keySet();
                    for (Integer is : integers1) {
                        List<BookingOrderRoomNum> bookingOrderRoomNums = bookRoomNum.get(is);
                        maxUseNum.put(is, 0 - bookingOrderRoomNums.size());
                    }
                } else {
                    for (Integer is : integers) {
                        List<BookingOrderRoomNum> bookingOrderRoomNums = bookRoomNum.get(is);
                        Integer integer = maxUseNum.get(is);
                        if (bookingOrderRoomNums != null) {
                            maxUseNum.put(is, integer - bookingOrderRoomNums.size());
                        }
                    }

                }
            }

            // 可用数量
            HashMap<Integer, Integer> canUseRoomNum = new HashMap<>();

            integers = maxUseNum.keySet();
            for (Integer is : integers) {
                List<RoomInfo> roomInfos1 = canUserRoomMap.get(is);
                if (roomInfos1 == null) {
                    roomInfos1 = new ArrayList<>();
                }
                canUseRoomNum.put(is, roomInfos1.size() - maxUseNum.get(is));
            }


            resultMap.put("trrb", maxUseNum);
            resultMap.put("canUseRoomNum", canUseRoomNum);

        } catch (Exception e) {
            log.error("", e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    @Override
    public ResponseData hourTurnAlways(HourRoomDayUseSearch hourRoomDayUseSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            final TbUserSession user = this.getTbUserSession(hourRoomDayUseSearch.getSessionToken());

            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHotelGroupId(user.getHotelGroupId());
            roomInfoSearch.setRoomTypeId(hourRoomDayUseSearch.getRoomTypeId());
            roomInfoSearch.setRoomNum(hourRoomDayUseSearch.getRoomNo());
            roomInfoSearch.setRoomInfoId(hourRoomDayUseSearch.getRoomInfoId());
            roomInfoSearch.setState(1);
            roomInfoSearch.setHid(hourRoomDayUseSearch.getHid());

            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);

            HashMap<Integer, Boolean> basRes = new HashMap<>();

            for (int i = 0; i < 24; i++) {

                basRes.put(i, true);

            }

            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDateTwo(hourRoomDayUseSearch.getStartTime(), hourRoomDayUseSearch.getEndTime());

            Date date = HotelUtils.parseStr2Date(hourRoomDayUseSearch.getStartTime().substring(0, 10) + " 00:00:00");
            Date date1 = HotelUtils.parseStr2Date(hourRoomDayUseSearch.getEndTime().substring(0, 10) + " 00:00:00");

            hourRoomDayUseSearch.setBusinessDayMin(HotelUtils.parseDate2Int(date));
            hourRoomDayUseSearch.setBusinessDayMax(HotelUtils.parseDate2Int(date1));

            Page<HourRoomDayUse> hourRoomDayUses = hourRoomDayUseDao.selectBySearch(hourRoomDayUseSearch);

            Map<Integer, List<HourRoomDayUse>> hourRoomMap = new HashMap<>();

            if (hourRoomDayUses.size() > 0) {

                hourRoomMap = hourRoomDayUses.stream().collect(Collectors.groupingBy(HourRoomDayUse::getRoomInfoId));

            }

            HashMap<Integer, HourTrunAlwaysResponse> integerhourTrunAlwaysResponseHashMap = new HashMap<Integer, HourTrunAlwaysResponse>();

            for (RoomInfo rf : roomInfos) {
                HourTrunAlwaysResponse hourTrunAlwaysResponse = new HourTrunAlwaysResponse();

                hourTrunAlwaysResponse.setRoomTypeName(rf.getRoomTypeName());
                hourTrunAlwaysResponse.setRoomTypeId(rf.getRoomTypeId());
                hourTrunAlwaysResponse.setRoomNo(rf.getRoomNum());
                hourTrunAlwaysResponse.setRoomInfoId(rf.getRoomInfoId());

                HashMap<Integer, Map<Integer, Boolean>> integerMapHashMap = new HashMap<>();

                List<HourRoomDayUse> roomUses = hourRoomMap.get(rf.getRoomInfoId());

                // 根据房间id 没有查出信息时，则都返回true
                if (roomUses == null || roomUses.size() < 1) {

                    roomUses = new ArrayList<>();
                }

                Map<Integer, HourRoomDayUse> collect = roomUses.stream().collect(Collectors.toMap(HourRoomDayUse::getBusinessDay, a -> a, (k1, k2) -> k2));

                for (String dayStr : allDayListBetweenDate) {

                    int day = Integer.parseInt(dayStr.replace("-", ""));

                    HourRoomDayUse hourRoomDayUse = collect.get(day);
                    if (hourRoomDayUse == null) {

                        integerMapHashMap.put(day, basRes);
                        continue;

                    }

                    String[] split = hourRoomDayUse.getUseMsg().split(",");


                    HashMap<Integer, Boolean> basResRoom = new HashMap<>();
                    for (int i = 0; i < 24; i++) {
                        basResRoom.put(i, true);
                    }

                    for (int i = 0; i < split.length; i++) {
                        int i1 = Integer.parseInt(split[i]);
                        basResRoom.put(i1, false);
                    }

                    integerMapHashMap.put(day, basResRoom);

                }
                hourTrunAlwaysResponse.setDayDetails(integerMapHashMap);

                integerhourTrunAlwaysResponseHashMap.put(rf.getRoomInfoId(), hourTrunAlwaysResponse);

            }

            responseData.setData(integerhourTrunAlwaysResponseHashMap);


        } catch (Exception e) {
            log.error("", e);
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * @param type      状态 1.入住信息  2.维修/停用 3.预定信息(已排房) 4.预定信息(未排房)
     * @param baseData  数据集合
     * @param roomNumId 房间id
     * @param beginTime 开始时间
     * @param endTime   结束时间
     */
    private List<JSONObject> dateBaseHandler(int type, List<JSONObject> baseData, Integer roomNumId, Date beginTime, Date endTime, Date minStartTime, Date maxEndTime, JSONObject obj) {

        if (beginTime == null || endTime == null) {
            return baseData;
        }

        // 获取每个的入住日期，取出日期差
        if (minStartTime.getTime() > beginTime.getTime()) {
            minStartTime.setTime(beginTime.getTime());
        }

        if (maxEndTime.getTime() < endTime.getTime()) {
            maxEndTime.setTime(endTime.getTime());
        }

        obj.put("createTime", "");
        obj.put("updateTime", "");
        obj.put("dataType", type);
        obj.put("beginTimeInt", Integer.parseInt(HotelUtils.parseDate2Str(beginTime).substring(0, 10).replace("-", "")));
        obj.put("endTimeInt", Integer.parseInt(HotelUtils.parseDate2Str(endTime).substring(0, 10).replace("-", "")));
        obj.put("roomNumId", roomNumId);
        baseData.add(obj);

        return baseData;
    }

    /**
     * 更新缓存中的房间信息
     *
     * @param user
     * @return
     */
    public List<RoomInfo> updateRoomListForCache(final TbUserSession user) {

        RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
        roomInfoSearch.setHid(user.getHid());

        List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);

        final HashOperations<String, Object, Object> ops = stringRedisTemplate.opsForHash();

        String key = ECache.ROOM_LIST + "_" + user.getHid();
        JsonConfig jsonConfig = new JsonConfig();
        jsonConfig.registerJsonValueProcessor(Date.class,
                new JsonDateValueProcessor());
        ops.put(ECache.ROOM_LIST, key, JSONArray.fromObject(roomInfos, jsonConfig).toString());

        return roomInfos;

    }
}
