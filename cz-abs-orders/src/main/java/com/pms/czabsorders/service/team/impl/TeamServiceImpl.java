package com.pms.czabsorders.service.team.impl;


import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.TeamInfo;
import com.pms.czabsorders.service.team.TeamService;
import com.pms.czabsorders.service.team.transaction.TeamTransactionService;
import com.pms.czabsorders.service.turnAlways.TurnAlwaysService;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomRepairRecordHistory;
import com.pms.czhotelfoundation.bean.room.search.RoomAuxiliaryRelationSearch;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.service.room.RoomService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.WebClientUtil;
import com.pms.czpmsutils.conf.HotelIotPlatConfig;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.Iot;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.room.ROOM_AUXILIARY;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.room.RoomUtils;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.IotHotelCheckOut;
import com.pms.czpmsutils.request.SmartLockRequest;
import com.pms.czpmsutils.thirdauth.HotelIotStrategy;
import com.pms.czpmsutils.view.AESUtil;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.request.RegistGroupRequest;
import com.pms.pmsorder.bean.search.*;
import com.pms.pmsorder.bean.view.RegistGroupDetails;
import com.pms.pmsorder.dao.*;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Primary
public class TeamServiceImpl extends BaseService implements TeamService {

    @Autowired
    private RegistDao registDao;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private RegistGroupDao registGroupDao;

    @Autowired
    private TeamTransactionService teamTransactionService;


    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    private BaseService baseService = this;

    @Autowired
    private TurnAlwaysService turnAlwaysService;

    @Autowired
    private RoomService roomService;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;


    @Resource
    private HotelIotPlatConfig iotPlatConfig;

    @Resource
    private WebClientUtil webClientUtil;

    /**
     * 解除团队
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> relieveTeam(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        resultMap.put("code", 1);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            ArrayList<Regist> regists = new ArrayList<>();

            // 拆分类型 1.单房间拆分  2.全部拆分
            int reliveType = param.getInt("reliveType");

            //辅助房态查询
            RoomAuxiliaryRelationSearch rars = new RoomAuxiliaryRelationSearch();

            //辅助房态集合
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            //主单id
            int teamCodeId = param.getInt("teamCodeId");
            RegistGroup registGroup = registGroupDao.selectById(teamCodeId);

            if (registGroup == null) {
                throw new Exception("未查询到团队信息");
            }

            RegistSearch registSearch = new RegistSearch();
            registSearch.setTeamCodeId(teamCodeId);
            List<Regist> teamRegists = registDao.selectBySearch(registSearch);
            if (reliveType == 1) {
                int registId = param.getInt("registId");
                Regist registInfo = registDao.selectById(registId);
                if (!registInfo.getHid().equals(user.getHid())) {
                    throw new Exception("未查到当前酒店的登记信息");
                }
                regists.add(registInfo);

                //重新计算团队的房间数，总消费，总付款等等
                int payMoney = 0;
                int costMoney = 0;
                int personCount = 0;
                for (int i = 0; i < teamRegists.size(); i++) {
                    Regist regist = teamRegists.get(i);
                    if (regist.getRegistId() != registInfo.getRegistId()) {
                        payMoney += regist.getSumPay();
                        costMoney += regist.getSumSale();
                        personCount += regist.getPersonCount();
                    }
                }
                registGroup.setSumPay(payMoney);
                registGroup.setSumSales(costMoney);
                registGroup.setSumRooms(teamRegists.size() - 1);
                registGroup.setSumPersonNum(personCount);
                //查询辅助房态
                rars.setRegistId(registId);
                if (9 == registGroup.getGroupType()) {
                    rars.setRoomAuxiliaryId(ROOM_AUXILIARY.JOINT_HOUSING);
                } else {
                    rars.setRoomAuxiliaryId(ROOM_AUXILIARY.TEAM);
                }
                List<RoomAuxiliaryRelation> relations = roomAuxiliaryRelationDao.selectBySearch(rars);
                roomAuxiliaryRelations.addAll(relations);

            } else {
                //解散团队
                registGroup.setState(0);
                for (Regist rg : teamRegists) {
                    //查询辅助房态
                    rars.setRegistId(rg.getRegistId());
                    if (9 == registGroup.getGroupType()) {
                        rars.setRoomAuxiliaryId(ROOM_AUXILIARY.JOINT_HOUSING);
                    } else {
                        rars.setRoomAuxiliaryId(ROOM_AUXILIARY.TEAM);
                    }
                    List<RoomAuxiliaryRelation> relations = roomAuxiliaryRelationDao.selectBySearch(rars);
                    roomAuxiliaryRelations.addAll(relations);
                    regists.add(rg);
                }
            }
            teamTransactionService.relieveTeam(reliveType, regists, registGroup, user, roomAuxiliaryRelations);

        } catch (Exception e) {
            resultMap.put("code", -1);
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    /**
     * 团队合并
     * 团队类型  1.单个房间合并   2.单房间并入团队  3.团队和团队合并
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> mergeTeam(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            ArrayList<Regist> regists = new ArrayList<>();

            // 合并类型 1.单个房间合并   2.单房间并入团队  3.团队和团队合并
            int mergeType = param.getInt("mergeType");

            //1.入住单集合
            JSONArray registArray = param.getJSONArray("registList");

            for (int i = 0; i < registArray.size(); i++) {

                JSONObject rg = registArray.getJSONObject(i);

                Regist regist = registDao.selectById(rg.getInt("registId"));

                if (regist == null || regist.getState() != 0) {
                    throw new Exception("房间:" + regist.getRoomNum() + " 的状态不支持合并。");
                }
                regists.add(regist);
            }

            //查找主团队
            RegistGroup mainTeam = new RegistGroup();

            ArrayList<RegistGroup> registGorupList = new ArrayList<>();

            //新团队创建
            if (mergeType == 1) {

                //团队参数
                JSONObject checkInParam = JSONObject.fromObject(URLDecoder.decode(param.getString("checkInParam"), "utf-8"));
                String rgSn = HotelUtils.getHIDUUID32("T", user.getHid());
                mainTeam.setSn(rgSn);
                mainTeam.setPayType(0);

                int resourceId = checkInParam.getInt("resourceId");
                mainTeam.setGroupType(resourceId);

                if (checkInParam.get("groupName") != null) {
                    mainTeam.setGroupName(checkInParam.getString("groupName"));
                } else {
                    mainTeam.setGroupName(rgSn);
                }

                mainTeam.setSumPay(0);
                mainTeam.setSumSales(0);
                mainTeam.setSumRooms(checkInParam.getInt("sumRoomNum"));
                mainTeam.setSumPersonNum(checkInParam.getInt("peopleNum"));
                //省
                mainTeam.setProvince(HotelUtils.validaStr(checkInParam.get("province")));
                //市
                mainTeam.setCity(HotelUtils.validaStr(checkInParam.get("city")));
                mainTeam.setRemark(HotelUtils.validaStr(checkInParam.get("remark")));
                mainTeam.setState(0);
                mainTeam.setHid(user.getHid());
                mainTeam.setHotelGroupId(user.getHotelGroupId());
                mainTeam.setBusinessDay(user.getBusinessDay());
                mainTeam.setClassId(user.getClassId());
                mainTeam.setCreateTime(new Date());
                mainTeam.setCreateUserId(user.getUserId());
                mainTeam.setCreateUserName(user.getUserName());

                //协议单位
                if (checkInParam.get("companyId") != null) {
                    mainTeam.setCompanyId(checkInParam.getInt("companyId"));
                    mainTeam.setCompayName(checkInParam.getString("compayName"));
                }

                //房价方案
                mainTeam.setRoomRateCodeId(checkInParam.getInt("rateId"));
                mainTeam.setRoomRateCodeName(checkInParam.getString("rateCode"));

                //会员卡号
                Object memberIdObj = param.get("memberId");
                if (memberIdObj != null) {
                    mainTeam.setMemberId(param.getInt("memberId"));
                    mainTeam.setMemberCard(param.getString("cardNo"));
                }


            } else {

                int mainTeamId = param.getInt("mainTeamId");

                //查询主团队信息
                mainTeam = registGroupDao.selectById(mainTeamId);

                if (mainTeam == null || mainTeam.getState() != 1) {

                    throw new Exception("当前" + mainTeam.getSn() + "状态不允许合并");

                }

                //合并的团队信息
                JSONArray registGroupArray = param.getJSONArray("registGroupArray");

                for (int i = 0; i < registArray.size(); i++) {

                    JSONObject rgObj = registArray.getJSONObject(0);

                    int registGroupId = rgObj.getInt("registGroupId");

                    RegistGroup registGroup = registGroupDao.selectById(registGroupId);

                    if (registGroup == null) {
                        continue;
                    }

                    if (mainTeam.getState() != 1) {
                        throw new Exception("当前团队不支持合并。团队号:" + registGroup.getSn());
                    }

                    registGorupList.add(registGroup);

                }

            }

            teamTransactionService.mergeTeam(mergeType, regists, mainTeam, registGorupList, user);


        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData setMainRoom(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if (param.get("registId") == null) {
                throw new Exception("参数不明确");
            }
            int registId = param.getInt("registId");
            Regist regist = registDao.selectById(registId);
            if (regist.getRegistId() == null || regist.getState() != 0) {
                throw new Exception("参数不明确");
            }
            Integer teamCodeId = regist.getTeamCodeId();

            RegistSearch registSearch = new RegistSearch();
            registSearch.setTeamCodeId(teamCodeId);
            List<Regist> regists = registDao.selectBySearch(registSearch);

            for (int i = 0; i < regists.size(); i++) {
                Regist registInfo = regists.get(i);
                if (registInfo.getRegistId().equals(regist.getRegistId())) {
                    registInfo.setIsMainRoom(1);
                } else {
                    registInfo.setIsMainRoom(0);
                }
                Integer update = registDao.update(registInfo);
                if (update < 1) {
                    throw new Exception("设置主账房失败");
                }
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updateTeamInfo(TeamInfo teamInfo) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = getTbUserSession(teamInfo);

            RegistGroup registGroup = registGroupDao.selectById(teamInfo.getRegistGroupId());
            if (registGroup.getRegistGroupId() == null) {
                throw new Exception("查询不到需要修改的账务信息");
            }
            if (teamInfo.getGroupName() != "") {
                registGroup.setGroupName(teamInfo.getGroupName());
            }
            if (teamInfo.getRemark() != "") {
                registGroup.setRemark(teamInfo.getRemark());
            }
            Integer update = registGroupDao.update(registGroup);

            if (update < 1) {
                throw new Exception("修改团队信息失败");
            }
            //日志记录


        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData teamOnAccount(TeamInfo teamInfo) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = getTbUserSession(teamInfo);
            // 团队信息
            RegistGroup registGroup = registGroupDao.selectById(teamInfo.getRegistGroupId());
            if (registGroup != null && registGroup.getState() != 1 && registGroup.getState() != 3) {
                throw new Exception("团队信息有误");
            }
            Date date = new Date();
            // 查询当前团队在住信息
            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setTeamCodeId(teamInfo.getRegistGroupId());
            registSearch.setState(0);
            List<Regist> regists = registDao.selectBySearch(registSearch);
            StringBuilder sbRoomIds = new StringBuilder();
            StringBuilder sbRoomNos = new StringBuilder();
            StringBuilder sbRegistIds = new StringBuilder();
            // 房间信息
            List<RoomInfo> roomInfos = new ArrayList<>();

            for (Regist regist : regists) {

                if (regist.getState() != 0) {
                    continue;
                }

                sbRoomNos.append(regist.getRoomNum());
                sbRoomNos.append(",");

                sbRoomIds.append(regist.getRoomNumId());
                sbRoomIds.append(",");

                sbRegistIds.append(regist.getRegistId());
                sbRegistIds.append(",");

            }

            // 查询辅助房态
            String rIdStr = sbRoomIds.toString();
            String rdStr = rIdStr.substring(0, rIdStr.length() - 1);

            // 查询辅助房态
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setHid(user.getHid());
            roomAuxiliaryRelationSearch.setRoomIds(rdStr);

            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

            Map<Integer, List<RoomAuxiliaryRelation>> relaMap = roomAuxiliaryRelations.stream().collect(Collectors.groupingBy(RoomAuxiliaryRelation::getRoomId));

            // 查询人员信息
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            String sbRegistStr = sbRegistIds.toString();
            String registIds = sbRegistStr.substring(0, sbRegistStr.length() - 1);
            registPersonSearch.setHid(user.getHid());
            registPersonSearch.setRegInKeys(registIds);
            registPersonSearch.setRegistState(0);

            List<RegistPerson> updaRegist = new ArrayList<RegistPerson>();

            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
            for (RegistPerson registPerson : registPeople) {
                registPerson.setRegistState(2);
                updaRegist.add(registPerson);
            }

            // 对房间进行处理
            final List<Regist> onAccountRegists = new ArrayList<>();

            List<RoomRepairRecordHistory> roomRepairRecordHistories = new ArrayList<>();


            ArrayList<Oprecord> oprecords = new ArrayList<>();

            Oprecord opt = new Oprecord(user);
            opt.setHid(user.getHid());
            opt.setMainId(registGroup.getSn());
            opt.setBcodeO(registGroup.getRegistGroupId() + "");
            opt.setDescription("将团队编号：" + registGroup.getSn() + "，名称：" + registGroup.getSn() + "进行一键挂账操作。");
            oprecords.add(opt);

            String s = HotelUtils.currentTime();

            for (Regist regist : regists) {

                Oprecord oprecord = new Oprecord(user);
                oprecord.setOccurTime(s);
                oprecord.setRegistId(regist.getRegistId());
                oprecord.setRoomNum(regist.getRoomNum());
                oprecord.setMainId(regist.getSn());
                oprecord.setChangedValue("2");
                oprecord.setSourceValue(regist.getState() + "");
                oprecord.setHid(user.getHid());
                oprecord.setDescription("进行团队挂账操作，将房间 " + regist.getRoomNum() + " 改为挂账状态。");
                oprecords.add(oprecord);

                RoomInfo roomInfo = new RoomInfo();
                roomInfo.setRoomInfoId(regist.getRoomNumId());
                roomInfo.setRoomNumState(2);
                roomInfos.add(roomInfo);

                // 登记
                /**
                 * 修改登记表中状态为挂账未结
                 */
                regist.setState(2);
                //修改挂帐时间为当前时间
                regist.setCheckoutBusinessDay(user.getBusinessDay());
                regist.setCheckoutClassId(user.getClassId());
                regist.setSettleAccountTime(date);

                // 辅助房态
                List<RoomAuxiliaryRelation> rarls = relaMap.get(regist.getRegistId());
                String rarlsStr = "";
                if (rarls != null && rarls.size() > 0) {
                    for (RoomAuxiliaryRelation rar : rarls) {
                        rarlsStr += rar.getRelationId() + ",";
                    }
                    rarlsStr = rarlsStr.substring(0, rarlsStr.length() - 1);
                }

                regist.setRoomAuxiliaryRecord(rarlsStr);

                onAccountRegists.add(regist);

                RoomRepairRecordHistory roomRepairRecord = new RoomRepairRecordHistory();

                roomRepairRecord.setRoomId(roomInfo.getRoomInfoId());
                roomRepairRecord.setRoomNum(roomInfo.getRoomNum());
                roomRepairRecord.setHid(regist.getHid());
                roomRepairRecord.setHotelGroupId(regist.getHotelGroupId());
                roomRepairRecord.setClassId(user.getClassId());
                roomRepairRecord.setCreateUserId(user.getUserId());
                roomRepairRecord.setCreateUserName(user.getUserName());
                roomRepairRecord.setCreateTime(date);
                roomRepairRecord.setUpdateUserId(user.getUserId());
                roomRepairRecord.setUpdateUserName(user.getUserName());
                roomRepairRecord.setUpdateTime(date);
                roomRepairRecord.setType(RoomUtils.getRoomRepairRecordType(3, ROOM_STATUS.VD));
                roomRepairRecord.setState(RoomUtils.getRoomRepairRecordState(3, ROOM_STATUS.VD));
                roomRepairRecord.setBusinessDay(user.getBusinessDay());
                roomRepairRecord.setDes(roomInfo.getRoomNum() + "入住。");

                roomRepairRecordHistories.add(roomRepairRecord);

            }


            teamTransactionService.teamOnAccount(registGroup, onAccountRegists, updaRegist, roomAuxiliaryRelations, roomInfos, roomRepairRecordHistories);

            this.addOprecords(oprecords);

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    try {

                        roomService.updateRoomListForCache(user);

                        HashMap<String, String> fieledMap = new HashMap<>();

                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();

                       turnAlwaysService.turnAlwaysCacheFunc(user);

                        fieledMap.put("roomList", sbRoomNos.toString());

                        baseService.push(user.getHotelGroupId(), user.getHid(), 30, fieledMap, new HashMap<String, String>(), true, true);
                    } catch (Exception e) {

                        log.info("-------------------结账线程处理报错-begin----------------");
                        log.error("",e);
                        log.info("-------------------结账线程处理报错-end----------------");
                    }
                }
            });

            final Map<Integer, List<RegistPerson>> collect = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));

            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    for (int i = 0; i < onAccountRegists.size(); i++) {
                        Regist regist = onAccountRegists.get(i);
                        SmartLockRequest smartLockRequest = new SmartLockRequest();
                        smartLockRequest.setSessionToken(user.getSessionId());
                        smartLockRequest.setRegistId(regist.getRegistId());
                        smartLockRequest.setCheckinTime(regist.getCheckinTime());
                        smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                        smartLockRequest.setRoomTypeId(regist.getRoomTypeId());
                        smartLockRequest.setRoomTypeName(regist.getRoomTypeName());
                        smartLockRequest.setRoomNumId(regist.getRoomNumId());
                        smartLockRequest.setRoomNum(regist.getRoomNum());
                        smartLockRequest.setHid(regist.getHid());
                        smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                        smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                        smartLockRequest.setCheckinType(regist.getCheckinType());
                        smartLockRequest.setBussType(2);
                        List<RegistPerson> registPeople = collect.get(regist.getRegistId());
                        List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                        for (int j = 0; j < registPeople.size(); j++) {
                            RegistPerson registPersonInfo = registPeople.get(j);
                            SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                            BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                            peoplesList.add(registPersonDetail);
                        }
                        smartLockRequest.setPeoples(peoplesList);
                        baseService.SmartLockCheckout(smartLockRequest);
                    }
                }
            });

            /**
             * 联房挂帐循环调用酒店退房
             */


            if(!regists.isEmpty()){
                for (Regist regist : regists) {
                    IotHotelCheckOut checkOut = new IotHotelCheckOut();
                    checkOut.setCode(regist.getRegistId() + "");
                    HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
                    Map<String, String> authParamMap = new HashMap<>();
                    authParamMap.put("eid",user.getHid().toString());
                    authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
                    authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
                    authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
                    try {
                        authParamMap.put("phone", AESUtil.encryptAesCbc(user.getPhone(),iotPlatConfig.getSassTokenSecret()));
                    } catch (Exception e) {
                        log.error("手机号加密失败");
                    }
                    hotelIotStrategy.initStrategy(authParamMap);
                    webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.CHECK_OUT,
                            checkOut,
                            hotelIotStrategy
                    );
                }
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData selectAllTeam(RegistGroupRequest registGroupSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = getTbUserSession(registGroupSearch);

            Page<RegistGroupView> registGroupViews = registGroupDao.selectAllTeam(registGroupSearch);

            responseData.setData(registGroupViews);

            return responseData;

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    public ResponseData searchTeamDetails(RegistGroupRequest registGroupSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = getTbUserSession(registGroupSearch);

            HashMap<Integer, RegistGroupDetails> registGroupMap = new HashMap<>();

            RegistGroupSearch rgs = new RegistGroupSearch();
            rgs.setHid(user.getHid());
            rgs.setRegistGroupIds(registGroupSearch.getRegistGroupIds());

            List<RegistGroup> registGroups = registGroupDao.selectBySearch(rgs);

            String bookIds = "";

            for(RegistGroup registGroup:registGroups){
                bookIds+=registGroup.getBookingOrderId()+",";
            }

            bookIds = bookIds.substring(0,bookIds.length()-1);


            // 查询预订单信息
            BookingOrderSearch bookingOrderSearch = new BookingOrderSearch();
            bookingOrderSearch.setHid(user.getHid());
            bookingOrderSearch.setBookingOrderIds(bookIds);
            List<BookingOrder> bookingOrders = bookingOrderDao.selectBySearch(bookingOrderSearch);

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setHid(user.getHid());
            bookingOrderRoomNumSearch.setIsCheckin(0);
            bookingOrderRoomNumSearch.setOrderState(1);
            bookingOrderRoomNumSearch.setBookingOrderIdStr(bookIds);
            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            Map<Integer, List<BookingOrderRoomNum>> bookRoomMap = bookingOrderRoomNums.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getBookingOrderId));

            // 查询登记单信息
            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setTeamCodeIdStr(registGroupSearch.getRegistGroupIds());

            // 登记单信息
            List<Regist> regists = registDao.selectBySearch(registSearch);

            Map<Integer, List<Regist>> registMap = regists.stream().collect(Collectors.groupingBy(Regist::getTeamCodeId));

            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setHid(user.getHid());
            registPersonSearch.setTeamCodeIdStr(registGroupSearch.getRegistGroupIds());
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);

            Map<Integer, List<RegistPerson>> personMap = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getTeamCodeId));

            for(RegistGroup bookingOrder:registGroups){

                Integer teamCodeId = bookingOrder.getRegistGroupId();

                RegistGroupDetails registGroupDetails = new RegistGroupDetails();

                List<BookingOrderRoomNum> roomNums = bookRoomMap.get(bookingOrder.getBookingOrderId());

                registGroupDetails.setCheckInCount(0);
                registGroupDetails.setPersonCount(0);
                registGroupDetails.setCheckoutCount(0);
                registGroupDetails.setCheckOnAccountCount(0);
                registGroupDetails.setSumSale(0);
                registGroupDetails.setSumPay(0);


                if(roomNums!=null){

                    // 订单房间数
                    registGroupDetails.setRoomCount(roomNums.size());

                    registGroupDetails.setCheckinTime(HotelUtils.parseDate2Str(roomNums.get(0).getCheckinTime()));
                    registGroupDetails.setCheckoutTime(HotelUtils.parseDate2Str(roomNums.get(0).getCheckoutTime()));
                    registGroupDetails.setCheckinLong(roomNums.get(0).getCheckinTime().getTime());
                    registGroupDetails.setCheckoutLong(roomNums.get(0).getCheckoutTime().getTime());

                    for(BookingOrderRoomNum born:roomNums){
                        if(born.getCheckinTime().getTime()<registGroupDetails.getCheckinLong()){
                            registGroupDetails.setCheckinTime(HotelUtils.parseDate2Str(born.getCheckinTime()));
                            registGroupDetails.setCheckinLong(born.getCheckinTime().getTime());
                        }
                        if(born.getCheckoutTime().getTime()>registGroupDetails.getCheckoutLong()){
                            registGroupDetails.setCheckoutTime(HotelUtils.parseDate2Str(born.getCheckoutTime()));
                            registGroupDetails.setCheckoutLong(born.getCheckoutTime().getTime());
                        }
                    }

                }else {
                    // 订单房间数
                    registGroupDetails.setRoomCount(0);
                }

                List<Regist> regists1 = registMap.get(teamCodeId);
                if(regists1!=null){


                    if(registGroupDetails.getCheckinTime()==null){
                        registGroupDetails.setCheckinTime(HotelUtils.parseDate2Str(regists1.get(0).getCheckinTime()));
                        registGroupDetails.setCheckoutTime(HotelUtils.parseDate2Str(regists1.get(0).getCheckoutTime()));
                        registGroupDetails.setCheckinLong(regists1.get(0).getCheckinTime().getTime());
                        registGroupDetails.setCheckoutLong(regists1.get(0).getCheckoutTime().getTime());
                        registGroupDetails.setCheckOnAccountCount(0);
                        registGroupDetails.setSumPay(0);
                        registGroupDetails.setSumSale(0);
                    }

                    registGroupDetails.setRoomCount(registGroupDetails.getRoomCount()+regists1.size());

                    for(Regist regist:regists1){

                        if(null!=regist.getSumPay()){
                            registGroupDetails.setSumPay(registGroupDetails.getSumPay()+regist.getSumPay());
                        }
                        if(null!=regist.getSumSale()){
                            registGroupDetails.setSumSale(registGroupDetails.getSumSale()+regist.getSumSale());
                        }

                        if(regist.getCheckinTime().getTime()<registGroupDetails.getCheckinLong()){
                            registGroupDetails.setCheckinTime(HotelUtils.parseDate2Str(regist.getCheckinTime()));
                            registGroupDetails.setCheckinLong(regist.getCheckinTime().getTime());
                        }
                        if(regist.getCheckoutTime().getTime()>registGroupDetails.getCheckoutLong()){
                            registGroupDetails.setCheckoutTime(HotelUtils.parseDate2Str(regist.getCheckoutTime()));
                            registGroupDetails.setCheckoutLong(regist.getCheckoutTime().getTime());
                        }
                        if(regist.getState()==0){
                            registGroupDetails.setCheckInCount(registGroupDetails.getCheckInCount()+1);

                        }else  if(regist.getState()==1){
                            registGroupDetails.setCheckoutCount(registGroupDetails.getCheckoutCount()+1);

                        }else  if(regist.getState()==2){
                            registGroupDetails.setCheckOnAccountCount(registGroupDetails.getCheckOnAccountCount()+1);

                        }
                    }

                }

                List<RegistPerson> registPeople1 = personMap.get(teamCodeId);
                if(registPeople1!=null){
                    registGroupDetails.setPersonCount(registPeople1.size());
                }

                registGroupMap.put(teamCodeId,registGroupDetails);

            }

            responseData.setData(registGroupMap);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
