package com.pms.czabsorders.service.checkout.transaction;



import com.pms.czaccount.bean.account.Account;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomRepairRecordHistory;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface CheckOutTransactionService {

    /**
     * 结账
     *
     * @param registList
     * @param registGroups
     * @param bookingOrderList
     * @param bookingOrderRoomTypeList
     * @param bookingOrderRoomNums
     * @param roomAuxiliaryRelations
     * @param user
     * @param accountList
     * @param roomInfoMap
     * @throws Exception
     */
    public default void checkOut(List<Regist> registList, List<RegistGroup> registGroups, List<BookingOrder> bookingOrderList,
                                 List<BookingOrderRoomType> bookingOrderRoomTypeList, List<BookingOrderRoomNum> bookingOrderRoomNums,
                                 List<RoomAuxiliaryRelation> roomAuxiliaryRelations, TbUserSession user, List<Account> accountList, Map<Integer, RoomInfo> roomInfoMap,
                                 ArrayList<Object> salesHotelCommissionDetailsList,List<RegistPerson> registPeople) throws Exception {

    }

    public default void checkOut(List<Regist> registList, List<RegistGroup> registGroups, List<BookingOrder> bookingOrderList,
                                 List<BookingOrderRoomType> bookingOrderRoomTypeList, List<BookingOrderRoomNum> bookingOrderRoomNums,
                                 List<RoomAuxiliaryRelation> roomAuxiliaryRelations, TbUserSession user, List<Account> accountList, Map<Integer, RoomInfo> roomInfoMap,
                                 ArrayList<Object> salesHotelCommissionDetailsList, List<RegistPerson> registPeople, ArrayList<HourRoomDayUse> upaHourUse, ArrayList<HourRoomDayUse> delHourUse) throws Exception {

    }


    /**
     * 挂账
     * @param regist
     * @param roomInfo
     * @param user
     * @param oprecords
     * @param oprecord
     * @throws Exception
     */
    public void onAccount(Regist regist, RoomInfo roomInfo, List<RegistPerson> personList , TbUserSession user,
                          ArrayList<Oprecord> oprecords, Oprecord oprecord,List<RoomAuxiliaryRelation> roomAuxiliaryRelations) throws Exception;

    public void onAccount(Regist regist, RoomInfo roomInfo, List<RegistPerson> personList , TbUserSession user,
                          ArrayList<Oprecord> oprecords, Oprecord oprecord,List<RoomAuxiliaryRelation> roomAuxiliaryRelations,ArrayList<HourRoomDayUse> upaHourUse, ArrayList<HourRoomDayUse> delHourUse) throws Exception;

    /**
     * 取消入住
     * @param regist
     * @param roomInfo
     * @param personList
     * @param roomAuxiliaryRelations
     * @param bookingOrder
     * @param bookingOrderRoomNum
     * @param roomRepairRecord
     * @throws Exception
     */
    public void registCancel(Regist regist, RoomInfo roomInfo, List<RegistPerson> personList, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, BookingOrder bookingOrder, BookingOrderRoomNum bookingOrderRoomNum , RoomRepairRecordHistory roomRepairRecord ) throws Exception ;

}
