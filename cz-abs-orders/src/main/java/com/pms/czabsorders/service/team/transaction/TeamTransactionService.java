package com.pms.czabsorders.service.team.transaction;


import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomRepairRecordHistory;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistGroup;
import com.pms.pmsorder.bean.RegistPerson;

import java.util.List;

public interface TeamTransactionService {

    /**
     * 解除团队
     * @param reliveType    1.单房间拆分  2.团队解散
     * @param registList    拆分的入住集合
     * @param registGroup   当前拆分的团队
     */
    public void relieveTeam(int reliveType, List<Regist> registList, RegistGroup registGroup, TbUserSession user, List<RoomAuxiliaryRelation> relationList) throws Exception;

    /**
     * 合并团队
     * @param mergeType
     * @param registList
     * @param registGroup
     * @param registGroupList
     * @param user
     */
    public void mergeTeam(int mergeType, List<Regist> registList, RegistGroup registGroup, List<RegistGroup> registGroupList, TbUserSession user) throws Exception ;

    /**
     *  团队挂账
     * @param registGroup
     * @param registList
     * @param updaRegistPerson
     * @param deleteRelations
     * @param roomInfos
     */
    public void teamOnAccount(RegistGroup registGroup, List<Regist> registList, List<RegistPerson> updaRegistPerson, List<RoomAuxiliaryRelation> deleteRelations, List<RoomInfo> roomInfos ,List<RoomRepairRecordHistory> roomRepairRecordHistories) throws Exception;


}
