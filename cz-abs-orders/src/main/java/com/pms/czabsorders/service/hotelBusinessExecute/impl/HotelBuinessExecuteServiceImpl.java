package com.pms.czabsorders.service.hotelBusinessExecute.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.service.hotelBusinessExecute.HotelBuinessExecuteService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomType;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeSearch;
import com.pms.czhotelfoundation.bean.setting.HotelInitialData;
import com.pms.czhotelfoundation.bean.setting.search.HotelInitialDataSearch;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czhotelfoundation.dao.setting.HotelInitialDataDao;
import com.pms.czmembership.bean.company.HotelCompanyAccount;
import com.pms.czmembership.bean.company.HotelCompanyInfo;
import com.pms.czmembership.bean.company.search.HotelCompanyAccountSearch;
import com.pms.czmembership.bean.company.search.HotelCompanyInfoSearch;
import com.pms.czmembership.bean.member.CardInfo;
import com.pms.czmembership.bean.member.CardLevel;
import com.pms.czmembership.bean.member.CardType;
import com.pms.czmembership.bean.member.search.CardInfoSearch;
import com.pms.czmembership.bean.member.search.CardLevelSearch;
import com.pms.czmembership.bean.member.search.CardTypeSearch;
import com.pms.czmembership.dao.company.HotelCompanyAccountDao;
import com.pms.czmembership.dao.company.HotelCompanyAccountInfoDao;
import com.pms.czmembership.dao.company.HotelCompanyInfoDao;
import com.pms.czmembership.dao.member.CardInfoDao;
import com.pms.czmembership.dao.member.CardLevelDao;
import com.pms.czmembership.dao.member.CardTypeDao;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.request.HotelDataClearRequest;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.search.BookingOrderSearch;
import com.pms.pmsorder.bean.search.RegistPersonSearch;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.dao.BookingOrderDao;
import com.pms.pmsorder.dao.RegistDao;
import com.pms.pmsorder.dao.RegistPersonDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Primary
@Slf4j
public class HotelBuinessExecuteServiceImpl implements HotelBuinessExecuteService {

    @Autowired
    private HotelInitialDataDao hotelInitialDataDao;

    @Autowired
    private RoomTypeDao roomTypeDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private HotelCompanyInfoDao hotelCompanyInfoDao;

    @Autowired
    private HotelCompanyAccountDao hotelCompanyAccountDao;

    @Autowired
    private HotelCompanyAccountInfoDao hotelCompanyAccountInfoDao;

    @Autowired
    private CardInfoDao cardInfoDao;

    @Autowired
    private CardTypeDao cardTypeDao;

    @Autowired
    private CardLevelDao cardLevelDao;


    @Override
    public ResponseData hotelDataClear(HotelDataClearRequest hotelDataClearRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (hotelDataClearRequest.getExecuteBusinessList() == null || hotelDataClearRequest.getExecuteBusinessList().size() < 1) {
                throw new Exception(HOTEL_CONST.DATA_LIST_IS_NULL);
            }

            if (hotelDataClearRequest.getHid() == null || hotelDataClearRequest.getHid() < 1) {
                throw new Exception(HOTEL_CONST.DATA_LIST_IS_NULL);
            }

            Integer hid = hotelDataClearRequest.getHid();
            Integer result = 0;


            List<HotelInitialData> hotelInitialDataListDel = new ArrayList<>();
            List<RoomInfo> roomInfoListDel = new ArrayList<>();
            List<RoomType> roomTypeListDel = new ArrayList<>();
            List<Account> accountListDel = new ArrayList<>();
            List<Regist> registListDel = new ArrayList<>();
            List<RegistPerson> registPersonListDel = new ArrayList<>();

            List<HotelCompanyInfo> hotelCompanyInfoListDel = new ArrayList<>();
            List<HotelCompanyAccount> hotelCompanyAccountListDel = new ArrayList<>();

            List<CardType> cardTypeListDel = new ArrayList<>();
            List<CardLevel> cardLevelListDel = new ArrayList<>();
            List<CardInfo> cardInfoListDel = new ArrayList<>();

            List<BookingOrder> bookingOrderListDel = new ArrayList<>();

            for (int i = 0; i < hotelDataClearRequest.getExecuteBusinessList().size(); i++) {
                Integer businessType = hotelDataClearRequest.getExecuteBusinessList().get(i);

                /**
                 * 楼栋楼层删除
                 */
                if (businessType.equals(HOTEL_CONST.HOTEL_DATA_DEL_BORF)) {
                    HotelInitialDataSearch hotelInitialDataSearch = new HotelInitialDataSearch();
                    hotelInitialDataSearch.setHid(hid);
                    Page<HotelInitialData> hotelInitialDataList = hotelInitialDataDao.selectBySearch(hotelInitialDataSearch);
                    for (int j = 0; j < hotelInitialDataList.size(); j++) {
                        if (hotelInitialDataList.get(j).getValueType().equals(1) || hotelInitialDataList.get(j).getValueType().equals(2)) {
                            HotelInitialData hotelInitialData = new HotelInitialData();
                            hotelInitialData.setInitialId(hotelInitialDataList.get(j).getInitialId());
                            hotelInitialData.setHid(0 - hid);
                            hotelInitialData.setHotelGroupId(0 - hotelInitialDataList.get(j).getHotelGroupId());
                            hotelInitialDataListDel.add(hotelInitialData);
                        }
                    }

                }
                /**
                 * 房型、房间数据
                 */
                else if (businessType.equals(HOTEL_CONST.HOTEL_DATA_DEL_ROOMTYPEORROOM)) {
                    RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
                    roomTypeSearch.setHid(hid);
                    Page<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);
                    for (int j = 0; j < roomTypes.size(); j++) {
                        RoomType roomType = new RoomType();
                        roomType.setRoomTypeId(roomTypes.get(j).getRoomTypeId());
                        roomType.setHid(0 - hid);
                        roomType.setHotelGroupId(0 - roomTypes.get(j).getHotelGroupId());
                        roomTypeListDel.add(roomType);
                    }

                    RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
                    roomInfoSearch.setHid(hid);
                    List<RoomInfo> roomInfoList = roomInfoDao.selectBySearch(roomInfoSearch);

                    for (int j = 0; j < roomInfoList.size(); j++) {
                        RoomInfo roomInfo = new RoomInfo();
                        roomInfo.setRoomInfoId(roomInfoList.get(j).getRoomInfoId());
                        roomInfo.setHid(0 - hid);
                        roomInfo.setHotelGroupId(0 - roomInfoList.get(j).getHotelGroupId());
                        roomInfoListDel.add(roomInfo);
                    }
                }
                /**
                 * 账务数据
                 */
                else if (businessType.equals(HOTEL_CONST.HOTEL_DATA_DEL_ACCOUNT)) {
                    AccountSearch accountSearch = new AccountSearch();
                    accountSearch.setHid(hid);
                    List<Account> accounts = accountDao.selectBySearch(accountSearch);
                    for (int j = 0; j < accounts.size(); j++) {
                        Account account = new Account();
                        account.setHid(0 - hid);
                        account.setHotelGroupId(0 - accounts.get(j).getHotelGroupId());
                        account.setAccountId(accounts.get(j).getAccountId());
                        accountListDel.add(account);
                    }
                }
                /**
                 * 入住数据
                 */
                else if (businessType.equals(HOTEL_CONST.HOTEL_DATA_DEL_REGIST)) {
                    RegistSearch registSearch = new RegistSearch();
                    registSearch.setHid(hid);
                    List<Regist> regists = registDao.selectBySearch(registSearch);
                    for (int j = 0; j < regists.size(); j++) {
                        Regist regist = new Regist();
                        regist.setRegistId(regists.get(j).getRegistId());
                        regist.setHid(0 - hid);
                        regist.setHotelGroupId(0 - regists.get(j).getHotelGroupId());
                        registListDel.add(regist);
                    }
                }

                /**
                 * 宾客数据
                 */
                else if (businessType.equals(HOTEL_CONST.HOTEL_DATA_DEL_GUEST)) {
                    RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                    registPersonSearch.setHid(hid);
                    List<RegistPerson> registPersons = registPersonDao.selectBySearch(registPersonSearch);
                    for (int j = 0; j < registPersons.size(); j++) {
                        RegistPerson registPerson = new RegistPerson();
                        registPerson.setRegistPersonId(registPersons.get(j).getRegistPersonId());
                        registPerson.setHid(0 - hid);
                        registPerson.setHotelGroupId(0 - registPersons.get(j).getHotelGroupId());
                        registPersonListDel.add(registPerson);
                    }
                }

                /**
                 * 协议单位
                 */
                else if (businessType.equals(HOTEL_CONST.HOTEL_DATA_DEL_COMPANY)) {
                    HotelCompanyInfoSearch hotelCompanyInfoSearch = new HotelCompanyInfoSearch();
                    hotelCompanyInfoSearch.setHid(hid);
                    List<HotelCompanyInfo> hotelCompanyInfos = hotelCompanyInfoDao.selectBySearch(hotelCompanyInfoSearch);
                    hotelCompanyInfos.forEach(a -> {
                        HotelCompanyInfo hotelCompanyInfo = new HotelCompanyInfo();
                        hotelCompanyInfo.setId(a.getId());
                        hotelCompanyInfo.setHid(0 - hid);
                        hotelCompanyInfo.setHotelGroupId(0 - a.getHotelGroupId());
                        hotelCompanyInfoListDel.add(hotelCompanyInfo);
                    });

                    HotelCompanyAccountSearch hotelCompanyAccountSearch = new HotelCompanyAccountSearch();
                    hotelCompanyAccountSearch.setHid(hid);

                    Page<HotelCompanyAccount> hotelCompanyAccounts = hotelCompanyAccountDao.selectBySearch(hotelCompanyAccountSearch);

                    hotelCompanyAccounts.forEach(a -> {
                        HotelCompanyAccount hotelCompanyAccount = new HotelCompanyAccount();
                        hotelCompanyAccount.setId(a.getId());
                        hotelCompanyAccount.setHid(0 - hid);
                        hotelCompanyAccount.setHotelGroupId(0 - a.getHotelGroupId());
                        hotelCompanyAccountListDel.add(hotelCompanyAccount);
                    });
                }

                /**
                 * 会员数据
                 */
                else if (businessType.equals(HOTEL_CONST.HOTEL_DATA_DEL_MEMBER)) {
                    CardInfoSearch cardInfoSearch = new CardInfoSearch();
                    cardInfoSearch.setHid(hid);

                    List<CardInfo> cardInfos = cardInfoDao.selectBySearch(cardInfoSearch);

                    cardInfos.forEach(a -> {
                        CardInfo cardInfo = new CardInfo();
                        cardInfo.setHid(0 - hid);
                        cardInfo.setHotelGroupId(0 - a.getHotelGroupId());
                        cardInfo.setId(a.getId());
                        cardInfoListDel.add(cardInfo);
                    });


                    CardTypeSearch cardTypeSearch = new CardTypeSearch();
                    cardTypeSearch.setHid(hid);

                    List<CardType> cardTypeList = cardTypeDao.selectBySearch(cardTypeSearch);

                    cardTypeList.forEach(a -> {
                        CardType cardType = new CardType();
                        cardType.setId(a.getId());
                        cardType.setHid(0 - hid);
                        cardType.setHotelGroupId(0 - a.getHotelGroupId());

                        cardTypeListDel.add(cardType);
                    });


                    CardLevelSearch cardLevelSearch = new CardLevelSearch();
                    cardLevelSearch.setHid(hid);

                    List<CardLevel> cardLevelList = cardLevelDao.selectBySearch(cardLevelSearch);
                    cardLevelList.forEach(a -> {
                        CardLevel cardLevel = new CardLevel();
                        cardLevel.setHid(0 - hid);
                        cardLevel.setHotelGroupId(0 - a.getHotelGroupId());
                        cardLevel.setId(a.getId());
                        cardLevelListDel.add(cardLevel);
                    });
                }

                /**
                 * 订单数据
                 */
                else if (businessType.equals(HOTEL_CONST.HOTEL_DATA_DEL_ORDER)) {
                    BookingOrderSearch bookingOrderSearch = new BookingOrderSearch();
                    bookingOrderSearch.setHid(hid);
                    List<BookingOrder> bookingOrders = bookingOrderDao.selectBySearch(bookingOrderSearch);
                    bookingOrders.forEach(a -> {
                        BookingOrder bookingOrder = new BookingOrder();
                        bookingOrder.setBookingOrderId(a.getBookingOrderId());
                        bookingOrder.setHid(0 - hid);
                        bookingOrder.setHotelGroupId(0 - a.getHotelGroupId());
                        bookingOrderListDel.add(bookingOrder);
                    });


                }
                /**
                 * 零售数据
                 */
                else if (businessType.equals(HOTEL_CONST.HOTEL_DATA_DEL_LS)) {

                }
            }
            executeHotelDataClearBusiness(hotelInitialDataListDel, roomTypeListDel, roomInfoListDel, accountListDel, registListDel, registPersonListDel, hotelCompanyInfoListDel, hotelCompanyAccountListDel, cardTypeListDel, cardLevelListDel, cardInfoListDel, bookingOrderListDel);

        } catch (Exception e) {
            log.error("业务处理异常",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void executeHotelDataClearBusiness(List<HotelInitialData> hotelInitialDataList, List<RoomType> roomTypeList, List<RoomInfo> roomInfoList, List<Account> accountList, List<Regist> registList, List<RegistPerson> registPersonList, List<HotelCompanyInfo> hotelCompanyInfoList, List<HotelCompanyAccount> hotelCompanyAccountList, List<CardType> cardTypeList, List<CardLevel> cardLevelList, List<CardInfo> cardInfoList, List<BookingOrder> bookingOrderList) throws Exception {
        log.info("hotelInitialDataList:" + hotelInitialDataList.size());
        log.info("roomTypeList:" + roomTypeList.size());
        log.info("roomInfoList:" + roomInfoList.size());
        log.info("accountList:" + accountList.size());
        log.info("registList:" + registList.size());
        log.info("registPersonList:" + registPersonList.size());
        log.info("hotelCompanyInfoList:" + hotelCompanyInfoList.size());
        log.info("hotelCompanyAccountList:" + hotelCompanyAccountList.size());
        log.info("cardTypeList:" + cardTypeList.size());
        log.info("cardLevelList:" + cardLevelList.size());
        log.info("cardInfoList:" + cardInfoList.size());
        log.info("bookingOrderList:" + bookingOrderList.size());
        Integer result = 0;
        for (int i = 0; i < hotelInitialDataList.size(); i++) {
            result = hotelInitialDataDao.editHotelInitialData(hotelInitialDataList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        for (int i = 0; i < roomTypeList.size(); i++) {
            result = roomTypeDao.editRoomType(roomTypeList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        for (int i = 0; i < roomInfoList.size(); i++) {
            result = roomInfoDao.editRoomInfo(roomInfoList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        for (int i = 0; i < accountList.size(); i++) {
            result = accountDao.editAccount(accountList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        for (int i = 0; i < registList.size(); i++) {
            result = registDao.update(registList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }


        for (int i = 0; i < registPersonList.size(); i++) {
            result = registPersonDao.update(registPersonList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        for (int i = 0; i < hotelCompanyInfoList.size(); i++) {
            result = hotelCompanyInfoDao.editHotelCompanyInfo(hotelCompanyInfoList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        for (int i = 0; i < hotelCompanyAccountList.size(); i++) {
            result = hotelCompanyAccountDao.update(hotelCompanyAccountList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }


        for (int i = 0; i < cardTypeList.size(); i++) {
            result = cardTypeDao.editCardType(cardTypeList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        for (int i = 0; i < cardInfoList.size(); i++) {
            result = cardInfoDao.update(cardInfoList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

        for (int i = 0; i < cardLevelList.size(); i++) {
            result = cardLevelDao.editCardLevel(cardLevelList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }
        for (int i = 0; i < bookingOrderList.size(); i++) {
            result = bookingOrderDao.editBookingOrder(bookingOrderList.get(i));
            if (result < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        }

    }
}
