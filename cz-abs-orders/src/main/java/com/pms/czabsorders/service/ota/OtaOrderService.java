package com.pms.czabsorders.service.ota;

import com.pms.czhotelfoundation.bean.ota.OtaOrderDailyPrice;
import com.pms.czhotelfoundation.bean.ota.OtaPmsOrderInfo;
import com.pms.czhotelfoundation.bean.ota.OtaOrderRoomUpdateInfo;
import com.pms.czhotelfoundation.bean.ota.OtaOrderStatusUpdate;
import com.pms.czpmsutils.ResponseData;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface OtaOrderService {

    /**
     * 通过订单sn查询预订单信息
     * **/
    OtaPmsOrderInfo getOtaOrderInfoBySn(String orderSn);

    /**
     * 接收ota过来得创建订单信息，创建订单
     * **/
    ResponseData  createOtaOrder(OtaPmsOrderInfo otaOrderInfo);

    /**
     * 接收ota过来得创建订单信息，创建订单
     * **/
    ResponseData  updateOtaOrderStatus(OtaOrderStatusUpdate statusUpdate);

    /***
     * 更新房间间夜
     * **/
    ResponseData  upaBookRoomNightAndNumFunc(OtaOrderRoomUpdateInfo orderRoomUpdateInfo);


    List<OtaOrderDailyPrice> getDailyPriceListByOrderList(List<Integer> orderIdList);


}
