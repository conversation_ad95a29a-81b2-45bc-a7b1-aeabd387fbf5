package com.pms.czabsorders.service.mini;

import com.pms.czabsorders.bean.mini.UpdateWxMsg;
import com.pms.czabsorders.bean.mini.WechatPhoneParam;
import com.pms.czmembership.bean.member.search.CardGroupUrlSearch;
import com.pms.czmembership.bean.member.search.CardRechargePlanDetailsSearch;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.AddCRechargeRequest;
import net.sf.json.JSONObject;

/**
 * 微信相关的接口
 */
public interface WxMemberService {

    /**
     * 查询会员信息
     * @param param
     * @return
     */
    public ResponseData findWxVip(JSONObject param);

    /**
     * 查询会员信息
     * @param param
     * @return
     */
    public ResponseData getWxPhone(WechatPhoneParam param);

    /**
     * 注册会员信息
     * @param param
     * @return
     */
    public ResponseData registWxVip(JSONObject param);

    /**
     * 修改微信信息
     * @param param
     * @return
     */
    public ResponseData updateWxMsg(UpdateWxMsg param);

    /**
     * 查询当前会员的在住信息
     * @param param
     * @return
     */
    public ResponseData searchCheckIn(JSONObject param);

    /**
     * 查询绑定的会员信息
     * @param cardGroupUrlSearch
     * @return
     */
    public ResponseData getBindVipMsg(CardGroupUrlSearch cardGroupUrlSearch);

    /**
     * 绑定会员信息
     * @return
     */
    public ResponseData bindVip(CardGroupUrlSearch cardGroupUrlSearch);

    /**
     * 查询会员储值计划
     * @param cardRechargePlanDetailsSearch
     * @return
     */
    public ResponseData selectRechargePlan(CardRechargePlanDetailsSearch cardRechargePlanDetailsSearch);


    /**
     * 会员储值信息
     * @param cardRechargeSearch
     * @return
     */
    public ResponseData addCRecharge(AddCRechargeRequest addCRechargeRequest);

}
