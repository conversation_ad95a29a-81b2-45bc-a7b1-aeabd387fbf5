package com.pms.czabsorders.service.roomstateimg;

import com.pms.czpmsutils.ResponseData;
import com.pms.pmsorder.bean.request.RegistPageRequest;
import net.sf.json.JSONObject;

/**
 *  房态图
 */
public interface RoomStateImgService {


    /**
     * 查询房态图
     * @param param
     * @return
     */
    public ResponseData searchRoomStateBag(JSONObject param);

    /**
     * 查询房态图
     * @param param
     * @return
     */
    public ResponseData searchRoomStateBagTwo(JSONObject param);

    /**
     * 统计当前酒店实时数据
     * @param registSearch
     * @return
     */
    public ResponseData roomDayData(RegistPageRequest registSearch);

}
