package com.pms.czabsorders.service.checkin.transaction.impl;


import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.CheckInRegist;
import com.pms.czabsorders.service.checkin.transaction.CheckInTransactionService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountThirdPayRecode;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czaccount.dao.account.AccountThirdPayRecodeDao;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomRepairRecordHistory;
import com.pms.czhotelfoundation.dao.HourRoomDayUseDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomCheckRecordDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomRepairRecordHistoryDao;
import com.pms.czhotelfoundation.service.room.transaction.RoomTransactionService;
import com.pms.czhotelfoundation.service.zimg.ZimgService;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.conf.HotelIotPlatConfig;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.constant.Iot;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.room.RoomUtils;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.*;
import com.pms.czpmsutils.thirdauth.HotelIotStrategy;
import com.pms.czpmsutils.view.AESUtil;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.search.PersonInfoSearch;
import com.pms.pmsorder.dao.*;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * 入住相关的事务操作
 */
@Service
@Primary
public class CheckInTransactionServiceImpl extends BaseService implements CheckInTransactionService {

    private static final Logger log = LoggerFactory.getLogger(CheckInTransactionService.class);

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private RegistGroupDao registGroupDao;

    @Autowired
    private ZimgService zimgService;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private AccountThirdPayRecodeDao accountThirdPayRecodeDao;

    @Autowired
    private RoomRepairRecordHistoryDao roomRepairRecordDao;

    @Autowired
    private RegistChangeRecordDao registChangeRecordDao;

    @Autowired
    private PersonInfoDao personInfoDao;

    @Autowired
    private HourRoomDayUseDao hourRoomDayUseDao;

    @Resource
    private HotelIotPlatConfig iotPlatConfig;

    @Resource
    private WebClientUtil webClientUtil;



    /**
     * 预定转入住
     *
     * @param bookingOrder                    预订单
     * @param bookingOrderRoomNumList         预定房间
     * @param checkInRegistMap                入住单信息
     * @param roomInfoList                    房间集合
     * @param registPersonMap                 入住人集合
     * @param bookingOrderConfigMap           入住配置信息
     * @param deleteRoomAuxiliaryRelationList 需要删除的辅助房态信息
     * @param addRoomAuxiliaryRelation        需要添加的辅助房态信息
     * @param registGroup                     团队信息
     * @param accounts                        账务信息
     * @param user                            登录信息
     * @param dayPriceMap                     价格信息
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public Map<Integer, Integer> bookingCheckInTransaction(BookingOrder bookingOrder, List<BookingOrderRoomNum> bookingOrderRoomNumList, Map<Integer, Regist> checkInRegistMap,
                                                           List<RoomInfo> roomInfoList, Map<Integer, List<RegistPerson>> registPersonMap, Map<Integer, BookingOrderConfig> bookingOrderConfigMap,
                                                           List<RoomAuxiliaryRelation> deleteRoomAuxiliaryRelationList, Map<Integer, List<RoomAuxiliaryRelation>> addRoomAuxiliaryRelation, RegistGroup registGroup,
                                                           List<Account> accounts, TbUserSession user, Map<Integer, List<BookingOrderDailyPrice>> dayPriceMap) throws Exception {
        HashMap<Integer, Integer> registMap = new HashMap<>();

        final ArrayList<Oprecord> oprecords = new ArrayList<>();
        Oprecord oprecord = new Oprecord(user);

        // 1.判断是否是团队，指定主账房
        //   第一个登记单为主账房
        //   registGroup === id : -2 需要注册团队信息 ，-1 不需要添加 团队信息  ，>0 说明已创建过团队信息
        Regist mainRegist = new Regist();
        boolean isMain = false;
        boolean needAccount = false;  //如果当前预定为第一次入住，则需把账务转入到主账房

        if (registGroup.getTeamType() == -1) {
            isMain = true;
            needAccount = true;
        }

        if (registGroup.getTeamType() == -2) {
            registGroup.setRegistGroupId(null);
            Integer insert = registGroupDao.insert(registGroup);

            if (insert < 1) {
                throw new Exception("创建订单团队信息失败。");
            }

            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setBcodeO(registGroup.getRegistGroupId() + "");

            oprecord.setDescription("创建预定团队记录。预订单号:" + bookingOrder.getSn());

            oprecords.add(oprecord);

            isMain = true;
            needAccount = true;

        }

        // 修改预订单状态
        bookingOrderDao.editBookingOrder(bookingOrder);

        // 2.遍历预定房型表，进行入住操作
        String currentTime = HotelUtils.currentTime();

        final CountDownLatch cdOrder = new CountDownLatch(1);
        final CountDownLatch cdAnswer = new CountDownLatch(3);

        for (BookingOrderRoomNum roomNum : bookingOrderRoomNumList) {
            Date date = new Date();
            // 2.1 入住，保存登记记录
            Regist regist = checkInRegistMap.get(roomNum.getRoomNumId());
            if (needAccount) {
                regist.setSumPay(registGroup.getSumPay());
                regist.setSumSale(registGroup.getSumSales());
                needAccount = false;
            }
            if (registGroup.getRegistGroupId() != null && registGroup.getRegistGroupId() > 0) {
                regist.setSessionToken(user.getSessionId());
                regist.setRegistGroupId(registGroup.getRegistGroupId());
                regist.setTeamCodeId(registGroup.getRegistGroupId());
                regist.setTeamCodeName(registGroup.getGroupName());
                regist.setIsMainRoom(isMain ? 1 : 0);
            }

            Integer registInsert = registDao.insert(regist);
            if (registInsert < 1) {
                throw new Exception("添加入住信息失败。排房编号:" + roomNum.getId());
            }
            registMap.put(regist.getRoomNumId(), regist.getRegistId());

            // 如果为第一间房，则表位主账房
            if (isMain) {
                mainRegist = regist;
                isMain = false;
            }

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(currentTime);
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setBcodeO(registGroup.getRegistGroupId() + "");
            oprecord.setDescription("将 " + roomNum.getRoomNum() + " 的排房信息转为入住信息。");

            oprecords.add(oprecord);

            // 2.2 添加入住人
            List<RegistPerson> registPeople = registPersonMap.get(roomNum.getRoomNumId());
            for (RegistPerson registPerson : registPeople) {
                registPerson.setRegistId(regist.getRegistId());
                registPerson.setRegistPersonId(null);
                registPerson.setTeamCodeId(regist.getTeamCodeId());
                registPerson.setRegistState(0);
                registPersonDao.insert(registPerson);

                oprecord = new Oprecord(user);
                oprecord.setOccurTime(currentTime);
                oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
                oprecord.setRegistId(regist.getRegistId());
                oprecord.setBcodeO(registGroup.getRegistGroupId().toString());
                oprecord.setDescription(roomNum.getRoomNum() + " 房间添加入住人 : " + registPerson.getPersonName());

                oprecords.add(oprecord);

            }
            if (registPeople.size() < 1) {
                RegistPerson registPerson = new RegistPerson();
                registPerson.setRegistPersonId(null);
                registPerson.setHid(user.getHid());
                registPerson.setHotelGroupId(user.getHotelGroupId());
                registPerson.setRegistId(regist.getRegistId());
                registPerson.setClassId(user.getClassId());
                registPerson.setRegistState(0);
                registPerson.setPersonName("");
                registPerson.setRoomNum(regist.getRoomNum());
                registPerson.setRoomNumId(regist.getRoomNumId());
                registPerson.setRegistState(0);
                registPerson.setTeamCodeId(registGroup.getRegistGroupId());
                registPerson.setBookingOrderRoomNumId(roomNum.getId());
                registPerson.setIsOther(0);
                registPerson.setStartTime(new Date());
                registPersonDao.insert(registPerson);


            }

            // 2.3 将预定排房表改成已入住的状态
            roomNum.setIsCheckin(1);
            roomNum.setUpdateTime(date);
            roomNum.setUpdateUserId(user.getUserId());
            roomNum.setRegistId(regist.getRegistId());

            Integer integer = bookingOrderRoomNumDao.editBookingOrderRoomNum(roomNum);

            if (integer < 1) {
                throw new Exception("将 " + roomNum.getRoomNum() + " 改为入住状态失败，预定编号:" + bookingOrder.getSn());
            }

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(currentTime);
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setBcodeO(registGroup.getRegistGroupId() + "");
            oprecord.setDescription("将 " + roomNum.getRoomNum() + " 的排房信息转为入住完成。");

            oprecords.add(oprecord);

            // 2.4 添加辅助房态
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = addRoomAuxiliaryRelation.get(roomNum.getRoomNumId());

            for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {
                roomAuxiliaryRelation.setRelationId(null);
                roomAuxiliaryRelation.setRegistId(regist.getRegistId());
                roomAuxiliaryRelationDao.saveRoomAuxiliaryRelation(roomAuxiliaryRelation);
            }

            // 2.5 添加配置信息
            BookingOrderConfig bookingOrderConfig = bookingOrderConfigMap.get(roomNum.getRoomNumId());
            bookingOrderConfig.setId(null);
            bookingOrderConfig.setBookingOrderId(null);
            bookingOrderConfig.setRegistId(regist.getRegistId());
            Integer saveBookingOrderConfig = bookingOrderConfigDao.saveBookingOrderConfig(bookingOrderConfig);

            if (saveBookingOrderConfig < 1) {
                throw new Exception("添加设置信息失败。排房编号:" + roomNum.getId());
            }

            // 2.6 添加房价信息
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = dayPriceMap.get(roomNum.getRoomNumId());
            for (BookingOrderDailyPrice bookingOrderDailyPrice : bookingOrderDailyPrices) {
                bookingOrderDailyPrice.setRegistId(regist.getRegistId());
                bookingOrderDailyPrice.setBreakNum(regist.getBreakfastNum());
                Integer integer1 = bookingOrderDailyPriceDao.editBookingOrderDailyPrice(bookingOrderDailyPrice);

                if (integer1 < 1) {
                    throw new Exception("添加价格失败，编号:" + bookingOrderDailyPrice.getId());
                }

            }

        }

        // 3. 添加账务信息
        if (needAccount) {
            currentTime = HotelUtils.currentTime();

            String accountDescr = "";
            for (Account account : accounts) {

                account.setRegistId(mainRegist.getRegistId());
                account.setRoomInfoId(mainRegist.getRoomNumId());
                account.setRoomNum(mainRegist.getRoomNum());
                account.setRoomTypeId(mainRegist.getRoomTypeId());
                account.setUpdateTime(new Date());
                account.setUpdateUserId(user.getUserId());
                account.setUpdateUserName(user.getUserName());

                Integer integer = accountDao.editAccount(account);

                if (integer < 1) {
                    throw new Exception("将预订账务:" + account.getAccountId() + " 转入房间:" + mainRegist.getRoomNum() + " 失败。");
                }

                accountDescr += account.getAccountId() + ",";
            }

            if (!StringUtil.isEmpty(accountDescr.toString())) {
                oprecord = new Oprecord(user);
                oprecord.setOccurTime(currentTime);
                oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
                oprecord.setRegistId(mainRegist.getRegistId());
                oprecord.setBcodeO(registGroup.getRegistGroupId() + "");
                oprecord.setDescription("将编号为  " + accountDescr + " 的预订单的账务转入到 : " + mainRegist.getRoomNum() + " 房间中");
                oprecords.add(oprecord);
            }
        }

        // 4.删除辅助房态
        for (RoomAuxiliaryRelation roomAuxiliaryRelation : deleteRoomAuxiliaryRelationList) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());
        }

        // 5.更改房间记录
        for (RoomInfo roomInfo : roomInfoList) {

            this.updateRoom(roomInfo, ROOM_STATUS.OCC, user, roomInfo.getRoomNum() + "入住成功。");

        }

        this.addOprecords(oprecords);

        return registMap;
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public Map<Integer, Integer> bookingCheckInTransactionNew(BookingOrder bookingOrder, List<BookingOrderRoomNum> bookingOrderRoomNumList, Map<Integer, Regist> checkInRegistMap,
                                                              List<RoomInfo> roomInfoList, Map<Integer, List<RegistPerson>> registPersonMap, Map<Integer, BookingOrderConfig> bookingOrderConfigMap,
                                                              List<RoomAuxiliaryRelation> deleteRoomAuxiliaryRelationList, Map<Integer, List<RoomAuxiliaryRelation>> addRoomAuxiliaryRelation, RegistGroup registGroup,
                                                              List<Account> accounts, TbUserSession user, Map<Integer, List<BookingOrderDailyPrice>> dayPriceMap) throws Exception {
        HashMap<Integer, Integer> registMap = new HashMap<>();

        final ArrayList<Oprecord> oprecords = new ArrayList<>();
        Oprecord oprecord = new Oprecord(user);

        // 1.判断是否是团队，指定主账房
        //   第一个登记单为主账房
        //   registGroup === id : -2 需要注册团队信息 ，-1 不需要添加 团队信息  ，>0 说明已创建过团队信息
        Regist mainRegist = new Regist();
        boolean isMain = false;
        boolean needAccount = false;  //如果当前预定为第一次入住，则需把账务转入到主账房

        if (registGroup.getTeamType() == -1) {
            isMain = true;
            needAccount = true;
        }

        if (registGroup.getTeamType() == -2) {
            registGroup.setRegistGroupId(null);
            Integer insert = registGroupDao.insert(registGroup);

            if (insert < 1) {
                throw new Exception("创建订单团队信息失败。");
            }

            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setBcodeO(registGroup.getRegistGroupId() + "");

            oprecord.setDescription("创建预定团队记录。预订单号:" + bookingOrder.getSn());

            oprecords.add(oprecord);

            isMain = true;
            needAccount = true;

        }

        // 修改预订单状态
        bookingOrderDao.editBookingOrder(bookingOrder);

        // 2.遍历预定房型表，进行入住操作
        String currentTime = HotelUtils.currentTime();

        final CountDownLatch cdOrder = new CountDownLatch(1);
        final CountDownLatch cdAnswer = new CountDownLatch(3);


        // 添加入住人
        ArrayList<RegistPerson> allRegistPeople = new ArrayList<>();
        ArrayList<BookingOrderRoomNum> allUpaOrderRoom = new ArrayList<>();
        ArrayList<RoomAuxiliaryRelation> allRoomAuxiliary = new ArrayList<>();
        ArrayList<BookingOrderDailyPrice> allUpaPriceList = new ArrayList<>();
        ArrayList<BookingOrderConfig> allBookingOrders = new ArrayList<>();
        ArrayList<RoomInfo> roomInfos = new ArrayList<>();
        ArrayList<RoomRepairRecordHistory> roomRepairRecordHistories = new ArrayList<>();


        for (BookingOrderRoomNum roomNum : bookingOrderRoomNumList) {
            Date date = new Date();
            // 2.1 入住，保存登记记录
            Regist regist = checkInRegistMap.get(roomNum.getRoomNumId());
            if (needAccount) {
                regist.setSumPay(registGroup.getSumPay());
                regist.setSumSale(registGroup.getSumSales());
                needAccount = false;
            }
            if (registGroup.getRegistGroupId() != null && registGroup.getRegistGroupId() > 0) {
                regist.setSessionToken(user.getSessionId());
                regist.setRegistGroupId(registGroup.getRegistGroupId());
                regist.setTeamCodeId(registGroup.getRegistGroupId());
                regist.setTeamCodeName(registGroup.getGroupName());
                regist.setIsMainRoom(isMain ? 1 : 0);
            }

            Integer registInsert = registDao.insert(regist);
            if (registInsert < 1) {
                throw new Exception("添加入住信息失败。排房编号:" + roomNum.getId());
            }
            registMap.put(regist.getRoomNumId(), regist.getRegistId());

            // 如果为第一间房，则表位主账房
            if (isMain) {
                mainRegist = regist;
                isMain = false;
            }

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(currentTime);
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setBcodeO(registGroup.getRegistGroupId() + "");
            oprecord.setDescription("将 " + roomNum.getRoomNum() + " 的排房信息转为入住信息。");

            oprecords.add(oprecord);

            // 2.2 添加入住人
            List<RegistPerson> registPeople = registPersonMap.get(roomNum.getRoomNumId());
            for (RegistPerson registPerson : registPeople) {
                registPerson.setRegistId(regist.getRegistId());
                registPerson.setRegistPersonId(null);
                registPerson.setTeamCodeId(regist.getTeamCodeId());
                registPerson.setRegistState(0);

                allRegistPeople.add(registPerson);

                oprecord = new Oprecord(user);
                oprecord.setOccurTime(currentTime);
                oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
                oprecord.setRegistId(regist.getRegistId());
                oprecord.setBcodeO(registGroup.getRegistGroupId() + "");
                oprecord.setDescription(roomNum.getRoomNum() + " 房间添加入住人 : " + registPerson.getPersonName());

                oprecords.add(oprecord);

            }
            if (registPeople.size() < 1) {
                RegistPerson registPerson = new RegistPerson();
                registPerson.setRegistPersonId(null);
                registPerson.setHid(user.getHid());
                registPerson.setHotelGroupId(user.getHotelGroupId());
                registPerson.setRegistId(regist.getRegistId());
                registPerson.setClassId(user.getClassId());
                registPerson.setRegistState(0);
                registPerson.setPersonName("");
                registPerson.setRoomNum(regist.getRoomNum());
                registPerson.setRoomNumId(regist.getRoomNumId());
                registPerson.setRegistState(0);
                registPerson.setTeamCodeId(registGroup.getRegistGroupId());
                registPerson.setBookingOrderRoomNumId(roomNum.getId());
                registPerson.setIsOther(0);
                registPerson.setStartTime(new Date());

                allRegistPeople.add(registPerson);

            }

            // 2.3 将预定排房表改成已入住的状态
            roomNum.setIsCheckin(1);
            roomNum.setUpdateTime(date);
            roomNum.setUpdateUserId(user.getUserId());
            roomNum.setRegistId(regist.getRegistId());

            allUpaOrderRoom.add(roomNum);

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(currentTime);
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setBcodeO(registGroup.getRegistGroupId() + "");
            oprecord.setDescription("将 " + roomNum.getRoomNum() + " 的排房信息转为入住完成。");

            oprecords.add(oprecord);

            // 2.4 添加辅助房态
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = addRoomAuxiliaryRelation.get(roomNum.getRoomNumId());

            for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {
                roomAuxiliaryRelation.setRelationId(null);
                roomAuxiliaryRelation.setRegistId(regist.getRegistId());
                allRoomAuxiliary.add(roomAuxiliaryRelation);
            }

            // 2.5 添加配置信息
            BookingOrderConfig bookingOrderConfig = bookingOrderConfigMap.get(roomNum.getRoomNumId());
            bookingOrderConfig.setId(null);
            bookingOrderConfig.setBookingOrderId(null);
            bookingOrderConfig.setRegistId(regist.getRegistId());

            // 批量修改
            allBookingOrders.add(bookingOrderConfig);

            // 2.6 添加房价信息
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = dayPriceMap.get(roomNum.getId());
            for (BookingOrderDailyPrice bookingOrderDailyPrice : bookingOrderDailyPrices) {
                bookingOrderDailyPrice.setRegistId(regist.getRegistId());
                bookingOrderDailyPrice.setBreakNum(regist.getBreakfastNum());

                allUpaPriceList.add(bookingOrderDailyPrice);

            }

            try {
                /**
                 * 预定转入住数据推送iot酒店
                 */
                IotHotelCheckIn iotHotelCheckIn = new IotHotelCheckIn();
                iotHotelCheckIn.setCheckOutTime(HotelUtils.parseDate2Str(regist.getCheckoutTime()));
                iotHotelCheckIn.setCode(regist.getRegistId() + "");
                iotHotelCheckIn.setPmsRoomId(regist.getRoomNumId() + "");
                iotHotelCheckIn.setGuests(allRegistPeople.stream().filter(item -> item.getRoomNumId().equals(regist.getRoomNumId())).map(
                        people -> {
                            IotHotelGuest hotelGuest = new IotHotelGuest();
                            hotelGuest.setIdentityCardNo(people.getIdCode());
                            hotelGuest.setName(people.getPersonName());
                            hotelGuest.setPhone(people.getPhone());
                            return hotelGuest;
                        }
                ).collect(Collectors.toList()));

                HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
                Map<String, String> authParamMap = new HashMap<>();
                authParamMap.put("eid", user.getHid().toString());
                authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
                authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
                authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());

                authParamMap.put("phone", AESUtil.encryptAesCbc(user.getPhone(),iotPlatConfig.getSassTokenSecret()));
                hotelIotStrategy.initStrategy(authParamMap);
                webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.CHECK_IN,
                        iotHotelCheckIn,
                        hotelIotStrategy);
            } catch (Exception e) {
                log.error("推送入住信息到Iot失败，异常信息{}",e);
            }
        }

        // 3. 添加账务信息
        currentTime = HotelUtils.currentTime();

        String accountDescr = "";
        for (Account account : accounts) {

            // 已添加账务不作处理
            Integer registId = account.getRegistId();
            if (registId != null && registId > 0) {
                continue;
            }
            account.setRegistPersonName("");
            account.setRegistPersonId(0);
            account.setRegistId(mainRegist.getRegistId());
            account.setRoomInfoId(mainRegist.getRoomNumId());
            account.setRoomNum(mainRegist.getRoomNum());
            account.setRoomTypeId(mainRegist.getRoomTypeId());
            account.setUpdateTime(new Date());
            account.setUpdateUserId(user.getUserId());
            account.setUpdateUserName(user.getUserName());

            Integer integer = accountDao.editAccount(account);

            if (integer < 1) {
                throw new Exception("将预订账务:" + account.getAccountId() + " 转入房间:" + mainRegist.getRoomNum() + " 失败。");
            }

            accountDescr += account.getAccountId() + ",";
        }

        if (!StringUtil.isEmpty(accountDescr.toString())) {
            oprecord = new Oprecord(user);
            oprecord.setOccurTime(currentTime);
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setRegistId(mainRegist.getRegistId());
            oprecord.setBcodeO(registGroup.getRegistGroupId() + "");
            oprecord.setDescription("将编号为  " + accountDescr + " 的预订单的账务转入到 : " + mainRegist.getRoomNum() + " 房间中");
            oprecords.add(oprecord);
        }
        // 4.删除辅助房态
        if (deleteRoomAuxiliaryRelationList.size() > 0) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(deleteRoomAuxiliaryRelationList);
        }

        if (allRoomAuxiliary.size() > 0) {
            roomAuxiliaryRelationDao.addRoomAuxiliaryRelationList(allRoomAuxiliary);
        }

        // 修改房价
        bookingOrderDailyPriceDao.updatePriceList(allUpaPriceList);

        // 批量修改预订信息
        HashMap<String, List> stringListHashMap = new HashMap<>();
        stringListHashMap.put("list", allUpaOrderRoom);
        bookingOrderRoomNumDao.updateBookingOrderRoomNumList(stringListHashMap);


        for (int i = 0; i < allRegistPeople.size(); i++) {
            registPersonDao.insert(allRegistPeople.get(i));
        }
        // 批量添加入住人
//        registPersonDao.addPeople(allRegistPeople);

        // 批量添加房间配置 bookingordercofig
        bookingOrderConfigDao.addBookingOrderConfigList(allBookingOrders);


        // 5.更改房间记录
        for (RoomInfo roomInfo : roomInfoList) {

            roomInfo.setRoomNumState(ROOM_STATUS.OCC);

            roomInfos.add(roomInfo);

            RoomRepairRecordHistory roomRepairRecord = new RoomRepairRecordHistory();

            roomRepairRecord.setRoomId(roomInfo.getRoomInfoId());
            roomRepairRecord.setRoomNum(roomInfo.getRoomNum());
            roomRepairRecord.setHid(roomInfo.getHid());
            roomRepairRecord.setHotelGroupId(roomInfo.getHotelGroupId());
            roomRepairRecord.setClassId(user.getClassId());
            roomRepairRecord.setRoomTypeId(roomInfo.getRoomTypeId());
            roomRepairRecord.setCreateUserId(user.getUserId());
            roomRepairRecord.setCreateUserName(user.getUserName());
            roomRepairRecord.setCreateTime(new Date());
            roomRepairRecord.setUpdateUserId(user.getUserId());
            roomRepairRecord.setUpdateUserName(user.getUserName());
            roomRepairRecord.setUpdateTime(new Date());
            roomRepairRecord.setType(RoomUtils.getRoomRepairRecordType(roomInfo.getRoomNumState(), ROOM_STATUS.OCC));
            roomRepairRecord.setState(RoomUtils.getRoomRepairRecordState(roomInfo.getRoomNumState(), ROOM_STATUS.OCC));
            roomRepairRecord.setBusinessDay(user.getBusinessDay());
            roomRepairRecord.setDes(roomInfo.getRoomNum() + "入住。");

            roomRepairRecordHistories.add(roomRepairRecord);

        }

        // 修改房间信息
        roomInfoDao.updateRoomList(roomInfos);

        // 添加房间修改记录
        roomRepairRecordDao.addRoomRepairRecordHistoryList(roomRepairRecordHistories);
        this.addOprecords(oprecords);
        return registMap;
    }

    public void updateRoom(RoomInfo roomInfo, Integer newState, TbUserSession user, String desc) throws Exception {

        roomInfo.setRoomNumState(newState);
        Integer integer = roomInfoDao.editRoomInfo(roomInfo);

        if (integer < 1) {
            throw new Exception(roomInfo.getRoomNum() + "修改为在住失败");
        }
        RoomRepairRecordHistory roomRepairRecord = new RoomRepairRecordHistory();

        roomRepairRecord.setRoomId(roomInfo.getRoomInfoId());
        roomRepairRecord.setRoomNum(roomInfo.getRoomNum());
        roomRepairRecord.setHid(roomInfo.getHid());
        roomRepairRecord.setHotelGroupId(roomInfo.getHotelGroupId());
        roomRepairRecord.setClassId(user.getClassId());
        roomRepairRecord.setCreateUserId(user.getUserId());
        roomRepairRecord.setCreateUserName(user.getUserName());
        roomRepairRecord.setCreateTime(new Date());
        roomRepairRecord.setUpdateUserId(user.getUserId());
        roomRepairRecord.setUpdateUserName(user.getUserName());
        roomRepairRecord.setUpdateTime(new Date());
        roomRepairRecord.setType(RoomUtils.getRoomRepairRecordType(roomInfo.getRoomNumState(), newState));
        roomRepairRecord.setState(RoomUtils.getRoomRepairRecordState(roomInfo.getRoomNumState(), newState));
        roomRepairRecord.setBusinessDay(user.getBusinessDay());
        roomRepairRecord.setDes(desc);
        roomRepairRecordDao.insert(roomRepairRecord);

    }


    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public void updateGuestInfoTransaction(RegistPerson registPerson, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, TbUserSession user, ArrayList<Oprecord> oprecords, Oprecord oprecord) throws Exception {

        Integer update;
        update = registPersonDao.update(registPerson);

        if (update < 1) {
            throw new Exception("更新宾客信息失败");
        }
        oprecord.setHid(user.getHid());
        oprecord.setRegistId(registPerson.getRegistId());
        oprecord.setDescription("修改宾客信息:" + registPerson.getPersonName() + ":成功");
        oprecords.add(oprecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void changeRoomTransaction(Regist regist, List<RegistPerson> registPersonList, List<RoomInfo> roomInfos, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, List<BookingOrderDailyPrice> bookingOrderDailyPrices, BookingOrderRoomNum bookingOrderRoomNum, RegistChangeRecord registChangeRecord, TbUserSession user, ArrayList<Oprecord> oprecords, Oprecord oprecord) throws Exception {
        Integer update;
        update = registDao.update(regist);
        if (update < 1) {
            throw new Exception("更新登记单失败");
        }

        oprecord = new Oprecord(user);
        oprecord.setHid(user.getHid());
        oprecord.setRegistId(regist.getRegistId());
        oprecord.setType(HOTEL_CONST.LOG_HF);
        oprecord.setDescription("换房修改登记单信息，新房号:" + regist.getRoomNum());
        oprecords.add(oprecord);

        for (int i = 0; i < roomInfos.size(); i++) {
            RoomInfo roomInfo = roomInfos.get(i);
            update = roomInfoDao.editRoomInfo(roomInfo);
            if (update < 1) {
                throw new Exception("更新房间信息失败");
            }
            oprecord = new Oprecord(user);
            oprecord.setType(HOTEL_CONST.LOG_HF);
            oprecord.setHid(user.getHid());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setDescription("更新房间" + roomInfo.getRoomNum() + "状态为:" + HotelUtils.getRoomState(roomInfo.getRoomNumState()));
            oprecords.add(oprecord);
        }


        for (int i = 0; i < registPersonList.size(); i++) {
            update = registPersonDao.update(registPersonList.get(i));
            if (update < 1) {
                throw new Exception("更新入住人信息失败");
            }
            oprecord = new Oprecord(user);
            oprecord.setType(HOTEL_CONST.LOG_HF);
            oprecord.setHid(user.getHid());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setDescription("将入住人:" + registPersonList.get(i).getPersonName() + "变更到了" + registPersonList.get(i).getRoomNum() + "房间");
            oprecords.add(oprecord);
        }


        /**
         * 如果之前有请求查房，查房中，查房完毕的辅助标签则删除
         */
        for (int i = 0; i < roomAuxiliaryRelations.size(); i++) {
            RoomAuxiliaryRelation roomAuxiliaryRelation = roomAuxiliaryRelations.get(i);
            Integer roomAuxiliaryId = roomAuxiliaryRelation.getRoomAuxiliaryId();
            if (roomAuxiliaryId == 14) {
                update = roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());
            } else {
                if (roomAuxiliaryId != 12 && roomAuxiliaryId != 13) {
                    update = roomAuxiliaryRelationDao.editRoomAuxiliaryRelation(roomAuxiliaryRelation);
                }
            }
            if (update < 1) {
                throw new Exception("更新辅助房态失败了");
            }
        }

        for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
            BookingOrderDailyPrice bookingOrderDailyPrice = bookingOrderDailyPrices.get(i);
            update = bookingOrderDailyPriceDao.editBookingOrderDailyPrice(bookingOrderDailyPrice);
            if (update < 1) {
                throw new Exception("更新每日房价失败");
            }
        }

        if (bookingOrderRoomNum.getRoomNumId() != null && bookingOrderRoomNum.getRoomNumId() > 0) {
            update = bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNum);
            if (update < 1) {
                throw new Exception("更新预订排房信息失败");
            }
        }

        update = registChangeRecordDao.insert(registChangeRecord);
        if (update < 1) {
            throw new Exception("插入换房历史记录失败");
        }
        /**
         * 数据推送iot酒店
         **/
        IotHotelRoomExchange roomExchange = new IotHotelRoomExchange();
        roomExchange.setCode(regist.getRegistId() + "");
        roomExchange.setPmsRoomId(regist.getRoomNumId() + "");
        HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
        Map<String, String> authParamMap = new HashMap<>();
        authParamMap.put("eid", user.getHid().toString());
        authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
        authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
        authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
        try {
            authParamMap.put("phone", AESUtil.encryptAesCbc(user.getPhone(),iotPlatConfig.getSassTokenSecret()));
        } catch (Exception e) {
            log.error("手机号加密失败");
        }
        hotelIotStrategy.initStrategy(authParamMap);

        webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.EXCHANGE,
                roomExchange,
                hotelIotStrategy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void updateRoomTypeTransaction(Regist regist, List<RegistPerson> registPersonList, List<RoomInfo> roomInfos, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, List<BookingOrderDailyPrice> bookingOrderDailyPrices, BookingOrderRoomNum bookingOrderRoomNum, RegistChangeRecord registChangeRecord, TbUserSession user, ArrayList<Oprecord> oprecords, Oprecord oprecord) throws Exception {
        Integer update;
        update = registDao.update(regist);
        if (update < 1) {
            throw new Exception("更新登记单失败");
        }
        oprecord.setHid(user.getHid());
        oprecord.setRegistId(regist.getRegistId());
        oprecord.setDescription("换房修改登记单信息，新房号:");
        oprecords.add(oprecord);

        for (int i = 0; i < roomInfos.size(); i++) {
            RoomInfo roomInfo = roomInfos.get(i);
            update = roomInfoDao.editRoomInfo(roomInfo);
            if (update < 1) {
                throw new Exception("更新房间信息失败");
            }
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setHid(user.getHid());
            oprecord.setDescription("更新房间" + roomInfo.getRoomNum() + "状态为" + roomInfo.getRoomNumState());
            oprecords.add(oprecord);
        }

        for (int i = 0; i < registPersonList.size(); i++) {
            update = registPersonDao.update(registPersonList.get(i));
            if (update < 1) {
                throw new Exception("更新入住人信息失败");
            }
            oprecord.setHid(user.getHid());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setDescription("将入住人" + registPersonList.get(i).getPersonName() + "变更到" + registPersonList.get(i).getRoomNum() + "房间失败");
            oprecords.add(oprecord);
        }


        /**
         * 如果之前有请求查房，查房中，查房完毕的辅助标签则删除
         */
        for (int i = 0; i < roomAuxiliaryRelations.size(); i++) {
            RoomAuxiliaryRelation roomAuxiliaryRelation = roomAuxiliaryRelations.get(i);
            Integer roomAuxiliaryId = roomAuxiliaryRelation.getRoomAuxiliaryId();
            if (roomAuxiliaryId == 14) {
                update = roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());
            } else {
                if (roomAuxiliaryId != 12 && roomAuxiliaryId != 13) {
                    update = roomAuxiliaryRelationDao.editRoomAuxiliaryRelation(roomAuxiliaryRelation);
                }
            }
            if (update < 1) {
                throw new Exception("更新辅助房态失败了");
            }
        }

        for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
            BookingOrderDailyPrice bookingOrderDailyPrice = bookingOrderDailyPrices.get(i);
            update = bookingOrderDailyPriceDao.editBookingOrderDailyPrice(bookingOrderDailyPrice);
            if (update < 1) {
                throw new Exception("更新每日房价失败");
            }
            oprecord.setHid(user.getHid());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setDescription("房型升级更新每日房价:" + bookingOrderDailyPrice.getDailyTime() + "为:" + bookingOrderDailyPrice.getPrice() / 100.0);
            oprecords.add(oprecord);
        }

        if (bookingOrderRoomNum.getRoomNumId() != null && bookingOrderRoomNum.getRoomNumId() > 0) {
            update = bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNum);
            if (update < 1) {
                throw new Exception("更新预订排房信息失败");
            }
        }

        update = registChangeRecordDao.insert(registChangeRecord);
        if (update < 1) {
            throw new Exception("插入换房历史记录失败");
        }
    }

    /**
     * 填充 RegistPerson 对象
     *
     * @param guestList
     * @param user
     * @param regist
     * @return
     * @throws Exception
     */
    @Override
    public ArrayList<RegistPerson> addCheckinGuest(JSONArray guestList, TbUserSession user, Regist regist, Integer bookingOrderRoomNumId) throws Exception {
        /**
         * 3.添加入住信息
         */
        ArrayList<RegistPerson> registPeople = new ArrayList<>();
        JSONObject imageData = new JSONObject();
        imageData.put(ER.SESSION_TOKEN, user.getSessionId());
        for (int i = 0; i < guestList.size(); i++) {
            JSONObject guest = guestList.getJSONObject(i);

            if (guest.get("personName") == null || guest.getString("personName").equals("") || guest.getString("personName").equals("null")) {
                continue;
            }
            String code = guest.containsKey("Code") ? guest.getString("Code") : guest.getString("idCode");

            RegistPerson person = new RegistPerson();
            person.setRegistState(0);
            person.setIdCode(code);
            if (guest.get("birthday") != null && !"".equals(guest.getString("birthday")) && !"null".equals(guest.getString("birthday"))) {
                int birthday = Integer.parseInt(guest.getString("birthday").replace("-", ""));
                person.setBirthday(birthday);
                person.setBirthYear(Integer.parseInt(String.valueOf(birthday).substring(0, 4)));
            } else {
                String birthday = code.length() >= 15 ? code.substring(6, 14) : "";
                String year = code.length() >= 15 ? code.substring(6, 10) : "";
                if (!birthday.equals("")) {
                    person.setBirthday(Integer.parseInt(birthday));
                }
                if (!year.equals("")) {
                    person.setBirthYear(Integer.parseInt(year));
                }
            }
            person.setRegistId(regist.getRegistId());
            person.setIdType(1);
            person.setSex(0);

            if (guest.get("sex") != null && !guest.getString("sex").equals("") && !guest.getString("sex").equals("null")) {
                person.setSex(guest.getInt("sex"));
            }

            person.setRegistPersonId(0);

            if (guest.get("registPersonId") != null && !guest.getString("registPersonId").equals("")) {
                int registPersonId = guest.getInt("registPersonId");
                if (registPersonId > 0) {
                    person.setRegistPersonId(guest.getInt("registPersonId"));
                }
            }
            person.setPersonName(guest.getString("personName"));
            person.setIsOther(i == 0 ? 0 : 1);
            person.setHid(user.getHid());
            person.setRoomNum(regist.getRoomNum());
            person.setRoomNumId(regist.getRoomNumId());
            person.setTeamCodeId(regist.getTeamCodeId());
            person.setBookingOrderId(regist.getBookingOrderId());
            if (bookingOrderRoomNumId > 0) {
                person.setBookingOrderRoomNumId(bookingOrderRoomNumId);
            }
            person.setUpdateUserId(user.getUserId());
            person.setUpdateUserName(user.getUserName());
            person.setClassId(user.getClassId());
            person.setHotelGroupId(user.getHotelGroupId());

            if (guest.get("address") != null) {
                person.setAddress(guest.getString("address"));
            }
            if (guest.get("nation") != null && !"".equals(guest.getString("nation")) && !"null".equals(guest.getString("nation"))) {
                String nation = guest.getString("nation");
                if (nation.indexOf("族") < 1) {
                    nation += "族";
                }
                person.setNation(HotelUtils.nationMap.getInt(nation));
            }

            if (guest.get("phone") != null && !guest.getString("phone").equals("") && !guest.getString("phone").equals("null")) {
                person.setPhone(guest.getString("phone"));
            }

            /**
             * 身份证照  新版本
             */
            if (guest.get("image") != null && !"".equals(guest.getString("image"))) {
                imageData.put("data", guest.getString("image"));
                String s = zimgService.uplaodImage(imageData);
                person.setIdImage(ZimgUploadUtil.ZIMGUPLOADURL + "/" + s);
            }

            /**
             * 证件照,新版本
             */
            if (guest.get("cameraPicture") != null && !"".equals(guest.getString("cameraPicture"))) {
                imageData.put("data", guest.getString("cameraPicture"));
                String s = zimgService.uplaodImage(imageData);
                person.setCameraImage(ZimgUploadUtil.ZIMGUPLOADURL + "/" + s);
            }

            /**
             * 证件照,老版本 Image
             */
            if (guest.get("photo") != null && !"".equals(guest.getString("photo"))) {
                String photo = guest.getString("photo").replace(' ', '+');
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(photo, 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(person.getIdCode(), "UTF-8") + ".jpeg");
                person.setIdImage(uploadObjectRsp.getFileName());
            }
            /**
             * 身份证照  老版本
             */
            if (guest.get("cameraPhoto") != null && !"".equals(guest.getString("cameraPhoto"))) {
                String s = person.getIdCode() + regist.getHid() + regist.getRoomNumId() + HotelUtils.currentTime();
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("cameraPhoto").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                person.setCameraImage(uploadObjectRsp.getFileName());
            }

            /**
             * 相似度   0~1 之间  Similarity，相似度
             */
            if (guest.get("semblance") != null && !"".equals(guest.getString("semblance"))) {
                person.setConfidence(guest.getString("semblance"));
            }

            /**
             * 成功或失败  0-失败 1-成功
             */
            if (guest.get("faceResult") != null && !"".equals(guest.getString("faceResult"))) {
                try {
                    person.setFaceResult(guest.getInt("faceResult"));
                } catch (Exception ex) {
                    person.setFaceResult(0);
                }
            }

            /**
             * 公安网上传状态
             */
            if (guest.get("guestStatus") != null && !"".equals(guest.getString("guestStatus"))) {
                try {
                    person.setGuestType(guest.getInt("guestStatus"));
                } catch (Exception ex) {
                    person.setFaceResult(0);
                }
            }

            /**
             * 公安网流水号
             */
            if (guest.get("guestNo") != null && !"".equals(guest.getString("guestNo"))) {
                person.setGuestNo(guest.getString("guestNo"));
            }

            /**
             * 公安网唯一标识
             */
            if (guest.get("guestId") != null && !"".equals(guest.getString("guestId"))) {
                person.setGuestId(guest.getString("guestId"));
            }

            registPeople.add(person);


            /**
             * 这里创建子线程处理 客史的问题
             */
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        createPersonInfo(registPeople);
                    } catch (Exception e) {
                        log.error("createPersonInfo fail  exception {}",e);
                    }
                }
            });
        }

        return registPeople;
    }

    @Transactional(rollbackFor = Exception.class)
    public ResponseData createPersonInfo(List<RegistPerson> registPersonList) {
        log.info("createPersonInfo param {}", registPersonList);
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<PersonInfo> personInfoList = new ArrayList<>();
            for (int i = 0; i < registPersonList.size(); i++) {
                RegistPerson registPerson = registPersonList.get(i);
                if (null == registPerson.getPersonName() || registPerson.getPersonName().equals("")) {
                    continue;
                }
                if (null == registPerson.getIdCode() || registPerson.getIdCode().equals("")) {
                    continue;
                }
                //以证件号为唯一标准去查询客史档案
                String idCode = registPerson.getIdCode();
//                IdcardValidator iv = new IdcardValidator();
//                if (!iv.isValidatedAllIdcard(idCode)) {
//                    continue;
//                }
                PersonInfoSearch personInfoSearch = new PersonInfoSearch();
                personInfoSearch.setHid(registPerson.getHid());
                personInfoSearch.setIdCode(idCode);
                Page<PersonInfo> personInfos = personInfoDao.selectBySearch(personInfoSearch);
                if (null == personInfos || personInfos.size() < 1) {
                    PersonInfo personInfo = new PersonInfo();
                    personInfo.setHid(registPerson.getHid());
                    personInfo.setHotelGroupId(registPerson.getHotelGroupId());
                    personInfo.setPersonName(registPerson.getPersonName());
                    personInfo.setIdCode(registPerson.getIdCode());
                    personInfo.setSex(registPerson.getSex());
                    personInfo.setBirthday(registPerson.getBirthday());
                    personInfo.setNation(registPerson.getNation());
                    personInfo.setIdImage(registPerson.getIdImage());
                    personInfo.setAddress(registPerson.getAddress());
                    personInfo.setCameraImage(registPerson.getCameraImage());
                    personInfo.setPhone(registPerson.getPhone());
                    personInfoList.add(personInfo);
                }
            }
            for (int i = 0; i < personInfoList.size(); i++) {
                Integer insert = personInfoDao.insert(personInfoList.get(i));
                if (insert < 1) {
                    log.debug("createPersonInfo {}", personInfoList.get(i));
                    throw new Exception("创建客历档案失败");
                }
            }
        } catch (Exception e) {
            log.info("createPersonInfo fail exception {}", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * @param guest
     * @param user
     * @param regist
     * @param bookingOrderRoomNumId
     * @return
     * @throws Exception
     */
    @Override
    public RegistPerson addCheckinGuest2(BookingRequest.Person guest, TbUserSession user, Regist regist, Integer bookingOrderRoomNumId) throws Exception {
        /**
         * 3.添加入住信息
         */
        JSONObject imageData = new JSONObject();
        imageData.put(ER.SESSION_TOKEN, user.getSessionId());
        RegistPerson person = new RegistPerson();
        person.setRegistState(0);

        if (StringUtils.isEmpty(guest.getPersonName()) || guest.getPersonName().equals("null")) {
            throw new Exception("宾客名称不能为空");
        }

        person.setIdCode(guest.getIdCode());

        if (StringUtils.isEmpty(guest.getBirthday()) && !StringUtils.hasText(guest.getBirthday()) && guest.getPersonName().equals("null")) {
            int birthday = Integer.parseInt(guest.getBirthday().replace("-", ""));
            person.setBirthday(birthday);
            person.setBirthYear(Integer.parseInt(String.valueOf(birthday).substring(0, 4)));
        } else {
            String birthday = null == guest.getIdCode() ? "" : guest.getIdCode().length() >= 15 ? guest.getIdCode().substring(6, 14) : "";
            String year = null == guest.getIdCode() ? "" : guest.getIdCode().length() >= 15 ? guest.getIdCode().substring(6, 10) : "";
            if (!birthday.equals("")) {
                person.setBirthday(Integer.parseInt(birthday));
            }
            if (!year.equals("")) {
                person.setBirthYear(Integer.parseInt(year));
            }
        }

        person.setRegistId(regist.getRegistId());
        person.setIdType(guest.getIdType());
        person.setSex(guest.getSex());

        person.setRegistPersonId(guest.getRegistPersonId());

        person.setPersonName(guest.getPersonName());
        person.setHid(user.getHid());
        person.setRoomNum(regist.getRoomNum());
        person.setRoomNumId(regist.getRoomNumId());
        person.setTeamCodeId(regist.getTeamCodeId());
        person.setBookingOrderId(regist.getBookingOrderId());
        if (bookingOrderRoomNumId > 0) {
            person.setBookingOrderRoomNumId(bookingOrderRoomNumId);
        }
        person.setUpdateUserId(user.getUserId());
        person.setUpdateUserName(user.getUserName());
        person.setClassId(user.getClassId());
        person.setHotelGroupId(user.getHotelGroupId());

        if (guest.getAddress() != null) {
            person.setAddress(guest.getAddress());
        }

        if (!StringUtils.isEmpty(guest.getNation()) && !"null".equals(guest.getNation())) {
            if (guest.getNation().indexOf("族") < 1) {
                guest.setNation(guest.getNation() + "族");
            }
            ;
            //person.setNation();
        }

        if (!StringUtils.isEmpty(guest.getPhone()) && !guest.getPhone().equals("null")) {
            person.setPhone(guest.getPhone());
        }

        /**
         * 身份证照  新版本
         */
        if (!StringUtils.isEmpty(guest.getImage())) {
            imageData.put("data", guest.getImage());
            String s = zimgService.uplaodImage(imageData);
            person.setIdImage(ZimgUploadUtil.ZIMGUPLOADURL + "/" + s);
        }

        /**
         * 身份证照  老版本
         */
        if (guest.getCameraPhoto() != null && !"".equals(guest.getCameraPhoto())) {
            String s = guest.getIdCode() + regist.getHid() + regist.getRoomNumId() + HotelUtils.currentTime();
            CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getCameraPhoto().replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
            person.setCameraImage(uploadObjectRsp.getFileName());
        }

        /**
         * 证件照,新版本
         */
        if (!StringUtils.isEmpty(guest.getCameraPicture())) {
            imageData.put("data", guest.getCameraPicture());
            String s = zimgService.uplaodImage(imageData);
            person.setCameraImage(ZimgUploadUtil.ZIMGUPLOADURL + "/" + s);
        }

        /**
         * 证件照,老版本 Image
         */
        if (!StringUtils.isEmpty(guest.getPhoto())) {
            imageData.put("data", guest.getPhoto());
            String s = zimgService.uplaodImage(imageData);
            person.setIdImage(ZimgUploadUtil.ZIMGUPLOADURL + "/" + s);
        }


        /**
         * 身份证照  老版本
         */
        if (!StringUtils.isEmpty(guest.getCameraPicture())) {
            imageData.put("data", guest.getCameraPicture());
            String s = zimgService.uplaodImage(imageData);
            person.setCameraImage(ZimgUploadUtil.ZIMGUPLOADURL + "/" + s);
        }

        /**
         * 相似度   0~1 之间  Similarity，相似度
         */
        if (!StringUtils.isEmpty(guest.getSemblance())) {
            person.setConfidence(guest.getSemblance());
        }

        /**
         * 成功或失败  0-失败 1-成功
         */
        person.setFaceResult(guest.getFaceResult());

        /**
         * 公安网上传状态
         */
        person.setGuestType(guest.getGuestStatus());

        /**
         * 公安网流水号
         */
        if (!StringUtils.isEmpty(guest.getGuestNo())) {
            person.setGuestNo(guest.getGuestNo());
        }

        /**
         * 公安网唯一标识
         */
        if (!StringUtils.isEmpty(guest.getGuestId())) {
            person.setGuestId(guest.getGuestId());
        }
        return person;
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public JSONArray blendCheckInTransaction(RegistGroup registGroup, List<CheckInRegist> checkInRegists, List<Account> accounts, int sumMoney, TbUserSession user, ArrayList<RoomAuxiliaryRelation> deleteRelations) throws Exception {

        JSONArray res = new JSONArray();

        ArrayList<Oprecord> oprecords = new ArrayList<>();

        Oprecord oprecord = new Oprecord(user);

        // 1.添加集团

        if (registGroup != null) {
            registGroup.setSumPay(sumMoney);
            Integer insert = registGroupDao.insert(registGroup);
            if (insert < 1) {
                throw new Exception("保存团队或联房信息失败");
            }
        }
        // 2.添加入住信息
        Regist mainRegist = new Regist();

        // 添加入住人
        ArrayList<RegistPerson> allRegistPeople = new ArrayList<>();
        ArrayList<RoomAuxiliaryRelation> allRoomAuxiliary = new ArrayList<>();
        ArrayList<BookingOrderDailyPrice> allAddPriceList = new ArrayList<>();
        ArrayList<BookingOrderConfig> allBookingOrderConfigs = new ArrayList<>();
        ArrayList<RoomInfo> roomInfos = new ArrayList<>();
        ArrayList<RoomRepairRecordHistory> roomRepairRecordHistories = new ArrayList<>();

        HashMap<Integer, RegistPerson> registPeopleMap = new HashMap<>();

        RoomAuxiliaryRelation addRelation = new RoomAuxiliaryRelation();

        for (CheckInRegist checkInRegist : checkInRegists) {

            // 1.主单
            Regist regist = checkInRegist.getRegist();
            regist.setRegistId(null);
            regist.setTeamCodeId(registGroup != null ? registGroup.getRegistGroupId() : 0);
            regist.setTeamCodeName(registGroup != null ? registGroup.getGroupName() : "");
            regist.setCheckoutBusinessDay((HotelUtils.parseDate2Int(regist.getCheckoutTime())));

            if (regist.getIsMainRoom() == 1 || checkInRegists.size() == 1) {
                regist.setSumPay(sumMoney);
            }

            Integer insert1 = registDao.insert(regist);

            if (insert1 < 1) {
                throw new Exception("房间:" + regist.getRoomNum() + "  入住失败");
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("roomNo", regist.getRoomNum());
            jsonObject.put("registId", regist.getRegistId());

            res.add(jsonObject);

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription(HotelUtils.currentTime() + "    房间" + regist.getRoomNum() + " 办理入住。");
            oprecords.add(oprecord);

            if (mainRegist.getRegistId() == null) {
                if (regist.getIsMainRoom() == 1) {
                    mainRegist = regist;
                } else {
                    mainRegist = regist;
                }
            }
            // 2.配置信息
            BookingOrderConfig bookingOrderConfig = checkInRegist.getBookingOrderConfig();
            bookingOrderConfig.setBookingOrderId(regist.getBookingOrderId());
            bookingOrderConfig.setRegistId(regist.getRegistId());

            allBookingOrderConfigs.add(bookingOrderConfig);

            Integer saveBookingOrderConfig = bookingOrderConfigDao.saveBookingOrderConfig(bookingOrderConfig);

            if (saveBookingOrderConfig < 1) {
                throw new Exception("房间:" + regist.getRoomNum() + "  添加配置失败");
            }

            // 3.辅助房态
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = checkInRegist.getRoomAuxiliaryRelations();

            for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {

                addRelation = new RoomAuxiliaryRelation();
                addRelation.setRelationId(null);
                addRelation.setHid(user.getHid());
                addRelation.setHotelGroupId(user.getHotelGroupId());
                addRelation.setBookingOrderId(regist.getBookingOrderId());
                addRelation.setRegistId(regist.getRegistId());
                addRelation.setRoomId(regist.getRoomNumId());
                addRelation.setRoomNum(regist.getRoomNum());
                addRelation.setRoomAuxiliaryId(roomAuxiliaryRelation.getRoomAuxiliaryId());
                addRelation.setSort(roomAuxiliaryRelation.getSort());

                allRoomAuxiliary.add(addRelation);
                //  roomAuxiliaryRelationDao.saveRoomAuxiliaryRelation(roomAuxiliaryRelation);

            }


            // 4.价格
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = checkInRegist.getBookingOrderDailyPrices();
            for (BookingOrderDailyPrice bookingOrderDailyPrice : bookingOrderDailyPrices) {

                bookingOrderDailyPrice.setRoomNumId(regist.getRoomNumId());
                bookingOrderDailyPrice.setRoomTypeId(regist.getRoomTypeId());
                bookingOrderDailyPrice.setRegistId(regist.getRegistId());
                bookingOrderDailyPrice.setBookingOrderId(regist.getBookingOrderId());

                allAddPriceList.add(bookingOrderDailyPrice);
                /*Integer integer = bookingOrderDailyPriceDao.saveBookingOrderDailyPrice(bookingOrderDailyPrice);

                if (integer < 1) {
                    throw new Exception("房间:" + regist.getRoomNum() + "  添加 " + bookingOrderDailyPrice.getDailyTime() + " 房价失败");
                }*/

            }

            // 5.入住人
            List<RegistPerson> registPeople = checkInRegist.getRegistPeople();
            for (RegistPerson registPerson : registPeople) {
                registPerson.setRegistPersonId(null);
                registPerson.setHid(user.getHid());
                registPerson.setHotelGroupId(user.getHotelGroupId());
                registPerson.setRegistId(regist.getRegistId());
                registPerson.setRegistState(0);

                allRegistPeople.add(registPerson);

              /*  Integer insert2 = registPersonDao.insert(registPerson);
                if (insert2 < 1) {
                    throw new Exception("房间:" + regist.getRoomNum() + "  添加入住人： " + registPerson.getPersonName() + " 失败");
                }
*/
                Integer integer = registPeopleMap.get(regist.getRegistId()) != null ? registPeopleMap.get(regist.getRegistId()).getRegistId() : null;
                if (integer == null) {
                    registPeopleMap.put(regist.getRegistId(), registPerson);
                }

                oprecord = new Oprecord(user);
                oprecord.setOccurTime(HotelUtils.currentTime());
                oprecord.setDescription("房间:" + regist.getRoomNum() + "  添加入住人： " + registPerson.getPersonName());
                oprecords.add(oprecord);

            }

            // 6 修改房间状态
            RoomInfo roomInfo = new RoomInfo();
            roomInfo.setRoomInfoId(regist.getRoomNumId());
            roomInfo.setRoomNumState(ROOM_STATUS.OCC);
            roomInfos.add(roomInfo);

            RoomRepairRecordHistory roomRepairRecord = new RoomRepairRecordHistory();

            roomRepairRecord.setRoomId(roomInfo.getRoomInfoId());
            roomRepairRecord.setRoomNum(roomInfo.getRoomNum());
            roomRepairRecord.setHid(roomInfo.getHid());
            roomRepairRecord.setHotelGroupId(roomInfo.getHotelGroupId());
            roomRepairRecord.setClassId(user.getClassId());
            roomRepairRecord.setCreateUserId(user.getUserId());
            roomRepairRecord.setCreateUserName(user.getUserName());
            roomRepairRecord.setCreateTime(new Date());
            roomRepairRecord.setUpdateUserId(user.getUserId());
            roomRepairRecord.setUpdateUserName(user.getUserName());
            roomRepairRecord.setRoomTypeId(roomInfo.getRoomTypeId());
            roomRepairRecord.setUpdateTime(new Date());
            roomRepairRecord.setType(RoomUtils.getRoomRepairRecordType(roomInfo.getRoomNumState(), ROOM_STATUS.OCC));
            roomRepairRecord.setState(RoomUtils.getRoomRepairRecordState(roomInfo.getRoomNumState(), ROOM_STATUS.OCC));
            roomRepairRecord.setBusinessDay(user.getBusinessDay());
            roomRepairRecord.setDes(roomInfo.getRoomNum() + "入住。");

            roomRepairRecordHistories.add(roomRepairRecord);

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription("房间:" + regist.getRoomNum() + " 改为在住。");
            oprecords.add(oprecord);

        }

        // 添加账务
        for (Account account : accounts) {
            account.setRoomInfoId(mainRegist.getRoomNumId());
            account.setRoomNum(mainRegist.getRoomNum());
            account.setRoomTypeId(mainRegist.getRoomTypeId());
            account.setRegistId(mainRegist.getRegistId());
            account.setRegistState(mainRegist.getState());
            account.setTeamCodeId(registGroup != null ? registGroup.getRegistGroupId() : 0);
            account.setRoomTypeId(mainRegist.getRoomTypeId());
            account.setBookingId(mainRegist.getBookingOrderId());
            if (registPeopleMap.get(mainRegist.getRegistId()) != null) {
                account.setRegistPersonId(registPeopleMap.get(mainRegist.getRegistId()).getRegistPersonId());
                account.setRegistPersonName(registPeopleMap.get(mainRegist.getRegistId()).getPersonName());
            }

            AccountThirdPayRecode accountThirdPayRecode = account.getAccountThirdPayRecode();
            if (accountThirdPayRecode != null) {
                Integer insert1 = accountThirdPayRecodeDao.insert(accountThirdPayRecode);
                account.setThirdAccoutId(accountThirdPayRecode.getAccountThirdId());
            }

            Integer integer = accountDao.saveAccount(account);
            if (integer < 1) {
                throw new Exception("房间:" + mainRegist.getRoomNum() + " 入账 --" + account.getGoodName() + " " + account.getPrice() / 100.0 + " 元 失败");
            }

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription("房间:" + mainRegist.getRoomNum() + " 入账 --" + account.getGoodName() + " " + account.getPrice() / 100.0 + " 元");
            oprecords.add(oprecord);

        }

        // 4.删除辅助房态
        if (deleteRelations.size() > 0) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(deleteRelations);
        }
        if (allRoomAuxiliary.size() > 0) {
            roomAuxiliaryRelationDao.addRoomAuxiliaryRelationList(allRoomAuxiliary);
        }

        // 修改房价
        bookingOrderDailyPriceDao.addPriceList(allAddPriceList);

        // 批量添加入住人
        registPersonDao.addPeople(allRegistPeople);

        // 批量添加房间配置 bookingordercofig
        // bookingOrderConfigDao.addBookingOrderConfigList(allBookingOrderConfigs);
       /* for (RoomAuxiliaryRelation roomAuxiliaryRelation : deleteRelations) {

            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());

        }*/

        // 修改房间信息
        roomInfoDao.updateRoomList(roomInfos);

        // 添加房间修改记录
        roomRepairRecordDao.addRoomRepairRecordHistoryList(roomRepairRecordHistories);


        addOprecords(oprecords);

        return res;
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public JSONArray blendCheckInTransaction(RegistGroup registGroup, List<CheckInRegist> checkInRegists, List<Account> accounts, int sumMoney, TbUserSession user, ArrayList<RoomAuxiliaryRelation> deleteRelations, ArrayList<HourRoomDayUse> addHourUse, ArrayList<HourRoomDayUse> upaHourUse) throws Exception {
        JSONArray res = new JSONArray();

        ArrayList<Oprecord> oprecords = new ArrayList<>();

        Oprecord oprecord = new Oprecord(user);

        // 1.添加集团

        if (registGroup != null) {
            registGroup.setSumPay(sumMoney);
            Integer insert = registGroupDao.insert(registGroup);
            if (insert < 1) {
                throw new Exception("保存团队或联房信息失败");
            }
        }
        // 2.添加入住信息
        Regist mainRegist = new Regist();

        // 添加入住人
        ArrayList<RegistPerson> allRegistPeople = new ArrayList<>();
        ArrayList<RoomAuxiliaryRelation> allRoomAuxiliary = new ArrayList<>();
        ArrayList<BookingOrderDailyPrice> allAddPriceList = new ArrayList<>();
        ArrayList<BookingOrderConfig> allBookingOrderConfigs = new ArrayList<>();
        ArrayList<RoomInfo> roomInfos = new ArrayList<>();
        ArrayList<RoomRepairRecordHistory> roomRepairRecordHistories = new ArrayList<>();

        HashMap<Integer, RegistPerson> registPeopleMap = new HashMap<>();

        RoomAuxiliaryRelation addRelation = new RoomAuxiliaryRelation();

        for (CheckInRegist checkInRegist : checkInRegists) {

            // 1.主单
            Regist regist = checkInRegist.getRegist();
            regist.setRegistId(null);
            regist.setTeamCodeId(registGroup != null ? registGroup.getRegistGroupId() : 0);
            regist.setTeamCodeName(registGroup != null ? registGroup.getGroupName() : "");
            regist.setCheckoutBusinessDay((HotelUtils.parseDate2Int(regist.getCheckoutTime())));

            if (regist.getIsMainRoom() == 1 || checkInRegists.size() == 1) {
                regist.setSumPay(sumMoney);
            }

            Integer insert1 = registDao.insert(regist);

            if (insert1 < 1) {
                throw new Exception("房间:" + regist.getRoomNum() + "  入住失败");
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("roomNo", regist.getRoomNum());
            jsonObject.put("registId", regist.getRegistId());
            jsonObject.put("checkinType", regist.getCheckinType());
            jsonObject.put("endTime", HotelUtils.parseDate2Str(regist.getCheckoutTime()));

            res.add(jsonObject);

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription(HotelUtils.currentTime() + "    房间" + regist.getRoomNum() + " 办理入住。");
            oprecords.add(oprecord);

            if (mainRegist.getRegistId() == null) {
                if (regist.getIsMainRoom() == 1) {
                    mainRegist = regist;
                } else {
                    mainRegist = regist;
                }
            }
            // 2.配置信息
            BookingOrderConfig bookingOrderConfig = checkInRegist.getBookingOrderConfig();
            bookingOrderConfig.setBookingOrderId(regist.getBookingOrderId());
            bookingOrderConfig.setRegistId(regist.getRegistId());

            allBookingOrderConfigs.add(bookingOrderConfig);

            Integer saveBookingOrderConfig = bookingOrderConfigDao.saveBookingOrderConfig(bookingOrderConfig);

            if (saveBookingOrderConfig < 1) {
                throw new Exception("房间:" + regist.getRoomNum() + "  添加配置失败");
            }

            // 3.辅助房态
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = checkInRegist.getRoomAuxiliaryRelations();

            for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {

                addRelation = new RoomAuxiliaryRelation();
                addRelation.setRelationId(null);
                addRelation.setHid(user.getHid());
                addRelation.setHotelGroupId(user.getHotelGroupId());
                addRelation.setBookingOrderId(regist.getBookingOrderId());
                addRelation.setRegistId(regist.getRegistId());
                addRelation.setRoomId(regist.getRoomNumId());
                addRelation.setRoomNum(regist.getRoomNum());
                addRelation.setRoomAuxiliaryId(roomAuxiliaryRelation.getRoomAuxiliaryId());
                addRelation.setSort(roomAuxiliaryRelation.getSort());

                allRoomAuxiliary.add(addRelation);
                //  roomAuxiliaryRelationDao.saveRoomAuxiliaryRelation(roomAuxiliaryRelation);

            }


            // 4.价格
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = checkInRegist.getBookingOrderDailyPrices();
            for (BookingOrderDailyPrice bookingOrderDailyPrice : bookingOrderDailyPrices) {

                bookingOrderDailyPrice.setRoomNumId(regist.getRoomNumId());
                bookingOrderDailyPrice.setRoomTypeId(regist.getRoomTypeId());
                bookingOrderDailyPrice.setRegistId(regist.getRegistId());
                bookingOrderDailyPrice.setBookingOrderId(regist.getBookingOrderId());

                allAddPriceList.add(bookingOrderDailyPrice);
                /*Integer integer = bookingOrderDailyPriceDao.saveBookingOrderDailyPrice(bookingOrderDailyPrice);

                if (integer < 1) {
                    throw new Exception("房间:" + regist.getRoomNum() + "  添加 " + bookingOrderDailyPrice.getDailyTime() + " 房价失败");
                }*/

            }

            // 5.入住人
            List<RegistPerson> registPeople = checkInRegist.getRegistPeople();
            for (RegistPerson registPerson : registPeople) {
                registPerson.setRegistPersonId(null);
                registPerson.setHid(user.getHid());
                registPerson.setHotelGroupId(user.getHotelGroupId());
                registPerson.setRegistId(regist.getRegistId());
                registPerson.setRegistState(0);

                allRegistPeople.add(registPerson);

              /*  Integer insert2 = registPersonDao.insert(registPerson);
                if (insert2 < 1) {
                    throw new Exception("房间:" + regist.getRoomNum() + "  添加入住人： " + registPerson.getPersonName() + " 失败");
                }
*/
                Integer integer = registPeopleMap.get(regist.getRegistId()) != null ? registPeopleMap.get(regist.getRegistId()).getRegistId() : null;
                if (integer == null) {
                    registPeopleMap.put(regist.getRegistId(), registPerson);
                }

                oprecord = new Oprecord(user);
                oprecord.setOccurTime(HotelUtils.currentTime());
                oprecord.setDescription("房间:" + regist.getRoomNum() + "  添加入住人： " + registPerson.getPersonName());
                oprecords.add(oprecord);

            }

            // 6 修改房间状态
            RoomInfo roomInfo = new RoomInfo();
            RoomInfo oldRoomInfo = roomInfoDao.selectById(regist.getRoomNumId());
            roomInfo.setRoomInfoId(regist.getRoomNumId());
            roomInfo.setRoomNumState(ROOM_STATUS.OCC);
            if(Objects.nonNull(oldRoomInfo)){
                roomInfo.setUpdateTime(DateUtil.strToDate(oldRoomInfo.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
                roomInfo.setCreateTime(DateUtil.strToDate(oldRoomInfo.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            }
            roomInfos.add(roomInfo);

            RoomRepairRecordHistory roomRepairRecord = new RoomRepairRecordHistory();

            roomRepairRecord.setRoomId(roomInfo.getRoomInfoId());
            roomRepairRecord.setRoomNum(roomInfo.getRoomNum());
            roomRepairRecord.setHid(roomInfo.getHid());
            roomRepairRecord.setHotelGroupId(roomInfo.getHotelGroupId());
            roomRepairRecord.setClassId(user.getClassId());
            roomRepairRecord.setCreateUserId(user.getUserId());
            roomRepairRecord.setCreateUserName(user.getUserName());
            roomRepairRecord.setCreateTime(new Date());
            roomRepairRecord.setUpdateUserId(user.getUserId());
            roomRepairRecord.setUpdateUserName(user.getUserName());
            roomRepairRecord.setRoomTypeId(roomInfo.getRoomTypeId());
            roomRepairRecord.setUpdateTime(new Date());
            roomRepairRecord.setType(RoomUtils.getRoomRepairRecordType(roomInfo.getRoomNumState(), ROOM_STATUS.OCC));
            roomRepairRecord.setState(RoomUtils.getRoomRepairRecordState(roomInfo.getRoomNumState(), ROOM_STATUS.OCC));
            roomRepairRecord.setBusinessDay(user.getBusinessDay());
            roomRepairRecord.setDes(roomInfo.getRoomNum() + "入住。");

            roomRepairRecordHistories.add(roomRepairRecord);

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription("房间:" + regist.getRoomNum() + " 改为在住。");
            oprecords.add(oprecord);
            /**
             * 数据推送iot酒店
             */
            IotHotelCheckIn iotHotelCheckIn = new IotHotelCheckIn();
            iotHotelCheckIn.setCheckOutTime(HotelUtils.parseDate2Str(regist.getCheckoutTime()));
            iotHotelCheckIn.setCode(regist.getRegistId() + "");
            iotHotelCheckIn.setPmsRoomId(regist.getRoomNumId() + "");
            iotHotelCheckIn.setGuests(registPeople.stream().map(
                    people -> {
                        IotHotelGuest hotelGuest = new IotHotelGuest();
                        hotelGuest.setIdentityCardNo(people.getIdCode());
                        hotelGuest.setName(people.getPersonName());
                        hotelGuest.setPhone(people.getPhone());
                        return hotelGuest;
                    }
            ).collect(Collectors.toList()));

            HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
            Map<String, String> authParamMap = new HashMap<>();
            authParamMap.put("eid", user.getHid().toString());
            authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
            authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
            authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
            try {
                authParamMap.put("phone", AESUtil.encryptAesCbc(user.getPhone(),iotPlatConfig.getSassTokenSecret()));
            } catch (Exception e) {
                log.error("手机号加密失败");
            }
            hotelIotStrategy.initStrategy(authParamMap);
            webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.CHECK_IN,
                    iotHotelCheckIn,
                    hotelIotStrategy);
        }

        // 添加账务
        for (Account account : accounts) {
            account.setRoomInfoId(mainRegist.getRoomNumId());
            account.setRoomNum(mainRegist.getRoomNum());
            account.setRoomTypeId(mainRegist.getRoomTypeId());
            account.setRegistId(mainRegist.getRegistId());
            account.setRegistState(mainRegist.getState());
            account.setTeamCodeId(registGroup != null ? registGroup.getRegistGroupId() : 0);
            account.setRoomTypeId(mainRegist.getRoomTypeId());
            account.setBookingId(mainRegist.getBookingOrderId());
            if (registPeopleMap.get(mainRegist.getRegistId()) != null) {
                account.setRegistPersonId(registPeopleMap.get(mainRegist.getRegistId()).getRegistPersonId());
                account.setRegistPersonName(registPeopleMap.get(mainRegist.getRegistId()).getPersonName());
            }

            AccountThirdPayRecode accountThirdPayRecode = account.getAccountThirdPayRecode();
            if (accountThirdPayRecode != null) {
                Integer insert1 = accountThirdPayRecodeDao.insert(accountThirdPayRecode);
                account.setThirdAccoutId(accountThirdPayRecode.getAccountThirdId());
            }

            Integer integer = accountDao.saveAccount(account);
            if (integer < 1) {
                throw new Exception("房间:" + mainRegist.getRoomNum() + " 入账 --" + account.getGoodName() + " " + account.getPrice() / 100.0 + " 元 失败");
            }

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription("房间:" + mainRegist.getRoomNum() + " 入账 --" + account.getGoodName() + " " + account.getPrice() / 100.0 + " 元");
            oprecords.add(oprecord);

        }

        // 4.删除辅助房态
        if (deleteRelations.size() > 0) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(deleteRelations);
        }
        if (allRoomAuxiliary.size() > 0) {
            roomAuxiliaryRelationDao.addRoomAuxiliaryRelationList(allRoomAuxiliary);
        }

        // 修改房价
        bookingOrderDailyPriceDao.addPriceList(allAddPriceList);

        // 批量添加入住人
        for (int i = 0; i < allRegistPeople.size(); i++) {
            registPersonDao.insert(allRegistPeople.get(i));
        }


//        registPersonDao.addPeople(allRegistPeople);

        // 批量添加房间配置 bookingordercofig
        // bookingOrderConfigDao.addBookingOrderConfigList(allBookingOrderConfigs);
       /* for (RoomAuxiliaryRelation roomAuxiliaryRelation : deleteRelations) {

            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());

        }*/

        // 修改房间信息
        roomInfoDao.updateRoomList(roomInfos);

        // 添加房间修改记录
        roomRepairRecordDao.addRoomRepairRecordHistoryList(roomRepairRecordHistories);

        if (addHourUse.size() > 0) {

            for (HourRoomDayUse hourRoomDayUse : addHourUse) {

                if (hourRoomDayUse.getRoomInfoId() == null || hourRoomDayUse.getRoomNo() == null) {
                    continue;
                }

                hourRoomDayUseDao.insert(hourRoomDayUse);

            }

        }

        if (upaHourUse.size() > 0) {
            for (HourRoomDayUse hourRoomDayUse : upaHourUse) {
                String useMsg = hourRoomDayUse.getUseMsg();
                if (useMsg.length() < 1) {
                    hourRoomDayUseDao.delete(hourRoomDayUse.getId());
                } else {
                    hourRoomDayUseDao.update(hourRoomDayUse);
                }
            }
        }

        addOprecords(oprecords);

        return res;
    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public void personCheckout(List<Account> accounts, List<RegistPerson> personList, RegistPerson registPerson ,Integer eid,String phone) throws Exception {
        for (int i = 0; i < accounts.size(); i++) {
            Account account = accounts.get(i);
            account.setRegistState(1);
            Integer update = accountDao.editAccount(account);
            if (update < 1) {
                throw new Exception("修改账务信息失败");
            }
        }
        for (int i = 0; i < personList.size(); i++) {
            RegistPerson registPersonInfo = personList.get(i);
            registPersonInfo.setRegistState(1);
            registPersonInfo.setAuthenticateTime(new Date());
            Integer update = registPersonDao.update(registPersonInfo);
            if (update < 1) {
                throw new Exception("修改宾客信息失败");
            }
        }
        registPerson.setIsOther(0);
        Integer update = registPersonDao.update(registPerson);
        if (update < 1) {
            throw new Exception("修改宾客为住客失败信息失败");
        }
        /**
         * 数据推送iot酒店
         */
        IotHotelCheckOut checkOut = new IotHotelCheckOut();
        checkOut.setCode(registPerson.getRegistId() + "");
        HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
        Map<String, String> authParamMap = new HashMap<>();
        authParamMap.put("eid", eid.toString());
        authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
        authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
        authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
        try {
            authParamMap.put("phone", AESUtil.encryptAesCbc(phone,iotPlatConfig.getSassTokenSecret()));
        } catch (Exception e) {
            log.error("手机号加密失败");
        }
        hotelIotStrategy.initStrategy(authParamMap);
        webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.CHECK_OUT,
                checkOut,
                hotelIotStrategy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void hourToDayTransaction(Regist regist, List<BookingOrderDailyPrice> delBookingOrderDailyPrices, List<BookingOrderDailyPrice> insertBookingOrderDailyPrices,
                                     List<RoomAuxiliaryRelation> deleteRelations, TbUserSession user, ArrayList<Oprecord> oprecords, Account account) throws Exception {
        Integer update = registDao.update(regist);
        if (update < 1) {
            throw new Exception("修改订单信息失败");
        }

        for (int i = 0; i < delBookingOrderDailyPrices.size(); i++) {
            update = bookingOrderDailyPriceDao.deleteBookingOrderDailyPrice(delBookingOrderDailyPrices.get(i).getId());
            if (update < 1) {
                throw new Exception("修改价格失败");
            }
        }

        for (int i = 0; i < insertBookingOrderDailyPrices.size(); i++) {
            update = bookingOrderDailyPriceDao.saveBookingOrderDailyPrice(insertBookingOrderDailyPrices.get(i));
            if (update < 1) {
                throw new Exception("添加价格失败");
            }
        }

        for (int i = 0; i < deleteRelations.size(); i++) {
            update = roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(deleteRelations.get(i).getRelationId());
            if (update < 1) {
                throw new Exception("修改房态失败");
            }
        }

        if (account != null) {
            accountDao.editAccount(account);
        }

        addOprecords(oprecords);

    }

    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public Integer roomJion(RegistGroup registGroup, Regist mainReg, List<RegistGroup> registGroups, List<Regist> regists, List<Account> accounts, List<RoomAuxiliaryRelation> addAuxRoom, List<RegistPerson> registPeople) throws Exception {

        Integer insert = registGroupDao.insert(registGroup);

        if (insert < 1) {
            throw new Exception("添加团队信息失败");
        }

        Integer registGroupId = registGroup.getRegistGroupId();

        // 删除原有联房或团队信息
        for (RegistGroup rpC : registGroups) {

            rpC.setState(2);

            Integer update = registGroupDao.update(rpC);

            if (update < 1) {
                throw new Exception("撤销团队：" + rpC.getSn() + "失败");
            }

        }

        ArrayList<Regist> regists1 = new ArrayList<>();

        for (Regist regist : regists) {

            Regist registInfo = Regist.CreateRegist(regist.getRegistId());

            regist.setTeamCodeId(registGroupId);
            regist.setTeamCodeName(registGroup.getGroupName());
            regist.setRegistGroupId(registGroupId);

            registInfo.setTeamCodeId(registGroupId);
            registInfo.setTeamCodeName(registGroup.getGroupName());
            registInfo.setRegistGroupId(registGroupId);


            if (regist.getRegistId().equals(mainReg.getRegistId())) {
                regist.setIsMainRoom(1);
                registInfo.setIsMainRoom(1);
            } else {
                regist.setIsMainRoom(0);
                registInfo.setIsMainRoom(0);
            }

            // regists1.add(regist);
            regists1.add(registInfo);
        }

        registDao.updateRegistList(regists1);

        if (accounts.size() > 0) {

            ArrayList<Account> accounts1 = new ArrayList<>();

            for (Account account : accounts) {

                account.setTeamCodeId(registGroupId);

                accounts1.add(account);
            }

            accountDao.editAccountList(accounts1);

        }


        if (addAuxRoom.size() > 0) {
            roomAuxiliaryRelationDao.addRoomAuxiliaryRelationList(addAuxRoom);
        }

        if (registPeople.size() > 0) {
            ArrayList<RegistPerson> rps = new ArrayList<>();
            for (RegistPerson registPerson : registPeople) {
                registPerson.setTeamCodeId(registGroupId);
                rps.add(registPerson);
            }
            registPersonDao.updatePeople(rps);
        }

        return registGroupId;
    }


    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public Integer withRoom(RegistGroup registGroup, Regist mainReg, List<RegistGroup> registGroups, List<Regist> regists, List<Account> accounts, List<RoomAuxiliaryRelation> addAuxRoom, List<RegistPerson> registPeople) throws Exception {

        Integer insert = registGroupDao.insert(registGroup);

        if (insert < 1) {
            throw new Exception("添加团队信息失败");
        }

        Integer registGroupId = registGroup.getRegistGroupId();

        // 删除原有联房或团队信息
        for (RegistGroup rpC : registGroups) {

            rpC.setState(2);

            Integer update = registGroupDao.update(rpC);

            if (update < 1) {
                throw new Exception("撤销团队：" + rpC.getSn() + "失败");
            }

        }

        ArrayList<Regist> regists1 = new ArrayList<>();

        for (Regist regist : regists) {

            Regist registInfo = Regist.CreateRegist(regist.getRegistId());

            regist.setTeamCodeId(registGroupId);
            regist.setTeamCodeName(registGroup.getGroupName());
            regist.setRegistGroupId(registGroupId);

            registInfo.setTeamCodeId(registGroupId);
            registInfo.setTeamCodeName(registGroup.getGroupName());
            registInfo.setRegistGroupId(registGroupId);

            if (regist.getRegistId().equals(mainReg.getRegistId())) {
                regist.setIsMainRoom(1);
                registInfo.setIsMainRoom(1);
            } else {
                regist.setIsMainRoom(0);
                registInfo.setIsMainRoom(0);
            }

            // regists1.add(regist);
            regists1.add(registInfo);
        }

        registDao.updateRegistList(regists1);

        if (accounts.size() > 0) {

            ArrayList<Account> accounts1 = new ArrayList<>();

            for (Account account : accounts) {

                account.setTeamCodeId(registGroupId);

                accounts1.add(account);
            }

            accountDao.editAccountList(accounts1);

        }


        if (addAuxRoom.size() > 0) {
            roomAuxiliaryRelationDao.addRoomAuxiliaryRelationList(addAuxRoom);
        }

        if (registPeople.size() > 0) {
            ArrayList<RegistPerson> rps = new ArrayList<>();
            for (RegistPerson registPerson : registPeople) {
                registPerson.setTeamCodeId(registGroupId);
                rps.add(registPerson);
            }
            registPersonDao.updatePeople(rps);
        }

        return registGroupId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void teamJion(RegistGroup registGroup, RegistGroup delRegistGroup, List<Regist> regists, List<Account> accounts, List<RegistPerson> registPeople, List<RoomAuxiliaryRelation> addAuxRoom) throws Exception {

        Integer update = registGroupDao.update(registGroup);

        if (update < 1) {
            throw new Exception("修改原团队信息失败");
        }
        if (delRegistGroup.getRegistGroupId() > 0) {
            delRegistGroup.setState(2);
            registGroupDao.update(delRegistGroup);
        }

        registDao.updateRegistList(regists);

        if (accounts.size() > 0) {
            ArrayList<Account> accounts1 = new ArrayList<>();

            for (Account account : accounts) {

                account.setTeamCodeId(registGroup.getRegistGroupId());

                accounts1.add(account);
            }

            accountDao.editAccountList(accounts1);

        }

        if (registPeople.size() > 0) {
            ArrayList<RegistPerson> rps = new ArrayList<>();
            for (RegistPerson registPerson : registPeople) {
                registPerson.setTeamCodeId(registGroup.getRegistGroupId());
                rps.add(registPerson);
            }
            registPersonDao.updatePeople(rps);
        }

        if (addAuxRoom.size() > 0) {
            roomAuxiliaryRelationDao.addRoomAuxiliaryRelationList(addAuxRoom);
        }
    }
}
