package com.pms.czabsorders.service.mini.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.service.mini.MobileOrderService;
import com.pms.czabsorders.service.mini.transaction.WxServiceTransaction;
import com.pms.czaccount.bean.account.Account;
import com.pms.czhotelfoundation.bean.FileInfo;
import com.pms.czhotelfoundation.bean.chess.HotelInfo;
import com.pms.czhotelfoundation.bean.code.HotelBusinessDay;
import com.pms.czhotelfoundation.bean.code.search.HotelBusinessDaySearch;
import com.pms.czhotelfoundation.bean.hotel.HotelBaseInfo;
import com.pms.czhotelfoundation.bean.hotel.search.HotelBaseInfoSearch;
import com.pms.czhotelfoundation.bean.hotel.search.ParamSearch;
import com.pms.czhotelfoundation.bean.price.RoomDayPrice;
import com.pms.czhotelfoundation.bean.price.RoomRateCodeSpecific;
import com.pms.czhotelfoundation.bean.price.search.RoomDayPriceSearch;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSpecificSearch;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliary;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomTypeImage;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeImageSearch;
import com.pms.czhotelfoundation.bean.search.FileInfoSearch;
import com.pms.czhotelfoundation.dao.FileInfoDao;
import com.pms.czhotelfoundation.dao.code.HotelBusinessDayDao;
import com.pms.czhotelfoundation.dao.hotel.HotelBaseInfoDao;
import com.pms.czhotelfoundation.dao.hotel.ParamDao;
import com.pms.czhotelfoundation.dao.price.RoomDayPriceDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeSpecificDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeImageDao;
import com.pms.czmembership.service.memeber.MemberService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.OrderNumUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.room.ROOM_AUXILIARY;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.*;
import com.pms.czpmsutils.view.GetMobileGroupHotelView;
import com.pms.czpmsutils.view.HotelRoomTypeView;
import com.pms.pmsorder.bean.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Primary
@Slf4j
public class MobileOrderServiceImpl extends BaseService implements MobileOrderService {
    @Autowired
    private HotelBaseInfoDao hotelBaseInfoDao;
    @Autowired
    private FileInfoDao fileInfoDao;
    @Autowired
    private RoomTypeDao roomTypeDao;
    @Autowired
    ParamDao paramDao;
    @Autowired
    private RoomDayPriceDao roomDayPriceDao;
    @Autowired
    private RoomTypeImageDao roomTypeImageDao;
    @Autowired
    private RoomRateCodeSpecificDao roomRateCodeSpecificDao;
    @Autowired
    private HotelBusinessDayDao hotelBusinessDayDao;
    @Autowired
    private RoomAuxiliaryDao roomAuxiliaryDao;
    @Autowired
    private MemberService memberService;
    @Autowired
    private WxServiceTransaction wxServiceTransaction;


    @Override
    public ResponseData getMobileGroupHotelList(GetMobileGroupHotelListRequest getMobileGroupHotelListRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(getMobileGroupHotelListRequest.getSessionToken());
            if (null == user) {
                throw new Exception(HOTEL_CONST.TOKENISNULL);
            }
            HotelBaseInfoSearch hotelBaseInfoSearch = new HotelBaseInfoSearch();
            hotelBaseInfoSearch.setHotelGroupId(user.getHotelGroupId());
            Page<HotelBaseInfo> hotelBaseInfos = hotelBaseInfoDao.selectBySearch(hotelBaseInfoSearch);
            FileInfoSearch fileInfoSearch = new FileInfoSearch();
            fileInfoSearch.setHid(user.getHotelGroupId());
            fileInfoSearch.setFileType(2);
            fileInfoSearch.setPageSize(0);
            Page<FileInfo> fileInfos = fileInfoDao.selectBySearch(fileInfoSearch);
            Map<String, List<FileInfo>> fileInfoCollect = fileInfos.stream().collect(Collectors.groupingBy(FileInfo::getMd5));
            List<HotelInfo> hotelInfos = new ArrayList<>();
            for (int i = 0; i < hotelBaseInfos.size(); i++) {
                HotelInfo hotelInfo = new HotelInfo();
                hotelInfo.setHotelName(hotelBaseInfos.get(i).getHotelName());
                hotelInfo.setAddr(hotelBaseInfos.get(i).getAddr());
                hotelInfo.setAttention(hotelBaseInfos.get(i).getAttention());
                hotelInfo.setHid(hotelBaseInfos.get(i).getHid());
                hotelInfo.setLat(hotelBaseInfos.get(i).getLat());
                hotelInfo.setLon(hotelBaseInfos.get(i).getLon());
                hotelInfo.setPoi(hotelBaseInfos.get(i).getPoi());
                List<FileInfo> fileInfos1 = fileInfoCollect.get("hotel:" + hotelBaseInfos.get(i).getHid());
                List<String> imgeList = new ArrayList<>();
                if (null != fileInfos1) {
                    for (int j = 0; j < fileInfos1.size(); j++) {
                        imgeList.add(fileInfos1.get(j).getUrl());
                    }
                }
                hotelInfo.setImages(imgeList);
                hotelInfo.setPhone(hotelBaseInfos.get(i).getContactPhone());
                hotelInfo.setShortName(hotelBaseInfos.get(i).getShortName());
                hotelInfos.add(hotelInfo);
            }
            responseData.setData(hotelInfos);
        } catch (Exception e) {
            log.error("",e);
            responseData.setData(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getMobileHotelRoomTypeList(GetMobileHotelRoomTypeListRequest getMobileHotelRoomTypeListRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(getMobileHotelRoomTypeListRequest.getSessionToken());
            if (null == user) {
                responseData.setMsg(HOTEL_CONST.TOKENISNULL);
                responseData.setCode(-1);
                return responseData;
            }
            if (null == getMobileHotelRoomTypeListRequest.getHid()) {
                responseData.setMsg(HOTEL_CONST.HOTEL_IS_NULL);
                responseData.setCode(-1);
                return responseData;
            }
            //首先查询酒店所有房型信息
            ParamSearch paramSearch = new ParamSearch();
            paramSearch.setHid(user.getHid());
            List<HotelRoomTypeView> hotelRoomTypeList = paramDao.getHotelRoomTypeList(paramSearch);

            JSONObject roomTypeMap = new JSONObject();
            List<HotelRoomTypeView> hotelRoomTypeViews = new ArrayList<>();
            for (int i = 0; i < hotelRoomTypeList.size(); i++) {
                if (hotelRoomTypeList.get(i).getState() == 1) {
                    hotelRoomTypeViews.add(hotelRoomTypeList.get(i));
                    roomTypeMap.put(hotelRoomTypeList.get(i).getRoomTypeId(), hotelRoomTypeList.get(i));
                }
            }
            Integer checkInType = getMobileHotelRoomTypeListRequest.getCheckInType();
            if (null == checkInType) {
                checkInType = 1;
            }

            String beginTime = getMobileHotelRoomTypeListRequest.getBeginTime();

            if (null == beginTime || beginTime.equals("")) {
                responseData.setMsg(HOTEL_CONST.PARAM_ERROR);
                responseData.setCode(-1);
                return responseData;
            }

            String endTime = getMobileHotelRoomTypeListRequest.getEndTime();

            if (null == endTime || endTime.equals("")) {
                responseData.setMsg(HOTEL_CONST.PARAM_ERROR);
                responseData.setCode(-1);
                return responseData;
            }

            RoomTypeImageSearch roomTypeImageSearch = new RoomTypeImageSearch();
            roomTypeImageSearch.setHid(user.getHid());
            List<RoomTypeImage> roomTypeImages = roomTypeImageDao.selectBySearch(roomTypeImageSearch);
            Map<Integer, List<RoomTypeImage>> imgMap = roomTypeImages.stream().collect(Collectors.groupingBy(RoomTypeImage::getRoomTypeId));

            //日租房
            if (checkInType == 1) {
                //查询酒店的价格信息
                RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
                roomDayPriceSearch.setHid(user.getHid());
                roomDayPriceSearch.setRoomRateId(Integer.parseInt(getMobileHotelRoomTypeListRequest.getRataCode()));
                roomDayPriceSearch.setDayTimeMin(Integer.parseInt(beginTime.replaceAll("-", "")));
                roomDayPriceSearch.setDayTimeMax(Integer.parseInt(endTime.replaceAll("-", "")));
                List<RoomDayPrice> roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);

                Map<String, List<RoomDayPrice>> roomTypePriceMap = new HashMap<>();
                for (RoomDayPrice roomDayPrice : roomDayPrices) {
                    Integer roomTypeId = roomDayPrice.getRoomTypeId();
                    if (roomTypePriceMap.containsKey(roomTypeId.toString())) {
                        roomTypePriceMap.get(roomTypeId.toString()).add(roomDayPrice);
                    } else {
                        List<RoomDayPrice> roomDayPriceList = new ArrayList<>();
                        roomDayPriceList.add(roomDayPrice);
                        roomTypePriceMap.put(roomTypeId.toString(), roomDayPriceList);
                    }
                }

                //查询早餐券信息
                RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
                roomRateCodeSpecificSearch.setHid(user.getHid());
                roomRateCodeSpecificSearch.setRateId(Integer.parseInt(getMobileHotelRoomTypeListRequest.getRataCode()));

                List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);


                Map<Integer, List<RoomRateCodeSpecific>> rateMap = roomRateCodeSpecifics.stream().collect(Collectors.groupingBy(RoomRateCodeSpecific::getRoomTypeId));


                // 3.获取两个日期差
                List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(beginTime, endTime);
                //查询酒店房型的可售信息
                AvailableRoom availableRoom = new AvailableRoom();
                availableRoom.setStartTime(beginTime);
                availableRoom.setEndTime(endTime);
                availableRoom.setType(1);
                availableRoom.setSessionToken(getMobileHotelRoomTypeListRequest.getSessionToken());
                Map<String, Object> canUserRoomType = this.findavailableRoom(availableRoom);
                log.info("canUserRoomType={}",canUserRoomType);
                JSONObject canUseRoomMap = JSONObject.fromObject(canUserRoomType.get("canUseRoomMap"));
                JSONObject canUseRoomNum = JSONObject.fromObject(canUserRoomType.get("canUseRoomNum"));
                JSONObject trrb = JSONObject.fromObject(canUserRoomType.get("trrb"));
                if (!canUserRoomType.get("Result").toString().equals("Success")) {
                    throw new Exception("查询酒店房型信息失败");
                }
                List<GetMobileGroupHotelView> roomTypeList = new ArrayList<>();
                for (int i = 0; i < hotelRoomTypeViews.size(); i++) {
                    GetMobileGroupHotelView getMobileGroupHotelView = new GetMobileGroupHotelView();
                    getMobileGroupHotelView.setRoomTypeId(hotelRoomTypeViews.get(i).getRoomTypeId());
                    getMobileGroupHotelView.setRoomTypeName(hotelRoomTypeViews.get(i).getRoomTypeName());
                    int roomNum;
                    if (!canUseRoomNum.containsKey(hotelRoomTypeViews.get(i).getRoomTypeId().toString())) {
                        roomNum = 0;
                    } else {
                        roomNum = canUseRoomNum.getInt(hotelRoomTypeViews.get(i).getRoomTypeId().toString());
                    }
                    getMobileGroupHotelView.setRoomNum(roomNum);
                    getMobileGroupHotelView.setBreakFastNum(rateMap.get(getMobileGroupHotelView.getRoomTypeId()).get(0).getBreakfastNum());
                    getMobileGroupHotelView.setRemark(hotelRoomTypeViews.get(i).getDes());
                    ArrayList<String> images = new ArrayList<>();
                    List<RoomTypeImage> rtis = imgMap.get(hotelRoomTypeViews.get(i).getRoomTypeId());
                    if (rtis != null) {
                        for (RoomTypeImage r : rtis) {
                            if (r.getUrl() == null || r.getUrl().length() < 2) {
                                continue;
                            }
                            images.add(r.getUrl());
                        }
                    }
                    getMobileGroupHotelView.setImages(images);
                    List<GetMobileGroupHotelView.PriceInfoView> priceInfoViewList = new JSONArray();
                    double SumPrice = 0.00;
                    for (String date : allDayListBetweenDate) {
                        if (roomTypePriceMap.containsKey(hotelRoomTypeViews.get(i).getRoomTypeId().toString())) {
                            List<RoomDayPrice> roomDayPriceList = roomTypePriceMap.get(hotelRoomTypeViews.get(i).getRoomTypeId().toString());
                            for (int j = 0; j < roomDayPriceList.size(); j++) {
                                if (date.replace("-", "").equals(roomDayPriceList.get(j).getDayTime().toString())) {
                                    GetMobileGroupHotelView.PriceInfoView priceInfo = new GetMobileGroupHotelView.PriceInfoView();
                                    priceInfo.setDate(date);
                                    priceInfo.setPrice(String.valueOf(roomDayPriceList.get(j).getPrice() / 100.0));
                                    SumPrice += roomDayPriceList.get(j).getPrice() / 100.0;
                                    priceInfoViewList.add(priceInfo);
                                    continue;
                                }
                            }
                        }
                        //每日房价中不包含房型房价信息
                        else {
                            double v = hotelRoomTypeViews.get(i).getPrice() / 100.0;
                            GetMobileGroupHotelView.PriceInfoView priceInfo = new GetMobileGroupHotelView.PriceInfoView();
                            priceInfo.setDate(date);
                            priceInfo.setPrice(String.valueOf(v));
                            SumPrice += v;
                            priceInfoViewList.add(priceInfo);
                        }
                    }

                    getMobileGroupHotelView.setPriceList(priceInfoViewList);
                    getMobileGroupHotelView.setCountMoney(String.valueOf(SumPrice));
                    roomTypeList.add(getMobileGroupHotelView);
                }
                responseData.setData(roomTypeList);
            }
            //钟点房
            else if (checkInType == 2) {

            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setData(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getMobileHotelRoomList(GetMobileHotelRoomListRequest getMobileHotelRoomListRequest) {
        return null;
    }

    @Override
    public ResponseData mobileCreateBookingOrder(MobileCreateBookingOrderRequest mobileCreateBookingOrderRequest) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = mobileCreateBookingOrderRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Integer hid = mobileCreateBookingOrderRequest.getHid();
            if (hid == null || hid < 0) {
                responseData.setCode(-1);
                responseData.setMsg(HOTEL_CONST.PARAM_ERROR);
                return responseData;
            }
            user.setHid(mobileCreateBookingOrderRequest.getHid());
            HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
            hotelBusinessDaySearch.setHid(user.getHid());
            HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);
            user.setBusinessDay(hotelBusinessDay.getBusinessDay());
            //生成预订单编号
            String sn = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.BOOK, this.stringRedisTemplate);
            Date date = new Date();
            BookingOrder book = new BookingOrder();
            book.setSn(sn);
            book.setHotelGroupId(user.getHotelGroupId());
            book.setHid(user.getHid());
            book.setCreateTime(date);
            book.setCreateUserId(user.getUserId());
            book.setCreateUserName(user.getUserName());
            book.setAcceptTime(date);
            book.setRoomCount(mobileCreateBookingOrderRequest.getRoomCount());
            book.setOrderTime(date);

            book.setBookingName(mobileCreateBookingOrderRequest.getBookingName());
            String phone = mobileCreateBookingOrderRequest.getBookingPhone();
            if (phone.equals("null")) {
                phone = "";
            }
            book.setBookingPhone(phone);
            book.setBookingName(mobileCreateBookingOrderRequest.getBookingName());
            book.setCheckinTime(mobileCreateBookingOrderRequest.getCheckinTime());
            book.setCheckoutTime(mobileCreateBookingOrderRequest.getCheckoutTime());
            book.setKeepTime(mobileCreateBookingOrderRequest.getKeepTime());
            //入住天数
            int dayCount = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(mobileCreateBookingOrderRequest.getCheckinTime()), HotelUtils.parseDate2Str(mobileCreateBookingOrderRequest.getCheckoutTime())).size();
            book.setDayCount(dayCount);
            book.setFromHid(user.getHid());
            if (mobileCreateBookingOrderRequest.getThirdPlatformOrderCode() != null) {
                book.setThirdPlatformOrderCode(mobileCreateBookingOrderRequest.getThirdPlatformOrderCode());
            }
            //客源类型
            int resourceId = mobileCreateBookingOrderRequest.getResourceId();
            book.setResourceId(resourceId);
            // 1.散客 2.会员
            if (resourceId == 2) {
                book.setCardId(mobileCreateBookingOrderRequest.getVipMsg().getCardId());
                book.setCardNo(mobileCreateBookingOrderRequest.getVipMsg().getCardNo());
            }
            if (resourceId == 3 || resourceId == 4 || resourceId == 5) {
                book.setCompanyId(mobileCreateBookingOrderRequest.getArMsg().getArId());
                book.setCompanyName(mobileCreateBookingOrderRequest.getArMsg().getArName());
                book.setCompanyAccountId(mobileCreateBookingOrderRequest.getArAntMsg().getId());
            }
            //25 ，小程序
            book.setFromType(25);
            if (null != mobileCreateBookingOrderRequest.getFromType()) {
                book.setFromType(mobileCreateBookingOrderRequest.getFromType());
            }
            /**
             *  缺失
             *      total_price 订单总金额
             *      unit_price  单价待补充
             *  待补充
             */
            Integer payMoney = 0;
            Object payMoneyObj = mobileCreateBookingOrderRequest.getPayPrice();
            if (payMoneyObj != null && !"".equals(payMoneyObj.toString())) {
                payMoney = mobileCreateBookingOrderRequest.getPayPrice();
            }
            book.setPayPrice(0);
            if (mobileCreateBookingOrderRequest.getSumMoney() != null) {
                Double d = mobileCreateBookingOrderRequest.getSumMoney() * 100;
                book.setTotalPrice(d.intValue());
            }
            book.setFromHid(user.getHid());
            //预订单类型 1.日租 2.钟点 3.长足
            book.setOrderType(1);
            Object orderType = mobileCreateBookingOrderRequest.getOrderType();
            if (orderType != null) {
                book.setOrderType(Integer.parseInt(orderType.toString()));
            }
            //订单状态 订单状态 1.有效 2.NoShow 3.部分入住 4.全部入住 5.已取消 6.入住完成
            book.setOrderStatus(BOOK.STA_YX);
            Object orderStatus = mobileCreateBookingOrderRequest.getOrderStatus();
            if (orderStatus != null) {
                book.setOrderStatus(Integer.parseInt(orderStatus.toString()));
            }
            if (mobileCreateBookingOrderRequest.getRemark() != null) {
                book.setRemark(mobileCreateBookingOrderRequest.getRemark());
            }

            // 订单时间
            book.setOrderYear(user.getBusinessYear());
            book.setOrderYearMonth(user.getBusinessMonth());
            book.setBusinessDay(user.getBusinessDay());
            book.setClassId(user.getClassId());
            book.setCreateTime(date);
            book.setCreateUserId(user.getSessionId());
            book.setCreateUserName(user.getUserName());
            book.setUpdateTime(date);
            book.setUpdateUserId(user.getSessionId());
            book.setUpdateUserName(user.getUserName());
            book.setRoomTypeSummary(mobileCreateBookingOrderRequest.getRoomTypeSummary());

            /**
             * 查询预订单辅助房态
             *  如果当前时间等于 预抵时间，则状态为预抵。
             *      否则 辅助房态为预离。
             */
            RoomAuxiliary auxiliary = new RoomAuxiliary();
            String nowDate = HotelUtils.currentDate();
            if (nowDate.equals(HotelUtils.currentDate(mobileCreateBookingOrderRequest.getCheckinTime()))) {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_ARRIVALS);
            } else {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_CREATE);
            }

            // 团队房  isGroup==1则为团队
            RegistGroup registGroup = null;
            int isGroup = mobileCreateBookingOrderRequest.getIsGroup();
            if (isGroup == 1) {
                registGroup = new RegistGroup();
                registGroup.setBusinessDay(user.getBusinessDay());
                registGroup.setHid(user.getHid());
                registGroup.setClassId(user.getClassId());
                registGroup.setCreateTime(new Date());
                registGroup.setCreateUserId(user.getUserId());
                registGroup.setCreateUserName(user.getUserName());
                registGroup.setSn(book.getSn());
                registGroup.setPayType(0);
                registGroup.setGroupName(mobileCreateBookingOrderRequest.getGroupName());
                registGroup.setSumRooms(book.getRoomCount());
                registGroup.setUpdateTime(date);
                registGroup.setUpdateUserId(user.getUserId());
                registGroup.setUpdateUserName(user.getUserName());
                registGroup.setState(1);
            }


            /**
             * 添加操作日志
             */
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);
            oprecord.setMainId(sn);
            oprecord.setBookingOrderId(book.getBookingOrderId());
            oprecord.setDescription("创建预订单,订单号:" + sn);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecords.add(oprecord);

            /**
             * 2.添加预订单设置
             */
            BookingOrderConfig bookingOrderConfig = new BookingOrderConfig();
            bookingOrderConfig.setPriceSecrecy(mobileCreateBookingOrderRequest.getPriceSecrecy());
            bookingOrderConfig.setInfoSecrecy(mobileCreateBookingOrderRequest.getInfoSecrecy());
            bookingOrderConfig.setAutoCheckin(mobileCreateBookingOrderRequest.getAutoCheckIn());
            bookingOrderConfig.setNoDeposit(mobileCreateBookingOrderRequest.getNoDposit());
            bookingOrderConfig.setNoPrice(mobileCreateBookingOrderRequest.getNoPrice());
            bookingOrderConfig.setContinueRes(mobileCreateBookingOrderRequest.getContinueRes());
            bookingOrderConfig.setAutoAr(0);
            if (mobileCreateBookingOrderRequest.getAutoAr() != null) {
                bookingOrderConfig.setAutoAr(mobileCreateBookingOrderRequest.getAutoAr());
            }
            bookingOrderConfig.setAvePrice(0);
            if (mobileCreateBookingOrderRequest.getAvePrice() != null) {
                bookingOrderConfig.setAutoAr(mobileCreateBookingOrderRequest.getAvePrice());
            }

            /**
             *  取出两个时间的日期差
             */
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.currentDate(mobileCreateBookingOrderRequest.getCheckinTime()), HotelUtils.currentDate(mobileCreateBookingOrderRequest.getCheckoutTime()));

            // 要添加的辅助房态
            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            /**
             * 3.添加预订房型信息
             */
            List<CreateBookingOrderRequest.roomType> roomTypeList = mobileCreateBookingOrderRequest.getRoomTypeList();

            BookingOrderDailyPrice bodp = new BookingOrderDailyPrice();

            // 需要添加的房型
            ArrayList<BookingOrderRoomType> addBort = new ArrayList<>();

            for (int i = 0; i < roomTypeList.size(); i++) {
                // 预订房型数据
                CreateBookingOrderRequest.roomType roomType = roomTypeList.get(i);
                // 房间数据
                List<CreateBookingOrderRequest.roomType.roomInfo> roomList = roomType.getRoomList();

                // 创建预订房型信息
                BookingOrderRoomType bort = new BookingOrderRoomType();
                bort.setHid(user.getHid());
                bort.setHotelGroupId(user.getHotelGroupId());
                bort.setArriveTime(HotelUtils.currentDate(mobileCreateBookingOrderRequest.getCheckinTime()) + " " + book.getKeepTime() + ":00");
                bort.setRoomTypeId(roomType.getRoomTypeId());
                bort.setRoomTypeNum(roomType.getNum());
                bort.setHasRoomNum(roomList.size());
                bort.setPriceCodeId(roomType.getRateId());
                bort.setPriceCode(roomType.getRateCode());
                book.setRateCodeId(bort.getPriceCodeId());
                book.setRateCodeName(bort.getPriceCode());
                bort.setState(BOOK.STA_YX);
                bort.setOrderState(BOOK.STA_YX);
                bort.setCreateTime(date);
                bort.setCreateUserId(user.getUserId());
                bort.setCreateUserName(user.getUserName());
                bort.setUpdateTime(date);
                bort.setUpdateUserId(user.getUserId());
                bort.setUpdateUserName(user.getUserName());
                bort.setCheckinTime(book.getCheckinTime());
                bort.setCheckoutTime(book.getCheckoutTime());

                //增加预订房型操作日志
                Oprecord oprecord1 = new Oprecord(user);
                ;
                oprecord1.setMainId(sn);
                oprecord1.setRegistId(book.getBookingOrderId());
                oprecord1.setDescription("对订单 : " + sn + "，添加预订房型：" + roomType.getRoomTypeName() + ",房间数量: " + bort.getRoomTypeNum() + ",房价码:" + bort.getPriceCode());
                oprecord1.setOccurTime(HotelUtils.currentTime());
                oprecords.add(oprecord1);

                List<CreateBookingOrderRequest.roomType.priceInfo> priceList = roomType.getPriceList();
                // 房型添加的房价
                ArrayList<BookingOrderDailyPrice> rtPriceList = new ArrayList<>();

                for (int pl = 0; pl < priceList.size(); pl++) {
                    CreateBookingOrderRequest.roomType.priceInfo priceInfo = priceList.get(pl);
                    bodp = new BookingOrderDailyPrice();
                    bodp.setBookingOrderRoomNumId(0);
                    bodp.setHid(user.getHid());
                    bodp.setHotelGroupId(user.getHotelGroupId());
                    bodp.setPrice(priceInfo.getPrice());
                    bodp.setDailyTime(Integer.parseInt(priceInfo.getDate().replace("-", "")));
                    bodp.setRoomTypeId(bort.getRoomTypeId());
                    bodp.setRoomNumId(0);
                    bodp.setBreakNum(0);
                    bodp.setDailyState(1);
                    bodp.setRateCodeId(bort.getPriceCodeId());
                    bodp.setRateCode(bort.getPriceCode());
                    bodp.setIsStayover(0);
                    bodp.setCreateTime(date);
                    bodp.setCreateUserId(user.getUserId());
                    bodp.setCreateUserName(user.getUserName());
                    bodp.setUpdateTime(date);
                    bodp.setUpdateUserId(user.getUserId());
                    bodp.setUpdateUserName(user.getUserName());
                    rtPriceList.add(bodp);
                }

                bort.setBookingOrderDailyPrices(rtPriceList);

                // 需要添加的预订房间

                ArrayList<BookingOrderRoomNum> bookingOrderRoomNums = new ArrayList<>();

                for (int k = 0; k < roomList.size(); k++) {

                    CreateBookingOrderRequest.roomType.roomInfo roomInfo = roomList.get(k);

                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(book.getBookingOrderId());
                    bookingOrderRoomNum.setBookingOrderRoomTypeId(bort.getId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(bort.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(roomInfo.getRoomInfoId());
                    bookingOrderRoomNum.setRoomNum(roomInfo.getRoomNum());
                    if (roomInfo.getRoomCode() != null) {
                        bookingOrderRoomNum.setRoomCode(roomInfo.getRoomCode());
                    }
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setCheckinTime(book.getCheckinTime());
                    bookingOrderRoomNum.setCheckoutTime(book.getCheckoutTime());
                    bookingOrderRoomNum.setRowRoom(1);
                    bookingOrderRoomNum.setRateCodeId(bort.getPriceCodeId());
                    bookingOrderRoomNum.setRateCode(bort.getPriceCode());
                    bookingOrderRoomNum.setBookingOrderDailyPrices(rtPriceList);


                    //记录操作日志
                    Oprecord oprecord2 = new Oprecord(user);
                    oprecord2.setMainId(sn);
                    oprecord2.setRegistId(book.getBookingOrderId());
                    oprecord2.setDescription("对订单 : " + sn + "，进行排房：" + bookingOrderRoomNum.getRoomNum());
                    oprecord2.setOccurTime(HotelUtils.currentTime());
                    oprecords.add(oprecord2);

                    //添加辅助房态
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(bookingOrderRoomNum.getRoomNumId());
                    roomAuxiliaryRelation.setRoomNum(bookingOrderRoomNum.getRoomNum());
                    roomAuxiliaryRelation.setBookingOrderId(book.getBookingOrderId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(auxiliary.getRoomAuxiliaryId());
                    roomAuxiliaryRelation.setSort(auxiliary.getSort());

                    roomAuxiliaryRelations.add(roomAuxiliaryRelation);

                    bookingOrderRoomNums.add(bookingOrderRoomNum);
                }

                // 添加预订未分房的房间
                for (int k = 0; k < bort.getRoomTypeNum() - bort.getHasRoomNum(); k++) {

                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(book.getBookingOrderId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(bodp.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(0);
                    bookingOrderRoomNum.setRoomNum("0");
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setCheckinTime(book.getCheckinTime());
                    bookingOrderRoomNum.setCheckoutTime(book.getCheckoutTime());
                    bookingOrderRoomNum.setRowRoom(0);
                    bookingOrderRoomNum.setBookingOrderDailyPrices(rtPriceList);
                    bookingOrderRoomNums.add(bookingOrderRoomNum);

                }
                bort.setBookingOrderRoomNums(bookingOrderRoomNums);
                addBort.add(bort);
            }

            // 支付类型 1.微信支付 2.会员储值支付
            Integer payType = 1;
            Object payTypeObj = mobileCreateBookingOrderRequest.getPayType();
            if (payTypeObj != null) {
                payType = Integer.parseInt(payTypeObj.toString());
            }

            Account account = null;

            if (payType == 2) {

                Integer totalPrice = book.getTotalPrice();

                book.setPayType(2);
                book.setPayPrice(totalPrice);

                JSONObject vipParam = new JSONObject();
                vipParam.put("cardId", book.getCardId());
                vipParam.put("type", 1);
                vipParam.put("money", totalPrice);
                vipParam.put(ER.SESSION_TOKEN, sessionToken);

                ResponseData responseData1 = memberService.memberConsumption(vipParam);

                int code = responseData1.getCode();
                if (code < 0) {
                    throw new Exception(responseData1.getMsg());
                }

                Object data = responseData1.getData();
                Date date1 = new Date();
                account = new Account();
                String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
                account.setAccountId(accountId);
                account.setHid(user.getHid());
                account.setHotelGroupId(user.getHotelGroupId());
                account.setCreateUserId(user.getUserId());
                account.setCreateUserName(user.getUserName());
                account.setCreateTime(date1);
                account.setUpdateTime(date1);
                account.setUpdateUserId(user.getUserId());
                account.setUpdateUserName(user.getUserName());
                account.setIsCancel(0);
                account.setAccountYear(user.getBusinessYear());
                account.setAccountYearMonth(user.getBusinessMonth());
                account.setBusinessDay(user.getBusinessDay());
                account.setClassId(user.getClassId());
                account.setPrice(totalPrice);
                account.setSettleAccountTime(new Date());
                account.setRegistState(0);
                //消费-付款
                account.setPayType(2);
                account.setSaleNum(1);
                account.setUintPrice(account.getPrice());
                account.setPayClassId(5);
                account.setPayClassName("会员储值");
                account.setPayCodeId("9600");
                account.setPayCodeName("会员储值卡");
                account.setAccountType(1);
                account.setRemark("小程序预订会员支付");
                account.setThirdAccoutId(data + "");
                account.setRefundPrice(0);
                account.setRegistPersonId(0);
                account.setRoomNum("");
                account.setRegistPersonName("");
                account.setRegistPersonId(0);


            }

            wxServiceTransaction.addBookService(book, registGroup, addBort, roomAuxiliaryRelations, oprecords, bookingOrderConfig, account);



            responseData.setData(book);
            this.addOprecords(oprecords);

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
//                        HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
//                        baseService.turnAlways(user);
                       /* SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                        smsHotelSendRecordRequest.setLocationId(SMS_LOC.ORDER_CREATE);
                        smsHotelSendRecordRequest.setSessionToken(sessionToken);
                        smsHotelSendRecordRequest.setPhoneNumber(smsParam.getString(SMS_LOC.PHONE));
                        ArrayList<String> list = (ArrayList<String>)JSONArray.toList(smsParam.getJSONArray(SMS_LOC.PARAM), String.class);
                        smsHotelSendRecordRequest.setParams(list);
                        baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);

                        HashMap<String, String> filed = new HashMap<>();
                        filed.put("sn", book.getSn());
                        filed.put("rtdesc", book.getRoomTypeSummary());
                        HashMap<String, String> dataMap = new HashMap<>();
                        dataMap.put("bookId", book.getBookingOrderId() + "");
                        HashMap<String, String> onClickCbData = new HashMap<>();
                        onClickCbData.put("bookingOrderId", book.getBookingOrderId().toString());
                        baseService.push(user.getHotelGroupId(), user.getHid(), 20, filed, dataMap, true, true, onClickCbData);*/

                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {

            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");

        }

        return responseData;
    }

    @Override
    public ResponseData getMobileOrderList() {
        return null;
    }
}
