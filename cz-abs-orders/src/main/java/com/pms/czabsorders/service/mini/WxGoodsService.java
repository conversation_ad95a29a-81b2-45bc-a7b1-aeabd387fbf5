package com.pms.czabsorders.service.mini;

import com.pms.czabsorders.bean.mini.AddGoodsParam;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmswarehouse.bean.request.HotelGoodsRequest;
import com.pms.pmswarehouse.bean.search.GoodsShoppingOrderSearch;
import net.sf.json.JSONObject;

public interface WxGoodsService {

    public ResponseData addGoodsToRegist(AddGoodsParam addGoodsParam, TbUserSession user);


    /**
     * 查询商品销售记录
     * @param goodsShoppingOrderSearch
     * @return
     */
    public ResponseData searchGoodsRecord(GoodsShoppingOrderSearch goodsShoppingOrderSearch);

    /**
     * 撤销商品销售记录
     * @param goodsShoppingOrderSearch
     * @return
     */
    public ResponseData cancelWxSaleGoods(GoodsShoppingOrderSearch goodsShoppingOrderSearch);

    /**
     * 取消预订单
     * @param param
     * @return
     */
    public ResponseData cancelBook(JSONObject param);

    /**
     * 获取微信可售商品
     * @param request
     * @return
     */
    public ResponseData findGoodsByWechat(HotelGoodsRequest request);


    public ResponseData findGoodsRecord(GoodsShoppingOrderSearch request);

}
