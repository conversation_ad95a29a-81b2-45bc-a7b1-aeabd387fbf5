package com.pms.czabsorders.service;


import com.alibaba.excel.util.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.*;
import com.pms.czhotelfoundation.bean.code.Area;
import com.pms.czhotelfoundation.bean.hotel.HotelBaseInfo;
import com.pms.czhotelfoundation.bean.price.RoomDayPrice;
import com.pms.czhotelfoundation.bean.price.RoomDayPriceShow;
import com.pms.czhotelfoundation.bean.price.RoomRateCode;
import com.pms.czhotelfoundation.bean.price.search.RoomDayPriceSearch;
import com.pms.czhotelfoundation.bean.room.RoomType;
import com.pms.czhotelfoundation.bean.room.RoomTypeDataRes;
import com.pms.czhotelfoundation.bean.room.RoomTypeRes;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeSearch;
import com.pms.czhotelfoundation.dao.OtaChanelConfDao;
import com.pms.czhotelfoundation.dao.OtaChannelConfExpandDao;
import com.pms.czhotelfoundation.dao.RoomTypePriceMapper;
import com.pms.czhotelfoundation.dao.code.AreaDao;
import com.pms.czhotelfoundation.dao.hotel.HotelBaseInfoDao;
import com.pms.czhotelfoundation.dao.price.RoomDayPriceDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.constant.ECache;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.enums.HotelTypeEnum;
import com.pms.czpmsutils.request.*;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.request.GetHotelDataInfoParam;
import com.pms.pmsorder.bean.search.BookingOrderDailyPriceSearch;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.bean.view.ResourceView;
import com.pms.pmsorder.dao.BookingOrderDailyPriceDao;
import com.pms.pmsorder.dao.BookingOrderDao;
import com.pms.pmsorder.dao.PmsMainDao;
import com.pms.pmsorder.dao.RegistDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;




/**
 * @Author: 陈星宇
 * @CreateTime: 2025-07-01
 * @Description:
 */
@Service
@Slf4j
@Primary
public class HotelAiServiceImpl extends BaseService implements HotelAiService {

    @Resource
    private AccountDao accountDao;

    @Resource
    private OtaChanelConfDao otaChanelConfDao;

    @Resource
    private RegistDao registDao;

    @Resource
    private PmsMainDao pmsMainDao;

    @Resource
    private BookingOrderDao bookingOrderDao;

    @Resource
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Resource
    private HotelBaseInfoDao hotelBaseInfoDao;


    @Resource
    private RoomTypePriceMapper roomTypePriceMapper;

    @Resource
    private RoomTypeDao roomTypeDao;

    @Resource
    private OtaChannelConfExpandDao otaChannelConfExpandDao;

    @Resource
    private RoomRateCodeDao roomRateCodeDao;

    @Resource
    private RoomDayPriceDao roomDayPriceDao;

    @Autowired
    private RestTemplate restTemplate;

    @Resource
    private AreaDao areaDao;




    @Override
    public ResponseData dataIndicators(DataIndicatorsReq dataIndicatorsReq) {
        ResponseData responseData = ResponseData.newSuccessData();
        JSONObject result = new JSONObject();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(dataIndicatorsReq.getSessionToken());
            List<String> dates = findEveryDay(dataIndicatorsReq.getStartTime(), dataIndicatorsReq.getEndTime());
            //总收入
            Integer revenue = getAllRevenue(tbUserSession, dates);
            //todo bcs不对 要用night_audit_room_type的数据
            Integer bcs = getBcs(dataIndicatorsReq.getSessionToken(),dataIndicatorsReq.getStartTime(),dataIndicatorsReq.getEndTime());
            if( revenue !=0 && bcs!=0){
                result.put("revpar", BigDecimal.valueOf(revenue)
                        .divide(BigDecimal.valueOf(bcs), 2, RoundingMode.HALF_UP).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            }else {
                result.put("revpar","0.00");
            }

            Integer allHotelRoomTypeRegister = getAllHotelRoomTypeRegister(tbUserSession, dates);
            //出租率
            if(allHotelRoomTypeRegister !=0 && bcs!=0){
                result.put("OCC", BigDecimal.valueOf(allHotelRoomTypeRegister)
                        .divide(BigDecimal.valueOf(bcs), 2, RoundingMode.HALF_UP).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            }else {
                result.put("OCC","0.00");
            }


            //售出客房数
            if(revenue !=0 && allHotelRoomTypeRegister!=0){
                result.put("ADR",BigDecimal.valueOf(revenue)
                        .divide(BigDecimal.valueOf(allHotelRoomTypeRegister), 2, RoundingMode.HALF_UP).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            }else {
                result.put("ADR","0.00");
            }
            responseData.setData(result);
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData overviewOrder(OverviewOrderReq overviewOrderReq) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            //订单数
            TbUserSession tbUserSession = this.getTbUserSession(overviewOrderReq.getSessionToken());
            String startTime = overviewOrderReq.getStartTime()+" 00:00:00";
            String endTime = overviewOrderReq.getEndTime()+" 23:59:59";
            Date startDate = DateUtil.parseDate(startTime, DateUtil.DATE_S);
            Date endDate = DateUtil.parseDate(endTime, DateUtil.DATE_S);
            List<BookingOrder> orderList = bookingOrderDao.selectByTime(startDate, endDate, tbUserSession.getHid());
            OverviewOrderResp overviewOrderResp = new OverviewOrderResp();
            overviewOrderResp.setOrderNum(orderList.size());
            List<String> dates = findEveryDay(overviewOrderReq.getStartTime(), overviewOrderReq.getEndTime());
            List<Line> lineList = new ArrayList<>();
            for (String date : dates) {
                String start = date+" 00:00:00";
                String end = date+" 23:59:59";
                Date sDate = DateUtil.parseDate(start, DateUtil.DATE_S);
                Date eDate = DateUtil.parseDate(end, DateUtil.DATE_S);
                Line line = new Line();
                line.setName(date);
                line.setValue1(bookingOrderDao.selectByTime(sDate, eDate, tbUserSession.getHid()).size());
                Integer businessDay = Integer.valueOf(date.replace("-", ""));
                Integer everyDayRoomRevenue = everyDayRoomRevenue(tbUserSession, businessDay);
                double value = BigDecimal.valueOf(everyDayRoomRevenue).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
                line.setValue2(value);
                lineList.add(line);
            }
            overviewOrderResp.setLineList(lineList);
            //房费收入
            Integer allRoomRevenue = getAllRoomRevenue(tbUserSession, dates);
            BigDecimal allRoomRevenueBigDecimal = BigDecimal.valueOf(allRoomRevenue).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            overviewOrderResp.setSaleRoom(allRoomRevenueBigDecimal.toString());
            responseData.setData(overviewOrderResp);
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public static Boolean isNumeric(String str) {
        for (char c : str.toCharArray()) {
            if(!Character.isDigit(c)) {
                return false;
            }
        }
        return true;
    }



    @Override
    public ResponseData getPriceList(PriceListReq priceListReq) {
        log.info("房价日历分页接口开始:{}",priceListReq);
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(priceListReq.getSessionToken());
            HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(tbUserSession.getHid());
            priceListReq.setHid(tbUserSession.getHid());
            if(StringUtils.isNotBlank(priceListReq.getSearchText())){
                Boolean numeric = isNumeric(priceListReq.getSearchText());
                if (numeric) {
                    int value = Integer.parseInt(priceListReq.getSearchText());
                    Integer result = value * 100;
                    priceListReq.setSearchText(String.valueOf(result));
                }
            }
            Page<PriceListResp> priceListResps = roomTypePriceMapper.selectByPage(priceListReq);

            if(!CollectionUtils.isEmpty(priceListResps.getResult())){
                priceListResps.getResult().forEach(priceListResp -> {
                    priceListResp.setBasePrice(priceListResp.getBasePrice().replace(",",""));
                    if(StringUtils.isNotBlank(priceListResp.getMinLimitPrice())){
                        priceListResp.setMinLimitPrice(priceListResp.getMinLimitPrice().replace(",",""));
                    }
                    if(StringUtils.isNotBlank(priceListResp.getMaxLimitPrice())){
                        priceListResp.setMaxLimitPrice(priceListResp.getMaxLimitPrice().replace(",",""));
                    }
                    if(StringUtils.isNotBlank(priceListResp.getAiPrice())){
                        priceListResp.setAiPrice(priceListResp.getBasePrice().replace(",",""));
                    }

                    JSONObject param = new JSONObject();
                    param.put("date",DateUtil.format(new Date(),"yyyy-MM-dd"));
                    param.put("hotelName",hotelBaseInfo.getHotelName());
                    param.put("roomType",priceListResp.getRoomTypeName());
                    HotelTypeEnum hotelTypeEnum = HotelTypeEnum.fromCode(hotelBaseInfo.getHotelType());
                    param.put("typeId",hotelTypeEnum.getDescription());
                    param.put("star",Objects.isNull(hotelBaseInfo.getStar()) ? 2 : hotelBaseInfo.getStar());
                    param.put("longitude",hotelBaseInfo.getLon());
                    param.put("latitude",hotelBaseInfo.getLat());
                    param.put("roomTypeId",priceListResp.getId().toString());
                    Area area = areaDao.selectById(hotelBaseInfo.getCity());
                    param.put("city",area.getName());
                    param.put("cityCode",hotelBaseInfo.getCity().toString());
                    param.put("hotelId",hotelBaseInfo.getHid().toString());
                    Map<String, String> headers = new HashMap<>();
                    headers.put("Content-Type", "application/json");
                    headers.put("Hid", hotelBaseInfo.getHid().toString());
                    log.info("send_url:{} body:{} header:{}","http://hotel-price-infer.com/price/aiprice",param,headers);
                    try {
                        AipriceResp aipriceResp =   HttpUtil.sendPostRequestWithHeader("http://hotel-price-infer.com/price/aiprice", param, headers,AipriceResp.class);
                        if(Objects.nonNull(aipriceResp)){
                            priceListResp.setAiPrice(aipriceResp.getPredictPrice().toString());
                        }
                    }catch (Exception e){
                        log.error("",e);
                        responseData.setResult(ER.ERR);
                        responseData.setMsg("业务处理异常");
                    }

                });
            }
            responseData.setData(priceListResps);
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        log.info("房价日历分页接口结束:{}",responseData);
        return responseData;

    }

    @Override
    public ResponseData editRoomTypePrice(EditRoomTypePriceReq editRoomTypePriceReq) {
        log.info("房价日历编辑接口开始:{}",editRoomTypePriceReq);
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(editRoomTypePriceReq.getSessionToken());
            RoomTypePrice roomTypePrice = roomTypePriceMapper.selectByRoomTypeId(editRoomTypePriceReq.getId(), tbUserSession.getHid());
            log.info("roomTypePrice:{}",roomTypePrice);
            if(Objects.nonNull(roomTypePrice)){
                roomTypePrice.setLessPrice(Integer.valueOf(editRoomTypePriceReq.getMinLimitPrice()));
                if(StringUtils.isNotBlank(editRoomTypePriceReq.getMaxLimitPrice())){
                    roomTypePrice.setMorePrice(Integer.valueOf(editRoomTypePriceReq.getMaxLimitPrice()));
                }else {
                    roomTypePrice.setMorePrice(null);
                }
                roomTypePriceMapper.updateByPrimaryKeySelective(roomTypePrice);
            }else {
                RoomTypePrice addRoomTypePrice = new RoomTypePrice();
                addRoomTypePrice.setLessPrice(Integer.valueOf(editRoomTypePriceReq.getMinLimitPrice()));
                if(StringUtils.isNotBlank(editRoomTypePriceReq.getMaxLimitPrice())){
                    addRoomTypePrice.setMorePrice(Integer.valueOf(editRoomTypePriceReq.getMaxLimitPrice()));
                }else {
                    addRoomTypePrice.setMorePrice(null);
                }
                addRoomTypePrice.setHid(tbUserSession.getHid());
                addRoomTypePrice.setCreateTime(new Date());
                addRoomTypePrice.setUpdateTime(new Date());
                addRoomTypePrice.setRoomTypeId(editRoomTypePriceReq.getId());
                roomTypePriceMapper.insertSelective(addRoomTypePrice);
            }
            RoomType roomType = roomTypeDao.selectById(editRoomTypePriceReq.getId());
            roomType.setPrice(Integer.valueOf(editRoomTypePriceReq.getBasePrice()));
            roomTypeDao.editRoomType(roomType);
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        log.info("房价日历编辑接口结束:{}",responseData);
        return responseData;
    }

    @Override
    public ResponseData getRoomTypeList(BaseRequest baseRequest) {
        log.info("获取可选房型开始:{}",baseRequest);
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(baseRequest.getSessionToken());
            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setState(1);
            roomTypeSearch.setHid(tbUserSession.getHid());
            List<RoomType> roomTypeList = roomTypeDao.selectByNameList(roomTypeSearch);
            RoomTypeDataRes roomTypeDataRes = new RoomTypeDataRes();

            if(!CollectionUtils.isEmpty(roomTypeList)) {
                List<RoomTypeRes> roomTypeResList = roomTypeList.stream().map(roomType -> {
                    RoomTypeRes roomTypeRes = new RoomTypeRes();
                    roomTypeRes.setRoomTypeId(roomType.getRoomTypeId());
                    roomTypeRes.setRoomTypeName(roomType.getRoomTypeName());
                    return roomTypeRes;
                }).collect(Collectors.toList());
                roomTypeDataRes.setList(roomTypeResList);
                responseData.setData(roomTypeDataRes);
            }
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        log.info("获取可选房型结束:{}",responseData);
        return responseData;
    }

    @Override
    public ResponseData getOptionalChannel(BaseRequest baseRequest) {
        log.info("获取可选渠道开始:{}",baseRequest);
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(baseRequest.getSessionToken());
            OtaChannelConfExpand otaChannelConfExpand = new OtaChannelConfExpand();
            otaChannelConfExpand.setHid(tbUserSession.getHid());
            List<OtaChannelConfExpand> otaOpenDetails = otaChannelConfExpandDao.queryAll(otaChannelConfExpand);
            OptionalChannelDataResp optionalChannelDataResp = new OptionalChannelDataResp();
            if(!CollectionUtils.isEmpty(otaOpenDetails)) {
                List<OptionalChannelResp> optionalChannelRespList = otaOpenDetails.stream().map(otaOpenDetail -> {
                    OtaChanelConf otaChanelConf = new OtaChanelConf();
                    otaChanelConf.setOtaChannelCode(otaOpenDetail.getChanelCode());
                    List<OtaChanelConf> list = otaChanelConfDao.queryAll(otaChanelConf);
                    OptionalChannelResp optionalChannelResp = new OptionalChannelResp();
                    if(!CollectionUtils.isEmpty(list)){
                        optionalChannelResp.setChannelId(list.get(0).getId()+"");
                        optionalChannelResp.setChannelName(list.get(0).getOtaChannelName());
                    }
                    return optionalChannelResp;
                }).collect(Collectors.toList());
                optionalChannelDataResp.setList(optionalChannelRespList);
                responseData.setData(optionalChannelDataResp);
            }
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        log.info("获取可选渠道结束:{}",responseData);
        return responseData;
    }

    @Override
    public ResponseData synchronousToChannel(SynchronousToChannelReq synchronousToChannelReq) {
        return null;
    }


    public static List<String> getFutureSevenDays(){
        List<String> futureSevenDays = new ArrayList<>();
        LocalDate date = LocalDate.now();
        for (int i = 0; i < 7; i++) {
            LocalDate localDate = date.plusDays(i);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String format = localDate.format(dateTimeFormatter);
            futureSevenDays.add(format);
        }
        return futureSevenDays;
    }

    public static String BigDecimalDifferencePercentage(String base,String price){
        BigDecimal basePrice = new BigDecimal(base);
        BigDecimal pricePrice = new BigDecimal(price);
        BigDecimal difference = pricePrice.subtract(basePrice);
        BigDecimal multiply = difference.divide(basePrice, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        return multiply.toString();
    }

    public static void main(String[] args) {
        BigDecimal bigDecimal = new BigDecimal("100");
        BigDecimal bigDecimal1 = new BigDecimal("110");
        BigDecimal difference = bigDecimal1.subtract(bigDecimal);
        BigDecimal multiply = difference.divide(bigDecimal, 2, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
        System.out.println(multiply);
    }

    @Override
    public ResponseData calender(CalenderReq calenderReq) {
        log.info("获取房价日历开始:{}",calenderReq);
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(calenderReq.getSessionToken());
            RoomRateCode roomRateCode = roomRateCodeDao.selectByHidAndName(tbUserSession.getHid(), "门市价");
            RoomType roomType = roomTypeDao.selectById(calenderReq.getRoomTypeId());
            HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(tbUserSession.getHid());

            final HashOperations<String, Object, Object> ops = stringRedisTemplate.opsForHash();
            /**
             * 查询缓存中的房价信息
             */
            String key = ECache.ROOM_TYPE_DAY_PRICE + "_" + calenderReq.getRoomTypeId() + "_" + roomRateCode.getRateId();
            Object o = ops.get(ECache.ROOM_TYPE_DAY_PRICE, key);

            List<String> futureSevenDays = getFutureSevenDays();

            JSONObject param = new JSONObject();

            HotelTypeEnum hotelTypeEnum = HotelTypeEnum.fromCode(hotelBaseInfo.getHotelType());
            Area area = areaDao.selectById(hotelBaseInfo.getCity());
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Hid", hotelBaseInfo.getHid().toString());


            List<AiPriceReq> aiPriceReqList = new ArrayList<>();
            for (String futureSevenDay : futureSevenDays) {
                AiPriceReq aiPriceReq = new AiPriceReq();
                aiPriceReq.setDate(futureSevenDay);
                aiPriceReq.setHotelName(hotelBaseInfo.getHotelName());
                aiPriceReq.setRoomType(roomType.getRoomTypeName());
                aiPriceReq.setTypeId(hotelTypeEnum.getDescription());
                aiPriceReq.setStar(Objects.isNull(hotelBaseInfo.getStar()) ? 2 : hotelBaseInfo.getStar());
                aiPriceReq.setLongitude(hotelBaseInfo.getLon().floatValue());
                aiPriceReq.setLatitude(hotelBaseInfo.getLat().floatValue());
                aiPriceReq.setRoomTypeId(roomType.getRoomTypeId().toString());
                aiPriceReq.setCity(area.getName());
                aiPriceReq.setCityCode(hotelBaseInfo.getCity().toString());
                aiPriceReq.setHotelId(hotelBaseInfo.getHid().toString());
            }
            param.put("list", aiPriceReqList);
            log.info("send_url:{} body:{} header:{}","http://hotel-price-infer.com/multiple_aiprice",param,headers);
            List<AipriceResp> aipriceResps = HttpUtil.sendPostRequestWithHeaderList("http://hotel-price-infer.com/multiple_aiprice", param, headers, AipriceResp.class);
            Map<String, AipriceResp> resultMap;
            if(!CollectionUtils.isEmpty(aipriceResps)){
                resultMap = aipriceResps.stream().collect(Collectors.toMap(AipriceResp::getPredictDate, Function.identity()));
            } else {
                resultMap = Collections.emptyMap();
            }
            RoomTypePrice roomTypePrice = roomTypePriceMapper.selectByRoomTypeId(calenderReq.getRoomTypeId(), tbUserSession.getHid());
            CalenderDataResp calenderDataResp = new CalenderDataResp();
            /*if (o == null || "[]".equals(o.toString())) {*/
                Date date = new Date();
                Date dateBefore = HotelUtils.addDayGetNewDate(date, -90);
                Date dateFuture = HotelUtils.addDayGetNewDate(date, 365);
                List<RoomDayPriceShow> roomPrices = updateRoomPriceForCache(tbUserSession, calenderReq.getRoomTypeId(), dateBefore, dateFuture, false, roomRateCode.getRateId());
                List<RoomDayPriceShow> filterRoomPrices = Collections.emptyList();
                log.info("roomPrices:{}",roomPrices);
                if(!CollectionUtils.isEmpty(roomPrices)){
                    filterRoomPrices = roomPrices.stream().filter(roomDayPriceShow -> roomDayPriceShow.getDayTime().toString().contains(calenderReq.getDate().replace("-", ""))).collect(Collectors.toList());
                }
                if(!CollectionUtils.isEmpty(filterRoomPrices)) {
                    List<CalenderResp> calenderRespList = filterRoomPrices.stream().map(roomDayPriceShow -> {
                        CalenderResp calenderResp = new CalenderResp();
                        calenderResp.setId(calenderReq.getRoomTypeId());

                        BigDecimal basePrice = BigDecimal.valueOf(roomType.getPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                        calenderResp.setBasePrice(basePrice.toString());

                        // 2.查询所有每日房价
                        RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
                        roomDayPriceSearch.setRoomTypeId(calenderReq.getRoomTypeId());
                        roomDayPriceSearch.setRoomRateId(roomRateCode.getRateId());
                        roomDayPriceSearch.setDayTime(roomDayPriceShow.getDayTime());
                        List<RoomDayPrice> roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);
                        if(Objects.nonNull(roomDayPrices.get(0).getCustomPrice())){
                            BigDecimal customPrice = BigDecimal.valueOf(roomDayPrices.get(0).getCustomPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                            calenderResp.setCustomPrice(customPrice.toString());
                        }else {
                            calenderResp.setCustomPrice(null);
                        }
                        String predictDate = calenderResp.getDayTime().toString().substring(0,4)+"-"+calenderResp.getDayTime().toString().substring(4,6)+"-"+calenderResp.getDayTime().toString().substring(6);
                        //包含这个就有ai
                        if(futureSevenDays.contains(predictDate)){
                            if(!resultMap.isEmpty()){
                                AipriceResp aipriceResp = resultMap.get(predictDate);
                                calenderResp.setAiPrice(aipriceResp.getPredictPrice().toString());
                                if(Objects.isNull(calenderResp.getCustomPrice())){
                                    String rate = BigDecimalDifferencePercentage(calenderResp.getBasePrice(), calenderResp.getAiPrice());
                                    calenderResp.setRate(rate);
                                }else {
                                    String rate = BigDecimalDifferencePercentage(calenderResp.getBasePrice(), calenderResp.getCustomPrice());
                                    calenderResp.setRate(rate);
                                }
                            }
                        }else {
                            calenderResp.setAiPrice(null);
                        }

                        calenderResp.setWeekDay(roomDayPriceShow.getWeekDay());
                        calenderResp.setDayTime(roomDayPriceShow.getDayTime());
                        calenderResp.setDayTimeStr(roomDayPriceShow.getDayTimeStr());
                        if(Objects.nonNull(roomTypePrice)){
                            calenderResp.setMinLimitPrice(Objects.isNull(roomTypePrice.getLessPrice()) ? null : BigDecimal.valueOf(roomTypePrice.getLessPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());
                            calenderResp.setMaxLimitPrice(Objects.isNull(roomTypePrice.getMorePrice()) ? null : BigDecimal.valueOf(roomTypePrice.getMorePrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());
                        }
                        return calenderResp;
                    }).collect(Collectors.toList());
                    calenderDataResp.setList(calenderRespList);
                }
                responseData.setData(calenderDataResp);
            /*} else {
                log.info("缓存中的对象:{}", com.alibaba.fastjson.JSONObject.toJSONString(o));
                List<RoomDayPriceShow> roomDayPriceShows = com.alibaba.fastjson.JSONArray.parseArray(com.alibaba.fastjson.JSONObject.toJSONString(o), RoomDayPriceShow.class);
                //List<RoomDayPriceShow> roomPrices = JSONArray.fromObject(o);
                List<RoomDayPriceShow> filterRoomPrices= roomDayPriceShows.stream().filter(roomDayPriceShow -> roomDayPriceShow.getDayTimeStr().contains(calenderReq.getDate().replace("-", ""))).collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(filterRoomPrices)) {
                    List<CalenderResp> calenderRespList = filterRoomPrices.stream().map(roomDayPriceShow -> {
                        CalenderResp calenderResp = new CalenderResp();
                        calenderResp.setId(calenderReq.getRoomTypeId());
                        BigDecimal price = BigDecimal.valueOf(roomDayPriceShow.getPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                        calenderResp.setPrice(price.toString());
                        calenderResp.setWeekDay(roomDayPriceShow.getWeekDay());
                        calenderResp.setDayTime(roomDayPriceShow.getDayTime());
                        calenderResp.setDayTimeStr(roomDayPriceShow.getDayTimeStr());
                        if(Objects.nonNull(roomTypePrice)){
                            calenderResp.setMinLimitPrice(Objects.isNull(roomTypePrice.getLessPrice()) ? null : BigDecimal.valueOf(roomTypePrice.getLessPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());
                            calenderResp.setMaxLimitPrice(Objects.isNull(roomTypePrice.getMorePrice()) ? null : BigDecimal.valueOf(roomTypePrice.getMorePrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());
                        }
                        return calenderResp;
                    }).collect(Collectors.toList());
                    calenderDataResp.setList(calenderRespList);
                    responseData.setData(calenderDataResp);
                }

            }*/
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        log.info("获取房价日历结束:{}",responseData);
        return responseData;
    }


    public List<RoomDayPriceShow> updateRoomPriceForCache(TbUserSession user, Integer roomTypeId, Date dateBefore, Date dateFuture, Boolean updateCache, int roomRateId) {

        // 1. 查询房型
        RoomType roomType = roomTypeDao.selectById(roomTypeId);
        Integer price = roomType.getPrice();

        // 2.查询所有每日房价
        RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
        roomDayPriceSearch.setRoomTypeId(roomTypeId);
        roomDayPriceSearch.setRoomRateId(roomRateId);
        roomDayPriceSearch.setDayTimeMin(HotelUtils.parseDate2Int(dateBefore));
        roomDayPriceSearch.setDayTimeMax(HotelUtils.parseDate2Int(dateFuture));

        List<RoomDayPrice> roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);

        Map<Integer, RoomDayPrice> dayPriceMap = roomDayPrices.stream().collect(Collectors.toMap(RoomDayPrice::getDayTime, a -> a, (k1, k2) -> k2));

        // 3.获取两个日期差
        List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(dateBefore).substring(0, 10), HotelUtils.parseDate2Str(dateFuture).substring(0, 10));

        // 返回的房价信息
        ArrayList<RoomDayPriceShow> roomDayPriceShows = new ArrayList<>();

        Calendar cal = Calendar.getInstance();

        RoomDayPriceShow rdps = new RoomDayPriceShow();

        RoomDayPrice roomDayPrice = new RoomDayPrice();

        for (String date : allDayListBetweenDate) {

            int day = Integer.parseInt(date.replace("-", ""));

            roomDayPrice = dayPriceMap.get(day);

            rdps = new RoomDayPriceShow();
            rdps.setDayTime(day);

            if (roomDayPrice == null) {

                cal.setTime(HotelUtils.parseStr2Date(date + " 00:00:00"));

                rdps.setId(-1);
                rdps.setPrice(price);
                rdps.setWeekDay(cal.get(Calendar.DAY_OF_WEEK));

            } else {

                rdps.setId(roomDayPrice.getId());
                rdps.setWeekDay(roomDayPrice.getWeekDay());
                rdps.setPrice(roomDayPrice.getPrice());

            }

            roomDayPriceShows.add(rdps);

        }
        final String key = ECache.ROOM_TYPE_DAY_PRICE + "_" + roomTypeId + "_" + roomRateId;

        if (updateCache) {
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    try {
                        final HashOperations<String, Object, Object> ops = stringRedisTemplate.opsForHash();
                        ops.put(ECache.ROOM_TYPE_DAY_PRICE, key, JSONArray.fromObject(roomDayPriceShows).toString());

                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                }
            });
        }


        return roomDayPriceShows;

    }


    @Override
    public ResponseData calenderDetail(CalenderDetailReq calenderDetailReq) {
        log.info("查询房价日历详情信息开始:{}",calenderDetailReq);
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(calenderDetailReq.getSessionToken());
            RoomType roomType = roomTypeDao.selectById(calenderDetailReq.getRoomTypeId());

            RoomTypePrice roomTypePrice = roomTypePriceMapper.selectByRoomTypeId(calenderDetailReq.getRoomTypeId(), tbUserSession.getHid());
            CalenderDetailResp calenderDetailResp = new CalenderDetailResp();
            calenderDetailResp.setBasePrice(BigDecimal.valueOf(roomType.getPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());
            if(Objects.nonNull(roomTypePrice)){

                calenderDetailResp.setMinLimitPrice(Objects.isNull(roomTypePrice.getLessPrice()) ? null : BigDecimal.valueOf(roomTypePrice.getLessPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());
                calenderDetailResp.setMaxLimitPrice(Objects.isNull(roomTypePrice.getMorePrice()) ? null : BigDecimal.valueOf(roomTypePrice.getMorePrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());

            }

            RoomRateCode roomRateCode = roomRateCodeDao.selectByHidAndName(tbUserSession.getHid(), "门市价");
            // 2.查询所有每日房价
            RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
            roomDayPriceSearch.setRoomTypeId(calenderDetailReq.getRoomTypeId());
            roomDayPriceSearch.setRoomRateId(roomRateCode.getRateId());
            roomDayPriceSearch.setDayTime(Integer.valueOf(calenderDetailReq.getDate().replace("-","")));
            List<RoomDayPrice> roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);
            if(!CollectionUtils.isEmpty(roomDayPrices)){
                if(Objects.nonNull(roomDayPrices.get(0).getCustomPrice())){
                    calenderDetailResp.setCustomPrice(BigDecimal.valueOf(roomDayPrices.get(0).getCustomPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).toString());
                }
            }
            HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(tbUserSession.getHid());


            //todo 影响因子还没调用
            JSONObject param = new JSONObject();
            param.put("date",calenderDetailReq.getDate());
            param.put("hotelName",hotelBaseInfo.getHotelName());
            param.put("roomType",roomType.getRoomTypeName());
            HotelTypeEnum hotelTypeEnum = HotelTypeEnum.fromCode(hotelBaseInfo.getHotelType());
            param.put("typeId",hotelTypeEnum.getDescription());
            param.put("star",Objects.isNull(hotelBaseInfo.getStar()) ? 2 : hotelBaseInfo.getStar());
            param.put("longitude",hotelBaseInfo.getLon());
            param.put("latitude",hotelBaseInfo.getLat());
            param.put("roomTypeId",roomType.getRoomTypeId().toString());
            Area area = areaDao.selectById(hotelBaseInfo.getCity());
            param.put("city",area.getName());
            param.put("cityCode",hotelBaseInfo.getCity().toString());
            param.put("hotelId",hotelBaseInfo.getHid().toString());
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("Hid", hotelBaseInfo.getHid().toString());
            log.info("send_url:{} body:{} header:{}","http://hotel-price-infer.com/price/aiprice",param,headers);
            AipriceResp aipriceResp =   HttpUtil.sendPostRequestWithHeader("http://hotel-price-infer.com/price/aiprice", param, headers,AipriceResp.class);
           log.info("调用AI调用预测影响因子结果:{}",aipriceResp);
            if(Objects.nonNull(aipriceResp)){
                calenderDetailResp.setAiPrice(aipriceResp.getPredictPrice().toString());
                ObjectMapper objectMapper = new ObjectMapper();
                Map<String, Integer> map = objectMapper.readValue(aipriceResp.getImpactFactor().toString(), Map.class);
                List<FactorsResp> factorsRespList = new ArrayList<>();
                if(!map.isEmpty()){
                    map.forEach((key, value) -> {
                        FactorsResp factorsResp = new FactorsResp();
                        factorsResp.setName(key);
                        factorsResp.setRate(value);
                        factorsRespList.add(factorsResp);
                    });

                }
                calenderDetailResp.setFactors(factorsRespList);
            }
            responseData.setData(calenderDetailResp);
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        log.info("查询房价日历详情信息结束:{}",responseData);
        return responseData;
    }

    @Override
    public ResponseData editPrice(EditPriceReq editPriceReq) {
        log.info("设置自定义价格开始:{}",editPriceReq);
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(editPriceReq.getSessionToken());
            RoomRateCode roomRateCode = roomRateCodeDao.selectByHidAndName(tbUserSession.getHid(), "门市价");
            // 2.查询所有每日房价
            RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
            roomDayPriceSearch.setRoomTypeId(editPriceReq.getRoomTypeId());
            roomDayPriceSearch.setRoomRateId(roomRateCode.getRateId());
            roomDayPriceSearch.setDayTime(Integer.valueOf(editPriceReq.getDate().replace("-","")));
            List<RoomDayPrice> roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);
            if(!CollectionUtils.isEmpty(roomDayPrices)){
                RoomDayPrice roomDayPrice = roomDayPrices.get(0);
                if(StringUtils.isNotBlank(editPriceReq.getCustomPrice())){
                    roomDayPrice.setCustomPrice(Integer.valueOf(editPriceReq.getCustomPrice()));
                } else {
                    roomDayPrice.setCustomPrice(null);
                }
                roomDayPriceDao.update(roomDayPrice);
            }else {
                RoomDayPrice roomDayPrice = new RoomDayPrice();
                if(StringUtils.isNotBlank(editPriceReq.getCustomPrice())){
                    roomDayPrice.setCustomPrice(Integer.valueOf(editPriceReq.getCustomPrice()));
                } else {
                    roomDayPrice.setCustomPrice(null);
                }
                roomDayPrice.setDayTime(Integer.valueOf(editPriceReq.getDate().replace("-","")));
                roomDayPrice.setHid(tbUserSession.getHid());
                roomDayPrice.setRoomTypeId(editPriceReq.getRoomTypeId());
                roomDayPrice.setRoomRateId(roomRateCode.getRateId());
                roomDayPrice.setWeekDay(editPriceReq.getWeek());
                roomDayPrice.setCreateTime(new Date());
                roomDayPrice.setUpdateTime(new Date());
                roomDayPrice.setCreateUserId(tbUserSession.getUserId());
                roomDayPrice.setUpdateUserId(tbUserSession.getUserId());
                roomDayPriceDao.insert(roomDayPrice);
            }

        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        log.info("设置自定义价格结束:{}",responseData);
        return responseData;
    }

    @Override
    public ResponseData locationInfo(String sessionToken) {
        ResponseData responseData = ResponseData.newSuccessData();
        LocationInfoResp locationInfoResp = new LocationInfoResp();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(sessionToken);
            HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(tbUserSession.getHid());
            if(Objects.isNull(hotelBaseInfo)) {
                log.error("该酒店不存在!");
            }
            locationInfoResp.setAddr(hotelBaseInfo.getAddr());
            locationInfoResp.setHotelName(hotelBaseInfo.getHotelName());
            locationInfoResp.setLat(Objects.isNull(hotelBaseInfo.getLat()) ? "" : hotelBaseInfo.getLat().toString());
            locationInfoResp.setLon(Objects.isNull(hotelBaseInfo.getLon()) ? "" : hotelBaseInfo.getLon().toString());
            responseData.setData(locationInfoResp);
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData editLocationInfo(LocationInfoReq locationInfoReq) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(locationInfoReq.getSessionToken());
            HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(tbUserSession.getHid());
            if(Objects.isNull(hotelBaseInfo)) {
                log.error("该酒店不存在!");
            }
            hotelBaseInfo.setAddr(locationInfoReq.getAddr());
            hotelBaseInfo.setLat(StringUtils.isNotBlank(locationInfoReq.getLat()) ? null : Double.valueOf(locationInfoReq.getLat()));
            hotelBaseInfo.setLon(StringUtils.isNotBlank(locationInfoReq.getLon()) ? null : Double.valueOf(locationInfoReq.getLon()));
            hotelBaseInfoDao.editHotelBaseInfo(hotelBaseInfo);
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    public Integer getAllRevenue(TbUserSession user,List<String> dates){
        Integer revenue = 0;
        for (String date : dates) {
            Integer businessDay = Integer.valueOf(date.replace("-", ""));
            Integer everyDayRevenue = everyDayRevenue(user, businessDay);
            revenue += everyDayRevenue;
        }
        return revenue;
    }


    private Integer getAllHotelRoomTypeRegister(TbUserSession user,List<String> dates){
        Integer hotelRoomTypeRegister = 0;
        for (String date : dates) {
            Integer businessDay = Integer.valueOf(date.replace("-", ""));
            Integer everyDayRevenue = everyDayRevenue(user, businessDay);
            Integer totalUseCount = getHotelRoomTypeRegister(user, everyDayRevenue);
            hotelRoomTypeRegister += totalUseCount;
        }
        return hotelRoomTypeRegister;
    }


    /**
     * 计算今日开房数
     * @param user
     * @return
     */
    private Integer getHotelRoomTypeRegister(TbUserSession user,Integer businessDay){
        GetHotelDataInfoParam param = new GetHotelDataInfoParam();
        param.setHid(user.getHid());
        if(null != businessDay){
            param.setBusinessDay(businessDay);
        }
        List<ResourceView> hotelDataInfo = pmsMainDao.getTodayRegister(param);
        //当日开房数
        Integer totalUseCount = 0;
        for (ResourceView view : hotelDataInfo) {
            totalUseCount += view.getCount();
        }
        return totalUseCount;
    }


    /**
     * 总客房数-维修-自用
     * @param sessionToken
     * @return
     */
    private Integer getBcs(String sessionToken,String startTime,String endTime){

        JSONObject param = new JSONObject();
        param.put("startTime",startTime);
        param.put("endTime",endTime);
        param.put("sessionToken",sessionToken);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONObject> request = new HttpEntity<JSONObject>(param, headers);
        log.info("param:{}",param );
        IncomeInfoResp incomeInfoResp = restTemplate.postForEntity("http://" + GURL.PMSAI + "/hotel/pms-price/incomeOverviewNight", request, IncomeInfoResp.class).getBody();
        log.info("IncomeInfoResp:{}",incomeInfoResp);
        if(Objects.nonNull(incomeInfoResp)) {
            return incomeInfoResp.getSoldOut();
        }else {
            return 0;
        }
    }


    /**
     * 查询自用房数量
     * @param hid
     * @param regists
     * @return
     */
    private Integer getZyNum(Integer hid,List<Regist> regists){
        // 查询在住信息
        if(null == regists){
            regists = getRegists(hid);
        }
        // 自用
        int zyNum = 0;
        for (Regist regist : regists) {
            Integer checkinType = regist.getCheckinType();
            if (checkinType == 5 || checkinType == 4) {
                zyNum++;
            }
        }
        return zyNum;
    }

    // 查询在住信息
    private List<Regist> getRegists(Integer hid){
        RegistSearch registSearch = new RegistSearch();
        registSearch.setHid(hid);
        registSearch.setState(0);
        return registDao.selectBySearch(registSearch);
    }



    /**
     * 给一个日期范围,然后获得日期的每一天
     * @param beginDate
     * @param endDate
     * @return
     * @throws ParseException
     */
    public static List<String> findEveryDay(String beginDate, String endDate) throws ParseException {
        List<String> dates = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        Date beginTime = sdf.parse(beginDate);
        Date endTime = sdf.parse(endDate);
        dates.add(sdf.format(beginTime));
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(beginTime);
        while (endTime.after(calendar.getTime())) {
            calendar.add(Calendar.DAY_OF_MONTH,1);
            dates.add(sdf.format(calendar.getTime()));
        }
        return dates;
    }

    /**
     * 今日收款
     * @param user
     * @param businessDay
     * @return
     */
    private Integer everyDayRevenue(TbUserSession user,Integer businessDay){
        AccountSearch accountSearch = new AccountSearch();
        accountSearch.setHid(user.getHid());
        accountSearch.setBusinessDay(businessDay);
        accountSearch.setPayType(2);
        accountSearch.setIsCancel(0);

        List<Account> accounts = accountDao.selectBySearch(accountSearch);
        Integer sumPayMoney = 0 ;
        for(Account account:accounts){
            if(account.getPrice() >= 0){
                sumPayMoney += account.getPrice();
            }
        }
        return sumPayMoney;
    }

    /**
     * 计算一个时间段总房费
     * @param user
     * @param dates
     * @return
     */
    private Integer getAllRoomRevenue(TbUserSession user,List<String> dates){
        Integer revenue = 0;
        for (String date : dates) {
            Integer businessDay = Integer.valueOf(date.replace("-", ""));
            Integer everyDayRevenue = everyDayRoomRevenue(user, businessDay);
            revenue += everyDayRevenue;
        }
        return revenue;
    }

    /**
     * 计算每天房费
     * @param user
     * @param businessDay ********
     * @return
     */
    private Integer everyDayRoomRevenue(TbUserSession user,Integer businessDay) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setDailyState(1);
            bookingOrderDailyPriceSearch.setDailyTime(businessDay);
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setState(1);
            List<Regist> regists = registDao.selectBySearch(registSearch);

            Map<Integer, Regist> registMap = regists.stream().collect(Collectors.toMap(Regist::getRegistId, a -> a, (k1, k2) -> k2));
            Integer sumPrice = 0;
            for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {

                Integer registId = bodp.getRegistId();
                if (registId != null && registId > 0) {
                    Regist regist = registMap.get(registId);
                    if (regist == null) {
                        continue;
                    }
                    // 判断是否是当天离店,非钟点房当天离店的也不算
                    Integer checkinType = regist.getCheckinType();
                    Integer date2Int = HotelUtils.parseDate2Int(regist.getCheckoutTime());
                    if (checkinType != 2 && businessDay.equals(date2Int)) {
                        log.info("registId:" + regist.getRegistId());
                        continue;
                    }
                    sumPrice += bodp.getPrice();
                }
            }


            // 查询当天已产生的账务信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setBusinessDay(businessDay);
            accountSearch.setPayType(1);
            accountSearch.setIsCancel(0);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            // 房费 餐费 商品费
            Integer roomPrice = 0;
            for (Account account : accounts) {
                Integer accountType = account.getAccountType();
                Integer price = account.getPrice();
                switch (accountType) {
                    case 2:
                        break;
                    case 3:
                        break;
                    default:
                        roomPrice += price;
                        break;
                }
            }
            return sumPrice+roomPrice;
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return 0;
    }
    
}
