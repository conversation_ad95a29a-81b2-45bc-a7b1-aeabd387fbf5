package com.pms.czabsorders.service.mini.transaction.impl;

import com.pms.czabsorders.service.mini.transaction.WxGoodsServiceTransaction;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.dao.RegistDao;
import com.pms.pmswarehouse.bean.GoodsDumb;
import com.pms.pmswarehouse.bean.GoodsSaleRecord;
import com.pms.pmswarehouse.bean.GoodsShoppingOrder;
import com.pms.pmswarehouse.dao.GoodsDumbDao;
import com.pms.pmswarehouse.dao.GoodsSaleRecordDao;
import com.pms.pmswarehouse.dao.GoodsShoppingOrderDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;

@Service
public class WxGoodsServiceTransactionImpl  extends BaseService implements WxGoodsServiceTransaction {

    @Autowired
    private RegistDao registDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private GoodsShoppingOrderDao goodsShoppingOrderDao;

    @Autowired
    private GoodsDumbDao goodsDumbDao;

    @Autowired
    private GoodsSaleRecordDao goodsSaleRecordDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addGoodsToRegist(Regist regist, List<Account> addAccounts, GoodsShoppingOrder goodsShoppingOrder,List<GoodsSaleRecord> goodsSaleRecords) throws Exception {

        Integer update = registDao.update(regist);

        if(update<1){
            throw new Exception("修改订单信息失败");
        }

        HashMap<String, String> snReg = new HashMap<>();

        for (Account account:addAccounts){
           try {
               accountDao.saveAccount(account);
           }catch (Exception e){
               String uuid = HotelUtils.getUUID();
               String accountId = account.getAccountId();
               snReg.put(accountId,uuid);
               account.setAccountId(uuid);
               accountDao.saveAccount(account);
           }
        }

        goodsShoppingOrder.setRegistId(regist.getRegistId());
        goodsShoppingOrderDao.insert(goodsShoppingOrder);

        for (GoodsSaleRecord gsr:goodsSaleRecords){
            String s = snReg.get(gsr.getAccountId());
            if(s!=null&&!s.equals("")){
                gsr.setAccountId(s);
            }
            goodsSaleRecordDao.insert(gsr);
        }

        return goodsShoppingOrder.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addGoodsToLs(GoodsDumb goodsDumb, List<Account> addAccounts, GoodsShoppingOrder goodsShoppingOrder,List<GoodsSaleRecord> goodsSaleRecords) throws Exception {

        Integer insert = goodsDumbDao.insert(goodsDumb);
        if(insert<1){
            throw new Exception("添加零售信息失败");
        }
        HashMap<String, String> snReg = new HashMap<>();
        for (Account account:addAccounts){
            account.setGoodDumbId(goodsDumb.getGoodsDumbId());
            try {
                accountDao.saveAccount(account);
            }catch (Exception e){
                String uuid = HotelUtils.getUUID();
                String accountId = account.getAccountId();
                snReg.put(accountId,uuid);
                account.setAccountId(uuid);
                accountDao.saveAccount(account);
            }
        }
        goodsShoppingOrder.setDumpId(goodsDumb.getGoodsDumbId());
        goodsShoppingOrderDao.insert(goodsShoppingOrder);

        for (GoodsSaleRecord gsr:goodsSaleRecords){
            gsr.setRegistId(goodsDumb.getGoodsDumbId());
            String s = snReg.get(gsr.getAccountId());
            if(s!=null&&!s.equals("")){
                gsr.setAccountId(s);
            }
            gsr.setRegistId(goodsDumb.getGoodsDumbId());
            goodsSaleRecordDao.insert(gsr);
        }

        return goodsShoppingOrder.getId();
    }
}
