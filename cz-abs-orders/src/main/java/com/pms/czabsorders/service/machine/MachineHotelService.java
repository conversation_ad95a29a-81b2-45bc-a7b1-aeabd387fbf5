package com.pms.czabsorders.service.machine;


import com.pms.pmsorder.bean.machine.Hotel;
import com.pms.pmsorder.dao.MachineHotelDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MachineHotelService implements ApplicationRunner {

    private static Map<Integer, Hotel> allHotelCache = new ConcurrentHashMap<>();

    @Resource
    private MachineHotelDao machineHotelDao;


    @Override
    public void run(ApplicationArguments args) throws Exception {
        List<Hotel> hotels = machineHotelDao.allHotel();
        allHotelCache = hotels.stream().collect(Collectors.toMap(Hotel::getHid, a -> a, (k1, k2) -> k2));
        new Thread(() -> {
            try {
                while (true) {
                    Thread.sleep(180 * 60 * 1000);
                    List<Hotel> hotels1 = machineHotelDao.allHotel();
                    allHotelCache = hotels1.stream().collect(Collectors.toMap(Hotel::getHid, a -> a, (k1, k2) -> k2));
                }

            } catch (InterruptedException e) {
                log.error("",e);
            }
        }).start();
    }

    public Map<Integer, Hotel> getAllHotelCache() {
        HashMap<Integer, Hotel> map = new HashMap<>();
        map.putAll(allHotelCache);
        return map;
    }
}
