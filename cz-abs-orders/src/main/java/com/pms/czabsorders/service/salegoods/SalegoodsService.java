package com.pms.czabsorders.service.salegoods;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.salesgood.AddGoodsForRegistParam;
import com.pms.czabsorders.bean.salesgood.ReturnedGoodsParam;
import com.pms.czpmsutils.ResponseData;
import com.pms.pmswarehouse.bean.GoodsDumb;
import com.pms.pmswarehouse.bean.request.SelectGoodsStockParam;
import com.pms.pmswarehouse.bean.search.GoodsDumbSearch;
import com.pms.pmswarehouse.bean.search.HotelSellGoodsDto;
import com.pms.pmswarehouse.bean.search.OrderRoomChargeDto;
import net.sf.json.JSONObject;

public interface SalegoodsService {
    /**
     * 查询当前酒店所有哑房账信息
     * @param goodsDumbSearch
     * @return
     */
    public Page<GoodsDumb> findAllGoodsDumb(GoodsDumbSearch goodsDumbSearch) throws Exception;

    /**
     * 添加或删除当前酒店的哑房账信息
     * @param param
     * @return
     */
    public ResponseData addOrUpdateGoodsDumb(JSONObject param);

    /**
     * 现付账 冲账
     * @param param
     * @return
     */
    public ResponseData cancelGoodsDumb(JSONObject param);


    /**
     * 查询现付账明细
     * @param param
     * @return
     */
    public ResponseData findGoodsDumbInfo(JSONObject param);

    /**
     * 登记单或者预订单添加小商品
      * @param param
     * @return
     */
    public ResponseData registAddGoods(AddGoodsForRegistParam param);


    /**
     * 小商品退货
     * @return
     */
    public ResponseData returnedGoods(ReturnedGoodsParam param);

    ResponseData selectGoodsStock(SelectGoodsStockParam param);

    /**
     * 商品下单确认操作
     * @param orderRoomChargeDto
     * @return
     */
    ResponseData orderRoomCharge(OrderRoomChargeDto orderRoomChargeDto);

    /**
     * 获取近7天热销商品
     * @param hotelSellGoodsDto
     * @return
     */
    ResponseData hotSellGoods(HotelSellGoodsDto hotelSellGoodsDto);

//    void exportGoodsStock(SelectGoodsStockParam param, HttpServletResponse response);

}
