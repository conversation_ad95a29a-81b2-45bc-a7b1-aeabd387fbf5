package com.pms.czabsorders.service.account;

import com.pms.czabsorders.bean.*;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.AddAccountForArRequest;
import com.pms.czpmsutils.request.AddAccountParam;
import com.pms.czpmsutils.request.CheckoutPayStatisticsRequest;
import com.pms.czpmsutils.request.FinishMemberFreezeRequest;
import net.sf.json.JSONObject;

public interface AccountService {
    /**
     * 入账
     *
     * @param addAccountParam
     * @return
     */
    ResponseData addAccount(AddAccountParam addAccountParam);

    /**
     * 部分结账
     *
     * @param settleAccountParam
     * @return
     */
    ResponseData settleAccount(SettleAccountParam settleAccountParam);

    ResponseData cancelAccount(CancelAccountParam cancelAccountParam);

    /**
     * 完成预授权
     *
     * @param balanceAccountParam
     * @return
     */
    ResponseData balanceAccount(BalanceAccountParam balanceAccountParam);

    /**
     * 冲账
     *
     * @param setOffAccountParam
     * @return
     */
    ResponseData setOff(SetOffAccountParam setOffAccountParam);

    /**
     * 转账
     *
     * @param param
     * @return
     */
    ResponseData transferAccount(JSONObject param);

    /**
     * 转账
     *
     * @param param
     * @return
     */
    ResponseData transferAccountNew(TransferAccontRequest param);


    ResponseData transferAccountForBooking(TransferAccountForBookingParam transferAccountForBookingParam);


    /**
     * 查询交班账务信息
     *
     * @param param
     * @return
     */
    public ResponseData searchClassAccount(JSONObject param);

    /**
     * 查询结账实收汇总
     *
     * @return
     */
    public ResponseData searchcheckoutPayStatistics(CheckoutPayStatisticsRequest param);

    /**
     * 结账实收明细
     *
     * @param param
     * @return
     */
    public ResponseData searchCheckoutPayDetail(CheckoutPayStatisticsRequest param);

    /**
     * 支付宝以及微信退款
     *
     * @return
     */
    public ResponseData refundMoney(JSONObject param);


    /**
     * 第三方冲账
     *
     * @param param
     * @return
     */
    public ResponseData thirdAccoutCancel(JSONObject param);

    /**
     * 开房自动产生房费
     *
     * @param userSession
     * @param registId
     * @param min
     * @return
     */
    public ResponseData autoAddRoomPrice(TbUserSession userSession, Integer registId, Integer min);


    public ResponseData searchPersonAccoun(RegistParam registParam);

    /**
     * AR账入账实现
     * @param addAccountForArRequest
     * @return
     */
    public ResponseData addAccountForAr(AddAccountForArRequest addAccountForArRequest);

    public ResponseData finishMemberFreeze(FinishMemberFreezeRequest finishMemberFreezeRequest);


}
