package com.pms.czabsorders.service.mini;

import com.pms.czabsorders.bean.mini.FindHotelMsg;
import com.pms.czabsorders.bean.mini.ShoppingOrderDelivery;
import com.pms.czaccount.bean.pay.search.WechatMiniprogramsSearch;
import com.pms.czhotelfoundation.bean.hotel.search.HotelBaseInfoSearch;
import com.pms.czmembership.bean.member.search.CardGroupUrlSearch;
import com.pms.czpmsutils.ResponseData;
import net.sf.json.JSONObject;

public interface WxHotelService {

    public ResponseData createHotelWxQrCode(WechatMiniprogramsSearch param);

    public ResponseData createHotelWxQrCodeOther(WechatMiniprogramsSearch param);

    public ResponseData findHotelWxMinipro(JSONObject param);

    public ResponseData findHotelJson(FindHotelMsg findHotelMsg);

    public ResponseData findHotelJsonOther(FindHotelMsg findHotelMsg);

    /**
     * 商品订单修改
     * @param shoppingOrderDelivery
     * @return
     */
    public ResponseData ShoppingOrderDeliveryFunc(ShoppingOrderDelivery shoppingOrderDelivery);

    /**
     * 查询酒店map
     * @param wechatMiniprogramsSearch
     * @return
     */
    public ResponseData hotelMap(WechatMiniprogramsSearch wechatMiniprogramsSearch);

    public ResponseData allWxHotel(HotelBaseInfoSearch hotelBaseInfoSearch);

    /**
     * 查询会员绑定二维码
     * @param cardGroupUrlSearch
     * @return
     */
    public ResponseData searchVipUrl(CardGroupUrlSearch cardGroupUrlSearch);

}
