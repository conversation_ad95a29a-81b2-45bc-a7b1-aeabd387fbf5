package com.pms.czabsorders.service.main.impl;

import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.Page;
import com.pms.czabsorders.service.main.HotelMainService;
import com.pms.czabsorders.service.order.OrderService;
import com.pms.czabsorders.service.turnAlways.TurnAlwaysService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.code.HotelBusinessDay;
import com.pms.czhotelfoundation.bean.code.search.HotelBusinessDaySearch;
import com.pms.czhotelfoundation.bean.hotel.CommonFunctionsSettingDto;
import com.pms.czhotelfoundation.bean.hotel.HotelCommonFunction;
import com.pms.czhotelfoundation.bean.hotel.HotelRoomStateData;
import com.pms.czhotelfoundation.bean.hotel.HotelShiftRecord;
import com.pms.czhotelfoundation.bean.hotel.search.HotelShiftRecordSearch;
import com.pms.czhotelfoundation.bean.room.RoomCheckRecord;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomTypeNight;
import com.pms.czhotelfoundation.bean.room.search.RoomCheckRecordSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoAllSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeRegisterSearch;
import com.pms.czhotelfoundation.dao.code.HotelBusinessDayDao;
import com.pms.czhotelfoundation.dao.hotel.HotelCommonFunctionDao;
import com.pms.czhotelfoundation.dao.hotel.HotelShiftRecordDao;
import com.pms.czhotelfoundation.dao.room.RoomCheckRecordDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czpmsutils.DateUtil;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.BaseRequest;
import com.pms.czpmsutils.request.HotelShiftRecordRequest;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import com.pms.pmsorder.bean.BookingOrderRoomNum;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.request.BookingOrderPageRequest;
import com.pms.pmsorder.bean.request.GetHotelDataInfoParam;
import com.pms.pmsorder.bean.request.RegistPageRequest;
import com.pms.pmsorder.bean.search.BookingOrderDailyPriceSearch;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.bean.view.ResourceView;
import com.pms.pmsorder.dao.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Primary
@Slf4j
public class HotelMainServiceImpl extends BaseService implements HotelMainService {

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private PmsMainDao pmsMainDao;

    @Autowired
    private HotelBusinessDayDao hotelBusinessDayDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private RoomCheckRecordDao roomCheckRecordDao;

    @Autowired
    private RoomTypeDao roomTypeDao;

    @Autowired
    private HotelShiftRecordDao hotelShiftRecordDao;

    @Autowired
    private HotelCommonFunctionDao hotelCommonFunctionDao;

    @Autowired
    private TurnAlwaysService turnAlwaysService;

    @Autowired
    private OrderService bookingOrderService;


    /**
     * 保留二位小数
     */
    public final static String df = "%.2f";

    @Override
    public ResponseData dataOverview(BaseRequest baseRequest) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            TbUserSession user = this.getTbUserSession(baseRequest.getSessionToken());
            HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
            hotelBusinessDaySearch.setHid(user.getHid());
            HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);
            Integer businessDay = user.getBusinessDay();
            if(null != hotelBusinessDay){
                businessDay = hotelBusinessDay.getBusinessDay();
            }
            JSONObject jsonObject = new JSONObject();
            // 当前入住率
            jsonObject.put("currentOccupancyRate",String.format(df, currentOccupancyRate(user.getHid()) * 100) + "%");
            // 计算前7天平均入住率
            preSevenBookRate(user.getHid(),jsonObject);
            // 未来7天订出率
            futureSevenBookRate(user.getHid(),jsonObject,baseRequest.getSessionToken());
            // 今日收款
            jsonObject.put("todayRevenue",revenue(user,businessDay)/100);
            // 今日开房数
            jsonObject.put("todayRoomOccupancy", getHotelRoomTypeRegister(user,businessDay));
            // 今日房费
            jsonObject.put("todayRoomRevenue", dayRevenue(baseRequest.getSessionToken())/100);

            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date yesterday = calendar.getTime();
            Integer yesterdayBusinessDay = Integer.parseInt(DateUtil.format(yesterday,DateUtil.DATE_D_NO_SYMBOL));

            // 昨日收款
            Integer yesterdayRevenue = revenue(user,yesterdayBusinessDay)/100;
            // 昨日开房数
            Integer yesterdayRoomOccupancy = getYesterdayRegister(user.getHid(),businessDay);
            // 昨日房费
            Integer yesterdayRoomRevenue = yestodayRevenue(user,yesterdayBusinessDay)/100;
            // 今日收款日环比
            jsonObject.put("todayRevenueDayOnDay",
                    0 == yesterdayRevenue ? (0 == jsonObject.getInt("todayRevenue")? 0:100) :
                            String.format(df,100.0 * (jsonObject.getInt("todayRevenue") - yesterdayRevenue)/yesterdayRevenue)
            );
            // 今日开房数日环比
            jsonObject.put("todayRoomOccupancyDayOnDay",
                    0 == yesterdayRoomOccupancy ? (0 == jsonObject.getInt("todayRoomOccupancy")? 0:100) :
                            String.format(df,100.0 * (jsonObject.getInt("todayRoomOccupancy") - yesterdayRoomOccupancy)/yesterdayRoomOccupancy)
            );
            // 今日房费日环比
            jsonObject.put("todayRoomRevenueDayOnDay",
                    0 == yesterdayRoomRevenue ? (0 == jsonObject.getInt("todayRoomRevenue")? 0:100) :
                            String.format(df,100.0 * (jsonObject.getInt("todayRoomRevenue") - yesterdayRoomRevenue)/yesterdayRoomRevenue)

            );
            // 当前入住率日环比
            String yesterdayDate =
                    LocalDate.parse(yesterdayBusinessDay+"", DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            Map<String, Object> param = new HashMap<>();
            param.put("hid",user.getHid());
            param.put("time",yesterdayDate+" 00:00:00");
            int yestodayNum = registDao.countByCheckTime(param);

            List<Regist> regists = getRegists(user.getHid());
            int todayNum = CollectionUtils.isEmpty(regists)?0:regists.size();
            jsonObject.put("currentOccupancyRateDayOnDay",
                    0 == yestodayNum ? (0 == todayNum? 0:100) :
                            String.format(df,100.0 * (todayNum - yestodayNum)/yestodayNum));
            // 未来7天订出率周环比
            double preSevenRateValue = jsonObject.getDouble("preSevenRateValue");
            double nextSevenRateValue = jsonObject.getDouble("nextSevenRateValue");
            jsonObject.put("nextSevenDaysOccupancyRateWeekOnWeek",
                    0 == preSevenRateValue ? (0 == nextSevenRateValue? 0:100) :
                            String.format(df,100.0 * (nextSevenRateValue - preSevenRateValue)/preSevenRateValue));
            responseData.setData(jsonObject);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 今日收款
     * @param user
     * @param businessDay
     * @return
     */
    private Integer revenue(TbUserSession user,Integer businessDay){
        AccountSearch accountSearch = new AccountSearch();
        accountSearch.setHid(user.getHid());
        accountSearch.setBusinessDay(businessDay);
        accountSearch.setPayType(2);
        accountSearch.setIsCancel(0);

        List<Account> accounts = accountDao.selectBySearch(accountSearch);
        Integer sumPayMoney = 0 ;
        for(Account account:accounts){
            if(account.getPrice() >= 0){
                sumPayMoney += account.getPrice();
            }
        }
        return sumPayMoney;
    }

    /**
     * 计算今日房费
     * sessionToken
     * @return
     */
    private Integer dayRevenue(String sessionToken) {
        Integer price = 0;
        try {
            JSONObject param = new JSONObject();
            param.put("sessionToken",sessionToken);
            ResponseData responseData = bookingOrderService.dayRevenue(param);
            if(1 == responseData.getCode()){
                JSONObject res = (JSONObject) responseData.getData();
                JSONObject generatePrice = res.getJSONObject("generatePrice");
                JSONObject forecastRoomPrice = res.getJSONObject("forecastRoomPrice");
                price = generatePrice.getInt("roomPrice")+forecastRoomPrice.getInt("money");
            }
        }catch (Exception e){
            log.error("今日房费计算异常",e);
        }
        return price;
    }

    /**
     * 计算昨日房费
     * @param user
     * @param yestodayBussinessDay 20250310
     * @return
     */
    private Integer yestodayRevenue(TbUserSession user,Integer yestodayBussinessDay) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setDailyState(1);
            bookingOrderDailyPriceSearch.setDailyTime(yestodayBussinessDay);
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setState(1);
            List<Regist> regists = registDao.selectBySearch(registSearch);

            Map<Integer, Regist> registMap = regists.stream().collect(Collectors.toMap(Regist::getRegistId, a -> a, (k1, k2) -> k2));
            Integer sumPrice = 0;
            for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {

                Integer registId = bodp.getRegistId();
                if (registId != null && registId > 0) {
                    Regist regist = registMap.get(registId);
                    if (regist == null) {
                        continue;
                    }
                    // 判断是否是当天离店,非钟点房当天离店的也不算
                    Integer checkinType = regist.getCheckinType();
                    Integer date2Int = HotelUtils.parseDate2Int(regist.getCheckoutTime());
                    if (checkinType != 2 && yestodayBussinessDay.equals(date2Int)) {
                        log.info("registId:" + regist.getRegistId());
                        continue;
                    }
                    sumPrice += bodp.getPrice();
                }
            }


            // 查询当天已产生的账务信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setBusinessDay(yestodayBussinessDay);
            accountSearch.setPayType(1);
            accountSearch.setIsCancel(0);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            // 房费 餐费 商品费
            Integer roomPrice = 0;
            for (Account account : accounts) {
                Integer accountType = account.getAccountType();
                Integer price = account.getPrice();
                switch (accountType) {
                    case 2:
                        break;
                    case 3:
                        break;
                    default:
                        roomPrice += price;
                        break;
                }
            }
            return sumPrice+roomPrice;
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return 0;
    }



    @Override
    public ResponseData realTimeData(BaseRequest baseRequest) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            JSONObject result = new JSONObject();
            TbUserSession user = this.getTbUserSession(baseRequest.getSessionToken());
            HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
            hotelBusinessDaySearch.setHid(user.getHid());
            HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);
            Object businessDay;
            if (hotelBusinessDay == null) {
                businessDay = HotelUtils.currentDate().replace("-", "");
            } else {
                businessDay = hotelBusinessDay.getBusinessDay();
            }



            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");

            LocalDate date = LocalDate.parse(String.valueOf(businessDay), inputFormatter);

            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            String output = date.format(outputFormatter);

            String startBusinessDay = output +" 00:00:00";
            String endBusinessDay = output +" 23:59:59";

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            Date startParseDate = sdf.parse(startBusinessDay);
            Date endParseDate = sdf.parse(endBusinessDay);
            ArrayList<Long> checkInLongs = new ArrayList<>();
            checkInLongs.add(startParseDate.getTime()/1000);
            checkInLongs.add(endParseDate.getTime()/1000);


            /*TodayComingRequest todayComingRequest = new TodayComingRequest();
            todayComingRequest.setSessionToken(baseRequest.getSessionToken());
            todayComingRequest.setPageNum(1);
            todayComingRequest.setPageSize(10000);
            todayComingRequest.setCheckinTime(checkInLongs);
            todayComingRequest.setOrderState(1);
            todayComingRequest.setIsCheckin(0);

            ResponseData hotelTodayComingBookingList = bookingOrderService.getHotelTodayComingBookingList(todayComingRequest);
            ObjectMapper objectMapper = new ObjectMapper();
            Map map = objectMapper.convertValue(hotelTodayComingBookingList.getData1(), Map.class);*/
            //今日预抵
            BookingOrderPageRequest request = new BookingOrderPageRequest();
            request.setHid(user.getHid());
            request.setOrderStatus(Arrays.asList(1,3));
            request.setPageNum(1);
            request.setPageSize(10000);
            request.setCheckinTime(Arrays.asList(startParseDate.getTime()/1000,endParseDate.getTime()/1000));
            Page<BookingOrder> bookingOrderList = bookingOrderDao.selectPageByRequest(request);

            result.put("todayArrivals",bookingOrderList.getTotal());


            //今日入住
            RegistPageRequest registPageRequest = new RegistPageRequest();
            registPageRequest.setHid(user.getHid());
            registPageRequest.setGroupType(1);
            registPageRequest.setPageNum(1);
            registPageRequest.setPageSize(10000);
            registPageRequest.setCheckinTime(checkInLongs);
            Page<Map<String, Object>> registAndRersonNamePage = registDao.selectRegistAndRersonNamePage(registPageRequest);
            result.put("todayCheckIns",String.valueOf(registAndRersonNamePage.getTotal()));

            //今日预离
            RegistPageRequest newRegistPageRequest = new RegistPageRequest();
            newRegistPageRequest.setHid(user.getHid());
            newRegistPageRequest.setGroupType(1);
            newRegistPageRequest.setPageNum(1);
            newRegistPageRequest.setPageSize(10000);
            newRegistPageRequest.setOrderStatus(Collections.singletonList(0));
            newRegistPageRequest.setCheckoutTime(checkInLongs);
            Page<Map<String, Object>> andRersonNamePage = registDao.selectRegistAndRersonNamePage(newRegistPageRequest);
            result.put("todayDepartures",String.valueOf(andRersonNamePage.getTotal()));

            //今日RevPAR
            Integer roomPrice = dayRevenue(baseRequest.getSessionToken());
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            roomInfoSearch.setState(1);
            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);


            RegistSearch registSearch1 = new RegistSearch();
            registSearch1.setHid(user.getHid());
            registSearch1.setState(0);
            List<Regist> regists = registDao.selectBySearch(registSearch1);

            // 在住房
            ArrayList<Regist> checkRegist = new ArrayList<>();
            String checkRegistIds = "";

            // 预离
            int ylNum = 0;

            // 自用
            int zyNum = 0;

            Integer nowDateInt = HotelUtils.parseDate2Int(new Date());

            for (Regist regist : regists) {
                boolean isFj = true;
                Integer checkinType = regist.getCheckinType();
                Integer date2Int = HotelUtils.parseDate2Int(regist.getCheckoutTime());
                if (date2Int.equals(nowDateInt)) {
                    ylNum++;
                    isFj = false;
                }
                if (checkinType == 5 || checkinType == 4) {
                    zyNum++;
                    isFj = false;
                }
                checkRegist.add(regist);
                checkRegistIds += regist.getRegistId() + ",";
//                if (isFj) {
//
//                }
            }
            // 维修
            int wxNum = 0;
            // 按照房态分组
            Map<Integer, List<RoomInfo>> roomStateMap = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomNumState));
            // 维修数据
            List<RoomInfo> wxList = roomStateMap.get(ROOM_STATUS.OOO);
            if (wxList != null) {
                wxNum = wxList.size();
            }

            int bcs = roomInfos.size() - wxNum - zyNum;

            if(!CollectionUtils.isEmpty(roomInfos) && roomPrice !=0 && bcs!=0){

                result.put("todayRevPAR",BigDecimal.valueOf(roomPrice)
                        .divide(BigDecimal.valueOf(bcs), 2, RoundingMode.HALF_UP).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
            }else {
                result.put("todayRevPAR","0.00");
            }


            checkRegistIds += "-999";

            // 今日平均房价
            /*Double pjprice = 0.0;
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setDailyTime(user.getBusinessDay());
            if (checkRegist.size() > 0) {
                bookingOrderDailyPriceSearch.setRegistIds(checkRegistIds);
                Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
                pjprice = bookingOrderDailyPrices.stream().collect(Collectors.summarizingInt(BookingOrderDailyPrice::getPrice)).getAverage();
            }
            result.put("todayAverageRate",BigDecimal.valueOf(pjprice)
                    .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));*/

            GetHotelDataInfoParam getHotelDataInfoParam = new GetHotelDataInfoParam();
            getHotelDataInfoParam.setHid(user.getHid());
            getHotelDataInfoParam.setBusinessDay(Integer.valueOf(businessDay.toString()));
            List<ResourceView> hotelDataInfo = pmsMainDao.getTodayRegister(getHotelDataInfoParam);
            //当日开房数
            Integer totalUseCount = 0;
            //当前班次开房数
            Integer dayUseCount = 0;

            JSONArray roomTypeInfoList = new JSONArray();

            for (ResourceView view : hotelDataInfo) {
                totalUseCount += view.getCount();
                /*if (view.getClassId().equals(classId)) {
                    dayUseCount += view.getCount();
                }*/
                JSONObject roomType = new JSONObject();
                roomType.put("x", view.getItem());
                roomType.put("y", view.getCount());
                roomTypeInfoList.add(roomType);
            }

            //处理除数是否为0
            if(totalUseCount != 0){
                result.put("todayAverageRate",BigDecimal.valueOf(roomPrice)
                        .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).divide(BigDecimal.valueOf(totalUseCount),2,RoundingMode.HALF_UP));
            }else {
                result.put("todayAverageRate","0.00");
            }



            //未排房
            BookingOrderPageRequest bookingOrderPageRequest = new BookingOrderPageRequest();
            bookingOrderPageRequest.setHid(user.getHid());
            bookingOrderPageRequest.setOrderStatus(Arrays.asList(1,3));
            bookingOrderPageRequest.setPageNum(1);
            bookingOrderPageRequest.setPageSize(10000);
            Page<BookingOrder> bookingOrders = bookingOrderDao.selectPageByRequest(bookingOrderPageRequest);
            List<BookingOrder> result1 = bookingOrders.getResult();
            if(CollectionUtils.isEmpty(result1)){
                result.put("unassignedRooms",0);
            }else {
                List<BookingOrder> orderList = new ArrayList<>();
                for (BookingOrder bookingOrder : result1) {
                    Integer bookingOrderId = bookingOrder.getBookingOrderId();
                    List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectByBookingOrderId(bookingOrderId);
                    //过滤出未排房的订单
                    List<BookingOrderRoomNum> collect = bookingOrderRoomNums.stream().filter(bookingOrderRoomNum -> bookingOrderRoomNum.getRowRoom() == 0).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(collect)){
                        orderList.add(bookingOrder);
                    }
                }
                result.put("unassignedRooms",orderList.size());
            }


            //待处理
            RoomCheckRecordSearch roomCheckRecordSearch =  new RoomCheckRecordSearch();
            roomCheckRecordSearch.setHid(user.getHid());
            roomCheckRecordSearch.setPageNum(1);
            roomCheckRecordSearch.setPageSize(10000);
            roomCheckRecordSearch.setIsCancel(0);
            roomCheckRecordSearch.setState(1);
            Page<RoomCheckRecord> roomCheckRecords = roomCheckRecordDao.selectBySearch(roomCheckRecordSearch);
            result.put("pendingOrders",String.valueOf(roomCheckRecords.getTotal()));
            //异常订单
            RegistPageRequest newRegistPageRequest1 = new RegistPageRequest();
            newRegistPageRequest1.setHid(user.getHid());
            newRegistPageRequest1.setGroupType(0);
            newRegistPageRequest1.setPageNum(1);
            newRegistPageRequest1.setPageSize(10000);
            newRegistPageRequest1.setOrderStatus(Collections.singletonList(0));

            DateTimeFormatter  dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            LocalDateTime dateTime = LocalDateTime.parse(startBusinessDay, dateTimeFormatter);

            // 3. 转换为秒级时间戳
            long timestampSeconds = dateTime.atZone(ZoneId.systemDefault()).toEpochSecond();

            ArrayList<Long> newCheckOutLongs = new ArrayList<>();
            newCheckOutLongs.add(1615273L/100);
            // 4. 减去一秒 秒级时间戳
            newCheckOutLongs.add(timestampSeconds - 1L);
            newRegistPageRequest1.setCheckoutTime(newCheckOutLongs);
            Page<Map<String, Object>> selectRegistAndRersonNamePage = registDao.selectRegistAndRersonNamePage(newRegistPageRequest1);
            result.put("abnormalOrders",String.valueOf(selectRegistAndRersonNamePage.getTotal()));
            responseData.setData(result);
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    public static Map<String,Object> objectToMap(Object obj) throws Exception{
        HashMap<String, Object> map = new HashMap<>();
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object value = field.get(obj);
            map.put(fieldName, value);
        }
        return map;
    }

    @Override
    public ResponseData newNote(HotelShiftRecordRequest hotelShiftRecordRequest) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            TbUserSession user = this.getTbUserSession(hotelShiftRecordRequest.getSessionToken());
            HotelShiftRecord hotelShiftRecord = new HotelShiftRecord();
            hotelShiftRecord.setHid(user.getHid());
            hotelShiftRecord.setHotelGroupId(user.getHotelGroupId());
            hotelShiftRecord.setCreateUserId(user.getUserId());
            hotelShiftRecord.setUpdateUserId(user.getUserId());
            hotelShiftRecord.setCreateTime(new Date());
            hotelShiftRecord.setUpdateTime(new Date());
            if(StringUtils.isNotBlank(hotelShiftRecordRequest.getCardExchange())){
                hotelShiftRecord.setRoomCardHandover(hotelShiftRecordRequest.getCardExchange());
            }
            if(StringUtils.isNotBlank(hotelShiftRecordRequest.getGuestRelated())){
                hotelShiftRecord.setGuestRelated(hotelShiftRecordRequest.getGuestRelated());
            }
            hotelShiftRecord.setHandoverMessage(hotelShiftRecordRequest.getHandoverNote());
            hotelShiftRecord.setCreateUserName(user.getUserName());
            hotelShiftRecord.setUpdateUserName(user.getUserName());
            hotelShiftRecordDao.saveHotelShiftRecord(hotelShiftRecord);
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData classNoteList(HotelShiftRecordSearch hotelShiftRecordSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            if(!CollectionUtils.isEmpty(hotelShiftRecordSearch.getDateTime())){
                hotelShiftRecordSearch.setStartTime(hotelShiftRecordSearch.getDateTime().get(0));
                hotelShiftRecordSearch.setEndTime(hotelShiftRecordSearch.getDateTime().get(1));
            }
            TbUserSession user = this.getTbUserSession(hotelShiftRecordSearch.getSessionToken());
            hotelShiftRecordSearch.setHid(user.getHid());
            hotelShiftRecordSearch.setHotelGroupId(user.getHotelGroupId());
            Page<HotelShiftRecord> hotelShiftRecords = hotelShiftRecordDao.selectBySearch(hotelShiftRecordSearch);
            if(!CollectionUtils.isEmpty(hotelShiftRecords.getResult())){
                hotelShiftRecords.forEach(hotelShiftRecord -> {
                    hotelShiftRecord.setCardExchange(StringUtils.isEmpty(hotelShiftRecord.getRoomCardHandover()) ? null : hotelShiftRecord.getRoomCardHandover());
                    hotelShiftRecord.setHandoverNote(hotelShiftRecord.getHandoverMessage());
                    hotelShiftRecord.setLeaver(hotelShiftRecord.getCreateUserName());
                    hotelShiftRecord.setLeaveTime(DateUtil.format(hotelShiftRecord.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                });

            }
            responseData.setData(hotelShiftRecords);
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData hotelBanner() {
        ResponseData responseData = new ResponseData(ER.SUCC);

        return responseData;
    }

    @Override
    public ResponseData roomState(BaseRequest baseRequest) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        HotelRoomStateData roomStateData = new HotelRoomStateData();
        try {
            RoomInfoAllSearch search = new RoomInfoAllSearch();
            TbUserSession user = this.getTbUserSession(baseRequest.getSessionToken());
            search.setHid(user.getHid()+"");
            search.setState(1);
            List<RoomInfo> list = roomInfoDao.findAllHotelRoomInfo(search);
            if(!CollectionUtils.isEmpty(list)){
                long emptyCleanRoom = list.stream().filter(roomInfo -> 1 == roomInfo.getRoomNumState()).count();
                long emptyDirtyRoom = list.stream().filter(roomInfo -> 2 == roomInfo.getRoomNumState()).count();
                roomStateData.setEmptyCleanRoom(emptyCleanRoom);
                roomStateData.setEmptyDirtyRoom(emptyDirtyRoom);
                roomStateData.setEmptyRoom(emptyCleanRoom+emptyDirtyRoom);

                long occupiedCleanRoom = list.stream().filter(roomInfo -> 3 == roomInfo.getRoomNumState()).count();
                long occupiedDirtyRoom = list.stream().filter(roomInfo -> 4 == roomInfo.getRoomNumState()).count();
                roomStateData.setOccupiedCleanRoom(occupiedCleanRoom);
                roomStateData.setOccupiedDirtyRoom(occupiedDirtyRoom);
                roomStateData.setOccupiedRoom(occupiedCleanRoom+occupiedDirtyRoom);
            }
            responseData.setData(roomStateData);
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData commonFunctions(BaseRequest baseRequest) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            TbUserSession user = this.getTbUserSession(baseRequest.getSessionToken());
            HotelCommonFunction hotelCommonFunction = hotelCommonFunctionDao.selectByCondition(user.getHid());
            if(Objects.isNull(hotelCommonFunction)){
                //默认值
                List<String> list = Arrays.asList("ssft","yqft","yd","rz","room_order","lb");
                responseData.setData(list);
            }else {
                String commonFunction = hotelCommonFunction.getCommonFunction();
                List<String> result = Arrays.stream(commonFunction.substring(1, commonFunction.length() - 1).split(",")).map(s -> s.replace(" ", "")).collect(Collectors.toList());
                responseData.setData(result);
            }
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData commonFunctionsSetting(CommonFunctionsSettingDto commonFunctionsSettingDto) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            TbUserSession user = this.getTbUserSession(commonFunctionsSettingDto.getSessionToken());
            HotelCommonFunction hotelCommonFunction = hotelCommonFunctionDao.selectByCondition(user.getHid());
            if(Objects.isNull(hotelCommonFunction)){
                HotelCommonFunction hotelCommonFunctionAdd = new HotelCommonFunction();
                hotelCommonFunctionAdd.setHid(user.getHid());
                hotelCommonFunctionAdd.setHotelGroupId(user.getHotelGroupId());
                hotelCommonFunctionAdd.setCommonFunction(commonFunctionsSettingDto.getCommonFunctions().toString());
                hotelCommonFunctionAdd.setCreateTime(new Date());
                hotelCommonFunctionAdd.setUpdateTime(new Date());
                hotelCommonFunctionAdd.setCreateUserId(user.getUserId());
                hotelCommonFunctionAdd.setUpdateUserId(user.getUserId());
                hotelCommonFunctionAdd.setCreateUserName(user.getUserName());
                hotelCommonFunctionAdd.setUpdateUserName(user.getUserName());
                hotelCommonFunctionDao.saveHotelCommonFunction(hotelCommonFunctionAdd);
            }else {
                hotelCommonFunction.setCommonFunction(commonFunctionsSettingDto.getCommonFunctions().toString());
                hotelCommonFunctionDao.updateHotelCommonFunction(hotelCommonFunction);
            }
        }catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 计算今日开房数
     * @param user
     * @return
     */
    private Integer getHotelRoomTypeRegister(TbUserSession user,Integer businessDay){
        GetHotelDataInfoParam param = new GetHotelDataInfoParam();
        param.setHid(user.getHid());
        if(null != businessDay){
            param.setBusinessDay(businessDay);
        }
        List<ResourceView> hotelDataInfo = pmsMainDao.getTodayRegister(param);
        //当日开房数
        Integer totalUseCount = 0;
        for (ResourceView view : hotelDataInfo) {
            totalUseCount += view.getCount();
        }
        return totalUseCount;
    }

    /**
     * 计算昨日开房数
     * @param hid
     * @param businessDay
     * @return
     */
    private Integer getYesterdayRegister(Integer hid,Integer businessDay){
        try {
            // 房型数据
            RoomTypeRegisterSearch nightAuditRoomTypeSearch = new RoomTypeRegisterSearch();
            nightAuditRoomTypeSearch.setHid(hid);
            nightAuditRoomTypeSearch.setBusinessDayMax(businessDay);
            nightAuditRoomTypeSearch.setBusinessDayMin(businessDay-1);
            List<RoomTypeNight> nightAuditRoomTypes = roomTypeDao.selectYesterdayRegister(nightAuditRoomTypeSearch);
            // 日期汇总
            int sumCount = 0;
            Map<Integer, List<RoomTypeNight>> dayMap = nightAuditRoomTypes.stream().collect(Collectors.groupingBy(RoomTypeNight::getBusinessDay));
            Set<Integer> dayKeys = dayMap.keySet();
            for(Integer key :dayKeys){
                List<RoomTypeNight> nightAuditRoomTypes1 = dayMap.get(key);
                int sum = nightAuditRoomTypes1.stream().mapToInt(RoomTypeNight::getOpenRoomCount).sum();
                sumCount+=sum;
            }
            return sumCount;
        } catch (Exception e) {
            log.error("",e);
        }
        return 0;
    }

    /**
     * 总客房数-维修-自用
     * @param hid
     * @return
     */
    private Integer getBcs(Integer hid){
        RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
        roomInfoSearch.setHid(hid);
        roomInfoSearch.setState(1);
        List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
        // 维修
        int wxNum = 0;
        // 按照房态分组
        Map<Integer, List<RoomInfo>> roomStateMap = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomNumState));
        // 维修数据
        List<RoomInfo> wxList = roomStateMap.get(ROOM_STATUS.OOO);
        if (wxList != null) {
            wxNum = wxList.size();
        }
        return roomInfos.size() - wxNum - getZyNum(hid,null);
    }

    /**
     * 查询自用房数量
     * @param hid
     * @param regists
     * @return
     */
    private Integer getZyNum(Integer hid,List<Regist> regists){
        // 查询在住信息
        if(null == regists){
            regists = getRegists(hid);
        }
        // 自用
        int zyNum = 0;
        for (Regist regist : regists) {
            Integer checkinType = regist.getCheckinType();
            if (checkinType == 5 || checkinType == 4) {
                zyNum++;
            }
        }
        return zyNum;
    }

    // 查询在住信息
    private List<Regist> getRegists(Integer hid){
        RegistSearch registSearch = new RegistSearch();
        registSearch.setHid(hid);
        registSearch.setState(0);
        return registDao.selectBySearch(registSearch);
    }

    /**
     * 计算昨天入住率
     * @param hid
     */
    @Override
    public double yesterdayOccupancyRate(Integer hid) {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date yesterday = calendar.getTime();
            Integer yesterdayBusinessDay = Integer.parseInt(DateUtil.format(yesterday,DateUtil.DATE_D_NO_SYMBOL));

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(hid);
            registSearch.setState(1);
            registSearch.setBusinessDay(yesterdayBusinessDay);
            List<Regist> regists = registDao.selectBySearch(registSearch);
            // 入住率 =（实时在住客房数－自用房）/（总客房数-维修-自用）×100%
            double v = (1.0 * (CollectionUtils.isEmpty(regists)?0:regists.size()) - getZyNum(hid,regists)) / getBcs(hid);
            if (Double.isNaN(v)) {
                v = 0.0;
            }
            return v;
        } catch (Exception e) {
            log.error("定时任务计算实时入住率异常", e);
        }
        return 0.00;
    }

    /**
     * 计算当前入住率
     * @param hid
     */
    @Override
    public double currentOccupancyRate(Integer hid) {
        try {
            List<Regist> regists = getRegists(hid);
            // 当前入住率 =（实时在住客房数－自用房）/（总客房数-维修-自用）×100%
            double v = (1.0 * (CollectionUtils.isEmpty(regists)?0:regists.size()) - getZyNum(hid,regists)) / getBcs(hid);
            if (Double.isNaN(v)) {
                v = 0.0;
            }
            // 当前入住率
            return v;
        } catch (Exception e) {
            log.error("定时任务计算实时入住率异常", e);
        }
        return 0.00;
    }

    /**
     * 之前7天订出率
     * @param hid
     * @param result
     */
    @Override
    public void preSevenBookRate(Integer hid,JSONObject result){
        double rate = 0;
        Integer bcs = getBcs(hid);
        for(int i=1;i<8;i++){
            List<Object> list = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -i);
            Date time = calendar.getTime();
            String timeStr = DateUtil.format(time,DateUtil.DATE_D)+" 00:00:00";

            Map<String, Object> param = new HashMap<>();
            param.put("hid",hid);
            param.put("time",timeStr);
            int rzCount = registDao.countByCheckTime(param);
            // 订出率 =（预定客房数）/（总客房数-维修-自用）×100%
            double v = (1.0 * rzCount) / bcs;
            if (Double.isNaN(v)){
                v = 0.0;
            }
            double rateValue = v * 100;
            rate += rateValue;
            String yyyyMMdd = DateUtil.format(time,DateUtil.DATE_D_NO_SYMBOL);
            list.add(yyyyMMdd);
            list.add(String.format(df,v * 100));
        }
        String preSevenDaysOccupancyRate = String.format(df,rate/7)+"%";
        result.put("preSevenDaysOccupancyRate",preSevenDaysOccupancyRate);
        result.put("preSevenRateValue",rate);
    }

    /**
     * 未来7天订出率
     * @param hid
     * @param result
     */
    @Override
    public void futureSevenBookRate(Integer hid,JSONObject result,String sessionToken){
        List<List<Object>> nextSevenDaysOccupancyChart = new ArrayList<>();
        Integer bcs = getBcs(hid);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sessionToken",sessionToken);
        ResponseData responseData = turnAlwaysService.findRoomTypeUseData(jsonObject);
        JSONObject data = new JSONObject();
        if(1 == responseData.getCode()){
            data = (JSONObject) responseData.getData();
        }
        double rate = 0;
        for(int i=0;i<7;i++){
            List<Object> list = new ArrayList<>();
            Calendar calendar = Calendar.getInstance();

            calendar.add(Calendar.DAY_OF_MONTH, i);
            Date time = calendar.getTime();
            String yyyyMMdd = DateUtil.format(time,DateUtil.DATE_D_NO_SYMBOL);
            long ydCount = 0;
            try {
                JSONObject dayData = data.getJSONObject(yyyyMMdd);
                int checkInNum = dayData.getInt("1");
                int bookNoRoomNum = dayData.getInt("4");
                ydCount = checkInNum+bookNoRoomNum;
            }catch (Exception e){
                log.warn("findRoomTypeUseData e:{}",data.toString());
            }
            // 订出率 =（预定客房数）/（总客房数-维修-自用）×100%
            double v = (1.0 * ydCount) / bcs;
            if (Double.isNaN(v)){
                v = 0.0;
            }
            double rateValue = v * 100;
            rate += rateValue;
            list.add(yyyyMMdd);
            list.add(String.format(df,rateValue));
            nextSevenDaysOccupancyChart.add(list);
        }
        String nextSevenDaysOccupancyRate = String.format(df,rate/7)+"%";
        result.put("nextSevenRateValue",rate);
        result.put("nextSevenDaysOccupancyRate",nextSevenDaysOccupancyRate);
        result.put("nextSevenDaysOccupancyChart",nextSevenDaysOccupancyChart);
    }
}
