package com.pms.czabsorders.service.overstay.transaction.impl;

import com.pms.czabsorders.bean.AddArAccount;
import com.pms.czabsorders.service.overstay.transaction.OverStayTransactionService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.dao.HourRoomDayUseDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czmembership.bean.company.HotelCompanyAccountInfo;
import com.pms.czmembership.bean.company.HotelCompanyArRecode;
import com.pms.czmembership.dao.company.HotelCompanyAccountInfoDao;
import com.pms.czmembership.dao.company.HotelCompanyArRecodeDao;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.dao.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Primary
public class OverStayTransactionServiceImpl implements OverStayTransactionService {

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;
    @Autowired
    private RegistDao registDao;
    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private HotelCompanyArRecodeDao hotelCompanyArRecodeDao;

    @Autowired
    private HotelCompanyAccountInfoDao hotelCompanyAccountInfoDao;

    @Autowired
    private RegistStayoverDao registStayoverDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private HourRoomDayUseDao hourRoomDayUseDao;

    @Autowired
    private RegistChangeRecordDao registChangeRecordDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void overStayTransaction(Regist regist, List<BookingOrderDailyPrice> bookingOrderDailyPrices, List<Integer> delBookingOrderDailyPriceids, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, TbUserSession user, ArrayList<Oprecord> oprecords) throws Exception {
        int update = 0;
        for (int i = 0; i < delBookingOrderDailyPriceids.size(); i++) {
            update = bookingOrderDailyPriceDao.deleteBookingOrderDailyPrice(delBookingOrderDailyPriceids.get(i));
            if (update < 1) {
                throw new Exception("删除房价信息失败");
            }
        }

        update = registDao.update(regist);
        if (update < 1) {
            throw new Exception("修改主单信息失败");
        }

        for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
            update = bookingOrderDailyPriceDao.saveBookingOrderDailyPrice(bookingOrderDailyPrices.get(i));
            if (update < 1) {
                throw new Exception("添加房价信息失败");
            }
        }

        //6.辅助房态
        for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {

            Integer integer = roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());
            if (integer < 1) {
                throw new Exception("删除辅助房态失败。编号:" + roomAuxiliaryRelation.getRelationId());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void overStayTransactionTwo(Regist regist, List<BookingOrderDailyPrice> bookingOrderDailyPrices, List<BookingOrderDailyPrice> deletePrices, AddArAccount addArAccount, HotelCompanyAccountInfo hotelCompanyAccountInfo, BookingOrderConfig bookingOrderConfig, RegistStayover rs) throws Exception {

        Integer update = registDao.update(regist);

        if (update < 1) {
            throw new Exception("修改登记单失败");
        }

        Integer integer = bookingOrderConfigDao.editBookingOrderConfig(bookingOrderConfig);

        if (integer < 1) {
            throw new Exception("修改配置信息失败");
        }

        if (bookingOrderDailyPrices.size() > 0) {
            bookingOrderDailyPriceDao.addPriceList(bookingOrderDailyPrices);
        }

        if (deletePrices.size() > 0) {
            bookingOrderDailyPriceDao.deletePriceList(deletePrices);
        }


        if (addArAccount != null) {

            Account account = addArAccount.getAccount();

            HotelCompanyArRecode hotelCompanyArRecode = addArAccount.getHotelCompanyArRecode();

            Integer integer1 = accountDao.saveAccount(account);

            if (integer1 < 1) {
                throw new Exception("添加挂账信息失败");
            }

            hotelCompanyArRecode.setTransactionId(account.getAccountId());
            Integer insert = hotelCompanyArRecodeDao.insert(hotelCompanyArRecode);


            if (insert < 1) {
                throw new Exception("添加挂账信息失败-1");
            }

            Integer update1 = hotelCompanyAccountInfoDao.update(hotelCompanyAccountInfo);

            if (update1 < 1) {
                throw new Exception("修改账户信息失败");
            }

        }


        registStayoverDao.insert(rs);

    }

    @Override
    public void overStayHourTransaction(Regist regist, Account account, RegistStayover rs, ArrayList<HourRoomDayUse> addHourUse, ArrayList<HourRoomDayUse> upaHourUse) throws Exception {
        Integer update = registDao.update(regist);

        if (update < 1) {
            throw new Exception("修改登记单失败");
        }
        accountDao.saveAccount(account);

        if (addHourUse.size() > 0) {

            for (HourRoomDayUse hourRoomDayUse : addHourUse) {
                String useMsg = hourRoomDayUse.getUseMsg();
                if (hourRoomDayUse.getRoomInfoId() == null || hourRoomDayUse.getRoomNo() == null) {
                    continue;
                }
                hourRoomDayUseDao.insert(hourRoomDayUse);

            }

        }

        if (upaHourUse.size() > 0) {
            for (HourRoomDayUse hourRoomDayUse : upaHourUse) {
                String useMsg = hourRoomDayUse.getUseMsg();
                if (useMsg.length() < 1) {
                    hourRoomDayUseDao.delete(hourRoomDayUse.getId());
                } else {
                    hourRoomDayUseDao.update(hourRoomDayUse);
                }

            }
        }

        registStayoverDao.insert(rs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void overStayTransactionOrder(Regist regist, List<BookingOrderDailyPrice> bookingOrderDailyPrices, BookingOrder oldBook, BookingOrder newBook, BookingOrderRoomNum oldBookRoom, BookingOrderRoomNum newBookRoom, BookingOrderConfig bookingOrderConfig, RegistStayover rs, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, List<RegistPerson> registPeople, RegistChangeRecord registChangeRecord, RoomInfo roomInfo, RoomInfo oldRoomInfo) throws Exception {

        // 修改订单信息
        Integer update = registDao.update(regist);

        if (update < 1) {
            throw new Exception("修改登记单失败");
        }

        Integer integer = bookingOrderConfigDao.editBookingOrderConfig(bookingOrderConfig);

        if (integer < 1) {
            throw new Exception("修改配置信息失败");
        }

        if (bookingOrderDailyPrices.size() > 0) {
            bookingOrderDailyPriceDao.updatePriceList(bookingOrderDailyPrices);
        }

        if (oldBook.getBookingOrderId() > 0) {
            bookingOrderDao.editBookingOrder(oldBook);
        }

        if (oldBookRoom.getId() > 0) {
            oldBookRoom.setIsCheckout(1);
            bookingOrderRoomNumDao.editBookingOrderRoomNum(oldBookRoom);
        }

        bookingOrderDao.editBookingOrder(newBook);

        bookingOrderRoomNumDao.editBookingOrderRoomNum(newBookRoom);

        if (roomAuxiliaryRelations.size() > 0) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(roomAuxiliaryRelations);
        }

        if (registPeople.size() > 0) {
            registPersonDao.updatePeople(registPeople);
        }

        if (null != registChangeRecord) {
            registChangeRecordDao.insert(registChangeRecord);
        }
//        if (roomInfo.getRoomNumState() != ROOM_STATUS.OCC && roomInfo.getRoomNumState() != ROOM_STATUS.OD) {
//            RoomInfo roomInfo1 = new RoomInfo();
//            roomInfo1.setRoomInfoId(roomInfo.getRoomInfoId());
//            roomInfo1.setRoomNumState(ROOM_STATUS.OCC);
//            roomInfoDao.editRoomInfo(roomInfo1);
//        }
        if (oldRoomInfo != null) {
            oldRoomInfo.setRoomNumState(ROOM_STATUS.VD);
            update = roomInfoDao.editRoomInfo(oldRoomInfo);
            if (update < 1) {
                throw new Exception("修改原来房间房态失败");
            }
            roomInfo.setRoomNumState(ROOM_STATUS.OCC);
            update = roomInfoDao.editRoomInfo(roomInfo);
            if (update < 1) {
                throw new Exception("修改新的房间房态失败");
            }
        }

        registStayoverDao.insert(rs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void overStayListTransaction(List<Regist> regist, List<BookingOrderDailyPrice> addPrices, List<BookingOrderDailyPrice> delPrices) throws Exception {
        registDao.updateRegistList(regist);
        if (addPrices.size() > 0) {
            bookingOrderDailyPriceDao.addPriceList(addPrices);
        }
        if (delPrices.size() > 0) {
            bookingOrderDailyPriceDao.deletePriceList(delPrices);
        }
    }
}
