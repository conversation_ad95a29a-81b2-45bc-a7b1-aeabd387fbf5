package com.pms.czabsorders.service.overstay.impl;


import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.AddArAccount;
import com.pms.czabsorders.service.overstay.OverStayService;
import com.pms.czabsorders.service.overstay.transaction.OverStayTransactionService;
import com.pms.czabsorders.service.turnAlways.TurnAlwaysService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.price.RoomRateCodeSpecific;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSpecificSearch;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.search.RoomAuxiliaryRelationSearch;
import com.pms.czhotelfoundation.bean.search.HourRoomDayUseSearch;
import com.pms.czhotelfoundation.dao.HourRoomDayUseDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeSpecificDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.service.price.RoomDayPriceService;
import com.pms.czhotelfoundation.service.room.RoomService;
import com.pms.czmembership.bean.company.HotelCompanyAccount;
import com.pms.czmembership.bean.company.HotelCompanyAccountInfo;
import com.pms.czmembership.dao.company.HotelCompanyAccountDao;
import com.pms.czmembership.dao.company.HotelCompanyAccountInfoDao;
import com.pms.czmembership.dao.company.HotelCompanyArRecodeDao;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.conf.HotelIotPlatConfig;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.constant.Iot;
import com.pms.czpmsutils.constant.SMS_LOC;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.OprecordInfoRequest;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.*;
import com.pms.czpmsutils.thirdauth.HotelIotStrategy;
import com.pms.czpmsutils.view.AESUtil;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.search.*;
import com.pms.pmsorder.dao.*;
import lombok.extern.log4j.Log4j2;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

//import com.pms.czbuinesscheckin.overstay.transaction.OverStayTransactionService;

@Log4j2
@Service
@Primary
public class OverStayServiceImpl extends BaseService implements OverStayService {

    @Autowired
    private RegistDao registDao;

    @Autowired
    private RoomRateCodeSpecificDao roomRateCodeSpecificDao;

    @Autowired
    private RoomRateCodeDao roomRateCodeDao;

    @Autowired
    private RoomDayPriceService roomDayPriceService;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private OverStayTransactionService overStayTransactionService;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private RoomService roomService;

    @Autowired
    private TurnAlwaysService turnAlwaysService;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private HotelCompanyArRecodeDao hotelCompanyArRecodeDao;

    private BaseService baseService = this;

    @Autowired
    private HotelCompanyAccountDao hotelCompanyAccountDao;

    @Autowired
    private HotelCompanyAccountInfoDao hotelCompanyAccountInfoDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private HourRoomDayUseDao hourRoomDayUseDao;


    @Resource
    private HotelIotPlatConfig iotPlatConfig;

    @Resource
    private WebClientUtil webClientUtil;


    @Override
    public ResponseData overStay(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            //操作日志
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);

            if (param.get("registId") == null || param.getString("registId").equals("")) {
                throw new Exception("registId不能空");
            }
            int registId = param.getInt("registId");

            if (param.get("stayOverParam") == null || param.getString("stayOverParam").length() < 1) {
                throw new Exception("缺少续住参数");
            }
            JSONObject stayOverParam = JSONObject.fromObject(URLDecoder.decode(param.getString("stayOverParam")));


            if (stayOverParam.get("newDate") == null || stayOverParam.getString("newDate").equals("")) {
                throw new Exception("新的离店日期不能空");
            }


            if (stayOverParam.get("priceList") == null || stayOverParam.getString("priceList").length() < 1) {
                throw new Exception("缺少房价信息");
            }

            JSONArray priceList = stayOverParam.getJSONArray("priceList");

            String checkOutTime = stayOverParam.getString("newDate");

            /**
             * 1，验证未来房情，暂时未实现
             */
            Regist regist = registDao.selectById(registId);
            if (regist == null || !regist.getHid().equals(user.getHid())) {
                throw new Exception("未查到当前房间信息。编号:" + regist.getRegistId());
            }
            if (regist.getState() != 0) {
                throw new Exception("当前订单状态为 " + HotelUtils.stateMap.get(regist.getState()) + " ，不允许续住。编号:" + regist.getRegistId());
            }

            // 查询当前订单配置信息
            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setRegistId(regist.getRegistId());
            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);

            BookingOrderConfig bookingOrderConfig = bookingOrderConfigs.get(0);
            /**
             * 续住执行的房价码
             */
            if (stayOverParam.get("rateCode") == null || stayOverParam.getString("rateCode").equals("")) {
                stayOverParam.put("rateCode", regist.getRoomRateCodeName());
            }

            if (stayOverParam.get("rateId") == null || stayOverParam.getString("rateId").equals("")) {
                stayOverParam.put("rateId", regist.getRoomRateCodeId());
            }

            Date startDate = regist.getCheckoutTime();
            int rateCodeId = stayOverParam.getInt("rateId");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date checkOutDate = simpleDateFormat.parse(checkOutTime);
            regist.setRoomRateCodeId(rateCodeId);
            //如果房价码不相同
            if (rateCodeId != regist.getRoomRateCodeId()) {
                regist.setRoomRateCodeName(stayOverParam.getString("rateCode"));
            }
            regist.setCheckoutTime(checkOutDate);

            List<BookingOrderDailyPrice> bookingOrderDailyPrices = new ArrayList<BookingOrderDailyPrice>();
            for (int i = 0; i < priceList.size(); i++) {
                BookingOrderDailyPrice bookingOrderDailyPrice = new BookingOrderDailyPrice();
                bookingOrderDailyPrice.setHid(user.getHid());
                bookingOrderDailyPrice.setHotelGroupId(user.getHotelGroupId());
                bookingOrderDailyPrice.setRegistId(regist.getRegistId());
                bookingOrderDailyPrice.setRoomNumId(regist.getRoomNumId());
                bookingOrderDailyPrice.setRoomTypeId(regist.getRoomTypeId());
                bookingOrderDailyPrice.setIsStayover(1);
                bookingOrderDailyPrice.setDailyState(1);
                bookingOrderDailyPrice.setUpdateUserName(user.getUserName());
                bookingOrderDailyPrice.setUpdateTime(new Date());
                bookingOrderDailyPrice.setUpdateUserId(user.getUserId());
                bookingOrderDailyPrice.setCreateUserName(user.getUserName());
                bookingOrderDailyPrice.setCreateUserId(user.getUserId());
                bookingOrderDailyPrice.setCreateTime(new Date());
                bookingOrderDailyPrice.setId(null);
                bookingOrderDailyPrice.setPrice(priceList.getJSONObject(i).getInt("price"));
                bookingOrderDailyPrice.setDailyTime(priceList.getJSONObject(i).getInt("dayTime"));
                //早餐券相关业务处理
                bookingOrderDailyPrice.setBreakAddNum(priceList.getJSONObject(i).containsKey("breakAddNum") ? priceList.getJSONObject(i).getInt("breakAddNum") : 0);
                bookingOrderDailyPrice.setBreakNum(priceList.getJSONObject(i).containsKey("breakNum") ? priceList.getJSONObject(i).getInt("breakNum") : 0);
                bookingOrderDailyPrice.setBreakUseNum(priceList.getJSONObject(i).containsKey("breakUseNum") ? priceList.getJSONObject(i).getInt("breakUseNum") : 0);
                bookingOrderDailyPrices.add(bookingOrderDailyPrice);
            }


            List<Integer> delBookingOrderDailyPriceids = new ArrayList<Integer>();
            /**
             * 提前离店
             */
            if (startDate.compareTo(checkOutDate) > 0 && regist.getCheckinTime().compareTo(checkOutDate) < 0) {

                Integer dayDate = Integer.parseInt(DateFormatUtils.format(checkOutDate, "yyyy-MM-dd").toString().replace("-", ""));
                BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
                bookingOrderDailyPriceSearch.setRegistId(registId);
                bookingOrderDailyPriceSearch.setDailyTime(dayDate);
                List<BookingOrderDailyPrice> DailyPrices = bookingOrderDailyPriceDao.selectByDate(bookingOrderDailyPriceSearch);

                for (int i = 0; i < DailyPrices.size(); i++) {
                    delBookingOrderDailyPriceids.add(DailyPrices.get(i).getId());
                }

                bookingOrderDailyPrices = new ArrayList<BookingOrderDailyPrice>();
            }

//            /**
//             * 2，修改每日房价表
//             */
//            if (param.get("dayPrice") == null){
//                throw new Exception("每日房价详情不能空");
//            }
//
//            List<BookingOrderDailyPrice> bookingOrderDailyPrices = new ArrayList<BookingOrderDailyPrice>();
//
//            JSONObject dayPrice = param.getJSONObject("dayPrice");
//
//
//            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(startDate).substring(0 ,10), checkOutTime.substring(0 ,10));
//            for (String date : allDayListBetweenDate) {
//                BookingOrderDailyPrice bookingOrderDailyPrice = new BookingOrderDailyPrice();
//                bookingOrderDailyPrice.setHid(user.getHid());
//                bookingOrderDailyPrice.setHotelGroupId(user.getHotelGroupId());
//                bookingOrderDailyPrice.setRegistId(regist.getRegistId());
//                bookingOrderDailyPrice.setRoomNumId(regist.getRoomNumId());
//                bookingOrderDailyPrice.setRoomTypeId(regist.getRoomTypeId());
//                bookingOrderDailyPrice.setIsStayover(1);
//                bookingOrderDailyPrice.setDailyState(1);
//                bookingOrderDailyPrice.setUpdateUserName(user.getUserName());
//                bookingOrderDailyPrice.setUpdateTime(new Date());
//                bookingOrderDailyPrice.setUpdateUserId(user.getUserId());
//                bookingOrderDailyPrice.setCreateUserName(user.getUserName());
//                bookingOrderDailyPrice.setCreateUserId(user.getUserId());
//                bookingOrderDailyPrice.setCreateTime(new Date());
//                bookingOrderDailyPrice.setId(null);
//                Double price = dayPrice.getDouble(date);
//                int idate = Integer.parseInt(date.replace("-", ""));
//                bookingOrderDailyPrice.setPrice(price.intValue());
//                bookingOrderDailyPrice.setDailyTime(idate);
//                bookingOrderDailyPrices.add(bookingOrderDailyPrice);
//            }


            /**
             * 4.对辅助房态进行修改
             */
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setHid(user.getHid());
            roomAuxiliaryRelationSearch.setRoomId(regist.getRoomNumId());
            List<RoomAuxiliaryRelation> roomAuxiliaryRelationList = new ArrayList<RoomAuxiliaryRelation>();
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);
            for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {
                Integer roomAuxiliaryId = roomAuxiliaryRelation.getRoomAuxiliaryId();
                if (roomAuxiliaryId == 3) {
                    roomAuxiliaryRelationList.add(roomAuxiliaryRelation);
                }
            }
            overStayTransactionService.overStayTransaction(regist, bookingOrderDailyPrices, delBookingOrderDailyPriceids, roomAuxiliaryRelationList, user, oprecords);

            final HashMap<String, String> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put("room", regist.getRoomNum());
            stringStringHashMap.put("oldDate", HotelUtils.getTime(startDate.getTime()).substring(0, 10));
            stringStringHashMap.put("newDate", stayOverParam.getString("newDate").substring(0, 10));

            if (5 == regist.getResourceId()) {
                bookingOrderConfig.setAutoAr(0);
                bookingOrderConfigDao.editBookingOrderConfig(bookingOrderConfig);
            }

            // 更新房间缓存
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        baseService.push(user.getHotelGroupId(), user.getHid(), 17, stringStringHashMap, stringStringHashMap, true, true);

                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 续住
     *
     * @param overStayRequest
     * @return
     */
    @Override
    public ResponseData overStayNew(OverStayRequest overStayRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = overStayRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Regist regist = registDao.selectById(overStayRequest.getRegistId());

            if (regist == null || !regist.getHid().equals(user.getHid()) || regist.getState() != 0) {
                throw new Exception("当前订单信息有误，未查到订单信息。");
            }

            //优化更新regist表字段业务
            Regist registInfo = Regist.CreateRegist(regist.getRegistId());

            Date checkoutTime = regist.getCheckoutTime();

            ArrayList<Oprecord> oprecords = new ArrayList<>();

            Oprecord oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setRoomNum(regist.getRoomNum());

            final HashMap<String, String> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put("room", regist.getRoomNum());
            stringStringHashMap.put("oldDate", HotelUtils.parseDate2Str(regist.getCheckoutTime()));


            // 查询当前订单配置信息
            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setRegistId(regist.getRegistId());
            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);

            BookingOrderConfig bookingOrderConfig = bookingOrderConfigs.get(0);

            // 查询房价方案，获取早餐信息
            RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
            roomRateCodeSpecificSearch.setHid(user.getHid());
            roomRateCodeSpecificSearch.setRateId(overStayRequest.getRateId());
            roomRateCodeSpecificSearch.setRoomTypeId(regist.getRoomTypeId());
            List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);

            int breakNum = 0;

            if (roomRateCodeSpecifics != null && roomRateCodeSpecifics.size() > 0) {
                RoomRateCodeSpecific roomRateCodeSpecific = roomRateCodeSpecifics.get(0);
                breakNum = roomRateCodeSpecific.getBreakfastNum();
            }

            regist.setRoomRateCodeId(overStayRequest.getRateId());
            regist.setRoomRateCodeName(overStayRequest.getRateCode());
            //新对象赋值
            registInfo.setRoomRateCodeId(overStayRequest.getRateId());
            registInfo.setRoomRateCodeName(overStayRequest.getRateCode());

            // 添加的房价信息
            ArrayList<BookingOrderDailyPrice> addPriceList = new ArrayList<>();

            AddArAccount addArAccount = null;

            // 查询原有房价
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setRegistId(regist.getRegistId());

            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, BookingOrderDailyPrice> priceMaps = bookingOrderDailyPrices.stream().collect(Collectors.toMap(BookingOrderDailyPrice::getDailyTime, a -> a, (k1, k2) -> k1));

            List<OverStayRequest.price> priceList = overStayRequest.getPriceList();

            Date date = new Date();

            StringBuilder dayPiceStr = new StringBuilder();

            Integer sumPrice = 0;

            Integer autoAr1 = overStayRequest.getAutoAr();

            Integer drid = 0;
            if (autoAr1 != null && autoAr1 == 1) {
                drid = regist.getRegistId();
            }


            for (OverStayRequest.price pc : priceList) {

                Integer daily = Integer.parseInt(pc.getDate().replace("-", ""));

                BookingOrderDailyPrice bookingOrderDailyPrice = priceMaps.get(daily);

                // 已添加的日期则过滤不添加
                if (bookingOrderDailyPrice != null && bookingOrderDailyPrice.getId() > 0) {
                    continue;
                }

                sumPrice += pc.getPrice();

                dayPiceStr.append(pc.getPrice());
                dayPiceStr.append(",");

                BookingOrderDailyPrice bodps = new BookingOrderDailyPrice();
                bodps.setId(null);
                bodps.setBookingOrderId(regist.getBookingOrderId());
                bodps.setHid(user.getHid());
                bodps.setHotelGroupId(user.getHotelGroupId());
                bodps.setPrice(pc.getPrice());
                bodps.setDailyTime(daily);
                bodps.setRoomTypeId(regist.getRoomTypeId());
                bodps.setRoomNumId(0);
                bodps.setDailyState(1);
                bodps.setIsStayover(0);
                bodps.setCreateTime(date);
                bodps.setCreateUserId(user.getUserId());
                bodps.setCreateUserName(user.getUserName());
                bodps.setUpdateTime(date);
                bodps.setUpdateUserId(user.getUserId());
                bodps.setUpdateUserName(user.getUserName());
                bodps.setRegistId(regist.getRegistId());
                bodps.setRoomNumId(regist.getRoomNumId());
                bodps.setBreakNum(breakNum);
                bodps.setBreakAddNum(0);
                bodps.setBreakUseNum(0);
                bodps.setBookingOrderRoomNumId(drid);

                addPriceList.add(bodps);

            }

            // 修改预订单时间
            Date newDates = HotelUtils.parseStr2Date(overStayRequest.getNewDate());

            regist.setCheckoutTime(newDates);
            registInfo.setCheckoutTime(newDates);
            if (regist.getDayCount() == null) {
                regist.setDayCount(1);
                registInfo.setDayCount(1);
            }
            regist.setDayCount(regist.getDayCount() + overStayRequest.getDayCount());
            registInfo.setDayCount(regist.getDayCount() + overStayRequest.getDayCount());

            stringStringHashMap.put("newDate", HotelUtils.parseDate2Str(regist.getCheckoutTime()));

            oprecord.setDescription("续住：" + overStayRequest.getDayCount() + "天。新的离店时间:" + HotelUtils.parseDate2Str(regist.getCheckoutTime()));
            oprecords.add(oprecord);

            // 判断当前订单信息 ,如果是OTA订单并且可以结账转AR，则说明是预付订单。
            Integer resourceId = regist.getResourceId();

            Integer autoAr = bookingOrderConfig.getAutoAr();

            HotelCompanyAccount hotelCompanyAccount = null;

            HotelCompanyAccountInfo hotelCompanyAccountInfo = null;


            int newDateInt = HotelUtils.parseDate2Int(regist.getCheckoutTime());

            ArrayList<BookingOrderDailyPrice> deletePrices = new ArrayList<>();

            Integer deleteMoney = 0;

            StringBuilder delDayPiceStr = new StringBuilder();

            for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {
                Integer dailyState = bodp.getDailyState();

                if (dailyState == 0) {
                    continue;
                }

                if (bodp.getDailyTime() >= newDateInt) {

                    deletePrices.add(bodp);
                    deleteMoney += bodp.getPrice();

                    delDayPiceStr.append(bodp.getPrice());
                    delDayPiceStr.append(",");

                }
            }


            // 判断是否是协议
        /*    if(resourceId==5 && autoAr==1){

                bookingOrderConfig.setAutoAr(0);

                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setIsCancel(0);
                accountSearch.setRegistId(regist.getRegistId());

                List<Account> accounts = accountDao.selectBySearch(accountSearch);

                Integer sumArAccount = 0 ;

                for(Account account:accounts){

                    String payCodeId = account.getPayCodeId();

                    if(!payCodeId.equals("9800")){
                        continue;
                    }

                    String thirdAccoutId = account.getAccountId();

                    HotelCompanyArRecodeSearch hotelCompanyArRecodeSearch = new HotelCompanyArRecodeSearch();
                    hotelCompanyArRecodeSearch.setHid(user.getHid());
                    hotelCompanyArRecodeSearch.setTransactionId(thirdAccoutId);
                    Page<HotelCompanyArRecode> hotelCompanyArRecodes = hotelCompanyArRecodeDao.selectBySearch(hotelCompanyArRecodeSearch);

                    if(hotelCompanyArRecodes==null||hotelCompanyArRecodes.size()<1){
                        continue;
                    }

                    HotelCompanyArRecode hotelCompanyArRecode = hotelCompanyArRecodes.get(0);

                    boolean equals = regist.getCompanyAccountId().equals(hotelCompanyArRecode.getCompanyAccountId());

                    if(equals){
                        sumArAccount+=account.getPrice();
                    }

                }

                Integer sumPrice = 0 ;
                // 查询所有的价格
                for(BookingOrderDailyPrice bodp:bookingOrderDailyPrices){
                    sumPrice+=bodp.getPrice();
                }

                sumPrice-=deleteMoney;

                int diff = sumPrice - sumArAccount;

                // 把原有的应挂账金额添加过去

                if(diff>0){

                    String a = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
                    Account account = new Account();
                    account.setAccountId(a);
                    account.setHid(user.getHid());
                    account.setCreateUserId(user.getUserId());
                    account.setCreateUserName(user.getUserName());
                    account.setCreateTime(new Date());
                    account.setIsCancel(0);
                    account.setAccountYear(user.getBusinessYear());
                    account.setAccountYearMonth(user.getBusinessMonth());
                    account.setBusinessDay(user.getBusinessDay());
                    account.setClassId(user.getClassId());
                    account.setAccountType(1);
                    account.setSettleAccountTime(new Date());
                    account.setThirdRefundState(0);
                    account.setRoomNum(regist.getRoomNum());
                    account.setRoomInfoId(regist.getRoomNumId());
                    account.setRoomTypeId(regist.getRoomTypeId());
                    account.setRegistState(0);
                    account.setRegistId(regist.getRegistId());
                    account.setRemark("续住自动挂房费");
                    account.setBookingId(regist.getBookingOrderId());
                    //类型 ：消费、付款 "9800", "AR帐", 9, "AR帐"
                    account.setPayType(2);
                    account.setSaleNum(1);
                    account.setPayClassId(9);
                    account.setPayClassName("AR帐");
                    //全天房费
                    account.setPayCodeId("9800");
                    account.setPayCodeName("AR帐");

                    //用户信息
                    account.setCreateTime(date);
                    account.setCreateUserId(user.getUserId());
                    account.setCreateUserName(user.getUserName());
                    account.setUpdateTime(date);
                    account.setUpdateUserId(user.getUserId());
                    account.setUpdateUserName(user.getUserName());

                    account.setClassId(user.getClassId());
                    account.setUpdateCalssId(user.getClassId());

                    // 营业日期
                    account.setBusinessDay(user.getBusinessDay());


                    //设置账务关联人id为0
                    account.setRegistPersonId(0);
                    account.setRegistPersonName("");
                    account.setPrice(diff);
                    account.setUintPrice(diff);



                    hotelCompanyAccount = hotelCompanyAccountDao.selectById(regist.getCompanyAccountId());


                    HotelCompanyArRecode hotelCompanyArRecode = new HotelCompanyArRecode();

                    hotelCompanyArRecode.setCompanyId(hotelCompanyAccount.getHotelCompanyId());
                    hotelCompanyArRecode.setGroupCompanyId(hotelCompanyAccount.getGroupCompanyId());
                    hotelCompanyArRecode.setCompanyAccountId(hotelCompanyAccount.getId());
                    // 房间id
                    hotelCompanyArRecode.setRoomInfoId(regist.getRoomNumId());
                    // 房号
                    hotelCompanyArRecode.setRoomNo(regist.getRoomNum());
                    hotelCompanyArRecode.setMoney(diff);
                    // 房间id
                    hotelCompanyArRecode.setRegistId(regist.getRegistId());

                    // 操作人id
                    //  hotelCompanyArRecode.setOperatorId(Integer.parseInt(user.getUserId()));
                    hotelCompanyArRecode.setOperatorName(user.getUserName());
                    // 备注
                    hotelCompanyArRecode.setOperatorName(user.getUserName() + "：续住自动挂房费 ");

                    hotelCompanyArRecode.setSettleId(regist.getRegistId());
                    hotelCompanyArRecode.setOperatTime(date);
                    hotelCompanyArRecode.setBusinessShiftId(user.getClassId());
                    hotelCompanyArRecode.setBusinessDay(user.getBusinessDay());
                    hotelCompanyArRecode.setCreateUserId(user.getUserId());
                    hotelCompanyArRecode.setCreateUserName(user.getUserName());
                    hotelCompanyArRecode.setCreateTime(date);
                    hotelCompanyArRecode.setUpdateUserId(user.getUserId());
                    hotelCompanyArRecode.setUpdateUserName(user.getUserName());
                    hotelCompanyArRecode.setCreateTime(date);
                    hotelCompanyArRecode.setHid(user.getHid());
                    hotelCompanyArRecode.setHotelGroupId(user.getHotelGroupId());
                    hotelCompanyArRecode.setPayState(0);

                    addArAccount = new AddArAccount();
                    addArAccount.setAccount(account);
                    addArAccount.setHotelCompanyArRecode(hotelCompanyArRecode);


                    hotelCompanyAccountInfo = hotelCompanyAccountInfoDao.selectById(hotelCompanyAccount.getId());
                    hotelCompanyAccountInfo.setId(hotelCompanyAccount.getId());
                    hotelCompanyAccountInfo.setNoOffWriteMoney(hotelCompanyAccountInfo.getNoOffWriteMoney() + diff);
                    hotelCompanyAccountInfo.setMaxLimit(hotelCompanyAccountInfo.getMaxLimit() - diff);

                    oprecord = new Oprecord(user);
                    oprecord.setOccurTime(HotelUtils.currentTime());
                    oprecord.setRegistId(regist.getRegistId());
                    oprecord.setRoomNum(regist.getRoomNum());

                    oprecord.setDescription("续住时将原有订单房费："+diff/100.0+"自动转到"+regist.getCompayName());

                    oprecords.add(oprecord);


                    regist.setSumPay(regist.getSumPay()+diff);

                }


            }*/

            RegistStayover rs = new RegistStayover();
            rs.setRegistId(regist.getRegistId());
            rs.setBookingOrderId(regist.getBookingOrderId());
            rs.setDayCount(overStayRequest.getDayCount());
            rs.setRoomNum(regist.getRoomNum());
            rs.setRoomTypeId(regist.getRoomTypeId());
            rs.setRoomTypeName(regist.getRoomTypeName());
            rs.setStartTime(checkoutTime);
            rs.setEndTime(regist.getCheckoutTime());
            rs.setNprices(dayPiceStr.toString());
            rs.setDescRemark("前台续住");
            rs.setState(1);
            if (deleteMoney > 0) {
                rs.setTotalPrice(deleteMoney);
                rs.setDayCount(overStayRequest.getDayCount() * -1);
                rs.setNprices(delDayPiceStr.toString());
            } else {
                rs.setTotalPrice(sumPrice);
            }

            rs.setHid(user.getHid());
            rs.setHotelGroupId(user.getHotelGroupId());
            rs.setClassId(user.getClassId());
            rs.setBusinessDay(user.getBusinessDay());
            rs.setBusinessDay(user.getBusinessMonth());
            rs.setBusinessYear(user.getBusinessYear());
            rs.setCreateUserId(user.getUserId());
            rs.setCreateUserName(user.getUserName());
            rs.setCreateTime(new Date());
            rs.setUpdateUserId(user.getUserId());
            rs.setUpdateUserName(user.getUserName());
            rs.setUpdateTime(new Date());
            rs.setType(1);

            // 班次6 说明自助机开房
            if (regist.getClassId() == 6) {
                rs.setTypeState(4);
            } else {
                rs.setTypeState(3);
            }

            overStayTransactionService.overStayTransactionTwo(registInfo, addPriceList, deletePrices, addArAccount, hotelCompanyAccountInfo, bookingOrderConfig, rs);

            this.addOprecords(oprecords);

            // 更新房间缓存
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        baseService.push(user.getHotelGroupId(), user.getHid(), 17, stringStringHashMap, stringStringHashMap, true, true);

                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });


            //2022-02-09 添加续住信息
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                        registPersonSearch.setRegistId(regist.getRegistId());
                        List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
                        if (registPersonList != null && registPersonList.get(0).getPhone() != null && registPersonList.get(0).getPhone().length() == 11) {
                            JSONObject postData = new JSONObject();
                            postData.put("sessionToken", sessionToken);
                            JSONObject hotelBaseInfo = JSONObject.fromObject(baseService.getHotelBaseInfo(postData));
                            JSONObject data = hotelBaseInfo.getJSONObject("data");
                            String hotelName = data.getString("hotelName");
                            String telephone = data.getString("telephone");
                            String addr = data.getString("addr");
                            //尊敬的{1}，您好，欢迎您入住{2}，您续住的房间号是{3}，退房时间为{4},如有疑问请拨打酒店电话：{5}
                            final ArrayList<String> strings = new ArrayList<>();
                            strings.add(registPersonList.get(0).getPersonName());
                            strings.add(hotelName);
                            strings.add(regist.getRoomNum());
                            strings.add(HotelUtils.parseDate2Str(regist.getCheckoutTime()));
                            strings.add(telephone);
                            SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                            smsHotelSendRecordRequest.setLocationId(SMS_LOC.STAY_OVER);
                            smsHotelSendRecordRequest.setSessionToken(user.getSessionId());
                            smsHotelSendRecordRequest.setPhoneNumber(registPersonList.get(0).getPhone());
                            smsHotelSendRecordRequest.setParams(strings);
                            baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);
                        }
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    //如果是智能门锁则调用
                    SmartLockRequest smartLockRequest = new SmartLockRequest();
                    smartLockRequest.setSessionToken(sessionToken);
                    smartLockRequest.setCheckinTime(regist.getCheckinTime());
                    smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                    smartLockRequest.setRoomTypeId(regist.getRoomTypeId());
                    smartLockRequest.setRoomTypeName(regist.getRoomTypeName());
                    smartLockRequest.setRoomNumId(regist.getRoomNumId());
                    smartLockRequest.setRoomNum(regist.getRoomNum());
                    smartLockRequest.setHid(regist.getHid());
                    smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                    smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                    smartLockRequest.setCheckinType(regist.getCheckinType());
                    smartLockRequest.setLockNo(regist.getSessionToken());
                    RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                    registPersonSearch.setRegistId(regist.getRegistId());
                    registPersonSearch.setRegistState(0);
                    List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                    List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
                    for (int j = 0; j < registPeople.size(); j++) {
                        RegistPerson registPersonInfo = registPeople.get(j);
                        SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                        BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                        peoplesList.add(registPersonDetail);
                    }
                    smartLockRequest.setPeoples(peoplesList);
                    baseService.SmartLockStayOver(smartLockRequest);
                }
            });

            ArrayList<OprecordInfoRequest> oprecordInfoRequests = new ArrayList<>();


            OprecordInfoRequest oprecordInfoRequest = new OprecordInfoRequest();
            oprecordInfoRequest.setSessionToken(sessionToken);
            JSONObject data = new JSONObject();
            data.put("roomNo", regist.getRoomNum());
            data.put("time", HotelUtils.currentTime());
            data.put("oldTime", stringStringHashMap.get("oldDate").toString());
            data.put("newTime", HotelUtils.parseDate2Str(regist.getCheckoutTime()));
           /* oprecordInfoRequest.setSessionToken(sessionToken);
            oprecordInfoRequest.setHid(user.getHid());
            oprecordInfoRequest.setRoomNum(regist.getRoomNum());
            oprecordInfoRequest.setRegistId(regist.getRegistId());
            oprecordInfoRequest.setOprecordTemplateId(13);
            oprecordInfoRequest.setBusinessId2(data.toString());
            oprecordInfoRequest.setData(data);
            oprecordInfoRequests.add(oprecordInfoRequest);
            this.baseService.addOprecordReqs(oprecordInfoRequests);*/


            /**
             * 续住数据推送iot酒店
             */
            IotHotelRoomStay iotHotelRoomStay = new IotHotelRoomStay();
            iotHotelRoomStay.setCode(overStayRequest.getRegistId()+"");
            iotHotelRoomStay.setNewCheckOutTime(overStayRequest.getNewDate());
            iotHotelRoomStay.setPmsRoomId(String.valueOf(regist.getRoomNumId()));
            iotHotelRoomStay.setDays(overStayRequest.getDayCount());
            HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
            Map<String, String> authParamMap = new HashMap<>();
            authParamMap.put("eid", user.getHid().toString());
            authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
            authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
            authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
            try {
                authParamMap.put("phone", AESUtil.encryptAesCbc(user.getPhone(),iotPlatConfig.getSassTokenSecret()));
            } catch (Exception e) {
                log.error("手机号加密失败");
            }
            hotelIotStrategy.initStrategy(authParamMap);
            webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.ROOM_STAY,
                    iotHotelRoomStay,
                    hotelIotStrategy);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    public ResponseData overHourStay(OverStayRequest overStayRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = overStayRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Regist regist = registDao.selectById(overStayRequest.getRegistId());

            if (regist == null || !regist.getHotelGroupId().equals(user.getHotelGroupId()) || regist.getState() != 0) {
                throw new Exception("当前订单信息有误，未查到订单信息。");
            }

            //优化更新regist表字段业务
            Regist registInfo = Regist.CreateRegist(regist.getRegistId());

            Date checkoutTime = regist.getCheckoutTime();

            ArrayList<Oprecord> oprecords = new ArrayList<>();

            Oprecord oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setRoomNum(regist.getRoomNum());

            final HashMap<String, String> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put("room", regist.getRoomNum());
            stringStringHashMap.put("oldDate", HotelUtils.parseDate2Str(regist.getCheckoutTime()));


            regist.setRoomRateCodeId(overStayRequest.getRateId());
            regist.setRoomRateCodeName(overStayRequest.getRateCode());
            //新对象赋值
            registInfo.setRoomRateCodeId(overStayRequest.getRateId());
            registInfo.setRoomRateCodeName(overStayRequest.getRateCode());

            // 修改预订单时间
            Date newDates = HotelUtils.parseStr2Date(overStayRequest.getNewDate());


            // 钟点房验证
            // 开始日期结束日期是否为同一天
            Boolean starEndOneDay = false;

            // 开始小时
            int inHours = regist.getCheckinTime().getHours();

            // 结束小时
            int outTours = regist.getCheckoutTime().getHours();

            HashMap<Integer, Boolean> hourStarMap = new HashMap<>();

            HashMap<Integer, Boolean> hourEndMap = new HashMap<>();

            String useStarHourStr = "";
            String useEndHourStr = "";

            // 开始时间
            Integer businessDayMin = HotelUtils.parseDate2Int(checkoutTime);

            // 结束时间
            Integer businessDayMax = HotelUtils.parseDate2Int(newDates);

            //  需要添加的钟点房使用
            ArrayList<HourRoomDayUse> addHourUse = new ArrayList<>();

            // 需要删除的钟点房使用详情
            ArrayList<HourRoomDayUse> upaHourUse = new ArrayList<>();
            // 钟点房验证
            HourRoomDayUseSearch hourRoomDayUseSearch = new HourRoomDayUseSearch();

            hourRoomDayUseSearch.setHid(user.getHid());
            hourRoomDayUseSearch.setBusinessDayMax(businessDayMax);
            hourRoomDayUseSearch.setBusinessDayMin(businessDayMin);

            // 每天使用情况
            Page<HourRoomDayUse> hourRoomDayUses = hourRoomDayUseDao.selectBySearch(hourRoomDayUseSearch);

            Map<Integer, List<HourRoomDayUse>> hourRoomMap = new HashMap<>();

            if (hourRoomDayUses.size() > 0) {

                hourRoomMap = hourRoomDayUses.stream().collect(Collectors.groupingBy(HourRoomDayUse::getRoomInfoId));

            }


            // 如果不是同一天，则把第二天日期也计算出来。
            if (!businessDayMin.equals(businessDayMax)) {
                starEndOneDay = true;

                for (int i = 0; i <= outTours; i++) {
                    hourEndMap.put(i, true);
                    useEndHourStr += i + ",";
                }
                outTours = 23;
                useEndHourStr = useEndHourStr.substring(0, useEndHourStr.length() - 1);
            }

            for (int i = inHours; i <= outTours; i++) {
                hourStarMap.put(i, true);
                useStarHourStr += i + ",";
            }
            useStarHourStr = useStarHourStr.substring(0, useStarHourStr.length() - 1);

            // 验证当前时段是否被租用
            List<HourRoomDayUse> roomHourUse = hourRoomMap.get(regist.getRoomNumId());

            // 说明当前时间没有
            if (roomHourUse == null || roomHourUse.size() < 1) {

                HourRoomDayUse hourRoomDayUse = getHourRoomDayUse(regist);
                hourRoomDayUse.setBusinessDay(businessDayMin);
                hourRoomDayUse.setUseMsg(useStarHourStr);

                addHourUse.add(hourRoomDayUse);

                // 如果是隔一天
                if (starEndOneDay) {

                    HourRoomDayUse hourRoomDayUseEnd = getHourRoomDayUse(regist);
                    hourRoomDayUseEnd.setBusinessDay(businessDayMax);
                    hourRoomDayUseEnd.setUseMsg(useEndHourStr);

                    addHourUse.add(hourRoomDayUseEnd);

                }

            } else {

                // 验证当天是否存在
                Map<Integer, HourRoomDayUse> hourRoomDayUseMap = roomHourUse.stream().collect(Collectors.toMap(HourRoomDayUse::getBusinessDay, a -> a, (k1, k2) -> k1));

                HourRoomDayUse hourRoomDayUse = hourRoomDayUseMap.get(businessDayMin);
                if (hourRoomDayUse == null) {

                    hourRoomDayUse = getHourRoomDayUse(regist);
                    hourRoomDayUse.setBusinessDay(businessDayMin);
                    hourRoomDayUse.setUseMsg(useStarHourStr);

                    addHourUse.add(hourRoomDayUse);

                } else {


                    String[] split = hourRoomDayUse.getUseMsg().split(",");
                    Arrays.sort(split);

                    Boolean noRoom = false;

                    String errMsg = "";

                    for (int ion = 0; ion < split.length; ion++) {

                        int i1 = Integer.parseInt(split[ion]);

                        Boolean aBoolean = hourStarMap.get(i1);
                        if (aBoolean != null && aBoolean) {
                            noRoom = true;
                            errMsg += split[ion];
                            errMsg += ",";
                        }
                    }

                    if (noRoom && user.getSessionType() == 3) {
                        throw new Exception("房间:" + regist.getRoomNum() + "在" + HotelUtils.businessDay2Str(businessDayMin) + "的" + errMsg + "时不可预订");
                    }

                    String newUseMsg = hourRoomDayUse.getUseMsg() + "," + useStarHourStr;
                    hourRoomDayUse.setUseMsg(newUseMsg);

                    upaHourUse.add(hourRoomDayUse);

                }


                // 如果是隔一天
                if (starEndOneDay) {

                    HourRoomDayUse hourRoomDayUseEnd = hourRoomDayUseMap.get(businessDayMax);

                    if (hourRoomDayUseEnd == null) {
                        hourRoomDayUseEnd = getHourRoomDayUse(regist);
                        hourRoomDayUseEnd.setBusinessDay(businessDayMax);
                        hourRoomDayUseEnd.setUseMsg(useEndHourStr);

                        addHourUse.add(hourRoomDayUseEnd);
                    } else {

                        String[] split = hourRoomDayUseEnd.getUseMsg().split(",");
                        Arrays.sort(split);

                        Boolean noRoom = false;

                        String errMsg = "";

                        for (int ion = 0; ion < split.length; ion++) {

                            int i1 = Integer.parseInt(split[ion]);

                            Boolean aBoolean = hourStarMap.get(i1);
                            if (aBoolean != null && aBoolean) {
                                noRoom = true;
                                errMsg += split[ion];
                                errMsg += ",";
                            }
                        }

                        if (noRoom && user.getSessionType() == 3) {
                            throw new Exception(regist.getRoomNum() + businessDayMin + "：" + errMsg + "时段被预订");
                        }

                        String newUseMsg = hourRoomDayUseEnd.getUseMsg() + "," + useEndHourStr;
                        hourRoomDayUseEnd.setUseMsg(newUseMsg);

                        upaHourUse.add(hourRoomDayUse);


                    }

                }
            }

            Double sumPrice = overStayRequest.getPrice() * 100;

            // 入账
            Account account = new Account(user);
            account.setHid(regist.getHid());
            account.setGroupAccount(0);
            account.setRegistId(regist.getRegistId());
            account.setRegistState(0);
            account.setIsCancel(0);
            account.setRoomInfoId(regist.getRoomNumId());
            account.setRoomNum(regist.getRoomNum());
            account.setRoomTypeId(regist.getRoomTypeId());
            account.setPayType(1);
            account.setPayClassId(10);
            account.setPayClassName("客房");
            account.setPayCodeId("0007");
            account.setPayCodeName("钟点房费");
            account.setRemark("续住加收房费");
            account.setPrice(sumPrice.intValue());
            account.setAccountType(1);
            account.setUintPrice(sumPrice.intValue());

            if (addHourUse.size() > 0) {

                for (HourRoomDayUse hourRoomDayUse : addHourUse) {
                    if (hourRoomDayUse.getRoomInfoId() == null || hourRoomDayUse.getRoomNo() == null) {
                        continue;
                    }
                    hourRoomDayUseDao.insert(hourRoomDayUse);

                }

            }

            if (upaHourUse.size() > 0) {
                for (HourRoomDayUse hourRoomDayUse : upaHourUse) {

                    hourRoomDayUseDao.update(hourRoomDayUse);

                }
            }


            regist.setCheckoutTime(newDates);
            registInfo.setCheckoutTime(newDates);
            if (regist.getDayCount() == null) {
                regist.setDayCount(1);
                registInfo.setDayCount(1);
            }

            //
            int differHour = HotelUtils.getDifferHour(regist.getCheckinTime(), newDates);

            regist.setDayCount(differHour);
            registInfo.setDayCount(differHour);

            stringStringHashMap.put("newDate", HotelUtils.parseDate2Str(regist.getCheckoutTime()));

            oprecord.setDescription("续住：" + overStayRequest.getDayCount() + "天。新的离店时间:" + HotelUtils.parseDate2Str(regist.getCheckoutTime()));
            oprecords.add(oprecord);


            RegistStayover rs = new RegistStayover();
            rs.setRegistId(regist.getRegistId());
            rs.setBookingOrderId(regist.getBookingOrderId());
            rs.setDayCount(overStayRequest.getDayCount());
            rs.setRoomNum(regist.getRoomNum());
            rs.setRoomTypeId(regist.getRoomTypeId());
            rs.setRoomTypeName(regist.getRoomTypeName());
            rs.setStartTime(checkoutTime);
            rs.setEndTime(regist.getCheckoutTime());
            rs.setNprices(overStayRequest.getPrice().toString());
            rs.setDescRemark("钟点续住");
            rs.setState(1);
            rs.setTotalPrice(sumPrice.intValue());

            rs.setHid(user.getHid());
            rs.setHotelGroupId(user.getHotelGroupId());
            rs.setClassId(user.getClassId());
            rs.setBusinessDay(user.getBusinessDay());
            rs.setBusinessDay(user.getBusinessMonth());
            rs.setBusinessYear(user.getBusinessYear());
            rs.setCreateUserId(user.getUserId());
            rs.setCreateUserName(user.getUserName());
            rs.setCreateTime(new Date());
            rs.setUpdateUserId(user.getUserId());
            rs.setUpdateUserName(user.getUserName());
            rs.setUpdateTime(new Date());
            rs.setType(1);

            // 班次6 说明自助机开房
            if (regist.getClassId() == 6) {
                rs.setTypeState(4);
            } else {
                rs.setTypeState(3);
            }

            String no = OrderNumUtils.getNo(regist.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);

            account.setAccountId(no);

            overStayTransactionService.overStayHourTransaction(registInfo, account, rs, addHourUse, upaHourUse);

            this.addOprecords(oprecords);

            // 更新房间缓存
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        baseService.push(user.getHotelGroupId(), user.getHid(), 17, stringStringHashMap, stringStringHashMap, true, true);

                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });


            //2022-02-09 添加续住信息
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                        registPersonSearch.setRegistId(regist.getRegistId());
                        List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
                        if (registPersonList != null && registPersonList.get(0).getPhone() != null && registPersonList.get(0).getPhone().length() == 11) {
                            JSONObject postData = new JSONObject();
                            postData.put("sessionToken", sessionToken);
                            JSONObject hotelBaseInfo = JSONObject.fromObject(baseService.getHotelBaseInfo(postData));
                            JSONObject data = hotelBaseInfo.getJSONObject("data");
                            String hotelName = data.getString("hotelName");
                            String telephone = data.getString("telephone");
                            String addr = data.getString("addr");
                            //尊敬的{1}，您好，欢迎您入住{2}，您续住的房间号是{3}，退房时间为{4},如有疑问请拨打酒店电话：{5}
                            final ArrayList<String> strings = new ArrayList<>();
                            strings.add(registPersonList.get(0).getPersonName());
                            strings.add(hotelName);
                            strings.add(regist.getRoomNum());
                            strings.add(HotelUtils.parseDate2Str(regist.getCheckoutTime()));
                            strings.add(telephone);
                            SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                            smsHotelSendRecordRequest.setLocationId(SMS_LOC.STAY_OVER);
                            smsHotelSendRecordRequest.setSessionToken(user.getSessionId());
                            smsHotelSendRecordRequest.setPhoneNumber(registPersonList.get(0).getPhone());
                            smsHotelSendRecordRequest.setParams(strings);
                            baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);
                        }
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    //如果是智能门锁则调用
                    SmartLockRequest smartLockRequest = new SmartLockRequest();
                    smartLockRequest.setSessionToken(sessionToken);
                    smartLockRequest.setCheckinTime(regist.getCheckinTime());
                    smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                    smartLockRequest.setRoomTypeId(regist.getRoomTypeId());
                    smartLockRequest.setRoomTypeName(regist.getRoomTypeName());
                    smartLockRequest.setRoomNumId(regist.getRoomNumId());
                    smartLockRequest.setRoomNum(regist.getRoomNum());
                    smartLockRequest.setHid(regist.getHid());
                    smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                    smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                    smartLockRequest.setCheckinType(regist.getCheckinType());
                    smartLockRequest.setLockNo(regist.getSessionToken());
                    RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                    registPersonSearch.setRegistId(regist.getRegistId());
                    registPersonSearch.setRegistState(0);
                    List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                    List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
                    for (int j = 0; j < registPeople.size(); j++) {
                        RegistPerson registPersonInfo = registPeople.get(j);
                        SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                        BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                        peoplesList.add(registPersonDetail);
                    }
                    smartLockRequest.setPeoples(peoplesList);
                    baseService.SmartLockStayOver(smartLockRequest);
                }
            });

            ArrayList<OprecordInfoRequest> oprecordInfoRequests = new ArrayList<>();


            OprecordInfoRequest oprecordInfoRequest = new OprecordInfoRequest();
            oprecordInfoRequest.setSessionToken(sessionToken);
            JSONObject data = new JSONObject();
            data.put("roomNo", regist.getRoomNum());
            data.put("time", HotelUtils.currentTime());
            data.put("oldTime", stringStringHashMap.get("oldDate").toString());
            data.put("newTime", HotelUtils.parseDate2Str(regist.getCheckoutTime()));

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    public HourRoomDayUse getHourRoomDayUse(Regist regist) {
        HourRoomDayUse hourRoomDayUse = new HourRoomDayUse();
        hourRoomDayUse.setRoomNo(regist.getRoomNum());
        hourRoomDayUse.setRoomInfoId(regist.getRoomNumId());
        hourRoomDayUse.setRoomTypeId(regist.getRoomTypeId());
        hourRoomDayUse.setHid(regist.getHid());
        hourRoomDayUse.setHotelGroupId(regist.getHotelGroupId());
        return hourRoomDayUse;
    }

    @Override
    public ResponseData overStayList(OverStayListRequest overStayListRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            final TbUserSession user = this.getTbUserSession(overStayListRequest);

            String regIds = "";

            Integer[] registIds = overStayListRequest.getRegistIds();

            if (registIds == null || registIds.length < 1) {
                throw new Exception("请选择订单信息");
            }


            for (Integer re : registIds) {
                regIds += re + ",";
            }
            regIds = regIds.substring(0, regIds.length() - 1);

            // 登记单信息
            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setRegistIds(regIds);
            List<Regist> regists = registDao.selectBySearch(registSearch);

            String dateHms = HotelUtils.parseDate2Str(regists.get(0).getCheckoutTime()).substring(10, 18);

            // 新的离店时间
            String checkoutTime = overStayListRequest.getCheckoutTime() + dateHms;

            Date checkoutDate = HotelUtils.parseStr2Date(checkoutTime);

            Integer checkoutInt = Integer.parseInt(checkoutTime.substring(0, 10).replace("-", ""));

            Date date = new Date();

            if (date.getTime() > checkoutDate.getTime()) {
                throw new Exception("续住时间不能小于当前时间");
            }

            // 价格信息
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setRegistIds(regIds);
            //批量续住，如果开房产生了房费则无法
//            bookingOrderDailyPriceSearch.setDailyState(1);
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, List<BookingOrderDailyPrice>> priceMap = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getRegistId));

            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistIds(regIds);
            registPersonSearch.setHid(user.getHid());
            registPersonSearch.setRegistState(0);
            Page<RegistPerson> registPeople = registPersonDao.searchRegistPersonList(registPersonSearch);

            Map<Integer, List<RegistPerson>> registPeopleMap = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));

            ArrayList<BookingOrderDailyPrice> addPrice = new ArrayList<>();
            ArrayList<BookingOrderDailyPrice> delPrice = new ArrayList<>();
            ArrayList<Regist> upaRegist = new ArrayList<>();

            // 日期差
            HashMap<String, List<String>> dateMap = new HashMap();

            // 日期差
            HashMap<String, HashMap<Integer, Integer>> canUseRoomMap = new HashMap();

            // 价格
            Boolean coRe = true;

            final HashMap<String, String> stringStringHashMap = new HashMap<>();

            String roomNos = "";

            ArrayList<Oprecord> oprecords = new ArrayList<>();


            for (Regist regist : regists) {

                Integer state = regist.getState();

                if (state != 0) {
                    continue;
                }

                Date regCheckout = regist.getCheckoutTime();

                String regCheckoutStr = HotelUtils.parseDate2Str(regCheckout).substring(0, 10);

                Integer regCheckoutInt = Integer.parseInt(regCheckoutStr.replace("-", ""));

                // 新离店时间与当前离店时间一致，则不处理
                if (checkoutInt.equals(regCheckoutInt)) {
                    continue;
                }
                coRe = false;


                String dateKey = regCheckoutInt + "-" + checkoutInt;

                List<String> days = dateMap.get(dateKey);

                if (days == null) {
                    days = HotelUtils.getAllDayListBetweenDate(regCheckoutStr, checkoutTime.substring(0, 10));

                    dateMap.put(dateKey, days);

                }

                regist.setCheckoutTime(checkoutDate);
                regist.setDayCount(days.size());
                regist.setBusinessDay(checkoutInt);

                roomNos += regist.getRoomNum() + " ";

                Oprecord oprecord = new Oprecord(user);
                oprecord.setOccurTime(HotelUtils.currentTime());
                oprecord.setRegistId(regist.getRegistId());
                oprecord.setRoomNum(regist.getRoomNum());
                oprecord.setDescription("批量续住，新离店时间为：" + checkoutTime);
                oprecords.add(oprecord);

                List<BookingOrderDailyPrice> priceList = priceMap.get(regist.getRegistId());

                Map<Integer, BookingOrderDailyPrice> priceRegMap = priceList.stream().collect(Collectors.toMap(BookingOrderDailyPrice::getDailyTime, a -> a, (k1, k2) -> k2));

                // 如果离店日期小于,删除对应房价，修改登记单信息
                if (checkoutInt < regCheckoutInt) {

                    for (BookingOrderDailyPrice price : priceList) {

                        if (price.getDailyState() == 0) {
                            continue;
                        }

                        if (price.getDailyTime() < checkoutInt) {
                            continue;
                        }

                        delPrice.add(price);

                    }

                    upaRegist.add(regist);

                    continue;

                }

                // 如果离店时间大于退房时间
                HashMap<Integer, Integer> canUseRoom = canUseRoomMap.get(dateKey);

                if (canUseRoom == null) {

                    // 获取未来房情信息
                    JSONObject jsonParam = new JSONObject();
                    jsonParam.put("type", 2);
                    jsonParam.put("startTime", regCheckoutStr);
                    jsonParam.put("endTime", checkoutTime.substring(0, 10));
                    jsonParam.put(ER.SESSION_TOKEN, user.getSessionId());

                    Map<String, Object> findavailableRoom = turnAlwaysService.findavailableRoom(jsonParam);
                    // 可用房数量
                    canUseRoom = (HashMap<Integer, Integer>) findavailableRoom.get("canUseRoomNum");

                }

                Integer roomTypeId = regist.getRoomTypeId();
                Integer roomTypeIdFu = roomTypeId * -1;

                Integer useNum = canUseRoom.get(roomTypeIdFu);
                if (useNum == null) {
                    useNum = 0;
                }

                useNum = useNum + 1;

                Integer num = canUseRoom.get(roomTypeId);

                if (num != null && useNum > num) {

                    throw new Exception(regist.getRoomTypeName() + "可用数量不足。");

                }

                BookingOrderDailyPrice bookingOrderDailyPrice = priceList.get(priceList.size() - 1);

                for (String day : days) {

                    Integer dayInt = Integer.parseInt(day.replace("-", ""));

                    BookingOrderDailyPrice bod = priceRegMap.get(dayInt);
                    if (bod != null) {
                        continue;
                    }

                    BookingOrderDailyPrice peoPrice = new BookingOrderDailyPrice();
                    BeanUtils.copyProperties(bookingOrderDailyPrice, peoPrice);
                    peoPrice.setId(null);
                    peoPrice.setDailyState(1);
                    peoPrice.setDailyTime(dayInt);

                    addPrice.add(peoPrice);

                }

                canUseRoom.put(roomTypeIdFu, useNum);
                canUseRoomMap.put(dateKey, canUseRoom);
            }


            if (coRe) {
                throw new Exception("续住房间状态不正确");
            }


            overStayTransactionService.overStayListTransaction(regists, addPrice, delPrice);

            this.addOprecords(oprecords);

            stringStringHashMap.put("room", roomNos);
            stringStringHashMap.put("oldDate", "");
            stringStringHashMap.put("newDate", checkoutTime);


            // 更新房间缓存
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        baseService.push(user.getHotelGroupId(), user.getHid(), 17, stringStringHashMap, stringStringHashMap, true, true);

                    } catch (Exception e) {

                    }
                }
            });


            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    String sessionToken = overStayListRequest.getSessionToken();

                    for (Regist regist : regists) {
                        //如果是智能门锁则调用
                        SmartLockRequest smartLockRequest = new SmartLockRequest();
                        smartLockRequest.setSessionToken(sessionToken);
                        smartLockRequest.setCheckinTime(regist.getCheckinTime());
                        smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                        smartLockRequest.setRoomTypeId(regist.getRoomTypeId());
                        smartLockRequest.setRoomTypeName(regist.getRoomTypeName());
                        smartLockRequest.setRoomNumId(regist.getRoomNumId());
                        smartLockRequest.setRoomNum(regist.getRoomNum());
                        smartLockRequest.setHid(regist.getHid());
                        smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                        smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                        smartLockRequest.setCheckinType(regist.getCheckinType());
                        smartLockRequest.setLockNo(regist.getSessionToken());
                        RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                        registPersonSearch.setRegistId(regist.getRegistId());
                        registPersonSearch.setRegistState(0);
                        List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                        List<RegistPerson> registPeople = registPeopleMap.get(regist.getRegistId());
                        for (int j = 0; j < registPeople.size(); j++) {
                            RegistPerson registPersonInfo = registPeople.get(j);
                            SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                            BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                            peoplesList.add(registPersonDetail);
                        }
                        smartLockRequest.setPeoples(peoplesList);
                        baseService.SmartLockStayOver(smartLockRequest);
                    }

                }
            });

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData overStayOrder(OverStayOrderRequest overStayOrderRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = overStayOrderRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Regist regist = registDao.selectById(overStayOrderRequest.getRegistId());

            if (regist == null || !regist.getHid().equals(user.getHid())) {
                throw new Exception("未查到订单信息");
            }

            if (regist.getState() != 0) {
                throw new Exception("当前订单状态不允许续住");
            }

            // 新的预订单信息
            BookingOrderRoomNum bookingOrderRoomNum = bookingOrderRoomNumDao.selectById(overStayOrderRequest.getBookingOrderRoomId());

            if (bookingOrderRoomNum == null || !bookingOrderRoomNum.getHid().equals(user.getHid())) {
                throw new Exception("预订单信息有误");
            }

            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderRoomNum.getBookingOrderId());


            //获取预订单的状态
            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);
            BookingOrderConfig bookingOrderConfig = bookingOrderConfigs.get(0);
            bookingOrderConfig.setRegistId(regist.getRegistId());

            bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setRegistId(regist.getRegistId());
            bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);
            BookingOrderConfig registConfig = bookingOrderConfigs.get(0);
            registConfig.setAutoAr(bookingOrderConfig.getAutoAr());

            Integer rowRoom = bookingOrderRoomNum.getRowRoom();
            RoomInfo roomInfo = new RoomInfo();
            RoomInfo oldRoomInfo = null;
            RegistChangeRecord registChangeRecord = null;
            // 换房号 不同房型换单续住
            if (rowRoom == 1 && overStayOrderRequest.getChangeRoom() == 1) {
                roomInfo = roomInfoDao.selectById(bookingOrderRoomNum.getRoomNumId());
                if (roomInfo.getRoomNumState() != ROOM_STATUS.VC) {
                    throw new Exception("当前房间状态不允许入住");
                }
                roomInfo.setRoomNumState(ROOM_STATUS.OCC);
                //如果更换了房间记录下之前房间的信息
                oldRoomInfo = new RoomInfo();
                oldRoomInfo.setRoomNumState(ROOM_STATUS.VD);
                oldRoomInfo.setRoomInfoId(regist.getRoomNumId());
                //把之前的房间状态设置成脏房
                /**
                 * 2021-03-28 插入换房记录表
                 */
                registChangeRecord = new RegistChangeRecord();
                BaseInfoUtils.setBaseInfo(user, registChangeRecord);
                //换房
                registChangeRecord.setType(1);
                registChangeRecord.setCheckinTime(regist.getCheckinTime());
                registChangeRecord.setCheckoutTime(regist.getCheckoutTime());
                registChangeRecord.setOldRoomId(regist.getRoomNumId());
                registChangeRecord.setOldRoomNum(regist.getRoomNum());
                registChangeRecord.setNewRoomId(roomInfo.getRoomInfoId());
                registChangeRecord.setNewRoomNum(roomInfo.getRoomNum());
                registChangeRecord.setOldRoomTypeId(regist.getRoomTypeId());
                registChangeRecord.setOldRoomTypeName(regist.getRoomTypeName());
                registChangeRecord.setNewRoomTypeId(roomInfo.getRoomTypeId());
                registChangeRecord.setNewRoomTypeName(roomInfo.getRoomTypeName());
                registChangeRecord.setRegistId(regist.getRegistId());
                registChangeRecord.setOldRoomPrice(0);
                registChangeRecord.setNewRoomPrice(0);
                registChangeRecord.setRemark("换单续住换房");
            } else {
                roomInfo = roomInfoDao.selectById(regist.getRoomNumId());
            }


            regist.setRoomNum(roomInfo.getRoomNum());
            regist.setRoomNumId(roomInfo.getRoomInfoId());
            bookingOrderRoomNum.setRoomNum(roomInfo.getRoomNum());
            bookingOrderRoomNum.setRoomNumId(roomInfo.getRoomInfoId());
            Date date = new Date();
            // 查询老的预订信息
            BookingOrder oldBookOrder = new BookingOrder();
            oldBookOrder.setBookingOrderId(-1);

            BookingOrderRoomNum oldBookRoom = new BookingOrderRoomNum();
            oldBookRoom.setId(-1);

            if (regist.getBookingOrderId() != null && regist.getBookingOrderId() > 0) {

                oldBookOrder = bookingOrderDao.selectById(regist.getBookingOrderId());

                BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                bookingOrderRoomNumSearch.setRegistId(regist.getRegistId());
                bookingOrderRoomNumSearch.setBookingOrderId(regist.getBookingOrderId());

                Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

                // 老预订信息改为以结账
                if (bookingOrderRoomNums != null && bookingOrderRoomNums.size() > 0) {

                    oldBookRoom = bookingOrderRoomNums.get(0);
                    oldBookRoom.setIsCheckout(1);

                } else {
                    oldBookOrder = new BookingOrder();
                    oldBookOrder.setBookingOrderId(-1);
                    oldBookRoom = new BookingOrderRoomNum();
                    oldBookRoom.setId(-1);
                }

            }

            // 处理老的预订单状态
            if (oldBookOrder.getBookingOrderId() > 0) {
                BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                bookingOrderRoomNumSearch.setBookingOrderId(oldBookOrder.getBookingOrderId());
                bookingOrderRoomNumSearch.setIsCheckout(0);
                Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
                // 如果未结的房间列表等于1 则说明只有当前房间未结账，把订单改为入住完成，非则不改预订信息
                if (bookingOrderRoomNums.size() == 1) {
                    oldBookOrder.setOrderStatus(6);
                } else {
                    oldBookOrder = new BookingOrder();
                    oldBookOrder.setBookingOrderId(-1);
                    oldBookRoom = new BookingOrderRoomNum();
                    oldBookRoom.setId(-1);
                }
            }

            //之前的离店时间
            Date checkoutTime = regist.getCheckoutTime();
            //新的离店时间
            Date newCheckout = bookingOrderRoomNum.getCheckoutTime();

            Integer checkBusinessDay = HotelUtils.parseDate2Int(newCheckout);

            // 查询当前预订单的价格信息
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setBookingOrderRoomNumId(overStayOrderRequest.getBookingOrderRoomId());
            Page<BookingOrderDailyPrice> bookPrice = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            ArrayList<BookingOrderDailyPrice> addPriceList = new ArrayList<>();
            String nprices = "";
            Integer sumPrice = 0;
            //新预订单价格处理
            for (int i = 0; i < bookPrice.size(); i++) {
                BookingOrderDailyPrice bookingOrderDailyPrice = bookPrice.get(i);
                bookingOrderDailyPrice.setRegistId(regist.getRegistId());
                bookingOrderDailyPrice.setReasonName("换单续住");
                bookingOrderDailyPrice.setCompanyId(bookingOrder.getCompanyId());
                bookingOrderDailyPrice.setCompanyAccountId(bookingOrder.getCompanyAccountId());
                bookingOrderDailyPrice.setRoomNumId(roomInfo.getRoomInfoId());
                nprices += bookingOrderDailyPrice.getPrice() + ",";
                sumPrice += bookingOrderDailyPrice.getPrice();
                addPriceList.add(bookingOrderDailyPrice);
            }

            //  修改登记单信息
            if (regist.getDayCount() == null) {
                regist.setDayCount(1);
            }
            regist.setDayCount(regist.getDayCount() + bookPrice.size());
            regist.setCheckoutBusinessDay(checkBusinessDay);
            //设置新的离店时间
            regist.setCheckoutTime(newCheckout);
            regist.setResourceId(bookingOrder.getResourceId());
            regist.setResourceName(HotelUtils.getResourceName(regist.getResourceId()));
            //设置协议信息
            if (bookingOrder.getResourceId() > 2) {
                regist.setCompanyAccountId(bookingOrder.getCompanyAccountId());
                regist.setCompanyId(bookingOrder.getCompanyId());
                regist.setCompayName(bookingOrder.getCompanyName());
            } else if (bookingOrder.getResourceId() == 2) {
                regist.setMemberCard(bookingOrder.getCardNo());
                regist.setMemberId(bookingOrder.getCardId());
            }
            //设置人员的离店时间
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(regist.getRegistId());
            registPersonSearch.setRegistState(HOTEL_CONST.REGIST_STATE_IN);
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
            for (int i = 0; i < registPeople.size(); i++) {
                registPeople.get(i).setRegistId(regist.getRegistId());
                registPeople.get(i).setRoomNum(regist.getRoomNum());
                registPeople.get(i).setRoomNumId(regist.getRoomNumId());
            }

            // 修改新的预订单信息  查询当前为在主的订单数量，如果是1 则订单改为全部入住 否则改为部分入驻
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            bookingOrderRoomNumSearch.setIsCheckin(0);
            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
            if (bookingOrderRoomNums.size() == 1) {
                bookingOrder.setOrderStatus(4);
            } else {
                bookingOrder.setOrderStatus(3);
            }
            bookingOrderRoomNum.setRegistId(regist.getRegistId());
            bookingOrderRoomNum.setIsCheckin(1);

            //如果之前预订单排过房间号，则清楚
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();
            // 判断排房信息
            if (bookingOrderRoomNum.getRoomNumId() > 0) {
                RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
                roomAuxiliaryRelationSearch.setHid(user.getHid());
                roomAuxiliaryRelationSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
                roomAuxiliaryRelationSearch.setRoomId(bookingOrderRoomNum.getRoomNumId());
                roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);
            }

            // 添加续住登记记录
            RegistStayover registStayover = new RegistStayover();
            registStayover.setRegistId(regist.getRegistId());
            registStayover.setBookingOrderId(regist.getBookingOrderId());
            registStayover.setDayCount(bookingOrder.getDayCount());
            registStayover.setRoomNum(regist.getRoomNum());
            registStayover.setRoomTypeId(regist.getRoomTypeId());
            registStayover.setRoomTypeName(regist.getRoomTypeName());
            registStayover.setStartTime(regist.getCheckoutTime());
            registStayover.setEndTime(checkoutTime);
            registStayover.setNprice(bookPrice.get(0).getPrice());
            registStayover.setDescRemark("换单续住");
            registStayover.setState(0);
            registStayover.setNprices(nprices);
            registStayover.setTotalPrice(sumPrice);
            registStayover.setHid(user.getHid());
            registStayover.setHotelGroupId(user.getHotelGroupId());
            registStayover.setType(2);
            registStayover.setOlBookingOrderId(bookingOrder.getBookingOrderId());
            registStayover.setOlBookingOrderRoomNumId(bookingOrderRoomNum.getRoomNumId());
            registStayover.setClassId(user.getClassId());
            registStayover.setBusinessYear(user.getBusinessYear());
            registStayover.setBusinessMonth(user.getBusinessMonth());
            registStayover.setBusinessYear(user.getBusinessYear());
            registStayover.setCreateTime(date);
            registStayover.setCreateUserId(user.getUserId());
            registStayover.setCreateUserName(user.getUserName());
            registStayover.setUpdateTime(date);
            registStayover.setUpdateUserId(user.getUserId());
            registStayover.setUpdateUserName(user.getUserName());
            registStayover.setBookingOrderId(bookingOrder.getBookingOrderId());

            // 班次6 说明自助机开房
            if (regist.getClassId() == 6) {
                registStayover.setTypeState(4);
            } else {
                registStayover.setTypeState(3);
            }

            overStayTransactionService.overStayTransactionOrder(regist, addPriceList, oldBookOrder, bookingOrder, oldBookRoom, bookingOrderRoomNum, registConfig, registStayover, roomAuxiliaryRelations, registPeople, registChangeRecord, roomInfo, oldRoomInfo);
            final HashMap<String, String> stringStringHashMap = new HashMap<>();
            stringStringHashMap.put("room", regist.getRoomNum());
            stringStringHashMap.put("oldDate", HotelUtils.parseDate2Str(checkoutTime));
            stringStringHashMap.put("newDate", HotelUtils.parseDate2Str(newCheckout));
            // 更新房间缓存
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        baseService.push(user.getHotelGroupId(), user.getHid(), 17, stringStringHashMap, stringStringHashMap, true, true);

                    } catch (Exception e) {

                    }
                }
            });


            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    //如果是智能门锁则调用
                    SmartLockRequest smartLockRequest = new SmartLockRequest();
                    smartLockRequest.setSessionToken(sessionToken);
                    smartLockRequest.setCheckinTime(regist.getCheckinTime());
                    smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                    smartLockRequest.setRoomTypeId(regist.getRoomTypeId());
                    smartLockRequest.setRoomTypeName(regist.getRoomTypeName());
                    smartLockRequest.setRoomNumId(regist.getRoomNumId());
                    smartLockRequest.setRoomNum(regist.getRoomNum());
                    smartLockRequest.setHid(regist.getHid());
                    smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                    smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                    smartLockRequest.setCheckinType(regist.getCheckinType());
                    smartLockRequest.setLockNo(regist.getSessionToken());
                    baseService.SmartLockStayOver(smartLockRequest);
                }
            });

            Oprecord oprecord = new Oprecord(user);
            oprecord.setSourceValue(oldBookOrder.getBookingOrderId() + "");
            oprecord.setChangedValue(bookingOrder.getBookingOrderId() + "");
            oprecord.setMainId(regist.getSn());
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setRoomNum(regist.getRoomNum());
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription("换单续住（预订人：" + bookingOrder.getBookingName() + "，订单号：" + bookingOrder.getSn() + "，手机号：" + bookingOrder.getBookingPhone() + "），离店时间由" + HotelUtils.parseDate2Str(checkoutTime) + " 更改为 " + HotelUtils.parseDate2Str(newCheckout));
            oprecord.setType(HOTEL_CONST.LOG_HDXZ);
            this.addOprecords(oprecord);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
