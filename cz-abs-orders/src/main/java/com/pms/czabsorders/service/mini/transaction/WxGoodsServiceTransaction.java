package com.pms.czabsorders.service.mini.transaction;

import com.pms.czaccount.bean.account.Account;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmswarehouse.bean.GoodsDumb;
import com.pms.pmswarehouse.bean.GoodsSaleRecord;
import com.pms.pmswarehouse.bean.GoodsShoppingOrder;

import java.util.List;

public interface WxGoodsServiceTransaction {

    public Integer addGoodsToRegist(Regist regist, List<Account> addAccounts, GoodsShoppingOrder goodsShoppingOrder,List<GoodsSaleRecord> goodsSaleRecords ) throws Exception;

    public Integer addGoodsToLs(GoodsDumb goodsDumb, List<Account> addAccounts, GoodsShoppingOrder goodsShoppingOrder,List<GoodsSaleRecord> goodsSaleRecords ) throws Exception;
}
