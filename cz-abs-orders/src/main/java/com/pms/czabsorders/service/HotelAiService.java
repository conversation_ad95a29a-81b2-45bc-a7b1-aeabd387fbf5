package com.pms.czabsorders.service;

import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.*;

public interface HotelAiService {
    ResponseData dataIndicators(DataIndicatorsReq dataIndicatorsReq);

    ResponseData overviewOrder(OverviewOrderReq overviewOrderReq);

    ResponseData getPriceList(PriceListReq priceListReq);

    ResponseData editRoomTypePrice(EditRoomTypePriceReq editRoomTypePriceReq);

    ResponseData getRoomTypeList(BaseRequest baseRequest);

    ResponseData getOptionalChannel(BaseRequest baseRequest);

    ResponseData synchronousToChannel(SynchronousToChannelReq synchronousToChannelReq);

    ResponseData calender(CalenderReq calenderReq);

    ResponseData calenderDetail(CalenderDetailReq calenderDetailReq);

    ResponseData editPrice(EditPriceReq editPriceReq);

    ResponseData locationInfo(String sessionToken);

    ResponseData editLocationInfo(LocationInfoReq locationInfoReq);
}
