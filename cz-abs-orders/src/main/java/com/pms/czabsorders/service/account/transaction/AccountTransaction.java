package com.pms.czabsorders.service.account.transaction;

import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountCancel;
import com.pms.czaccount.bean.account.AccountThirdPayRecode;
import com.pms.czmembership.bean.company.HotelCompanyAccount;
import com.pms.czmembership.bean.company.HotelCompanyAccountInfo;
import com.pms.czmembership.bean.company.HotelCompanyArRecode;
import com.pms.czmembership.bean.member.*;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;

import java.util.ArrayList;
import java.util.List;

public interface AccountTransaction {
    public void executeFinishMemberFreeze(CardFreezeRecord cardFreezeRecord, CardInfo cardInfo, CardGroupInfo cardGroupInfo, CardOperationRecord cardOperationRecord, Account account) throws Exception;

    public void addAccountTransaction(Account account, AccountThirdPayRecode accountThirdPayRecode, ArrayList<Oprecord> oprecords, Regist regist, BookingOrderDailyPrice bookingOrderDailyPrice) throws Exception;

    public void balanceAccountTransaction(Account account, AccountThirdPayRecode accountThirdPayRecode, Regist regist, BookingOrder bookingOrder, ArrayList<Oprecord> oprecords) throws Exception;

    public void setOffTransaction(ArrayList<Account> accounts, ArrayList<AccountThirdPayRecode> accountThirdPayRecodes, ArrayList<HotelCompanyArRecode> hotelCompanyArRecodes, HotelCompanyAccountInfo hotelCompanyAccountInfo, ArrayList<CardConsumptionRecord> cardConsumptionRecords, ArrayList<CardFreezeRecord> cardFreezeRecords, ArrayList<Oprecord> oprecords, TbUserSession user, Regist regist) throws Exception;

    public void refundMoneyTransaction(Account account, Account refundAccount, ArrayList<Oprecord> oprecords, TbUserSession user, Regist regist, BookingOrder bookingOrder) throws Exception;

    public void autoAddRoomPrice(BookingOrderDailyPrice bookingOrderDailyPrice, Regist regist, RegistPerson registPerson, TbUserSession user) throws Exception;


    // 转账
    public void tranAccountFunc(ArrayList<Account> addAccounts, ArrayList<Account> upaAccounts, ArrayList<AccountCancel> addAccountCancels) throws Exception;

    /**
     * 入AR账
     *
     * @param account
     * @param hotelCompanyArRecode
     * @param hotelCompanyAccount
     * @param hotelCompanyAccountInfo
     * @param oprecords
     */
    public void addAccountForArTransaction(Account account, HotelCompanyArRecode hotelCompanyArRecode, HotelCompanyAccount hotelCompanyAccount, HotelCompanyAccountInfo hotelCompanyAccountInfo, List<Oprecord> oprecords) throws Exception;
}
