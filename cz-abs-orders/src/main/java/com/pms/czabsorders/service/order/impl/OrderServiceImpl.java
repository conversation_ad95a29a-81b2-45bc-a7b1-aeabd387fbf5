package com.pms.czabsorders.service.order.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.*;
import com.pms.czabsorders.feign.OtaFeign;
import com.pms.czabsorders.service.checkin.transaction.CheckInTransactionService;
import com.pms.czabsorders.service.order.OrderService;
import com.pms.czabsorders.service.order.transaction.BookTransactionService;
import com.pms.czabsorders.service.ota.OtaChangePushUtil;
import com.pms.czabsorders.service.turnAlways.TurnAlwaysService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.price.RoomDayPrice;
import com.pms.czhotelfoundation.bean.price.RoomDayPriceShow;
import com.pms.czhotelfoundation.bean.price.RoomRateCodeSpecific;
import com.pms.czhotelfoundation.bean.price.search.RoomDayPriceSearch;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSpecificSearch;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliary;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomType;
import com.pms.czhotelfoundation.bean.room.search.RoomAuxiliaryRelationSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeSearch;
import com.pms.czhotelfoundation.bean.search.HourRoomDayUseSearch;
import com.pms.czhotelfoundation.dao.HourRoomDayUseDao;
import com.pms.czhotelfoundation.dao.price.RoomDayPriceDao;
import com.pms.czhotelfoundation.dao.price.RoomPriceDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeSpecificDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.constant.SMS_LOC;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.room.ROOM_AUXILIARY;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.enums.HmhOrderStatusEnum;
import com.pms.czpmsutils.request.*;
import com.pms.czpmsutils.rsa.RSA;
import com.pms.czpmsutils.view.TodayComingView;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.request.OtaHotelInfoRequest;
import com.pms.pmsorder.bean.request.OtaRoomTypeRequest;
import com.pms.pmsorder.bean.search.*;
import com.pms.pmsorder.bean.view.BookingOrderRoomNumView;
import com.pms.pmsorder.dao.*;
import com.pms.pmsorder.service.RegistService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import java.net.URLDecoder;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
@Primary
@Slf4j
public class OrderServiceImpl extends BaseService implements OrderService {

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;


    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private RoomAuxiliaryDao roomAuxiliaryDao;

    @Autowired
    private AccountDao accountDao;


    @Autowired
    private RoomPriceDao roomPriceDao;

    @Autowired
    private RoomRateCodeSpecificDao roomRateCodeSpecificDao;

    @Autowired
    private BookTransactionService bookTransactionService;

    @Autowired
    private OtaChangePushUtil otaChangePushService;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private TurnAlwaysService turnAlwaysService;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RegistService registService;

    @Autowired
    private RoomTypeDao roomTypeDao;

    @Autowired
    private RoomDayPriceDao roomDayPriceDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private CheckInTransactionService checkInTransactionService;

    @Autowired
    private OtaRoomTypeDao otaRoomTypeDao;

    @Autowired
    private OtaHotelInfoDao otaHotelInfoDao;

    private BaseService baseService = this;

    @Autowired
    private HourRoomDayUseDao hourRoomDayUseDao;
    @Autowired
    private OtaFeign otaFeign;

    @Override
    public JSONObject addBookNew(JSONObject param) {
        JSONObject resultMap = new JSONObject();

        resultMap.put(ER.RES, ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            // 预订单参数
            String bookData = URLDecoder.decode(param.getString("bookData"), "utf-8");
            JSONObject bookJson = JSONObject.fromObject(bookData);

            //生成预订单编号
            String sn = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.BOOK, stringRedisTemplate);

            Date date = new Date();

            BookingOrder book = new BookingOrder();
            book.setSn(sn);
            book.setHotelGroupId(user.getHotelGroupId());
            book.setHid(user.getHid());
            book.setCreateTime(date);
            book.setCreateUserId(user.getUserId());
            book.setCreateUserName(user.getUserName());
            book.setAcceptTime(date);
            book.setRoomCount(bookJson.getInt("roomCount"));
            book.setOrderTime(date);
            book.setOtaStatus(HmhOrderStatusEnum.TO_BE_CONFIRMED.getType());
            String phone = bookJson.getString("bookingPhone");
            if (bookJson.getString("bookingPhone").equals("null")) {
                phone = "";
            }
            book.setBookingPhone(phone);
            book.setBookingName(bookJson.getString("bookingName"));
            if (bookJson.get("bookIdCode") != null) {
                book.setBookingIdCode(bookJson.getString("bookIdCode"));
            }

            book.setCheckinTime(HotelUtils.parseStr2Date(bookJson.getString("checkinTime") + " 00:00:00"));
            book.setCheckoutTime(HotelUtils.parseStr2Date(bookJson.getString("checkoutTime") + " 00:00:00"));
            book.setKeepTime(bookJson.getString("keepTime"));
            //入住天数
            int dayCount = HotelUtils.getAllDayListBetweenDate(bookJson.getString("checkinTime"), bookJson.getString("checkoutTime")).size();

            book.setDayCount(dayCount);
            book.setFromHid(user.getHid());
            if (bookJson.get("otherOrderId") != null) {
                book.setThirdPlatformOrderCode(bookJson.getString("otherOrderId"));
            }


            //客源类型
            int resourceId = bookJson.getInt("resourceId");
            book.setResourceId(resourceId);

            // 1.散客 2.会员
            if (resourceId == 2) {
                JSONObject memberInfo = bookJson.getJSONObject("vipMsg");
                book.setCardId(memberInfo.getInt("cardId"));
                book.setCardNo(memberInfo.getString("cardNo"));
            }
            if (resourceId == 3 || resourceId == 4 || resourceId == 5) {
                JSONObject arMsg = bookJson.getJSONObject("arMsg");

                book.setCompanyId(arMsg.getInt("arId"));
                book.setCompanyName(arMsg.getString("arName"));

                if (bookJson.get("arAntMsg") != null || !"".equals(bookJson.getString("arAntMsg"))) {
                    book.setCompanyAccountId(bookJson.getJSONObject("arAntMsg").getInt("id"));
                }

            }
            book.setFromType(bookJson.getInt("fromType"));

            /**
             *  缺失
             *      total_price 订单总金额
             *      unit_price  单价待补充
             *  待补充
             */
            Double payMoney = 0.0;
            Object payMoneyObj = bookJson.get("payMoney");
            if (payMoneyObj != null && !"".equals(payMoneyObj.toString())) {
                payMoney = bookJson.getDouble("payMoney") * 100;
            }
            book.setPayPrice(payMoney.intValue());

            book.setFromHid(user.getHid());
            if (bookJson.get("fromHid") != null) {
                book.setFromHid(bookJson.getInt("fromHid"));
            }

            //取卡码
            if (bookJson.get("orderCode") != null) {
                book.setOrderCode(bookJson.getString("orderCode"));
            }
            //预订单类型 1.日租 2.钟点 3.长足
            book.setOrderType(1);
            Object orderType = bookJson.get("orderType");
            if (orderType != null) {
                book.setOrderType(Integer.parseInt(orderType.toString()));
            }
            //订单状态 订单状态 1.有效 2.NoShow 3.部分入住 4.全部入住 5.已取消 6.入住完成
            book.setOrderStatus(BOOK.STA_YX);
            Object orderStatus = bookJson.get("orderStatus");
            if (orderStatus != null) {
                book.setOrderStatus(Integer.parseInt(orderStatus.toString()));
            }

            // 订单时间
            book.setOrderYear(user.getBusinessYear());
            book.setOrderYearMonth(user.getBusinessMonth());
            book.setBusinessDay(user.getBusinessDay());
            book.setClassId(user.getClassId());
            book.setCreateTime(date);
            book.setCreateUserId(user.getUserId());
            book.setCreateUserName(user.getUserName());
            book.setUpdateTime(date);
            book.setUpdateUserId(user.getUserId());
            book.setUpdateUserName(user.getUserName());
            book.setRoomTypeSummary(bookJson.getString("roomTypeSummary"));
            book.setRemark(bookJson.get("remark") == null ? "" : bookJson.getString("remark"));
            book.setThirdPlatformOrderCode(bookJson.get("thirdPlatformOrderCode") == null ? "" : bookJson.getString("thirdPlatformOrderCode"));
            if (bookJson.get("rateCodeId") != null) {
                book.setRateCodeId(bookJson.getInt("rateCodeId"));
            }
            if (bookJson.get("rateCodeName") != null) {
                book.setRateCodeName(bookJson.getString("rateCodeName"));
            }
            /**
             * 查询预订单辅助房态
             *  如果当前时间等于 预抵时间，则状态为预抵。
             *      否则 辅助房态为预离。
             */
            RoomAuxiliary auxiliary = new RoomAuxiliary();
            String nowDate = HotelUtils.currentDate();
            if (nowDate.equals(bookJson.getString("checkinTime"))) {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_ARRIVALS);
            } else {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_CREATE);
            }

            // 团队房  isGroup==1则为团队
            RegistGroup registGroup = null;

            int teamType = 0;

            Object teamTypeObj = bookJson.get("teamType");
            if (teamTypeObj != null && !"".equals(teamTypeObj.toString()) && !"null".equals(teamTypeObj.toString())) {
                teamType = Integer.parseInt(teamTypeObj.toString());
            }

            book.setBookTeam(0);

            int isGroup = bookJson.getInt("isGroup");
            if (isGroup == 1) {
                registGroup = new RegistGroup();
                registGroup.setBusinessDay(user.getBusinessDay());
                registGroup.setHid(user.getHid());
                registGroup.setClassId(user.getClassId());
                registGroup.setCreateTime(new Date());
                registGroup.setCreateUserId(user.getUserId());
                registGroup.setCreateUserName(user.getUserName());
                registGroup.setSn(book.getSn());
                registGroup.setPayType(0);
                registGroup.setGroupName(bookJson.getString("groupName"));
                registGroup.setSumRooms(book.getRoomCount());
                registGroup.setUpdateTime(date);
                registGroup.setUpdateUserId(user.getUserId());
                registGroup.setUpdateUserName(user.getUserName());
                registGroup.setState(1);
                registGroup.setGroupType(1);
                registGroup.setSumSales(0);
                registGroup.setSumPay(0);
                registGroup.setTeamType(teamType);
                book.setTeamCodeName(bookJson.getString("groupName"));
                book.setBookTeam(1);
                book.setTeamType(teamType);
            }


            /**
             * 添加操作日志
             */
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);
            oprecord.setMainId(sn);
            oprecord.setType(HOTEL_CONST.LOG_YD);
            oprecord.setBookingOrderId(book.getBookingOrderId());

            StringBuilder desc = new StringBuilder();
            desc.append("创建预订单,订单号:");
            desc.append(sn);
            desc.append(",");
            desc.append("预订人:");
            desc.append(book.getBookingName());
            desc.append(",");
            desc.append("入住日期:");
            desc.append(HotelUtils.parseDate2Str(book.getCheckinTime()).substring(0, 10));
            desc.append("离店日期:");
            desc.append(HotelUtils.parseDate2Str(book.getCheckoutTime()).substring(0, 10));
            oprecord.setDescription(desc.toString());
            oprecord.setBusinessDay(user.getBusinessDay());
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecords.add(oprecord);

            /**
             * 2.添加预订单设置
             */
            BookingOrderConfig bookingOrderConfig = new BookingOrderConfig();
            bookingOrderConfig.setPriceSecrecy(bookJson.getInt("priceSecrecy"));
            bookingOrderConfig.setInfoSecrecy(bookJson.getInt("infoSecrecy"));
            bookingOrderConfig.setAutoCheckin(bookJson.getInt("autoCheckIn"));
            bookingOrderConfig.setNoDeposit(bookJson.getInt("noDposit"));
            bookingOrderConfig.setNoPrice(bookJson.getInt("noPrice"));
            bookingOrderConfig.setContinueRes(bookJson.getInt("continueRes"));
            bookingOrderConfig.setAutoAr(0);
            if (bookJson.get("autoAr") != null) {
                bookingOrderConfig.setAutoAr(bookJson.getInt("autoAr"));
            }
            bookingOrderConfig.setAvePrice(0);
            if (bookJson.get("avePrice") != null) {
                bookingOrderConfig.setAutoAr(bookJson.getInt("avePrice"));
            }


            /**
             *  取出两个时间的日期差
             */
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(bookJson.getString("checkinTime"), bookJson.getString("checkoutTime"));

            // 要添加的辅助房态
            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            /**
             * 3.添加预订房型信息
             */
            JSONArray roomTypeList = bookJson.getJSONArray("roomTypeList");

            BookingOrderDailyPrice bodp = new BookingOrderDailyPrice();

            // 需要添加的房型
            ArrayList<BookingOrderRoomType> addBort = new ArrayList<>();

            for (int i = 0; i < roomTypeList.size(); i++) {
                // 预订房型数据
                JSONObject rtObj = roomTypeList.getJSONObject(i);
                // 房间数据
                JSONArray roomList = rtObj.getJSONArray("roomList");

                // 查询费用
                RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
                roomRateCodeSpecificSearch.setHid(user.getHid());
                roomRateCodeSpecificSearch.setRateId(rtObj.getInt("rateId"));
                roomRateCodeSpecificSearch.setRoomTypeId(rtObj.getInt("roomTypeId"));
                List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);

                RoomRateCodeSpecific roomRateCodeSpecific = new RoomRateCodeSpecific();
                if (!CollectionUtils.isEmpty(roomRateCodeSpecifics)) {
                    roomRateCodeSpecific = roomRateCodeSpecifics.get(0);
                }

                // 创建预订房型信息
                BookingOrderRoomType bort = new BookingOrderRoomType();
                bort.setHid(user.getHid());
                bort.setHotelGroupId(user.getHotelGroupId());
                bort.setArriveTime(bookJson.getString("checkinTime") + " " + book.getKeepTime() + ":00");
                bort.setRoomTypeId(rtObj.getInt("roomTypeId"));
                bort.setRoomTypeNum(rtObj.getInt("num"));
                bort.setHasRoomNum(roomList.size());
                bort.setPriceCodeId(rtObj.getInt("rateId"));
                bort.setPriceCode(rtObj.getString("rateCode"));
                bort.setState(BOOK.STA_YX);
                bort.setOrderState(BOOK.STA_YX);
                bort.setCreateTime(date);
                bort.setCreateUserId(user.getUserId());
                bort.setCreateUserName(user.getUserName());
                bort.setUpdateTime(date);
                bort.setUpdateUserId(user.getUserId());
                bort.setUpdateUserName(user.getUserName());
                bort.setCheckinTime(book.getCheckinTime());
                bort.setCheckoutTime(book.getCheckoutTime());


                //增加预订房型操作日志
                Oprecord oprecord1 = new Oprecord(user);
                oprecord1.setMainId(sn);
                oprecord1.setType(HOTEL_CONST.LOG_YD);
                oprecord1.setRegistId(book.getBookingOrderId());
                oprecord1.setDescription("对订单 : " + sn + "，添加预订房型：" + rtObj.getString("roomTypeName") + ",房间数量: " + bort.getRoomTypeNum() + ",房价码:" + bort.getPriceCode());
                oprecord1.setOccurTime(HotelUtils.currentTime());
                oprecords.add(oprecord1);
                JSONArray   priceList = rtObj.getJSONArray("priceList");

                // 房型添加的房价
                ArrayList<BookingOrderDailyPrice> rtPriceList = new ArrayList<>();

                for (int pl = 0; pl < priceList.size(); pl++) {
                    JSONObject plObj = priceList.getJSONObject(pl);
                    bodp = new BookingOrderDailyPrice();
                    bodp.setBookingOrderRoomNumId(0);
                    bodp.setHid(user.getHid());
                    bodp.setHotelGroupId(user.getHotelGroupId());
                    bodp.setPrice(plObj.getInt("price"));
                    bodp.setDailyTime(Integer.parseInt(plObj.getString("date").replace("-", "")));
                    bodp.setRoomTypeId(bort.getRoomTypeId());
                    bodp.setRoomNumId(0);
                    bodp.setBreakNum(roomRateCodeSpecific.getBreakfastNum());
                    bodp.setDailyState(1);
                    bodp.setRateCodeId(bort.getPriceCodeId());
                    bodp.setRateCode(bort.getPriceCode());
                    bodp.setIsStayover(0);
                    bodp.setCreateTime(date);
                    bodp.setCreateUserId(user.getUserId());
                    bodp.setCreateUserName(user.getUserName());
                    bodp.setUpdateTime(date);
                    bodp.setUpdateUserId(user.getUserId());
                    bodp.setUpdateUserName(user.getUserName());
                    rtPriceList.add(bodp);
                }

                bort.setBookingOrderDailyPrices(rtPriceList);

                // 需要添加的预订房间

                ArrayList<BookingOrderRoomNum> bookingOrderRoomNums = new ArrayList<>();

                for (int k = 0; k < roomList.size(); k++) {

                    JSONObject room = roomList.getJSONObject(k);

                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(book.getBookingOrderId());
                    bookingOrderRoomNum.setBookingOrderRoomTypeId(bort.getId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(bort.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(room.getInt("roomInfoId"));
                    bookingOrderRoomNum.setRoomNum(room.getString("roomNum"));
                    if (room.get("roomCode") != null) {
                        bookingOrderRoomNum.setRoomCode(room.getString("roomCode"));
                    }
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setCheckinTime(book.getCheckinTime());
                    bookingOrderRoomNum.setCheckoutTime(book.getCheckoutTime());
                    bookingOrderRoomNum.setRowRoom(1);
                    bookingOrderRoomNum.setRateCodeId(bort.getPriceCodeId());
                    bookingOrderRoomNum.setRateCode(bort.getPriceCode());
                    bookingOrderRoomNum.setBookingOrderDailyPrices(rtPriceList);


                    //记录操作日志
                    Oprecord oprecord2 = new Oprecord(user);
                    oprecord2.setMainId(sn);
                    oprecord2.setRegistId(book.getBookingOrderId());
                    oprecord2.setDescription("对订单 : " + sn + "，进行排房：" + bookingOrderRoomNum.getRoomNum());
                    oprecord2.setOccurTime(HotelUtils.currentTime());
                    oprecords.add(oprecord2);

                    //添加辅助房态
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(bookingOrderRoomNum.getRoomNumId());
                    roomAuxiliaryRelation.setRoomNum(bookingOrderRoomNum.getRoomNum());
                    roomAuxiliaryRelation.setBookingOrderId(book.getBookingOrderId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(auxiliary.getRoomAuxiliaryId());
                    roomAuxiliaryRelation.setSort(auxiliary.getSort());

                    roomAuxiliaryRelations.add(roomAuxiliaryRelation);

                    bookingOrderRoomNums.add(bookingOrderRoomNum);
                }

                // 添加预订未分房的房间
                for (int k = 0; k < bort.getRoomTypeNum() - bort.getHasRoomNum(); k++) {

                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(book.getBookingOrderId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(bodp.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(0);
                    bookingOrderRoomNum.setRoomNum("0");
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setRateCodeId(bort.getPriceCodeId());
                    bookingOrderRoomNum.setRateCode(bort.getPriceCode());
                    bookingOrderRoomNum.setCheckinTime(book.getCheckinTime());
                    bookingOrderRoomNum.setCheckoutTime(book.getCheckoutTime());
                    bookingOrderRoomNum.setRowRoom(0);
                    bookingOrderRoomNum.setBookingOrderDailyPrices(rtPriceList);
                    bookingOrderRoomNums.add(bookingOrderRoomNum);

                }
                bort.setBookingOrderRoomNums(bookingOrderRoomNums);
                addBort.add(bort);
            }

            Integer bookingOrderId = bookTransactionService.addBookService(book, registGroup, addBort, roomAuxiliaryRelations, oprecords, bookingOrderConfig);

            resultMap.put(ER.RES_DATA, bookingOrderId);
            resultMap.put("orderSn",  book.getSn());

            this.addOprecords(oprecords);
            //推送订单状态变更以及价量态信息
            List<BookingOrderRoomNum> bookingOrderRoomNumList= new ArrayList<>();
            addBort.forEach(bort -> {
                bookingOrderRoomNumList.addAll(bort.getBookingOrderRoomNums());
            });
            otaChangePushService.pushOrderAndNumChange(book,bookingOrderRoomNumList);

            ArrayList<String> params = new ArrayList<>();
            params.add(book.getBookingName());
            params.add(book.getRoomTypeSummary());
            params.add(book.getSn());
            params.add(HotelUtils.parseDate2Str(book.getCheckinTime()).substring(0, 10) + " 14:00");
            params.add(HotelUtils.parseDate2Str(book.getCheckoutTime()).substring(0, 16));
            params.add(user.getHotelName());
            final JSONObject smsParam = new JSONObject();
            smsParam.put(SMS_LOC.PHONE, book.getBookingPhone());
            smsParam.put(SMS_LOC.PARAM, params);


            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {

                        SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                        smsHotelSendRecordRequest.setLocationId(SMS_LOC.ORDER_CREATE);
                        smsHotelSendRecordRequest.setSessionToken(sessionToken);
                        smsHotelSendRecordRequest.setPhoneNumber(smsParam.getString(SMS_LOC.PHONE));


                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();

                        turnAlwaysService.turnAlwaysCacheFunc(user);

                        HashMap<String, String> filed = new HashMap<>();
                        filed.put("sn", book.getSn());
                        filed.put("rtdesc", book.getRoomTypeSummary());
                        HashMap<String, String> dataMap = new HashMap<>();
                        dataMap.put("bookId", book.getBookingOrderId() + "");
                        HashMap<String, String> onClickCbData = new HashMap<>();
                        onClickCbData.put("bookingOrderId", book.getBookingOrderId().toString());
                        baseService.push(user.getHotelGroupId(), user.getHid(), 15, filed, dataMap, true, true, onClickCbData);

                        ArrayList<String> list = (ArrayList<String>) JSONArray.toList(smsParam.getJSONArray(SMS_LOC.PARAM), String.class);
                        smsHotelSendRecordRequest.setParams(list);
                        baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });


        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());

        }
        return resultMap;
    }

    public ResponseData createBookingOrder(CreateBookingOrderRequest createBookingOrderRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = createBookingOrderRequest.getSessionToken();
            TbUserSession user = this.getTbUserSession(sessionToken);
            if (null == user) {
                throw new Exception(HOTEL_CONST.TOKENISNULL);
            }
            if (null != createBookingOrderRequest.getHid() && createBookingOrderRequest.getHid() > 0) {
                user.setHid(createBookingOrderRequest.getHid());
            }

            Date date = new Date();
            //生成预订单编号
            String sn = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.BOOK, stringRedisTemplate);
            BookingOrder book = new BookingOrder();
            book.setSn(sn);
            book.setHotelGroupId(user.getHotelGroupId());
            book.setHid(user.getHid());
            book.setCreateTime(date);
            book.setCreateUserId(user.getUserId());
            book.setCreateUserName(user.getUserName());
            book.setAcceptTime(date);
            book.setRoomCount(createBookingOrderRequest.getRoomCount());
            book.setOrderTime(date);
            String phone = createBookingOrderRequest.getBookingPhone();
            if (phone.equals("null")) {
                phone = "";
            }
            book.setBookingPhone(phone);
            book.setBookingName(createBookingOrderRequest.getBookingName());
            book.setCheckinTime(createBookingOrderRequest.getCheckinTime());
            book.setCheckoutTime(createBookingOrderRequest.getCheckoutTime());
            book.setKeepTime(createBookingOrderRequest.getKeepTime());
            //入住天数
            int dayCount = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(createBookingOrderRequest.getCheckinTime()), HotelUtils.parseDate2Str(createBookingOrderRequest.getCheckoutTime())).size();
            book.setDayCount(dayCount);
            book.setFromHid(user.getHid());
            if (createBookingOrderRequest.getThirdPlatformOrderCode() != null) {
                book.setThirdPlatformOrderCode(createBookingOrderRequest.getThirdPlatformOrderCode());
            }
            //客源类型
            int resourceId = createBookingOrderRequest.getResourceId();
            book.setResourceId(resourceId);
            // 1.散客 2.会员
            if (resourceId == 2) {
                book.setCardId(createBookingOrderRequest.getVipMsg().getCardId());
                book.setCardNo(createBookingOrderRequest.getVipMsg().getCardNo());
            }
            if (resourceId == 3 || resourceId == 4 || resourceId == 5) {
                book.setCompanyId(createBookingOrderRequest.getArMsg().getArId());
                book.setCompanyName(createBookingOrderRequest.getArMsg().getArName());
                book.setCompanyAccountId(createBookingOrderRequest.getArAntMsg().getId());
            }
            book.setFromType(createBookingOrderRequest.getFromType());
            book.setPayPrice(createBookingOrderRequest.getPayPrice());
            book.setTotalPrice(createBookingOrderRequest.getTotalPrice());
            book.setUnitPrice(createBookingOrderRequest.getUnitPrice());
            book.setFromHid(user.getHid());
            //预订单类型 1.日租 2.钟点 3.长租
            book.setOrderType(2);
            book.setOrderStatus(BOOK.STA_YX);
            // 订单时间
            book.setOrderYear(user.getBusinessYear());
            book.setOrderYearMonth(user.getBusinessMonth());
            book.setBusinessDay(user.getBusinessDay());
            book.setClassId(user.getClassId());
            book.setCreateTime(date);
            book.setCreateUserId(user.getUserId());
            book.setCreateUserName(user.getUserName());
            book.setUpdateTime(date);
            book.setUpdateUserId(user.getUserId());
            book.setUpdateUserName(user.getUserName());
            book.setRoomTypeSummary(createBookingOrderRequest.getRoomTypeSummary());
            book.setRemark(createBookingOrderRequest.getRemark());
            book.setRateCodeId(createBookingOrderRequest.getRateCodeId());
            book.setRateCodeName(createBookingOrderRequest.getRateCodeName());

            /**
             * 查询预订单辅助房态
             *  如果当前时间等于 预抵时间，则状态为预抵。
             *      否则 辅助房态为预离。
             */
            RoomAuxiliary auxiliary = new RoomAuxiliary();
            String nowDate = HotelUtils.currentDate();
            if (nowDate.equals(HotelUtils.currentDate(createBookingOrderRequest.getCheckinTime()))) {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_ARRIVALS);
            } else {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_CREATE);
            }

            // 团队房  isGroup==1则为团队
            RegistGroup registGroup = null;
            int teamType = 0;
            Object teamTypeObj = createBookingOrderRequest.getTeamType();
            if (teamTypeObj != null && !"".equals(teamTypeObj.toString()) && !"null".equals(teamTypeObj.toString())) {
                teamType = Integer.parseInt(teamTypeObj.toString());
            }
            book.setBookTeam(0);

            int isGroup = createBookingOrderRequest.getIsGroup();
            if (isGroup == 1) {
                registGroup = new RegistGroup();
                registGroup.setBusinessDay(user.getBusinessDay());
                registGroup.setHid(user.getHid());
                registGroup.setClassId(user.getClassId());
                registGroup.setCreateTime(new Date());
                registGroup.setCreateUserId(user.getUserId());
                registGroup.setCreateUserName(user.getUserName());
                registGroup.setSn(book.getSn());
                registGroup.setPayType(0);
                registGroup.setGroupName(createBookingOrderRequest.getGroupName());
                registGroup.setSumRooms(book.getRoomCount());
                registGroup.setUpdateTime(date);
                registGroup.setUpdateUserId(user.getUserId());
                registGroup.setUpdateUserName(user.getUserName());
                registGroup.setState(1);
                registGroup.setGroupType(1);
                registGroup.setSumSales(0);
                registGroup.setSumPay(0);
                registGroup.setTeamType(teamType);
                book.setTeamCodeName(createBookingOrderRequest.getGroupName());
                book.setBookTeam(1);
                book.setTeamType(teamType);
            }

            /**
             * 添加操作日志
             */
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);
            oprecord.setMainId(sn);
            oprecord.setBookingOrderId(book.getBookingOrderId());
            oprecord.setDescription("创建预订单,订单号:" + sn);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecords.add(oprecord);

            /**
             * 2.添加预订单设置
             */
            BookingOrderConfig bookingOrderConfig = new BookingOrderConfig();
            bookingOrderConfig.setPriceSecrecy(createBookingOrderRequest.getPriceSecrecy());
            bookingOrderConfig.setInfoSecrecy(createBookingOrderRequest.getInfoSecrecy());
            bookingOrderConfig.setAutoCheckin(createBookingOrderRequest.getAutoCheckIn());
            bookingOrderConfig.setNoDeposit(createBookingOrderRequest.getNoDposit());
            bookingOrderConfig.setNoPrice(createBookingOrderRequest.getNoPrice());
            bookingOrderConfig.setContinueRes(createBookingOrderRequest.getContinueRes());
            bookingOrderConfig.setAutoAr(0);
            if (createBookingOrderRequest.getAutoAr() != null) {
                bookingOrderConfig.setAutoAr(createBookingOrderRequest.getAutoAr());
            }
            bookingOrderConfig.setAvePrice(0);
            if (createBookingOrderRequest.getAvePrice() != null) {
                bookingOrderConfig.setAutoAr(createBookingOrderRequest.getAvePrice());
            }


            // 开始时间
            Integer businessDayMin = HotelUtils.parseDate2Int(createBookingOrderRequest.getCheckinTime());

            // 结束时间
            Integer businessDayMax = HotelUtils.parseDate2Int(createBookingOrderRequest.getCheckoutTime());


            HourRoomDayUseSearch hourRoomDayUseSearch = new HourRoomDayUseSearch();

            hourRoomDayUseSearch.setHid(user.getHid());
            hourRoomDayUseSearch.setBusinessDayMax(businessDayMax);
            hourRoomDayUseSearch.setBusinessDayMin(businessDayMin);

            // 每天使用情况
            Page<HourRoomDayUse> hourRoomDayUses = hourRoomDayUseDao.selectBySearch(hourRoomDayUseSearch);

            Map<Integer, List<HourRoomDayUse>> hourRoomMap = new HashMap<>();

            if (hourRoomDayUses.size() > 0) {

                hourRoomMap = hourRoomDayUses.stream().collect(Collectors.groupingBy(HourRoomDayUse::getRoomInfoId));

            }

            /**
             *  取出两个时间的日期差
             */
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.currentDate(createBookingOrderRequest.getCheckinTime()), HotelUtils.currentDate(createBookingOrderRequest.getCheckoutTime()));


            // 要添加的辅助房态
            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            // 开始日期结束日期是否为同一天
            Boolean starEndOneDay = false;

            // 开始小时
            int inHours = book.getCheckinTime().getHours();

            // 结束小时
            int outTours = book.getCheckoutTime().getHours();

            HashMap<Integer, Boolean> hourStarMap = new HashMap<>();

            HashMap<Integer, Boolean> hourEndMap = new HashMap<>();

            String useStarHourStr = "";
            String useEndHourStr = "";

            // 如果不是同一天，则把第二天日期也计算出来。
            if (!businessDayMin.equals(businessDayMax)) {
                starEndOneDay = true;

                for (int i = 0; i <= outTours; i++) {
                    hourEndMap.put(i, true);
                    useEndHourStr += i + ",";
                }
                outTours = 23;
                useEndHourStr = useEndHourStr.substring(0, useEndHourStr.length() - 1);
            }

            for (int i = inHours; i <= outTours; i++) {
                hourStarMap.put(i, true);
                useStarHourStr += i + ",";
            }
            useStarHourStr = useStarHourStr.substring(0, useStarHourStr.length() - 1);


            /**
             * 3.添加预订房型信息
             */
            List<CreateBookingOrderRequest.roomType> roomTypeList = createBookingOrderRequest.getRoomTypeList();

            BookingOrderDailyPrice bodp = new BookingOrderDailyPrice();

            // 需要添加的房型
            ArrayList<BookingOrderRoomType> addBort = new ArrayList<>();

            //  需要添加的钟点房使用
            ArrayList<HourRoomDayUse> addHourUse = new ArrayList<>();

            // 需要删除的钟点房使用详情
            ArrayList<HourRoomDayUse> upaHourUse = new ArrayList<>();

            Integer sumPrice = 0;

            for (int i = 0; i < roomTypeList.size(); i++) {
                // 预订房型数据
                CreateBookingOrderRequest.roomType roomType = roomTypeList.get(i);
                // 房间数据
                List<CreateBookingOrderRequest.roomType.roomInfo> roomList = roomType.getRoomList();
                // 查询费用
                // 创建预订房型信息
                BookingOrderRoomType bort = new BookingOrderRoomType();
                bort.setHid(user.getHid());
                bort.setHotelGroupId(user.getHotelGroupId());
                bort.setArriveTime(HotelUtils.currentDate(createBookingOrderRequest.getCheckinTime()) + " " + book.getKeepTime() + ":00");
                bort.setRoomTypeId(roomType.getRoomTypeId());
                bort.setRoomTypeNum(roomType.getNum());
                bort.setHasRoomNum(roomList.size());
                bort.setPriceCodeId(roomType.getRateId());
                bort.setPriceCode(roomType.getRateCode());
                bort.setState(BOOK.STA_YX);
                bort.setOrderState(BOOK.STA_YX);
                bort.setCreateTime(date);
                bort.setCreateUserId(user.getUserId());
                bort.setCreateUserName(user.getUserName());
                bort.setUpdateTime(date);
                bort.setUpdateUserId(user.getUserId());
                bort.setUpdateUserName(user.getUserName());
                bort.setCheckinTime(book.getCheckinTime());
                bort.setCheckoutTime(book.getCheckoutTime());
                //增加预订房型操作日志
                Oprecord oprecord1 = new Oprecord(user);
                oprecord1.setMainId(sn);
                oprecord1.setRegistId(book.getBookingOrderId());
                oprecord1.setDescription("对订单 : " + sn + "，添加预订房型：" + roomType.getRoomTypeName() + ",房间数量: " + bort.getRoomTypeNum() + ",房价码:" + bort.getPriceCode());
                oprecord1.setOccurTime(HotelUtils.currentTime());
                oprecords.add(oprecord1);
                List<CreateBookingOrderRequest.roomType.priceInfo> priceList = roomType.getPriceList();
                // 房型添加的房价
                ArrayList<BookingOrderDailyPrice> rtPriceList = new ArrayList<>();

                int allPrice = 0;
                for (int pl = 0; pl < priceList.size(); pl++) {
                    CreateBookingOrderRequest.roomType.priceInfo priceInfo = priceList.get(pl);
                    bodp = new BookingOrderDailyPrice();
                    bodp.setBookingOrderRoomNumId(0);
                    bodp.setHid(user.getHid());
                    bodp.setHotelGroupId(user.getHotelGroupId());
                    bodp.setPrice(priceInfo.getPrice());
                    bodp.setDailyTime(Integer.parseInt(priceInfo.getDate().replace("-", "")));
                    bodp.setRoomTypeId(bort.getRoomTypeId());
                    bodp.setRoomNumId(0);
                    bodp.setBreakNum(0);
                    bodp.setDailyState(1);
                    bodp.setRateCodeId(bort.getPriceCodeId());
                    bodp.setRateCode(bort.getPriceCode());
                    bodp.setIsStayover(0);
                    bodp.setCreateTime(date);
                    bodp.setCreateUserId(user.getUserId());
                    bodp.setCreateUserName(user.getUserName());
                    bodp.setUpdateTime(date);
                    bodp.setUpdateUserId(user.getUserId());
                    bodp.setUpdateUserName(user.getUserName());
                    rtPriceList.add(bodp);
                    allPrice += priceInfo.getPrice();
                }

                sumPrice += allPrice * roomType.getNum();

                bort.setBookingOrderDailyPrices(rtPriceList);

                // 需要添加的预订房间

                ArrayList<BookingOrderRoomNum> bookingOrderRoomNums = new ArrayList<>();

                for (int k = 0; k < roomList.size(); k++) {

                    CreateBookingOrderRequest.roomType.roomInfo roomInfo = roomList.get(k);

                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(book.getBookingOrderId());
                    bookingOrderRoomNum.setBookingOrderRoomTypeId(bort.getId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(bort.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(roomInfo.getRoomInfoId());
                    bookingOrderRoomNum.setRoomNum(roomInfo.getRoomNum());
                    if (roomInfo.getRoomCode() != null) {
                        bookingOrderRoomNum.setRoomCode(roomInfo.getRoomCode());
                    }
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setCheckinTime(book.getCheckinTime());
                    bookingOrderRoomNum.setCheckoutTime(book.getCheckoutTime());
                    bookingOrderRoomNum.setRowRoom(1);
                    bookingOrderRoomNum.setIsHour(1);
                    bookingOrderRoomNum.setRateCodeId(bort.getPriceCodeId());
                    bookingOrderRoomNum.setRateCode(bort.getPriceCode());
                    bookingOrderRoomNum.setBookingOrderDailyPrices(rtPriceList);

                    // 验证当前时段是否被租用
                    List<HourRoomDayUse> roomHourUse = hourRoomMap.get(roomInfo.getRoomInfoId());

                    // 说明当前时间没有
                    if (roomHourUse == null || roomHourUse.size() < 1) {

                        HourRoomDayUse hourRoomDayUse = getHourRoomDayUse(bookingOrderRoomNum);
                        hourRoomDayUse.setBusinessDay(businessDayMin);
                        hourRoomDayUse.setUseMsg(useStarHourStr);

                        addHourUse.add(hourRoomDayUse);

                        // 如果是隔一天
                        if (starEndOneDay) {

                            HourRoomDayUse hourRoomDayUseEnd = getHourRoomDayUse(bookingOrderRoomNum);
                            hourRoomDayUseEnd.setBusinessDay(businessDayMax);
                            hourRoomDayUseEnd.setUseMsg(useEndHourStr);

                            addHourUse.add(hourRoomDayUseEnd);

                        }

                    } else {

                        // 验证当天是否存在
                        Map<Integer, HourRoomDayUse> hourRoomDayUseMap = roomHourUse.stream().collect(Collectors.toMap(HourRoomDayUse::getBusinessDay, a -> a, (k1, k2) -> k1));

                        HourRoomDayUse hourRoomDayUse = hourRoomDayUseMap.get(businessDayMin);
                        if (hourRoomDayUse == null) {

                            hourRoomDayUse = getHourRoomDayUse(bookingOrderRoomNum);
                            hourRoomDayUse.setBusinessDay(businessDayMin);
                            hourRoomDayUse.setUseMsg(useStarHourStr);

                            addHourUse.add(hourRoomDayUse);

                        } else {


                            String[] split = hourRoomDayUse.getUseMsg().split(",");
                            Arrays.sort(split);

                            Boolean noRoom = false;

                            String errMsg = "";

                            for (int ion = 0; ion < split.length; ion++) {

                                int i1 = Integer.parseInt(split[ion]);

                                Boolean aBoolean = hourStarMap.get(i1);
                                if (aBoolean != null && aBoolean) {
                                    noRoom = true;
                                    errMsg += split[ion];
                                    errMsg += ",";
                                }
                            }

                            if (noRoom) {
                                throw new Exception(bookingOrderRoomNum.getRoomNum() + businessDayMin + "：" + errMsg + "时段不可以");
                            }

                            String newUseMsg = hourRoomDayUse.getUseMsg() + "," + useStarHourStr;
                            hourRoomDayUse.setUseMsg(newUseMsg);

                            upaHourUse.add(hourRoomDayUse);

                        }


                        // 如果是隔一天
                        if (starEndOneDay) {

                            HourRoomDayUse hourRoomDayUseEnd = hourRoomDayUseMap.get(businessDayMax);

                            if (hourRoomDayUseEnd == null) {
                                hourRoomDayUseEnd = getHourRoomDayUse(bookingOrderRoomNum);
                                hourRoomDayUseEnd.setBusinessDay(businessDayMax);
                                hourRoomDayUseEnd.setUseMsg(useEndHourStr);

                                addHourUse.add(hourRoomDayUseEnd);
                            } else {

                                String[] split = hourRoomDayUseEnd.getUseMsg().split(",");
                                Arrays.sort(split);

                                Boolean noRoom = false;

                                String errMsg = "";

                                for (int ion = 0; ion < split.length; ion++) {

                                    int i1 = Integer.parseInt(split[ion]);

                                    Boolean aBoolean = hourStarMap.get(i1);
                                    if (aBoolean != null && aBoolean) {
                                        noRoom = true;
                                        errMsg += split[ion];
                                        errMsg += ",";
                                    }
                                }

                                if (noRoom) {
                                    throw new Exception(bookingOrderRoomNum.getRoomNum() + businessDayMin + "：" + errMsg + "时段不可以");
                                }

                                String newUseMsg = hourRoomDayUseEnd.getUseMsg() + "," + useEndHourStr;
                                hourRoomDayUseEnd.setUseMsg(newUseMsg);

                                upaHourUse.add(hourRoomDayUse);

                            }

                        }

                    }


                    //记录操作日志
                    Oprecord oprecord2 = new Oprecord(user);
                    oprecord2.setMainId(sn);
                    oprecord2.setRegistId(book.getBookingOrderId());
                    oprecord2.setDescription("对订单 : " + sn + "，进行排房：" + bookingOrderRoomNum.getRoomNum());
                    oprecord2.setOccurTime(HotelUtils.currentTime());
                    oprecords.add(oprecord2);

                    //添加辅助房态
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(bookingOrderRoomNum.getRoomNumId());
                    roomAuxiliaryRelation.setRoomNum(bookingOrderRoomNum.getRoomNum());
                    roomAuxiliaryRelation.setBookingOrderId(book.getBookingOrderId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(auxiliary.getRoomAuxiliaryId());
                    roomAuxiliaryRelation.setSort(auxiliary.getSort());

                    roomAuxiliaryRelations.add(roomAuxiliaryRelation);

                    bookingOrderRoomNums.add(bookingOrderRoomNum);
                }

                // 添加预订未分房的房间
                for (int k = 0; k < bort.getRoomTypeNum() - bort.getHasRoomNum(); k++) {

                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(book.getBookingOrderId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(bodp.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(0);
                    bookingOrderRoomNum.setRoomNum("0");
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setRateCodeId(bort.getPriceCodeId());
                    bookingOrderRoomNum.setRateCode(bort.getPriceCode());
                    bookingOrderRoomNum.setCheckinTime(book.getCheckinTime());
                    bookingOrderRoomNum.setCheckoutTime(book.getCheckoutTime());
                    bookingOrderRoomNum.setRowRoom(0);
                    bookingOrderRoomNum.setIsHour(1);
                    bookingOrderRoomNum.setBookingOrderDailyPrices(rtPriceList);
                    bookingOrderRoomNums.add(bookingOrderRoomNum);

                }
                bort.setBookingOrderRoomNums(bookingOrderRoomNums);
                addBort.add(bort);
            }

            book.setTotalPrice(sumPrice);

            Integer bookingOrderId = bookTransactionService.addBookHourService(book, registGroup, addBort, roomAuxiliaryRelations, oprecords, bookingOrderConfig, addHourUse, upaHourUse);
            responseData.setData(bookingOrderId);
            responseData.setData1(HotelUtils.parseDate2Str(new Date(date.getTime() + 15 * 60 * 1000)));
            this.addOprecords(oprecords);
            ArrayList<String> params = new ArrayList<>();
            params.add(book.getBookingName());
            params.add(book.getRoomTypeSummary());
            params.add(book.getSn());
            params.add(HotelUtils.parseDate2Str(book.getCheckinTime()).substring(0, 10) + " 14:00");
            params.add(HotelUtils.parseDate2Str(book.getCheckoutTime()).substring(0, 16));
            params.add(user.getHotelName());
            final JSONObject smsParam = new JSONObject();
            smsParam.put(SMS_LOC.PHONE, book.getBookingPhone());
            smsParam.put(SMS_LOC.PARAM, params);
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {

                        SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                        smsHotelSendRecordRequest.setLocationId(SMS_LOC.ORDER_CREATE);
                        smsHotelSendRecordRequest.setSessionToken(createBookingOrderRequest.getSessionToken());
                        smsHotelSendRecordRequest.setPhoneNumber(smsParam.getString(SMS_LOC.PHONE));

                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        HashMap<String, String> filed = new HashMap<>();
                        filed.put("sn", book.getSn());
                        filed.put("rtdesc", book.getRoomTypeSummary());
                        HashMap<String, String> dataMap = new HashMap<>();
                        dataMap.put("bookId", book.getBookingOrderId() + "");
                        HashMap<String, String> onClickCbData = new HashMap<>();
                        onClickCbData.put("bookingOrderId", book.getBookingOrderId().toString());
                        baseService.push(user.getHotelGroupId(), user.getHid(), 15, filed, dataMap, true, true, onClickCbData);

                        ArrayList<String> list = (ArrayList<String>) JSONArray.toList(smsParam.getJSONArray(SMS_LOC.PARAM), String.class);
                        smsHotelSendRecordRequest.setParams(list);
                        baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    //判断是否是小程序进行的预订，如果是的则15分钟不支付则取消订单
                    if (createBookingOrderRequest.getFromType() == 25) {
                        Map<String, Object> data = new HashMap<>();
                        data.put("bokingOrderId", bookingOrderId.toString());
                        data.put("sessionToken", sessionToken);
                        data.put("reason", "支付超时");
                        JSONObject cancleBookingOrderJob = JobName.cancleBookingOrderJob(2, data);
                        baseService.addCornJob(cancleBookingOrderJob);
                    }
                }
            });


        } catch (Exception e) {
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setCode(-1);
        }
        return responseData;
    }

    public HourRoomDayUse getHourRoomDayUse(BookingOrderRoomNum bookingOrderRoomNum) {
        HourRoomDayUse hourRoomDayUse = new HourRoomDayUse();
        hourRoomDayUse.setRoomNo(bookingOrderRoomNum.getRoomNum());
        hourRoomDayUse.setRoomInfoId(bookingOrderRoomNum.getRoomNumId());
        hourRoomDayUse.setRoomTypeId(bookingOrderRoomNum.getRoomTypeId());
        hourRoomDayUse.setHid(bookingOrderRoomNum.getHid());
        hourRoomDayUse.setHotelGroupId(bookingOrderRoomNum.getHotelGroupId());
        return hourRoomDayUse;
    }

    public List<RoomDayPriceShow> updateRoomPriceForCache(TbUserSession user, Integer roomTypeId, Date dateBefore, Date dateFuture, Boolean updateCache, int roomRateId) {

        // 1. 查询房型
        RoomType roomType = roomTypeDao.selectById(roomTypeId);
        Integer price = roomType.getPrice();

        // 2.查询所有每日房价
        RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
        roomDayPriceSearch.setRoomTypeId(roomTypeId);
        roomDayPriceSearch.setRoomRateId(roomRateId);
        roomDayPriceSearch.setDayTimeMin(HotelUtils.parseDate2Int(dateBefore));
        roomDayPriceSearch.setDayTimeMax(HotelUtils.parseDate2Int(dateFuture));

        List<RoomDayPrice> roomDayPrices = roomDayPriceDao.selectBySearch(roomDayPriceSearch);

        Map<Integer, RoomDayPrice> dayPriceMap = roomDayPrices.stream().collect(Collectors.toMap(RoomDayPrice::getDayTime, a -> a, (k1, k2) -> k2));

        // 3.获取两个日期差
        List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(dateBefore).substring(0, 10), HotelUtils.parseDate2Str(dateFuture).substring(0, 10));

        // 返回的房价信息
        ArrayList<RoomDayPriceShow> roomDayPriceShows = new ArrayList<>();

        Calendar cal = Calendar.getInstance();

        RoomDayPriceShow rdps = new RoomDayPriceShow();

        RoomDayPrice roomDayPrice = new RoomDayPrice();

        for (String date : allDayListBetweenDate) {

            int day = Integer.parseInt(date.replace("-", ""));

            roomDayPrice = dayPriceMap.get(day);

            rdps = new RoomDayPriceShow();
            rdps.setDayTime(day);

            if (roomDayPrice == null) {

                cal.setTime(HotelUtils.parseStr2Date(date + " 00:00:00"));

                rdps.setId(-1);
                rdps.setPrice(price);
                rdps.setWeekDay(cal.get(Calendar.DAY_OF_WEEK));

            } else {

                rdps.setId(roomDayPrice.getId());
                rdps.setWeekDay(roomDayPrice.getWeekDay());
                rdps.setPrice(roomDayPrice.getPrice());

            }

            roomDayPriceShows.add(rdps);

        }

        return roomDayPriceShows;

    }


    /**
     * 修改预订房型
     *
     * @param bookUpdateRoomType
     * @return
     */
    @Override
    public ResponseData updateBookRoomType(BookUpdateRoomTypeTwo bookUpdateRoomType) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            Date kesss = new Date();


            /*1.获取用户信息*/
            String sessionToken = bookUpdateRoomType.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /**
             * 1.查询预订单信息
             */
            BookingOrder bookingOrder = bookingOrderDao.selectById(bookUpdateRoomType.getBookId());
            String sn = bookingOrder.getSn();
            if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())) {
                throw new Exception("未查询到相应的订单信息");
            }

            //需要增加的辅助房态记录
            RoomAuxiliary auxiliary = new RoomAuxiliary();

            /**
             * 查询预订单辅助房态
             *  如果当前时间等于 预抵时间，则状态为预抵。
             *      否则 辅助房态为预离。
             */
            String nowDate = HotelUtils.currentDate();
            if (nowDate.equals(HotelUtils.parseDate2Str(bookingOrder.getCheckinTime()).substring(0, 10))) {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_ARRIVALS);
            } else {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_CREATE);
            }


            Date date = new Date();

            /**
             * 添加操作日志
             */
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);
            oprecord.setMainId(bookingOrder.getSn());
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setOccurTime(HotelUtils.currentTime());
            JsonConfig jsonConfig = new JsonConfig();
            jsonConfig.registerJsonValueProcessor(Date.class,
                    new JsonDateValueProcessor());
            oprecord.setSourceValue(JSONObject.fromObject(bookingOrder, jsonConfig).toString());

            UpdateBookingOrderInfoRequest request = bookUpdateRoomType.getUpdateBookingOrderInfoRequest();


            if (request.getBookingOrderId() == null) {
                throw new Exception("订单状态异常");
            }
            Integer bookingOrderId = request.getBookingOrderId();
            //预订类型
            if (request.getOrderType() != null) {
                bookingOrder.setOrderType(request.getOrderType());
            }
            //客源类型
            if (request.getResourceId() != null) {
                bookingOrder.setResourceId(request.getResourceId());
            }
            if (request.getFromType() != null) {
                bookingOrder.setFromType(request.getFromType());
            }
            //预订时间
            bookingOrder.setCheckinTime(HotelUtils.parseStr2Date(request.getCheckinTime()));
            bookingOrder.setCheckoutTime(HotelUtils.parseStr2Date(request.getCheckoutTime()));
            bookingOrder.setDayCount(request.getDayCount());
            bookingOrder.setKeepTime(request.getKeepTime());
            bookingOrder.setBookingName(request.getBookingName());
            bookingOrder.setBookingPhone(request.getBookingPhone());
            bookingOrder.setRoomTypeSummary(request.getRoomTypeSummary());
            //新增修改备注以及第三方订单号
            bookingOrder.setRemark(request.getRemark());
            bookingOrder.setThirdPlatformOrderCode(request.getThirdPlatformOrderCode());
            if (request.getRateCodeId() != null) {
                bookingOrder.setRateCodeId(request.getRateCodeId());
            }
            //会员信息
            if (request.getCardId() != null) {
                bookingOrder.setCardId(request.getCardId());
                bookingOrder.setCardNo(request.getCardNo());
            }
            //协议单位
            if (request.getCompanyId() != null) {
                bookingOrder.setCompanyId(request.getCompanyId());
                bookingOrder.setCompanyName(request.getCompanyName());
            }
            if (request.getCheckinName() != null && !"".equals(request.getCheckinName())) {
                bookingOrder.setCheckinName(request.getCheckinName());

            }
            if (request.getCheckinPhone() != null && !"".equals(request.getCheckinPhone())) {
                bookingOrder.setCheckinPhone(request.getCheckinPhone());

            }
            oprecord.setChangedValue(JSONObject.fromObject(bookingOrder).toString());
            oprecords.add(oprecord);

            Date checkinTime = HotelUtils.parseStr2Date(bookUpdateRoomType.getStartTime());
            Date checkoutTime = HotelUtils.parseStr2Date(bookUpdateRoomType.getEndTime());

            // 查询所有房型信息
            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setHid(user.getHid());
            List<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);
            Map<Integer, RoomType> hidRtMap = roomTypes.stream().collect(Collectors.toMap(RoomType::getRoomTypeId, a -> a, (k1, k2) -> k1));

            // 查询所有房间信息
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
            Map<Integer, RoomInfo> hidRoomMap = roomInfos.stream().collect(Collectors.toMap(RoomInfo::getRoomInfoId, a -> a, (k1, k2) -> k1));

            // 预订房型
            BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
            bookingOrderRoomTypeSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            bookingOrderRoomTypeSearch.setOrderState(BOOK.STA_YX);
            List<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);
            Map<Integer, BookingOrderRoomType> bookingOrderRoomTypesMap = bookingOrderRoomTypes.stream().collect(Collectors.toMap(BookingOrderRoomType::getRoomTypeId, a -> a, (k1, k2) -> k1));

            // 预订房间,只查未入住的
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            bookingOrderRoomNumSearch.setOrderState(BOOK.STA_YX);
            List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
            Map<Integer, List<BookingOrderRoomNum>> collect = bookingOrderRoomNums.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getIsCheckin));
            Map<Integer, List<BookingOrderRoomNum>> roomMap = new HashMap<>();
            if (collect.get(0) != null) {
                roomMap = collect.get(0).stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getRoomTypeId));
            }
            int allCheckInNum = 0;
            if (collect.get(1) != null) {
                allCheckInNum = collect.get(1).size();
            }
            // 查询所有房价
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
            // 房间的房价
            Map<Integer, List<BookingOrderDailyPrice>> priceMapForRoom = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getBookingOrderRoomNumId));

            // 房型的价格
            bookingOrderDailyPriceSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            bookingOrderDailyPriceSearch.setRoomNumId(0);
            List<BookingOrderDailyPrice> bookingOrderDailyPrices2 = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
            Map<Integer, List<BookingOrderDailyPrice>> priceMapForRoomType = bookingOrderDailyPrices2.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getRoomTypeId));


            // 修改的房型,新增的房型,删除的房型
            ArrayList<BookingOrderRoomType> updateRoomType = new ArrayList<>();
            ArrayList<BookingOrderRoomType> addRoomType = new ArrayList<>();
            ArrayList<BookingOrderRoomType> deleteRoomType = new ArrayList<>();

            // 新增的房间,修改的房间,删除的房间
            ArrayList<BookingOrderRoomNum> addRooms = new ArrayList<>();
            ArrayList<BookingOrderRoomNum> deleteRooms = new ArrayList<>();

            // 要新增,删除的房价
            ArrayList<BookingOrderDailyPrice> addPrices = new ArrayList<>();
            ArrayList<BookingOrderDailyPrice> deletePrices = new ArrayList<>();

            // 要新增,删除的辅助房态
            ArrayList<RoomAuxiliaryRelation> addAuxi = new ArrayList<>();
            ArrayList<RoomAuxiliaryRelation> deleteAuxi = new ArrayList<>();

            // 前台传值的房型信息
            //Map<Integer, BookUpdateRoomType.RoomType> roomTypeMap = bookUpdateRoomType.getRoomTypeMap();
            Map<Integer, BookUpdateRoomType.RoomType> roomTypeMap = new HashMap<>();
            BookingOrderDailyPrice bodp = new BookingOrderDailyPrice();

            // 所有房间数量
            int ollRoomNums = 0;

            HashMap<Integer, Boolean> deleteRoomMap = new HashMap<>();

            // 遍历传过来的房型
            List<BookUpdateRoomTypeTwo.RoomType> paramRoomTypes = bookUpdateRoomType.getRoomTypes();

            StringBuilder rtSummary = new StringBuilder();


            for (BookUpdateRoomTypeTwo.RoomType rt : paramRoomTypes) {

                Integer roomTypeId = rt.getRoomTypeId();

                List<BookUpdateRoomTypeTwo.PriceMsg> priceList = rt.getPriceList();

                // 新的预订房间
                List<BookUpdateRoomTypeTwo.Room> newBookRooms = rt.getRoomList();

                ArrayList<BookingOrderRoomNum> addRtRooms = new ArrayList<>();

                RoomType roomType = hidRtMap.get(roomTypeId);
                if (roomType == null) {
                    throw new Exception("未查询到编号：" + roomTypeId + " 的房型");
                }
                // 新的预订数量
                Integer newRoomNum = rt.getNum();

                ollRoomNums += newRoomNum;

                rtSummary.append(roomType.getRoomTypeName());
                rtSummary.append("*");
                rtSummary.append(newRoomNum);
                rtSummary.append("间。");

                // 验证预订房型是否存在,原房型为空则说明是添加
                BookingOrderRoomType bookingOrderRoomType = bookingOrderRoomTypesMap.get(roomTypeId);
                if (bookingOrderRoomType == null) {


                    // 添加新的预订房型
                    BookingOrderRoomType bort = new BookingOrderRoomType();
                    bort.setHid(user.getHid());
                    bort.setHotelGroupId(user.getHotelGroupId());
                    bort.setBookingOrderId(bookingOrder.getBookingOrderId());
                    bort.setArriveTime(HotelUtils.parseDate2Str(checkinTime));
                    bort.setRoomTypeId(rt.getRoomTypeId());
                    bort.setRoomTypeNum(rt.getNum());
                    bort.setHasRoomNum(newBookRooms.size());
                    bort.setPriceCodeId(rt.getPriceCodeId());
                    bort.setPriceCode(rt.getPriceCode());
                    bort.setState(BOOK.STA_YX);
                    bort.setOrderState(BOOK.STA_YX);
                    bort.setCreateTime(date);
                    bort.setCreateUserId(user.getUserId());
                    bort.setCreateUserName(user.getUserName());
                    bort.setUpdateTime(date);
                    bort.setUpdateUserId(user.getUserId());
                    bort.setUpdateUserName(user.getUserName());
                    bort.setCheckinTime(checkinTime);
                    bort.setCheckoutTime(checkoutTime);
                    // 房型添加的房价
                    ArrayList<BookingOrderDailyPrice> rtPriceList = new ArrayList<>();

                    // 添加价格
                    for (BookUpdateRoomTypeTwo.PriceMsg priceMsg : priceList) {
                        bodp = new BookingOrderDailyPrice();
                        bodp.setBookingOrderRoomNumId(0);
                        bodp.setHid(user.getHid());
                        bodp.setBookingOrderId(bookingOrder.getBookingOrderId());
                        bodp.setHotelGroupId(user.getHotelGroupId());
                        bodp.setPrice(priceMsg.getPrice());
                        bodp.setDailyTime(priceMsg.getDate());
                        bodp.setRoomTypeId(bort.getRoomTypeId());
                        bodp.setRoomNumId(0);
                        bodp.setDailyState(1);
                        bodp.setIsStayover(0);
                        bodp.setCreateTime(date);
                        bodp.setCreateUserId(user.getUserId());
                        bodp.setCreateUserName(user.getUserName());
                        bodp.setUpdateTime(date);
                        bodp.setUpdateUserId(user.getUserId());
                        bodp.setUpdateUserName(user.getUserName());
                        rtPriceList.add(bodp);
                    }

                    bort.setBookingOrderDailyPrices(rtPriceList);

                    StringBuilder sb = new StringBuilder();

                    // 添加预订房间
                    for (BookUpdateRoomTypeTwo.Room room : newBookRooms) {

                        BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                        bookingOrderRoomNum.setHid(user.getHid());
                        bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                        bookingOrderRoomNum.setBookingOrderId(bookingOrder.getBookingOrderId());
                        bookingOrderRoomNum.setBookingOrderRoomTypeId(bort.getId());
                        bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                        bookingOrderRoomNum.setCreateTime(date);
                        bookingOrderRoomNum.setCreateUserId(user.getUserId());
                        bookingOrderRoomNum.setUpdateTime(date);
                        bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                        bookingOrderRoomNum.setRoomTypeId(bort.getRoomTypeId());
                        bookingOrderRoomNum.setRoomNumId(room.getRoomInfoId());
                        bookingOrderRoomNum.setRoomNum(room.getRoomNum());
                        bookingOrderRoomNum.setIsCheckin(0);
                        bookingOrderRoomNum.setOrderState(1);
                        bookingOrderRoomNum.setIsCheckout(0);
                        bookingOrderRoomNum.setCheckinTime(checkinTime);
                        bookingOrderRoomNum.setCheckoutTime(checkoutTime);
                        bookingOrderRoomNum.setRowRoom(1);
                        bookingOrderRoomNum.setBookingOrderDailyPrices(rtPriceList);

                        RoomInfo roomInfo = hidRoomMap.get(room.getRoomInfoId());

                        if (roomInfo == null) {

                            throw new Exception("未查询到编号：" + room.getRoomInfoId() + " 的房间");

                        }

                        sb.append(room.getRoomNum());
                        sb.append("、");

                        //添加辅助房态
                        RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                        roomAuxiliaryRelation.setHid(user.getHid());
                        roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                        roomAuxiliaryRelation.setRoomId(bookingOrderRoomNum.getRoomNumId());
                        roomAuxiliaryRelation.setRoomNum(bookingOrderRoomNum.getRoomNum());
                        roomAuxiliaryRelation.setBookingOrderId(bookingOrder.getBookingOrderId());
                        roomAuxiliaryRelation.setRoomAuxiliaryId(auxiliary.getRoomAuxiliaryId());
                        roomAuxiliaryRelation.setSort(auxiliary.getSort());

                        addAuxi.add(roomAuxiliaryRelation);

                        addRtRooms.add(bookingOrderRoomNum);

                    }

                    // 房间号
                    int roomNum = newRoomNum - newBookRooms.size();
                    for (int i = 0; i < roomNum; i++) {
                        BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                        bookingOrderRoomNum.setHid(user.getHid());
                        bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                        bookingOrderRoomNum.setBookingOrderId(bookingOrder.getBookingOrderId());
                        bookingOrderRoomNum.setBookingOrderRoomTypeId(bort.getId());
                        bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                        bookingOrderRoomNum.setCreateTime(date);
                        bookingOrderRoomNum.setCreateUserId(user.getUserId());
                        bookingOrderRoomNum.setUpdateTime(date);
                        bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                        bookingOrderRoomNum.setRoomTypeId(bort.getRoomTypeId());
                        bookingOrderRoomNum.setRoomNumId(0);
                        bookingOrderRoomNum.setRoomNum("");
                        bookingOrderRoomNum.setIsCheckin(0);
                        bookingOrderRoomNum.setOrderState(1);
                        bookingOrderRoomNum.setIsCheckout(0);
                        bookingOrderRoomNum.setCheckinTime(checkinTime);
                        bookingOrderRoomNum.setCheckoutTime(checkoutTime);
                        bookingOrderRoomNum.setRowRoom(0);
                        addRtRooms.add(bookingOrderRoomNum);
                    }

                    bort.setBookingOrderRoomNums(addRtRooms);
                    addRoomType.add(bort);

                    //增加预订房型操作日志
                    Oprecord oprecord1 = new Oprecord(user);
                    oprecord1.setMainId(bookingOrder.getSn());
                    oprecord1.setBookingOrderId(bookingOrder.getBookingOrderId());
                    oprecord1.setDescription("对订单 : " + bookingOrder.getSn() + "，添加预订房型：" + roomType.getRoomTypeName() + ",房间数量: " + bort.getRoomTypeNum() + ",房价码:" + bort.getPriceCode() + ",房间：" + sb.toString());
                    oprecord1.setOccurTime(HotelUtils.currentTime());
                    oprecords.add(oprecord1);

                    continue;

                }

                bookingOrderRoomTypesMap.get(roomTypeId).setNeedDelete(false);

                bookingOrderRoomType.setCheckinTime(checkinTime);
                bookingOrderRoomType.setCheckoutTime(checkoutTime);
                // 老的预订数量
                Integer oldRoomNum = bookingOrderRoomType.getRoomTypeNum();
                // 老的分房数量
                Integer hasRoomNum = bookingOrderRoomType.getHasRoomNum();

                // 老的预订房间
                List<BookingOrderRoomNum> borns = roomMap.get(rt.getRoomTypeId());

                // 已入住的数量
                int checkInNum = 0;

                Map<Integer, List<BookingOrderRoomNum>> bookRoomNumList = new HashMap<>();
                List<BookingOrderRoomNum> ckeckInRooms = new ArrayList<>();
                List<BookingOrderRoomNum> oldBookRooms = new ArrayList<>();
                if (borns != null && borns.size() > 0) {
                    bookRoomNumList = borns.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getIsCheckin));
                    ckeckInRooms = bookRoomNumList.get(1);
                    oldBookRooms = bookRoomNumList.get(0);
                }


                if (ckeckInRooms != null) {
                    checkInNum = ckeckInRooms.size();
                }


                bookingOrderRoomType.setRoomTypeNum(rt.getNum());
                bookingOrderRoomType.setHasRoomNum(checkInNum + newBookRooms.size());


                // 删除原有房价
                deletePrices.addAll(priceMapForRoomType.get(roomTypeId));

                // 房型添加的房价
                ArrayList<BookingOrderDailyPrice> rtPriceList = new ArrayList<>();

                // 添加价格
                for (BookUpdateRoomTypeTwo.PriceMsg priceMsg : priceList) {
                    bodp = new BookingOrderDailyPrice();
                    bodp.setBookingOrderId(bookingOrderRoomType.getBookingOrderId());
                    bodp.setBookingOrderRoomNumId(0);
                    bodp.setHid(user.getHid());
                    bodp.setHotelGroupId(user.getHotelGroupId());
                    bodp.setPrice(priceMsg.getPrice());
                    bodp.setDailyTime(priceMsg.getDate());
                    bodp.setRoomTypeId(bookingOrderRoomType.getRoomTypeId());
                    bodp.setRoomNumId(0);
                    bodp.setDailyState(1);
                    bodp.setIsStayover(0);
                    bodp.setCreateTime(date);
                    bodp.setCreateUserId(user.getUserId());
                    bodp.setCreateUserName(user.getUserName());
                    bodp.setUpdateTime(date);
                    bodp.setUpdateUserId(user.getUserId());
                    bodp.setUpdateUserName(user.getUserName());
                    rtPriceList.add(bodp);
                }

                addPrices.addAll(rtPriceList);

                StringBuilder oldRoom = new StringBuilder();
                int oldRowRoomNum = 0; //原分房数量
                // 先删除原有房间
                for (BookingOrderRoomNum bord : oldBookRooms) {

                    if (bord.getRoomNumId() == 0) {
                        continue;
                    }
                    oldRowRoomNum++;
                    oldRoom.append(bord.getRoomNum());
                    oldRoom.append("、");

                    // 删除房间
                    deleteRooms.add(bord);
                    deleteRoomMap.put(bord.getId(), true);

                    // 删除房价
                    deletePrices.addAll(priceMapForRoom.get(bord.getId()));

                    // 删除原有辅助房态
                    RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
                    roomAuxiliaryRelationSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
                    roomAuxiliaryRelationSearch.setRoomId(bord.getRoomNumId());
                    List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

                    if (roomAuxiliaryRelations != null) {
                        deleteAuxi.addAll(roomAuxiliaryRelations);
                    }

                }


                // 新增排房的房间
                StringBuilder newRoom = new StringBuilder();

                for (BookUpdateRoomTypeTwo.Room room : newBookRooms) {

                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(bookingOrder.getBookingOrderId());
                    bookingOrderRoomNum.setBookingOrderRoomTypeId(bookingOrderRoomType.getId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(bookingOrderRoomType.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(room.getRoomInfoId());
                    bookingOrderRoomNum.setRoomNum(room.getRoomNum());
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setCheckinTime(checkinTime);
                    bookingOrderRoomNum.setCheckoutTime(checkoutTime);
                    bookingOrderRoomNum.setRowRoom(1);

                    RoomInfo roomInfo = hidRoomMap.get(room.getRoomInfoId());

                    if (roomInfo == null) {

                        throw new Exception("未查询到编号：" + room.getRoomInfoId() + " 的房间");

                    }

                    newRoom.append(room.getRoomNum());
                    newRoom.append("、");

                    //添加辅助房态
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(bookingOrderRoomNum.getRoomNumId());
                    roomAuxiliaryRelation.setRoomNum(bookingOrderRoomNum.getRoomNum());
                    roomAuxiliaryRelation.setBookingOrderId(bookingOrder.getBookingOrderId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(auxiliary.getRoomAuxiliaryId());
                    roomAuxiliaryRelation.setSort(auxiliary.getSort());

                    bookingOrderRoomNum.setBookingOrderDailyPrices(rtPriceList);
                    addAuxi.add(roomAuxiliaryRelation);
                    addRooms.add(bookingOrderRoomNum);

                }

                // 当预订数量变化 等于 预订房间的变化时，则不需要修改预订未排房的房间信息
                if ((newRoomNum - oldRoomNum) != (newBookRooms.size() - oldRowRoomNum)) {

                    // 新预订数 - 新排房未入住数-已入住数 - 已排房的数量（已排房的数量已经添加）
                    int noRowNum = newRoomNum - oldRoomNum - newBookRooms.size() + oldRowRoomNum;
                    // 如果大于0 则添加预订信息
                    if (noRowNum > 0) {
                        for (int i = 0; i < noRowNum; i++) {
                            BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                            bookingOrderRoomNum.setHid(user.getHid());
                            bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                            bookingOrderRoomNum.setBookingOrderId(bookingOrderRoomType.getBookingOrderId());
                            bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                            bookingOrderRoomNum.setBookingOrderRoomTypeId(bookingOrderRoomType.getId());
                            bookingOrderRoomNum.setRoomTypeId(rt.getRoomTypeId());
                            bookingOrderRoomNum.setCreateTime(date);
                            bookingOrderRoomNum.setCreateUserId(user.getUserId());
                            bookingOrderRoomNum.setUpdateTime(date);
                            bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                            bookingOrderRoomNum.setRoomNumId(0);
                            bookingOrderRoomNum.setRoomNum("0");
                            bookingOrderRoomNum.setIsCheckin(0);
                            bookingOrderRoomNum.setOrderState(1);
                            bookingOrderRoomNum.setIsCheckout(0);
                            bookingOrderRoomNum.setCheckinTime(checkinTime);
                            bookingOrderRoomNum.setCheckoutTime(checkoutTime);
                            bookingOrderRoomNum.setRowRoom(0);
                            addRooms.add(bookingOrderRoomNum);
                        }
                    }
                    if (noRowNum < 0) {
                        // 遍历原有预订房间，把未排房的都删除
                        for (BookingOrderRoomNum bodn : oldBookRooms) {
                            if (noRowNum >= 0) {
                                continue;
                            }
                            deleteRooms.add(bodn);
                            deleteRoomMap.put(bodn.getId(), true);
                            noRowNum++;

                        }
                    }

                }

                //增加预订房型操作日志
                Oprecord oprecord1 = new Oprecord(user);
                oprecord1.setMainId(bookingOrder.getSn());
                oprecord1.setSourceValue(oldRoomNum + "");
                oprecord1.setChangedValue(newRoomNum + "");
                oprecord1.setBookingOrderId(bookingOrder.getBookingOrderId());
                oprecord1.setDescription("预订房型房间由：" + oldRoom.toString() + " ,修改为: " + newRoom.toString());
                oprecord1.setOccurTime(HotelUtils.currentTime());
                oprecords.add(oprecord1);


                updateRoomType.add(bookingOrderRoomType);
            }


            // 对未筛选到的房型进行删除
            Set<Integer> integers = bookingOrderRoomTypesMap.keySet();

            for (Integer key : integers) {

                BookingOrderRoomType bookingOrderRoomType = bookingOrderRoomTypesMap.get(key);

                RoomType roomType = hidRtMap.get(bookingOrderRoomType.getRoomTypeId());

                Oprecord oprecord1 = new Oprecord(user);
                oprecord1.setMainId(bookingOrder.getSn());
                oprecord1.setBookingOrderId(bookingOrder.getBookingOrderId());
                oprecord1.setDescription("取消预订房型：" + roomType.getRoomTypeName());
                oprecord1.setOccurTime(HotelUtils.currentTime());
                oprecords.add(oprecord1);

                if (!bookingOrderRoomType.getNeedDelete()) {
                    continue;
                }

                deleteRoomType.add(bookingOrderRoomType);

                List<BookingOrderDailyPrice> bookingOrderDailyPrices1 = priceMapForRoomType.get(bookingOrderRoomType.getRoomTypeId());
                deletePrices.addAll(bookingOrderDailyPrices1);

                // 需要删除的房间
                List<BookingOrderRoomNum> bookingOrderRoomNums1 = roomMap.get(bookingOrderRoomType.getRoomTypeId());

                if (bookingOrderRoomNums1 == null || bookingOrderRoomNums1.size() < 1) {
                    continue;
                }

                // 如果房间数量和排房数量不一致，说明有入住的房间
                if (bookingOrderRoomNums1.size() != bookingOrderRoomType.getRoomTypeNum()) {
                    throw new Exception(roomType.getRoomTypeName() + " 下有已入住的房间，不允许取消。");
                }

                for (BookingOrderRoomNum born : bookingOrderRoomNums1) {

                    deleteRooms.add(born);
                    deleteRoomMap.put(born.getId(), true);

                    if (born.getRoomNumId() == 0) {
                        continue;
                    }

                    // 删除辅助房态
                    deletePrices.addAll(priceMapForRoom.get(born.getId()));

                    // 删除原有辅助房态
                    RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
                    roomAuxiliaryRelationSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
                    roomAuxiliaryRelationSearch.setRoomId(born.getRoomNumId());
                    List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

                    if (roomAuxiliaryRelations != null) {
                        deleteAuxi.addAll(roomAuxiliaryRelations);
                    }

                }

            }


            Date date1 = new Date();


            // 判断时间是否更改
            Integer newCheckInTime = HotelUtils.parseDate2Int(checkinTime);
            Integer newCheckOutTime = HotelUtils.parseDate2Int(checkoutTime);

            Integer checkInTime = HotelUtils.parseDate2Int(bookingOrder.getCheckinTime());
            Integer checkOutTime = HotelUtils.parseDate2Int(bookingOrder.getCheckoutTime());

            ArrayList<BookingOrderRoomNum> updateRoomNums = new ArrayList<>();

            // 当时间发生改变时，则修改预订单时间
            if (!newCheckInTime.equals(checkInTime) || newCheckOutTime.equals(checkOutTime)) {

                for (BookingOrderRoomNum bookingOrderRoomNum : bookingOrderRoomNums) {

                    if (deleteRoomMap.get(bookingOrderRoomNum.getId()) != null && deleteRoomMap.get(bookingOrderRoomNum.getId())) {
                        continue;
                    }
                    bookingOrderRoomNum.setCheckinTime(checkinTime);
                    bookingOrderRoomNum.setCheckoutTime(checkoutTime);

                    updateRoomNums.add(bookingOrderRoomNum);
                }

            }

            /**
             *  遍历所有房型
             */
            if (bookingOrder.getOrderStatus().equals(BOOK.STA_QBRZ) || bookingOrder.getOrderStatus().equals(BOOK.STA_BFRZ)) {
                if (ollRoomNums == allCheckInNum) {
                    bookingOrder.setOrderStatus(BOOK.STA_QBRZ);
                } else {
                    bookingOrder.setOrderStatus(BOOK.STA_BFRZ);
                }
            }
            bookingOrder.setRoomCount(ollRoomNums);

            bookingOrder.setRoomTypeSummary(rtSummary.toString());
            bookTransactionService.updateBookService(bookingOrder, updateRoomType, addRoomType, deleteRoomType, addRooms, deleteRooms, addPrices, deletePrices, addAuxi, deleteAuxi, updateRoomNums);

            // 添加操作日志，更新辅助房态
            this.addOprecords(oprecords);
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);

        }

        return responseData;
    }

    public ArrayList<BookingOrderDailyPrice> upRoomTypeAddPrice(List<BookUpdateRoomType.PriceMsg> prices, TbUserSession user, Integer bookingOrderId, Integer bookingOrderNumId, Integer roomTypeId, Integer roomId) {
        ArrayList<BookingOrderDailyPrice> bookingOrderDailyPrices = new ArrayList<>();
        Date date = new Date();
        BookingOrderDailyPrice bodp = new BookingOrderDailyPrice();
        for (BookUpdateRoomType.PriceMsg pm : prices) {
            bodp = new BookingOrderDailyPrice();
            bodp.setBookingOrderRoomNumId(0);
            bodp.setHid(user.getHid());
            bodp.setBookingOrderId(bookingOrderId);
            bodp.setBookingOrderRoomNumId(bookingOrderNumId);
            bodp.setHotelGroupId(user.getHotelGroupId());
            bodp.setPrice(pm.getPrice());
            bodp.setDailyTime(pm.getDate());
            bodp.setRoomTypeId(roomTypeId);
            bodp.setRoomNumId(roomId);
            bodp.setDailyState(1);
            bodp.setIsStayover(0);
            bodp.setCreateTime(date);
            bodp.setCreateUserId(user.getUserId());
            bodp.setCreateUserName(user.getUserName());
            bodp.setUpdateTime(date);
            bodp.setUpdateUserId(user.getUserId());
            bodp.setUpdateUserName(user.getUserName());
            bookingOrderDailyPrices.add(bodp);
        }
        return bookingOrderDailyPrices;
    }


    @Override
    public ResponseData addBookOrderRoomNew(JSONObject param) {

        ResponseData responseData = new ResponseData(ER.SUCC);

        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (user.getSessionType() == 3) {
                user.setHid(param.getInt("hid"));
            }

            int bookingOrderId = param.getInt("bookingOrderId");

            /**
             * 1.查询预订房间表
             */
            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderId);

            if (!user.getHid().equals(bookingOrder.getHid())) {
                throw new Exception("此订单有误");
            }


/*            if (bookingOrder.getOrderStatus() != BOOK.STA_YX && bookingOrder.getOrderStatus() != BOOK.STA_BFRZ) {
                throw new Exception("当前状态不允许排房");
            }*/

            List<BookingOrderRoomNum> newRoomList = JSONArray.toList(param.getJSONArray("roomList"), BookingOrderRoomNum.class);

            Oprecord oprecord = new Oprecord(user);


            //需要增加的辅助房态记录
            RoomAuxiliary auxiliary = new RoomAuxiliary();

            /**
             * 查询预订单辅助房态
             *  如果当前时间等于 预抵时间，则状态为预抵。
             *      否则 辅助房态为预离。
             */
            String nowDate = HotelUtils.currentDate();
            if (nowDate.equals(HotelUtils.parseDate2Str(bookingOrder.getCheckinTime()).substring(0, 10))) {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_ARRIVALS);
            } else {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_CREATE);
            }
            // 删除、增加、修改辅助房态
            ArrayList<RoomAuxiliaryRelation> deleteRoomAuRea = new ArrayList<>();
            ArrayList<RoomAuxiliaryRelation> addRoomAuRea = new ArrayList<>();
            ArrayList<RoomAuxiliaryRelation> upaRoomAuRea = new ArrayList<>();

            // 删除、增加、修改的价格
            ArrayList<BookingOrderDailyPrice> deletePrices = new ArrayList<>();
            ArrayList<BookingOrderDailyPrice> addPrices = new ArrayList<>();
            ArrayList<BookingOrderDailyPrice> updaPrices = new ArrayList<>();

            // 修改的预定房间
            ArrayList<BookingOrderRoomNum> upaRooms = new ArrayList<>();

            /**
             * 2. 查询预订房型信息价格
             */
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, List<BookingOrderDailyPrice>> priceMap = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getRoomNumId));

            List<BookingOrderDailyPrice> roomTypePrices = priceMap.get(0);
            Map<Integer, List<BookingOrderDailyPrice>> roomTypePriceMap = roomTypePrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getRoomTypeId));

            /**
             * 3.查询辅助房态
             */
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations1 = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            Map<Integer, List<RoomAuxiliaryRelation>> collect = roomAuxiliaryRelations1.stream().collect(Collectors.groupingBy(RoomAuxiliaryRelation::getRoomAuxiliaryId));
            List<RoomAuxiliaryRelation> arrList = collect.get(ROOM_AUXILIARY.BOOK_ARRIVALS);
            if (arrList != null) {
                roomAuxiliaryRelations.addAll(arrList);
            }
            List<RoomAuxiliaryRelation> creatList = collect.get(ROOM_AUXILIARY.BOOK_CREATE);
            if (creatList != null) {
                roomAuxiliaryRelations.addAll(creatList);
            }

            Map<Integer, RoomAuxiliaryRelation> roomAuxiliaryRelationMap = roomAuxiliaryRelations.stream().collect(Collectors.toMap(RoomAuxiliaryRelation::getRoomId, a -> a, (k1, k2) -> k2));

            /**
             * 4.查询预订房型
             */
            BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
            bookingOrderRoomTypeSearch.setBookingOrderId(bookingOrderId);
            List<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);

            Map<Integer, BookingOrderRoomType> roomTypeMap = bookingOrderRoomTypes.stream().collect(Collectors.toMap(BookingOrderRoomType::getRoomTypeId, a -> a, (k1, k2) -> k2));

            /**
             * 5.查询预订房间信息
             */

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingOrderId);
            List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            Map<Integer, BookingOrderRoomNum> bookRoomMap = bookingOrderRoomNums.stream().collect(Collectors.toMap(BookingOrderRoomNum::getId, a -> a, (k1, k2) -> k2));


            BookingOrderDailyPrice addPr = new BookingOrderDailyPrice();

            Date date = new Date();

            String addRoom = "";
            String deleteRoom = "";

            JSONArray upaRoomlist = new JSONArray();


            /**
             * 组装信息
             */
            for (BookingOrderRoomNum bor : newRoomList) {

                BookingOrderRoomNum bookingOrderRoomNum = bookRoomMap.get(bor.getId());
                if (bookingOrderRoomNum.getOrderState() != 1 || bookingOrderRoomNum.getIsCheckin() > 0) {
                    continue;
                }
                Integer newRoomNumId = bor.getRoomNumId();


                if (bookingOrderRoomNum == null || newRoomNumId.equals(bookingOrderRoomNum.getRoomNumId())) {
                    continue;
                }

                Integer roomNumId = bookingOrderRoomNum.getRoomNumId();

                BookingOrderRoomType bort = roomTypeMap.get(bor.getRoomTypeId());

                // 新roomId等于0 老roomId不等于0 说明是取消排房
                if (newRoomNumId == 0 && roomNumId != 0) {

                    // 预订房型分房数-1
                    bort.setHasRoomNum(bort.getHasRoomNum() - 1);
                    roomTypeMap.put(bor.getRoomTypeId(), bort);

                    // 删除预订房价和辅助房态
                    RoomAuxiliaryRelation roomAuxiliaryRelation = roomAuxiliaryRelationMap.get(roomNumId);
                    if (roomAuxiliaryRelation != null) {
                        deleteRoomAuRea.add(roomAuxiliaryRelation);
                    }

                    List<BookingOrderDailyPrice> bookingOrderDailyPrices1 = priceMap.get(roomNumId);
                    if (bookingOrderDailyPrices1 != null) {
                        deletePrices.addAll(bookingOrderDailyPrices1);
                    }

                    deleteRoom += bookingOrderRoomNum.getRoomNum() + ",";

                    // 分房数改为0
                    bookingOrderRoomNum.setRowRoom(0);
                    bookingOrderRoomNum.setRoomNumId(0);
                    bookingOrderRoomNum.setRoomNum("");
                    JsonConfig jsonConfig = new JsonConfig();
                    jsonConfig.registerJsonValueProcessor(Date.class,
                            new JsonDateValueProcessor());
                    JSONObject jsonObject = JSONObject.fromObject(bookingOrderRoomNum, jsonConfig);
                    jsonObject.remove("bookingOrderDailyPrices");
                    upaRooms.add(bookingOrderRoomNum);

                    continue;

                }

                // 新roomId不等于0 老roomId等于0 则说明是新增房间
                if (newRoomNumId != 0 && roomNumId == 0) {

                    // 预订房型分房数+1
                    bort.setHasRoomNum(bort.getHasRoomNum() + 1);
                    roomTypeMap.put(bor.getRoomTypeId(), bort);

                    // 添加预订房价和辅助房态
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setRoomNum(bor.getRoomNum());
                    roomAuxiliaryRelation.setRoomId(bor.getRoomNumId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(auxiliary.getRoomAuxiliaryId());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setBookingOrderId(bookingOrderId);
                    roomAuxiliaryRelation.setSort(auxiliary.getSort());

                    addRoomAuRea.add(roomAuxiliaryRelation);

                    addRoom += bor.getRoomNum() + ",";

                    // 添加房价
                    List<BookingOrderDailyPrice> bodpes = roomTypePriceMap.get(bor.getRoomTypeId());

                    for (BookingOrderDailyPrice bodp : bodpes) {

                        addPr = new BookingOrderDailyPrice();
                        addPr.setBookingOrderRoomNumId(0);
                        addPr.setBookingOrderId(bookingOrderId);
                        addPr.setHid(bodp.getHid());
                        addPr.setHotelGroupId(bodp.getHotelGroupId());
                        addPr.setPrice(bodp.getPrice());
                        addPr.setDailyTime(bodp.getDailyTime());
                        addPr.setRoomTypeId(bodp.getRoomTypeId());
                        addPr.setBreakNum(bodp.getBreakNum());
                        addPr.setDailyState(1);
                        addPr.setIsStayover(0);
                        addPr.setCreateTime(date);
                        addPr.setCreateUserId(user.getUserId());
                        addPr.setCreateUserName(user.getUserName());
                        addPr.setUpdateTime(date);
                        addPr.setUpdateUserId(user.getUserId());
                        addPr.setUpdateUserName(user.getUserName());
                        addPr.setRoomNumId(bor.getRoomNumId());
                        addPr.setBookingOrderRoomNumId(bor.getId());

                        addPrices.add(addPr);

                    }

                    bookingOrderRoomNum.setRowRoom(1);
                    bookingOrderRoomNum.setRoomNumId(bor.getRoomNumId());
                    bookingOrderRoomNum.setRoomNum(bor.getRoomNum());

                    upaRooms.add(bookingOrderRoomNum);

                    continue;

                }

                // 更改排房房间
                List<BookingOrderDailyPrice> bookingOrderDailyPrices1 = priceMap.get(roomNumId);

                // 价格更改
                for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices1) {

                    bodp.setRoomNumId(bor.getRoomNumId());

                    updaPrices.add(bodp);
                }

                // 辅助房态更改
                RoomAuxiliaryRelation roomAuxiliaryRelation = roomAuxiliaryRelationMap.get(roomNumId);
                roomAuxiliaryRelation.setRoomId(bor.getRoomNumId());
                roomAuxiliaryRelation.setRoomNum(bor.getRoomNum());

                upaRoomAuRea.add(roomAuxiliaryRelation);
                addRoom += bor.getRoomNum() + ",";
                deleteRoom += bookingOrderRoomNum.getRoomNum() + ",";

                bookingOrderRoomNum.setRowRoom(1);
                bookingOrderRoomNum.setRoomNumId(bor.getRoomNumId());
                bookingOrderRoomNum.setRoomNum(bor.getRoomNum());

                upaRooms.add(bookingOrderRoomNum);

            }

            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription("新增排房：" + addRoom + "  删除排房：" + deleteRoom);
            /**
             * 调用排房方法
             */
            bookTransactionService.addBookRoomList(deleteRoomAuRea, addRoomAuRea, upaRoomAuRea, deletePrices, addPrices, updaPrices, upaRooms, roomTypeMap);


            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);

        }

        return responseData;
    }

    // 修改预订单信息
    public ResponseData upaBookMsg(BookUpdateRoomTypeTwo bookUpdateRoomType, ResponseData responseData) throws Exception {
        TbUserSession tbUserSession = this.getTbUserSession(bookUpdateRoomType.getSessionToken());
        UpdateBookingOrderInfoRequest updateBookingOrderInfoRequest = bookUpdateRoomType.getUpdateBookingOrderInfoRequest();
        Integer bookingOrderId = updateBookingOrderInfoRequest.getBookingOrderId();
        BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderId);
        if (bookingOrder.getOrderStatus() != 1 && bookingOrder.getOrderStatus() != 3) {
            throw new Exception("当前订单不允许修改");
        }
        Date checkinTime = bookingOrder.getCheckinTime();
        Date checkoutTime = bookingOrder.getCheckoutTime();
        String startTime = bookUpdateRoomType.getStartTime().substring(0, 10);
        String endTime = bookUpdateRoomType.getEndTime().substring(0, 10);
        bookingOrder.setBookingName(updateBookingOrderInfoRequest.getBookingName());
        bookingOrder.setBookingPhone(updateBookingOrderInfoRequest.getBookingPhone());
        bookingOrder.setBookingIdCode(updateBookingOrderInfoRequest.getBookingName());
        bookingOrder.setCardId(updateBookingOrderInfoRequest.getCardId());
        bookingOrder.setCardNo(updateBookingOrderInfoRequest.getCardNo());
        bookingOrder.setCompanyName(updateBookingOrderInfoRequest.getCompanyName());
        bookingOrder.setCompanyId(updateBookingOrderInfoRequest.getCompanyId());
        bookingOrder.setKeepTime(updateBookingOrderInfoRequest.getKeepTime());
        bookingOrder.setThirdPlatformOrderCode(updateBookingOrderInfoRequest.getThirdPlatformOrderCode());
        bookingOrder.setRemark(bookingOrder.getRemark());

        String checkinTimeStr = HotelUtils.parseDate2Str(checkinTime).substring(0, 10);
        String checkoutTimeStr = HotelUtils.parseDate2Str(checkoutTime).substring(0, 10);


        if (startTime.equals(checkinTimeStr) && endTime.equals(checkoutTimeStr)) {
            bookingOrderDao.editBookingOrder(bookingOrder);
            return responseData;
        }

        Date indate = HotelUtils.parseStr2Date(bookUpdateRoomType.getStartTime());
        Date outdate = HotelUtils.parseStr2Date(bookUpdateRoomType.getEndTime());

        bookingOrder.setCheckinTime(indate);
        bookingOrder.setCheckoutTime(outdate);

        // 预订单信息
        BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
        bookingOrderRoomTypeSearch.setBookingOrderId(bookingOrderId);
        bookingOrderRoomTypeSearch.setOrderState(1);
        bookingOrderRoomTypeSearch.setState(1);


        Page<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);
        ArrayList<BookingOrderRoomType> upaRoomType = new ArrayList<>();
        for (BookingOrderRoomType bort : bookingOrderRoomTypes) {
            bort.setCheckinTime(indate);
            bort.setCheckoutTime(outdate);
            upaRoomType.add(bort);
        }

        // 房间信息
        BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
        bookingOrderRoomNumSearch.setBookingOrderId(bookingOrderId);
        bookingOrderRoomNumSearch.setOrderState(1);
        bookingOrderRoomNumSearch.setIsCheckin(0);
        Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

        ArrayList<BookingOrderRoomNum> upaRooms = new ArrayList<>();

        for (BookingOrderRoomNum born : bookingOrderRoomNums) {
            born.setCheckinTime(indate);
            born.setCheckoutTime(outdate);
            upaRooms.add(born);
        }

        // 房价
        BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
        bookingOrderDailyPriceSearch.setBookingOrderId(bookingOrderId);
        Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

        ArrayList<BookingOrderDailyPrice> bookPriceList = new ArrayList<>();

        for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {
            Integer registId = bodp.getRegistId();
            if (registId == null || registId < 1) {
                bookPriceList.add(bodp);
            }
        }

        Map<Integer, List<BookingOrderDailyPrice>> roomTypePrices = bookPriceList.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getRoomTypeId));


        Integer paramIndate = HotelUtils.parseDate2Int(indate);
        Integer paramOutdate = HotelUtils.parseDate2Int(outdate);

        Integer orderIndate = HotelUtils.parseDate2Int(checkinTime);
        Integer orderOutdate = HotelUtils.parseDate2Int(checkoutTime);

        ArrayList<BookingOrderDailyPrice> delPriceList = new ArrayList<>();
        ArrayList<BookingOrderDailyPrice> addPriceList = new ArrayList<>();

        // 开始时间大于订单时间 ， 离店时间小于订单时间，删除掉对应的房价列表
        for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {
            // 开始日期 大于入住日期
            Integer dailyTime = bodp.getDailyTime() - paramIndate;
            if (dailyTime < 0) {
                delPriceList.add(bodp);
                continue;
            }

            // 结束日期小于离店日期
            int i = bodp.getDailyTime() - orderOutdate;
            if (i > 0) {
                delPriceList.add(bodp);
            }


        }
        ArrayList<String> addDayList = new ArrayList<>();

        // 判断开始时间  大于 删除，小于 新增
        if (paramIndate < orderIndate) {

            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(startTime, checkinTimeStr);
            addDayList.addAll(allDayListBetweenDate);

        }
        if (paramOutdate > orderOutdate) {
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(endTime, checkoutTimeStr);
            addDayList.addAll(allDayListBetweenDate);
        }

        Date date = new Date();

        for (String dayStr : addDayList) {

            Integer day = Integer.parseInt(dayStr.replace("-", ""));

            Set<Integer> integers = roomTypePrices.keySet();

            for (Integer key : integers) {

                // 在按照房间分组
                List<BookingOrderDailyPrice> rtpList = roomTypePrices.get(key);

                Map<Integer, List<BookingOrderDailyPrice>> collect = rtpList.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getRoomNumId));

                Set<Integer> integers1 = collect.keySet();

                for (Integer pkey : integers1) {

                    List<BookingOrderDailyPrice> bos = collect.get(pkey);

                    if (bos == null || bos.size() < 1) {
                        continue;
                    }

                    Map<Integer, BookingOrderDailyPrice> collect1 = bos.stream().collect(Collectors.toMap(BookingOrderDailyPrice::getDailyTime, a -> a, (k1, k2) -> k2));

                    if (collect1.get(day) != null) {
                        continue;
                    }

                    BookingOrderDailyPrice bodp = bos.get(0);

                    BookingOrderDailyPrice bookingOrderDailyPrice = new BookingOrderDailyPrice();

                    bookingOrderDailyPrice.setBookingOrderId(bodp.getBookingOrderId());
                    bookingOrderDailyPrice.setBookingOrderRoomNumId(bodp.getBookingOrderRoomNumId());
                    bookingOrderDailyPrice.setHid(bodp.getHid());
                    bookingOrderDailyPrice.setHotelGroupId(bodp.getHotelGroupId());
                    bookingOrderDailyPrice.setPrice(bodp.getPrice());
                    bookingOrderDailyPrice.setDailyTime(bodp.getDailyTime());
                    bookingOrderDailyPrice.setRoomTypeId(bodp.getRoomTypeId());
                    bookingOrderDailyPrice.setRoomNumId(0);
                    bookingOrderDailyPrice.setDailyState(1);
                    bookingOrderDailyPrice.setIsStayover(0);
                    bookingOrderDailyPrice.setCreateTime(date);
                    bookingOrderDailyPrice.setCreateUserId(bodp.getCreateUserId());
                    bookingOrderDailyPrice.setCreateUserName(bodp.getCreateUserName());
                    bookingOrderDailyPrice.setUpdateTime(date);
                    bookingOrderDailyPrice.setUpdateUserId(bodp.getUpdateUserId());
                    bookingOrderDailyPrice.setUpdateUserName(bodp.getUpdateUserName());
                    bookingOrderDailyPrice.setBookingOrderRoomNumId(bodp.getBookingOrderRoomNumId());
                    bookingOrderDailyPrice.setRoomNumId(bodp.getRoomNumId());

                    addPriceList.add(bookingOrderDailyPrice);
                }

            }

        }

        ArrayList<BookingOrderRoomType> rts = new ArrayList<>();
        ArrayList<BookingOrderRoomNum> rms = new ArrayList<>();
        ArrayList<RoomAuxiliaryRelation> ras = new ArrayList<>();

        bookTransactionService.updateBookService(bookingOrder, upaRoomType, rts, rts, rms, rms, addPriceList, delPriceList, ras, ras, upaRooms);


        return responseData;
    }

    /**
     * 排房
     * roomId 房间id
     * bookingOrderRoomId 预订房间id
     *
     * @param param
     * @return
     */
    @Override
    public JSONObject addBookOrderRoom(JSONObject param) {
        JSONObject resultMap = new JSONObject();
        resultMap.put(ER.RES, ER.SUCC);

        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            int bookingOrderRoomId = param.getInt("bookingOrderRoomId");

            /**
             * 1.查询预订房间表
             */
            BookingOrderRoomNum bookingOrderRoomNum = bookingOrderRoomNumDao.selectById(bookingOrderRoomId);
            if (bookingOrderRoomNum == null || bookingOrderRoomNum.getIsCheckin() == 1) {
                throw new Exception("当前订单不允许排房。");
            }

            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderRoomNum.getBookingOrderId());

            if (!user.getHid().equals(bookingOrder.getHid())) {
                throw new Exception("此订单有误");
            }


            if (bookingOrder.getOrderStatus() != BOOK.STA_YX && bookingOrder.getOrderStatus() != BOOK.STA_BFRZ) {
                throw new Exception("当前状态不允许排房");
            }


            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            /**
             * 2.如果已排房，则查询当前辅助房态
             */
            if (bookingOrderRoomNum.getRoomNumId() != null && bookingOrderRoomNum.getRoomNumId() > 0) {

                RoomAuxiliaryRelationSearch roomAuxiliarySearch = new RoomAuxiliaryRelationSearch();
                roomAuxiliarySearch.setBookingOrderId(bookingOrderRoomNum.getBookingOrderId());
                roomAuxiliarySearch.setRoomId(bookingOrderRoomNum.getRoomNumId());
                roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliarySearch);
            }

            //需要增加的辅助房态记录
            RoomAuxiliary auxiliary = new RoomAuxiliary();

            /**
             * 查询预订单辅助房态
             *  如果当前时间等于 预抵时间，则状态为预抵。
             *      否则 辅助房态为预离。
             */
            String nowDate = HotelUtils.currentDate();
            if (nowDate.equals(HotelUtils.parseDate2Str(bookingOrder.getCheckinTime()).substring(0, 10))) {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_ARRIVALS);
            } else {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_CREATE);
            }


            /**
             * 3.如果未传房间号，则说明是取消排房
             */
            Integer roomId = param.getInt("roomId");

            RoomInfo roomInfo = new RoomInfo();
            roomInfo.setRoomInfoId(0);
            if (roomId != 0) {
                roomInfo = roomInfoDao.selectById(roomId);
                if (roomInfo == null || !roomInfo.getHid().equals(roomInfo.getHid())) {
                    throw new Exception("传入房间相信有误");
                }
            }

            /**
             * 4.查询预订排房信息
             */
         /*   BookingOrderRoomType bookingOrderRoomType = bookingOrderRoomTypeDao.selectById(bookingOrderRoomNum.getBookingOrderRoomTypeId());
            if (bookingOrderRoomType == null) {
                throw new Exception("查询预订房型失败");
            }*/


            /**
             * 5.查询之前分房后的每日房价
             */
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setBookingOrderId(bookingOrderRoomNum.getBookingOrderId());
            bookingOrderDailyPriceSearch.setBookingOrderRoomNumId(bookingOrderRoomNum.getId());

            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);


            /**
             * 调用排房方法
             */
            bookTransactionService.addBookOrderRoomService(roomInfo, bookingOrderRoomNum, user, roomAuxiliaryRelations, auxiliary, null, null, bookingOrderDailyPrices);
           /* * HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });*/
        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());

        }

        return resultMap;
    }

    @Override
    public ResponseData addBookOrderRoomAll(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (param.get("bookingOrderId") == null || param.getInt("bookingOrderId") < 1) {
                throw new Exception("订单号不能空");
            }
            int bookingOrderId = param.getInt("bookingOrderId");

            if (param.get("type") == null) {
                throw new Exception("类型不确定");
            }
            int type = param.getInt("type");

            /**
             * type = 0  取消所有分房  type = 1 批量排房
             */
            if (type == 0) {

                BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                bookingOrderRoomNumSearch.setBookingOrderId(bookingOrderId);
                bookingOrderRoomNumSearch.setHid(user.getHid());

                List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

                if (bookingOrderRoomNums == null || bookingOrderRoomNums.size() < 1) {
                    return responseData;
                }


            } else if (type == 1) {
                if (!param.containsKey("roomList") || param.getJSONArray("roomList").size() < 1) {
                    throw new Exception("要排房的列表为空");
                }

                JSONArray roomList = param.getJSONArray("roomList");

                int size = roomList.size();


                if (size < 4) {
                    for (int i = 0; i < roomList.size(); i++) {
                        param.put("bookingOrderRoomId", roomList.getJSONObject(i).getInt("id"));
                        param.put("roomId", roomList.getJSONObject(i).getInt("roomNumId"));
                        this.addBookOrderRoom(param);
                    }
                } else {

                    // 三组数据为一个线程处理
                    int ik = size / 3;

                    ExecutorService service = Executors.newCachedThreadPool();

                    OrderServiceImpl orderService = this;

                    final CountDownLatch cdOrder = new CountDownLatch(1);
                    final CountDownLatch cdAnswer = new CountDownLatch(ik);

                    for (int i = 0; i < ik; i++) {

                        final int len = i * ik;
                        final int endlen = 1 + i != ik ? len + 3 : roomList.size();

                        log.info(len + "-----------------" + endlen);
                        // 房间信息相关
                        Runnable roomRunnable = new Runnable() {
                            @Override
                            public void run() {
                                try {

                                    for (int i = len; i < endlen; i++) {

                                        log.info("循环次数------------------------------" + i);

                                        param.put("bookingOrderRoomId", roomList.getJSONObject(i).getInt("id"));
                                        param.put("roomId", roomList.getJSONObject(i).getInt("roomNumId"));
                                        orderService.addBookOrderRoom(param);
                                    }
                                    cdOrder.await();
                                    cdAnswer.countDown();
                                } catch (Exception e) {
                                    cdOrder.countDown();
                                    log.error("",e);
                                }
                            }
                        };
                        service.execute(roomRunnable);
                    }
                    cdOrder.countDown();
                    cdAnswer.await();
                    service.shutdown();
                }
            }
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        baseService.push(user.getHotelGroupId(), user.getHid(), 9, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 取消预订单
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData cancelBook(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            int bookingOrderId = param.getInt("bookingOrderId");

            //1.查询预订单
            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderId);
            if (!user.getHid().equals(bookingOrder.getHid()) && !param.containsKey("hid")) {
                throw new Exception("此订单有误");
            }

            if (bookingOrder.getOrderStatus() != BOOK.STA_YX && bookingOrder.getOrderStatus() != BOOK.STA_NS) {
                throw new Exception("当前订单状态不允许取消");
            }

            if (!param.containsKey("reason")) {
                throw new Exception("取消理由不能空");
            }
            bookingOrder.setCancelBookingLation(param.getString("reason"));
            bookingOrder.setOtaStatus(HmhOrderStatusEnum.ORDER_CANCELLED.getType());
            //查询当前订单的账务信息，如果账不平则不允许取消
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setBookingId(bookingOrderId);
            accountSearch.setRegistIdIsNull(-1);

            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            int cash = 0;
            int cost = 0;
            for (int i = 0; i < accounts.size(); i++) {
                Account account = accounts.get(i);
                if (account.getRegistState() != 0 || account.getIsCancel() != 0) {
                    continue;
                }
                if (account.getPayType() == 1) {
                    cost += account.getPrice();
                }
                if (account.getPayType() == 2) {
                    cash += account.getPrice();
                }
            }

            if (cash != cost) {
                throw new Exception("请先处理账务信息");
            }
            //2.查询预订房型
            BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
            bookingOrderRoomTypeSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            List<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);

            //3.查询预订房间
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingOrderId);
            List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            // 修改的钟点房
            ArrayList<HourRoomDayUse> upaHourUse = new ArrayList<>();

            // 删除的钟点房房情
            ArrayList<HourRoomDayUse> delHourUse = new ArrayList<>();

            // 钟点房
            if (bookingOrder.getOrderType() == 2) {

                for (BookingOrderRoomNum born : bookingOrderRoomNums) {

                    Integer start = HotelUtils.parseDate2Int(born.getCheckinTime());
                    Integer end = HotelUtils.parseDate2Int(born.getCheckoutTime());

                    int startHours = born.getCheckinTime().getHours();
                    int endHours = born.getCheckoutTime().getHours();


                    // 判断开始日期和结束日期是否一致
                    if (!start.equals(end)) {


                        HourRoomDayUseSearch hourRoomDayUseSearch = new HourRoomDayUseSearch();
                        hourRoomDayUseSearch.setHid(user.getHid());
                        hourRoomDayUseSearch.setRoomInfoId(born.getRoomNumId());
                        hourRoomDayUseSearch.setBusinessDay(end);

                        Page<HourRoomDayUse> hourRoomDayUses = hourRoomDayUseDao.selectBySearch(hourRoomDayUseSearch);

                        if (hourRoomDayUses.size() > 0) {

                            HourRoomDayUse hourRoomDayUse = hourRoomDayUses.get(0);

                            String useMsg = hourRoomDayUse.getUseMsg();

                            String[] split = useMsg.split(",");

                            Boolean isUpa = false;

                            String newUseStr = "";

                            for (int i = 0; i < split.length; i++) {

                                int i1 = Integer.parseInt(split[i]);

                                if (i1 <= endHours) {
                                    continue;
                                }
                                isUpa = true;

                                newUseStr += split[i];
                                newUseStr += ",";

                            }

                            if (isUpa) {
                                hourRoomDayUse.setUseMsg(newUseStr.substring(0, newUseStr.length() - 1));
                                upaHourUse.add(hourRoomDayUse);
                            } else {
                                delHourUse.add(hourRoomDayUse);
                            }

                        }

                        endHours = 23;

                    }


                    HourRoomDayUseSearch hourRoomDayUseSearch = new HourRoomDayUseSearch();
                    hourRoomDayUseSearch.setHid(user.getHid());
                    hourRoomDayUseSearch.setRoomInfoId(born.getRoomNumId());
                    hourRoomDayUseSearch.setBusinessDay(start);

                    Page<HourRoomDayUse> hourRoomDayUses = hourRoomDayUseDao.selectBySearch(hourRoomDayUseSearch);


                    if (hourRoomDayUses.size() > 0) {

                        HourRoomDayUse hourRoomDayUse = hourRoomDayUses.get(0);

                        // 查询使用记录
                        String useMsg = hourRoomDayUse.getUseMsg();

                        String[] split = useMsg.split(",");

                        Boolean isUpa = false;

                        String newUseStr = "";

                        for (int i = 0; i < split.length; i++) {

                            int i1 = Integer.parseInt(split[i]);

                            if (i1 >= startHours && i1 <= endHours) {
                                continue;
                            }
                            isUpa = true;

                            newUseStr += split[i];
                            newUseStr += ",";

                        }

                        if (isUpa) {
                            hourRoomDayUse.setUseMsg(newUseStr.substring(0, newUseStr.length() - 1));
                            upaHourUse.add(hourRoomDayUse);
                        } else {
                            delHourUse.add(hourRoomDayUse);
                        }

                    }


                }

            }

            //4.查询当前预订单的辅助房态
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setBookingOrderId(bookingOrderId);
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

            ArrayList<BookingOrder> bookingOrders = new ArrayList<>();
            bookingOrders.add(bookingOrder);

            // 钟点房
            if (bookingOrder.getOrderType() != null && bookingOrder.getOrderType() == 2) {
                bookTransactionService.cancelHourOrderService(bookingOrders, bookingOrderRoomTypes, bookingOrderRoomNums, user, roomAuxiliaryRelations, upaHourUse, delHourUse);
            } else {
                bookTransactionService.cancelOrderService(bookingOrders, bookingOrderRoomTypes, bookingOrderRoomNums, user, roomAuxiliaryRelations);
            }
            //调用ota实现订单状态变更推送，以及价量态推送
            otaChangePushService.pushOrderAndNumChange(bookingOrder, bookingOrderRoomNums);
            // 更新房间缓存
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        baseService.push(user.getHotelGroupId(), user.getHid(), 9, new HashMap<String, String>(), new HashMap<String, String>(), true, true);

                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });
            //2022-02-09 添加会员消费撤销短信提醒
            String bookingPhone = bookingOrder.getBookingPhone();
            if (bookingPhone != null && bookingPhone.length() == 11) {
                HotelUtils.cachedThreadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            JSONObject postData = new JSONObject();
                            postData.put("sessionToken", sessionToken);
                            JSONObject hotelBaseInfo = JSONObject.fromObject(baseService.getHotelBaseInfo(postData));
                            JSONObject data = hotelBaseInfo.getJSONObject("data");
                            String hotelName = data.getString("hotelName");
                            String telephone = data.getString("telephone");
                            String addr = data.getString("addr");
                            //尊敬的{1}，您在{2}，预订的{3}，订单号为{4}，的订单已经取消。如有疑问请拨打酒店电话：{5}
                            final ArrayList<String> strings = new ArrayList<>();
                            strings.add(bookingOrder.getBookingName());
                            strings.add(hotelName);
                            strings.add(bookingOrder.getRoomTypeSummary());
                            strings.add(bookingOrder.getBookingOrderId().toString());
                            strings.add(telephone);
                            SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                            smsHotelSendRecordRequest.setLocationId(SMS_LOC.CANCEL_BOOKING);
                            smsHotelSendRecordRequest.setSessionToken(user.getSessionId());
                            smsHotelSendRecordRequest.setPhoneNumber(bookingPhone);
                            smsHotelSendRecordRequest.setParams(strings);
                            baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);
                        } catch (Exception e) {
                            log.error("",e);
                        }
                    }
                });
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    public ResponseData orderRecovery(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            int bookingOrderId = param.getInt("bookingOrderId");

            //1.查询预订单
            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderId);
            if (!user.getHid().equals(bookingOrder.getHid())) {
                throw new Exception("此订单有误");
            }

            if (bookingOrder.getOrderStatus() != BOOK.STA_YQX) {
                throw new Exception("当前订单非取消状态");
            }

            //2.查询预订房型
            BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
            bookingOrderRoomTypeSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            List<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);

            //3.查询预订房间
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingOrderId);
            List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            /**
             * 查询预订单辅助房态
             *  如果当前时间等于 预抵时间，则状态为预抵。
             *      否则 辅助房态为预离。
             */
            RoomAuxiliary auxiliary = new RoomAuxiliary();
            String nowDate = HotelUtils.currentDate();
            if (nowDate.equals(HotelUtils.parseDate2Str(bookingOrder.getCheckinTime()).substring(0, 10))) {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_ARRIVALS);
            } else {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_CREATE);
            }

            bookTransactionService.orderRecoveryService(bookingOrder, bookingOrderRoomTypes, bookingOrderRoomNums, user, auxiliary);

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        HashMap<String, String> filed = new HashMap<>();
                        filed.put("sn", bookingOrder.getSn());
                        filed.put("rtdesc", bookingOrder.getRoomTypeSummary());
                        HashMap<String, String> dataMap = new HashMap<>();
                        dataMap.put("bookId", bookingOrder.getBookingOrderId() + "");
                        HashMap<String, String> onClickCbData = new HashMap<>();
                        onClickCbData.put("bookingOrderId", bookingOrder.getBookingOrderId().toString());
                        baseService.push(user.getHotelGroupId(), user.getHid(), 15, filed, dataMap, true, true, onClickCbData);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");

        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public JSONObject updateDayPrice(JSONObject param) {
        JSONObject resultMap = new JSONObject();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            //操作日志
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);

            if (param.get("dayPriceData") == null) {
                throw new Exception("房价信息不能空");
            }

            JSONArray dayPrices = JSONArray.fromObject(URLDecoder.decode(param.getString("dayPriceData"), "utf-8"));

            if (dayPrices.size() < 1) {
                throw new Exception("房价信息不能空");
            }

            for (int i = 0; i < dayPrices.size(); i++) {

                BookingOrderDailyPrice bookingOrderDailyPrice = (BookingOrderDailyPrice) JSONObject.toBean(JSONObject.fromObject(dayPrices.getJSONObject(i).toString()), BookingOrderDailyPrice.class);
                Integer integer = bookingOrderDailyPriceDao.editBookingOrderDailyPrice(bookingOrderDailyPrice);
                if (integer < 1) {
                    throw new Exception("修改价格出错");
                }
                oprecord.setHid(user.getHid());
                oprecord.setRegistId(bookingOrderDailyPrice.getRegistId());
                oprecord.setBookingOrderId(bookingOrderDailyPrice.getBookingOrderRoomNumId());
            }
            oprecords.add(oprecord);
            this.addOprecords(oprecords);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    @Override
    public JSONObject updateBooking(JSONObject param) {
        JSONObject resultMap = new JSONObject();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            String bookingData = URLDecoder.decode(param.getString("bookingData"), "utf-8");

            JSONObject jsonObject = JSONObject.fromObject(bookingData);

            BookingOrder bookingOrder = (BookingOrder) JSONObject.toBean(jsonObject, BookingOrder.class);

            bookingOrder.setCheckinTime(HotelUtils.parseStr2Date(jsonObject.getString("checkinTimeStr").substring(0, 10) + " 00:00:00"));
            bookingOrder.setCheckoutTime(HotelUtils.parseStr2Date(jsonObject.getString("checkoutTimeStr").substring(0, 10) + " 00:00:00"));

            if (bookingOrder.getBookingOrderId() == null || bookingOrder.getBookingOrderId().toString().equals("")) {
                throw new Exception("订单编号不能空");
            }

            Integer integer = bookingOrderDao.editBookingOrder(bookingOrder);

            if (integer < 1) {
                throw new Exception("订单修改失败");
            }

        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    /**
     * bookingOrderId预订单id
     * id 预订房间id
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData cancelBookingRoomNo(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);


            Object bookingOrderIdObject = param.get("bookingOrderId");
            int bookingOrderId = Integer.parseInt(bookingOrderIdObject.toString());
            if (bookingOrderId < 1) {
                throw new Exception("订单号不能空");
            }


            /**
             * 查询订单状态，如果无效则不允许排房
             */
            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderId);

            if (bookingOrder == null) {
                throw new Exception("未查询到订单");
            }
            Integer orderStatus = bookingOrder.getOrderStatus();

            if (orderStatus == 2 || orderStatus == 5 || orderStatus == 6) {
                throw new Exception("订单状态不允许取消分房");
            }


            Object id = param.get("id");
            if (id == null || id.toString().equals("")) {
                /**
                 * 释放所有排房资源
                 */

                //1.查询所有预订房间的信息
                BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                bookingOrderRoomNumSearch.setHid(user.getHid());
                bookingOrderRoomNumSearch.setBookingOrderId(bookingOrderId);
                bookingOrderRoomNumSearch.setRowRoom(1);
                bookingOrderRoomNumSearch.setOrderState(1);

                List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

                Map<String, Integer> roomTypeHasRoomNum = new HashMap<>();

                for (int i = 0; i < bookingOrderRoomNums.size(); i++) {
                    if (bookingOrderRoomNums.get(i).getIsCheckin() == 1) {
                        String roomTypeId = bookingOrderRoomNums.get(i).getRoomTypeId().toString();
                        if (roomTypeHasRoomNum.containsKey(roomTypeId)) {
                            Integer integer = roomTypeHasRoomNum.get(roomTypeId);
                            integer += 1;
                            roomTypeHasRoomNum.put(roomTypeId, integer);
                        } else {
                            roomTypeHasRoomNum.put(roomTypeId, 1);
                        }
                        continue;
                    }
                    bookingOrderRoomNums.get(i).setRowRoom(0);
                    bookingOrderRoomNums.get(i).setRoomNumId(0);
                    bookingOrderRoomNums.get(i).setRoomNum("");
                }

                //2.查询所有预订房型信息 新版本取消了预订房型信息
//                BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
//                bookingOrderRoomTypeSearch.setBookingOrderId(bookingOrderId);
//                bookingOrderRoomTypeSearch.setOrderState(1);
//                bookingOrderRoomTypeSearch.setState(1);
//
//
//                List<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);
//
//                for (int i = 0; i < bookingOrderRoomTypes.size(); i++) {
//
//                    Integer roomTypeId = bookingOrderRoomTypes.get(i).getRoomTypeId();
//
//                    /**
//                     * 预订数量
//                     */
//                    Integer roomTypeNum = bookingOrderRoomTypes.get(i).getRoomTypeNum();
//
//                    /**
//                     * 获取已入住的数量,就是已分房的数量
//                     */
//                    if (roomTypeHasRoomNum.size() == 0) {
//                        bookingOrderRoomTypes.get(i).setHasRoomNum(0);
//                    } else {
//                        Integer checkInNum = roomTypeHasRoomNum.get(roomTypeId.toString());
//                        if (checkInNum == null) {
//                            checkInNum = 0;
//                        }
//                        bookingOrderRoomTypes.get(i).setHasRoomNum(checkInNum);
//                    }
//
//                }
//                Integer a = bookingOrderRoomTypes.size();

                //3.查询当前预订单的辅助房态
                RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
                roomAuxiliaryRelationSearch.setBookingOrderId(bookingOrderId);
                List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);


                BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
                bookingOrderDailyPriceSearch.setBookingOrderId(bookingOrderId);
                bookingOrderDailyPriceSearch.setDailyState(1);

                List<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

                List<BookingOrderDailyPrice> bookingOrderDailyPriceList = new ArrayList<>();
                for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
                    if (bookingOrderDailyPrices.get(i).getBookingOrderRoomNumId() != 0) {
                        bookingOrderDailyPriceList.add(bookingOrderDailyPrices.get(i));
                    }
                }

                bookTransactionService.cancelBookingRoomNoTran(bookingOrder, bookingOrderRoomNums, user, roomAuxiliaryRelations, bookingOrderDailyPriceList);


            } else {
                /**
                 * 释放单一排房资源
                 */


                /***
                 * 查询预订分房表中数据
                 */
                BookingOrderRoomNum bookingOrderRoomNum = bookingOrderRoomNumDao.selectById(Integer.parseInt(id.toString()));

                if (bookingOrderRoomNum.getOrderState() == 5) {
                    throw new Exception("订单已取消");
                }

                if (bookingOrderRoomNum.getIsCheckin() == 1) {
                    throw new Exception("当前房间已入住不允许取消排房");
                }

                if (bookingOrderRoomNum.getIsCheckout() == 1) {
                    throw new Exception("当前房间已经结账离店不允许取消排房");
                }

                Integer roomNumId = bookingOrderRoomNum.getRoomNumId();


                /**
                 * 设置预订排房房间id
                 */
                bookingOrderRoomNum.setRoomNumId(0);
                /**
                 * 设置预订排房房间号
                 */
                bookingOrderRoomNum.setRoomNum("");

                /**
                 * 设置成为排放的状态
                 */
                bookingOrderRoomNum.setRowRoom(0);


                /**
                 * 查询预订房型数据  新版本取消了预订房型信息
                 */

//                BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
//                bookingOrderRoomTypeSearch.setHid(user.getHid());
//                bookingOrderRoomTypeSearch.setBookingOrderId(bookingOrderId);
//                bookingOrderRoomTypeSearch.setRoomTypeId(bookingOrderRoomNum.getRoomTypeId());
//
//                List<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);
//
//                if (bookingOrderRoomTypes == null || bookingOrderRoomTypes.size() < 1) {
//                    throw new Exception("未查询到预订房型信息");
//                }
//
//                /**
//                 * 修改已经排的房间数
//                 */
//
//
//                BookingOrderRoomType bookingOrderRoomType = bookingOrderRoomTypes.get(0);
//                Integer hasRoomNum = bookingOrderRoomType.getHasRoomNum();
//                Integer roomNum = hasRoomNum - 1 >= 0 ? hasRoomNum - 1 : 0;
//                bookingOrderRoomType.setHasRoomNum(roomNum);


                /**
                 * 房间辅助房态表
                 */

                //4.查询当前预订单的辅助房态
                RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
                roomAuxiliaryRelationSearch.setBookingOrderId(bookingOrderId);
                roomAuxiliaryRelationSearch.setRoomId(roomNumId);
                List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

                List<BookingOrderRoomNum> bookingOrderRoomNums = new ArrayList<BookingOrderRoomNum>();
                bookingOrderRoomNums.add(bookingOrderRoomNum);


                //查询之前的价格
                List<BookingOrderDailyPrice> bookingOrderDailyPrices = new ArrayList<>();
                bookTransactionService.cancelBookingRoomNoTran(bookingOrder, bookingOrderRoomNums, user, roomAuxiliaryRelations, bookingOrderDailyPrices);
            }
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });


        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public JSONObject addPersonForBookingRoom(JSONObject param) {
        JSONObject resultMap = new JSONObject();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);


            String addPersonData = URLDecoder.decode(param.getString("addPersonData"), "utf-8");

            JSONArray roomList = JSONArray.fromObject(addPersonData);

            if (roomList == null || roomList.size() < 1) {
                throw new Exception("添加数据不能空");
            }

            ArrayList<RegistPerson> personList = new ArrayList<>();


            for (int i = 0; i < roomList.size(); i++) {
                JSONObject bookRoom = roomList.getJSONObject(i);
                Regist regist = new Regist();
                regist.setRoomTypeId(bookRoom.getInt("roomTypeId"));
                regist.setRoomNumId(bookRoom.getInt("roomNumId"));
                regist.setRoomNum(bookRoom.getString("roomNum"));
                regist.setHid(user.getHid());
                regist.setBookingOrderId(bookRoom.getInt("bookingOrderId"));

                JSONArray guestList = bookRoom.getJSONArray("guestList");
                int bookingOrderRoomNumId = bookRoom.getInt("id");

                ArrayList<RegistPerson> personArrayList = checkInTransactionService.addCheckinGuest(guestList, user, regist, bookingOrderRoomNumId);

                personList.addAll(personArrayList);
            }

            log.info("personList.size()={}",personList.size());

            for (int i = 0; i < personList.size(); i++) {
                Integer insert = 0;
                if (personList.get(i).getRegistPersonId() > 0) {
                    insert = registPersonDao.update(personList.get(i));
                } else {
                    insert = registPersonDao.insert(personList.get(i));
                }

                if (insert < 1) {
                    throw new Exception("添加人员失败");
                }
            }

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    @Override
    public HashMap<Integer, List<Integer>> addPersonForBookingRoom2(BookingRequest bookingRequest) throws Exception {
        TbUserSession user = getTbUserSession(bookingRequest);
        ArrayList<RegistPerson> registPersonArrayList = new ArrayList<>();
        Regist regist = new Regist();
        Set<String> roomNumSet = new TreeSet<>();
        for (BookingRequest.Person person : bookingRequest.getPersonList()) {
            regist.setRoomTypeId(person.getRoomTypeId());
            regist.setRoomNumId(person.getRoomNumId());
            regist.setRoomNum(person.getRoomNum());
            regist.setBookingOrderId(bookingRequest.getBookingOrderId());
            regist.setHid(user.getHid());
            RegistPerson registPerson = checkInTransactionService.addCheckinGuest2(person, user, regist, person.getBookingOrderRoomNumId());
            if (roomNumSet.contains(regist.getRoomNum())) {
                registPerson.setIsOther(1);
            } else {
                registPerson.setIsOther(0);
                roomNumSet.add(regist.getRoomNum());
            }

            registPersonArrayList.add(registPerson);
        }

        HashMap<Integer, List<Integer>> registerIdMap = new HashMap<>();
        for (RegistPerson registPerson : registPersonArrayList) {
            int rt = 0;
            if (registPerson.getRegistPersonId() > 0) {
                rt = registPersonDao.update(registPerson);
            } else {
                rt = registPersonDao.insert(registPerson);
            }
            if (rt == 0) {
                throw new Exception(String.format("添加人员[%s]失败", registPerson.getPersonName()));
            }

            List<Integer> list = registerIdMap.get(registPerson.getBookingOrderRoomNumId());
            if (list == null) {
                registerIdMap.put(registPerson.getBookingOrderRoomNumId(), new ArrayList<>());
            }
            registerIdMap.get(registPerson.getBookingOrderRoomNumId()).add(registPerson.getRegistPersonId());
        }
        return registerIdMap;
    }


    @Override
    public void delPersonForBookingRoom(BookingRequest bookingRequest) throws Exception {
        final TbUserSession user = this.getTbUserSession(bookingRequest);
        for (BookingRequest.Person person : bookingRequest.getPersonList()) {
            Integer rt = 0;
            RegistPerson registPerson = registPersonDao.selectById(person.getRegistPersonId());
            if (!registPerson.getHid().equals(user.getHid())) {
                throw new Exception("删除人员失败");
            }
            rt = registPersonDao.delete(person.getRegistPersonId());
            if (rt < 1) {
                throw new Exception("删除人员失败");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public Map<String, Object> updateBookingOrderConfig(JSONObject param) {
        JSONObject resultMap = new JSONObject();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (param.get("data") == null || param.getString("data").equals("")) {
                throw new Exception("无效数据");
            }

            String dataStr = URLDecoder.decode(param.getString("data"), "utf-8");

            BookingOrderConfig bookingOrderConfig = (BookingOrderConfig) JSONObject.toBean(JSONObject.fromObject(dataStr), BookingOrderConfig.class);

            Integer update;
            if (bookingOrderConfig.getId() != null && bookingOrderConfig.getId() > 0) {
                update = bookingOrderConfigDao.editBookingOrderConfig(bookingOrderConfig);
                if (update < 1) {
                    throw new Exception("修改失败");
                }
            } else {
                update = bookingOrderConfigDao.saveBookingOrderConfig(bookingOrderConfig);
                if (update < 1) {
                    throw new Exception("添加失败");
                }
            }
            resultMap.put("data", bookingOrderConfig);

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData updateBookingOrderInfo(UpdateBookingOrderInfoRequest request) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = request.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            //操作日志
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Integer bookingOrderId = request.getBookingOrderId();
            if (null == bookingOrderId) {
                throw new Exception("订单号不能空");
            }
            BookingOrder bookingOrder = new BookingOrder();
            bookingOrder.setBookingOrderId(bookingOrderId);
            bookingOrder.setBookingName(request.getBookingName());
            bookingOrder.setBookingPhone(request.getBookingPhone());
            bookingOrder.setThirdPlatformOrderCode(request.getThirdPlatformOrderCode());
            bookingOrder.setResourceId(request.getResourceId());
            bookingOrder.setSn(request.getSn());
            bookingOrder.setOtaStatus(HmhOrderStatusEnum.TO_BE_CONFIRMED.getType());
            if (request.getResourceId() == 2) {
                bookingOrder.setCardId(request.getCardId());
                bookingOrder.setCardNo(request.getCardNo());
            } else if (request.getResourceId() > 2) {
                bookingOrder.setCompanyId(request.getCompanyId());
                bookingOrder.setCompanyName(request.getCompanyName());
                bookingOrder.setCompanyAccountId(request.getCompanyAccountId());
            }
            Integer integer = bookingOrderDao.editBookingOrder(bookingOrder);
            if (integer < 1) {
                throw new Exception("修改预订主单失败");
            }
            // 入住人
//            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
//            registPersonSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
//            List<RegistPerson> registPeoples = registPersonDao.selectBySearch(registPersonSearch);
//            if (CollectionUtils.isEmpty(registPeoples)) {
//                log.error("预订单【sn={}】入住信息为空，请检查后重试", bookingOrder.getSn());
//                throw new RuntimeException("预订单入住信息为空，请检查后重试");
//            }
            //推送订单状态以及对应的房量变化
            List<BookingOrderRoomNum> bookingOrderRoomNumList = bookingOrderRoomNumDao.selectByBookingOrderId(bookingOrderId);
            otaChangePushService.pushOrderAndNumChange(bookingOrder,bookingOrderRoomNumList);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData accountBack(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {


            RegistSearch registSearch = new RegistSearch();
            registSearch.setState(0);
            registSearch.setHid(2122);
            List<Regist> regists = registDao.selectBySearch(registSearch);

            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(2122);
            accountSearch.setBusinessDay(********);
            accountSearch.setBusinessDay(5);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);


        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public String getRegistPeopleName(Integer registId) {
        String name = "";
        RegistPersonSearch registPersonSearch = new RegistPersonSearch();
        registPersonSearch.setRegistId(registId);
        List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
        for (RegistPerson registPerson : registPeople) {
            name += registPerson.getPersonName() + " ";
        }
        return name;
    }

    /**
     * 获取今日实况
     *
     * @return
     */
    public ResponseData getTodaySituation(BaseRequest baseRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {


//            平均房价 = 实时全天房费/（实时在住-今日预离-自用）
//            预计平均房价 = 预计总房费/（实时在住客房数＋今日预抵客房数－自用房-预离）

//            实时时租房费 = 实时在住时租房同住主单房价累计
//            预计总房费 = 实时全天房费收入＋今日预抵客房房价累计
            String sessionToken = baseRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            roomInfoSearch.setState(1);
            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
            Integer vc = 0;
            Integer vd = 0;
            Integer oc = 0;
            Integer od = 0;
            Integer ooo = 0;
            Integer os = 0;
            for (int i = 0; i < roomInfos.size(); i++) {
                if (roomInfos.get(i).getRoomNumState() == ROOM_STATUS.VC) {
                    vc += 1;
                } else if (roomInfos.get(i).getRoomNumState() == ROOM_STATUS.VD) {
                    vd += 1;
                } else if (roomInfos.get(i).getRoomNumState() == ROOM_STATUS.OCC) {
                    oc += 1;
                } else if (roomInfos.get(i).getRoomNumState() == ROOM_STATUS.OD) {
                    od += 1;
                } else if (roomInfos.get(i).getRoomNumState() == ROOM_STATUS.OOO) {
                    ooo += 1;
                } else if (roomInfos.get(i).getRoomNumState() == ROOM_STATUS.OS) {
                    os += 1;
                }
            }
            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setState(0);
            List<Regist> regists = registDao.selectBySearch(registSearch);

            //预离
            Integer ylNum = 0;
            //在住
            Integer zy = 0;
            StringBuilder daysRegistIdsStringBuilder = new StringBuilder();
            StringBuilder hoursRegistIdsStringBuilder = new StringBuilder();
            Integer nowDateInt = HotelUtils.parseDate2Int(new Date());
            for (int i = 0; i < regists.size(); i++) {
                Integer date2Int = HotelUtils.parseDate2Int(regists.get(i).getCheckoutTime());
                if (date2Int.equals(nowDateInt)) {
                    ylNum++;
                }
                if (regists.get(i).getCheckinType().equals(HOTEL_CONST.CHECK_IN_TYPE_ZY)) {
                    zy++;
                } else if (regists.get(i).getCheckinType().equals(HOTEL_CONST.CHECK_IN_TYPE_HOUR)) {
                    hoursRegistIdsStringBuilder.append(regists.get(i).getRegistId());
                    hoursRegistIdsStringBuilder.append(",");
                } else {
                    daysRegistIdsStringBuilder.append(regists.get(i).getRegistId());
                    daysRegistIdsStringBuilder.append(",");
                }

            }

            String dayRegistIds = "";
            if (!StringUtil.isEmpty(daysRegistIdsStringBuilder.toString())) {
                dayRegistIds = daysRegistIdsStringBuilder.substring(0, daysRegistIdsStringBuilder.length() - 1);
            }
            String hoursRegistIds = "";
            if (!StringUtil.isEmpty(hoursRegistIdsStringBuilder.toString())) {
                hoursRegistIds = hoursRegistIdsStringBuilder.substring(0, hoursRegistIdsStringBuilder.length() - 1);
            }

            //实时出租率 =（实时在住客房数－自用房）/（总客房数-维修-自用）×100%
            NumberFormat numberformat = NumberFormat.getInstance();
            numberformat.setMaximumFractionDigits(2);
            String ssczl = HotelUtils.getNumberForBaiFenBi((oc + od - zy), (roomInfos.size() - ooo - zy), 100);
            log.info("ssczl={}",ssczl + "%");
            //预计出租率 =（实时在住客房数＋今日预抵客房数－自用房-预离）/（总客房数-维修-自用）×100%

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setHid(user.getHid());
            ArrayList<Long> longs = new ArrayList<>();
            String substring = HotelUtils.parseDate2Str(new Date()).substring(0, 10);
            Date date = HotelUtils.parseStr2Date(substring + " 00:00:00");
            longs.add((date.getTime() - 100) / 1000);
            longs.add((date.getTime() + 86400000) / 1000);
            bookingOrderRoomNumSearch.setCheckinTime(longs);
            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
            log.info("bookingOrderRoomNums.size()={}",bookingOrderRoomNums.size());
            log.info("ylNum:" + ylNum);
            String yjczl = HotelUtils.getNumberForBaiFenBi((oc + od + bookingOrderRoomNums.size() - zy - ylNum), (roomInfos.size() - ooo - zy), 100);
            log.info("yjczl={}",yjczl + "%");
            //实时全天房费 =（实时在住客房-今日预离房）只看同住主单房价累计


            //平均房价 = 实时全天房费/（实时在住-今日预离-自用）

            Integer dayPrice = 0;
            Integer hourPrice = 0;
            if (!StringUtil.isEmpty(dayRegistIds)) {
                BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
                bookingOrderDailyPriceSearch.setHid(user.getHid());
                bookingOrderDailyPriceSearch.setDailyTime(user.getBusinessDay());
                bookingOrderDailyPriceSearch.setRegistIds(dayRegistIds);
                Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
                for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
                    dayPrice += bookingOrderDailyPrices.get(i).getPrice();
                }
            }

            if (!StringUtil.isEmpty(hoursRegistIds)) {
                BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
                bookingOrderDailyPriceSearch.setHid(user.getHid());
                bookingOrderDailyPriceSearch.setDailyTime(user.getBusinessDay());
                bookingOrderDailyPriceSearch.setRegistIds(hoursRegistIds);
                Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
                for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
                    hourPrice += bookingOrderDailyPrices.get(i).getPrice();
                }
            }

            String pjff = HotelUtils.getNumberForBaiFenBi((dayPrice / 100), (oc + od - ylNum - zy), 1);
            log.info("pjff={}",pjff);
            log.info("hourPrice:" + hourPrice);
            log.info("dayPrice:" + dayPrice);
            String ssqtff = HotelUtils.getNumberForBaiFenBi((dayPrice), (100), 1);
            log.info("ssqtff:" + ssqtff);
            String sszdff = HotelUtils.getNumberForBaiFenBi((hourPrice), (100), 1);
            JSONObject postData = new JSONObject();
            postData.put("ssqtff", ssqtff);
            postData.put("yjczl", yjczl);
            postData.put("ssczl", ssczl);
            postData.put("sszdff", sszdff);

            responseData.setData(postData);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData dayRevenue(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setDailyState(1);
            bookingOrderDailyPriceSearch.setDailyTime(user.getBusinessDay());

            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setState(0);
            List<Regist> regists = registDao.selectBySearch(registSearch);

            Map<Integer, Regist> registMap = regists.stream().collect(Collectors.toMap(Regist::getRegistId, a -> a, (k1, k2) -> k2));

            Integer sumPrice = 0;

            Integer count = 0;

            Integer nowDateInt = HotelUtils.parseDate2Int(new Date());

            // 排除已结的房间
            for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {

                Integer registId = bodp.getRegistId();
                if (registId != null && registId > 0) {
                    Regist regist = registMap.get(registId);
                    if (regist == null) {
                        continue;
                    }
                    // 判断是否是当天离店,非钟点房当天离店的也不算
                    Integer checkinType = regist.getCheckinType();
                    Integer date2Int = HotelUtils.parseDate2Int(regist.getCheckoutTime());
                    if (checkinType != 2 && nowDateInt.equals(date2Int)) {
                        log.info("registId:" + regist.getRegistId());
                        continue;
                    }
                    count++;
                    sumPrice += bodp.getPrice();
                }
            }
            // 预计将产生的房费
            JSONObject forecastRoomPrice = new JSONObject();
            forecastRoomPrice.put("count", count);
            forecastRoomPrice.put("money", sumPrice);
            forecastRoomPrice.put("des", "夜审预计产生的房费");

            // 查询当天已产生的账务信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setBusinessDay(user.getBusinessDay());
            accountSearch.setPayType(1);
            accountSearch.setIsCancel(0);

            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            // 已产生的费用
            JSONObject generatePrice = new JSONObject();

            // 房费 餐费 商品费
            Integer roomPrice = 0;
            Integer foodPrice = 0;
            Integer goodPrice = 0;

            for (Account account : accounts) {

                Integer accountType = account.getAccountType();

                Integer price = account.getPrice();

                switch (accountType) {
                    case 1:
                        roomPrice += price;
                        break;
                    case 2:
                        foodPrice += price;
                        break;
                    case 3:
                        goodPrice += price;
                        break;
                    default:
                        roomPrice += price;
                        break;
                }

            }

            generatePrice.put("roomPrice", roomPrice);
            generatePrice.put("foodPrice", foodPrice);
            generatePrice.put("goodPrice", goodPrice);
            generatePrice.put("des", "已产生的费用");

            JSONObject res = new JSONObject();
            res.put("generatePrice", generatePrice);
            res.put("forecastRoomPrice", forecastRoomPrice);

            responseData.setData(res);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData hotelLiveInfo(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        ArrayList<String> timeList = new ArrayList<>();
        timeList.add("1 month");
        timeList.add("7 day");
        timeList.add("3 day");
        timeList.add("1 day");
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            JSONObject res = new JSONObject();
            for (String t : timeList) {
                List<Map<String, Object>> maps = registDao.selectHoteLiveInfo(t);
                for (Map<String, Object> map : maps) {
                    String contactPhone = (String) map.get("contact_phone");
                    map.put("contact_phone", RSA.encrypt(contactPhone));
                }
                res.put(t, maps);
            }
            responseData.setData(res);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData searchOtaRoomType(OtaRoomTypeSearch otaRoomTypeSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = otaRoomTypeSearch.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            otaRoomTypeSearch.setHid(user.getHid());
            Page<OtaRoomType> otaRoomTypes = otaRoomTypeDao.selectBySearch(otaRoomTypeSearch);
            responseData.setData(otaRoomTypes);
        } catch (Exception e) {
            responseData.setCode(-1);
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updateOtaRoomType(OtaRoomTypeRequest otaRoomTypeRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = otaRoomTypeRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Integer res = 0;
            OtaRoomType otaRoomType = new OtaRoomType();
            BeanUtils.copyProperties(otaRoomTypeRequest, otaRoomType);
            if (null == otaRoomTypeRequest.getRoomTypeId()) {
                throw new Exception("房型ID不能空");
            }
            OtaRoomType otaRoomTypeInfo = otaRoomTypeDao.selectById(otaRoomTypeRequest.getRoomTypeId());
            if (null == otaRoomTypeInfo) {
                otaRoomType.setCreateTime(new Date());
                otaRoomType.setCreateUserId(user.getUserId());
                otaRoomType.setCreateUserName(user.getUserName());
                otaRoomType.setHid(user.getHid());
                otaRoomType.setHotelGroupId(user.getHotelGroupId());
                res = otaRoomTypeDao.insert(otaRoomType);
            } else {
                otaRoomType.setUpdateTime(new Date());
                otaRoomType.setUpdateUserId(user.getUserId());
                otaRoomType.setUpdateUserName(user.getUserName());
                res = otaRoomTypeDao.update(otaRoomType);
            }
            if (res < 1) {
                throw new Exception("操作失败");
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData searchOtaHotelInfo(OtaHotelInfoSearch otaHotelInfoSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = otaHotelInfoSearch.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            otaHotelInfoSearch.setHid(user.getHid());
            List<OtaHotelInfo> otaHotelInfos = otaHotelInfoDao.selectBySearch(otaHotelInfoSearch);
            responseData.setData(otaHotelInfos);
        } catch (Exception e) {
            responseData.setCode(-1);
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updateOtaHotelInfo(OtaHotelInfoRequest otaHotelInfoRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = otaHotelInfoRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Integer hid = otaHotelInfoRequest.getHid();
            OtaHotelInfo otaHotelInfo = new OtaHotelInfo();
            BeanUtils.copyProperties(otaHotelInfoRequest, otaHotelInfo);
            Integer res = 0;
            if (null == hid) {
                otaHotelInfo.setCreateTime(new Date());
                otaHotelInfo.setCreateUserId(user.getUserId());
                otaHotelInfo.setCreateUserName(user.getUserName());
                otaHotelInfo.setHid(user.getHid());
                otaHotelInfo.setState(1);
                res = otaHotelInfoDao.insert(otaHotelInfo);
            } else {
                otaHotelInfo.setUpdateTime(new Date());
                otaHotelInfo.setUpdateUserId(user.getUserId());
                otaHotelInfo.setUpdateUserName(user.getUserName());
                res = otaHotelInfoDao.update(otaHotelInfo);
            }
            if (res < 1) {
                throw new Exception("操作失败");
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData addBookRoomFunc(AddBookRoomRequest addBookRoomRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            final TbUserSession user = this.getTbUserSession(addBookRoomRequest);

            Integer bookingOrderId = addBookRoomRequest.getBookingOrderId();

            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderId);

            if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())) {
                throw new Exception("未查询到订单信息");
            }

            Date date = new Date();

            String s = HotelUtils.parseDate2Str(bookingOrder.getCheckoutTime());

            String leaveTime = s.substring(10);

            Date checkinTime = HotelUtils.parseStr2Date(addBookRoomRequest.getStartTime() + " 00:00:00");

            Date checkoutTime = HotelUtils.parseStr2Date(addBookRoomRequest.getEndTime() + leaveTime);

            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setHid(user.getHid());
            Page<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);

            Map<Integer, RoomType> roomTypeMap = roomTypes.stream().collect(Collectors.toMap(RoomType::getRoomTypeId, a -> a, (k1, k2) -> k2));

            // 获取未来房情信息
            JSONObject jsonParam = new JSONObject();
            jsonParam.put("type", 2);
            jsonParam.put("startTime", addBookRoomRequest.getStartTime() + " 00:00:00");
            jsonParam.put("endTime", addBookRoomRequest.getEndTime() + leaveTime);
            jsonParam.put(ER.SESSION_TOKEN, user.getSessionId());

            Map<String, Object> findavailableRoom = turnAlwaysService.findavailableRoom(jsonParam);

            // 可用房数量
            HashMap<Integer, Integer> canUseRoomNum = (HashMap<Integer, Integer>) findavailableRoom.get("canUseRoomNum");

            List<AddBookRoomRequest.RoomType> roomTypeList = addBookRoomRequest.getRoomTypeList();
            // 添加的房价
            ArrayList<BookingOrderDailyPrice> rtPriceList = new ArrayList<>();

            for (AddBookRoomRequest.RoomType rt : roomTypeList) {

                List<AddBookRoomRequest.Price> priceList = rt.getPriceList();

                List<AddBookRoomRequest.Room> roomList = rt.getRoomList();

                Integer o = canUseRoomNum.get(rt.getRoomTypeId());
                RoomType roomType = roomTypeMap.get(rt.getRoomTypeId());
                if (roomType == null) {
                    throw new Exception("房型id:" + roomType.getRoomTypeId() + " 不正确！");
                }
                // 验证未来房情
                if (o != null) {
                    Integer canOverbooking = roomType.getCanOverbooking();
                    Integer canOverbookingNum = roomType.getCanOverbookingNum();
                    if (canOverbookingNum == null || canOverbooking != 1) {
                        canOverbookingNum = 0;
                    }
                    int saleNum = o + canOverbookingNum;
                    if (rt.getNum() > saleNum) {
                        throw new Exception(roomType.getRoomTypeName() + ":可售数量不足，当前可售：" + saleNum + "，新增预订数：" + rt.getNum());
                    }
                }

                // 已经排房
                for (AddBookRoomRequest.Room room : roomList) {

                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(bookingOrder.getBookingOrderId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(rt.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(room.getRoomInfoId());
                    bookingOrderRoomNum.setRoomNum(room.getRoomNum());
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setCheckinTime(checkinTime);
                    bookingOrderRoomNum.setCheckoutTime(checkoutTime);
                    bookingOrderRoomNum.setRowRoom(1);
                    bookingOrderRoomNum.setRateCode(rt.getRateCode());
                    bookingOrderRoomNum.setRateCodeId(rt.getRateId());
                    bookingOrderRoomNumDao.saveBookingOrderRoomNum(bookingOrderRoomNum);

                    for (AddBookRoomRequest.Price price : priceList) {
                        BookingOrderDailyPrice bodp = new BookingOrderDailyPrice();
                        bodp.setBookingOrderRoomNumId(0);
                        bodp.setHid(user.getHid());
                        bodp.setHotelGroupId(user.getHotelGroupId());
                        bodp.setPrice(price.getPrice());
                        bodp.setDailyTime(Integer.parseInt(price.getDate().replace("-", "")));
                        bodp.setRoomTypeId(rt.getRoomTypeId());
                        bodp.setRoomNumId(0);
                        bodp.setBreakNum(price.getBreakfastNum());
                        bodp.setDailyState(1);
                        bodp.setRoomNumId(room.getRoomInfoId());
                        bodp.setBookingOrderId(bookingOrderId);
                        bodp.setIsStayover(0);
                        bodp.setCreateTime(date);
                        bodp.setCreateUserId(user.getUserId());
                        bodp.setCreateUserName(user.getUserName());
                        bodp.setUpdateTime(date);
                        bodp.setUpdateUserId(user.getUserId());
                        bodp.setUpdateUserName(user.getUserName());
                        bodp.setRateCode(rt.getRateCode());
                        bodp.setRateCodeId(rt.getRateId());
                        bodp.setBookingOrderRoomNumId(bookingOrderRoomNum.getId());
                        rtPriceList.add(bodp);
                    }

                }

                // 未排房
                int noRoomNum = rt.getNum() - roomList.size();

                for (int i = 0; i < noRoomNum; i++) {
                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(bookingOrder.getBookingOrderId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(rt.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(0);
                    bookingOrderRoomNum.setRoomNum("");
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setCheckinTime(checkinTime);
                    bookingOrderRoomNum.setCheckoutTime(checkoutTime);
                    bookingOrderRoomNum.setRowRoom(1);
                    bookingOrderRoomNum.setRateCode(rt.getRateCode());
                    bookingOrderRoomNum.setRateCodeId(rt.getRateId());
                    bookingOrderRoomNumDao.saveBookingOrderRoomNum(bookingOrderRoomNum);

                    for (AddBookRoomRequest.Price price : priceList) {
                        BookingOrderDailyPrice bodp = new BookingOrderDailyPrice();
                        bodp.setBookingOrderRoomNumId(0);
                        bodp.setHid(user.getHid());
                        bodp.setHotelGroupId(user.getHotelGroupId());
                        bodp.setPrice(price.getPrice());
                        bodp.setDailyTime(Integer.parseInt(price.getDate().replace("-", "")));
                        bodp.setRoomTypeId(rt.getRoomTypeId());
                        bodp.setRoomNumId(0);
                        bodp.setBreakNum(price.getBreakfastNum());
                        bodp.setDailyState(1);
                        bodp.setIsStayover(0);
                        bodp.setBookingOrderId(bookingOrderId);
                        bodp.setCreateTime(date);
                        bodp.setCreateUserId(user.getUserId());
                        bodp.setCreateUserName(user.getUserName());
                        bodp.setUpdateTime(date);
                        bodp.setUpdateUserId(user.getUserId());
                        bodp.setUpdateUserName(user.getUserName());
                        bodp.setRateCode(rt.getRateCode());
                        bodp.setRateCodeId(rt.getRateId());
                        bodp.setBookingOrderRoomNumId(bookingOrderRoomNum.getId());
                        rtPriceList.add(bodp);
                    }
                }
            }

            bookingOrderDailyPriceDao.addPriceList(rtPriceList);

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        upaRoomTypeSummary(bookingOrder.getBookingOrderId(), user.getHid());
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData delBookRoomFunc(DelBookRoomRequest delBookRoomRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = delBookRoomRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            List<BookingOrderRoomNumSearch> bookingOrderRoomNumSearches = delBookRoomRequest.getBookingOrderRoomNumSearches();

            BookingOrder bookingOrder = bookingOrderDao.selectById(delBookRoomRequest.getBookingOrderId());

            ArrayList<BookingOrderRoomNum> delRoom = new ArrayList<>();
            ArrayList<BookingOrderDailyPrice> delPrice = new ArrayList<>();

            if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())) {
                throw new Exception("未查询到订单信息");
            }

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            bookingOrderRoomNumSearch.setOrderState(1);

            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            Map<Integer, BookingOrderRoomNum> roomMap = bookingOrderRoomNums.stream().collect(Collectors.toMap(BookingOrderRoomNum::getId, a -> a, (k1, k2) -> k2));

            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();

            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setBookingOrderId(bookingOrder.getBookingOrderId());

            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, List<BookingOrderDailyPrice>> priceMap = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getBookingOrderRoomNumId));

            for (BookingOrderRoomNumSearch bors : bookingOrderRoomNumSearches) {
                BookingOrderRoomNum bookingOrderRoomNum = roomMap.get(bors.getId());

                // 预订房型
                if (bookingOrderRoomNum == null) {
                    continue;
                }

                if (bookingOrderRoomNum.getIsCheckin() != 0) {
                    continue;
                }
                delRoom.add(bookingOrderRoomNum);

                List<BookingOrderDailyPrice> bookingOrderDailyPrices1 = priceMap.get(bors.getId());

                if (bookingOrderDailyPrices1 != null) {
                    delPrice.addAll(bookingOrderDailyPrices1);
                }

            }

            if (delRoom.size() > 0) {
                bookingOrderRoomNumDao.deleteBookingOrderRoomNumList(delRoom);
                if (delPrice.size() > 0) {
                    bookingOrderDailyPriceDao.deletePriceList(delPrice);
                }
            }

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        upaRoomTypeSummary(bookingOrder.getBookingOrderId(), user.getHid());
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 修改预订单时间
     *
     * @param upaBookRoomRequest
     * @return
     */
    @Override
    public ResponseData upaBookRoomFunc(UpaBookRoomRequest upaBookRoomRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = upaBookRoomRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            List<BookingOrderRoomNumSearch> bookingOrderRoomNumSearches = upaBookRoomRequest.getIds();

            BookingOrder bookingOrder = bookingOrderDao.selectById(upaBookRoomRequest.getBookingOrderId());

            ArrayList<BookingOrderRoomNum> upaRoom = new ArrayList<>();
            ArrayList<BookingOrderDailyPrice> delPrice = new ArrayList<>();
            ArrayList<BookingOrderDailyPrice> addPrice = new ArrayList<>();

            if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())) {
                throw new Exception("未查询到订单信息");
            }

            Date date = new Date();

            String s = HotelUtils.parseDate2Str(bookingOrder.getCheckoutTime());

            String leaveTime = s.substring(10);

            Date checkinTime = HotelUtils.parseStr2Date(upaBookRoomRequest.getStartTime().substring(0, 10) + " 00:00:00");

            Date checkoutTime = HotelUtils.parseStr2Date(upaBookRoomRequest.getEndTime().substring(0, 10) + leaveTime);


            // 房价
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, List<BookingOrderDailyPrice>> roomTypePrices = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getBookingOrderRoomNumId));


            // 开始结束营业日
            Integer checkinBusinessDay = HotelUtils.parseDate2Int(checkinTime);
            Integer checkoutBusinessDay = HotelUtils.parseDate2Int(checkoutTime);

            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setHid(user.getHid());
            Page<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);

            Map<Integer, RoomType> roomTypeMap = roomTypes.stream().collect(Collectors.toMap(RoomType::getRoomTypeId, a -> a, (k1, k2) -> k2));

            // 房间信息
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            bookingOrderRoomNumSearch.setOrderState(1);
            bookingOrderRoomNumSearch.setIsCheckin(0);
            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            Map<Integer, BookingOrderRoomNum> orderRoomNumMap = bookingOrderRoomNums.stream().collect(Collectors.toMap(BookingOrderRoomNum::getId, a -> a, (k1, k2) -> k2));

            // 预订房间信息
            bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setOrderState(1);
            bookingOrderRoomNumSearch.setHid(user.getHid());

            long outTime = HotelUtils.parseStr2Date(upaBookRoomRequest.getEndTime().substring(0, 10) + " 00:00:00").getTime() / 1000;

            ArrayList<Long> checkinLongs = new ArrayList<>();
            checkinLongs.add(null);
            checkinLongs.add(outTime);
            bookingOrderRoomNumSearch.setCheckinTime(checkinLongs);
            bookingOrderRoomNumSearch.setRowRoom(1);

            ArrayList<Long> checkoutLongs = new ArrayList<>();
            checkoutLongs.add(checkinTime.getTime() / 1000);
            bookingOrderRoomNumSearch.setCheckoutTime(checkoutLongs);
            Page<BookingOrderRoomNum> nowBook = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            Map<Integer, List<BookingOrderRoomNum>> bookRoomMap = nowBook.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getRoomNumId));


            // 未来房情需要验证的数据
            HashMap<String, HashMap<Integer, Integer>> checkInMap = new HashMap<>();

            HashMap<Integer, HashMap<Integer, Integer>> checkInUseMap = new HashMap<>();

            // 循环查询前端传过来的数据
            // 取开始时间，和离店时间
            for (BookingOrderRoomNumSearch borns : bookingOrderRoomNumSearches) {

                // 小订单信息
                BookingOrderRoomNum bookingOrderRoomNum = orderRoomNumMap.get(borns.getId());

                if (bookingOrderRoomNum == null || bookingOrderRoomNum.getIsCheckin() > 0) {
                    continue;
                }

                // 开始 结束时间
                Integer ordercheckin = HotelUtils.parseDate2Int(bookingOrderRoomNum.getCheckinTime());
                // 离店时间
                Integer ordercheckout = HotelUtils.parseDate2Int(bookingOrderRoomNum.getCheckoutTime());

                if (ordercheckin.equals(checkinBusinessDay) && ordercheckout.equals(checkoutBusinessDay)) {
                    continue;
                }

                // 如果开始时间 大于原有的离店时间、或者离店时间小于原有的开始时间
                if (checkinBusinessDay >= ordercheckout || checkoutBusinessDay <= ordercheckin) {

                    String s1 = checkinBusinessDay + "-" + checkoutBusinessDay;
                    HashMap<Integer, Integer> canUseRoom = checkInMap.get(s1);
                    if (canUseRoom == null) {
                        // 获取未来房情信息
                        JSONObject jsonParam = new JSONObject();
                        jsonParam.put("type", 2);
                        jsonParam.put("startTime", upaBookRoomRequest.getStartTime().substring(0, 10));
                        jsonParam.put("endTime", upaBookRoomRequest.getEndTime().substring(0, 10));
                        jsonParam.put("bookingOrderId", bookingOrder.getBookingOrderId());
                        jsonParam.put(ER.SESSION_TOKEN, user.getSessionId());

                        Map<String, Object> findavailableRoom = turnAlwaysService.findavailableRoom(jsonParam);
                        // 可用房数量
                        HashMap<Integer, Integer> canUseRoomNum = (HashMap<Integer, Integer>) findavailableRoom.get("canUseRoomNum");
                        checkInMap.put(s1, canUseRoomNum);


                    }

                    HashMap<Integer, Integer> integerIntegerHashMap = checkInUseMap.get(checkinBusinessDay);
                    if (integerIntegerHashMap == null) {
                        integerIntegerHashMap = new HashMap<>();
                    }

                    integerIntegerHashMap.put(bookingOrderRoomNum.getRoomTypeId(), 0);
                    checkInUseMap.put(checkinBusinessDay, integerIntegerHashMap);

                } else {
                    if (checkinBusinessDay < ordercheckin) {
                        String s1 = checkinBusinessDay + "-" + ordercheckin;
                        HashMap<Integer, Integer> canUseRoom = checkInMap.get(s1);
                        if (canUseRoom == null) {
                            // 获取未来房情信息
                            JSONObject jsonParam = new JSONObject();
                            jsonParam.put("type", 2);
                            jsonParam.put("startTime", upaBookRoomRequest.getStartTime().substring(0, 10));
                            jsonParam.put("endTime", HotelUtils.parseDate2Str(bookingOrderRoomNum.getCheckinTime()) + " 00:00:00");
                            jsonParam.put(ER.SESSION_TOKEN, user.getSessionId());

                            Map<String, Object> findavailableRoom = turnAlwaysService.findavailableRoom(jsonParam);

                            // 可用房数量
                            HashMap<Integer, Integer> canUseRoomNum = (HashMap<Integer, Integer>) findavailableRoom.get("canUseRoomNum");
                            checkInMap.put(s1, canUseRoomNum);
                        }

                        HashMap<Integer, Integer> integerIntegerHashMap = checkInUseMap.get(ordercheckin);
                        if (integerIntegerHashMap == null) {
                            integerIntegerHashMap = new HashMap<>();
                        }

                        integerIntegerHashMap.put(bookingOrderRoomNum.getRoomTypeId(), 0);
                        checkInUseMap.put(ordercheckin, integerIntegerHashMap);

                    }


                    if (checkoutBusinessDay > ordercheckout) {
                        String s1 = ordercheckout + "-" + checkoutBusinessDay;
                        HashMap<Integer, Integer> canUseRoom = checkInMap.get(s1);
                        if (canUseRoom == null) {
                            // 获取未来房情信息
                            JSONObject jsonParam = new JSONObject();
                            jsonParam.put("type", 2);
                            jsonParam.put("startTime", HotelUtils.parseDate2Str(bookingOrderRoomNum.getCheckoutTime()).substring(0, 10));
                            jsonParam.put("endTime", upaBookRoomRequest.getEndTime().substring(0, 10));
                            jsonParam.put(ER.SESSION_TOKEN, user.getSessionId());

                            Map<String, Object> findavailableRoom = turnAlwaysService.findavailableRoom(jsonParam);

                            // 可用房数量
                            HashMap<Integer, Integer> canUseRoomNum = (HashMap<Integer, Integer>) findavailableRoom.get("canUseRoomNum");

                            checkInMap.put(s1, canUseRoomNum);

                        }

                        HashMap<Integer, Integer> integerIntegerHashMap = checkInUseMap.get(checkoutBusinessDay);
                        if (integerIntegerHashMap == null) {
                            integerIntegerHashMap = new HashMap<>();
                        }

                        integerIntegerHashMap.put(bookingOrderRoomNum.getRoomTypeId(), 0);
                        checkInUseMap.put(checkoutBusinessDay, integerIntegerHashMap);

                    }
                }


                upaRoom.add(bookingOrderRoomNum);

            }

            // 需要修改的预订房间信息
            ArrayList<BookingOrderRoomNum> reUpaRoom = new ArrayList<>();

            List<String> dayList = HotelUtils.getAllDayListBetweenDate(upaBookRoomRequest.getStartTime(), upaBookRoomRequest.getEndTime());

            for (BookingOrderRoomNum born : upaRoom) {

                // 开始 结束时间
                Integer ordercheckin = HotelUtils.parseDate2Int(born.getCheckinTime());
                // 离店时间
                Integer ordercheckout = HotelUtils.parseDate2Int(born.getCheckoutTime());

                String checkInKey = checkinBusinessDay + "-" + ordercheckin;
                String checkOutKey = ordercheckout + "-" + checkoutBusinessDay;

                Integer roomTypeId = born.getRoomTypeId();

                RoomType roomType = roomTypeMap.get(roomTypeId);
                // 可超预定数量
                Integer canOverbooking = roomType.getCanOverbooking();
                Integer canOverbookingNum = roomType.getCanOverbookingNum();
                if (canOverbookingNum == null || canOverbooking != 1) {
                    canOverbookingNum = 0;
                }

                List<BookingOrderDailyPrice> borps = roomTypePrices.get(born.getId());
                Map<Integer, BookingOrderDailyPrice> roomPriceMap = borps.stream().collect(Collectors.toMap(BookingOrderDailyPrice::getDailyTime, a -> a, (k1, k2) -> k2));
                // 最后一条价格数据为模板、、 添加需要
                BookingOrderDailyPrice bookingOrderDailyPrice = borps.get(borps.size() - 1);

                for (BookingOrderDailyPrice borp : borps) {

                    // 已过夜审不可删除
                    if (borp.getDailyState() == 0) {
                        continue;
                    }

                    Integer dailyTime = borp.getDailyTime();

                    // 日期小于开始时间
                    if (dailyTime < checkinBusinessDay) {
                        delPrice.add(borp);
                    } else if (dailyTime >= checkoutBusinessDay) {
                        delPrice.add(borp);
                    }

                }

                // 添加房价
                for (String day : dayList) {

                    Integer dayInt = Integer.parseInt(day.replace("-", ""));

                    BookingOrderDailyPrice bookingOrderDailyPrice1 = roomPriceMap.get(dayInt);
                    if (bookingOrderDailyPrice1 != null) {
                        continue;
                    }

                    BookingOrderDailyPrice bodp = new BookingOrderDailyPrice();
                    bodp.setHid(user.getHid());
                    bodp.setHotelGroupId(user.getHotelGroupId());
                    bodp.setPrice(bookingOrderDailyPrice.getPrice());
                    bodp.setBookingOrderId(bookingOrder.getBookingOrderId());
                    bodp.setDailyTime(dayInt);
                    bodp.setRoomTypeId(roomTypeId);
                    bodp.setBreakNum(bookingOrderDailyPrice.getBreakNum());
                    bodp.setDailyState(1);
                    bodp.setIsStayover(0);
                    bodp.setCreateTime(date);
                    bodp.setCreateUserId(user.getUserId());
                    bodp.setCreateUserName(user.getUserName());
                    bodp.setUpdateTime(date);
                    bodp.setUpdateUserId(user.getUserId());
                    bodp.setUpdateUserName(user.getUserName());
                    bodp.setRoomNumId(born.getRoomNumId());
                    bodp.setBookingOrderRoomNumId(born.getId());

                    addPrice.add(bodp);

                }

                // 如果开始时间 大于原有的离店时间、或者离店时间小于原有的开始时间
                if (checkinBusinessDay >= ordercheckout || checkoutBusinessDay <= ordercheckin) {

                    String s1 = checkinBusinessDay + "-" + checkoutBusinessDay;

                    // 可用数量
                    HashMap<Integer, Integer> canUseRoom = checkInMap.get(s1);
                    Integer integer = canUseRoom.get(roomTypeId);

                    // 房型使用情况
                    HashMap<Integer, Integer> rooTypeUseNum = checkInUseMap.get(checkinBusinessDay);

                    Integer num = rooTypeUseNum.get(roomTypeId);
                    num++;

                    int i = integer + canOverbookingNum - num;
                    if (i < 0) {
                        throw new Exception(roomType.getRoomTypeName() + ":" + checkinBusinessDay + "~" + checkoutBusinessDay + "内可用数量不足");
                    }
                    rooTypeUseNum.put(roomTypeId, num);
                    checkInUseMap.put(checkinBusinessDay, rooTypeUseNum);

                } else {

                    // 开始的可用房型 不等于空 说明已经查询过，需要验证
                    HashMap<Integer, Integer> checkInCanUseRoom = checkInMap.get(checkInKey);
                    if (checkInCanUseRoom != null) {

                        Integer integer = checkInCanUseRoom.get(roomTypeId);
                        // 房型使用情况
                        HashMap<Integer, Integer> rooTypeUseNum = checkInUseMap.get(ordercheckin);

                        Integer num = rooTypeUseNum.get(roomTypeId);
                        num++;
                        int i = integer + canOverbookingNum - num;
                        if (i < 0) {
                            throw new Exception(roomType.getRoomTypeName() + ":" + checkinBusinessDay + "~" + ordercheckin + "内可用数量不足");
                        }

                        rooTypeUseNum.put(roomTypeId, num);
                        checkInUseMap.put(checkinBusinessDay, rooTypeUseNum);

                        Set<Integer> keys = checkInUseMap.keySet();
                        // 使用数量：如果订单开始时间 小于 当前订单的开始时间，则 使用数量 +1
                        for (Integer key : keys) {

                            if (key >= ordercheckin) {
                                continue;
                            }
                            HashMap<Integer, Integer> rooTypeUseNumCh = checkInUseMap.get(key);
                            Integer integer1 = rooTypeUseNumCh.get(roomTypeId);
                            integer1++;
                            rooTypeUseNumCh.put(roomTypeId, integer1);
                            checkInUseMap.put(key, rooTypeUseNum);
                        }

                    }

                    HashMap<Integer, Integer> checkOutCanUseRoom = checkInMap.get(checkOutKey);
                    if (checkOutCanUseRoom != null) {
                        Integer integer = checkOutCanUseRoom.get(roomTypeId);
                        // 房型使用情况
                        HashMap<Integer, Integer> rooTypeUseNum = checkInUseMap.get(checkoutBusinessDay);

                        Integer num = rooTypeUseNum.get(roomTypeId);
                        num++;

                        int i = integer + canOverbookingNum - num;
                        if (i < 0) {
                            throw new Exception(roomType.getRoomTypeName() + ":" + ordercheckout + "~" + checkoutBusinessDay + "内可用数量不足");
                        }

                        rooTypeUseNum.put(roomTypeId, num);
                        checkInUseMap.put(checkoutBusinessDay, rooTypeUseNum);

                        Set<Integer> keys = checkInUseMap.keySet();
                        // 使用数量：如果订单开始时间 大于 当前订单的开始时间，则 使用数量 +1
                        for (Integer key : keys) {

                            if (key <= checkoutBusinessDay) {
                                continue;
                            }
                            HashMap<Integer, Integer> rooTypeUseNumCh = checkInUseMap.get(key);
                            Integer integer1 = rooTypeUseNumCh.get(roomTypeId);
                            integer1++;
                            rooTypeUseNumCh.put(roomTypeId, integer1);
                            checkInUseMap.put(key, rooTypeUseNum);
                        }
                    }
                }

                Integer roomNumId = born.getRoomNumId();

                // 分房的处理
                if (roomNumId != null && roomNumId > 0) {
                    List<BookingOrderRoomNum> bookingOrderRoomNums1 = bookRoomMap.get(roomNumId);

                    if (bookingOrderRoomNums1 != null && bookingOrderRoomNums1.size() > 0) {

                        // 查询当前时段内，房间使用数量的集合，默认取第一个
                        BookingOrderRoomNum roomNum = bookingOrderRoomNums1.get(0);
                        Integer id = roomNum.getId();

                        // 当前房间的使用状态，默认未被使用
                        Boolean isUse = false;

                        // 如果查出当前的子订单id 和便利的子订单id 重复，说明是一条数据，不做对比
                        if (born.getId().equals(id)) {
                            // 如果还存在其他的订单，说明已经被占用
                            if (bookingOrderRoomNums1.size() > 1) {
                                roomNum = bookingOrderRoomNums1.get(1);
                                isUse = true;
                            }
                        } else {
                            // 订单id不一致，说明也被占用
                            isUse = true;
                        }
                        // 已经被占用，则抛出异常
                        if (isUse) {
                            throw new Exception(roomNum.getRoomNum() + "：" + HotelUtils.parseDate2Str(roomNum.getCheckinTime()).substring(0, 10) + "-" + HotelUtils.parseDate2Str(roomNum.getCheckoutTime()) + "已被占用");
                        }

                    }

                }

                // 房价相关的修改
                born.setCheckinTime(checkinTime);
                born.setCheckoutTime(checkoutTime);
                reUpaRoom.add(born);

            }

            ArrayList<BookingOrderDailyPrice> bookingOrderDailyPrices1 = new ArrayList<>();
            bookTransactionService.updateBookRoomFunc(reUpaRoom, addPrice, delPrice, bookingOrderDailyPrices1);


            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        upaRoomTypeSummary(bookingOrder.getBookingOrderId(), user.getHid());
                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {
            log.error("",e);
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData planRoomFunc(PlanBookRoomRequest planBookRoomRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = planBookRoomRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);


            BookingOrder bookingOrder = bookingOrderDao.selectById(planBookRoomRequest.getBookingOrderId());
            if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())) {
                throw new Exception("未查询到订单信息");
            }

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setHid(user.getHid());
            bookingOrderRoomNumSearch.setBookingOrderId(planBookRoomRequest.getBookingOrderId());
            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            Map<Integer, BookingOrderRoomNum> roomNumMap = bookingOrderRoomNums.stream().collect(Collectors.toMap(BookingOrderRoomNum::getId, a -> a, (k1, k2) -> k2));

            ArrayList<BookingOrderRoomNum> upaRoom = new ArrayList<>();

            List<BookingOrderRoomNum> rooms = planBookRoomRequest.getRoomList();

            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setDailyState(1);
            bookingOrderDailyPriceSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
            Map<Integer, List<BookingOrderDailyPrice>> collect = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getBookingOrderRoomNumId));

            ArrayList<BookingOrderDailyPrice> upaPriceList = new ArrayList<>();

            for (BookingOrderRoomNum born : rooms) {
                Integer id = born.getId();
                BookingOrderRoomNum bookingOrderRoomNum = roomNumMap.get(id);

                if (bookingOrderRoomNum == null) {
                    throw new Exception("子订单：" + id + "不存在。");
                }

                BookingOrderRoomNum bookingOrderRoomNum1 = new BookingOrderRoomNum();
                bookingOrderRoomNum1.setRoomNum(born.getRoomNum());
                bookingOrderRoomNum1.setRoomNumId(born.getRoomNumId());
                bookingOrderRoomNum1.setId(id);
                bookingOrderRoomNum1.setRoomTypeId(born.getRoomTypeId());
                if (born.getRoomNumId() != null && born.getRoomNumId() > 0) {
                    bookingOrderRoomNum1.setRowRoom(1);
                } else {
                    bookingOrderRoomNum1.setRowRoom(0);
                }

                List<BookingOrderDailyPrice> priceList = collect.get(id);

                for (BookingOrderDailyPrice borp : priceList) {
                    borp.setRoomNumId(born.getRoomNumId());
                    borp.setRoomTypeId(born.getRoomTypeId());
                    upaPriceList.add(borp);

                }


                upaRoom.add(bookingOrderRoomNum1);

            }
            ArrayList<BookingOrderDailyPrice> objects = new ArrayList<>();
            bookTransactionService.updateBookRoomFunc(upaRoom, objects, objects, upaPriceList);
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {
            log.error("",e);
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updatePriceOrBreakFunc(UpdatePriceOrBreak updatePriceOrBreak) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = updatePriceOrBreak.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Integer registId = updatePriceOrBreak.getRegistId();

            Integer bookingOrderId = updatePriceOrBreak.getBookingOrderId();

            if (registId == null && bookingOrderId == null) {
                throw new Exception("订单号和登记单号必须传一个");
            }

            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setDailyState(1);
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            if (bookingOrderId != null) {
                BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderId);
                if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())) {
                    throw new Exception("未查询到订单信息");
                }

                bookingOrderDailyPriceSearch.setBookingOrderId(bookingOrderId);
            } else {
                Regist regist = registDao.selectById(registId);
                if (regist == null || !regist.getHid().equals(user.getHid())) {
                    throw new Exception("未查询到登记单信息");
                }

                if (regist.getTeamCodeId() > 0) {
                    Integer teamCodeId = regist.getTeamCodeId();
                    RegistSearch registSearch = new RegistSearch();
                    registSearch.setTeamCodeId(teamCodeId);
                    registSearch.setHid(user.getHid());
                    registSearch.setState(0);
                    List<Regist> regists = registDao.selectBySearch(registSearch);

                    String ids = "";
                    for (Regist rg : regists) {
                        ids += rg.getRegistId() + ",";
                    }
                    ids += "-3";
                    bookingOrderDailyPriceSearch.setRegistIds(ids);
                } else {
                    bookingOrderDailyPriceSearch.setRegistId(regist.getRegistId());
                }

            }

            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, BookingOrderDailyPrice> priceMap = bookingOrderDailyPrices.stream().collect(Collectors.toMap(BookingOrderDailyPrice::getId, a -> a, (k1, k2) -> k2));

            List<BookingOrderDailyPrice> priceList = updatePriceOrBreak.getPriceList();

            ArrayList<BookingOrderDailyPrice> upaList = new ArrayList<>();


            Integer type = updatePriceOrBreak.getType();

            for (BookingOrderDailyPrice dodp : priceList) {

                BookingOrderDailyPrice bookingOrderDailyPrice = priceMap.get(dodp.getId());

                if (bookingOrderDailyPrice == null) {
                    continue;
                }

                BookingOrderDailyPrice upaPrice = new BookingOrderDailyPrice();
                upaPrice.setId(bookingOrderDailyPrice.getId());
                if (type == 1) {
                    upaPrice.setPrice(dodp.getPrice());
                } else {
                    upaPrice.setBreakNum(dodp.getBreakNum());
                }

                upaList.add(upaPrice);

            }


            bookTransactionService.updatePriceList(upaList);

        } catch (Exception e) {
            log.error("",e);
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    private void upaRoomTypeSummary(Integer bookingOrderId, Integer hid) {
        try {

            if (bookingOrderId == null || hid == null) {
                return;
            }

            BookingOrder bookingOrder = new BookingOrder();

            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setHid(hid);
            Page<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);

            Map<Integer, RoomType> roomTypeMap = roomTypes.stream().collect(Collectors.toMap(RoomType::getRoomTypeId, a -> a, (k1, k2) -> k2));

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingOrderId);
            bookingOrderRoomNumSearch.setOrderState(1);

            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            Map<Integer, List<BookingOrderRoomNum>> collect = new HashMap<>();

            BookingOrderRoomNum bookingOrderRoomNum = bookingOrderRoomNums.get(0);
            long checkinTime = bookingOrderRoomNum.getCheckinTime().getTime();
            long checkoutTime = bookingOrderRoomNum.getCheckoutTime().getTime();


            int checkIn = 0;
            int checkIn1 = 0;

            int checkOut = 0;

            for (BookingOrderRoomNum bo : bookingOrderRoomNums) {

                Integer isCheckin = bo.getIsCheckin();
                Integer isCheckout = bo.getIsCheckout();

                if (isCheckin == 0) {
                    checkIn++;
                } else {
                    checkIn1++;
                }

                if (isCheckout == 0) {
                    checkOut++;
                }

                Integer roomTypeId = bo.getRoomTypeId();

                List<BookingOrderRoomNum> brs = collect.get(roomTypeId);

                if (brs == null) {
                    brs = new ArrayList<>();
                }

                brs.add(bo);

                collect.put(roomTypeId, brs);

                // 日期比对
                long in = bo.getCheckinTime().getTime();
                if (in < checkinTime) {
                    checkinTime = in;
                }
                long out = bo.getCheckoutTime().getTime();
                if (out > checkoutTime) {
                    checkoutTime = out;
                }
            }

            StringBuilder bookingSummary = new StringBuilder();

            // 先验证是否有未入住
            if (checkIn > 0) {

                // 如有已入住的订单，则改成部分入住。没有则改成全部入住
                if (checkIn1 > 0) {
                    bookingOrder.setOrderStatus(BOOK.STA_BFRZ);
                } else {
                    bookingOrder.setOrderStatus(BOOK.STA_YX);
                }

            } else {

                // 如果有没有结账的。则改成全部入住。没有未结账的，改完入住完成。
                if (checkOut > 0) {
                    bookingOrder.setOrderStatus(BOOK.STA_QBRZ);

                } else {
                    bookingOrder.setOrderStatus(BOOK.STA_RZWC);
                }

            }

            Set<Integer> integers = collect.keySet();

            for (Integer key : integers) {

                RoomType roomType = roomTypeMap.get(key);
                List<BookingOrderRoomNum> bookingOrderRoomNums1 = collect.get(key);

                bookingSummary.append(roomType.getRoomTypeName());
                bookingSummary.append("*");
                bookingSummary.append(bookingOrderRoomNums1.size());
                bookingSummary.append("间。");

            }

            bookingOrder.setCheckinTime(new Date(checkinTime));
            bookingOrder.setCheckoutTime(new Date(checkoutTime));
            bookingOrder.setBookingOrderId(bookingOrderId);
            bookingOrder.setRoomTypeSummary(bookingSummary.toString());
            bookingOrder.setRoomCount(bookingOrderRoomNums.size());
            bookingOrderDao.editBookingOrder(bookingOrder);

        } catch (Exception e) {
            log.error("",e);
        }
    }

    @Override
    public ResponseData copyPrice() {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setOrderState(1);
            bookingOrderRoomNumSearch.setIsCheckin(0);
            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            Integer integer = HotelUtils.parseDate2Int(new Date());

            for (BookingOrderRoomNum born : bookingOrderRoomNums) {

                if (born.getCheckinTime() == null) {
                    continue;
                }
                Integer integer1 = HotelUtils.parseDate2Int(born.getCheckinTime());
                if (integer1 + 1 < integer) {
                    continue;
                }

                BookingOrderDailyPriceSearch bookingOrderDailyPrice = new BookingOrderDailyPriceSearch();
                bookingOrderDailyPrice.setHid(born.getHid());
                bookingOrderDailyPrice.setBookingOrderId(born.getBookingOrderId());
                bookingOrderDailyPrice.setBookingOrderRoomNumId(born.getId());
                Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPrice);
                if (bookingOrderDailyPrices != null && bookingOrderDailyPrices.size() > 0) {
                    continue;
                }

                BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
                bookingOrderDailyPriceSearch.setHid(born.getHid());
                bookingOrderDailyPriceSearch.setBookingOrderId(born.getBookingOrderId());
                bookingOrderDailyPriceSearch.setRoomTypeId(born.getRoomTypeId());
                bookingOrderDailyPriceSearch.setBookingOrderRoomNumId(0);
                Page<BookingOrderDailyPrice> orderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

                ArrayList<BookingOrderDailyPrice> addPrices = new ArrayList<>();

                for (BookingOrderDailyPrice bodp : orderDailyPrices) {

                    BookingOrderDailyPrice bookingOrderDailyPrice1 = new BookingOrderDailyPrice();

                    BeanUtils.copyProperties(bodp, bookingOrderDailyPrice1);
                    bookingOrderDailyPrice1.setId(null);
                    bookingOrderDailyPrice1.setBookingOrderRoomNumId(born.getId());
                    addPrices.add(bookingOrderDailyPrice1);

                }

                if (addPrices.size() > 0) {
                    bookingOrderDailyPriceDao.addPriceList(addPrices);
                }

            }


        } catch (Exception e) {
            log.error("",e);
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getHotelTodayComingBookingList(TodayComingRequest todayComingRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = todayComingRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setHid(user.getHid());
            bookingOrderRoomNumSearch.setIsCheckin(todayComingRequest.getIsCheckin());
            bookingOrderRoomNumSearch.setOrderState(todayComingRequest.getOrderState());
            bookingOrderRoomNumSearch.setSearchValue(todayComingRequest.getSearchValue());
            if (todayComingRequest.getCheckinTime() != null) {
                ArrayList<Long> longs = new ArrayList<>();
                todayComingRequest.getCheckinTime().forEach(t -> {
                    if (t != null) {
                        longs.add(t / 1000);
                    } else {
                        longs.add(null);
                    }
                });
                bookingOrderRoomNumSearch.setCheckinTime(longs);
            }
            if (todayComingRequest.getCheckoutTime() != null) {
                ArrayList<Long> longs = new ArrayList<>();
                todayComingRequest.getCheckoutTime().forEach(t -> {
                    if (t != null) {
                        longs.add(t / 1000);
                    } else {
                        longs.add(null);
                    }
                });
                bookingOrderRoomNumSearch.setCheckoutTime(longs);
            }
            Page<BookingOrderRoomNumView> bookingOrderRoomNumViews = bookingOrderRoomNumDao.selectBySearchView(bookingOrderRoomNumSearch);

            if (null != todayComingRequest.getIsGroup() && todayComingRequest.getIsGroup() == 1) {
                responseData.setData(bookingOrderRoomNumViews);
                return responseData;
            }

            Map<Integer, List<BookingOrderRoomNumView>> collect = bookingOrderRoomNumViews.stream().collect(Collectors.groupingBy(BookingOrderRoomNumView::getBookingOrderId));
            Page<TodayComingView> todayComingViewList = new Page<>();
            for (Map.Entry<Integer, List<BookingOrderRoomNumView>> item : collect.entrySet()
            ) {
                TodayComingView todayComingView = new TodayComingView();
                List<BookingOrderRoomNumView> value = item.getValue();
                todayComingView.setBookingName(value.get(0).getBookingName());
                todayComingView.setBookingPhone(value.get(0).getBookingPhone());
                todayComingView.setBookingOrderId(value.get(0).getBookingOrderId());
                todayComingView.setResourceId(value.get(0).getResourceId());
                todayComingView.setCheckinTime(value.get(0).getCheckinTime());
                todayComingView.setCheckoutTime(value.get(0).getCheckoutTime());
                todayComingView.setCreateTime(value.get(0).getCheckinTime());
                todayComingView.setOrderState(value.get(0).getOrderState());
                todayComingView.setOrderType(value.get(0).getOrderType());
                todayComingView.setCreateTime(value.get(0).getCreateTime());
                todayComingView.setCreateUserName(value.get(0).getCreateUserName());
                todayComingView.setKeepTime(value.get(0).getKeepTime());
                todayComingView.setFromType(value.get(0).getFromType());
                todayComingView.setRemark(value.get(0).getRemark());
                todayComingView.setOrderStatus(value.get(0).getOrderStatus());
                List<TodayComingView.RoomTypeInfo> roomTypeInfos = new ArrayList<>();
                //再根据房型进行分组
                Map<Integer, List<BookingOrderRoomNumView>> collect1 = value.stream().collect(Collectors.groupingBy(BookingOrderRoomNumView::getRoomTypeId));
                for (Map.Entry<Integer, List<BookingOrderRoomNumView>> entry : collect1.entrySet()) {
                    List<BookingOrderRoomNumView> roomTypeList = entry.getValue();
                    TodayComingView.RoomTypeInfo roomTypeInfo = new TodayComingView.RoomTypeInfo();
                    roomTypeInfo.setRoomTypeId(roomTypeList.get(0).getRoomTypeId());
                    roomTypeInfo.setRoomNum(roomTypeList.size());
                    List<TodayComingView.RoomInfo> roomInfos = new ArrayList<>();
                    for (int i = 0; i < roomTypeList.size(); i++) {
                        TodayComingView.RoomInfo roomInfo = new TodayComingView.RoomInfo();
                        roomInfo.setRoomNum(roomTypeList.get(i).getRoomNum());
                        roomInfo.setRoomNumId(roomTypeList.get(i).getRoomNumId());
                        roomInfo.setRateCodeId(roomTypeList.get(i).getRateCodeId());
                        roomInfo.setBookingOrderRoomNumId(roomTypeList.get(i).getId());
                        roomInfos.add(roomInfo);
                    }
                    roomTypeInfo.setRoomInfoList(roomInfos);
                    roomTypeInfos.add(roomTypeInfo);
                }
                todayComingView.setRoomTypeInfoList(roomTypeInfos);
                todayComingViewList.add(todayComingView);
            }
            responseData.setData(todayComingViewList);
            JSONObject data = new JSONObject();
            data.put("orderNum", todayComingViewList.size());
            data.put("roomNum", bookingOrderRoomNumViews.size());
            responseData.setData1(data);
        } catch (Exception e) {
            log.error("",e);
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    public ResponseData searchRegistAndBook(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            RegistSearch registSearch = new RegistSearch();
            registSearch.setState(0);
            registSearch.setHid(user.getHid());
            List<Regist> regists = registDao.selectBySearch(registSearch);

            HashMap<String, JSONArray> stringJSONArrayHashMap = new HashMap<>();

            for (Regist regist : regists) {

                Integer roomNumId = regist.getRoomNumId();

                List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDateTwo(HotelUtils.parseDate2Str(regist.getCheckinTime()), HotelUtils.parseDate2Str(regist.getCheckoutTime()));

                for (int i = 0; i < allDayListBetweenDate.size(); i++) {
                    String s = allDayListBetweenDate.get(i);
                    String key = s + roomNumId;
                    JSONArray jsonArray = stringJSONArrayHashMap.get(key);
                    if (jsonArray == null) {
                        jsonArray = new JSONArray();
                    }

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("id", regist.getRegistId());
                    jsonObject.put("type", 2); // 2.在住单
                    jsonObject.put("roomInfoId", roomNumId);
                    jsonObject.put("regType", "1"); // 1.在住  2.入住当天 3.离店当天
                    jsonObject.put("name", "在住");
                    if (allDayListBetweenDate.size() == 1) {
                        jsonArray.add(jsonObject);
                        continue;
                    }
                    if (i == 0) {
                        jsonObject.put("name", "入住");
                        jsonObject.put("regType", 2);

                    } else if (i == (allDayListBetweenDate.size() - 1)) {
                        jsonObject.put("regType", 3);
                        jsonObject.put("name", "离店");
                    }
                    jsonArray.add(jsonObject);
                    stringJSONArrayHashMap.put(key, jsonArray);
                }

            }

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setRowRoom(1);
            bookingOrderRoomNumSearch.setHid(user.getHid());
            bookingOrderRoomNumSearch.setIsCheckin(0);
            bookingOrderRoomNumSearch.setOrderState(1);
            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            for (BookingOrderRoomNum born : bookingOrderRoomNums) {

                Integer roomNumId = born.getRoomNumId();

                List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDateTwo(HotelUtils.parseDate2Str(born.getCheckinTime()), HotelUtils.parseDate2Str(born.getCheckoutTime()));


                for (int i = 0; i < allDayListBetweenDate.size(); i++) {
                    String s = allDayListBetweenDate.get(i);
                    String key = s + roomNumId;
                    JSONArray jsonArray = stringJSONArrayHashMap.get(key);
                    if (jsonArray == null) {
                        jsonArray = new JSONArray();
                    }

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("id", born.getBookingOrderId());
                    jsonObject.put("bid", born.getId());
                    jsonObject.put("type", 1); // 1.预订单
                    jsonObject.put("roomInfoId", roomNumId);
                    jsonObject.put("regType", "1"); // 1.在住  2.入住当天 3.离店当天
                    jsonObject.put("name", "在住");

                    if (allDayListBetweenDate.size() == 1) {
                        jsonArray.add(jsonObject);
                        continue;
                    }
                    if (i == 0) {
                        jsonObject.put("name", "入住");
                        jsonObject.put("regType", 2);
                    } else if (i == (allDayListBetweenDate.size() - 1)) {
                        jsonObject.put("regType", 3);
                        jsonObject.put("name", "离店");
                    }
                    jsonArray.add(jsonObject);
                    stringJSONArrayHashMap.put(key, jsonArray);
                }

            }

            responseData.setData(stringJSONArrayHashMap);

        } catch (Exception e) {
            log.error("",e);
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getOrderListPriceListAndAccountList(GetOrderListPriceListAndAccountListRequest getOrderListPriceListAndAccountListRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> accountInfoResult = new HashMap<>();
        Map<String, Object> priceInfoResult = new HashMap<>();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(getOrderListPriceListAndAccountListRequest);

            if (getOrderListPriceListAndAccountListRequest.getSearchPrice() != null) {
                BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
                bookingOrderDailyPriceSearch.setHid(tbUserSession.getHid());
                if (getOrderListPriceListAndAccountListRequest.getRegistIds() != null && getOrderListPriceListAndAccountListRequest.getRegistIds().size() > 0) {
                    bookingOrderDailyPriceSearch.setRegistId(null);
                    bookingOrderDailyPriceSearch.setBookingOrderId(null);
                    String registIdsOrBookingOrderIds = HotelUtils.getRegistIdsOrBookingOrderIds(getOrderListPriceListAndAccountListRequest.getRegistIds());
                    bookingOrderDailyPriceSearch.setRegistIds(registIdsOrBookingOrderIds);

                    Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

                    Map<Integer, List<BookingOrderDailyPrice>> collect = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getRegistId));
                    for (Integer id : collect.keySet()) {
                        Map<String, Object> priceInfo = new HashMap<>();
                        List<BookingOrderDailyPrice> priceList = collect.get(id);
                        priceInfo.put("priceList", priceList);
                        for (int i = 0; i < priceList.size(); i++) {
                            BookingOrderDailyPrice bookingOrderDailyPrice = priceList.get(i);
                            if (bookingOrderDailyPrice.getDailyTime().equals(tbUserSession.getBusinessDay())) {
                                priceInfo.put("dayPrice", bookingOrderDailyPrice.getPrice());
                            }
                        }
                        //如果没有取首日价格
                        if (!priceInfo.containsKey("dayPrice")) {
                            priceInfo.put("dayPrice", bookingOrderDailyPrices.get(0).getPrice());
                        }
                        priceInfoResult.put(id.toString(), priceInfo);
                    }
                } else if (getOrderListPriceListAndAccountListRequest.getBookingOrderIds() != null && getOrderListPriceListAndAccountListRequest.getBookingOrderIds().size() > 0) {
                    bookingOrderDailyPriceSearch.setRegistId(null);
                    bookingOrderDailyPriceSearch.setBookingOrderId(null);
                    bookingOrderDailyPriceSearch.setBookingOrderIds(getOrderListPriceListAndAccountListRequest.getBookingOrderIds());
                    Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

                    Map<Integer, List<BookingOrderDailyPrice>> collect = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getBookingOrderRoomNumId));
                    for (Integer id : collect.keySet()) {
                        Map<String, Object> priceInfo = new HashMap<>();
                        List<BookingOrderDailyPrice> priceList = collect.get(id);
                        priceInfo.put("priceList", priceList);
                        for (int i = 0; i < priceList.size(); i++) {
                            BookingOrderDailyPrice bookingOrderDailyPrice = priceList.get(i);
                            if (bookingOrderDailyPrice.getDailyTime().equals(tbUserSession.getBusinessDay())) {
                                priceInfo.put("dayPrice", bookingOrderDailyPrice.getPrice());
                            }
                        }
                        priceInfoResult.put(id.toString(), priceInfo);
                    }
                }
            }

            if (getOrderListPriceListAndAccountListRequest.getSearchAccount() != null) {
                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setHid(tbUserSession.getHid());
                accountSearch.setIsCancel(0);
                List<Integer> accountStateList = new ArrayList<>();
                accountStateList.add(0);
                accountStateList.add(1);
                accountSearch.setAccountStateList(accountStateList);

                if (getOrderListPriceListAndAccountListRequest.getRegistIds() != null && getOrderListPriceListAndAccountListRequest.getRegistIds().size() > 0) {
                    accountSearch.setRegistId(null);
                    accountSearch.setBookingId(null);
                    String registIdsOrBookingOrderIds = HotelUtils.getRegistIdsOrBookingOrderIds(getOrderListPriceListAndAccountListRequest.getRegistIds());
                    accountSearch.setRegistIds(registIdsOrBookingOrderIds);
                    List<Account> accounts = accountDao.selectBySearch(accountSearch);
                    Map<Integer, List<Account>> collect = accounts.stream().collect(Collectors.groupingBy(Account::getRegistId));
                    for (Integer id : collect.keySet()) {
                        Map<String, Object> accountInfo = new HashMap<>();
                        double cost = 0.00;
                        double cash = 0.00;
                        double free = 0.00;
                        double count = 0.00;
                        ArrayList<Account> costList = new ArrayList<>();
                        ArrayList<Account> cashList = new ArrayList<>();
                        ArrayList<Account> freeList = new ArrayList<>();
                        for (int i = 0; i < collect.get(id).size(); i++) {
                            Account account = collect.get(id).get(i);

                            Integer payType = account.getPayType();
                            Integer isCancel = account.getIsCancel();
                            Integer price = account.getPrice();

                            if (payType == 1 && isCancel == 0) {
                                cost += price;
                                costList.add(account);
                            } else if (payType == 2 && isCancel == 0) {
                                cash += price;
                                cashList.add(account);
                            }

                            if ((account.getPayCodeId().equals("9100") || account.getPayCodeId().equals("9620")) && account.getThirdRefundState() == 0 && isCancel == 0) {
                                free += price;
                                freeList.add(account);
                            }
                        }
                        cash = cash - free;
                        count = cash + free - cost;
                        if (cash + free >= 0) {
                            count = cash + free - cost;
                        } else {
                            count = cash + free + cost;
                        }
                        accountInfo.put("cost", cost);
                        accountInfo.put("cash", cash);
                        accountInfo.put("free", free);
                        accountInfo.put("count", count);
                        accountInfo.put("costList", costList);
                        accountInfo.put("cashList", cashList);
                        accountInfo.put("freeList", freeList);
                        accountInfoResult.put(id.toString(), accountInfo);
                    }
                } else if (getOrderListPriceListAndAccountListRequest.getBookingOrderIds() != null && getOrderListPriceListAndAccountListRequest.getBookingOrderIds().size() > 0) {
                    accountSearch.setRegistId(null);
                    accountSearch.setBookingId(null);
                    String bookingOrderIds = HotelUtils.getRegistIdsOrBookingOrderIds(getOrderListPriceListAndAccountListRequest.getBookingOrderIds());
                    accountSearch.setBookingIds(bookingOrderIds);
                    List<Account> accounts = accountDao.selectBySearch(accountSearch);
                    Map<Integer, List<Account>> collect = accounts.stream().collect(Collectors.groupingBy(Account::getBookingId));
                    for (Integer id : collect.keySet()) {
                        Map<String, Object> accountInfo = new HashMap<>();
                        double cost = 0.00;
                        double cash = 0.00;
                        double free = 0.00;
                        double count = 0.00;
                        ArrayList<Account> costList = new ArrayList<>();
                        ArrayList<Account> cashList = new ArrayList<>();
                        ArrayList<Account> freeList = new ArrayList<>();
                        for (int i = 0; i < collect.get(id).size(); i++) {
                            Account account = collect.get(id).get(i);

                            Integer payType = account.getPayType();
                            Integer isCancel = account.getIsCancel();
                            Integer price = account.getPrice();

                            if (payType == 1 && isCancel == 0) {
                                cost += price;
                                costList.add(account);
                            } else if (payType == 2 && isCancel == 0) {
                                cash += price;
                                cashList.add(account);
                            }

                            if ((account.getPayCodeId().equals("9100") || account.getPayCodeId().equals("9620")) && account.getThirdRefundState() == 0 && isCancel == 0) {
                                free += price;
                                freeList.add(account);
                            }
                        }
                        cash = cash - free;
                        count = cash + free - cost;
                        if (cash + free >= 0) {
                            count = cash + free - cost;
                        } else {
                            count = cash + free + cost;
                        }
                        accountInfo.put("cost", cost);
                        accountInfo.put("cash", cash);
                        accountInfo.put("free", free);
                        accountInfo.put("count", count);
                        accountInfo.put("costList", costList);
                        accountInfo.put("cashList", cashList);
                        accountInfo.put("freeList", freeList);
                        accountInfoResult.put(id.toString(), accountInfo);
                    }
                }
            }
            result.put("accountInfo", accountInfoResult);
            result.put("priceInfo", priceInfoResult);
            responseData.setData(result);

        } catch (Exception e) {
            log.error("",e);
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
