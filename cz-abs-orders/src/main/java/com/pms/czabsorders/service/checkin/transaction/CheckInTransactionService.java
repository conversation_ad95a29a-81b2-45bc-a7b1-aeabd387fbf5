package com.pms.czabsorders.service.checkin.transaction;


import com.pms.czabsorders.bean.CheckInRegist;
import com.pms.czaccount.bean.account.Account;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.BookingRequest;
import com.pms.pmsorder.bean.*;
import net.sf.json.JSONArray;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 关于入住的事务处理
 */
public interface CheckInTransactionService {


    /**
     * 预定转入住
     *
     * @param bookingOrder                    预订单
     * @param bookingOrderRoomNumList         预定房间
     * @param checkInRegistMap                入住单信息
     * @param roomInfoList                    房间集合
     * @param registPersonMap                 入住人集合
     * @param bookingOrderConfigMap           入住配置信息
     * @param deleteRoomAuxiliaryRelationList 需要删除的辅助房态信息
     * @param addRoomAuxiliaryRelation        需要添加的辅助房态信息
     * @param registGroup                     团队信息
     * @param accounts                        账务信息
     * @param user                            登录信息
     * @param dayPriceMap                     价格信息
     * @throws Exception
     */
    public Map<Integer, Integer> bookingCheckInTransaction(BookingOrder bookingOrder, List<BookingOrderRoomNum> bookingOrderRoomNumList, Map<Integer, Regist> checkInRegistMap, List<RoomInfo> roomInfoList, Map<Integer, List<RegistPerson>> registPersonMap,
                                                           Map<Integer, BookingOrderConfig> bookingOrderConfigMap, List<RoomAuxiliaryRelation> deleteRoomAuxiliaryRelationList, Map<Integer, List<RoomAuxiliaryRelation>> addRoomAuxiliaryRelation,
                                                           RegistGroup registGroup, List<Account> accounts, TbUserSession user, Map<Integer, List<BookingOrderDailyPrice>> dayPriceMap) throws Exception;

    public Map<Integer, Integer> bookingCheckInTransactionNew(BookingOrder bookingOrder, List<BookingOrderRoomNum> bookingOrderRoomNumList, Map<Integer, Regist> checkInRegistMap, List<RoomInfo> roomInfoList, Map<Integer, List<RegistPerson>> registPersonMap,
                                                              Map<Integer, BookingOrderConfig> bookingOrderConfigMap, List<RoomAuxiliaryRelation> deleteRoomAuxiliaryRelationList, Map<Integer, List<RoomAuxiliaryRelation>> addRoomAuxiliaryRelation,
                                                              RegistGroup registGroup, List<Account> accounts, TbUserSession user, Map<Integer, List<BookingOrderDailyPrice>> dayPriceMap) throws Exception;


    public void updateGuestInfoTransaction(RegistPerson registPerson, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, TbUserSession user, ArrayList<Oprecord> oprecords, Oprecord oprecord) throws Exception;

    public void changeRoomTransaction(Regist regist, List<RegistPerson> registPersonList, List<RoomInfo> roomInfos, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, List<BookingOrderDailyPrice> bookingOrderDailyPrices, BookingOrderRoomNum bookingOrderRoomNum, RegistChangeRecord registChangeRecord, TbUserSession user, ArrayList<Oprecord> oprecords, Oprecord oprecord) throws Exception;

    public void updateRoomTypeTransaction(Regist regist, List<RegistPerson> registPersonList, List<RoomInfo> roomInfos, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, List<BookingOrderDailyPrice> bookingOrderDailyPrices, BookingOrderRoomNum bookingOrderRoomNum, RegistChangeRecord registChangeRecord, TbUserSession user, ArrayList<Oprecord> oprecords, Oprecord oprecord) throws Exception;


    public ArrayList<RegistPerson> addCheckinGuest(JSONArray guestList, TbUserSession user, Regist regist, Integer bookingOrderRoomNumId) throws Exception;

    public RegistPerson addCheckinGuest2(BookingRequest.Person guestList, TbUserSession user, Regist regist, Integer bookingOrderRoomNumId) throws Exception;


    /**
     * @param registGroup     集团信息
     * @param checkInRegists  入住人集合
     * @param accounts        账务集合
     * @param sumMoney        总支付金额
     * @param deleteRelations 需要删除的辅助房态
     * @return
     */
    public JSONArray blendCheckInTransaction(RegistGroup registGroup, List<CheckInRegist> checkInRegists, List<Account> accounts, int sumMoney, TbUserSession user, ArrayList<RoomAuxiliaryRelation> deleteRelations) throws Exception;

    public JSONArray blendCheckInTransaction(RegistGroup registGroup, List<CheckInRegist> checkInRegists, List<Account> accounts, int sumMoney, TbUserSession user,
                                             ArrayList<RoomAuxiliaryRelation> deleteRelations, ArrayList<HourRoomDayUse> addHourUse, ArrayList<HourRoomDayUse> upaHourUse) throws Exception;

    /**
     * 部分宾客结账
     *
     * @param accounts     部分宾客的账务信息
     * @param personList   部分宾客集合
     * @param registPerson 如果部分宾客中有住客，则需要把未结账的客人修改成主客
     * @throws Exception
     */
    public void personCheckout(List<Account> accounts, List<RegistPerson> personList, RegistPerson registPerson ,Integer eid,String userId) throws Exception;


    /**
     * 钟点转日租
     *
     * @param regist
     * @param delBookingOrderDailyPrices
     * @param insertBookingOrderDailyPrices
     * @param deleteRelations
     * @param user
     * @param oprecords
     * @throws Exception
     */
    public void hourToDayTransaction(Regist regist, List<BookingOrderDailyPrice> delBookingOrderDailyPrices, List<BookingOrderDailyPrice> insertBookingOrderDailyPrices, List<RoomAuxiliaryRelation> deleteRelations, TbUserSession user, ArrayList<Oprecord> oprecords, Account account) throws Exception;


    /**
     * 联房
     *
     * @param registGroup
     * @param registGroups
     * @param regists
     * @param accounts
     * @param addAuxRoom
     * @return
     */
    public Integer roomJion(RegistGroup registGroup, Regist mainReg, List<RegistGroup> registGroups, List<Regist> regists, List<Account> accounts, List<RoomAuxiliaryRelation> addAuxRoom, List<RegistPerson> registPeople) throws Exception;


    public void teamJion(RegistGroup registGroup, RegistGroup delRegistGroup, List<Regist> regists, List<Account> accounts, List<RegistPerson> registPeople, List<RoomAuxiliaryRelation> addAuxRoom) throws Exception;


    /**
     * 联房
     *
     * @param registGroup
     * @param registGroups
     * @param regists
     * @param accounts
     * @param addAuxRoom
     * @return
     */
    public Integer withRoom(RegistGroup registGroup, Regist mainReg, List<RegistGroup> registGroups, List<Regist> regists, List<Account> accounts, List<RoomAuxiliaryRelation> addAuxRoom, List<RegistPerson> registPeople) throws Exception;

}
