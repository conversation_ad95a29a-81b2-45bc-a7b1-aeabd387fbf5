package com.pms.czabsorders.service.order.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.*;
import com.pms.czabsorders.service.order.ItHotelService;
import com.pms.czabsorders.service.turnAlways.TurnAlwaysService;
import com.pms.czhotelfoundation.bean.hotel.HotelBaseInfo;
import com.pms.czhotelfoundation.bean.price.RoomRateCode;
import com.pms.czhotelfoundation.bean.price.RoomRateCodeSpecific;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSearch;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSpecificSearch;
import com.pms.czhotelfoundation.bean.room.*;
import com.pms.czhotelfoundation.bean.room.search.RoomFeatureSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeImageSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeSearch;
import com.pms.czhotelfoundation.bean.setting.HotelInitialData;
import com.pms.czhotelfoundation.bean.setting.search.HotelInitialDataSearch;
import com.pms.czhotelfoundation.dao.hotel.HotelBaseInfoDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeSpecificDao;
import com.pms.czhotelfoundation.dao.room.RoomFeatureDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeImageDao;
import com.pms.czhotelfoundation.dao.setting.HotelInitialDataDao;
import com.pms.czhotelfoundation.service.price.RoomDayPriceService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.Md5;
import com.pms.czpmsutils.RSAUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ECache;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用于给OTA渠道调用的服务
 *
 * **/
@Service
@Slf4j
public class ItHotelServiceImpl extends BaseService implements ItHotelService {

    @Autowired
    private RoomRateCodeDao roomRateCodeDao;

    @Autowired
    private RoomRateCodeSpecificDao roomRateCodeSpecificDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private RoomTypeDao roomTypeDao;

    @Autowired
    private RoomDayPriceService roomDayPriceService;

    @Autowired
    private HotelInitialDataDao hotelInitialDataDao;

    @Autowired
    private RoomFeatureDao roomFeatureDao;

    @Autowired
    private TurnAlwaysService turnAlwaysService;

    @Autowired
    private RoomTypeImageDao roomTypeImageDao;

    @Autowired
    private HotelBaseInfoDao hotelBaseInfoDao;

    @Override
    public ResponseData getPriceCodeByHid(GetPriceCodeByHidDTO getPriceCodeByHidDTO) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = RSAUtils.getStringDecrypt(getPriceCodeByHidDTO.getHotelId()); ;;
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /*2.查询缓存中是否有信息*/
            final HashOperations<String, Object, Object> ops = stringRedisTemplate.opsForHash();

            String key = ECache.ROOM_RATE_PRCIE_CODE + "_" + user.getHid();

            Object o = ops.get(ECache.ROOM_RATE_PRCIE_CODE, key);

            if (o == null || "[]".equals(o.toString())) {
                RoomRateCodeSearch roomRateCodeSearch = new RoomRateCodeSearch();
                roomRateCodeSearch.setHid(user.getHid());
                List<RoomRateCode> roomRateCodes = roomRateCodeDao.selectBySearch(roomRateCodeSearch);

                RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
                roomRateCodeSpecificSearch.setHid(user.getHid());
                List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);


                for (RoomRateCodeSpecific rrcs : roomRateCodeSpecifics) {

                    for (RoomRateCode rrc : roomRateCodes) {

                        if (!rrcs.getRateId().equals(rrc.getRateId())) {
                            continue;
                        }

                        List<RoomRateCodeSpecific> roomRateCodeSpecifics1 = rrc.getRoomRateCodeSpecifics();

                        if (roomRateCodeSpecifics1 == null) {
                            roomRateCodeSpecifics1 = new ArrayList<RoomRateCodeSpecific>();
                        }

                        Map<String, RoomRateCodeSpecific> rateByRooType = rrc.getRateByRooType();

                        if (rateByRooType == null) {
                            rateByRooType = new HashMap<>();
                        }
                        roomRateCodeSpecifics1.add(rrcs);
                        rrc.setRoomRateCodeSpecifics(roomRateCodeSpecifics1);
                        rrc.setAuthorizeCode(Md5.calcMD5(rrc.getAuthorizeCode()));
                        rateByRooType.put(rrcs.getRoomTypeId().toString(), rrcs);
                        rrc.setRateByRooType(rateByRooType);

                    }

                }
                responseData.setData(roomRateCodes);
            } else {
                responseData.setData(JSONArray.fromObject(o));
            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }


    /**
     *  根据房型id和房价id获取明细信息
     * @param getPriceCodeByRoomTypeDTO
     * @return
     */
    @Override
    public ResponseData getPriceCodeByRoomType(GetPriceCodeByRoomTypeDTO getPriceCodeByRoomTypeDTO) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            TbUserSession user = this.getTbUserSession(RSAUtils.getStringDecrypt(getPriceCodeByRoomTypeDTO.getHotelId()));

            RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
            roomRateCodeSpecificSearch.setHid(user.getHid());
            if(getPriceCodeByRoomTypeDTO.getRoomTypeId()!=null){
                roomRateCodeSpecificSearch.setRoomTypeId(getPriceCodeByRoomTypeDTO.getRoomTypeId());
            }
            roomRateCodeSpecificSearch.setRateId(getPriceCodeByRoomTypeDTO.getRateId());
            List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);

            if(roomRateCodeSpecifics.size()<1){
                throw new Exception("未查询到对应的房价信息");
            }
            Map<Integer, RoomRateCodeSpecific> collect = roomRateCodeSpecifics.stream().collect(Collectors.toMap(RoomRateCodeSpecific::getRoomTypeId, a -> a, (k1, k2) -> k2));

            responseData.setData(collect);

            return responseData;
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 获取房型可用信息
     * @param roomTypeAvailableRoomRequest
     * @return
     */
    @Override
    public ResponseData roomTypeAvailableRoom(RoomTypeAvailableRoomDTO roomTypeAvailableRoomRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            TbUserSession user = this.getTbUserSession(RSAUtils.getStringDecrypt(roomTypeAvailableRoomRequest.getHotelId()));
            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();

            // 房型的使用详情
            // 存在缓存里
            // key格式  yyyyMMdd+roomTypeId
            // 值 json  key  1.入住信息  2.维修/停用 3.预定信息(已排房) 4.预定信息(未排房)
            String tdrt = ECache.TURNALWAYS_ROOMTYPE + "_" + user.getHid();
            Object o = userCahe.get(ECache.TURNALWAYS_ROOMTYPE, tdrt);


            JSONObject roomTypeRoomNum = JSONObject.fromObject(o);

            Integer roomTypeId = roomTypeAvailableRoomRequest.getRoomTypeId();

            // 日期差
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(roomTypeAvailableRoomRequest.getBeginTime(), roomTypeAvailableRoomRequest.getEndTime());

            // 获取当前酒店所有的房间信息
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            roomInfoSearch.setState(1);

            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);

            Map<Integer, List<RoomInfo>> roomMap = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomTypeId));

            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setHid(user.getHid());
            roomTypeSearch.setState(1);

            List<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);


            HashMap<Integer, RoomTypeAvailableRoomVo> availableRoomVoHashMap = new HashMap<>();

            for(RoomType rt:roomTypes){
                Integer key = rt.getRoomTypeId();
                // 如果房型不为空 说明以传房型信息
                if(roomTypeId!=null&&roomTypeId<1){

                    if(!key.equals(roomTypeId)){
                        continue;
                    }

                }

                RoomTypeAvailableRoomVo roomTypeAvailableRoomVo = new RoomTypeAvailableRoomVo();
                roomTypeAvailableRoomVo.setHid(user.getHid());
                roomTypeAvailableRoomVo.setHotelGroupId(user.getHotelGroupId());
                roomTypeAvailableRoomVo.setRoomTypeName(rt.getRoomTypeName());
                roomTypeAvailableRoomVo.setRoomTypeId(rt.getRoomTypeId());

                // 房间总数量
                List<RoomInfo> rs = roomMap.get(key);

                int num  = 0;

                if(rs!=null){
                    num = rs.size();
                }

                JSONArray dateList = new JSONArray();

                for(String date : allDayListBetweenDate){

                    JSONObject d = new JSONObject();
                    d.put("date",date);

                    if(num==0){
                        d.put("num",0);
                    }else {

                        Object numObj = roomTypeRoomNum.get(date.replace("-","")+ key);
                        if(numObj==null){
                            d.put("num",num);
                        }else {

                            // 已用的房间数
                            Map<String,Integer> numJson = JSONObject.fromObject(numObj);

                            int sum = numJson.values().stream().mapToInt(Integer::intValue).sum();
                            d.put("num",num-sum);

                        }

                    }

                    dateList.add(d);

                }

                roomTypeAvailableRoomVo.setList(dateList);

                availableRoomVoHashMap.put(key,roomTypeAvailableRoomVo);

            }

            responseData.setData(availableRoomVoHashMap);

            return responseData;
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData findRoomDayPrice(FindRoomDayPriceDTO findRoomDayPriceRequest)  {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            JSONObject param = new JSONObject();
            param.put(ER.SESSION_TOKEN,RSAUtils.getStringDecrypt(findRoomDayPriceRequest.getHotelId()));
            param.put("startTime",findRoomDayPriceRequest.getBeginTime());
            param.put("endTime",findRoomDayPriceRequest.getEndTime());
            param.put("roomRateId",findRoomDayPriceRequest.getRateId());
            param.put("roomTypeId",findRoomDayPriceRequest.getRoomTypeId());
            responseData =  roomDayPriceService.findRoomPrice(param);
        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData searchRoomTypeMsg(SearchRoomTypeMsgDTO searchRoomTypeMsgRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken = RSAUtils.getStringDecrypt(searchRoomTypeMsgRequest.getHotelId()); ;;
            final TbUserSession user = this.getTbUserSession(sessionToken);

            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setState(1);
            roomInfoSearch.setHid(user.getHid());
            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
            Map<Integer, List<RoomInfo>> collect = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomTypeId));

            // 房型
            List<SearchRoomTypeMsgView> searchRoomTypeMsgViews = new ArrayList<>();

            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setHid(user.getHid());
            roomTypeSearch.setState(1);

            Page<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);

            Boolean yz = searchRoomTypeMsgRequest.getRoomTypeId()!=null&&searchRoomTypeMsgRequest.getRoomTypeId()>0;

            // 房型图片
            RoomTypeImageSearch roomTypeImageSearch = new RoomTypeImageSearch();
            roomTypeImageSearch.setHid(user.getHid());
            List<RoomTypeImage> roomTypeImages = roomTypeImageDao.selectBySearch(roomTypeImageSearch);
            Map<Integer, List<RoomTypeImage>> imgMap = roomTypeImages.stream().collect(Collectors.groupingBy(RoomTypeImage::getRoomTypeId));



            for (RoomType roomType:roomTypes){

                if(yz&&!roomType.getRoomTypeId().equals(searchRoomTypeMsgRequest.getRoomTypeId())){
                    continue;
                }

                SearchRoomTypeMsgView searchRoomTypeMsgView = new SearchRoomTypeMsgView();
                searchRoomTypeMsgView.setRoomTypeNameEn(roomType.getRoomTypeNameEn());
                searchRoomTypeMsgView.setRoomTypeId(roomType.getRoomTypeId());
                searchRoomTypeMsgView.setRoomTypeName(roomType.getRoomTypeName());
                searchRoomTypeMsgView.setDes(roomType.getDes());
                searchRoomTypeMsgView.setDesEn(roomType.getDesEn());
                searchRoomTypeMsgView.setShortName(roomType.getShortName());
                searchRoomTypeMsgView.setState(roomType.getState());
                searchRoomTypeMsgView.setPrice(roomType.getPrice());
                searchRoomTypeMsgView.setRoomCount(collect.get(roomType.getRoomTypeId()).size());
                searchRoomTypeMsgView.setBedType(roomType.getBedtype());
                searchRoomTypeMsgView.setSqm(roomType.getArea());

                ArrayList<String> strings = new ArrayList<>();

                List<RoomTypeImage> rti = imgMap.get(roomType.getRoomTypeId());

                if(rti!=null){
                    for (RoomTypeImage r:rti){
                        strings.add(r.getUrl());
                    }
                }

                searchRoomTypeMsgView.setImgs(strings);

                searchRoomTypeMsgViews.add(searchRoomTypeMsgView);
            }



            responseData.setData(searchRoomTypeMsgViews);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    @Override
    public ResponseData findBulidFloor(FindBulidFloorDTO findBulidFloorDTO) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken = RSAUtils.getStringDecrypt(findBulidFloorDTO.getHotelId()); ;;
            final TbUserSession user = this.getTbUserSession(sessionToken);

            JSONArray buildList  = new JSONArray();
            JSONArray floorList  = new JSONArray();

            HotelInitialDataSearch hotelInitialDataSearch = new HotelInitialDataSearch();
            hotelInitialDataSearch.setHid(user.getHid());
            hotelInitialDataSearch.setEnable(1);

            Page<HotelInitialData> hotelInitialDatas = hotelInitialDataDao.selectBySearch(hotelInitialDataSearch);

            HashMap<Integer, JSONArray> integerJSONArrayHashMap = new HashMap<>();

            for(HotelInitialData hotelInitialData:hotelInitialDatas){

                Integer valueType = hotelInitialData.getValueType();



                if(valueType==1){

                    JSONObject o = new JSONObject();
                    o.put("code",hotelInitialData.getCode());
                    o.put("name",hotelInitialData.getCodeName());
                    o.put("id",hotelInitialData.getInitialId());

                    buildList.add(o);

                }else if(valueType==2){


                    JSONObject o = new JSONObject();
                    o.put("code",hotelInitialData.getCode());
                    o.put("name",hotelInitialData.getCodeName());
                    o.put("id",hotelInitialData.getInitialId());
                    o.put("buildingId",hotelInitialData.getParentId());
                    floorList.add(o);

                }

            }

            JSONObject data = new JSONObject();
            data.put("buildList",buildList);
            data.put("floorList",floorList);

            responseData.setData(data);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    @Override
    public ResponseData findHotelRoomInfo(FindHotelRoomInfoDTO findHotelRoomInfoDTO) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken =RSAUtils.getStringDecrypt(findHotelRoomInfoDTO.getHotelId()); ;;
            final TbUserSession user = this.getTbUserSession(sessionToken);
            List<RoomInfo> roomInfos = new ArrayList<>();
            String startTime = findHotelRoomInfoDTO.getStartTime();
            String endTime = findHotelRoomInfoDTO.getEndTime();
            // 如果时间没传，则按条件返回
            if(HotelUtils.validaStr(startTime).length()<5||HotelUtils.validaStr(endTime).length()<5){
                RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
                roomInfoSearch.setHid(user.getHid());
                roomInfoSearch.setPageNum(findHotelRoomInfoDTO.getPageNum());
                roomInfoSearch.setPageSize(findHotelRoomInfoDTO.getPageSize());
                roomInfoSearch.setFloorId(findHotelRoomInfoDTO.getFloorId());
                roomInfoSearch.setBuildingId(findHotelRoomInfoDTO.getBuildingId());

                roomInfos= roomInfoDao.selectBySearch(roomInfoSearch);
            }else {

                JSONObject param = new JSONObject();
                param.put("type",2);
                param.put("startTime",startTime);
                param.put("endTime",endTime);
                param.put("floorId",findHotelRoomInfoDTO.getFloorId());
                param.put("buildingId",findHotelRoomInfoDTO.getBuildingId());
                param.put("roomTypeId",findHotelRoomInfoDTO.getRoomTypeId());

                List<RoomInfoResult> roomInfoResults = turnAlwaysService.canUseRoom(user,param);
                if(!CollectionUtils.isEmpty(roomInfoResults)){
                    roomInfos = roomInfoResults.stream().map(roomInfoResult -> {
                        RoomInfo roomInfo = new RoomInfo();
                        BeanUtils.copyProperties(roomInfoResult,roomInfo);
                        return roomInfo;
                    }).collect(Collectors.toList());
                }
            }

            RoomFeatureSearch roomFeatureSearch = new RoomFeatureSearch();
            roomFeatureSearch.setHid(user.getHid());
            List<RoomFeature> roomFeatures = roomFeatureDao.selectBySearch(roomFeatureSearch);

            Map<Integer, List<RoomFeature>> collect = roomFeatures.stream().collect(Collectors.groupingBy(RoomFeature::getRoomId));

            JSONArray jsonArray = new JSONArray();

            for(RoomInfo roomInfo:roomInfos){
                JSONObject jo = new JSONObject();
                jo.put("buildingId",roomInfo.getBuildingId());
                jo.put("buildingName",roomInfo.getBuildingName());
                jo.put("floorId",roomInfo.getFloorId());
                jo.put("floorName",roomInfo.getFloorName());
                jo.put("lockNum",roomInfo.getLockNum());
                jo.put("roomNo",roomInfo.getRoomNum());
                jo.put("roomInfoId",roomInfo.getRoomInfoId());
                jo.put("roomNoEn",roomInfo.getRoomNumEn());
                jo.put("roomNoState",roomInfo.getRoomNumState());
                jo.put("roomTypeId",roomInfo.getRoomTypeId());
                jo.put("roomTypeName",roomInfo.getRoomTypeName());
                jo.put("checkinNum",roomInfo.getGuestNum());
                List<RoomFeature> roomFeatures1 = collect.get(roomInfo.getRoomInfoId());
                if(roomFeatures1!=null){
                    JSONArray pf = new JSONArray();
                    for(RoomFeature rf :roomFeatures1){
                        JSONObject p = new JSONObject();
                        p.put("code",rf.getCode());
                        p.put("name",rf.getCodeType());
                        pf.add(p);
                    }
                    jo.put("pf",pf);
                }else {
                    jo.put("pf",new JSONArray());
                }
                jsonArray.add(jo);
            }
            responseData.setData(jsonArray);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    @Override
    public ResponseData searchRoomType(SearchRoomTypeMsgDTO searchRoomTypeMsgDTO) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken = RSAUtils.getStringDecrypt(searchRoomTypeMsgDTO.getHotelId()); ;;
            final TbUserSession user = this.getTbUserSession(sessionToken);

            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setHid(user.getHid());
            roomTypeSearch.setState(1);
            roomTypeSearch.setRoomTypeId(searchRoomTypeMsgDTO.getRoomTypeId());
            Page<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    @Override
    public ResponseData findHotelRoomNum(FindHotelRoomNumDTO findHotelRoomNumDTO) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken = RSAUtils.getStringDecrypt(findHotelRoomNumDTO.getHotelId()); ;;
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Integer roomTypeId = findHotelRoomNumDTO.getRoomTypeId();

            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            roomInfoSearch.setState(1);
            if(roomTypeId!=null){
                roomInfoSearch.setRoomTypeId(roomTypeId);
            }

            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            // 房型的使用详情
            // 存在缓存里
            // key格式  yyyyMMdd+roomTypeId
            // 值 json  key  1.入住信息  2.维修/停用 3.预定信息(已排房) 4.预定信息(未排房)
            String tdrt = ECache.TURNALWAYS_ROOMTYPE + "_" + user.getHid();
            Object o = userCahe.get(ECache.TURNALWAYS_ROOMTYPE, tdrt);


            JSONObject roomTypeRoomNum = JSONObject.fromObject(o);

            log.info("roomTypeRoomNum={}",roomTypeRoomNum.toString());

            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);

            // 日期差
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(findHotelRoomNumDTO.getStartTime(), findHotelRoomNumDTO.getEndTime());

            log.info("allDayListBetweenDate={}",JSONArray.fromObject(allDayListBetweenDate).toString());


            Map<Integer, List<RoomInfo>> roomMap = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomTypeId));

            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setHid(user.getHid());
            roomTypeSearch.setState(1);

            List<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);

            Integer useRtMap = 0;
            Integer checinRtMap = 0;
            Integer bookRtMap = 0;
            Integer wxRtMap = 0;

            HashMap<Integer, JSONObject> integerJSONObjectHashMap = new HashMap<>();

            for(RoomType rt:roomTypes){
                Integer key = rt.getRoomTypeId();
                // 如果房型不为空 说明以传房型信息
                if(roomTypeId!=null&&roomTypeId>0){

                    if(!key.equals(roomTypeId)){
                        continue;
                    }

                }

                Integer useRtMap1 = 0;
                Integer checinRtMap1 = 0;
                Integer bookRtMap1 = 0;
                Integer wxRtMap1 = 0;

                for(String date : allDayListBetweenDate){

                    Object numObj = roomTypeRoomNum.get(date.replace("-","")+ key);
                    if(numObj==null){
                        continue;
                    }
                    // 已用的房间数
                    Map<String,Integer> numJson = JSONObject.fromObject(numObj);

                    int sum = numJson.values().stream().mapToInt(Integer::intValue).sum();

                    if(sum<=useRtMap){
                       continue;
                    }
                    Integer integer = numJson.get("1");
                    if(integer!=null&&integer>useRtMap1){
                        useRtMap1 = integer;
                    }
                    Integer integer2 = numJson.get("2");
                    if(integer2!=null&&integer2>checinRtMap1){
                        checinRtMap1 = integer2;
                    }

                    Integer integer3 = numJson.get("3");
                    if(integer3 !=null&&integer3>bookRtMap1){
                        bookRtMap1 = integer3;
                    }
                    Integer integer4 = numJson.get("4");
                    if(integer4!=null&&integer4>wxRtMap1){
                        wxRtMap1 = integer4;
                    }
                }
                useRtMap+=useRtMap1;
                checinRtMap+=checinRtMap1;
                bookRtMap+=bookRtMap1;
                wxRtMap+=wxRtMap1;

            }

            int size = roomInfos.size();
            int i1 = useRtMap + checinRtMap + bookRtMap + wxRtMap;
            int i = size - i1;

            JSONObject jsonObject = new JSONObject();

            jsonObject.put("sumNum",size);
            jsonObject.put("useNum",i1);
            jsonObject.put("remaindNum",i);

            JSONObject useData = new JSONObject();
            useData.put("orderNum",bookRtMap + wxRtMap);
            useData.put("registNum",useRtMap);
            useData.put("repairNum",checinRtMap);
            jsonObject.put("useData",useData);

            responseData.setData(jsonObject);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    @Override
    public ResponseData dayRoomList(FindHotelRoomNumDTO findHotelRoomNumDTO) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken =RSAUtils.getStringDecrypt(findHotelRoomNumDTO.getHotelId());
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Integer roomTypeId = findHotelRoomNumDTO.getRoomTypeId();

            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            roomInfoSearch.setState(1);

            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            // 房型的使用详情
            // 存在缓存里
            // key格式  yyyyMMdd+roomTypeId
            // 值 json  key  1.入住信息  2.维修/停用 3.预定信息(已排房) 4.预定信息(未排房)
            String tdrt = ECache.TURNALWAYS_ROOMTYPE + "_" + user.getHid();
            Object o = userCahe.get(ECache.TURNALWAYS_ROOMTYPE, tdrt);


            JSONObject roomTypeRoomNum = JSONObject.fromObject(o);

            log.info("roomTypeRoomNum={}",roomTypeRoomNum.toString());

            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);

            // 日期差
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(findHotelRoomNumDTO.getStartTime(), findHotelRoomNumDTO.getEndTime());

            log.info("allDayListBetweenDate={}",JSONArray.fromObject(allDayListBetweenDate).toString());


            Map<Integer, List<RoomInfo>> roomMap = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomTypeId));

            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setHid(user.getHid());
            roomTypeSearch.setState(1);

            List<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);
            JSONArray rtList = new JSONArray();
            for(RoomType rt:roomTypes){
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("roomTypeId",rt.getRoomTypeId());
                jsonObject.put("roomTypeName",rt.getRoomTypeName());
                rtList.add(jsonObject);
            }

            JSONArray roomList = new JSONArray();

            for(String date : allDayListBetweenDate){

                String dateNum = date.replace("-", "");

                JSONObject roomObj = new JSONObject();
                roomObj.put("dateStr",date);

                JSONArray roomTypeDay = new JSONArray();

                for(RoomType rt:roomTypes){

                    Integer key = rt.getRoomTypeId();

                    Integer sumRoomNum = 0;

                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("dateStr",date);
                    jsonObject.put("roomTypeId",key);
                    jsonObject.put("roomTypeName",rt.getRoomTypeName());

                    List<RoomInfo> roomInfos1 = roomMap.get(key);
                    if(roomInfos1!=null){
                        sumRoomNum = roomInfos1.size();
                    }

                    Object numObj = roomTypeRoomNum.get(dateNum+ key);
                    if(numObj==null){
                        jsonObject.put("roomNum",sumRoomNum);
                        roomTypeDay.add(jsonObject);
                        continue;
                    }
                    // 已用的房间数
                    Map<String,Integer> numJson = JSONObject.fromObject(numObj);

                    int sum = numJson.values().stream().mapToInt(Integer::intValue).sum();

                    int diff = sumRoomNum - sum;
                    jsonObject.put("roomNum",diff);
                    roomTypeDay.add(jsonObject);
                }
                roomObj.put("roomInfo",roomTypeDay);
                roomList.add(roomObj);
            }
            responseData.setData(roomList);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    @Override
    public ResponseData findHotelMsg(FindHotelDTO findHotelDTO) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            /*1.获取用户信息*/
            String sessionToken = RSAUtils.getStringDecrypt(findHotelDTO.getHotelId());
            final TbUserSession user = this.getTbUserSession(sessionToken);

            HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(user.getHid());
            if(hotelBaseInfo==null){
                throw new Exception("未查询到酒店信息");
            }

            JSONObject res = new JSONObject();
            res.put("name",hotelBaseInfo.getHotelName());
            res.put("nameEn",hotelBaseInfo.getHotelNameEn());
            res.put("img",hotelBaseInfo.getOpenId());
            res.put("phone",hotelBaseInfo.getContactPhone());
            res.put("address",hotelBaseInfo.getAddr());

            RoomRateCodeSearch roomRateCodeSearch = new RoomRateCodeSearch();
            roomRateCodeSearch.setHid(user.getHid());
            roomRateCodeSearch.setRateCode("ZJCS");
            List<RoomRateCode> roomRateCodes = roomRateCodeDao.selectBySearch(roomRateCodeSearch);
            if(roomRateCodes==null||roomRateCodes.size()<1){
                throw new Exception("请设置对应的房价码");
            }
            RoomRateCode roomRateCode = roomRateCodes.get(0);
            res.put("rateId",roomRateCode.getRateId());
            res.put("rateCode",roomRateCode.getRateCode());
            res.put("km","-1");
            // 值小于1 说明是0 0代表不开启
            if(findHotelDTO.getLat()!=null&&findHotelDTO.getLon()!=null){

                java.text.DecimalFormat   df   =new   java.text.DecimalFormat("#.0");

                Double distance = HotelUtils.getDistance(findHotelDTO.getLat(), findHotelDTO.getLon(), hotelBaseInfo.getLat(), hotelBaseInfo.getLon());
                double v = distance.intValue() / 1000.0;
                if(v<0.1){
                    res.put("km","0.1");
                }else {
                    res.put("km",df.format(v));
                }

            }

            responseData.setData(res);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }



}
