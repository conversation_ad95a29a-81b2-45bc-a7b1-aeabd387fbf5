package com.pms.czabsorders.service.machine;

import com.pms.czpmsutils.ResponseData;
import net.sf.json.JSONObject;

public interface MachineUserInfoService {
    /**
     * 获取酒店自助机使用率
     * @param param
     * @return
     */
    ResponseData getHotelMachineUserRate(JSONObject param);

    /**
     * 获取自助机当年的使用总数以及每个月使用的总数
     * @param param
     * @return
     */
    ResponseData getMachineUserInfo(JSONObject param);


    /**
     * 获取自助机当前支付总和以及每个月支付的总和
     * @param param
     * @return
     */
    ResponseData getMachinePayInfo(JSONObject param);







}
