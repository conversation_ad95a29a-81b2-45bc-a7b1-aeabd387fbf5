package com.pms.czabsorders.service.account.impl;

import com.pms.czabsorders.bean.*;
import com.pms.czabsorders.service.account.AccountService;
import com.pms.czabsorders.service.account.transaction.AccountTransaction;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountCancel;
import com.pms.czaccount.bean.account.AccountThirdPayRecode;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.bean.account.search.AccountThirdPayRecodeSearch;
import com.pms.czaccount.dao.account.AccountCancelDao;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czaccount.dao.account.AccountThirdPayRecodeDao;
import com.pms.czaccount.service.alipay.AliPayService;
import com.pms.czaccount.service.wechat.WeChatPayService;
import com.pms.czmembership.bean.company.HotelCompanyAccount;
import com.pms.czmembership.bean.company.HotelCompanyAccountInfo;
import com.pms.czmembership.bean.company.HotelCompanyArRecode;
import com.pms.czmembership.bean.member.*;
import com.pms.czmembership.dao.company.HotelCompanyAccountDao;
import com.pms.czmembership.dao.company.HotelCompanyAccountInfoDao;
import com.pms.czmembership.dao.company.HotelCompanyArRecodeDao;
import com.pms.czmembership.dao.member.CardFreezeRecordDao;
import com.pms.czmembership.dao.member.CardGroupInfoDao;
import com.pms.czmembership.dao.member.CardInfoDao;
import com.pms.czmembership.service.company.CompanyInfoService;
import com.pms.czmembership.service.memeber.MemberService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.OrderNumUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.OprecordInfoRequest;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.*;
import com.pms.czpmsutils.request.search.AccountSummarySearch;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.search.BookingOrderDailyPriceSearch;
import com.pms.pmsorder.bean.search.RegistPersonSearch;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.dao.BookingOrderDailyPriceDao;
import com.pms.pmsorder.dao.BookingOrderDao;
import com.pms.pmsorder.dao.RegistDao;
import com.pms.pmsorder.dao.RegistPersonDao;
import com.pms.pmsorder.service.BreakfastService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Primary
@Slf4j
public class AccountServiceImpl extends BaseService implements AccountService {
    @Autowired
    AccountTransaction accountTransaction;
    @Autowired
    AccountDao accountDao;
    @Autowired
    AccountThirdPayRecodeDao accountThirdPayRecodeDao;
    @Autowired
    HotelCompanyArRecodeDao hotelCompanyArRecodeDao;
    @Autowired
    RegistDao registDao;
    @Autowired
    BookingOrderDao bookingOrderDao;
    @Autowired
    AccountCancelDao accountCancelDao;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private WeChatPayService weChatPayService;

    @Autowired
    AliPayService aliPayService;

    @Autowired
    MemberService memberService;
    @Autowired
    CompanyInfoService companyInfoService;

    @Autowired
    private BreakfastService breakfastService;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private HotelCompanyAccountInfoDao hotelCompanyAccountInfoDao;

    @Autowired
    private HotelCompanyAccountDao hotelCompanyAccountDao;

    @Autowired
    private CardFreezeRecordDao cardFreezeRecordDao;

    @Autowired
    private CardInfoDao cardInfoDao;

    @Autowired
    private CardGroupInfoDao cardGroupInfoDao;


    private BaseService baseService = this;

    @Override
    public ResponseData addAccount(AddAccountParam accountInfo) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {


            String sessionToken = accountInfo.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Regist regist = null;
            BookingOrder bookingOrder = null;

            AccountThirdPayRecode accountThirdPayRecode = null;
            //日志记录
            ArrayList<OprecordInfoRequest> oprecordInfoRequests = new ArrayList<>();
            OprecordInfoRequest oprecordInfoRequest = new OprecordInfoRequest();

            String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
            Account account = new Account();
            account.setAccountId(accountId);
            account.setHid(user.getHid());
            account.setHotelGroupId(user.getHotelGroupId());
            account.setCreateUserId(user.getUserId());
            account.setCreateUserName(user.getUserName());
            account.setCreateTime(new Date());
            account.setIsCancel(0);
            account.setAccountYear(user.getBusinessYear());
            account.setAccountYearMonth(user.getBusinessMonth());
            account.setBusinessDay(user.getBusinessDay());
            account.setClassId(user.getClassId());
            account.setPrice(accountInfo.getPrice());
            account.setSettleAccountTime(new Date());
            //消费-付款
            account.setPayType(accountInfo.getPayType());
            account.setSaleNum(accountInfo.getSaleNum().equals(null) ? 1 : accountInfo.getSaleNum());
            account.setUintPrice(accountInfo.getPrice() / account.getSaleNum());
            account.setPayClassId(accountInfo.getPayClassId());
            account.setPayClassName(accountInfo.getPayClassName());
            account.setPayCodeId(accountInfo.getPayCodeId());
            account.setPayCodeName(accountInfo.getPayCodeName());
            account.setRegistPersonId(accountInfo.getRegistPersonId().equals(null) ? 0 : accountInfo.getRegistPersonId());
            account.setAccountType(accountInfo.getAccountType().equals(null) ? 1 : accountInfo.getAccountType());
            account.setRemark(HotelUtils.validaStr(accountInfo.getRemark()));
            account.setReason(HotelUtils.validaStr(accountInfo.getReason()));
            account.setRefundPrice(0);
            account.setGroupAccount(accountInfo.getGroupAccount());
            //入住单入账
            if (accountInfo.getGroupAccount() == 0) {
                if (accountInfo.getRegistId() != null && accountInfo.getRegistId() > 0) {
                    regist = registDao.selectById(accountInfo.getRegistId());
                    if (regist.getState() == 1) {
                        throw new Exception("登记单状态异常");
                    }
                    account.setRegistId(accountInfo.getRegistId());
                    account.setRoomNum(regist.getRoomNum());
                    account.setRoomInfoId(regist.getRoomNumId());
                    account.setRoomTypeId(regist.getRoomTypeId());
                    account.setRegistState(accountInfo.getRegistState().equals(null) ? 0 : accountInfo.getRegistState());
                    account.setAccountCode(accountInfo.getAccountCode());
                    account.setRegistPersonName(accountInfo.getRegistPersonName());
                    account.setTeamCodeId(regist.getTeamCodeId());
                    //添加日志记录
                    oprecordInfoRequest.setRegistId(accountInfo.getRegistId());
                    oprecordInfoRequest.setRoomNum(regist.getRoomNum());
                } else if (accountInfo.getBookingId() != null && accountInfo.getBookingId() > 0) {
                    bookingOrder = bookingOrderDao.selectById(accountInfo.getBookingId());
                    //只有预订单是有效的时候可以入账
                    if (bookingOrder.getOrderStatus() != 1) {
                        throw new Exception("预订单状态异常无法入账");
                    }
                    account.setBookingId(accountInfo.getBookingId());
                    account.setRegistState(0);
                    account.setTeamCodeId(bookingOrder.getTeamCodeId());
                    //添加日志记录
                    oprecordInfoRequest.setBookingOrderId(accountInfo.getBookingId());
                }
            } else {
                account.setRegistId(accountInfo.getRegistId());
                account.setRegistPersonId(accountInfo.getRegistPersonId());
                account.setRegistPersonName(accountInfo.getRegistPersonName());
                account.setTeamCodeId(accountInfo.getRegistId());
                account.setRegistPersonName(accountInfo.getRegistPersonName());
                account.setRegistState(0);
                account.setRoomNum("");
            }
            String smainId = accountInfo.getThirdAccoutId();
            BookingOrderDailyPrice bookingOrderDailyPrice = null;
            //final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            switch (account.getPayCodeId()) {
                // 全天房费
                case "0002":
                    if (regist == null) {
                        break;
                    }
                 /*   BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
                    bookingOrderDailyPriceSearch.setRegistId(regist.getRegistId());
                    bookingOrderDailyPriceSearch.setDailyTime(user.getBusinessDay());
                    bookingOrderDailyPriceSearch.setDailyState(1);

                    Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
                    if(bookingOrderDailyPrices!=null&&bookingOrderDailyPrices.size()>0){
                        BookingOrderDailyPrice bodcs = bookingOrderDailyPrices.get(0);
                        if(bodcs.getPrice().equals(account.getPrice())){
                            bookingOrderDailyPrice = bodcs;
                            account.setThirdAccoutId(bookingOrderDailyPrice.getId()+"");
                        }
                    }*/
                    break;

                //微信扫码支付
                case "9320":

                    // 重复入住判断
                    //将mainid设置到Redis 只有Redis中不存在是才会成功, 超时时间设置为3小时
                    Boolean ok = stringRedisTemplate.opsForValue().setIfAbsent(smainId, "", 3, TimeUnit.HOURS);
                    //设置失败, 说明之前已经有数据了, 直接返回
                    if (Boolean.FALSE.equals(ok)) {
                        responseData.setResult(ER.ERR);
                        responseData.setMsg(ER.EXECUTE_SUCCESS);
                        return responseData;
                    }
//                    Object hotelWechatPay = userCahe.get("addhotelWechatPay", smainId);
//                    if (hotelWechatPay != null) {
//                        int i = Integer.parseInt(hotelWechatPay.toString());
//                        if (i > 0) {
//                            return responseData;
//                        }
//                    }
//                    userCahe.put("addhotelWechatPay", smainId, accountInfo.getPrice() + "");

                    // 验证是否重复入账
                    AccountSearch accountSearch = new AccountSearch();
                    accountSearch.setHid(user.getHid());
                    accountSearch.setThirdAccoutId(accountInfo.getThirdAccoutId());

                    List<Account> accounts = accountDao.selectBySearch(accountSearch);

                    if (accounts != null && accounts.size() > 0) {
                        return responseData;
                    }

                    account.setThirdRefundState(0);
                    account.setThirdAccoutId(accountInfo.getThirdAccoutId());
                    account.setAccountCode(accountInfo.getAccountCode());
                    break;
                case "9300": //支付宝扫码支付
                    // 重复入住判断
                    //将mainid设置到Redis 只有Redis中不存在是才会成功, 超时时间设置为3小时
                    Boolean ok1 = stringRedisTemplate.opsForValue().setIfAbsent(smainId, "", 3, TimeUnit.HOURS);
                    //设置失败, 说明之前已经有数据了, 直接返回
                    if (Boolean.FALSE.equals(ok1)) {
                        responseData.setResult(ER.ERR);
                        responseData.setMsg(ER.EXECUTE_SUCCESS);
                        return responseData;
                    }
//                    Object addHotelaliPay = userCahe.get("addHotelaliPay", smainId);
//                    if (addHotelaliPay != null) {
//                        int i = Integer.parseInt(addHotelaliPay.toString());
//                        if (i > 0) {
//                            return responseData;
//                        }
//                    }
//                    userCahe.put("addHotelaliPay", smainId, accountInfo.getPrice() + "");

                    // 验证是否重复入账
                    AccountSearch aliaccountSearch = new AccountSearch();
                    aliaccountSearch.setHid(user.getHid());
                    aliaccountSearch.setThirdAccoutId(accountInfo.getThirdAccoutId());
                    List<Account> aliaccounts = accountDao.selectBySearch(aliaccountSearch);
                    if (aliaccounts != null && aliaccounts.size() > 0) {
                        return responseData;
                    }
                    account.setThirdRefundState(0);
                    account.setThirdAccoutId(accountInfo.getThirdAccoutId());
                    account.setAccountCode(accountInfo.getAccountCode());
                    break;
                case "9100": //银行卡预授权
                    account.setThirdRefundState(0);
                    accountThirdPayRecode = new AccountThirdPayRecode();
                    String at = HotelUtils.getHIDUUID32("AT", user.getHid());
                    accountThirdPayRecode.setHid(user.getHid());
                    accountThirdPayRecode.setAccountId(account.getAccountId());
                    accountThirdPayRecode.setHotelGroupId(user.getHotelGroupId());
                    accountThirdPayRecode.setAccountThirdId(at);
                    //款台号
                    accountThirdPayRecode.setCounterId(HotelUtils.validaStr(accountInfo.getBankPayInfo().getCounterId()));
                    //操作员号
                    accountThirdPayRecode.setOperatorId(HotelUtils.validaStr(accountInfo.getBankPayInfo().getOperatorId()));
                    //交易编号
                    accountThirdPayRecode.setTransType(HotelUtils.validaStr(accountInfo.getBankPayInfo().getTransType()));
                    //金额
                    accountThirdPayRecode.setAmount(account.getPrice());
                    //48域附加信息
                    accountThirdPayRecode.setMemo(HotelUtils.validaStr(accountInfo.getBankPayInfo().getMemo()));
                    //三个校验字符串
                    accountThirdPayRecode.setLrc(HotelUtils.validaStr(accountInfo.getBankPayInfo().getLrc()));
                    //终端流水号
                    accountThirdPayRecode.setTrace(HotelUtils.validaStr(accountInfo.getBankPayInfo().getTrace()));
                    //银行id
                    accountThirdPayRecode.setBarkId(HotelUtils.validaStr(accountInfo.getBankPayInfo().getBarkId()));
                    //批次号
                    accountThirdPayRecode.setBatch(HotelUtils.validaStr(accountInfo.getBankPayInfo().getBatch()));
                    //交易日期 yyyyMMdd
                    accountThirdPayRecode.setTransDate(HotelUtils.validaStr(accountInfo.getBankPayInfo().getTransDate()));
                    //交易时间 hhmmss
                    accountThirdPayRecode.setTransTime(HotelUtils.validaStr(accountInfo.getBankPayInfo().getTransTime()));
                    //系统参考号
                    accountThirdPayRecode.setRef(HotelUtils.validaStr(accountInfo.getBankPayInfo().getRef()));
                    //授权号
                    accountThirdPayRecode.setAuth(HotelUtils.validaStr(accountInfo.getBankPayInfo().getAuth()));
                    //商户号
                    accountThirdPayRecode.setMid(HotelUtils.validaStr(accountInfo.getBankPayInfo().getmId()));
                    //终端号
                    accountThirdPayRecode.setTid(HotelUtils.validaStr(accountInfo.getBankPayInfo().gettId()));
                    //有效期
                    accountThirdPayRecode.setEffectiveDays(HotelUtils.validaStr(accountInfo.getBankPayInfo().getEffectiveDays()));
                    //预授权
                    accountThirdPayRecode.setPayType(3);
                    //营业日
                    accountThirdPayRecode.setBusinessDay(user.getBusinessDay());
                    //日期
                    accountThirdPayRecode.setCreateTime(new Date());
                    accountThirdPayRecode.setClassId(user.getClassId());
                    accountThirdPayRecode.setCreateUserId(user.getUserId());
                    accountThirdPayRecode.setCreateUserName(user.getUserName());
                    break;
                //会员支付
                case "9600":
                    account.setAccountCode(accountInfo.getAccountCode());
                    account.setThirdAccoutId(accountInfo.getThirdAccoutId());
                    account.setThirdRefundState(0);
                    break;
                //会员冻结
                case "9620":
                    account.setAccountCode(accountInfo.getAccountCode());
                    account.setThirdAccoutId(accountInfo.getThirdAccoutId());
                    account.setThirdRefundState(0);
                    break;
                //积分兑换
                case "9630":
                    account.setAccountCode(accountInfo.getAccountCode());
                    account.setThirdAccoutId(accountInfo.getThirdAccoutId());
                    account.setThirdRefundState(0);
                    break;
                //AR账务
                case "9800":
                    account.setAccountCode(accountInfo.getAccountCode());
                    account.setThirdAccoutId(accountInfo.getThirdAccoutId());
                    account.setCompanyId(accountInfo.getCompanyId());
                    account.setCompanyName(accountInfo.getCompanyName());
                    account.setThirdRefundState(0);
                    break;
            }

            if (accountInfo.getGroupAccount() == 1) {
                account.setBegRegistId(accountInfo.getBegRegistId());
                account.setBegRegistPersonId(accountInfo.getBegRegistPersonId());
                account.setGroupAccount(1);
            } else {
                account.setBegRegistId(account.getRegistId());
                account.setBegRegistPersonId(account.getRegistPersonId());
                account.setGroupAccount(0);
            }

//            oprecord.setMainId(account.getAccountId());
//            oprecord.setDescription(account.getPayCodeName() + ",数量:" + account.getSaleNum() + ",总金额为:" + account.getPrice() / 100.0 + "元");
//            oprecords.add(oprecord);
            accountTransaction.addAccountTransaction(account, accountThirdPayRecode, null, regist, bookingOrderDailyPrice);
            //会员相关信息
            responseData.setData(account);


            //推送刷新房态
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        //推送刷新房态
                        baseService.push(user.getHotelGroupId(), user.getHid(), 7, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });
            //将入住单登记状态改为已经
            oprecordInfoRequest.setSessionToken(sessionToken);
            oprecordInfoRequest.setOprecordTemplateId(6);
            JSONObject oprecordJson = new JSONObject();
            oprecordJson.put("payCodeName", account.getPayCodeName());
            oprecordJson.put("sum", account.getSaleNum());
            oprecordJson.put("price", account.getPrice() / 100.0 + "元");
            oprecordInfoRequest.setBusinessId2(oprecordJson.toString());
            oprecordInfoRequests.add(oprecordInfoRequest);
            //处理操作日志
            this.baseService.addOprecordReqs(oprecordInfoRequests);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public ResponseData settleAccount(SettleAccountParam settleAccountParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(settleAccountParam);
            Date date = new Date();
            for (int i = 0; i < settleAccountParam.getAccountList().size(); i++) {

                Account accountInfo = settleAccountParam.getAccountList().get(i);
                accountInfo.setRegistState(1);
                accountInfo.setSettleAccountTime(date);
                int result = accountDao.editAccount(accountInfo);
                if (result < 1) {
                    throw new Exception("部分结账失败");
                }
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public ResponseData cancelAccount(CancelAccountParam cancelAccountParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(cancelAccountParam);
            Date date = new Date();
            for (int i = 0; i < cancelAccountParam.getAccountList().size(); i++) {
                Account account = accountDao.selectById(cancelAccountParam.getAccountList().get(i));
                account.setRegistState(0);
                account.setSettleAccountTime(date);
                int result = accountDao.editAccount(account);
                if (result < 1) {
                    throw new Exception("部分结账失败");
                }
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    public ResponseData balanceAccount(BalanceAccountParam balanceAccountParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = balanceAccountParam.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            String accountId = balanceAccountParam.getAccountId();
            if (accountId == null) {
                throw new Exception("账单编号不能空");
            }
            Integer money = balanceAccountParam.getMoney();
            if (money == null) {
                throw new Exception("金额不明确");
            }
            Account account = accountDao.selectById(accountId);
            if (account == null) {
                throw new Exception("账单异常");
            }
            //原來的金額
            Integer price = account.getPrice();
            Regist regist = null;
            BookingOrder bookingOrder = null;

            if (balanceAccountParam.getRegistId() != null && balanceAccountParam.getRegistId() > 0) {
                regist = registDao.selectById(balanceAccountParam.getRegistId());
                regist.setSumPay(regist.getSumPay() - price + money);
            }
            if (balanceAccountParam.getBookingOrderId() != null && balanceAccountParam.getBookingOrderId() > 0) {
                bookingOrder = bookingOrderDao.selectById(balanceAccountParam.getBookingOrderId());
                bookingOrder.setPayPrice(bookingOrder.getPayPrice() - price + money);
            }

            //设置预授权完成的信息
            account.setUpdateUserId(user.getUserId());
            account.setUpdateTime(new Date());
            account.setClassId(user.getClassId());
            account.setUpdateCalssId(user.getClassId());
            account.setUpdateUserName(user.getUserName());
            /**
             * 银行卡预授权
             */
            if (account.getPayCodeId().equals("9100")) {
                account.setThirdRefundState(2);
                account.setPrice(money);
                account.setRefundPrice(money);
                account.setBusinessDay(user.getBusinessDay());

                AccountThirdPayRecodeSearch accountThirdPayRecodeSearch = new AccountThirdPayRecodeSearch();
                accountThirdPayRecodeSearch.setHid(user.getHid());
                accountThirdPayRecodeSearch.setAccountId(accountId);
                List<AccountThirdPayRecode> accountThirdPayRecodes = accountThirdPayRecodeDao.selectBySearch(accountThirdPayRecodeSearch);
                if (accountThirdPayRecodes == null || accountThirdPayRecodes.size() != 1) {
                    throw new Exception("账单异常");
                }
                AccountThirdPayRecode accountThirdPayRecode = accountThirdPayRecodes.get(0);
                accountThirdPayRecode.setFinishBusinessDay(user.getBusinessDay());
                accountThirdPayRecode.setRefund(money);
                accountThirdPayRecode.setThirdRefundState(2);
                accountThirdPayRecode.setUpdateTime(new Date());
                accountThirdPayRecode.setUpdateCalssId(user.getClassId());
                accountThirdPayRecode.setUpdateUserName(user.getUserName());
                accountThirdPayRecode.setUpdateUserId(user.getUserId());

                //取消预授权
                if (money == 0) {
                    accountThirdPayRecode.setThirdState(2);
                    account.setRegistState(3);
                } else {
                    accountThirdPayRecode.setThirdState(1);
                }


                Oprecord oprecord = new Oprecord(user);
                oprecord.setDescription("完成预授权:" + money);
                oprecords.add(oprecord);
                accountTransaction.balanceAccountTransaction(account, accountThirdPayRecode, regist, bookingOrder, oprecords);
            }
            //会员储值卡
            else if (account.getPayCodeId().equals("9620")) {
                JSONObject postData = new JSONObject();
                postData.put("accountId", account.getThirdAccoutId());
                postData.put("money", money);
                postData.put("sessionToken", sessionToken);
                ResponseData res = memberService.finishMemberFreeze(postData);
                if (res.getCode() != 1) {
                    throw new Exception("解除冻结失败");
                }
                account.setRefundPrice(money);
                account.setThirdRefundState(2);
                account.setPrice(money);
                account.setBusinessDay(user.getBusinessDay());
                account.setPayCodeId("9600");
//                account.setPayCodeId("会员储值卡");
                accountTransaction.balanceAccountTransaction(account, null, regist, bookingOrder, oprecords);
            }
            responseData.setData(account);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData setOff(SetOffAccountParam setOffAccountParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = setOffAccountParam.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /**
             * 账务集合
             */
            ArrayList<Account> accounts = new ArrayList<>();

            Regist regist = null;

            /**
             * 预授权记录集合
             */
            ArrayList<AccountThirdPayRecode> accountThirdPayRecodeList = new ArrayList<>();

            ArrayList<HotelCompanyArRecode> hotelCompanyArRecodes = new ArrayList<>();

            ArrayList<CardConsumptionRecord> cardConsumptionRecords = new ArrayList<>();

            ArrayList<CardFreezeRecord> cardFreezeRecords = new ArrayList<>();

            HotelCompanyAccountInfo hotelCompanyAccountInfo = null;

            ArrayList<Oprecord> oprecords = new ArrayList<>();


            if (setOffAccountParam.getAccountList().get(0).getRegistId() != null) {
                Integer registId = setOffAccountParam.getAccountList().get(0).getRegistId();
                regist = registDao.selectById(registId);
            }

            Date date = new Date();
            for (int i = 0; i < setOffAccountParam.getAccountList().size(); i++) {
                Account accountInfo = setOffAccountParam.getAccountList().get(i);

                if (accountInfo == null) {
                    throw new Exception("为查询到账务信息");
                }
                //2020-08-30更新
//                if (!accountInfo.getCreateUserId().equals(user.getUserId())) {
//                    throw new Exception("不用冲其他人员的账务");
//                }
//                if (!accountInfo.getClassId().equals(user.getClassId())) {
//                    throw new Exception("不允许冲其他班次的账务");
//                }
                if (!accountInfo.getBusinessDay().equals(user.getBusinessDay())) {
                    throw new Exception("不允许冲其他营业日的账务");
                }

                accountInfo.setIsCancel(1);
                //2020-08-27修改了，冲账的状态是 3
                accountInfo.setRegistState(3);

                String payCodeId = accountInfo.getPayCodeId();
                //会员相关
                if (payCodeId.equals("9600") || payCodeId.equals("9620") || payCodeId.equals("9620")) {

                }
                //AR账务
                else if (payCodeId.equals("9800")) {


                    HotelCompanyArRecode hotelCompanyArRecode = hotelCompanyArRecodeDao.selectById(Integer.parseInt(accountInfo.getThirdAccoutId()));
                    hotelCompanyArRecode.setPayState(-1);
                    hotelCompanyArRecodes.add(hotelCompanyArRecode);
                    hotelCompanyAccountInfo = hotelCompanyAccountInfoDao.selectById(hotelCompanyArRecode.getCompanyAccountId());
                    //可用额度增加
                    hotelCompanyAccountInfo.setMaxLimit(hotelCompanyAccountInfo.getMaxLimit() + hotelCompanyArRecode.getMoney());
                    //未核销的减去
                    hotelCompanyAccountInfo.setNoOffWriteMoney(hotelCompanyAccountInfo.getNoOffWriteMoney() - hotelCompanyArRecode.getMoney());
                }
                //国内卡预授权
                else if (payCodeId.equals("9100")) {
                    AccountThirdPayRecodeSearch accountThirdPayRecodeSearch = new AccountThirdPayRecodeSearch();
                    accountThirdPayRecodeSearch.setHid(user.getHid());
                    accountThirdPayRecodeSearch.setAccountId(accountInfo.getAccountId());
                    List<AccountThirdPayRecode> accountThirdPayRecodes = accountThirdPayRecodeDao.selectBySearch(accountThirdPayRecodeSearch);
                    if (accountThirdPayRecodes == null || accountThirdPayRecodes.size() != 1) {
                        throw new Exception("账单异常");
                    }
                    AccountThirdPayRecode accountThirdPayRecode = accountThirdPayRecodes.get(0);
                    accountThirdPayRecode.setFinishBusinessDay(user.getBusinessDay());
                    accountThirdPayRecode.setRefund(0);
                    accountThirdPayRecode.setThirdRefundState(2);
                    accountThirdPayRecode.setUpdateTime(new Date());
                    accountThirdPayRecode.setUpdateCalssId(user.getClassId());
                    accountThirdPayRecode.setUpdateUserName(user.getUserName());
                    accountThirdPayRecode.setThirdState(2);

                    accountInfo.setThirdRefundState(2);
                    accountInfo.setPrice(0);
                    accountInfo.setRefundPrice(0);
                    accountInfo.setRegistState(3);
                    accountThirdPayRecodeList.add(accountThirdPayRecode);
                }
                accounts.add(accountInfo);
            }
            accountTransaction.setOffTransaction(accounts, accountThirdPayRecodeList, hotelCompanyArRecodes, hotelCompanyAccountInfo, cardConsumptionRecords, cardFreezeRecords, oprecords, user, regist);

            //推送刷新房态
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        baseService.push(user.getHotelGroupId(), user.getHid(), 7, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public ResponseData transferAccount(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Oprecord oprecord = new Oprecord(user);
            Oprecord oprecord1 = new Oprecord(user);
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            if (param.get("accounts") == null || param.getString("accounts").equals("")) {
                throw new Exception("账务信息不能空");
            }
            JSONArray accounts = JSONArray.fromObject(URLDecoder.decode(param.getString("accounts"), "utf-8"));
            /**
             * 获取转账前的registId
             */
            if (param.get("registInfoBefore") == null || param.getString("registInfoBefore").equals("")) {
                throw new Exception("登记信息不能空");
            }
            JSONObject registInfoBefore = JSONObject.fromObject(URLDecoder.decode(param.getString("registInfoBefore"), "utf-8"));

            /**
             * 获取转账后的registId
             */
            if (param.get("registInfoAfter") == null || param.getString("registInfoAfter").equals("")) {
                throw new Exception("登记信息不能空");
            }
            int registPersonId = 0;

            if (param.get("registPersonId") != null && param.getInt("registPersonId") > 0) {
                registPersonId = param.getInt("registPersonId");
            }


            JSONObject registInfoAfter = JSONObject.fromObject(URLDecoder.decode(param.getString("registInfoAfter"), "utf-8"));

            JSONObject transferAccountParam = JSONObject.fromObject(URLDecoder.decode(param.getString("transferAccountParam"), "utf-8"));

            ArrayList<AccountCancel> accountCancels = new ArrayList<>();

            int registIdBefore = registInfoBefore.getInt("registId");
            int registIdAfter = registInfoAfter.getInt("registId");


            Regist registBefore = registDao.selectById(registIdBefore);
            Regist registAfter = registDao.selectById(registIdAfter);


            int cost = 0;
            int cash = 0;

            /**
             * 插入新的账务数据到账务表中
             */
            for (int i = 0; i < accounts.size(); i++) {
                Account account = (Account) JSONObject.toBean(accounts.getJSONObject(i), Account.class);
                if (account == null) {
                    throw new Exception("为查询到账务信息");
                }
                if (account.getRegistState() != 0) {
                    continue;
                }
                account.setRegistId(registInfoAfter.getInt("registId"));
                if (registPersonId > 0) {
                    account.setRegistPersonId(registPersonId);
                    account.setAccountCode(URLDecoder.decode(param.getString("personName"), "utf-8"));
                }

                if (account.getPayType() == 1 && account.getIsCancel() != 1) {
                    cost += account.getPrice();
                } else if (account.getPayType() == 2 && account.getIsCancel() != 1) {
                    cash += account.getPrice();
                }

                int value = accountDao.editAccount(account);
                if (value < 1) {
                    throw new Exception("更新账务信息失败");
                }
                Date date = new Date();
                AccountCancel accountCancel = new AccountCancel();
                accountCancel.setHid(account.getHid());
                accountCancel.setHotelGroupId(account.getHotelGroupId());
                accountCancel.setAccountId(account.getAccountId());
                accountCancel.setPrice(account.getPrice());
                accountCancel.setPayType(account.getPayType());
                accountCancel.setPayClassName(account.getPayClassName());
                accountCancel.setPayCodeId(Integer.parseInt(account.getPayCodeId()));
                accountCancel.setPayCodeName(account.getPayCodeName());
                accountCancel.setRoomInfoId(account.getRoomInfoId());
                accountCancel.setRoomNum(account.getRoomNum());
                accountCancel.setAccountCode(account.getAccountCode());
                accountCancel.setAccountCreateUserName(account.getCreateUserName());
                accountCancel.setIsSale(account.getIsSale());
                accountCancel.setBusinessDay(account.getBusinessDay());
                accountCancel.setClassId(account.getClassId());
                accountCancel.setCreateTime(date);
                accountCancel.setCreateUserId(user.getUserId());
                accountCancel.setCreateUserName(user.getUserName());
                accountCancel.setRegistId(registInfoAfter.getInt("registId"));
                accountCancel.setBookingId(registInfoAfter.getInt("bookingOrderId"));
                accountCancel.setRoomNum(registInfoAfter.getString("roomNum"));
                accountCancel.setRoomInfoId(registInfoAfter.getInt("roomNumId"));
                accountCancel.setCancelType(1);
                accountCancel.setSourceRegistId(registInfoBefore.getInt("registId"));
                accountCancel.setSourceRoomNum(registInfoBefore.getString("roomNum"));
                accountCancel.setReason(transferAccountParam != null ? transferAccountParam.containsKey("reason") ? transferAccountParam.getString("reason") : "" : "");
                accountCancel.setRemark(transferAccountParam != null ? transferAccountParam.containsKey("remark") ? transferAccountParam.getString("remark") : "" : "");
                value = accountCancelDao.insert(accountCancel);
                if (value < 1) {
                    throw new Exception("更新账务信息失败");
                }
            }
            registBefore.setSumSale(registBefore.getSumSale() - cost);
            registBefore.setSumPay(registBefore.getSumPay() - cash);
            Integer update = registDao.update(registBefore);
            if (update < 1) {
                throw new Exception("修改主单信息失败");
            }

            registAfter.setSumSale(registAfter.getSumSale() + cost);
            registAfter.setSumPay(registAfter.getSumPay() + cash);
            update = registDao.update(registAfter);
            if (update < 1) {
                throw new Exception("修改主单信息失败");
            }
            oprecord.setRegistId(registBefore.getRegistId());
            oprecord1.setRegistId(registAfter.getRegistId());
            oprecord.setDescription(registBefore.getRoomNum() + ":房间转账到:" + registAfter.getRoomNum() + ":房间 消费:" + cost / 100.0 + "元" + " 付款:" + cash / 100.0 + "元");
            oprecord1.setDescription("由" + registBefore.getRoomNum() + "转入消费:" + cost / 100.0 + "元" + " 付款:" + cash / 100.0 + "元");
            this.addOprecords(oprecord);
            this.addOprecords(oprecord1);

            //推送刷新房态
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        baseService.push(user.getHotelGroupId(), user.getHid(), 7, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return responseData;
    }

    @Override
    public ResponseData transferAccountNew(TransferAccontRequest param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            final TbUserSession user = this.getTbUserSession(param);


            List<String> accountIds = param.getAccountIds();
            if (accountIds == null || accountIds.size() < 1) {
                throw new Exception("请传入账务信息");
            }


            // 新房间
            Regist regist = registDao.selectById(param.getRegistId());

            Integer groupAccount = 0;

            RegistPerson registPerson = null;
            String personName = "";

            if (!param.getRegistId().equals(param.getRegistPersonId())) {
                if (regist == null || !regist.getHid().equals(user.getHid())) {
                    throw new Exception("新转入房间或住客信息不正确");
                }
                if (param.getRegistPersonId() != null && param.getRegistPersonId() > 0) {
                    // 新人
                    registPerson = registPersonDao.selectById(param.getRegistPersonId());

                    if (registPerson == null || !registPerson.getRegistId().equals(regist.getRegistId())) {
                        throw new Exception("房间信息和客人信息不匹配");
                    }
                    personName = registPerson.getPersonName();
                }
            } else {
                groupAccount = 1;
                regist = new Regist();
                regist.setRegistId(param.getRegistId());
                registPerson = new RegistPerson();
                registPerson.setRegistPersonId(param.getRegistId());
                registPerson.setRegistId(param.getRegistId());
                regist.setRoomNum("团账");
            }


            List<Integer> registIds = param.getRegistIds();

            String regIds = "";
            for (Integer key : registIds) {
                regIds += key + ",";
            }
            regIds = regIds.substring(0, regIds.length() - 1);

            // 查询老房间的账务信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setRegistIds(regIds);

            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            Map<String, Account> accountMap = accounts.stream().collect(Collectors.toMap(Account::getAccountId, a -> a, (k1, k2) -> k1));

            // 查询老的房间信息
            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setRegistIds(regIds);
            List<Regist> regists = registDao.selectBySearch(registSearch);

            Map<Integer, Regist> registMap = regists.stream().collect(Collectors.toMap(Regist::getRegistId, a -> a, (k1, k2) -> k1));

            String reason = param.getReason();
            String remark = param.getRemark();

            ArrayList<Oprecord> oprecords = new ArrayList<>();

            ArrayList<AccountCancel> addAccountCancels = new ArrayList<>();
            ArrayList<Account> addAccounts = new ArrayList<>();
            ArrayList<Account> upaAccounts = new ArrayList<>();
            /**
             * 插入新的账务数据到账务表中
             */
            for (String accountId : accountIds) {

                Account account = accountMap.get(accountId);

                if (account == null) {
                    throw new Exception("未查到编号：" + accountId + "的账务信息");
                }
                Regist beforeRegist = registMap.get(account.getRegistId());

                if (account.getBookingId() != null && account.getBookingId() > 0 && (account.getRegistId() == null || account.getRegistId() < 1)) {

                    account.setRoomInfoId(regist.getRoomNumId());
                    account.setRoomNum(regist.getRoomNum());
                    account.setRoomTypeId(regist.getRoomTypeId());
                    account.setRegistId(regist.getRegistId());
                    account.setBegRegistId(regist.getRegistId());
                    account.setBegRegistPersonId(registPerson.getRegistPersonId());
                    account.setRegistPersonName(registPerson.getPersonName());
                    account.setRegistPersonId(registPerson.getRegistPersonId());

                    upaAccounts.add(account);

                    continue;
                }

                if (account.getGroupAccount() != 1 && beforeRegist == null) {
                    throw new Exception("编号：" + accountId + "的账务信息有误");
                }

                // 原有的账务进行修改
                Account upaAccount = new Account();
                BeanUtils.copyProperties(account, upaAccount);
                // 添加新增的账务信息 正 accountId后缀+a
                Account addAccountA = new Account();
                BeanUtils.copyProperties(account, addAccountA);
                // 添加新增的账务信息 负 accountId后缀+b
                Account addAccountB = new Account();
                BeanUtils.copyProperties(account, addAccountB);

                upaAccount.setRegistId(regist.getRegistId());
                upaAccount.setGroupAccount(account.getGroupAccount());
                upaAccount.setRegistPersonId(registPerson.getRegistPersonId());
                upaAccount.setGroupAccount(groupAccount);
                if (account.getGroupAccount() == 1 && account.getBegRegistId() < 1) {
                    upaAccount.setRegistPersonName(registPerson.getPersonName());
                    upaAccount.setRoomInfoId(regist.getRoomNumId());
                    upaAccount.setRoomNum(regist.getRoomNum());
                    upaAccount.setRoomTypeId(regist.getRoomTypeId());
                    upaAccount.setGroupAccount(0);
                }


                upaAccounts.add(upaAccount);

                addAccountA.setIsCancel(4);
                UUID uuid = UUID.randomUUID();
                String uuidStr = uuid.toString().replace("-", "");
                String s = accountId + user.getHid() + uuidStr.substring(2, 9);
                addAccountA.setAccountId(s + "A");
                addAccountA.setRegistState(4);
                addAccounts.add(addAccountA);

                addAccountB.setIsCancel(4);
                addAccountB.setAccountId(s + "B");
                addAccountB.setPrice(account.getPrice() * -1);
                addAccountB.setUintPrice(account.getUintPrice() * -1);
                addAccountB.setRegistState(4);
                addAccountB.setRemark("转出至" + regist.getRoomNum() + "房间");
                addAccounts.add(addAccountB);


                Date date = new Date();
                AccountCancel accountCancel = new AccountCancel();
                accountCancel.setHid(account.getHid());
                accountCancel.setHotelGroupId(account.getHotelGroupId());
                accountCancel.setAccountId(account.getAccountId());
                accountCancel.setPrice(account.getPrice());
                accountCancel.setPayType(account.getPayType());
                accountCancel.setPayClassName(account.getPayClassName());
                accountCancel.setPayCodeId(Integer.parseInt(account.getPayCodeId()));
                accountCancel.setPayCodeName(account.getPayCodeName());
                accountCancel.setRoomInfoId(account.getRoomInfoId());
                accountCancel.setRoomNum(account.getRoomNum());
                accountCancel.setAccountCode(account.getAccountCode());
                accountCancel.setAccountCreateUserName(account.getCreateUserName());
                accountCancel.setIsSale(account.getIsSale());
                accountCancel.setBusinessDay(account.getBusinessDay());
                accountCancel.setClassId(account.getClassId());
                accountCancel.setCreateTime(date);
                accountCancel.setCreateUserId(user.getUserId());
                accountCancel.setCreateUserName(user.getUserName());
                accountCancel.setRegistId(regist.getRegistId());
                accountCancel.setBookingId(regist.getBookingOrderId());
                accountCancel.setRoomNum(regist.getRoomNum());
                accountCancel.setRoomInfoId(regist.getRoomNumId());
                if (registPerson != null) {
                    accountCancel.setRegistPersonId(registPerson.getRegistPersonId());
                    accountCancel.setRegistPersonName(registPerson.getPersonName());
                }


                accountCancel.setCancelType(1);

                if (beforeRegist == null) {
                    beforeRegist = new Regist();
                    beforeRegist.setRegistId(account.getRegistId());
                    beforeRegist.setRoomNum(account.getRoomNum());
                    beforeRegist.setRoomNumId(account.getRegistPersonId());
                    beforeRegist.setPeopleName("团队账" + account.getRegistPersonName());
                }

                Oprecord oprecord = new Oprecord(user);

                accountCancel.setSourceRegistId(beforeRegist.getRegistId());
                accountCancel.setSourceRoomNum(beforeRegist.getRoomNum());
                accountCancel.setSourceRegistPersonId(account.getRegistPersonId());
                accountCancel.setSourceRegistPersonName(account.getRegistPersonName());
                accountCancel.setReason(reason);
                accountCancel.setRemark(remark);
                addAccountCancels.add(accountCancel);


                oprecord.setMainId(beforeRegist.getSn());
                oprecord.setBookingOrderId(beforeRegist.getBookingOrderId());
                oprecord.setRegistId(beforeRegist.getRegistId());
                oprecord.setRoomNum(beforeRegist.getRoomNum());
                oprecord.setType(9);  // 转账
                oprecord.setBcodeO(account.getPayCodeId());
                oprecord.setBcodeT(beforeRegist.getRoomNumId() + "");
                oprecord.setDescription("由房间：" + beforeRegist.getRoomNum() + "/" + account.getRegistPersonName() + "转出" + account.getPayClassName()
                        + ":" + account.getPrice() / 100.0 + "元到" + regist.getRoomNum() + "/" + personName + "账户");

                oprecords.add(oprecord);

                Oprecord oprecordA = new Oprecord(user);
                oprecordA.setMainId(regist.getSn());
                oprecordA.setBookingOrderId(regist.getBookingOrderId());
                oprecordA.setRegistId(regist.getRegistId());
                oprecordA.setRoomNum(regist.getRoomNum());
                oprecordA.setType(9);  // 转账
                oprecordA.setBcodeO(account.getPayCodeId());
                oprecordA.setBcodeT(regist.getRoomNumId() + "");
                oprecordA.setDescription("由房间：" + beforeRegist.getRoomNum() + "/" + account.getRegistPersonName() + "转入" + account.getPayCodeId()
                        + ":" + account.getPrice() / 100.0 + "元到" + regist.getRoomNum() + "/" + personName + "账户");

                oprecords.add(oprecordA);

            }

            accountTransaction.tranAccountFunc(addAccounts, upaAccounts, addAccountCancels);

            this.addOprecords(oprecords);

            //推送刷新房态
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        baseService.push(user.getHotelGroupId(), user.getHid(), 7, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public ResponseData transferAccountForBooking(TransferAccountForBookingParam transferAccountForBookingParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = transferAccountForBookingParam.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if (null == transferAccountForBookingParam.getRegistId()) {
                throw new Exception("需要转入的订单信息不明确");
            }
            if (null == transferAccountForBookingParam.getBookingOrderId()) {
                throw new Exception("订单信息不明确");
            }
            if (null == transferAccountForBookingParam.getAccounts() || transferAccountForBookingParam.getAccounts().size() < 1) {
                throw new Exception("账务清单不明确");
            }
            Regist regist = registDao.selectById(transferAccountForBookingParam.getRegistId());
            if (null == regist) {
                throw new Exception("需要转入的订单信息不明确");
            }
            BookingOrder bookingOrder = bookingOrderDao.selectById(transferAccountForBookingParam.getBookingOrderId());
            if (null == bookingOrder) {
                throw new Exception("订单信息不明确");
            }
            int cost = 0;
            int cash = 0;
            for (int i = 0; i < transferAccountForBookingParam.getAccounts().size(); i++) {
                Account account = transferAccountForBookingParam.getAccounts().get(i);
                account.setBookingId(null);
                account.setRegistId(regist.getRegistId());
                if (transferAccountForBookingParam.getRegistPersonId() > 0) {
                    account.setRegistPersonId(transferAccountForBookingParam.getRegistPersonId());
                    account.setAccountCode(transferAccountForBookingParam.getRegistPersonName());
                }
                if (account.getPayType() == 1 && account.getIsCancel() != 1) {
                    cost += account.getPrice();
                } else if (account.getPayType() == 2 && account.getIsCancel() != 1) {
                    cash += account.getPrice();
                }
                int value = accountDao.editAccount(account);
                if (value < 1) {
                    throw new Exception("更新账务信息失败");
                }
                Date date = new Date();
                AccountCancel accountCancel = new AccountCancel();
                accountCancel.setHid(account.getHid());
                accountCancel.setHotelGroupId(account.getHotelGroupId());
                accountCancel.setAccountId(account.getAccountId());
                accountCancel.setPrice(account.getPrice());
                accountCancel.setPayType(account.getPayType());
                accountCancel.setPayClassName(account.getPayClassName());
                accountCancel.setPayCodeId(Integer.parseInt(account.getPayCodeId()));
                accountCancel.setPayCodeName(account.getPayCodeName());
                accountCancel.setRoomInfoId(account.getRoomInfoId());
                accountCancel.setRoomNum(account.getRoomNum());
                accountCancel.setAccountCode(account.getAccountCode());
                accountCancel.setAccountCreateUserName(account.getCreateUserName());
                accountCancel.setIsSale(account.getIsSale());
                accountCancel.setBusinessDay(account.getBusinessDay());
                accountCancel.setClassId(account.getClassId());
                accountCancel.setCreateTime(date);
                accountCancel.setCreateUserId(user.getUserId());
                accountCancel.setCreateUserName(user.getUserName());
                accountCancel.setRegistId(regist.getRegistId());
                accountCancel.setBookingId(regist.getBookingOrderId());
                accountCancel.setRoomNum(regist.getRoomNum());
                accountCancel.setRoomInfoId(regist.getRoomNumId());
                accountCancel.setCancelType(1);
                accountCancel.setSourceRegistId(bookingOrder.getBookingOrderId());
                accountCancel.setSourceRoomNum("预订单转入");
                accountCancel.setReason(transferAccountForBookingParam.getReason());
                accountCancel.setRemark(transferAccountForBookingParam.getRemark());
                value = accountCancelDao.insert(accountCancel);
                if (value < 1) {
                    throw new Exception("更新账务信息失败");
                }
            }
//            regist.setSumSale(regist.getSumSale() + cost);
//            regist.setSumPay(regist.getSumPay() + cash);
//            Integer update = registDao.update(regist);
//            if (update < 1) {
//                throw new Exception("修改主单信息失败");
//            }

            //推送刷新房态
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        baseService.push(user.getHotelGroupId(), user.getHid(), 7, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });


        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return responseData;
    }

    /**
     * 交接班查询报表
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData searchClassAccount(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Object businessDayObj = param.get("businessDay");
            Object classIdObj = param.get("classId");

            if (businessDayObj != null && classIdObj != null) {

                user.setBusinessDay(Integer.parseInt(businessDayObj.toString()));
                user.setClassId(Integer.parseInt(classIdObj.toString()));

            }

            Object userId = param.get("userId");
            // 结账实收
            int classType = param.getInt("classType");
            if (classType != 1) {
                return this.searchClassAccountJzss(user, userId);
            }

            // 备用金模式
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setClassId(user.getClassId());
            accountSearch.setIsCancel(0);
            accountSearch.setBusinessDay(user.getBusinessDay());
            accountSearch.setIsDump(null);
            if (userId != null && !"".equals(userId.toString())) {
                accountSearch.setCreateUserId(userId.toString());
            }
            Object startTime = param.get("startTime");
            if (startTime != null && !"".equals(startTime.toString())) {
                accountSearch.setStartTime(startTime.toString());
            }
            Object endTime = param.get("endTime");
            if (endTime != null && !"".equals(endTime.toString())) {
                accountSearch.setEndTime(endTime.toString());
            }

            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            HashMap<Integer, Integer> saleMap = new HashMap<>();


            int sumPay = 0;
            int sumSale = 0;

            // 新增的会员冻结和预授权
            int vipYsMoney = 0;
            int ysMoney = 0;

            HashMap<Integer, Integer> payMap = new HashMap<>();
            payMap.put(1, 0);
            payMap.put(11, 0);
            payMap.put(12, 0);

            for (Account account : accounts) {

                // 消费
                if (account.getPayType() == 1) {
                    Integer integer = saleMap.get(account.getAccountType());
                    if (integer == null) {
                        integer = 0;
                    }
                    integer += account.getPrice();
                    sumSale += account.getPrice();
                    saleMap.put(account.getAccountType(), integer);
                    continue;
                }
                Integer integer = payMap.get(account.getPayClassId());

                String payCodeId = account.getPayCodeId();
                if (payCodeId.equals("9100")) {
                    ysMoney += account.getUintPrice();
                    continue;
                }
                if (payCodeId.equals("9620")) {
                    vipYsMoney += account.getUintPrice();
                    continue;
                }
                if (integer == null) {
                    integer = 0;

                }
                integer += account.getPrice();
                sumPay += account.getPrice();
                payMap.put(account.getPayClassId(), integer);
            }

            saleMap.put(0, sumSale);
            payMap.put(0, sumPay);

            // 查询当班已完成的预授权
            AccountThirdPayRecodeSearch accountThirdPayRecodeSearch = new AccountThirdPayRecodeSearch();
            accountThirdPayRecodeSearch.setFinishBusinessDay(user.getBusinessDay());
            accountThirdPayRecodeSearch.setHid(user.getHid());
            accountThirdPayRecodeSearch.setThirdRefundState(2);
            accountThirdPayRecodeSearch.setUpdateCalssId(user.getClassId());
            if (userId != null) {
                accountThirdPayRecodeSearch.setUpdateUserId(userId.toString());
            }
            List<AccountThirdPayRecode> accountThirdPayRecodes = accountThirdPayRecodeDao.selectBySearch(accountThirdPayRecodeSearch);

            //  完成的预授权和会员消费金额
            int sumFinishVipMoney = 0;
            int sumFinishYsMoney = 0;

            for (AccountThirdPayRecode atpr : accountThirdPayRecodes) {
                Integer payType = atpr.getPayType();
                if (payType == 3) {
                    sumFinishYsMoney += atpr.getRefund();
                }
                if (payType == 5) {
                    sumFinishVipMoney += atpr.getRefund();
                }
            }

            HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("saleMap", saleMap);
            objectObjectHashMap.put("payMap", payMap);
            objectObjectHashMap.put("vipYsMoney", vipYsMoney);
            objectObjectHashMap.put("ysMoney", ysMoney);
            objectObjectHashMap.put("sumFinishVipMoney", sumFinishVipMoney);
            objectObjectHashMap.put("sumFinishYsMoney", sumFinishYsMoney);

            responseData.setData(objectObjectHashMap);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 交接班查询结账实收
     *
     * @param user
     * @return
     */
    private ResponseData searchClassAccountJzss(TbUserSession user, Object userId) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        // 查询当前班次已经的班次信息
        RegistSearch registSearch = new RegistSearch();

        registSearch.setHid(user.getHid());
        registSearch.setCheckoutClassId(user.getClassId());
        registSearch.setCheckoutBusinessDay(user.getBusinessDay());
        registSearch.setState(1);
        List<Regist> regists = registDao.selectBySearch(registSearch);

        StringBuilder sb = new StringBuilder();

        for (Regist rs : regists) {
            sb.append(rs.getRegistId());
            sb.append(",");
        }
        sb.append("0");

        // 查询零售账务信息
        AccountSearch accountSearch = new AccountSearch();
        accountSearch.setHid(user.getHid());
        accountSearch.setClassId(user.getClassId());
        accountSearch.setIsCancel(0);
        accountSearch.setIsDump(2);
        accountSearch.setBusinessDay(user.getBusinessDay());
        if (userId != null && !"".equals(userId.toString())) {
            accountSearch.setCreateUserId(userId.toString());
        }
        List<Account> accounts = accountDao.selectBySearch(accountSearch);
        if (accounts == null) {
            accounts = new ArrayList<>();
        }

        AccountSearch regAccountSearch = new AccountSearch();
        regAccountSearch.setIsDump(1);
        regAccountSearch.setRegInKeys(sb.toString());
        regAccountSearch.setHid(user.getHid());
        regAccountSearch.setIsCancel(0);
        if (userId != null && !"".equals(userId.toString())) {
            regAccountSearch.setCreateUserId(userId.toString());
        }
        List<Account> accounts1 = accountDao.selectBySearch(regAccountSearch);
        if (accounts1 == null) {
            accounts1 = new ArrayList<>();
        }

        accounts.addAll(accounts1);

        HashMap<Integer, Integer> saleMap = new HashMap<>();


        int sumPay = 0;
        int sumSale = 0;

        // 新增的会员冻结和预授权
        int vipYsMoney = 0;
        int ysMoney = 0;

        HashMap<Integer, Integer> payMap = new HashMap<>();
        payMap.put(1, 0);
        payMap.put(11, 0);
        payMap.put(12, 0);

        for (Account account : accounts) {

            // 消费
            if (account.getPayType() == 1) {
                Integer integer = saleMap.get(account.getAccountType());
                if (integer == null) {
                    integer = 0;
                }
                integer += account.getPrice();
                sumSale += account.getPrice();
                saleMap.put(account.getAccountType(), integer);
                continue;
            }
            Integer integer = payMap.get(account.getPayClassId());

            String payCodeId = account.getPayCodeId();
            if (payCodeId.equals("9100")) {
                vipYsMoney += account.getPrice();
                continue;
            }
            if (payCodeId.equals("9620")) {
                ysMoney += account.getPrice();
                continue;
            }
            if (integer == null) {
                integer = 0;

            }
            integer += account.getPrice();
            sumPay += account.getPrice();
            payMap.put(account.getPayClassId(), integer);
        }

        saleMap.put(0, sumSale);
        payMap.put(0, sumPay);


        //  完成的预授权和会员消费金额
        int sumFinishVipMoney = vipYsMoney;
        int sumFinishYsMoney = ysMoney;


        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("saleMap", saleMap);
        objectObjectHashMap.put("payMap", payMap);
        objectObjectHashMap.put("vipYsMoney", vipYsMoney);
        objectObjectHashMap.put("ysMoney", ysMoney);
        objectObjectHashMap.put("sumFinishVipMoney", sumFinishVipMoney);
        objectObjectHashMap.put("sumFinishYsMoney", sumFinishYsMoney);

        responseData.setData(objectObjectHashMap);

        return responseData;
    }

    /**
     * 查询结账实收汇总
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData searchcheckoutPayStatistics(CheckoutPayStatisticsRequest param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            // 查询当前班次已经的班次信息
            RegistSearch registSearch = new RegistSearch();
            registSearch.setState(1);
            registSearch.setHid(user.getHid());
            registSearch.setCheckoutClassId(param.getClassId());
            registSearch.setCheckoutBusMin(param.getBusinessDayMin());
            registSearch.setCheckoutBusMax(param.getBusinessDayMax());
            registSearch.setCheckoutOperatorId(param.getCheckoutOperatorId());
            registSearch.setState(1);

            List<Regist> regists = registDao.selectBySearch(registSearch);

            StringBuilder sb = new StringBuilder();

            for (Regist rs : regists) {
                sb.append(rs.getRegistId());
                sb.append(",");
            }
            sb.append("0");

            AccountSummarySearch accountSummarySearch = new AccountSummarySearch();

            accountSummarySearch.setHid(user.getHid());
            accountSummarySearch.setPayType(1);
            accountSummarySearch.setRegInKeys(sb.toString());
            accountSummarySearch.setGroupType(1);

            JSONObject jsonObject = new JSONObject();

            // 消费
            List<AccountSummary> accountSummaries = accountDao.accountSummary(accountSummarySearch);
            jsonObject.put("sales", accountSummaries);

            accountSummarySearch.setPayType(2);
            List<AccountSummary> accountSummaries1 = accountDao.accountSummary(accountSummarySearch);
            jsonObject.put("pay", accountSummaries1);

            responseData.setData(jsonObject);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData searchCheckoutPayDetail(CheckoutPayStatisticsRequest param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            // 查询当前班次已经的班次信息
            RegistSearch registSearch = new RegistSearch();
            registSearch.setState(1);
            registSearch.setHid(user.getHid());
            registSearch.setCheckoutClassId(param.getClassId());
            registSearch.setCheckoutBusMin(param.getBusinessDayMin());
            registSearch.setCheckoutBusMax(param.getBusinessDayMax());
            registSearch.setCheckoutOperatorId(param.getCheckoutOperatorId());
            registSearch.setState(1);

            List<Regist> regists = registDao.selectBySearch(registSearch);

            StringBuilder sb = new StringBuilder();

            for (Regist rs : regists) {
                sb.append(rs.getRegistId());
                sb.append(",");
            }
            sb.append("0");

            // 查询客人信息
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setIsOther(0);
            registPersonSearch.setRegInKeys(sb.toString());
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);

            Map<Integer, RegistPerson> personMap = registPeople.stream().collect(Collectors.toMap(RegistPerson::getRegistId, a -> a, (k1, k2) -> k1));

            // 查询账务信息
            AccountSummarySearch accountSummarySearch = new AccountSummarySearch();

            accountSummarySearch.setHid(user.getHid());
            accountSummarySearch.setPayType(2);
            accountSummarySearch.setRegInKeys(sb.toString());
            accountSummarySearch.setGroupType(6);
            List<AccountSummary> accountSummaries = accountDao.accountSummary(accountSummarySearch);

            HashMap<String, Boolean> otherPayCode = new HashMap<>();
            otherPayCode.put("1", true);
            otherPayCode.put("3", true);
            otherPayCode.put("5", true);
            otherPayCode.put("15", true);
            otherPayCode.put("9", true);
            otherPayCode.put("11", true);
            otherPayCode.put("12", true);


            Integer sumMoney = 0;

            // 获取登记单汇总信息汇总信息
            HashMap<Integer, Integer> registSummary = new HashMap<>();
            HashMap<Integer, Integer> otherRegistSummary = new HashMap<>();
            // 获取付款汇总信息
            HashMap<String, Integer> payClassSummary = new HashMap<>();
            for (AccountSummary accountSummary : accountSummaries) {
                sumMoney += accountSummary.getSumMoney();
                Integer regSummary = registSummary.get(accountSummary.getRegistId());
                if (regSummary == null) {
                    regSummary = 0;
                }
                regSummary += accountSummary.getSumMoney();
                registSummary.put(accountSummary.getRegistId(), regSummary);

                String payClassId = accountSummary.getPayClassId();
                if (otherPayCode.get(payClassId) == null) {
                    Integer otherRegSummary = otherRegistSummary.get(accountSummary.getRegistId());
                    if (otherRegSummary == null) {
                        otherRegSummary = 0;
                    }
                    otherRegSummary += accountSummary.getSumMoney();
                    otherRegistSummary.put(accountSummary.getRegistId(), otherRegSummary);
                    payClassId = "99";
                }

                Integer payClass = payClassSummary.get(payClassId);
                if (payClass == null) {
                    payClass = 0;
                }
                payClass += accountSummary.getSumMoney();
                payClassSummary.put(payClassId, payClass);
            }

            Map<String, AccountSummary> collect = accountSummaries.stream().collect(Collectors.toMap(AccountSummary::getRegPayId, a -> a, (k1, k2) -> k1));

            HashMap<String, Object> data = new HashMap<>();
            data.put("regists", regists);
            data.put("personMap", personMap);
            data.put("payMap", collect);
            data.put("registSummary", registSummary);
            data.put("payClassSummary", payClassSummary);
            data.put("otherRegistSummary", otherRegistSummary);
            data.put("sumMoney", sumMoney);
            responseData.setData(data);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData refundMoney(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            //日志记录
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);

            if (param.get("accountId") == null || param.getString("accountId").equals("")) {
                throw new Exception("帐务信息不能空");
            }
            if (param.get("money") == null || param.getString("money").equals("")) {
                throw new Exception("退款金额不明确");
            }
            Regist regist = null;
            BookingOrder bookingOrder = null;

            String accountId = param.getString("accountId");
            Account account = accountDao.selectById(accountId);
            if (account == null || account.getRegistState() != 0) {
                throw new Exception("帐务信息异常");
            }
            //退款金额乘以100

            int money = (new Double(param.getDouble("money") * 100).intValue());

            //插入一条退款记录
            Account refundAccount = new Account();

            BeanUtils.copyProperties(account, refundAccount);

            String id = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
            //修改
            refundAccount.setAccountId(id);
            refundAccount.setCreateUserName(user.getUserName());
            refundAccount.setClassId(user.getClassId());
            refundAccount.setPrice(0 - money);
            refundAccount.setUpdateTime(new Date());
            refundAccount.setUpdateUserId(user.getUserId());
            refundAccount.setUpdateCalssId(user.getClassId());
            refundAccount.setUpdateUserName(user.getUserName());
            refundAccount.setClassId(user.getClassId());
            refundAccount.setBusinessDay(user.getBusinessDay());
            refundAccount.setIsCancel(0);
            refundAccount.setAccountYear(user.getBusinessYear());
            refundAccount.setCreateUserId(user.getUserId());
            refundAccount.setAccountYearMonth(user.getBusinessMonth());
            refundAccount.setSettleAccountTime(new Date());
            if (param.get("remark") != null && !param.getString("remark").equals("")) {
                refundAccount.setRemark(param.getString("remark"));
            }
            Map<String, Object> postData = new HashMap<>();
            postData.put(ER.SESSION_TOKEN, sessionToken);
            postData.put("mainId", account.getThirdAccoutId());
            postData.put("refundMoney", money);
            Map<String, Object> resultMap = new HashMap<>();

            //支付宝在线支付
            if (account.getPayCodeId().equals("9300")) {
                resultMap = aliPayService.alipayRefund(postData);

                refundAccount.setPayClassId(11);
                refundAccount.setPayClassName("支付宝");
                refundAccount.setPayCodeId("9309");
                refundAccount.setPayCodeName("支付宝退款");

                oprecord.setDescription("支付宝退款:" + param.getDouble("money"));
            }
            //微信在线支付
            else if (account.getPayCodeId().equals("9320")) {
                resultMap = weChatPayService.wechatRefund(postData);

                refundAccount.setPayClassId(12);
                refundAccount.setPayClassName("微信支付");
                refundAccount.setPayCodeId("9329");
                refundAccount.setPayCodeName("微信扫码支付退款");
                oprecord.setDescription("微信扫码支付退款:" + param.getDouble("money"));
            }
            //微信在线支付
            else if (account.getPayCodeId().equals("9340")) {
                resultMap = weChatPayService.wechatMinigroRefund(postData);

                refundAccount.setPayClassId(12);
                refundAccount.setPayClassName("微信支付");
                refundAccount.setPayCodeId("9341");
                refundAccount.setPayCodeName("微信公众号支付退款");
                oprecord.setDescription("微信公众号支付退款:" + param.getDouble("money"));
            }

            if (!resultMap.get(ER.RES).toString().equals(ER.SUCC)) {
                throw new Exception(resultMap.get(ER.MSG).toString());
            }

            //退款成功修改帐务信息
            account.setRefundPrice(money);
            account.setThirdRefundState(1);
            refundAccount.setRefundPrice(money);
            refundAccount.setThirdRefundState(1);
            refundAccount.setGroupAccount(account.getGroupAccount());
            //入住单入账
            if (account.getRegistId() != null && account.getRegistId() > 0 && account.getGroupAccount() != 1) {
                regist = registDao.selectById(account.getRegistId());
                if (regist.getState() == 1) {
                    throw new Exception("登记单状态异常");
                }
                regist.setSumPay(regist.getSumPay() - money);
                //添加日志记录
                oprecord.setRegistId(account.getRegistId());
            } else if (account.getBookingId() != null && account.getBookingId() > 0) {
                bookingOrder = bookingOrderDao.selectById(account.getBookingId());
                //只有预订单是有效的时候可以入账
                if (bookingOrder.getOrderStatus() != 1) {
                    throw new Exception("预订单状态异常无法入账");
                }
                bookingOrder.setPayPrice(bookingOrder.getPayPrice() - money);
                //添加日志记录
                oprecord.setBookingOrderId(account.getBookingId());
            }
            //日志记录
            oprecords.add(oprecord);
            accountTransaction.refundMoneyTransaction(account, refundAccount, oprecords, user, regist, bookingOrder);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public ResponseData thirdAccoutCancel(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            //日志记录
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);

            if (param.get("accountId") == null || param.getString("accountId").equals("")) {
                throw new Exception("帐务信息不能空");
            }

            String accountId = param.getString("accountId");
            Account account = accountDao.selectById(accountId);
            if (account == null || account.getRegistState() != 0) {
                throw new Exception("帐务信息异常");
            }

            Regist regist = null;
            BookingOrder bookingOrder = null;
            //入住单入账
            if (account.getRegistId() != null && account.getRegistId() > 0) {
                regist = registDao.selectById(account.getRegistId());
                if (regist.getState() == 1) {
                    throw new Exception("登记单状态异常");
                }
                regist.setSumPay(regist.getSumPay() - account.getPrice());
                //添加日志记录
                oprecord.setRegistId(account.getRegistId());
            } else if (account.getBookingId() != null && account.getBookingId() > 0) {
                bookingOrder = bookingOrderDao.selectById(account.getBookingId());
                //只有预订单是有效的时候可以入账
                if (bookingOrder.getOrderStatus() != 1) {
                    throw new Exception("预订单状态异常无法入账");
                }
                bookingOrder.setPayPrice(bookingOrder.getPayPrice() - account.getPrice());
                //添加日志记录
                oprecord.setBookingOrderId(account.getBookingId());
            }

            ResponseData res = ResponseData.newSuccessData();
            JSONObject postData = new JSONObject();
            postData.put("aid", account.getThirdAccoutId());
            postData.put("payCostId", account.getPayCodeId());
            postData.put("sessionToken", sessionToken);
            if (account.getPayCodeId().equals("9600") || account.getPayCodeId().equals("9620") || account.getPayCodeId().equals("9630")) {

                res = memberService.strikeMemberConsumption(postData);
                if (res.getCode() != 1) {
                    throw new Exception("会员冲账失败");
                }
            } else if (account.getPayCodeId().equals("9800")) {
                res = companyInfoService.strikeArRecode(postData);
                if (res.getCode() != 1) {
                    throw new Exception("协议单位冲账失败");
                }
            } else if (account.getPayCodeId().equals("3599")) {
                String thirdAccoutId = account.getThirdAccoutId();
                if (thirdAccoutId != null) {
                    breakfastService.breakFastCancel(Integer.parseInt(thirdAccoutId), user.getBusinessDay());
                }
            }

            account.setIsCancel(1);
            account.setRegistState(3);
            account.setThirdRefundState(1);
            account.setRefundPrice(account.getPrice());
            Integer integer = accountDao.editAccount(account);
            if (integer < 1) {
                throw new Exception("修改帐务信息失败");
            }

            /**
             * 插入第三方 冲账记录表中
             */
            AccountCancel accountCancel = new AccountCancel();
            accountCancel.setAccountId(account.getAccountId());
            accountCancel.setPrice(account.getPrice());
            accountCancel.setPayType(account.getPayType());
            accountCancel.setHid(account.getHid());
            accountCancel.setHotelGroupId(account.getHotelGroupId());
            accountCancel.setPayClassId(account.getPayClassId());
            accountCancel.setPayClassName(account.getPayClassName());
            accountCancel.setPayCodeId(Integer.parseInt(account.getPayCodeId()));
            accountCancel.setPayCodeName(account.getPayCodeName());
            accountCancel.setRoomInfoId(account.getRoomInfoId());
            accountCancel.setRoomNum(account.getRoomNum());
            accountCancel.setAccountCode(account.getAccountCode());
            accountCancel.setAccountCreateUserName(account.getCreateUserName());
            accountCancel.setIsSale(account.getIsSale());
            accountCancel.setBusinessDay(user.getBusinessDay());
            accountCancel.setClassId(user.getClassId());
            accountCancel.setCreateTime(new Date());
            accountCancel.setCreateUserId(user.getUserId());
            accountCancel.setBookingId(account.getBookingId());
            accountCancel.setRegistId(account.getRegistId());
            accountCancel.setTeamCodeId(account.getTeamCodeId());
            accountCancel.setCreateUserName(user.getUserName());
            accountCancel.setCancelType(2);
            Integer insert = accountCancelDao.insert(accountCancel);
            if (insert < 1) {
                throw new Exception("冲账失败");
            }
            /**
             * 修改主单的金额
             */
            if (regist != null) {
                Integer update = registDao.update(regist);
                if (update < 1) {
                    throw new Exception("修改主单信息失败");
                }
            } else if (bookingOrder != null) {
                Integer update = bookingOrderDao.editBookingOrder(bookingOrder);
                if (update < 1) {
                    throw new Exception("修改预订主单失败");
                }
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return responseData;
    }

    @Override
    public ResponseData autoAddRoomPrice(TbUserSession user, Integer registId, Integer min) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            // 登记单id
            Regist regist = registDao.selectById(registId);
            if (regist == null || regist.getState() != 0) {
                return responseData;
            }

            // 分钟

            if (min == 0) {

                return responseData;
            }

            // 营业日
            Integer businessDay = user.getBusinessDay();

            long time = new Date().getTime();

            long diff = time - regist.getCheckinTime().getTime();

            if (diff < min) {
                return responseData;
            }

            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(registId);
            registPersonSearch.setRegistState(0);

            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
            RegistPerson registPerson = registPeople.get(0);

            // 查询当天的房价是否产生
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setHid(user.getHid());
            bookingOrderDailyPriceSearch.setRegistId(registId);
            bookingOrderDailyPriceSearch.setDailyTime(businessDay);
            bookingOrderDailyPriceSearch.setDailyState(1);

            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            if (bookingOrderDailyPrices == null || bookingOrderDailyPrices.size() < 1) {
                return responseData;
            }

            BookingOrderDailyPrice bookingOrderDailyPrice = bookingOrderDailyPrices.get(0);

            accountTransaction.autoAddRoomPrice(bookingOrderDailyPrice, regist, registPerson, user);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData searchPersonAccoun(RegistParam param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            final TbUserSession user = this.getTbUserSession(param);
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setRegistIds(param.getRegInKeys());
            /**
             * 新增的
             */
//            List<Integer> stateList = new ArrayList<>();
//            stateList.add(0);
//            stateList.add(1);
//            accountSearch.setAccountStateList(stateList);
            accountSearch.setHid(user.getHid());
            accountSearch.setIsCancel(0);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            Map<Integer, List<Account>> accountMaps = accounts.stream().collect(Collectors.groupingBy(Account::getRegistPersonId));
            HashMap<Integer, HashMap<String, Integer>> resMap = new HashMap<>();
            Set<Integer> integers = accountMaps.keySet();
            HashMap<Integer, Integer> registAccountMap = new HashMap<>();
            for (Integer key : integers) {
                List<Account> accounts1 = accountMaps.get(key);
                HashMap<String, Integer> hashMap = new HashMap<>();
                int sumPay = 0;
                int sumSale = 0;
                int registId = 0;
                for (Account acc : accounts1) {
                    registId = acc.getRegistId();
                    if (acc.getPayType() == 1 && acc.getIsCancel() == 0) {
                        sumSale += acc.getPrice();
                    } else if (acc.getPayType() == 2 && acc.getIsCancel() == 0) {
                        sumPay += acc.getPrice();
                    }
                }
                Integer integer = registAccountMap.get(registId);
                if (integer == null) {
                    integer = 0;
                }
                registAccountMap.put(registId, integer + sumPay - sumSale);
                hashMap.put("sumPay", sumPay);
                hashMap.put("sumSale", sumSale);
                hashMap.put("sum", sumPay - sumSale);
                resMap.put(key, hashMap);
            }

            /**
             * 新的计算方法
             */
//            Map<Integer, List<Account>> registMap = accounts.stream().collect(Collectors.groupingBy(Account::getRegistId));
//            Set<Integer> registList = registMap.keySet();
//            registAccountMap = new HashMap<>();
//            for (Integer key : registList) {
//                List<Account> accountList = registMap.get(key);
//                Integer cost = 0;
//                Integer cash = 0;
//                Integer free = 0;
//                Integer count = 0;
//                for (int i = 0; i < accountList.size(); i++) {
//                    Account account = accountList.get(i);
//
//                    Integer payType = account.getPayType();
//                    Integer isCancel = account.getIsCancel();
//                    Integer price = account.getPrice();
//                    if (payType == 1 && isCancel == 0) {
//                        cost += price;
//                    } else if (payType == 2 && isCancel == 0) {
//                        cash += price;
//                    }
//                    if ((account.getPayCodeId().equals("9100") || account.getPayCodeId().equals("9620")) && account.getThirdRefundState() == 0 && isCancel == 0) {
//                        free += price;
//                    }
//                }
//                cash = cash - free;
//                count = cash + free - cost;
//                if (cash + free >= 0) {
//                    count = cash + free - cost;
//                } else {
//                    count = cash + free + cost;
//                }
//                registAccountMap.put(key,count);
//            }

            responseData.setData(resMap);
            responseData.setData1(registAccountMap);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData addAccountForAr(AddAccountForArRequest addAccountForArRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(addAccountForArRequest);
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);
            oprecord.setType(HOTEL_CONST.LOG_RZ);

            if (null == addAccountForArRequest.getArAccountId()) {
                throw new Exception(HOTEL_CONST.AR_ACCOUNT_IS_NULL);
            }
            Integer arAccountId = addAccountForArRequest.getArAccountId();

            if (null == addAccountForArRequest.getCompanyId()) {
                throw new Exception(HOTEL_CONST.AR_ACCOUNT_IS_NULL);
            }

            if (null == addAccountForArRequest.getCompanyName()) {
                throw new Exception(HOTEL_CONST.AR_ACCOUNT_IS_NULL);
            }

            // 查询协议单位账户
            HotelCompanyAccount hotelCompanyAccount = hotelCompanyAccountDao.selectById(arAccountId);
            if (hotelCompanyAccount == null || !hotelCompanyAccount.getHid().equals(user.getHid())) {
                throw new Exception(HOTEL_CONST.AR_ACCOUNT_IS_NULL);
            }

            HotelCompanyAccountInfo hotelCompanyAccountInfo = hotelCompanyAccountInfoDao.selectById(arAccountId);
            if (hotelCompanyAccountInfo == null) {
                throw new Exception(HOTEL_CONST.AR_ACCOUNT_IS_NULL);
            }

            if (null == addAccountForArRequest.getMoney()) {
                throw new Exception(HOTEL_CONST.MONEY_IS_NULL);
            }
            // 挂账金额
            int money = addAccountForArRequest.getMoney();
            int diff = hotelCompanyAccount.getMaxLimit() - hotelCompanyAccountInfo.getNoOffWriteMoney() - money;
            if (diff < 0) {
                throw new Exception(HOTEL_CONST.AR_ACCOUNT_MONEY_IS_NULL);
            }


            Date date = new Date();
            String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
            Account account = new Account();
            account.setAccountId(accountId);
            account.setHid(user.getHid());
            account.setHotelGroupId(user.getHotelGroupId());
            account.setCreateUserId(user.getUserId());
            account.setCreateUserName(user.getUserName());
            account.setCreateTime(new Date());
            account.setIsCancel(0);
            account.setAccountYear(user.getBusinessYear());
            account.setAccountYearMonth(user.getBusinessMonth());
            account.setBusinessDay(user.getBusinessDay());
            account.setClassId(user.getClassId());
            account.setPrice(money);
            account.setSettleAccountTime(new Date());
            //消费-付款
            account.setPayType(2);
            account.setSaleNum(1);
            account.setUintPrice(money / account.getSaleNum());
            account.setPayClassId(9);
            account.setPayClassName("AR帐");
            account.setPayCodeId("9800");
            account.setPayCodeName("AR帐");
            account.setRegistPersonId(0);
            account.setAccountType(1);
            account.setRemark(HotelUtils.validaStr(addAccountForArRequest.getRemark()));
            account.setReason(HotelUtils.validaStr(addAccountForArRequest.getRemark()));
            account.setRefundPrice(0);
            account.setGroupAccount(0);
            if (addAccountForArRequest.getGroupAccount() != null) {
                account.setGroupAccount(addAccountForArRequest.getGroupAccount());
            }
            account.setAccountCode(addAccountForArRequest.getArAccountId().toString());

            account.setCompanyId(addAccountForArRequest.getCompanyId());
            account.setCompanyName(addAccountForArRequest.getCompanyName());

            if (null != addAccountForArRequest.getGroupAccount() && addAccountForArRequest.getGroupAccount() == 1) {
                account.setBegRegistPersonId(addAccountForArRequest.getBegRegistPersonId());
                account.setBegRegistId(addAccountForArRequest.getBegRegistId());
                account.setGroupAccount(addAccountForArRequest.getGroupAccount());
            }

            HotelCompanyArRecode hotelCompanyArRecode = new HotelCompanyArRecode();
            hotelCompanyArRecode.setCompanyId(hotelCompanyAccount.getHotelCompanyId());
            hotelCompanyArRecode.setGroupCompanyId(hotelCompanyAccount.getGroupCompanyId());
            hotelCompanyArRecode.setCompanyAccountId(hotelCompanyAccount.getId());
            hotelCompanyArRecode.setMoney(money);
            hotelCompanyArRecode.setDepartmentId(Integer.parseInt(user.getUserId()));
            hotelCompanyArRecode.setOperatorId(Integer.parseInt(user.getUserId()));
            hotelCompanyArRecode.setOperatorName(user.getUserName());
            hotelCompanyArRecode.setOperatTime(date);
            hotelCompanyArRecode.setBusinessShiftId(user.getClassId());
            hotelCompanyArRecode.setBusinessDay(user.getBusinessDay());
            hotelCompanyArRecode.setCreateUserId(user.getUserId());
            hotelCompanyArRecode.setCreateUserName(user.getUserName());
            hotelCompanyArRecode.setCreateTime(date);
            hotelCompanyArRecode.setUpdateUserId(user.getUserId());
            hotelCompanyArRecode.setUpdateUserName(user.getUserName());
            hotelCompanyArRecode.setCreateTime(date);
            hotelCompanyArRecode.setHid(user.getHid());
            hotelCompanyArRecode.setHotelGroupId(user.getHotelGroupId());
            hotelCompanyArRecode.setPayState(0);

            Regist regist = null;
            if (null != addAccountForArRequest.getRegistId()) {
                Integer registId = addAccountForArRequest.getRegistId();
                regist = registDao.selectById(registId);
                if (regist == null || (regist.getState() != HOTEL_CONST.REGIST_STATE_IN && regist.getState() != HOTEL_CONST.REGIST_STATE_ON)) {
                    throw new Exception(HOTEL_CONST.REGIST_STATE_IS_ERROR);
                }
                hotelCompanyArRecode.setSettleId(addAccountForArRequest.getRegistId());
                hotelCompanyArRecode.setRoomInfoId(regist.getRoomNumId());
                hotelCompanyArRecode.setRoomNo(regist.getRoomNum());
                hotelCompanyArRecode.setRegistId(regist.getRegistId());
                hotelCompanyArRecode.setSource(HOTEL_CONST.AR_RECORD_SOURCE_REGIST);

                account.setRegistId(regist.getRegistId());
                account.setRoomNum(regist.getRoomNum());
                account.setRoomInfoId(regist.getRoomNumId());
                account.setRoomTypeId(regist.getRoomTypeId());
                account.setRegistState(0);
                account.setTeamCodeId(regist.getTeamCodeId());
                account.setThirdRefundState(0);

                if (null != addAccountForArRequest.getRegistPersonId()) {
                    account.setRegistPersonId(addAccountForArRequest.getRegistPersonId());
                    account.setRegistId(addAccountForArRequest.getRegistId());
                }
                if (null != addAccountForArRequest.getRegistPersonName() && !"".equals(addAccountForArRequest.getRegistPersonName())) {
                    account.setRegistPersonName(addAccountForArRequest.getRegistPersonName());
                    hotelCompanyArRecode.setPersonName(addAccountForArRequest.getRegistPersonName());
                }

                oprecord.setRegistId(regist.getRegistId());
                oprecord.setRoomNum(regist.getRoomNum());

            }

            if (null != addAccountForArRequest.getBookingOrderId()) {
                BookingOrder bookingOrder = bookingOrderDao.selectById(addAccountForArRequest.getBookingOrderId());

                if (bookingOrder.getOrderStatus() != 1 && bookingOrder.getOrderStatus() != 3) {
                    throw new Exception(HOTEL_CONST.BOOKINGORDER_STATE_IS_ERROR);
                }
                hotelCompanyArRecode.setRegistId(bookingOrder.getBookingOrderId());
                account.setBookingId(bookingOrder.getBookingOrderId());
                account.setRegistState(0);
                account.setThirdRefundState(0);
                account.setTeamCodeId(bookingOrder.getTeamCodeId());
                hotelCompanyArRecode.setSource(HOTEL_CONST.AR_RECORD_SOURCE_BOOKING);
                oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            }
            accountTransaction.addAccountForArTransaction(account, hotelCompanyArRecode, hotelCompanyAccount, hotelCompanyAccountInfo, oprecords);
            oprecord.setOperator(user.getUserName());
            oprecord.setOccurTime(HotelUtils.currentTime());
            StringBuilder desc = new StringBuilder();
            desc.append("入账科目: ");
            desc.append(account.getPayCodeName());
            desc.append(",");
            desc.append("单位: ");
            desc.append(hotelCompanyAccount.getName());
            desc.append(",");
            desc.append("总金额: ");
            desc.append(account.getPrice() / 100.0 + "元");
            oprecord.setDescription(desc.toString());
            oprecords.add(oprecord);
            this.addOprecords(oprecords);

            //推送刷新房态
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        //推送刷新房态
                        baseService.push(user.getHotelGroupId(), user.getHid(), 7, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });
            responseData.setData(account);
        } catch (Exception e) {
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData finishMemberFreeze(FinishMemberFreezeRequest finishMemberFreezeRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            final TbUserSession user = this.getTbUserSession(finishMemberFreezeRequest);
            // 完成金额
            Integer money = finishMemberFreezeRequest.getMoney();
            if (null == money) {
                responseData.setCode(-1);
                responseData.setMsg(HOTEL_CONST.PARAM_ERROR);
                return responseData;
            }
            int finishMoney = money;
            if (null == finishMemberFreezeRequest.getThirdAccoutId()) {
                responseData.setCode(-1);
                responseData.setMsg(HOTEL_CONST.PARAM_ERROR);
                return responseData;
            }
            if (null == finishMemberFreezeRequest.getAccountId() || finishMemberFreezeRequest.getAccountId().equals("")) {
                responseData.setCode(-1);
                responseData.setMsg(HOTEL_CONST.PARAM_ERROR);
                return responseData;
            }
            //查询账务信息
            Account account = accountDao.selectById(finishMemberFreezeRequest.getAccountId());
            if (null == account) {
                responseData.setCode(-1);
                responseData.setMsg(HOTEL_CONST.DATA_LIST_IS_NULL);
                return responseData;
            }
            CardFreezeRecord cardFreezeRecord = new CardFreezeRecord();
            cardFreezeRecord = cardFreezeRecordDao.selectById(finishMemberFreezeRequest.getThirdAccoutId());
            if (cardFreezeRecord == null || cardFreezeRecord.getId() < 1) {
                responseData.setCode(-1);
                responseData.setMsg(HOTEL_CONST.DATA_LIST_IS_NULL);
                return responseData;
            }
            Integer freezeMoney = cardFreezeRecord.getMoney();
            if (freezeMoney < money || money < 0) {
                throw new Exception("请输入正确的金额：" + money);
            }
            // 以完成
            if (cardFreezeRecord.getIsFinish() == 1) {
                return responseData;
            }
            // 查询会员信息
            CardInfo cardInfo = cardInfoDao.selectById(cardFreezeRecord.getCardId());
            CardGroupInfo cardGroupInfo = cardGroupInfoDao.selectById(cardFreezeRecord.getCardGroupId());
            if (cardGroupInfo == null) {
                cardGroupInfo = new CardGroupInfo();
            }
            Integer freeze = cardFreezeRecord.getFreeze();
            Integer finishFreeze = 0;
            Integer largessFreeze = cardFreezeRecord.getLargessFreeze();
            Integer finishLargessFreeze = 0;
            Integer groupFreeze = cardFreezeRecord.getGroupFreeze();
            Integer finishGroupFreeze = 0;
            Integer groupLargessFreeze = cardFreezeRecord.getGroupLargessFreeze();
            Integer finishGroupLargessFreeze = 0;
            JSONObject beforData = new JSONObject();
            beforData.put("balance", cardInfo.getBalance());
            beforData.put("largessBalance", cardInfo.getLargessBalance());
            beforData.put("freeze", cardInfo.getFreeze());
            beforData.put("groupBalance", cardGroupInfo.getBalance());
            beforData.put("groupLargessBalance", cardGroupInfo.getLargessBalance());
            beforData.put("groupFreeze", cardGroupInfo.getFreeze());
            // 单店版余额
            if (freeze + largessFreeze > 0) {
                if (money >= freeze) {
                    finishFreeze = freeze;
                    money = money - freeze;
                } else {
                    finishFreeze = money;
                    money = 0;
                }
                if (money >= largessFreeze) {
                    finishLargessFreeze = largessFreeze;
                    money = money - largessFreeze;
                } else {
                    finishLargessFreeze = money;
                    money = 0;
                }
                cardFreezeRecord.setFinishMoney(finishMoney);
                cardFreezeRecord.setIsFinish(1);
                cardFreezeRecord.setFinishFreeze(finishFreeze);
                cardFreezeRecord.setFinishLargessFreeze(finishLargessFreeze);
                cardInfo.setFreeze(cardInfo.getFreeze() - freeze);
                cardInfo.setBalance(cardInfo.getBalance() + freeze - finishFreeze);
                cardInfo.setLargessFreeze(cardInfo.getLargessFreeze() - largessFreeze);
                cardInfo.setLargessBalance(cardInfo.getLargessBalance() + largessFreeze - finishLargessFreeze);
                cardInfo.setLastUserTime(new Date());
            }
            // 集团
            if (groupFreeze + groupLargessFreeze > 0) {
                if (money >= groupFreeze) {
                    finishGroupFreeze = groupFreeze;
                    money = money - groupFreeze;
                } else {
                    finishGroupFreeze = money;
                    money = 0;
                }
                if (money >= groupLargessFreeze) {
                    finishGroupLargessFreeze = groupLargessFreeze;
                    money = money - groupLargessFreeze;
                } else {
                    finishGroupLargessFreeze = money;
                    money = 0;
                }
                cardFreezeRecord.setFinishMoney(finishMoney);
                cardFreezeRecord.setIsFinish(1);
                cardFreezeRecord.setFinishGroupFreeze(finishGroupFreeze);
                cardFreezeRecord.setFinishGroupLargessFreeze(finishGroupLargessFreeze);
                cardGroupInfo.setFreeze(cardGroupInfo.getFreeze() - groupFreeze);
                cardGroupInfo.setBalance(cardGroupInfo.getBalance() + groupFreeze - finishGroupFreeze);
                cardGroupInfo.setLargessFreeze(cardGroupInfo.getLargessFreeze() - groupLargessFreeze);
                cardGroupInfo.setLargessBalance(cardGroupInfo.getLargessBalance() + groupLargessFreeze - finishGroupLargessFreeze);
                cardGroupInfo.setLastUserTime(new Date());
            }
            cardFreezeRecord.setUpdateClassId(user.getClassId());
            cardFreezeRecord.setUpdateTime(new Date());
            cardFreezeRecord.setUpdateUserId(user.getUserId());
            cardFreezeRecord.setUpdateUserName(user.getUserName());
            cardFreezeRecord.setFininshBusinessDay(user.getBusinessDay());
            JSONObject lastData = new JSONObject();
            lastData.put("balance", cardInfo.getBalance());
            lastData.put("largessBalance", cardInfo.getLargessBalance());
            lastData.put("freeze", cardInfo.getFreeze());
            lastData.put("groupBalance", cardGroupInfo.getBalance());
            lastData.put("groupLargessBalance", cardGroupInfo.getLargessBalance());
            lastData.put("groupFreeze", cardGroupInfo.getFreeze());

            /**
             * 添加会员操作日志
             */
            CardOperationRecord cardOperationRecord = new CardOperationRecord();
            cardOperationRecord.setCardId(cardInfo.getId());
            cardOperationRecord.setCardGroupId(cardInfo.getCardGroupId());
            cardOperationRecord.setCardNo(cardInfo.getCardNo());
            cardOperationRecord.setHid(user.getHid());
            cardOperationRecord.setHotelGroupId(user.getHotelGroupId());
            cardOperationRecord.setType(5);
            cardOperationRecord.setLastData(beforData.toString());
            cardOperationRecord.setAfterData(lastData.toString());
            cardOperationRecord.setDesc(cardInfo.getCardNo() + " 完成预授权：" + money / 100.0 + " 元");
            cardOperationRecord.setMoney(money);
            cardOperationRecord.setBusinessDay(user.getBusinessDay());
            cardOperationRecord.setYearMonth(user.getBusinessMonth());
            cardOperationRecord.setYear(user.getBusinessYear());
            cardOperationRecord.setCreateUserId(user.getUserId());
            cardOperationRecord.setCreateUserName(user.getUserName());
            cardOperationRecord.setCreateTime(new Date());
            account.setThirdRefundState(1);
            account.setRefundPrice(finishMemberFreezeRequest.getMoney());
            accountTransaction.executeFinishMemberFreeze(cardFreezeRecord, cardInfo, cardGroupInfo, cardOperationRecord, account);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

}
