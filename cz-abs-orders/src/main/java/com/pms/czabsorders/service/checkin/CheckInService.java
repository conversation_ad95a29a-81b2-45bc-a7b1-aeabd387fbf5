package com.pms.czabsorders.service.checkin;

import com.pms.czabsorders.bean.RegistParam;
import com.pms.czabsorders.bean.WithRoomRequest;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.*;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.request.GetHotelDataInfoParam;
import net.sf.json.JSONObject;

import java.util.List;
import java.util.Map;

/**
 * 入住 续住 结账相关的流程
 */
public interface CheckInService {
    /**
     * 散客步入
     * @param param
     * @return
     */
    public ResponseData blendCheckIn(JSONObject param);

    /**
     * 结账业务
     * @param request
     * @return
     */
    public ResponseData checkout(CheckOutRequest request) throws Exception;

    /**
     * 查询入住信息
     * @param param
     * @return
     */
    public Map<String,Object> searchCheckinDetails(JSONObject param);

    /**
     * 预定入住
     * @param param
     * @return
     */
    public ResponseData bookingCheckIn(JSONObject param);


    public ResponseData bookingOrderCheckIn(BookingOrderCheckInRequest bookingOrderCheckInRequest);

    /**
     * 添加同住人
     * @param param
     * @return
     */
    public Map<String,Object> addGuest(JSONObject param);

    /**
     * 修改在住宾客信息
     * @param param
     * @return
     */
    public Map<String,Object> updateGuest(JSONObject param);


    /**
     * 换房
     * @param param
     * @return
     */
    public ResponseData changeRoom(JSONObject param);

    /**
     * 房型升级
     * @param param
     * @return
     */
    public ResponseData updateRoomType(JSONObject param);

    public Map<String ,Object> updateCheckinType(JSONObject param);


    /**
     * 设置主客
     * @param param
     * @return
     */
    public ResponseData setHostPerson(JSONObject param);

    /**
     * 客人提前离店，或者重新入住
     * @param param
     * @return
     */
    public ResponseData updateRegistState(JSONObject param);

    /**
     * 修改主单信息
     * @param param
     * @return
     */
    public ResponseData updateRegistInfo(JSONObject param);

    /**
     * 联房或者合并团队
     * @param param
     * @return
     */
    public ResponseData teamCombine(JSONObject param);

    /**
     * 钟点房转日租
     * @return
     */
    public ResponseData hourToDay(HourToDayRequest hourToDayRequest);

    /**
     * 反结、重新入住
     * @param param
     * @return
     */
    public ResponseData aginCheckin(JSONObject param);


    /**
     * 修改登记单信息
     * @param registParam
     * @return
     */
    public ResponseData updateRegistResource(RegistParam registParam);

    /**
     * 反结
     * @param registParam
     * @return
     */
    public ResponseData cancelCheckout(RegistParam registParam);


    public ResponseData cancelCheckin(RegistParam registParam);

    /**
     * 修改入住类型
     * @param registParam
     * @return
     */
    public ResponseData updateCheckinType(RegistParam registParam);


    /**
     * 查询酒店数据分析
     * @param param
     * @return
     */
    public ResponseData getHotelDataInfo(GetHotelDataInfoParam param);


    /**
     * 通过房型查询酒店当前的入住情况
     * @param param
     * @return
     */
    public ResponseData getHotelRoomTypeRegister(GetHotelDataInfoParam param);

    /**
     *获取酒店入住率，以及预计出租率
     * @param param
     * @return
     */
    public ResponseData getHotelRegisterNum(GetHotelDataInfoParam param);


    /**
     * 新的联房的方法
     * @param withRoomRequest
     * @return
     */
    public ResponseData withRoom(WithRoomRequest withRoomRequest);

    /**
     * 创建客史档案
     * @param registPersonList
     * @return
     */
    public ResponseData createPersonInfo(List<RegistPerson> registPersonList);



    public ResponseData upLoadPersonImage(UpLoadPersonImageRequest upLoadPersonImageRequest);


    /**
     * 查询房间最一次入住的登记单
     * @return
     */
    public ResponseData getLastCheckoutRegist(GetLastCheckoutRegistRequest getLastCheckoutRegistRequest);

}
