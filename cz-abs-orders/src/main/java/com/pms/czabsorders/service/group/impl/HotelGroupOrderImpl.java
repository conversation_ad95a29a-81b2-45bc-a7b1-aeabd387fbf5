package com.pms.czabsorders.service.group.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.CardGroupInfoSearchRequest;
import com.pms.czabsorders.bean.CardGroupLevelSearchRequest;
import com.pms.czabsorders.bean.CardGroupTypeSearchRequest;
import com.pms.czabsorders.service.group.HotelGroupOrderService;
import com.pms.czabsorders.service.turnAlways.TurnAlwaysService;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomType;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeSearch;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czmembership.bean.company.HotelCompanyAccountInfo;
import com.pms.czmembership.bean.company.HotelCompanyInfo;
import com.pms.czmembership.bean.company.search.HotelCompanyAccountInfoSearch;
import com.pms.czmembership.bean.company.search.HotelCompanyInfoSearch;
import com.pms.czmembership.bean.member.CardGroupInfo;
import com.pms.czmembership.bean.member.CardGroupLevel;
import com.pms.czmembership.bean.member.CardGroupType;
import com.pms.czmembership.bean.member.search.CardGroupInfoSearch;
import com.pms.czmembership.bean.member.search.CardGroupLevelSearch;
import com.pms.czmembership.bean.member.search.CardGroupTypeSearch;
import com.pms.czmembership.dao.company.HotelCompanyAccountInfoDao;
import com.pms.czmembership.dao.company.HotelCompanyInfoDao;
import com.pms.czmembership.dao.member.CardGroupInfoDao;
import com.pms.czmembership.dao.member.CardGroupLevelDao;
import com.pms.czmembership.dao.member.CardGroupTypeDao;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.ERROR_MSG;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.BaseRequest;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.request.BookingOrderPageRequest;
import com.pms.pmsorder.bean.request.GetHotelGroupAvailableRoomRequest;
import com.pms.pmsorder.bean.search.RegistPersonSearch;
import com.pms.pmsorder.dao.BookingOrderDao;
import com.pms.pmsorder.dao.RegistPersonDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Primary
@Slf4j
public class HotelGroupOrderImpl extends BaseService implements HotelGroupOrderService {

    @Autowired
    BookingOrderDao bookingOrderDao;

    @Autowired
    private TurnAlwaysService turnAlwaysService;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private RoomTypeDao roomTypeDao;

    @Autowired
    private CardGroupInfoDao cardGroupInfoDao;

    @Autowired
    private CardGroupLevelDao cardGroupLevelDao;
    @Autowired
    private CardGroupTypeDao cardGroupTypeDao;
    @Autowired
    private RegistPersonDao registPersonDao;
    @Autowired
    private HotelCompanyInfoDao hotelCompanyInfoDao;
    @Autowired
    private HotelCompanyAccountInfoDao hotelCompanyAccountInfoDao;

    @Override
    public ResponseData getHotelGroupOrderList(BookingOrderPageRequest bookingOrderPageRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(bookingOrderPageRequest.getSessionToken());
            bookingOrderPageRequest.setHotelGroupId(tbUserSession.getHotelGroupId());
            Page<BookingOrder> bookingOrders = bookingOrderDao.selectPageByRequest(bookingOrderPageRequest);
            Map<Integer, List<BookingOrder>> collect = bookingOrders.stream().filter(BookingOrder -> {
                return BookingOrder != null && BookingOrder.getHid() != null;
            }).collect(Collectors.groupingBy(BookingOrder::getHid));
            responseData.setData(collect);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getHotelGroupAvailableRoom(GetHotelGroupAvailableRoomRequest getHotelGroupAvailableRoomRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<Integer> hids = getHotelGroupAvailableRoomRequest.getHids();
            if (null == hids || hids.isEmpty()) {
                throw new Exception("缺少酒店参数");
            }
            Map<Integer,Object> res  = new HashMap<>();
            for (int i = 0; i < hids.size(); i++) {
                JSONObject postData = new JSONObject();
                postData.put("hid", hids.get(i));
                postData.put("startTime", getHotelGroupAvailableRoomRequest.getStartTime());
                postData.put("endTime", getHotelGroupAvailableRoomRequest.getEndTime());
                postData.put("type", getHotelGroupAvailableRoomRequest.getType());
                Map<String, Object> stringObjectMap = turnAlwaysService.findGroupHotelRoom(postData);
                log.info("stringObjectMap",stringObjectMap);
                res.put(hids.get(i),stringObjectMap);
            }
            responseData.setData(res);
            log.info("responseData={}",responseData.toString());
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getHotelGroupRoomTypeNum(BaseRequest request) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        String runInfo = "";
        try {
            final TbUserSession user = this.getTbUserSession(request.getSessionToken());
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHotelGroupId(user.getHotelGroupId());
            roomInfoSearch.setState(1);
            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);
            Map<Integer, List<RoomInfo>> collect = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomTypeId));
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("", roomInfos.size());
            Set<Integer> integers = collect.keySet();
            for (Integer key : integers) {
                jsonObject.put(key + "", collect.get(key).size());
            }
            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setHotelGroupId(user.getHotelGroupId());
            roomTypeSearch.setState(1);
            Page<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);
            Map<Integer ,RoomType> roomTypeMap = new HashMap<>();
            for (int i = 0; i < roomTypes.size(); i++) {
                roomTypeMap.put(roomTypes.get(i).getRoomTypeId() , roomTypes.get(i));
            }
            Map<String ,Object> res = new HashMap<>();
            res.put("roomNumMap", jsonObject);
            res.put("roomTypeMap",roomTypeMap);
            responseData.setData(res);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getHotelGroupMember(CardGroupInfoSearchRequest groupInfoSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(groupInfoSearch.getSessionToken());
            groupInfoSearch.setHotelGroupId(tbUserSession.getHotelGroupId());
            CardGroupInfoSearch cardGroupInfoSearch = new CardGroupInfoSearch();
            BeanUtils.copyProperties(groupInfoSearch,cardGroupInfoSearch);
            List<CardGroupInfo> cardGroupInfos = cardGroupInfoDao.selectBySearch(cardGroupInfoSearch);
            responseData.setData(cardGroupInfos);
        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getHotelGroupLevel(CardGroupLevelSearchRequest cardGroupLevelSearchRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(cardGroupLevelSearchRequest.getSessionToken());
            if (tbUserSession == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            cardGroupLevelSearchRequest.setHotelGroupId(tbUserSession.getHotelGroupId());
            CardGroupLevelSearch cardGroupLevelSearch = new CardGroupLevelSearch();
            BeanUtils.copyProperties(cardGroupLevelSearchRequest,cardGroupLevelSearch);
            List<CardGroupLevel> cardGroupLevels = cardGroupLevelDao.selectBySearch(cardGroupLevelSearch);
            responseData.setData(cardGroupLevels);
        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getHotelGroupRegistPerson(RegistPersonSearch registPersonSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(registPersonSearch.getSessionToken());
            if (tbUserSession == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            registPersonSearch.setHotelGroupId(tbUserSession.getHotelGroupId());
            List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
            responseData.setData(registPersonList);
        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getHotelGroupCompany(HotelCompanyInfoSearch hotelCompanyInfoSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(hotelCompanyInfoSearch.getSessionToken());
            if (tbUserSession == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            hotelCompanyInfoSearch.setHotelGroupId(tbUserSession.getHotelGroupId());
            List<HotelCompanyInfo> hotelCompanyInfos = hotelCompanyInfoDao.selectBySearch(hotelCompanyInfoSearch);
            responseData.setData(hotelCompanyInfos);
        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getHotelGroupCompanyAccount(HotelCompanyAccountInfoSearch hotelCompanyAccountInfoSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(hotelCompanyAccountInfoSearch.getSessionToken());
            if (tbUserSession == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            hotelCompanyAccountInfoSearch.setHotelGroupId(tbUserSession.getHotelGroupId());
            Page<HotelCompanyAccountInfo> hotelCompanyAccountInfos = hotelCompanyAccountInfoDao.selectBySearch(hotelCompanyAccountInfoSearch);
            responseData.setData(hotelCompanyAccountInfos);
        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getHotelGroupType(CardGroupTypeSearchRequest cardGroupTypeSearchRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession tbUserSession = this.getTbUserSession(cardGroupTypeSearchRequest.getSessionToken());
            if (tbUserSession == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            cardGroupTypeSearchRequest.setHotelGroupId(tbUserSession.getHotelGroupId());
            CardGroupTypeSearch cardGroupTypeSearch = new CardGroupTypeSearch();
            BeanUtils.copyProperties(cardGroupTypeSearchRequest,cardGroupTypeSearch);
            List<CardGroupType> cardGroupTypes = cardGroupTypeDao.selectBySearch(cardGroupTypeSearch);
            responseData.setData(cardGroupTypes);
        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
