package com.pms.czabsorders.service.roomstateimg.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.RoomStateResult;
import com.pms.czabsorders.service.roomstateimg.RoomStateImgService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.room.*;
import com.pms.czhotelfoundation.bean.room.search.*;
import com.pms.czhotelfoundation.bean.setting.HotelInitialData;
import com.pms.czhotelfoundation.bean.setting.search.HotelInitialDataSearch;
import com.pms.czhotelfoundation.dao.room.*;
import com.pms.czhotelfoundation.dao.setting.HotelInitialDataDao;
import com.pms.czmembership.bean.company.HotelCompanyInfo;
import com.pms.czmembership.bean.company.search.HotelCompanyInfoSearch;
import com.pms.czmembership.dao.company.HotelCompanyInfoDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.room.ROOM_AUXILIARY;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.search.AccountSummarySearch;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.request.BookingOrderPageRequest;
import com.pms.pmsorder.bean.request.RegistPageRequest;
import com.pms.pmsorder.bean.search.*;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.bean.view.BookingOrderRoomNumView;
import com.pms.pmsorder.dao.*;
import com.pms.pmsorder.dao.RegistDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
@Primary
@Slf4j
public class RoomStateImgServiceImpl extends BaseService implements RoomStateImgService {


    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private RegistGroupDao registGroupDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private RoomRepairRecordHistoryDao roomRepairRecordDao;

    @Autowired
    private RegistPersonDao registPersonDao;


    @Autowired
    private RoomAuxiliaryDao roomAuxiliaryDao;

    @Autowired
    private HotelCompanyInfoDao hotelCompanyInfoDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private RoomTypeDao roomTypeDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;


    @Autowired
    private HotelInitialDataDao hotelInitialDataDao;

    java.text.DecimalFormat df = new java.text.DecimalFormat("#.00");

    /**
     * 查询房态图方法
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData searchRoomStateBag(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            ExecutorService service = Executors.newCachedThreadPool();

            final Map jsonObject = new HashMap<Object, Object>();

            final CountDownLatch cdOrder = new CountDownLatch(1);
            final CountDownLatch cdAnswer = new CountDownLatch(3);

            Integer groupType = 0;
            Object groupType1 = param.get("groupType");
            if (groupType1 != null) {
                groupType = param.getInt("groupType");
            }

            Integer finalGroupType = groupType;

            final SortedMap<String, JSONObject> stringJSONObjectHashMap = new TreeMap<String, JSONObject>();
            JSONObject noGroup = new JSONObject();
            noGroup.put("groupType", groupType);
            noGroup.put("id", 0);
            noGroup.put("name", "未分组");
            noGroup.put("list", new JSONArray());

            JSONArray noGroupList = new JSONArray();


            /**
             * 1.查询房间信息
             */

            // 房间信息相关
            Runnable roomRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
                        roomInfoSearch.setHid(user.getHid());
                        roomInfoSearch.setState(1);
                        List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);

                        jsonObject.put("roomInfos", roomInfos);

                        RoomRepairRecordHistorySearch roomRepairRecordSearch = new RoomRepairRecordHistorySearch();
                        roomRepairRecordSearch.setHid(user.getHid());
                        roomRepairRecordSearch.setState(0);
                        List<RoomRepairRecordHistory> roomRepairRecords = roomRepairRecordDao.selectBySearch(roomRepairRecordSearch);

                        Map<Integer, RoomRepairRecordHistory> roomRepairRecordMap = roomRepairRecords.stream().collect(Collectors.toMap(RoomRepairRecordHistory::getRoomId, a -> a, (k1, k2) -> k1));

                        jsonObject.put("roomRepairRecordMap", roomRepairRecordMap);


                        if (finalGroupType == 1) {  // 房型分组
                            Map<Integer, RoomInfo> collect = roomInfos.stream().collect(Collectors.toMap(RoomInfo::getRoomTypeId, a -> a, (k1, k2) -> k1));

                            Set<Integer> integers = collect.keySet();

                            for (Integer keys : integers) {

                                JSONObject rtJson = new JSONObject();
                                rtJson.put("groupType", finalGroupType);
                                rtJson.put("id", collect.get(keys).getRoomTypeId());
                                rtJson.put("name", collect.get(keys).getRoomTypeName());
                                rtJson.put("list", new JSONArray());
                                stringJSONObjectHashMap.put(collect.get(keys).getRoomTypeId() + "", rtJson);
                            }

                        } else if (finalGroupType == 2) { // 楼层分组
                            Map<Integer, RoomInfo> collect = roomInfos.stream().collect(Collectors.toMap(RoomInfo::getFloorId, a -> a, (k1, k2) -> k1));
                            Set<Integer> integers = collect.keySet();

                            for (Integer keys : integers) {

                                Integer floorId = collect.get(keys).getFloorId();
                                if (floorId == null || floorId == 0) {
                                    floorId = 9999999;
                                    collect.get(keys).setFloorName("未选楼层");
                                    collect.get(keys).setBuildingName("");
                                }
                                JSONObject flJson = new JSONObject();
                                flJson.put("groupType", finalGroupType);
                                flJson.put("id", floorId);
                                flJson.put("name", collect.get(keys).getFloorName() + "--" + collect.get(keys).getBuildingName());
                                flJson.put("list", new JSONArray());
                                stringJSONObjectHashMap.put(floorId + "", flJson);

                            }
                        }

                        cdOrder.await();
                        cdAnswer.countDown();
                    } catch (Exception e) {
                        cdAnswer.countDown();
                        log.error("",e);
                    }
                }
            };
            service.execute(roomRunnable);

            // 查询账务信息
            Runnable accountRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        AccountSummarySearch accountSummarySearch = new AccountSummarySearch();
                       /* accountSummarySearch.setHid(user.getHid());
                        accountSummarySearch.setGroupType(4);
                        accountSummarySearch.setRegistState(0);
                        accountSummarySearch.setPayType(1);

                        // 总付款总消费
                        List<AccountSummary> accountSummaries = accountDao.accountSummary(accountSummarySearch);
                        Map<Integer, AccountSummary> payAccountSummaries = accountSummaries.stream().collect(Collectors.toMap(AccountSummary::getRegistId, a -> a, (k1, k2) -> k1));

                        // 总消费
                        accountSummarySearch.setPayType(2);
                        List<AccountSummary> accountSummaries1 = accountDao.accountSummary(accountSummarySearch);
                        Map<Integer, AccountSummary> saleAccountSummaries = accountSummaries1.stream().collect(Collectors.toMap(AccountSummary::getRegistId, a -> a, (k1, k2) -> k1));

                        if(saleAccountSummaries==null){
                            saleAccountSummaries = new HashMap<>();
                        }

                        if(payAccountSummaries == null){
                            payAccountSummaries = new HashMap<>();
                        }

                        jsonObject.put("payMap", payAccountSummaries);
                        jsonObject.put("saleMap", saleAccountSummaries);
*/


                        RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
                        roomAuxiliaryRelationSearch.setHid(user.getHid());
                        List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

                        /**
                         * 辅助 房态id_当前辅助房态房间数量
                         */
                        HashMap<Integer, Integer> idForNum = new HashMap<>();

                        /**
                         *  房间id下有多少 辅助房态id
                         */
                        for (int i = 1; i <= 19; i++) {
                            idForNum.put(i, 0);
                        }
                        for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {
                            Integer relationId = roomAuxiliaryRelation.getRoomAuxiliaryId();
                            if(relationId<3){
                                continue;
                            }
                            Integer num = idForNum.get(relationId);
                            if (num == null) {
                                num = 0;
                            }
                            num++;
                            idForNum.put(relationId, num);
                        }

                        jsonObject.put("idForNum", idForNum);

                        Map<Integer, List<RoomAuxiliaryRelation>> collect = roomAuxiliaryRelations.stream().collect(Collectors.groupingBy(RoomAuxiliaryRelation::getRoomId));
                        jsonObject.put("roomAuxiliaryMap", collect);

                        // 预订房型
                        BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                        bookingOrderRoomNumSearch.setHid(user.getHid());
                        bookingOrderRoomNumSearch.setOrderState(1);
                        bookingOrderRoomNumSearch.setIsCheckout(0);
                        bookingOrderRoomNumSearch.setRowRoom(1);
                        bookingOrderRoomNumSearch.setIsCheckin(0);

                        List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
                        Map<Integer, List<BookingOrderRoomNum>> collect1 = bookingOrderRoomNums.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getRoomNumId));

                        jsonObject.put("bookingOrderRoomNumsMap", collect1);
                        cdOrder.await();
                        cdAnswer.countDown();
                    } catch (Exception e) {
                        cdAnswer.countDown();
                        log.error("",e);
                    }
                }
            };
            service.execute(accountRunnable);

            // 查询团队、预订、登记信息
            Runnable orderRunnable = new Runnable() {
                @Override
                public void run() {
                    try {
                        // 登记信息
                        RegistSearch registSearch = new RegistSearch();
                        registSearch.setHid(user.getHid());
                        registSearch.setState(0);
                        List<Regist> regists = registDao.selectBySearch(registSearch);

                        RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                        registPersonSearch.setHid(user.getHid());
                        registPersonSearch.setRegistState(0);
                        List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
                        Map<Integer, List<RegistPerson>> collect = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));

                        Map<Integer, Regist> registMap = new HashMap<Integer, Regist>();

                        HashMap<Integer, Boolean> idForMap = new HashMap<>();

                        for (Regist regist : regists) {

                            List<RegistPerson> registPeople1 = collect.get(regist.getRegistId());
                            if (registPeople1 == null) {
                                registPeople1 = new ArrayList<>();
                            }
                            String name = "";

                            for (RegistPerson registPerson : registPeople1) {
                                name += registPerson.getPersonName() + " ";
                            }

                            regist.setPeoples(registPeople1);
                            regist.setPeopleName(name);
                            registMap.put(regist.getRoomNumId(), regist);

                            idForMap.put(regist.getTeamCodeId(), true);

                        }

                        // 团队信息
                        RegistGroupSearch registGroupSearch = new RegistGroupSearch();
                        registGroupSearch.setHid(user.getHid());
                        registGroupSearch.setState(1);
                        List<RegistGroup> registGroups = registGroupDao.selectBySearch(registGroupSearch);

                        Map<Integer, RegistGroup> groupMap = new HashMap<>();

                        for (RegistGroup registGroup : registGroups) {
                            if (idForMap.get(registGroup.getRegistGroupId()) == null) {
                                continue;
                            }
                            groupMap.put(registGroup.getRegistGroupId(), registGroup);
                        }

                        RegistGroup registGroup = new RegistGroup();
                        registGroup.setHid(user.getHid());
                        registGroup.setGroupName("全部团队");
                        registGroup.setRegistGroupId(0);
                        groupMap.put(0, registGroup);

                        jsonObject.put("groupMap", groupMap);

                        jsonObject.put("registMap", registMap);

                        cdOrder.await();
                        cdAnswer.countDown();
                    } catch (Exception e) {
                        cdAnswer.countDown();
                        log.error("",e);
                    }
                }
            };
            service.execute(orderRunnable);

            cdOrder.countDown();
            cdAnswer.await();

            // 组装数据
            // 房间
            ArrayList<RoomInfo> roomInfos = (ArrayList<RoomInfo>) JSONArray.toList(JSONArray.fromObject(jsonObject.get("roomInfos")), RoomInfo.class);

            // 维修房
            Map<Integer, RoomRepairRecordHistory> roomRepairRecordMap = (Map<Integer, RoomRepairRecordHistory>) jsonObject.get("roomRepairRecordMap");


            // 登记信息
            Map<Integer, Regist> registMap = (Map<Integer, Regist>) jsonObject.get("registMap");

            // 团队信息
            Map<Integer, RegistGroup> groupMap = (Map<Integer, RegistGroup>) jsonObject.get("groupMap");

            // 预订信息
            Map<Integer, List<BookingOrderRoomNum>> bookingOrderRoomNumsMap = (Map<Integer, List<BookingOrderRoomNum>>) jsonObject.get("bookingOrderRoomNumsMap");

            // 辅助房态数亮
            Map<Integer, Integer> idForNum = (Map<Integer, Integer>) jsonObject.get("idForNum");
            // 辅助房态集合
            Map<Integer, List<RoomAuxiliaryRelation>> roomAuxiliaryMap = (Map<Integer, List<RoomAuxiliaryRelation>>) jsonObject.get("roomAuxiliaryMap");

            JSONArray soar = new JSONArray();

            // 当前时间
            long time = new Date().getTime();

            HashMap<Integer, Integer> roomStatePlanJson = new HashMap<>();
            roomStatePlanJson.put(1, 0);
            roomStatePlanJson.put(2, 0);
            roomStatePlanJson.put(3, 0);
            roomStatePlanJson.put(4, 0);
            roomStatePlanJson.put(5, 0);
            roomStatePlanJson.put(6, 0);

            HashMap<Integer, Integer> roomTypeNum = new HashMap<>();


            for (RoomInfo room : roomInfos) {

                Integer roomInfoId = room.getRoomInfoId();

                Integer rtNum = roomTypeNum.get(room.getRoomTypeId());
                if (rtNum == null) {
                    if (groupType == 2) {

                    }
                    rtNum = 0;
                }

                rtNum++;
                roomTypeNum.put(room.getRoomTypeId(), rtNum);


                RoomStateResult roomStateResult = new RoomStateResult();
                roomStateResult.setRoomInfoId(room.getRoomInfoId());
                roomStateResult.setRoomNum(room.getRoomNum());
                roomStateResult.setRoomTypeId(room.getRoomTypeId());
                roomStateResult.setRoomTypeName(room.getRoomTypeName());
                roomStateResult.setRoomNumState(room.getRoomNumState());
                roomStateResult.setBuildingId(room.getBuildingId());
                roomStateResult.setFloorId(room.getFloorId());

                Integer roomNumState = roomStatePlanJson.get(room.getRoomNumState());
                roomNumState++;

                roomStatePlanJson.put(room.getRoomNumState(), roomNumState);

                // 登记数据
                Regist regist = registMap.get(roomInfoId);

                // 是否超时与欠费
                Boolean isCs = false;
                Boolean isQf = false;

                Boolean isZy = false;
                Boolean isMf = false;

                if (regist != null) {

                    roomStateResult.setRegistId(regist.getRegistId());
                    roomStateResult.setBookingOrderId(regist.getBookingOrderId());
                    roomStateResult.setPeopleName(regist.getPeopleName());
                    roomStateResult.setSumPay(regist.getSumPay());
                    roomStateResult.setSumSale(regist.getSumSale());
                    roomStateResult.setResourceId(regist.getResourceId());
                    roomStateResult.setResourceName(regist.getResourceName());
                    roomStateResult.setBeginTime(HotelUtils.parseDate2Str(regist.getCheckinTime()));
                    roomStateResult.setEndTime(HotelUtils.parseDate2Str(regist.getCheckoutTime()));

                    roomStateResult.setRoomRateId(regist.getRoomRateCodeId());
                    roomStateResult.setRateCode(regist.getRoomRateCodeName());

                    // 团队信息
                    roomStateResult.setTeamCodeId(regist.getTeamCodeId());
                    roomStateResult.setTeamCodeName(regist.getTeamCodeName());
                    roomStateResult.setIsMainRoom(regist.getIsMainRoom());

                    // 是否超时
                    Long checkouTimeLong = regist.getCheckouTimeLong();
                    if (time > checkouTimeLong) {
                        isCs = true;
                    }

                    if (regist.getSumPay() - regist.getSumSale() < 0) {
                        isQf = true;
                    }

                    Integer checkinType = regist.getCheckinType();

                    if (checkinType == 4) {
                        isZy = true;

                    } else if (checkinType == 5) {
                        isMf = true;
                    }


                }

                // 维修信息
                RoomRepairRecordHistory roomRepairRecord = roomRepairRecordMap.get(roomInfoId);

                if (roomRepairRecord != null) {
                    roomStateResult.setBeginTime(HotelUtils.parseDate2Str(roomRepairRecord.getBegintime()));
                    roomStateResult.setEndTime(HotelUtils.parseDate2Str(roomRepairRecord.getEndtime()));
                    roomStateResult.setRoomRepairId(roomRepairRecord.getRepairCheckRoomRecordId());
                    roomStateResult.setDescr(roomRepairRecord.getDes());
                }


                // 预订排房信息
                List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumsMap.get(roomInfoId);
                // roomStateResult.setBookingOrderRoomNumList(bookingOrderRoomNums);

                JSONObject rsrJson = JSONObject.fromObject(roomStateResult);
                rsrJson.put("0", true);

                List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryMap.get(roomInfoId);
                if (roomAuxiliaryRelations != null) {
                    for (RoomAuxiliaryRelation rar : roomAuxiliaryRelations) {

                        rsrJson.put(rar.getRoomAuxiliaryId() + "", true);
                    }
                }
                rsrJson.put("show", true);

                if (roomStateResult.getRoomNumState() == 2 || roomStateResult.getRoomNumState() == 4) {
                    rsrJson.put(ROOM_AUXILIARY.ROOM_CLEAR, true);
                    Integer integer = idForNum.get(ROOM_AUXILIARY.ROOM_CLEAR);
                    integer++;
                    idForNum.put(ROOM_AUXILIARY.ROOM_CLEAR, integer);
                }

                // 超时
                if (isCs) {

                    rsrJson.put(ROOM_AUXILIARY.OVERTIME, true);

                    Integer integer = idForNum.get(ROOM_AUXILIARY.OVERTIME);
                    integer++;
                    idForNum.put(ROOM_AUXILIARY.OVERTIME, integer);
                }

                // 欠费
                if (isQf) {

                    rsrJson.put(ROOM_AUXILIARY.ARREARS, true);
                    Integer integer = idForNum.get(ROOM_AUXILIARY.ARREARS);
                    integer++;
                    idForNum.put(ROOM_AUXILIARY.ARREARS, integer);

                }

                // 自用
                if (isZy) {

                    rsrJson.put(ROOM_AUXILIARY.SELF_USER, true);

                    Integer integer = idForNum.get(ROOM_AUXILIARY.SELF_USER);
                    integer++;
                    idForNum.put(ROOM_AUXILIARY.SELF_USER, integer);
                }

                // 免费
                if (isMf) {

                    rsrJson.put(ROOM_AUXILIARY.SELF_FREE, true);

                    Integer integer = idForNum.get(ROOM_AUXILIARY.SELF_FREE);
                    integer++;
                    idForNum.put(ROOM_AUXILIARY.SELF_FREE, integer);
                }

                if (groupType == 0) {
                    noGroupList.add(rsrJson);
                    continue;
                }

                if (groupType == 1) {
                    JSONObject rtJson = stringJSONObjectHashMap.get(roomStateResult.getRoomTypeId() + "");
                    JSONArray rtlist = rtJson.getJSONArray("list");
                    rtlist.add(rsrJson);
                    rtJson.put("list", rtlist);
                    stringJSONObjectHashMap.put(roomStateResult.getRoomTypeId() + "", rtJson);
                    continue;
                }

                if (groupType == 2) {
                    Integer floorId = room.getFloorId();
                    if (floorId == null || floorId == 0) {
                        floorId = 9999999;
                    }
                    JSONObject flJson = stringJSONObjectHashMap.get(floorId + "");
                    JSONArray fllist = flJson.getJSONArray("list");
                    fllist.add(rsrJson);
                    flJson.put("list", fllist);
                    stringJSONObjectHashMap.put(floorId + "", flJson);
                }

            }
            if (groupType == 0) {
                noGroup.put("list", noGroupList);
                stringJSONObjectHashMap.put("noGroup", noGroup);
            }
            HashMap<String, Object> resMap = new HashMap<>();
            resMap.put("idForNum", idForNum);
            resMap.put("roomList", stringJSONObjectHashMap);
            resMap.put("groupMap", groupMap);
            resMap.put("roomStatePlanJson", roomStatePlanJson);
            resMap.put("roomTypeNum", roomTypeNum);
            resMap.put("roomSumNum", roomInfos.size());
            responseData.setData(resMap);

            service.shutdown();
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }


    /**
     * 查询房态图方法 2
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData searchRoomStateBagTwo(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            ExecutorService service = Executors.newCachedThreadPool();


            Integer groupType = 0;
            Object groupType1 = param.get("groupType");
            if (groupType1 != null) {
                groupType = param.getInt("groupType");
            }

            Integer finalGroupType = groupType;

            final JSONObject stringJSONObjectHashMap = new JSONObject();
            JSONObject noGroup = new JSONObject();
            noGroup.put("groupType", groupType);
            noGroup.put("id", 0);
            noGroup.put("name", "未分组");
            noGroup.put("list", new JSONArray());

            JSONArray noGroupList = new JSONArray();

            /**
             * 1.查询房间信息
             */

            // 房间信息相关
            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            roomInfoSearch.setState(1);
            List<RoomInfo> roomInfos = roomInfoDao.findHotelRoomInfo(roomInfoSearch);

            // 对房间按照房间号中的数字的大小进行排序
            // 自定义排序逻辑，提取 RoomNum 中的数字并比较
            roomInfos.sort((r1, r2) -> {
                String roomNum1 = r1.getRoomNum();
                String roomNum2 = r2.getRoomNum();

                // 提取数字部分
                Integer num1 = extractNumber(roomNum1);
                Integer num2 = extractNumber(roomNum2);

                // 如果都不存在数字，默认按字符串比较
                if (num1 == null && num2 == null) {
                    return roomNum1.compareToIgnoreCase(roomNum2);
                }

                // 否则按照数字大小排序
                return Integer.compare(num1 != null ? num1 : 0, num2 != null ? num2 : 0);
            });


            RoomRepairRecordHistorySearch roomRepairRecordSearch = new RoomRepairRecordHistorySearch();
            roomRepairRecordSearch.setHid(user.getHid());
            roomRepairRecordSearch.setState(0);
            List<RoomRepairRecordHistory> roomRepairRecords = roomRepairRecordDao.selectBySearch(roomRepairRecordSearch);

            Map<Integer, RoomRepairRecordHistory> roomRepairRecordMap = roomRepairRecords.stream().collect(Collectors.toMap(RoomRepairRecordHistory::getRoomId, a -> a, (k1, k2) -> k1));

            if (finalGroupType == 1) {  // 房型分组
                RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
                roomTypeSearch.setState(1);
                roomTypeSearch.setHid(user.getHid());
                Page<RoomType> roomTypes = roomTypeDao.selectBySearchBySort(roomTypeSearch);
                for (int i = 0; i < roomTypes.size(); i++) {
                    JSONObject rtJson = new JSONObject();
                    rtJson.put("groupType", finalGroupType);
                    rtJson.put("id", roomTypes.get(i).getRoomTypeId());
                    rtJson.put("name", roomTypes.get(i).getRoomTypeName());
                    rtJson.put("list", new JSONArray());
                    stringJSONObjectHashMap.put(roomTypes.get(i).getRoomTypeId() + "", rtJson);
                }
            } else if (finalGroupType == 2) { // 楼层分组

                HotelInitialDataSearch hotelInitialDataSearch = new HotelInitialDataSearch();
                hotelInitialDataSearch.setHid(user.getHid());
                hotelInitialDataSearch.setValueType(2);
//                            hotelInitialDataSearch.setEnable(1);
                hotelInitialDataSearch.setOrderBy("sort asc");
                Page<HotelInitialData> floorList = hotelInitialDataDao.selectBySearch(hotelInitialDataSearch);
                Map<Integer, RoomInfo> collect = roomInfos.stream().collect(Collectors.toMap(RoomInfo::getFloorId, a -> a, (k1, k2) -> k1));


                for (int i = 0; i < floorList.size(); i++) {
                    Integer floorId = floorList.get(i).getInitialId();
                    if (floorId == null || floorId == 0) {
                        floorId = 9999999;
                        collect.get(floorList.get(i).getInitialId()).setFloorName("未选楼层");
                        collect.get(floorList.get(i).getInitialId()).setBuildingName("");
                    }
                    if (collect.containsKey(floorId)){
                        JSONObject flJson = new JSONObject();
                        flJson.put("groupType", finalGroupType);
                        flJson.put("id", floorId);
                        flJson.put("name", floorList.get(i).getCodeName() + "--" + collect.get(floorList.get(i).getInitialId()).getBuildingName());
                        flJson.put("list", new JSONArray());
                        stringJSONObjectHashMap.put(floorId + "", flJson);
                    }
                }

            }

            // 查询账务信息
            List<RoomAuxiliary> roomAuxiliaries = roomAuxiliaryDao.selectBySearch(new RoomAuxiliarySearch());
            Map<Integer, RoomAuxiliary> auxiliariesMap = roomAuxiliaries.stream().collect(Collectors.toMap(RoomAuxiliary::getRoomAuxiliaryId, a -> a, (k1, k2) -> k1));

            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setHid(user.getHid());
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

            /**
             * 辅助 房态id_当前辅助房态房间数量
             */
            HashMap<Integer, Integer> idForNum = new HashMap<>();

            /**
             *  房间id下有多少 辅助房态id
             */


            for (int i = 1; i <= 19; i++) {
                idForNum.put(i, 0);
            }

            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations1 = new ArrayList<>();
            HashMap<String, Boolean> stringBooleanHashMap = new HashMap<>();

            for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {

                String s = roomAuxiliaryRelation.getRoomId() + "" + roomAuxiliaryRelation.getRoomAuxiliaryId();
                Boolean aBoolean = stringBooleanHashMap.get(s);
                if (aBoolean != null && aBoolean) {
                    continue;
                }
                stringBooleanHashMap.put(s, true);
                Integer relationId = roomAuxiliaryRelation.getRoomAuxiliaryId();
                if(relationId<3||relationId==10){
                    continue;
                }
                Integer num = idForNum.get(relationId);
                if (num == null) {
                    num = 0;
                }
                num++;
                idForNum.put(relationId, num);
                roomAuxiliaryRelations1.add(roomAuxiliaryRelation);
            }

            Map<Integer, List<RoomAuxiliaryRelation>> roomAuxiliaryMap = roomAuxiliaryRelations1.stream().collect(Collectors.groupingBy(RoomAuxiliaryRelation::getRoomId));

            // 预订房型
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setHid(user.getHid());
            bookingOrderRoomNumSearch.setOrderState(1);
            bookingOrderRoomNumSearch.setIsCheckout(0);
            bookingOrderRoomNumSearch.setRowRoom(1);
            bookingOrderRoomNumSearch.setIsCheckin(0);

            String s = HotelUtils.currentDate()+" 00:00:00";
            long time = HotelUtils.parseStr2Date(s).getTime();
            ArrayList<Long> longs = new ArrayList<>();
            longs.add(time/1000);
            bookingOrderRoomNumSearch.setCheckoutTime(longs);

            Page<BookingOrderRoomNumView> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearchView(bookingOrderRoomNumSearch);
            Map<Integer, List<BookingOrderRoomNumView>> bookingOrderRoomNumsMap = bookingOrderRoomNums.stream().collect(Collectors.groupingBy(BookingOrderRoomNumView::getRoomNumId));
            if (bookingOrderRoomNumsMap == null) {
                bookingOrderRoomNumsMap = new HashMap<>();
            }

            HotelCompanyInfoSearch hotelCompanyInfoSearch = new HotelCompanyInfoSearch();
            hotelCompanyInfoSearch.setHid(user.getHid());
            hotelCompanyInfoSearch.setCompanyType(3);
            List<HotelCompanyInfo> hotelCompanyInfos = hotelCompanyInfoDao.selectBySearch(hotelCompanyInfoSearch);
            Map<Integer, HotelCompanyInfo> hotelCompanyMap = hotelCompanyInfos.stream().collect(Collectors.toMap(HotelCompanyInfo::getId, a -> a, (k1, k2) -> k1));

            // 登记信息
            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setState(0);
            List<Regist> regists = registDao.selectBySearch(registSearch);

            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setHid(user.getHid());
            registPersonSearch.setRegistState(0);
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
            Map<Integer, List<RegistPerson>> personMap = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));

            Map<Integer, Regist> registMap = new HashMap<Integer, Regist>();

            HashMap<Integer, Boolean> idForMap = new HashMap<>();

            String registIds = "";

            for (Regist regist : regists) {

                List<RegistPerson> registPeople1 = personMap.get(regist.getRegistId());
                if (registPeople1 == null) {
                    registPeople1 = new ArrayList<>();
                }
                String name = "";

                for (RegistPerson registPerson : registPeople1) {
                    name += registPerson.getPersonName() + " ";
                }

                registIds+= regist.getRegistId()+",";

                regist.setPeoples(registPeople1);
                regist.setPeopleName(name);
                registMap.put(regist.getRoomNumId(), regist);

                idForMap.put(regist.getTeamCodeId(), true);

            }


            Map<Integer, List<Account>> accountMap = new HashMap<>();

            if(regists.size()>0){
                registIds = registIds.substring(0,registIds.length()-1);
                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setHid(user.getHid());
                accountSearch.setRegistIds(registIds);
                accountSearch.setIsCancel(0);
                List<Account> accounts = accountDao.selectBySearch(accountSearch);
                accountMap = accounts.stream().collect(Collectors.groupingBy(Account::getRegistId));
            }


            // 团队信息
            RegistGroupSearch registGroupSearch = new RegistGroupSearch();
            registGroupSearch.setHid(user.getHid());
            registGroupSearch.setState(1);
            List<RegistGroup> registGroups = registGroupDao.selectBySearch(registGroupSearch);

            Map<Integer, RegistGroup> groupMap = new HashMap<>();

            for (RegistGroup registGroup : registGroups) {
                if (idForMap.get(registGroup.getRegistGroupId()) == null) {
                    continue;
                }
                groupMap.put(registGroup.getRegistGroupId(), registGroup);
            }

            RegistGroup registGroup = new RegistGroup();
            registGroup.setHid(user.getHid());
            registGroup.setGroupName("全部团队");
            registGroup.setRegistGroupId(0);
            groupMap.put(0, registGroup);




            // 预订信息

            // 辅助房态数亮


            JSONArray soar = new JSONArray();

            // 当前时间
            long timeNow = new Date().getTime();

            HashMap<Integer, Integer> roomStatePlanJson = new HashMap<>();
            roomStatePlanJson.put(1, 0);
            roomStatePlanJson.put(2, 0);
            roomStatePlanJson.put(3, 0);
            roomStatePlanJson.put(4, 0);
            roomStatePlanJson.put(5, 0);
            roomStatePlanJson.put(6, 0);

            HashMap<Integer, String> resourceMap = new HashMap<>();
            resourceMap.put(1, "散");
            resourceMap.put(2, "会");
            resourceMap.put(5, "Ｔ");
            resourceMap.put(3, "协");
            resourceMap.put(4, "旅");

            HashMap<Integer, Integer> roomTypeNum = new HashMap<>();

            Integer dateInt = HotelUtils.parseDate2Int(new Date());

            for (RoomInfo room : roomInfos) {

                Integer roomInfoId = room.getRoomInfoId();

                String roomMemo = "";
                // 气泡窗说明
                ArrayList<JSONObject> popDesList = new ArrayList<>();

                Integer rtNum = roomTypeNum.get(room.getRoomTypeId());
                if (rtNum == null) {
                    if (groupType == 2) {

                    }
                    rtNum = 0;
                }

                rtNum++;
                roomTypeNum.put(room.getRoomTypeId(), rtNum);

                ArrayList<String> strings = new ArrayList<>();

                RoomStateResult roomStateResult = new RoomStateResult();
                roomStateResult.setRoomInfoId(room.getRoomInfoId());
                roomStateResult.setRoomNum(room.getRoomNum());
                roomStateResult.setRoomTypeId(room.getRoomTypeId());
                roomStateResult.setRoomTypeName(room.getRoomTypeName());
                roomStateResult.setRoomNumState(room.getRoomNumState());
                roomStateResult.setBuildingId(room.getBuildingId());
                roomStateResult.setFloorId(room.getFloorId());

                Integer roomNumState = roomStatePlanJson.get(room.getRoomNumState());
                roomNumState++;

                roomStatePlanJson.put(room.getRoomNumState(), roomNumState);

                // 登记数据
                Regist regist = registMap.get(roomInfoId);

                // 是否超时与欠费
                Boolean isCs = false;
                Boolean isQf = false;

                Boolean isZy = false;
                Boolean isMf = false;

                // 预离
                Boolean isYl = false;

                ArrayList<String> relations = new ArrayList<>();

                if (regist != null) {

                    roomStateResult.setRegistId(regist.getRegistId());
                    roomStateResult.setBookingOrderId(regist.getBookingOrderId());
                    roomStateResult.setPeopleName(regist.getPeopleName());
                    roomStateResult.setSumPay(regist.getSumPay());
                    roomStateResult.setSumSale(regist.getSumSale());
                    roomStateResult.setResourceId(regist.getResourceId());
                    roomStateResult.setResourceName(regist.getResourceName());
                    roomStateResult.setBeginTime(HotelUtils.parseDate2Str(regist.getCheckinTime()));
                    roomStateResult.setEndTime(HotelUtils.parseDate2Str(regist.getCheckoutTime()));

                    roomStateResult.setRoomRateId(regist.getRoomRateCodeId());
                    roomStateResult.setRateCode(regist.getRoomRateCodeName());

                    // 团队信息
                    roomStateResult.setTeamCodeId(regist.getTeamCodeId());
                    roomStateResult.setTeamCodeName(regist.getTeamCodeName());
                    roomStateResult.setIsMainRoom(regist.getIsMainRoom());

                    if (regist.getResourceId() == 5 && regist.getCompayName() != null && regist.getCompayName().length() > 0) {

                        String substring = regist.getCompayName().substring(0, 1);
                        roomStateResult.setResTitle(substring);

                        HotelCompanyInfo hotelCompanyInfo = hotelCompanyMap.get(regist.getCompanyId());
                        if (hotelCompanyInfo != null) {
                            roomStateResult.setOtaType(hotelCompanyInfo.getOtaType());
                            switch (hotelCompanyInfo.getOtaType()) {
                                case 2:
                                    roomStateResult.setOtaIcon("xiecheng");
                                    roomStateResult.setOtaPayType("");
                                    break;
                                case 3:
                                    roomStateResult.setOtaIcon("xiecheng");
                                    roomStateResult.setOtaPayType("到付");
                                    break;
                                case 4:
                                    roomStateResult.setOtaIcon("xiecheng");
                                    roomStateResult.setOtaPayType("闪住");
                                    break;
                                case 5:
                                    roomStateResult.setOtaIcon("meituan");
                                    roomStateResult.setOtaPayType("");
                                    break;
                                case 6:
                                    roomStateResult.setOtaIcon("meituan");
                                    roomStateResult.setOtaPayType("到付");
                                    break;
                                case 7:
                                    roomStateResult.setOtaIcon("meituan");
                                    roomStateResult.setOtaPayType("溜溜住");
                                    break;
                                case 8:
                                    roomStateResult.setOtaIcon("yilong");
                                    roomStateResult.setOtaPayType("");
                                    break;
                                case 9:
                                    roomStateResult.setOtaIcon("yilong");
                                    roomStateResult.setOtaPayType("到付");
                                    break;
                                case 10:
                                    roomStateResult.setOtaIcon("qunaer");
                                    roomStateResult.setOtaPayType("");
                                    break;
                                case 11:
                                    roomStateResult.setOtaIcon("qunaer");
                                    roomStateResult.setOtaPayType("到付");
                                    break;
                                case 12:
                                    roomStateResult.setOtaIcon("feizhu");
                                    roomStateResult.setOtaPayType("");
                                    break;
                                case 13:
                                    roomStateResult.setOtaIcon("feizhu");
                                    roomStateResult.setOtaPayType("到付");
                                    break;
                                case 14:
                                    roomStateResult.setOtaIcon("alxyz");
                                    roomStateResult.setOtaPayType("信用住");
                                case 18:
                                    roomStateResult.setOtaIcon("tilktok");
                                    roomStateResult.setOtaPayType("抖音");
                                    break;
                            }
                        }

                    } else {
                        roomStateResult.setResTitle(resourceMap.get(regist.getResourceId()));
                    }

                    if (regist.getTeamCodeId() > 0) {
                        JSONObject jsonObject1 = new JSONObject();
                        jsonObject1.put("name", "团队名称");
                        jsonObject1.put("value", regist.getTeamCodeName());
                        popDesList.add(jsonObject1);
                    }

                    int sumPay = 0;
                    int sumSale = 0;
                    List<Account> accounts = accountMap.get(regist.getRegistId());

                    if(accounts!=null){

                        for(Account account:accounts){

                            if(account.getPayType()==1){
                                sumSale+=account.getPrice();
                            }else {
                                sumPay+=account.getPrice();
                            }
                        }

                    }
                    regist.setSumSale(sumSale);
                    regist.setSumPay(sumPay);

                    JSONObject startTimeJson = new JSONObject();
                    startTimeJson.put("name", "入住时间");
                    startTimeJson.put("value", HotelUtils.parseDate2Str(regist.getCheckinTime()));
                    popDesList.add(startTimeJson);

                    JSONObject endTimeJson = new JSONObject();
                    endTimeJson.put("name", "预离时间");
                    endTimeJson.put("value", HotelUtils.parseDate2Str(regist.getCheckoutTime()));
                    popDesList.add(endTimeJson);

                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("name", "房价方案");
                    jsonObject1.put("value", regist.getRoomRateCodeName());
                    popDesList.add(jsonObject1);

                    JSONObject salePay = new JSONObject();
                    salePay.put("name", "付款金额");
                    String payStr = "0";
                    if (regist.getSumPay() > 0) {
                        BigDecimal b = new BigDecimal(regist.getSumPay() / 100.0);
                        double f1 = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        payStr = f1 + "";
                    }
                    salePay.put("value", payStr);
                    popDesList.add(salePay);
                    String saleStr = "0";
                    if (regist.getSumSale() > 0) {
                        BigDecimal b = new BigDecimal(regist.getSumSale() / 100.0);
                        double f1 = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        saleStr = f1 + "";
                    }
                    JSONObject saleMemo = new JSONObject();
                    saleMemo.put("name", "消费金额");
                    saleMemo.put("value", saleStr);
                    popDesList.add(saleMemo);

                    // 是否超时
                    Long checkouTimeLong = regist.getCheckouTimeLong();
                    if (time > checkouTimeLong) {
                        isCs = true;
                        // 预离
                        isYl = true;
                    } else {

                        Integer checkOutbusd = HotelUtils.parseDate2Int(regist.getCheckoutTime());

                        if (checkOutbusd.equals(user.getBusinessDay())) {
                            // 预离
                            isYl = true;
                        }

                    }

                    if (regist.getSumPay() - regist.getSumSale() < 0) {
                        isQf = true;
                    }

                    Integer checkinType = regist.getCheckinType();

                    if (checkinType == 4) {
                        isZy = true;

                    } else if (checkinType == 5) {
                        isMf = true;
                    }

                }else {
                    Integer roomState = room.getRoomNumState();
                    // 如果在住状态，且登记单为null,则把房间改成脏房
                    if(roomState==3||roomState==4){
                        roomStateResult.setRoomNumState(2);
                        RoomInfo roomInfo = new RoomInfo();
                        roomInfo.setRoomNumState(2);
                        roomInfo.setRoomInfoId(room.getRoomInfoId());
                        roomInfoDao.editRoomInfo(roomInfo);
                    }

                }
                roomMemo = roomStateResult.getPeopleName();
                // 维修信息
                RoomRepairRecordHistory roomRepairRecord = roomRepairRecordMap.get(roomInfoId);

                if (roomRepairRecord != null && room.getRoomNumState() == 5) {
                    roomStateResult.setBeginTime(HotelUtils.parseDate2Str(roomRepairRecord.getBegintime()));
                    roomStateResult.setEndTime(HotelUtils.parseDate2Str(roomRepairRecord.getEndtime()));
                    roomStateResult.setRoomRepairId(roomRepairRecord.getRepairCheckRoomRecordId());
                    roomStateResult.setDescr(roomRepairRecord.getDes());
                    roomMemo = "维修原因:" + roomRepairRecord.getDes();

                    JSONObject startTimeJson = new JSONObject();
                    startTimeJson.put("name", "开始时间");
                    startTimeJson.put("value", HotelUtils.parseDate2Str(roomRepairRecord.getBegintime()));
                    popDesList.add(startTimeJson);

                    JSONObject endTimeJson = new JSONObject();
                    endTimeJson.put("name", "结束时间");
                    endTimeJson.put("value", HotelUtils.parseDate2Str(roomRepairRecord.getEndtime()));
                    popDesList.add(endTimeJson);

                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("name", "维修原因");
                    jsonObject1.put("value", roomRepairRecord.getDes());
                    popDesList.add(jsonObject1);

                }

                if (roomRepairRecord != null && room.getRoomNumState() == 6 && null != roomRepairRecord.getDes()) {
                    roomStateResult.setRoomRepairId(roomRepairRecord.getRepairCheckRoomRecordId());
                    roomStateResult.setDescr(roomRepairRecord.getDes());
                    roomMemo = "锁定原因:" + roomRepairRecord.getDes();

                    JSONObject jsonObject1 = new JSONObject();
                    jsonObject1.put("name", "锁定原因");
                    jsonObject1.put("value", roomRepairRecord.getDes());
                    popDesList.add(jsonObject1);

                }

                // 预订排房信息
                List<BookingOrderRoomNumView> bookingOrderRoomNumsTMP = bookingOrderRoomNumsMap.get(roomInfoId);
                roomStateResult.setBookingOrderRoomNumList(bookingOrderRoomNumsTMP);

                JSONObject rsrJson = JSONObject.fromObject(roomStateResult);
                rsrJson.put("0", true);
                rsrJson.put("roomMemo", roomMemo);

                if(bookingOrderRoomNumsTMP!=null&&bookingOrderRoomNumsTMP.size()>0){

                    // 是否预抵
                    Boolean isYd = false;

                    for(BookingOrderRoomNumView borv:bookingOrderRoomNumsTMP){
                        Integer integer = HotelUtils.parseDate2Int(borv.getCheckinTime());
                        if(dateInt.equals(integer)){
                            isYd = true;
                            break;
                        }
                    }

                    if(isYd){
                        rsrJson.put(ROOM_AUXILIARY.BOOK_ARRIVALS, true);

                        Integer integer = idForNum.get(ROOM_AUXILIARY.BOOK_ARRIVALS);
                        integer++;
                        idForNum.put(ROOM_AUXILIARY.BOOK_ARRIVALS, integer);

                        strings.add(auxiliariesMap.get(ROOM_AUXILIARY.BOOK_ARRIVALS).getRoomAuxiliaryIcon());
                    }else {
                        rsrJson.put(ROOM_AUXILIARY.BOOK_CREATE, true);

                        Integer integer = idForNum.get(ROOM_AUXILIARY.BOOK_CREATE);
                        integer++;
                        idForNum.put(ROOM_AUXILIARY.BOOK_CREATE, integer);

                        strings.add(auxiliariesMap.get(ROOM_AUXILIARY.BOOK_CREATE).getRoomAuxiliaryIcon());
                    }

                }

                List<RoomAuxiliaryRelation> roomAuxiliaryRelationsTmps = roomAuxiliaryMap.get(roomInfoId);
                if (roomAuxiliaryRelationsTmps != null) {
                    for (RoomAuxiliaryRelation rar : roomAuxiliaryRelationsTmps) {

                        if (rar.getRoomAuxiliaryId()< 3) {
                            continue;
                        }

                        rsrJson.put(rar.getRoomAuxiliaryId() + "", true);

                        if (auxiliariesMap.get(rar.getRoomAuxiliaryId()).getRoomAuxiliaryType() == 3) {
                            continue;
                        }
                        strings.add(auxiliariesMap.get(rar.getRoomAuxiliaryId()).getRoomAuxiliaryIcon());
                    }
                }
                rsrJson.put("show", true);

                if (roomStateResult.getRoomNumState() == 2 || roomStateResult.getRoomNumState() == 4) {
                    rsrJson.put(ROOM_AUXILIARY.ROOM_CLEAR, true);
                    Integer integer = idForNum.get(ROOM_AUXILIARY.ROOM_CLEAR);
                    integer++;
                    idForNum.put(ROOM_AUXILIARY.ROOM_CLEAR, integer);
                }

                // 超时 说明是团队或者联房
                if (regist!=null&&regist.getTeamCodeId()>0) {

                    RegistGroup registGroupTmp = groupMap.get(regist.getTeamCodeId());

                    if(registGroupTmp!=null&&registGroupTmp.getGroupType()==9){
                        rsrJson.put(ROOM_AUXILIARY.JOINT_HOUSING, true);

                        Integer integer = idForNum.get(ROOM_AUXILIARY.JOINT_HOUSING);
                        if(integer==null){
                            integer = 0;
                        }
                        integer++;
                        idForNum.put(ROOM_AUXILIARY.JOINT_HOUSING, integer);

                        strings.add(auxiliariesMap.get(ROOM_AUXILIARY.JOINT_HOUSING).getRoomAuxiliaryIcon());
                    }

                }

                // 超时
                if (isCs) {

                    rsrJson.put(ROOM_AUXILIARY.OVERTIME, true);

                    Integer integer = idForNum.get(ROOM_AUXILIARY.OVERTIME);
                    integer++;
                    idForNum.put(ROOM_AUXILIARY.OVERTIME, integer);

                    strings.add(auxiliariesMap.get(ROOM_AUXILIARY.OVERTIME).getRoomAuxiliaryIcon());
                }

                // 预离
                if (isYl) {

                    rsrJson.put(ROOM_AUXILIARY.PRE_DEPARTURE, true);

                    Integer integer = idForNum.get(ROOM_AUXILIARY.PRE_DEPARTURE);
                    integer++;
                    idForNum.put(ROOM_AUXILIARY.PRE_DEPARTURE, integer);

                    strings.add(auxiliariesMap.get(ROOM_AUXILIARY.PRE_DEPARTURE).getRoomAuxiliaryIcon());
                }

                // 欠费
                if (isQf) {

                    rsrJson.put(ROOM_AUXILIARY.ARREARS, true);
                    Integer integer = idForNum.get(ROOM_AUXILIARY.ARREARS);
                    integer++;
                    idForNum.put(ROOM_AUXILIARY.ARREARS, integer);

                    strings.add(auxiliariesMap.get(ROOM_AUXILIARY.ARREARS).getRoomAuxiliaryIcon());

                }

                // 自用
                if (isZy) {

                    rsrJson.put(ROOM_AUXILIARY.SELF_USER, true);

                    Integer integer = idForNum.get(ROOM_AUXILIARY.SELF_USER);
                    integer++;
                    idForNum.put(ROOM_AUXILIARY.SELF_USER, integer);

                    strings.add(auxiliariesMap.get(ROOM_AUXILIARY.SELF_USER).getRoomAuxiliaryIcon());
                }

                // 免费
                if (isMf) {

                    rsrJson.put(ROOM_AUXILIARY.SELF_FREE, true);

                    Integer integer = idForNum.get(ROOM_AUXILIARY.SELF_FREE);
                    integer++;
                    idForNum.put(ROOM_AUXILIARY.SELF_FREE, integer);

                    strings.add(auxiliariesMap.get(ROOM_AUXILIARY.SELF_FREE).getRoomAuxiliaryIcon());
                }

                // 预订人姓名
                if (room.getRoomNumState() < 3) {
                    if (bookingOrderRoomNumsTMP != null && bookingOrderRoomNumsTMP.size() > 0) {
                        BookingOrderRoomNumView bookingOrderRoomNumView = bookingOrderRoomNumsTMP.get(0);
                        if (HotelUtils.parseDate2Int(bookingOrderRoomNumView.getCheckinTime()).equals(dateInt)) {
                            rsrJson.put("roomMemo", bookingOrderRoomNumView.getBookingName());
                        }
                    }
                }

                rsrJson.put("auxiList", strings);
                rsrJson.put("popDesList", popDesList);

                if (groupType == 0) {
                    noGroupList.add(rsrJson);
                    continue;
                }

                if (groupType == 1) {
                    JSONObject rtJson = stringJSONObjectHashMap.getJSONObject(roomStateResult.getRoomTypeId() + "");
                    if (null == rtJson || rtJson.size() < 1) {
                        continue;
                    }
                    JSONArray rtlist = rtJson.getJSONArray("list");
                    rtlist.add(rsrJson);
                    rtJson.put("list", rtlist);
                    stringJSONObjectHashMap.put(roomStateResult.getRoomTypeId() + "", rtJson);
                    continue;
                }

                if (groupType == 2) {
                    Integer floorId = room.getFloorId();
                    if (floorId == null || floorId == 0) {
                        floorId = 9999999;
                    }
                    JSONObject flJson = stringJSONObjectHashMap.getJSONObject(floorId + "");
                    JSONArray fllist = flJson.getJSONArray("list");
                    fllist.add(rsrJson);
                    flJson.put("list", fllist);
                    stringJSONObjectHashMap.put(floorId + "", flJson);
                }

            }
            if (groupType == 0) {
                noGroup.put("list", noGroupList);
                stringJSONObjectHashMap.put("noGroup", noGroup);
            }

            ArrayList arrayList = new ArrayList(stringJSONObjectHashMap.values());


            HashMap<String, Object> resMap = new HashMap<>();
            resMap.put("idForNum", idForNum);
            resMap.put("roomList", arrayList);
            resMap.put("groupMap", groupMap);
            resMap.put("roomStatePlanJson", roomStatePlanJson);
            resMap.put("roomTypeNum", roomTypeNum);
            resMap.put("roomSumNum", roomInfos.size());
            responseData.setData(resMap);

            service.shutdown();
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }

    /**
     * 从字符串中提取整数，如果不存在数字则返回 null
     */
    private Integer extractNumber(String str) {
        if (str == null || str.isEmpty()) {
            return null;
        }

        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\d+");
        java.util.regex.Matcher matcher = pattern.matcher(str);

        StringBuilder digits = new StringBuilder();

        while (matcher.find()) {
            digits.append(matcher.group());
        }

        if (digits.length() == 0) {
            return null; // 没有任何数字
        }

        try {
            return Integer.parseInt(digits.toString());
        } catch (NumberFormatException e) {
            // 如果超出 Integer 范围，可按需处理：抛异常、返回 null 或 Long 类型
            return null;
        }
    }

    @Override
    public ResponseData roomDayData(RegistPageRequest registSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            TbUserSession user = this.getTbUserSession(registSearch);

            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            roomInfoSearch.setState(1);
            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);

            // 查询在住信息
            RegistSearch registSearch1 = new RegistSearch();
            registSearch1.setHid(user.getHid());
            registSearch1.setState(0);
            List<Regist> regists = registDao.selectBySearch(registSearch1);

            // 在住房
            ArrayList<Regist> checkRegist = new ArrayList<>();
            String checkRegistIds = "";

            // 预离
            int ylNum = 0;

            // 自用
            int zyNum = 0;

            Integer nowDateInt = HotelUtils.parseDate2Int(new Date());

            for (Regist regist : regists) {
                boolean isFj = true;
                Integer checkinType = regist.getCheckinType();
                Integer date2Int = HotelUtils.parseDate2Int(regist.getCheckoutTime());
                if (date2Int.equals(nowDateInt)) {
                    ylNum++;
                    isFj = false;
                }
                if (checkinType == 5 || checkinType == 4) {
                    zyNum++;
                    isFj = false;
                }
                checkRegist.add(regist);
                checkRegistIds += regist.getRegistId() + ",";
//                if (isFj) {
//
//                }
            }
            checkRegistIds += "-999";

            // 平均房价
            Double pjprice = 0.0;
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setDailyTime(user.getBusinessDay());
            if (checkRegist.size() > 0) {
                bookingOrderDailyPriceSearch.setRegistIds(checkRegistIds);
                Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
                pjprice = bookingOrderDailyPrices.stream().collect(Collectors.summarizingInt(BookingOrderDailyPrice::getPrice)).getAverage();
            }

            // 预抵房间
            String substring = HotelUtils.parseDate2Str(new Date()).substring(0, 10);
            Date date = HotelUtils.parseStr2Date(substring + " 00:00:00");
            BookingOrderPageRequest bookingOrderPageRequest = new BookingOrderPageRequest();
            bookingOrderPageRequest.setHid(user.getHid());
            ArrayList<Long> longs = new ArrayList<>();
            longs.add((date.getTime() - 100) / 1000);
            longs.add((date.getTime() + 86400000) / 1000);
            bookingOrderPageRequest.setCheckinTime(longs);

            Page<BookingOrder> bookingOrders = bookingOrderDao.selectPageByRequest(bookingOrderPageRequest);
            // 预抵
            int ydNum = 0;
            for (BookingOrder bo : bookingOrders) {
                if (bo.getOrderStatus() != 1) {
                    continue;
                }
                ydNum += bo.getRoomCount();
            }

            // 维修
            int wxNum = 0;

            // 按照房态分组
            Map<Integer, List<RoomInfo>> roomStateMap = roomInfos.stream().collect(Collectors.groupingBy(RoomInfo::getRoomNumState));

            // 维修数据
            List<RoomInfo> wxList = roomStateMap.get(ROOM_STATUS.OOO);
            if (wxList != null) {
                wxNum = wxList.size();
            }
            //
            JSONObject jsonObject = new JSONObject();
            int bcs = roomInfos.size() - wxNum - zyNum;
            // 1.实时出租率 =（实时在住客房数－自用房）/（总客房数-维修-自用）×100%
            double v = (1.0 * regists.size() - zyNum) / bcs;
            if (Double.isNaN(v)){
                v = 0.0;
            }
            jsonObject.put("d1", v);
            // 2.预计出租率 =（实时在住客房数＋今日预抵客房数－自用房-预离）/（总客房数-维修-自用）×100%
            double v1 = (1.0 * checkRegist.size() +ydNum - zyNum - ylNum) / bcs;
            if (Double.isNaN(v1)){
                v1 = 0.0;
            }
            jsonObject.put("d2", v1);
            // 3. 平均房价
            jsonObject.put("d3", pjprice.intValue());

            responseData.setData(jsonObject);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;
    }
}
