package com.pms.czabsorders.service.ota;

import com.pms.czabsorders.bean.OpenDailyPriceReqDto;
import com.pms.czabsorders.bean.OpenOrderUpdateStatusDto;
import com.pms.czabsorders.feign.OtaFeign;
import com.pms.czpmsutils.thread.ThreadPool;
import com.pms.czpmsutils.thread.ThreadPoolFactory;
import com.pms.czpmsutils.thread.ThreadTypeEnum;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.BookingOrderRoomNum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class OtaChangePushUtil {
    private static final Logger logger = LoggerFactory.getLogger(OtaChangePushUtil.class);

    @Autowired
    private OtaFeign otaFeign;

    public void pushOrderChange(BookingOrder bookingOrder) {
        ThreadPool threadPool = ThreadPoolFactory.getInstance().getThreadPool(ThreadTypeEnum.EXECUTE_OTA_REQ_SEND_TASK);
        threadPool.monitor(ThreadTypeEnum.EXECUTE_OTA_REQ_SEND_TASK.getMessage());
        threadPool.execute(() -> {
            logger.info("ota order change push begin： bookingOrder={}", bookingOrder);
            try {
                OpenOrderUpdateStatusDto openOrderUpdateStatusDto = new OpenOrderUpdateStatusDto();
                openOrderUpdateStatusDto.setSn(bookingOrder.getSn());
                openOrderUpdateStatusDto.setNoticeType(3);
                otaFeign.openOrderUpdateStatus(openOrderUpdateStatusDto);
                logger.info("ota order：{} change push end",bookingOrder.getBookingOrderId());
            } catch (Exception e) {
                logger.info("ota order change push begin： bookingOrder={}",e);
            }
        });
    }

    public void pushOrderAndNumChange(BookingOrder bookingOrder, List<BookingOrderRoomNum> bookingOrderRoomNumList) {
        ThreadPool threadPool = ThreadPoolFactory.getInstance().getThreadPool(ThreadTypeEnum.EXECUTE_OTA_REQ_SEND_TASK);
        threadPool.monitor(ThreadTypeEnum.EXECUTE_OTA_REQ_SEND_TASK.getMessage());
        threadPool.execute(() -> {
            logger.info("ota order and num change push begin： bookingOrder={}", bookingOrder);
            try {
                List<Integer> roomTypeIds = bookingOrderRoomNumList.stream()
                        .filter(item->item.getRoomTypeId() != null)
                        .map(BookingOrderRoomNum::getRoomTypeId)
                        .distinct()
                        .collect(Collectors.toList());
                //推送OTA订单状态信息
                OpenOrderUpdateStatusDto openOrderUpdateStatusDto = new OpenOrderUpdateStatusDto();
                openOrderUpdateStatusDto.setSn(bookingOrder.getSn());
                openOrderUpdateStatusDto.setNoticeType(3);
                this.otaFeign.openOrderUpdateStatus(openOrderUpdateStatusDto);
                //推送OTA房型价态量信息
                if (CollectionUtils.isEmpty(roomTypeIds)) {
                    logger.error("预订单Sn为{}的房型为空，请检查后重试", bookingOrder.getSn());
                    throw new RuntimeException("预订单Sn为" +bookingOrder.getSn()  + "的房型为空，请检查后重试");
                }
                OpenDailyPriceReqDto openDailyPriceReqDto = new OpenDailyPriceReqDto();
                openDailyPriceReqDto.setHotelId(bookingOrder.getHid());
                openDailyPriceReqDto.setRoomTypeIds(roomTypeIds);
                this.otaFeign.productDailyPricePush(openDailyPriceReqDto);
                logger.info("ota order:{} and num change  push end",bookingOrder.getBookingOrderId());
            } catch (Exception e) {
                logger.info("ota order:{} and num change push exception {}",bookingOrder.getBookingOrderId(),e);
            }
        });
    }

    public void pushNumChange(Integer hid, List<Integer> roomTypeIdList) {
        ThreadPool threadPool = ThreadPoolFactory.getInstance().getThreadPool(ThreadTypeEnum.EXECUTE_OTA_REQ_SEND_TASK);
        threadPool.monitor(ThreadTypeEnum.EXECUTE_OTA_REQ_SEND_TASK.getMessage());
        threadPool.execute(() -> {
            logger.info("ota hotel{},roomType{} num change push begin： bookingOrder={}",hid, roomTypeIdList);
            try {
                //推送OTA房型价态量信息
                if (CollectionUtils.isEmpty(roomTypeIdList)) {
                    logger.error("价量态变更推送状态为，房型数量为空");
                }
                OpenDailyPriceReqDto openDailyPriceReqDto = new OpenDailyPriceReqDto();
                openDailyPriceReqDto.setHotelId(hid);
                openDailyPriceReqDto.setRoomTypeIds(roomTypeIdList);
                this.otaFeign.productDailyPricePush(openDailyPriceReqDto);
                logger.info("ota hotel{},roomType{} num change push begin",hid, roomTypeIdList);
            } catch (Exception e) {
                logger.info("ota hotel{},roomType{} num change push exception {}",hid, roomTypeIdList,e);
            }
        });
    }



}
