package com.pms.czabsorders.service.order;

import com.pms.czabsorders.bean.AddBookDTO;
import com.pms.czabsorders.bean.BookMsgForOtherPlatformDTO;
import com.pms.czabsorders.bean.CancelBookForOtherPlatPlatformDTO;
import com.pms.czpmsutils.ResponseData;
import com.pms.pmsorder.bean.request.BookingOrderPageRequest;

public interface ItOrderService {

    public ResponseData addBookForOtherPlatPlatform( AddBookDTO addBookRequest);

    public ResponseData cancelBookForOtherPlatPlatform( CancelBookForOtherPlatPlatformDTO param);

    public ResponseData searchBookMsgForOtherPlatform(BookMsgForOtherPlatformDTO bookMsgForOtherPlatformDTO);

    public ResponseData searchBooking(BookingOrderPageRequest bookingOrderPageRequest);


}
