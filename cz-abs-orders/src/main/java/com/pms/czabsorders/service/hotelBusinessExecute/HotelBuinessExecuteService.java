package com.pms.czabsorders.service.hotelBusinessExecute;

import com.pms.czaccount.bean.account.Account;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomType;
import com.pms.czhotelfoundation.bean.setting.HotelInitialData;
import com.pms.czmembership.bean.company.HotelCompanyAccount;
import com.pms.czmembership.bean.company.HotelCompanyInfo;
import com.pms.czmembership.bean.member.CardInfo;
import com.pms.czmembership.bean.member.CardLevel;
import com.pms.czmembership.bean.member.CardType;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.HotelDataClearRequest;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;

import java.util.List;

public interface HotelBuinessExecuteService {
    public ResponseData hotelDataClear(HotelDataClearRequest hotelDataClearRequest);

    public void executeHotelDataClearBusiness(List<HotelInitialData> hotelInitialDataList, List<RoomType> roomTypeList, List<RoomInfo> roomInfoList, List<Account> accountList, List<Regist> registList, List<RegistPerson> registPersonList, List<HotelCompanyInfo> hotelCompanyInfoList, List<HotelCompanyAccount> hotelCompanyAccountList, List<CardType> cardTypeList, List<CardLevel> cardLevelList, List<CardInfo> cardInfoList, List<BookingOrder> bookingOrderList) throws Exception;
}
