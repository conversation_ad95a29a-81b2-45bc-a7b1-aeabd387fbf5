package com.pms.czabsorders.service.mini;

import com.pms.czabsorders.bean.mini.WxShoppingCardRequest;
import com.pms.czabsorders.bean.mini.WxShoppingSearch;
import com.pms.czmembership.bean.member.search.CardRechargeSearch;
import com.pms.czpmsutils.ResponseData;
import net.sf.json.JSONObject;

public interface WxMiniprogramsService {

    // 获取微信小程序
    public ResponseData jscode2session(JSONObject param);


    /**
     * 获取微信统一下单支付接口
     * @param param
     * @return
     */
    public ResponseData getWxUnifiedorder(JSONObject param);

    /**
     * 获取微信订单状态
     * @param param
     * @return
     */
    public ResponseData getWeChatPayResult(JSONObject param);


    /**
     * 查询购物车
     * @param wxShoppingSearch
     * @return
     */
    public ResponseData searchWxShoppingCar(WxShoppingSearch wxShoppingSearch);

    /**
     * 添加购物车
     * @param wxShoppingCardRequest
     * @return
     */
    public ResponseData addWxShoppingCar(WxShoppingCardRequest wxShoppingCardRequest);

    /**
     * 添加购物车
     * @param wxShoppingCardRequest
     * @return
     */
    public ResponseData addWxShoppingCarOther(WxShoppingCardRequest wxShoppingCardRequest);

    /**
     *  会员储值成功
     * @param cardRechargeSearch
     * @return
     */
    public ResponseData paySuccRecharge(CardRechargeSearch cardRechargeSearch);

    public ResponseData copyVipLevel(CardRechargeSearch cardRechargeSearch);

    public ResponseData copyVipInfo(CardRechargeSearch cardRechargeSearch);

}
