package com.pms.czabsorders.service.machine.impl;


import com.github.pagehelper.Page;
import com.pms.czabsorders.service.machine.MachineService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.HttpRequest;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ECache;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.hotelsetting.HOTEL_SETTING;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.machine.*;
import com.pms.pmsorder.bean.machine.model.AvailRoomRequest;
import com.pms.pmsorder.bean.machine.model.ExecuteAssignRoomRequest;
import com.pms.pmsorder.bean.machine.model.HotelOrderListRequest;
import com.pms.pmsorder.bean.machine.search.MachineMainSearch;
import com.pms.pmsorder.bean.machine.search.MachineParamTypeSearch;
import com.pms.pmsorder.bean.machine.search.MachineSettingSearch;
import com.pms.pmsorder.bean.machine.search.MachineUpdateRecordSearch;
import com.pms.pmsorder.dao.MachineMainDao;
import com.pms.pmsorder.dao.MachineParamTypeDao;
import com.pms.pmsorder.dao.MachineSettingDao;
import com.pms.pmsorder.dao.MachineUpdateRecordDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLDecoder;
import java.util.*;

/**
 * 自助机相关的业务
 */
@Service
@Primary
@Slf4j
public class MachineServiceImpl extends BaseService implements MachineService {

    @Autowired
    private MachineMainDao machineMainDao;


    @Autowired
    private MachineUpdateRecordDao machineUpdateRecordDao;


    @Autowired
    private MachineSettingDao machineSettingDao;

    @Autowired
    private MachineParamTypeDao machineParamTypeDao;

    private BaseService baseService = this;


    /**
     * 查询酒店下所有自助机
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData findAllMachineByHid(MachineMainSearch param) {

        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            String sessionToken = param.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);


            List<MachineMain> machineMains = machineMainDao.selectBySearch(param);

            responseData.setData(machineMains);

            return responseData;

        } catch (Exception e) {
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("业务处理异常",e);
        }

        return responseData;
    }

    @Override
    public ResponseData hotelMachineExistence(MachineMainSearch param) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            String sessionToken = param.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            List<MachineMain> machineMains = machineMainDao.selectBySearch(param);

            if(machineMains!=null &&machineMains.size()>0){
                responseData.setData(true);
            }else {
                responseData.setData(false);
            }

            return responseData;

        } catch (Exception e) {
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("业务处理异常",e);
        }

        return responseData;
    }

    /**
     * 查询自助机日志
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData findRecordByMachineUUID(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            String uuid = param.getString("uuid");
            MachineUpdateRecordSearch machineUpdateRecordSearch = new MachineUpdateRecordSearch();
            machineUpdateRecordSearch.setMachineUuid(uuid);
            Page<MachineUpdateRecord> machineUpdateRecords = machineUpdateRecordDao.selectBySearch(machineUpdateRecordSearch);
            responseData.setData(machineUpdateRecords);
        } catch (Exception e) {
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }


    /**
     * 查询所有自助机设置
     *
     * @param param machineId
     * @return
     */

    @Override
    public ResponseData findAllMachineSetting(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            String uuid = param.getString("uuid");
            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            Object settingList = userCahe.get(ECache.MACHINE_SETTING_LIST, uuid);
            Object settingJson = userCahe.get(ECache.MACHINE_SETTING_JSON, uuid);
            /**
             * 查询缓存信息失败则更新缓存信息
             */
            boolean b = settingList == null || settingJson == null || "[]".equals(settingList.toString()) || "{}".equals(settingJson.toString());
            JSONObject resultMap = new JSONObject();
            JSONObject pat = updateMachineSettingToCache(param);
            resultMap.putAll(pat);
            responseData.setData(resultMap);
        } catch (Exception e) {
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }

    @Override
    public ResponseData findMachineByMac(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Object mac = param.get("mac");
            if (mac == null || mac.toString().equals("")){
               throw new Exception("MAC不能空");
            }
            MachineMainSearch machineMainSearch = new MachineMainSearch();
            machineMainSearch.setMacUuid(mac.toString());
            Page<MachineMain> machineMains = machineMainDao.selectBySearch(machineMainSearch);
            responseData.setData(machineMains);
        } catch (Exception e) {
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }

    /**
     * 修改自助机设置
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData updateMachineSetting(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            String uuid = param.getString("uuid");
            int machineId = param.getInt("machineId");

            //查询自助机详细信息
            MachineMain machineMain = machineMainDao.selectById(machineId);

            if (machineMain == null) {
                throw new Exception("查询自助机信息失败");
            }

            //要修改的id
            int paramId = param.getInt(HOTEL_SETTING.PARAM_ID);
            //要修改的值
            String paramValue = URLDecoder.decode(param.getString(HOTEL_SETTING.PARAM_VALUE), "utf-8");

            StringBuffer sb = new StringBuffer();
            /**
             * 1.查询自助机设置
             */
            List<MachineSetting> machineSettings = machineSettingDao.searchSettingByMachineId(param);

            if (machineSettings == null || machineSettings.size() < 1) {
                throw new Exception(HOTEL_SETTING.ERR_PARAM_NOT_FOUND);
            }

            MachineSetting machineSetting = machineSettings.get(0);

            sb.append(machineSetting.getParamName());
            sb.append("由");
            sb.append(machineSetting.getParamValue());
            sb.append("   修改为:  ");
            sb.append(paramValue);

            //1代表是系统公用码，需要新增
            int updateValue = 0;
            if (machineSetting.getSystemParam().equals(1)) {
                machineSetting.setId(null);
                machineSetting.setParamValue(paramValue);
                machineSetting.setHid(user.getHid());
                machineSetting.setHotelGroupId(user.getHotelGroupId());
                machineSetting.setMachineId(machineId);
                machineSetting.setMachineUuid(uuid);
                machineSetting.setUpdateTime(new Date());
                machineSetting.setUpdateUserId(user.getUserId());
                machineSetting.setCreateTime(new Date());
                machineSetting.setCreateUserId(user.getUserId());
                machineSetting.setSystemParam(0);
                updateValue = machineSettingDao.insert(machineSetting);
            } else {
                machineSetting.setParamValue(paramValue);
                machineSetting.setUpdateTime(new Date());
                machineSetting.setUpdateUserId(user.getUserId());
                updateValue = machineSettingDao.update(machineSetting);
            }

            if (updateValue < 1) {
                throw new Exception(HOTEL_SETTING.ERR_PARAM_UPDATE_FAILD);
            }

            /**
             * 2.添加日志
             */
            MachineUpdateRecord machineUpdateRecord = new MachineUpdateRecord();
            machineUpdateRecord.setHid(user.getHid());
            machineUpdateRecord.setMachineExpireData(machineMain.getExpireData());
            machineUpdateRecord.setMachineUuid(uuid);
            machineUpdateRecord.setUpdateTime(new Date());
            machineUpdateRecord.setUserName(user.getUserName());
            machineUpdateRecord.setMemo(sb.toString());
            machineUpdateRecordDao.insert(machineUpdateRecord);
            JSONObject jsonObject1 = updateMachineSettingToCache(param);

            JSONObject jsonObject = new JSONObject();
            jsonObject.putAll(jsonObject1);
            StringBuffer push = new StringBuffer();
            push.append("hid=");
            push.append(machineMain.getHid());
            push.append("&type=4");
            push.append("&termType=machine");
            HotelUtils.pushForHid(push.toString());

            baseService.push(user.getHotelGroupId(), user.getHid(), 29, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
            responseData.setData(jsonObject);
        } catch (Exception e) {
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }

    /**
     * 根据machineId修改设置信息
     * 唯一值是UUID
     *
     * @param param
     */
    public JSONObject updateMachineSettingToCache(JSONObject param) {

        final String uuid = param.getString("uuid");

        /**
         * 根据UUID查询自助机
         */
        MachineMainSearch machineMainSearch = new MachineMainSearch();
        machineMainSearch.setUuid(uuid);
        List<MachineMain> machineMains = machineMainDao.selectBySearch(machineMainSearch);
        MachineMain machineMain = machineMains.get(0);
        param.put("machineId",machineMain.getId());
        param.put("paramId", null);
        /**
         * 1.查询自助机设置信息
         *      区分父类和子类
         */
        List<MachineSetting> machineSettings = machineSettingDao.searchSettingByMachineId(param);

        ArrayList<MachineSetting> parentList = new ArrayList<>();

        ArrayList<MachineSetting> childList = new ArrayList<>();

        for (MachineSetting ms : machineSettings) {

            Integer parentId = ms.getParentId();

            if (parentId.equals(0)) {
                parentList.add(ms);
            } else {
                childList.add(ms);
            }

        }

        /**
         * 2.对父类和子类进行组合
         */
        //已JSON类型 存储子类
        final JSONObject setMap = new JSONObject();
        //中转的子类
        ArrayList<MachineSetting> midChildList = new ArrayList<>();
        midChildList.addAll(childList);
        //组合后的父类
        final ArrayList<MachineSetting> newParentList = new ArrayList<>();

        for (MachineSetting msp : parentList) {

            childList.clear();
            childList.addAll(midChildList);
            midChildList.clear();

            ArrayList<MachineSetting> cd = msp.getChildList();

            Integer id = msp.getId();

            for (MachineSetting msc : childList) {

                Integer parentId = msc.getParentId();

                /**
                 * 判断  父类id是否匹配
                 *  匹配添加到父类集合中
                 *
                 */
                if (parentId.equals(id)) {
                    int pv = Integer.parseInt(msp.getParamValue());

                    if (pv > 0 && pv != machineMain.getPmsType()) {
                        continue;
                    }
                    cd.add(msc);
                    setMap.put(msc.getParamIdStr(), msc);

                } else {

                    midChildList.add(msc);

                }

            }

            msp.setChildList(cd);

            newParentList.add(msp);

        }

        /**
         * 3.更新缓存信息
         */

        HotelUtils.cachedThreadPool.execute(new Runnable() {
            @Override
            public void run() {

                try {
                    final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                    userCahe.put(ECache.MACHINE_SETTING_JSON, uuid, JSONObject.fromObject(setMap).toString());
                    userCahe.put(ECache.MACHINE_SETTING_LIST, uuid, JSONArray.fromObject(newParentList).toString());

                } catch (Exception e) {
                    log.error("",e);
                }

            }
        });

        JSONObject jsonObject = new JSONObject();

        jsonObject.put("data", newParentList);

        jsonObject.put("json", setMap);

        return jsonObject;

    }

    /**
     * 获取所有自助机参数
     *
     * @param machineParamTypeSearch
     * @return
     */
    @Override
    public ResponseData getMachineParam(MachineParamTypeSearch machineParamTypeSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = machineParamTypeSearch.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            machineParamTypeSearch.setHid(user.getHid());
            Page<MachineParamType> machineParamTypes = machineParamTypeDao.selectBySearch(machineParamTypeSearch);
            responseData.setData(machineParamTypes);
        } catch (Exception e) {
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }


    /**
     * 根据mac地址获取自助机信息
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> findMachineByMacId(JSONObject param) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ER.RES, ER.SUCC);
        jsonObject.put("code",1);

        try {

            String mac = param.getString("mac").trim();

            MachineMainSearch machineMainSearch = new MachineMainSearch();
            machineMainSearch.setMacUuid(mac);
            machineMainSearch.setState(1);

            List<MachineMain> machineMains = machineMainDao.selectBySearch(machineMainSearch);

            //是否有自助机信息
            Boolean haveMachineMsg = false;

            if (machineMains != null && machineMains.size() > 0) {
                haveMachineMsg = true;
            }

            //如果mac地址未查到自助机信息，则查询备用两个mac地址
            if (!haveMachineMsg) {
                machineMainSearch.setMacUuid(null);
                machineMainSearch.setMacUuidTwo(mac);
                machineMains = machineMainDao.selectBySearch(machineMainSearch);
            }

            if (machineMains != null && machineMains.size() > 0) {
                haveMachineMsg = true;
            }

            if (!haveMachineMsg) {
                machineMainSearch.setMacUuid(null);
                machineMainSearch.setMacUuidTwo(null);
                machineMainSearch.setMacUuidThree(mac);
                machineMains = machineMainDao.selectBySearch(machineMainSearch);

            }

            if (machineMains == null || machineMains.size() < 1) {
                throw new Exception("未查到相应的自助机信息");
            }

            MachineMain machineMain = machineMains.get(0);

            /**
             * 根据自助机获取sessionToken
             */
            TbUserSession tbUserSession = this.getTbUserSession(machineMain.getUuid());
            machineMain.setSessionToken(tbUserSession.getSessionId());

            jsonObject.putAll(JSONObject.fromObject(machineMain));

            return jsonObject;

        } catch (Exception e) {
            jsonObject.put(ER.RES, ER.ERR);
            jsonObject.put(ER.MSG, e.getMessage());
            jsonObject.put("code",-1);
            log.error("",e);

        }

        return jsonObject;
    }

    /**
     * 复制自助机配置
     *
     * @param param
     * @return
     */
    @Override
    @Transactional
    public Map<String, Object> copyMachineSetting(final JSONObject param) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (!user.getSessionType().equals(2)) {
                throw new Exception("非管理员不能复制自助机");
            }

            /**
             * 1.获取参数
             */
            //源自助机id
            int copyMachineId = param.getInt("copyMachineId");
            //目标自助机id
            int sourceMachineId = param.getInt("sourceMachineId");

            /**
             * 2.查询自助机
             */
            MachineMain copyMachine = machineMainDao.selectById(copyMachineId);
            if (copyMachine == null) {
                throw new Exception("选择的数据源自助机id不正确");
            }

            MachineMain sourceMachine = machineMainDao.selectById(sourceMachineId);
            if (sourceMachine == null) {
                throw new Exception("目标自助机id不正确");
            }

            /**
             * 3.查询数据源自助机设置
             */
            MachineSettingSearch copyMachineSettingSearch = new MachineSettingSearch();
            copyMachineSettingSearch.setMachineId(copyMachineId);
            List<MachineSetting> copyMachineSettings = machineSettingDao.selectBySearch(copyMachineSettingSearch);

            if (copyMachineSettings.size() < 1) {
                throw new Exception("选择的自助机中没有配置信息");
            }

            /**
             * 4.查询要复制的自助机设置数据
             *     并做删除处理
             */
            MachineSettingSearch machineSettingSearch = new MachineSettingSearch();
            machineSettingSearch.setMachineId(sourceMachineId);
            List<MachineSetting> machineSettings = machineSettingDao.selectBySearch(machineSettingSearch);

            for (MachineSetting machineSet : machineSettings) {
                machineSettingDao.delete(machineSet.getId());
            }

            /**
             * 5.把 copyMachine 的自助机设置 复制到 sourceMachine 中
             */
            Date date = new Date();
            for (MachineSetting ms : copyMachineSettings) {

                ms.setId(null);
                ms.setMachineId(sourceMachine.getId());
                ms.setMachineUuid(sourceMachine.getUuid());
                ms.setCreateTime(date);
                ms.setCreateUserId(user.getUserId());
                ms.setUpdateUserId(user.getUserId());
                ms.setUpdateTime(date);

                machineSettingDao.insert(ms);
            }

            /**
             * 6.更新缓存中的自助机设置信息
             */
            param.put("uuid", sourceMachine.getUuid());


            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    updateMachineSettingToCache(param);

                }
            });


            return jsonObject;

        } catch (Exception e) {
            jsonObject.put(ER.RES, ER.ERR);
            jsonObject.put(ER.MSG, e.getMessage());
            jsonObject.put("code",-1);
            log.error("",e);

        }

        return jsonObject;
    }

    /**
     * 根据hid获取设置信息
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData findMachineSettingByHid(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            MachineMainSearch machineMainSearch = new MachineMainSearch();
            machineMainSearch.setHid(user.getHid());

            /**
             * 1.查询酒店下自助机
             */
            List<MachineMain> machineMains = machineMainDao.selectBySearch(machineMainSearch);

            if (machineMains.size() < 1) {
                throw new Exception("未查询到自助机信息");
            }
            MachineMain machineMain = machineMains.get(0);
            /**
             * 2.查询自助机设置
             */
            JSONObject machineParam = new JSONObject();
            machineParam.put("machineId", machineMain.getId());
            machineParam.put("parentId", param.get("parentId"));
            List<MachineSetting> machineSettings = machineSettingDao.searchSettingByMachineId(machineParam);
            JSONObject machineSetting = new JSONObject();
            for (MachineSetting ms : machineSettings) {
                machineSetting.put(ms.getParamIdStr(), ms.getParamValue());
            }
            responseData.setData(machineSetting);
        } catch (Exception e) {
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }

    public JSONObject addPmsPostPublicParam(JSONObject paramInfo) {
        JSONObject postData = new JSONObject();
        postData.put("GroupUrl", paramInfo.containsKey("Pms_GroupUrl") ? paramInfo.getJSONObject("Pms_GroupUrl").getString("paramValue") : "");
        postData.put("Url", paramInfo.containsKey("Pms_Url") ? paramInfo.getJSONObject("Pms_Url").getString("paramValue") : "");
        postData.put("HotelGroupCode", paramInfo.containsKey("Pms_HotelGroupCode") ? paramInfo.getJSONObject("Pms_HotelGroupCode").getString("paramValue") : "");
        postData.put("HotelCode", paramInfo.containsKey("Pms_HotelCode") ? paramInfo.getJSONObject("Pms_HotelCode").getString("paramValue") : "");
        postData.put("appKey", paramInfo.containsKey("Pms_AppKey") ? paramInfo.getJSONObject("Pms_AppKey").getString("paramValue") : "");
        postData.put("appSecret", paramInfo.containsKey("Pms_AppSecret") ? paramInfo.getJSONObject("Pms_AppSecret").getString("paramValue") : "");
        postData.put("username", paramInfo.containsKey("Pms_Username") ? paramInfo.getJSONObject("Pms_Username").getString("paramValue") : "");
        postData.put("password", paramInfo.containsKey("Pms_Password") ? paramInfo.getJSONObject("Pms_Password").getString("paramValue") : "");
        postData.put("PmsMarket", paramInfo.containsKey("Pms_Market") ? paramInfo.getJSONObject("Pms_Market").getString("paramValue") : "");
        postData.put("PmsNameSpace", paramInfo.containsKey("Pms_NameSpace") ? paramInfo.getJSONObject("Pms_NameSpace").getString("paramValue") : "");
        postData.put("PmsResType", paramInfo.containsKey("Pms_ResType") ? paramInfo.getJSONObject("Pms_ResType").getString("paramValue") : "");
        postData.put("PmsSource", paramInfo.containsKey("Pms_Source") ? paramInfo.getJSONObject("Pms_Source").getString("paramValue") : "");
        postData.put("PmsWechatPayCode", paramInfo.containsKey("Pms_WechatPayCode") ? paramInfo.getJSONObject("Pms_WechatPayCode").getString("paramValue") : "");
        postData.put("PmsAlipayCode", paramInfo.containsKey("Pms_AlipayCode") ? paramInfo.getJSONObject("Pms_AlipayCode").getString("paramValue") : "");
        postData.put("PmsChannel", paramInfo.containsKey("Pms_Channel") ? paramInfo.getJSONObject("Pms_Channel").getString("paramValue") : "");
        postData.put("PmsHourMarket", paramInfo.containsKey("Pms_Hour_Market") ? paramInfo.getJSONObject("Pms_Hour_Market").getString("paramValue") : "");
        postData.put("OpenAutoCheckOut", paramInfo.containsKey("Pms_OpenAutoCheckOut") ? paramInfo.getJSONObject("Pms_OpenAutoCheckOut").getString("paramValue") : "");
        return postData;
    }

    @Override
    public ResponseData findHotelOrderList(HotelOrderListRequest hotelOrderListRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = hotelOrderListRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            MachineMainSearch machineMainSearch = new MachineMainSearch();
            machineMainSearch.setHid(user.getHid());
            List<MachineMain> machineMains = machineMainDao.selectBySearch(machineMainSearch);
            if (machineMains == null || machineMains.size() < 1) {
                throw new Exception("未查询到酒店自助机");
            }
            MachineMain machineMain = machineMains.get(0);
            JSONObject postData = new JSONObject();
            postData.put("uuid", machineMain.getUuid());
            postData.put("sessionToken", sessionToken);
            ResponseData allMachineSetting = findAllMachineSetting(postData);
            if (allMachineSetting.getCode() != 1) {
                throw new Exception("查询自助机参数配置失败");
            }
            JSONObject pmsData = JSONObject.fromObject(allMachineSetting.getData());
            JSONObject pmsInfo = pmsData.getJSONObject("json");
            String url = pmsInfo.getJSONObject("HotelParam_SourceIP").getString("paramValue");
            JSONObject requestData = this.addPmsPostPublicParam(pmsInfo);
            requestData.put("beginTime", hotelOrderListRequest.getBeginTime());
            requestData.put("endTime", hotelOrderListRequest.getEndTime());
            requestData.put("orderType", hotelOrderListRequest.getOrderType());
            requestData.put("searchValue", hotelOrderListRequest.getSearchValue());
            requestData.put("ScanCode", hotelOrderListRequest.getSearchValue());
            log.info("requestData={}",requestData.toString());
            String s = HttpRequest.sendPost(url + "/ElinesoftApi/api/Get_Hotel_Order_List.do", requestData.toString());
            JSONObject resultMap = JSONObject.fromObject(s);
            if (!resultMap.getString("Result").equals("True")) {
                throw new Exception("查询失败");
            }
            responseData.setData(resultMap);
        } catch (Exception e) {
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }

    @Override
    public ResponseData getAvailRoomList(AvailRoomRequest availRoomRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = availRoomRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            MachineMainSearch machineMainSearch = new MachineMainSearch();
            machineMainSearch.setHid(user.getHid());
            List<MachineMain> machineMains = machineMainDao.selectBySearch(machineMainSearch);
            if (machineMains == null || machineMains.size() < 1) {
                throw new Exception("未查询到酒店自助机");
            }
            MachineMain machineMain = machineMains.get(0);
            JSONObject postData = new JSONObject();
            postData.put("uuid", machineMain.getUuid());
            postData.put("sessionToken", sessionToken);
            ResponseData allMachineSetting = findAllMachineSetting(postData);
            if (allMachineSetting.getCode() != 1) {
                throw new Exception("查询自助机参数配置失败");
            }
            JSONObject pmsData = JSONObject.fromObject(allMachineSetting.getData());
            JSONObject pmsInfo = pmsData.getJSONObject("json");
            String url = pmsInfo.getJSONObject("HotelParam_SourceIP").getString("paramValue");
            JSONObject requestData = this.addPmsPostPublicParam(pmsInfo);
            requestData.put("BeginTime", availRoomRequest.getBeginTime());
            requestData.put("EndTime", availRoomRequest.getEndTime());
            requestData.put("RoomTypeID", availRoomRequest.getRoomTypeId());
            requestData.put("CheckInType", availRoomRequest.getCheckInType());
            requestData.put("PriceCode", availRoomRequest.getPriceCode());
            log.info("requestData={}",requestData.toString());
            String s = HttpRequest.sendPost(url + "/ElinesoftApi/api/Get_Avail_Room_List.do", requestData.toString());
            JSONObject resultMap = JSONObject.fromObject(s);
            if (!resultMap.getString("Result").equals("True")) {
                throw new Exception("查询失败");
            }
            responseData.setData(resultMap);
        } catch (Exception e) {
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }

    @Override
    public ResponseData availRoom(ExecuteAssignRoomRequest executeAssignRoomRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = executeAssignRoomRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            MachineMainSearch machineMainSearch = new MachineMainSearch();
            machineMainSearch.setHid(user.getHid());
            List<MachineMain> machineMains = machineMainDao.selectBySearch(machineMainSearch);
            if (machineMains == null || machineMains.size() < 1) {
                throw new Exception("未查询到酒店自助机");
            }
            MachineMain machineMain = machineMains.get(0);
            JSONObject postData = new JSONObject();
            postData.put("uuid", machineMain.getUuid());
            postData.put("sessionToken", sessionToken);
            ResponseData allMachineSetting = findAllMachineSetting(postData);
            if (allMachineSetting.getCode() != 1) {
                throw new Exception("查询自助机参数配置失败");
            }
            JSONObject pmsData = JSONObject.fromObject(allMachineSetting.getData());
            JSONObject pmsInfo = pmsData.getJSONObject("json");
            String url = pmsInfo.getJSONObject("HotelParam_SourceIP").getString("paramValue");
            JSONObject requestData = this.addPmsPostPublicParam(pmsInfo);
            requestData.put("OldRoomNo", executeAssignRoomRequest.getOldRoomNo());
            requestData.put("RoomNo", executeAssignRoomRequest.getRoomNo());
            requestData.put("RoomId", executeAssignRoomRequest.getRoomId());
            requestData.put("Reserved", executeAssignRoomRequest.getReserved());
            requestData.put("PriceCode", executeAssignRoomRequest.getPriceCode());
            requestData.put("OrderID", executeAssignRoomRequest.getOrderID());
            requestData.put("AssignType", executeAssignRoomRequest.getAssignType());
            log.info("requestData={}",requestData.toString());
            String s = HttpRequest.sendPost(url + "/ElinesoftApi/api/Assign_Room.do", requestData.toString());
            JSONObject resultMap = JSONObject.fromObject(s);
            if (!resultMap.getString("Result").equals("True")) {
                throw new Exception("执行失败");
            }
            responseData.setData(resultMap);
        } catch (Exception e) {
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }

    @Override
    public ResponseData machineMakeRoomCard(MakeRoomCardParam makeRoomCardParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = makeRoomCardParam.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            MachineMainSearch machineMainSearch = new MachineMainSearch();
            machineMainSearch.setHid(user.getHid());
            machineMainSearch.setState(1);
            Page<MachineMain> machineMains = machineMainDao.selectBySearch(machineMainSearch);


            HashMap<String, String> filedMap = new HashMap<>();
            baseService.push(user.getHotelGroupId(), user.getHid(), 24, filedMap, new HashMap<String, String>(), true, true);
        }
        catch (Exception e){
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }
}
