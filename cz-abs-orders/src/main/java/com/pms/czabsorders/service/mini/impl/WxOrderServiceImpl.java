package com.pms.czabsorders.service.mini.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.mini.VipOrderPayRequest;
import com.pms.czabsorders.service.mini.WxOrderService;
import com.pms.czabsorders.service.mini.transaction.WxServiceTransaction;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.code.HotelBusinessDay;
import com.pms.czhotelfoundation.bean.code.search.HotelBusinessDaySearch;
import com.pms.czhotelfoundation.bean.hotel.HotelBaseInfo;
import com.pms.czhotelfoundation.bean.hotel.HotelHourRoomType;
import com.pms.czhotelfoundation.bean.hotel.search.HotelBaseInfoSearch;
import com.pms.czhotelfoundation.bean.hotel.search.HotelHourRoomTypeSearch;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliary;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomRepairRecord;
import com.pms.czhotelfoundation.bean.room.RoomType;
import com.pms.czhotelfoundation.bean.room.search.RoomRepairRecordSearch;
import com.pms.czhotelfoundation.bean.search.HourRoomDayUseSearch;
import com.pms.czhotelfoundation.dao.HourRoomDayUseDao;
import com.pms.czhotelfoundation.dao.code.HotelBusinessDayDao;
import com.pms.czhotelfoundation.dao.hotel.HotelBaseInfoDao;
import com.pms.czhotelfoundation.dao.hotel.HotelHourRoomTypeDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomRepairRecordDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czmembership.bean.member.CardGroupInfo;
import com.pms.czmembership.bean.member.CardInfo;
import com.pms.czmembership.bean.member.search.CardGroupInfoSearch;
import com.pms.czmembership.bean.member.search.CardInfoSearch;
import com.pms.czmembership.dao.member.CardGroupInfoDao;
import com.pms.czmembership.dao.member.CardInfoDao;
import com.pms.czmembership.service.memeber.MemberService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.JobName;
import com.pms.czpmsutils.OrderNumUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ECache;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.room.ROOM_AUXILIARY;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.*;
import com.pms.czpmsutils.view.SearchOrderInfoView;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.search.BookingOrderDailyPriceSearch;
import com.pms.pmsorder.bean.search.BookingOrderRoomNumSearch;
import com.pms.pmsorder.bean.search.RegistPersonSearch;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.bean.view.BookingOrderRoomNumForDayView;
import com.pms.pmsorder.dao.*;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;

import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class WxOrderServiceImpl extends BaseService implements WxOrderService {

    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * 登记信息表
     */
    @Autowired
    private RegistDao registDao;

    /**
     * 预定分房表
     */
    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    /**
     * 维修房
     */
    @Autowired
    private RoomRepairRecordDao roomRepairRecordDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    private BaseService baseService = this;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private HotelBusinessDayDao hotelBusinessDayDao;

    /**
     * 在住人
     */
    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    // 房型
    @Autowired
    private RoomTypeDao roomTypeDao;

    @Autowired
    private RoomAuxiliaryDao roomAuxiliaryDao;

    @Autowired
    private WxServiceTransaction wxServiceTransaction;

    @Autowired
    private HotelBaseInfoDao hotelBaseInfoDao;


    @Autowired
    private MemberService memberService;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private CardGroupInfoDao cardGroupInfoDao;

    @Autowired
    private CardInfoDao cardInfoDao;

    @Autowired
    private HourRoomDayUseDao hourRoomDayUseDao;

    @Autowired
    private HotelHourRoomTypeDao hotelHourRoomTypeDao;

    @Override
    public ResponseData addBook(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Object hid = param.get("hid");
            if (hid == null) {
                throw new Exception("酒店信息不能为空");
            }

            user.setHid(param.getInt("hid"));
            HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
            hotelBusinessDaySearch.setHid(user.getHid());
            HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);
            user.setBusinessDay(hotelBusinessDay.getBusinessDay());


            // 预订单参数
            String bookData = URLDecoder.decode(param.getString("bookData"), "utf-8");
            JSONObject bookJson = JSONObject.fromObject(bookData);

            //生成预订单编号
            String sn = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.BOOK, this.stringRedisTemplate);

            Date date = new Date();

            BookingOrder book = new BookingOrder();
            book.setSn(sn);
            book.setHotelGroupId(user.getHotelGroupId());
            book.setHid(user.getHid());
            book.setCreateTime(date);
            book.setCreateUserId(user.getUserId());
            book.setCreateUserName(user.getUserName());
            book.setAcceptTime(date);
            book.setRoomCount(bookJson.getInt("roomCount"));
            book.setOrderTime(date);
            book.setBookingPhone(bookJson.getString("bookingPhone"));
            book.setBookingName(bookJson.getString("bookingName"));
            if (bookJson.get("bookIdCode") != null) {
                book.setBookingIdCode(bookJson.getString("bookIdCode"));
            }

            book.setCheckinTime(HotelUtils.parseStr2Date(bookJson.getString("checkinTime") + " 00:00:00"));
            book.setCheckoutTime(HotelUtils.parseStr2Date(bookJson.getString("checkoutTime") + " 00:00:00"));
            book.setKeepTime(bookJson.getString("keepTime"));
            //入住天数
            int dayCount = HotelUtils.getAllDayListBetweenDate(bookJson.getString("checkinTime"), bookJson.getString("checkoutTime")).size();

            book.setDayCount(dayCount);
            book.setFromHid(user.getHid());
            if (bookJson.get("otherOrderId") != null) {
                book.setThirdPlatformOrderCode(bookJson.getString("otherOrderId"));
            }


            //客源类型
            int resourceId = bookJson.getInt("resourceId");
            book.setResourceId(resourceId);

            // 1.散客 2.会员
            if (resourceId == 2) {
                JSONObject memberInfo = bookJson.getJSONObject("vipMsg");
                book.setCardId(memberInfo.getInt("cardId"));
                book.setCardNo(memberInfo.getString("cardNo"));
            }
            if (resourceId == 3) {
                JSONObject arMsg = bookJson.getJSONObject("arMsg");

                book.setCompanyId(arMsg.getInt("arId"));
                book.setCompanyName(arMsg.getString("arName"));

                if (bookJson.get("arAntMsg") != null || !"".equals(bookJson.getString("arAntMsg"))) {
                    book.setCompanyAccountId(bookJson.getJSONObject("arAntMsg").getInt("id"));
                }

            }
            book.setFromType(25);
            if (param.get("fromType") != null && !param.getString("fromType").equals("")) {
                book.setFromType(param.getInt("fromType"));
            }

            /**
             *  缺失
             *      total_price 订单总金额
             *      unit_price  单价待补充
             *  待补充
             */
            Double payMoney = 0.0;
            Object payMoneyObj = bookJson.get("payMoney");
            if (payMoneyObj != null && !"".equals(payMoneyObj.toString())) {
                payMoney = bookJson.getDouble("payMoney") * 100;
            }
            book.setPayPrice(0);

            if (bookJson.get("sumMoney") != null) {
                Double d = bookJson.getDouble("sumMoney") * 100;
                book.setTotalPrice(d.intValue());
            }


            book.setFromHid(user.getHid());
            if (bookJson.get("fromHid") != null) {
                book.setFromHid(bookJson.getInt("fromHid"));
            }

            //取卡码
            if (bookJson.get("orderCode") != null) {
                book.setOrderCode(bookJson.getString("orderCode"));
            }
            //预订单类型 1.日租 2.钟点 3.长足
            book.setOrderType(1);
            Object orderType = bookJson.get("orderType");
            if (orderType != null) {
                book.setOrderType(Integer.parseInt(orderType.toString()));
            }
            //订单状态 订单状态 1.有效 2.NoShow 3.部分入住 4.全部入住 5.已取消 6.入住完成
            book.setOrderStatus(BOOK.STA_YX);
            Object orderStatus = bookJson.get("orderStatus");
            if (orderStatus != null) {
                book.setOrderStatus(Integer.parseInt(orderStatus.toString()));
            }
            if (bookJson.get("remark") != null) {
                book.setRemark(bookJson.getString("remark"));
            }

            // 订单时间
            book.setOrderYear(user.getBusinessYear());
            book.setOrderYearMonth(user.getBusinessMonth());
            book.setBusinessDay(user.getBusinessDay());
            book.setClassId(user.getClassId());
            book.setCreateTime(date);
            book.setCreateUserId(user.getSessionId());
            book.setCreateUserName(user.getUserName());
            book.setUpdateTime(date);
            book.setUpdateUserId(user.getSessionId());
            book.setUpdateUserName(user.getUserName());
            book.setRoomTypeSummary(bookJson.getString("roomTypeSummary"));

            /**
             * 查询预订单辅助房态
             *  如果当前时间等于 预抵时间，则状态为预抵。
             *      否则 辅助房态为预离。
             */
            RoomAuxiliary auxiliary = new RoomAuxiliary();
            String nowDate = HotelUtils.currentDate();
            if (nowDate.equals(bookJson.getString("checkinTime"))) {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_ARRIVALS);
            } else {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_CREATE);
            }

            // 团队房  isGroup==1则为团队
            RegistGroup registGroup = null;
            int isGroup = bookJson.getInt("isGroup");
            if (isGroup == 1) {
                registGroup = new RegistGroup();
                registGroup.setBusinessDay(user.getBusinessDay());
                registGroup.setHid(user.getHid());
                registGroup.setClassId(user.getClassId());
                registGroup.setCreateTime(new Date());
                registGroup.setCreateUserId(user.getUserId());
                registGroup.setCreateUserName(user.getUserName());
                registGroup.setSn(book.getSn());
                registGroup.setPayType(0);
                registGroup.setGroupName(bookJson.getString("groupName"));
                registGroup.setSumRooms(book.getRoomCount());
                registGroup.setUpdateTime(date);
                registGroup.setUpdateUserId(user.getUserId());
                registGroup.setUpdateUserName(user.getUserName());
                registGroup.setState(1);
            }


            /**
             * 添加操作日志
             */
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);
            oprecord.setMainId(sn);
            oprecord.setBookingOrderId(book.getBookingOrderId());
            oprecord.setDescription("创建预订单,订单号:" + sn);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecords.add(oprecord);

            /**
             * 2.添加预订单设置
             */
            BookingOrderConfig bookingOrderConfig = new BookingOrderConfig();
            bookingOrderConfig.setPriceSecrecy(bookJson.getInt("priceSecrecy"));
            bookingOrderConfig.setInfoSecrecy(bookJson.getInt("infoSecrecy"));
            bookingOrderConfig.setAutoCheckin(bookJson.getInt("autoCheckIn"));
            bookingOrderConfig.setNoDeposit(bookJson.getInt("noDposit"));
            bookingOrderConfig.setNoPrice(bookJson.getInt("noPrice"));
            bookingOrderConfig.setContinueRes(bookJson.getInt("continueRes"));
            bookingOrderConfig.setAutoAr(bookJson.getInt("autoAr"));
            /**
             *  取出两个时间的日期差
             */
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(bookJson.getString("checkinTime"), bookJson.getString("checkoutTime"));

            // 要添加的辅助房态
            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            /**
             * 3.添加预订房型信息
             */
            JSONArray roomTypeList = bookJson.getJSONArray("roomTypeList");

            BookingOrderDailyPrice bodp = new BookingOrderDailyPrice();

            // 需要添加的房型
            ArrayList<BookingOrderRoomType> addBort = new ArrayList<>();

            for (int i = 0; i < roomTypeList.size(); i++) {
                // 预订房型数据
                JSONObject rtObj = roomTypeList.getJSONObject(i);
                // 房间数据
                JSONArray roomList = rtObj.getJSONArray("roomList");
                // 创建预订房型信息
                BookingOrderRoomType bort = new BookingOrderRoomType();
                bort.setHid(user.getHid());
                bort.setHotelGroupId(user.getHotelGroupId());
                bort.setArriveTime(bookJson.getString("checkinTime") + " " + book.getKeepTime() + ":00");
                bort.setRoomTypeId(rtObj.getInt("roomTypeId"));
                bort.setRoomTypeNum(rtObj.getInt("num"));
                bort.setHasRoomNum(roomList.size());
                bort.setPriceCodeId(rtObj.getInt("rateId"));
                bort.setPriceCode(rtObj.getString("rateCode"));
                book.setRateCodeId(bort.getPriceCodeId());
                book.setRateCodeName(bort.getPriceCode());
                bort.setState(BOOK.STA_YX);
                bort.setOrderState(BOOK.STA_YX);
                bort.setCreateTime(date);
                bort.setCreateUserId(user.getUserId());
                bort.setCreateUserName(user.getUserName());
                bort.setUpdateTime(date);
                bort.setUpdateUserId(user.getUserId());
                bort.setUpdateUserName(user.getUserName());
                bort.setCheckinTime(book.getCheckinTime());
                bort.setCheckoutTime(book.getCheckoutTime());

                //增加预订房型操作日志
                Oprecord oprecord1 = new Oprecord(user);
                ;
                oprecord1.setMainId(sn);
                oprecord1.setRegistId(book.getBookingOrderId());
                oprecord1.setDescription("对订单 : " + sn + "，添加预订房型：" + rtObj.getString("roomTypeName") + ",房间数量: " + bort.getRoomTypeNum() + ",房价码:" + bort.getPriceCode());
                oprecord1.setOccurTime(HotelUtils.currentTime());
                oprecords.add(oprecord1);

                JSONArray priceList = rtObj.getJSONArray("priceList");

                // 房型添加的房价
                ArrayList<BookingOrderDailyPrice> rtPriceList = new ArrayList<>();

                for (int pl = 0; pl < priceList.size(); pl++) {
                    JSONObject plObj = priceList.getJSONObject(pl);
                    bodp = new BookingOrderDailyPrice();
                    bodp.setBookingOrderRoomNumId(0);
                    bodp.setHid(user.getHid());
                    bodp.setHotelGroupId(user.getHotelGroupId());
                    bodp.setPrice(plObj.getInt("price"));
                    bodp.setDailyTime(Integer.parseInt(plObj.getString("date")));
                    bodp.setRoomTypeId(bort.getRoomTypeId());
                    bodp.setRoomNumId(0);
                    bodp.setDailyState(1);
                    bodp.setIsStayover(0);
                    bodp.setCreateTime(date);
                    bodp.setCreateUserId(user.getUserId());
                    bodp.setCreateUserName(user.getUserName());
                    bodp.setUpdateTime(date);
                    bodp.setUpdateUserId(user.getUserId());
                    bodp.setUpdateUserName(user.getUserName());
                    rtPriceList.add(bodp);
                }

                bort.setBookingOrderDailyPrices(rtPriceList);

                // 需要添加的预订房间

                ArrayList<BookingOrderRoomNum> bookingOrderRoomNums = new ArrayList<>();

                for (int k = 0; k < roomList.size(); k++) {

                    JSONObject room = roomList.getJSONObject(k);

                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(book.getBookingOrderId());
                    bookingOrderRoomNum.setBookingOrderRoomTypeId(bort.getId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(bort.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(room.getInt("roomInfoId"));
                    bookingOrderRoomNum.setRoomNum(room.getString("roomNum"));
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setCheckinTime(book.getCheckinTime());
                    bookingOrderRoomNum.setCheckoutTime(book.getCheckoutTime());
                    bookingOrderRoomNum.setRowRoom(1);
                    bookingOrderRoomNum.setBookingOrderDailyPrices(rtPriceList);


                    //记录操作日志
                    Oprecord oprecord2 = new Oprecord(user);
                    oprecord2.setMainId(sn);
                    oprecord2.setRegistId(book.getBookingOrderId());
                    oprecord2.setDescription("对订单 : " + sn + "，进行排房：" + bookingOrderRoomNum.getRoomNum());
                    oprecord2.setOccurTime(HotelUtils.currentTime());
                    oprecords.add(oprecord2);

                    //添加辅助房态
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(bookingOrderRoomNum.getRoomNumId());
                    roomAuxiliaryRelation.setRoomNum(bookingOrderRoomNum.getRoomNum());
                    roomAuxiliaryRelation.setBookingOrderId(book.getBookingOrderId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(auxiliary.getRoomAuxiliaryId());
                    roomAuxiliaryRelation.setSort(auxiliary.getSort());

                    roomAuxiliaryRelations.add(roomAuxiliaryRelation);

                    bookingOrderRoomNums.add(bookingOrderRoomNum);
                }

                // 添加预订未分房的房间
                for (int k = 0; k < bort.getRoomTypeNum() - bort.getHasRoomNum(); k++) {

                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(book.getBookingOrderId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(bodp.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(0);
                    bookingOrderRoomNum.setRoomNum("0");
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setCheckinTime(book.getCheckinTime());
                    bookingOrderRoomNum.setCheckoutTime(book.getCheckoutTime());
                    bookingOrderRoomNum.setRowRoom(0);
                    bookingOrderRoomNum.setBookingOrderDailyPrices(rtPriceList);

                    bookingOrderRoomNums.add(bookingOrderRoomNum);

                }
                bort.setBookingOrderRoomNums(bookingOrderRoomNums);
                addBort.add(bort);
            }

            // 支付类型 1.微信支付 2.会员储值支付
            Integer payType = 1;
            Object payTypeObj = param.get("payType");
            if (payTypeObj != null) {
                payType = Integer.parseInt(payTypeObj.toString());
            }

            Account account = null;

            if (payType == 2) {

                Integer totalPrice = book.getTotalPrice();

                book.setPayType(2);
                book.setPayPrice(totalPrice);

                JSONObject vipParam = new JSONObject();
                vipParam.put("cardId", book.getCardId());
                vipParam.put("type", 1);
                vipParam.put("money", totalPrice);
                vipParam.put(ER.SESSION_TOKEN, sessionToken);

                ResponseData responseData1 = memberService.memberConsumption(vipParam);

                int code = responseData1.getCode();
                if (code < 0) {
                    throw new Exception(responseData1.getMsg());
                }

                Object data = responseData1.getData();
                Date date1 = new Date();
                account = new Account();
                String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
                account.setAccountId(accountId);
                account.setHid(user.getHid());
                account.setHotelGroupId(user.getHotelGroupId());
                account.setCreateUserId(user.getUserId());
                account.setCreateUserName(user.getUserName());
                account.setCreateTime(date1);
                account.setUpdateTime(date1);
                account.setUpdateUserId(user.getUserId());
                account.setUpdateUserName(user.getUserName());
                account.setIsCancel(0);
                account.setAccountYear(user.getBusinessYear());
                account.setAccountYearMonth(user.getBusinessMonth());
                account.setBusinessDay(user.getBusinessDay());
                account.setClassId(user.getClassId());
                account.setPrice(totalPrice);
                account.setSettleAccountTime(new Date());
                account.setRegistState(0);
                //消费-付款
                account.setPayType(2);
                account.setSaleNum(1);
                account.setUintPrice(account.getPrice());
                account.setPayClassId(5);
                account.setPayClassName("会员储值");
                account.setPayCodeId("9600");
                account.setPayCodeName("会员储值卡");
                account.setAccountType(1);
                account.setRemark("小程序预订会员支付");
                account.setThirdAccoutId(data + "");
                account.setRefundPrice(0);
                account.setRegistPersonId(0);
                account.setRoomNum("");
                account.setRegistPersonName("");
                account.setRegistPersonId(0);


            }

            wxServiceTransaction.addBookService(book, registGroup, addBort, roomAuxiliaryRelations, oprecords, bookingOrderConfig, account);

            responseData.setData(book);

            this.addOprecords(oprecords);

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        baseService.turnAlways(user);

                       /* SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                        smsHotelSendRecordRequest.setLocationId(SMS_LOC.ORDER_CREATE);
                        smsHotelSendRecordRequest.setSessionToken(sessionToken);
                        smsHotelSendRecordRequest.setPhoneNumber(smsParam.getString(SMS_LOC.PHONE));
                        ArrayList<String> list = (ArrayList<String>)JSONArray.toList(smsParam.getJSONArray(SMS_LOC.PARAM), String.class);
                        smsHotelSendRecordRequest.setParams(list);
                        baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);

                        HashMap<String, String> filed = new HashMap<>();
                        filed.put("sn", book.getSn());
                        filed.put("rtdesc", book.getRoomTypeSummary());
                        HashMap<String, String> dataMap = new HashMap<>();
                        dataMap.put("bookId", book.getBookingOrderId() + "");
                        HashMap<String, String> onClickCbData = new HashMap<>();
                        onClickCbData.put("bookingOrderId", book.getBookingOrderId().toString());
                        baseService.push(user.getHotelGroupId(), user.getHid(), 20, filed, dataMap, true, true, onClickCbData);*/

                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {

            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");

        }

        return responseData;
    }


    public ResponseData createBookingOrder(CreateBookingOrderRequest createBookingOrderRequest) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = createBookingOrderRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Integer hid = createBookingOrderRequest.getHid();
            if (hid == null || hid < 0) {
                throw new Exception("酒店信息不能为空");
            }
            user.setHid(createBookingOrderRequest.getHid());

            HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
            hotelBusinessDaySearch.setHid(user.getHid());
            HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);
            user.setBusinessDay(hotelBusinessDay.getBusinessDay());

//            createBookingOrderRequest.setCheckinTime(HotelUtils.parseStr2Date(HotelUtils.currentDate(createBookingOrderRequest.getCheckinTime()) + " 00:00:00"));
            // 预订单参数
//            String bookData = URLDecoder.decode(param.getString("bookData"), "utf-8");
//            JSONObject bookJson = JSONObject.fromObject(bookData);

            //生成预订单编号
            String sn = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.BOOK, this.stringRedisTemplate);

            Date date = new Date();

            BookingOrder book = new BookingOrder();
            book.setSn(sn);
            book.setHotelGroupId(user.getHotelGroupId());
            book.setHid(user.getHid());
            book.setCreateTime(date);
            book.setCreateUserId(user.getUserId());
            book.setCreateUserName(user.getUserName());
            book.setAcceptTime(date);
            book.setRoomCount(createBookingOrderRequest.getRoomCount());
            book.setOrderTime(date);

            book.setBookingName(createBookingOrderRequest.getBookingName());
            String phone = createBookingOrderRequest.getBookingPhone();
            if (phone.equals("null")) {
                phone = "";
            }
            book.setBookingPhone(phone);
            book.setBookingName(createBookingOrderRequest.getBookingName());
            book.setCheckinTime(createBookingOrderRequest.getCheckinTime());
            book.setCheckoutTime(createBookingOrderRequest.getCheckoutTime());
            book.setKeepTime(createBookingOrderRequest.getKeepTime());
            //入住天数
            int dayCount = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(createBookingOrderRequest.getCheckinTime()), HotelUtils.parseDate2Str(createBookingOrderRequest.getCheckoutTime())).size();

            book.setDayCount(dayCount);
            book.setFromHid(user.getHid());
            if (createBookingOrderRequest.getThirdPlatformOrderCode() != null) {
                book.setThirdPlatformOrderCode(createBookingOrderRequest.getThirdPlatformOrderCode());
            }


            //客源类型
            //客源类型
            int resourceId = createBookingOrderRequest.getResourceId();
            book.setResourceId(resourceId);
            // 1.散客 2.会员
            if (resourceId == 2) {
                book.setCardId(createBookingOrderRequest.getVipMsg().getCardId());
                book.setCardNo(createBookingOrderRequest.getVipMsg().getCardNo());
            }
            if (resourceId == 3 || resourceId == 4 || resourceId == 5) {
                book.setCompanyId(createBookingOrderRequest.getArMsg().getArId());
                book.setCompanyName(createBookingOrderRequest.getArMsg().getArName());
                book.setCompanyAccountId(createBookingOrderRequest.getArAntMsg().getId());
            }

            book.setFromType(25);
//            if (createBookingOrderRequest.getFromType() != null && !createBookingOrderRequest.getFromType().toString().equals("")) {
//                book.setFromType(createBookingOrderRequest.getFromType());
//            }

            /**
             *  缺失
             *      total_price 订单总金额
             *      unit_price  单价待补充
             *  待补充
             */
            Integer payMoney = 0;
            Object payMoneyObj = createBookingOrderRequest.getPayPrice();
            if (payMoneyObj != null && !"".equals(payMoneyObj.toString())) {
                payMoney = createBookingOrderRequest.getPayPrice();
            }
            book.setPayPrice(0);
            if (createBookingOrderRequest.getSumMoney() != null) {
                Double d = createBookingOrderRequest.getSumMoney() * 100;
                book.setTotalPrice(d.intValue());
            }
            book.setFromHid(user.getHid());

            //预订单类型 1.日租 2.钟点 3.长足
            book.setOrderType(1);
            Object orderType = createBookingOrderRequest.getOrderType();
            if (orderType != null) {
                book.setOrderType(Integer.parseInt(orderType.toString()));
            }
            //订单状态 订单状态 1.有效 2.NoShow 3.部分入住 4.全部入住 5.已取消 6.入住完成
            book.setOrderStatus(BOOK.STA_YX);
            Object orderStatus = createBookingOrderRequest.getOrderStatus();
            if (orderStatus != null) {
                book.setOrderStatus(Integer.parseInt(orderStatus.toString()));
            }
            if (createBookingOrderRequest.getRemark() != null) {
                book.setRemark(createBookingOrderRequest.getRemark());
            }

            // 订单时间
            book.setOrderYear(user.getBusinessYear());
            book.setOrderYearMonth(user.getBusinessMonth());
            book.setBusinessDay(user.getBusinessDay());
            book.setClassId(user.getClassId());
            book.setCreateTime(date);
            book.setCreateUserId(user.getSessionId());
            book.setCreateUserName(user.getUserName());
            book.setUpdateTime(date);
            book.setUpdateUserId(user.getSessionId());
            book.setUpdateUserName(user.getUserName());
            book.setRoomTypeSummary(createBookingOrderRequest.getRoomTypeSummary());

            /**
             * 查询预订单辅助房态
             *  如果当前时间等于 预抵时间，则状态为预抵。
             *      否则 辅助房态为预离。
             */
            RoomAuxiliary auxiliary = new RoomAuxiliary();
            String nowDate = HotelUtils.currentDate();
            if (nowDate.equals(HotelUtils.currentDate(createBookingOrderRequest.getCheckinTime()))) {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_ARRIVALS);
            } else {
                auxiliary = roomAuxiliaryDao.selectById(ROOM_AUXILIARY.BOOK_CREATE);
            }

            // 团队房  isGroup==1则为团队
            RegistGroup registGroup = null;
            int isGroup = createBookingOrderRequest.getIsGroup();
            if (isGroup == 1) {
                registGroup = new RegistGroup();
                registGroup.setBusinessDay(user.getBusinessDay());
                registGroup.setHid(user.getHid());
                registGroup.setClassId(user.getClassId());
                registGroup.setCreateTime(new Date());
                registGroup.setCreateUserId(user.getUserId());
                registGroup.setCreateUserName(user.getUserName());
                registGroup.setSn(book.getSn());
                registGroup.setPayType(0);
                registGroup.setGroupName(createBookingOrderRequest.getGroupName());
                registGroup.setSumRooms(book.getRoomCount());
                registGroup.setUpdateTime(date);
                registGroup.setUpdateUserId(user.getUserId());
                registGroup.setUpdateUserName(user.getUserName());
                registGroup.setState(1);
            }


            /**
             * 添加操作日志
             */
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);
            oprecord.setMainId(sn);
            oprecord.setBookingOrderId(book.getBookingOrderId());
            oprecord.setDescription("创建预订单,订单号:" + sn);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecords.add(oprecord);

            /**
             * 2.添加预订单设置
             */
            BookingOrderConfig bookingOrderConfig = new BookingOrderConfig();
            bookingOrderConfig.setPriceSecrecy(createBookingOrderRequest.getPriceSecrecy());
            bookingOrderConfig.setInfoSecrecy(createBookingOrderRequest.getInfoSecrecy());
            bookingOrderConfig.setAutoCheckin(createBookingOrderRequest.getAutoCheckIn());
            bookingOrderConfig.setNoDeposit(createBookingOrderRequest.getNoDposit());
            bookingOrderConfig.setNoPrice(createBookingOrderRequest.getNoPrice());
            bookingOrderConfig.setContinueRes(createBookingOrderRequest.getContinueRes());
            bookingOrderConfig.setAutoAr(0);
            if (createBookingOrderRequest.getAutoAr() != null) {
                bookingOrderConfig.setAutoAr(createBookingOrderRequest.getAutoAr());
            }
            bookingOrderConfig.setAvePrice(0);
            if (createBookingOrderRequest.getAvePrice() != null) {
                bookingOrderConfig.setAutoAr(createBookingOrderRequest.getAvePrice());
            }

            /**
             *  取出两个时间的日期差
             */
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.currentDate(createBookingOrderRequest.getCheckinTime()), HotelUtils.currentDate(createBookingOrderRequest.getCheckoutTime()));

            // 要添加的辅助房态
            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            /**
             * 3.添加预订房型信息
             */
            List<CreateBookingOrderRequest.roomType> roomTypeList = createBookingOrderRequest.getRoomTypeList();

            BookingOrderDailyPrice bodp = new BookingOrderDailyPrice();

            // 需要添加的房型
            ArrayList<BookingOrderRoomType> addBort = new ArrayList<>();

            for (int i = 0; i < roomTypeList.size(); i++) {
                // 预订房型数据
                CreateBookingOrderRequest.roomType roomType = roomTypeList.get(i);
                // 房间数据
                List<CreateBookingOrderRequest.roomType.roomInfo> roomList = roomType.getRoomList();

                // 创建预订房型信息
                BookingOrderRoomType bort = new BookingOrderRoomType();
                bort.setHid(user.getHid());
                bort.setHotelGroupId(user.getHotelGroupId());
                bort.setArriveTime(HotelUtils.currentDate(createBookingOrderRequest.getCheckinTime()) + " " + book.getKeepTime() + ":00");
                bort.setRoomTypeId(roomType.getRoomTypeId());
                bort.setRoomTypeNum(roomType.getNum());
                bort.setHasRoomNum(roomList.size());
                bort.setPriceCodeId(roomType.getRateId());
                bort.setPriceCode(roomType.getRateCode());
                book.setRateCodeId(bort.getPriceCodeId());
                book.setRateCodeName(bort.getPriceCode());
                bort.setState(BOOK.STA_YX);
                bort.setOrderState(BOOK.STA_YX);
                bort.setCreateTime(date);
                bort.setCreateUserId(user.getUserId());
                bort.setCreateUserName(user.getUserName());
                bort.setUpdateTime(date);
                bort.setUpdateUserId(user.getUserId());
                bort.setUpdateUserName(user.getUserName());
                bort.setCheckinTime(book.getCheckinTime());
                bort.setCheckoutTime(book.getCheckoutTime());

                //增加预订房型操作日志
                Oprecord oprecord1 = new Oprecord(user);
                ;
                oprecord1.setMainId(sn);
                oprecord1.setRegistId(book.getBookingOrderId());
                oprecord1.setDescription("对订单 : " + sn + "，添加预订房型：" + roomType.getRoomTypeName() + ",房间数量: " + bort.getRoomTypeNum() + ",房价码:" + bort.getPriceCode());
                oprecord1.setOccurTime(HotelUtils.currentTime());
                oprecords.add(oprecord1);

                List<CreateBookingOrderRequest.roomType.priceInfo> priceList = roomType.getPriceList();
                // 房型添加的房价
                ArrayList<BookingOrderDailyPrice> rtPriceList = new ArrayList<>();

                for (int pl = 0; pl < priceList.size(); pl++) {
                    CreateBookingOrderRequest.roomType.priceInfo priceInfo = priceList.get(pl);
                    bodp = new BookingOrderDailyPrice();
                    bodp.setBookingOrderRoomNumId(0);
                    bodp.setHid(user.getHid());
                    bodp.setHotelGroupId(user.getHotelGroupId());
                    bodp.setPrice(priceInfo.getPrice());
                    bodp.setDailyTime(Integer.parseInt(priceInfo.getDate().replace("-", "")));
                    bodp.setRoomTypeId(bort.getRoomTypeId());
                    bodp.setRoomNumId(0);
                    bodp.setBreakNum(0);
                    bodp.setDailyState(1);
                    bodp.setRateCodeId(bort.getPriceCodeId());
                    bodp.setRateCode(bort.getPriceCode());
                    bodp.setIsStayover(0);
                    bodp.setCreateTime(date);
                    bodp.setCreateUserId(user.getUserId());
                    bodp.setCreateUserName(user.getUserName());
                    bodp.setUpdateTime(date);
                    bodp.setUpdateUserId(user.getUserId());
                    bodp.setUpdateUserName(user.getUserName());
                    rtPriceList.add(bodp);
                }

                bort.setBookingOrderDailyPrices(rtPriceList);

                // 需要添加的预订房间

                ArrayList<BookingOrderRoomNum> bookingOrderRoomNums = new ArrayList<>();

                for (int k = 0; k < roomList.size(); k++) {

                    CreateBookingOrderRequest.roomType.roomInfo roomInfo = roomList.get(k);

                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(book.getBookingOrderId());
                    bookingOrderRoomNum.setBookingOrderRoomTypeId(bort.getId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(bort.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(roomInfo.getRoomInfoId());
                    bookingOrderRoomNum.setRoomNum(roomInfo.getRoomNum());
                    if (roomInfo.getRoomCode() != null) {
                        bookingOrderRoomNum.setRoomCode(roomInfo.getRoomCode());
                    }
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setCheckinTime(book.getCheckinTime());
                    bookingOrderRoomNum.setCheckoutTime(book.getCheckoutTime());
                    bookingOrderRoomNum.setRowRoom(1);
                    bookingOrderRoomNum.setRateCodeId(bort.getPriceCodeId());
                    bookingOrderRoomNum.setRateCode(bort.getPriceCode());
                    bookingOrderRoomNum.setBookingOrderDailyPrices(rtPriceList);


                    //记录操作日志
                    Oprecord oprecord2 = new Oprecord(user);
                    oprecord2.setMainId(sn);
                    oprecord2.setRegistId(book.getBookingOrderId());
                    oprecord2.setDescription("对订单 : " + sn + "，进行排房：" + bookingOrderRoomNum.getRoomNum());
                    oprecord2.setOccurTime(HotelUtils.currentTime());
                    oprecords.add(oprecord2);

                    //添加辅助房态
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(bookingOrderRoomNum.getRoomNumId());
                    roomAuxiliaryRelation.setRoomNum(bookingOrderRoomNum.getRoomNum());
                    roomAuxiliaryRelation.setBookingOrderId(book.getBookingOrderId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(auxiliary.getRoomAuxiliaryId());
                    roomAuxiliaryRelation.setSort(auxiliary.getSort());

                    roomAuxiliaryRelations.add(roomAuxiliaryRelation);

                    bookingOrderRoomNums.add(bookingOrderRoomNum);
                }

                // 添加预订未分房的房间
                for (int k = 0; k < bort.getRoomTypeNum() - bort.getHasRoomNum(); k++) {

                    BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
                    bookingOrderRoomNum.setHid(user.getHid());
                    bookingOrderRoomNum.setHotelGroupId(user.getHotelGroupId());
                    bookingOrderRoomNum.setBookingOrderId(book.getBookingOrderId());
                    bookingOrderRoomNum.setRoomCode(bookingOrderRoomNum.getRoomCode());
                    bookingOrderRoomNum.setCreateTime(date);
                    bookingOrderRoomNum.setCreateUserId(user.getUserId());
                    bookingOrderRoomNum.setUpdateTime(date);
                    bookingOrderRoomNum.setUpdateUserId(user.getUserId());
                    bookingOrderRoomNum.setRoomTypeId(bodp.getRoomTypeId());
                    bookingOrderRoomNum.setRoomNumId(0);
                    bookingOrderRoomNum.setRoomNum("0");
                    bookingOrderRoomNum.setIsCheckin(0);
                    bookingOrderRoomNum.setOrderState(1);
                    bookingOrderRoomNum.setIsCheckout(0);
                    bookingOrderRoomNum.setCheckinTime(book.getCheckinTime());
                    bookingOrderRoomNum.setCheckoutTime(book.getCheckoutTime());
                    bookingOrderRoomNum.setRowRoom(0);
                    bookingOrderRoomNum.setBookingOrderDailyPrices(rtPriceList);
                    bookingOrderRoomNums.add(bookingOrderRoomNum);

                }
                bort.setBookingOrderRoomNums(bookingOrderRoomNums);
                addBort.add(bort);
            }

            // 支付类型 1.微信支付 2.会员储值支付
            Integer payType = 1;
            Object payTypeObj = createBookingOrderRequest.getPayType();
            if (payTypeObj != null) {
                payType = Integer.parseInt(payTypeObj.toString());
            }

            Account account = null;

            if (payType == 2) {

                Integer totalPrice = book.getTotalPrice();

                book.setPayType(2);
                book.setPayPrice(totalPrice);

                JSONObject vipParam = new JSONObject();
                vipParam.put("cardId", book.getCardId());
                vipParam.put("type", 1);
                vipParam.put("money", totalPrice);
                vipParam.put(ER.SESSION_TOKEN, sessionToken);

                ResponseData responseData1 = memberService.memberConsumption(vipParam);

                int code = responseData1.getCode();
                if (code < 0) {
                    throw new Exception(responseData1.getMsg());
                }

                Object data = responseData1.getData();
                Date date1 = new Date();
                account = new Account();
                String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
                account.setAccountId(accountId);
                account.setHid(user.getHid());
                account.setHotelGroupId(user.getHotelGroupId());
                account.setCreateUserId(user.getUserId());
                account.setCreateUserName(user.getUserName());
                account.setCreateTime(date1);
                account.setUpdateTime(date1);
                account.setUpdateUserId(user.getUserId());
                account.setUpdateUserName(user.getUserName());
                account.setIsCancel(0);
                account.setAccountYear(user.getBusinessYear());
                account.setAccountYearMonth(user.getBusinessMonth());
                account.setBusinessDay(user.getBusinessDay());
                account.setClassId(user.getClassId());
                account.setPrice(totalPrice);
                account.setSettleAccountTime(new Date());
                account.setRegistState(0);
                //消费-付款
                account.setPayType(2);
                account.setSaleNum(1);
                account.setUintPrice(account.getPrice());
                account.setPayClassId(5);
                account.setPayClassName("会员储值");
                account.setPayCodeId("9600");
                account.setPayCodeName("会员储值卡");
                account.setAccountType(1);
                account.setRemark("小程序预订会员支付");
                account.setThirdAccoutId(data + "");
                account.setRefundPrice(0);
                account.setRegistPersonId(0);
                account.setRoomNum("");
                account.setRegistPersonName("");
                account.setRegistPersonId(0);


            }

            wxServiceTransaction.addBookService(book, registGroup, addBort, roomAuxiliaryRelations, oprecords, bookingOrderConfig, account);

            responseData.setData(book);

            this.addOprecords(oprecords);

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        baseService.turnAlways(user);

                       /* SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                        smsHotelSendRecordRequest.setLocationId(SMS_LOC.ORDER_CREATE);
                        smsHotelSendRecordRequest.setSessionToken(sessionToken);
                        smsHotelSendRecordRequest.setPhoneNumber(smsParam.getString(SMS_LOC.PHONE));
                        ArrayList<String> list = (ArrayList<String>)JSONArray.toList(smsParam.getJSONArray(SMS_LOC.PARAM), String.class);
                        smsHotelSendRecordRequest.setParams(list);
                        baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);

                        HashMap<String, String> filed = new HashMap<>();
                        filed.put("sn", book.getSn());
                        filed.put("rtdesc", book.getRoomTypeSummary());
                        HashMap<String, String> dataMap = new HashMap<>();
                        dataMap.put("bookId", book.getBookingOrderId() + "");
                        HashMap<String, String> onClickCbData = new HashMap<>();
                        onClickCbData.put("bookingOrderId", book.getBookingOrderId().toString());
                        baseService.push(user.getHotelGroupId(), user.getHid(), 20, filed, dataMap, true, true, onClickCbData);*/

                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {

            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");

        }

        return responseData;
    }

    @Override
    public ResponseData searchOrderInfo(SearchOrderInfoRequest searchOrderInfoRequest) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = searchOrderInfoRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if (null == searchOrderInfoRequest.getBookingOrderId()) {
                throw new Exception(HOTEL_CONST.BOOKING_ORDER_ID_IS_NULL);
            }
            Integer bookingOrderId = searchOrderInfoRequest.getBookingOrderId();
            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderId);

            if (null == bookingOrder) {
                throw new Exception(HOTEL_CONST.BOOKING_ORDER_ID_IS_NULL);
            }
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingOrderId);
            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
            if (bookingOrderRoomNums.size() < 1) {
                throw new Exception(HOTEL_CONST.DATA_LIST_IS_NULL);
            }
            BookingOrderRoomNum bookingOrderRoomNum = bookingOrderRoomNums.get(0);
            SearchOrderInfoView searchOrderInfoView = new SearchOrderInfoView();
            searchOrderInfoView.setBookingOrderId(bookingOrder.getBookingOrderId());
            searchOrderInfoView.setCheckinTime(HotelUtils.parseDate2Str(bookingOrderRoomNum.getCheckinTime()));
            searchOrderInfoView.setCheckoutTime(HotelUtils.parseDate2Str(bookingOrderRoomNum.getCheckoutTime()));
            searchOrderInfoView.setRoomNo(bookingOrderRoomNum.getRoomNum());
            searchOrderInfoView.setRoomInfoId(bookingOrderRoomNum.getRoomNumId());
            searchOrderInfoView.setRoomTypeId(bookingOrderRoomNum.getRoomTypeId());
            searchOrderInfoView.setCreateTime(bookingOrder.getCreateTime());
            searchOrderInfoView.setSn(bookingOrder.getSn());
            searchOrderInfoView.setHid(bookingOrder.getHid());
            searchOrderInfoView.setRateId(bookingOrderRoomNum.getRateCodeId());
            searchOrderInfoView.setOrderStatus(bookingOrder.getOrderStatus());
            searchOrderInfoView.setBookingRoomId(bookingOrderRoomNum.getId());
            String expireTime = HotelUtils.parseDate2Str(new Date(bookingOrderRoomNum.getCheckinTime().getTime() + 2 * 60 * 1000));
            searchOrderInfoView.setExpireTime(expireTime);
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setBookingOrderId(bookingOrderId);
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
            Integer orderMoney = 0;
            for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
                if (null == bookingOrderDailyPrices.get(i).getRoomNumId() || bookingOrderDailyPrices.get(i).getRoomNumId() == 0) {
                    continue;
                }
                orderMoney += bookingOrderDailyPrices.get(i).getPrice();
                searchOrderInfoView.setRataCodeName(bookingOrderDailyPrices.get(i).getRateCode());
            }
            searchOrderInfoView.setRegistId(bookingOrderRoomNum.getRegistId());
            searchOrderInfoView.setOrderMoney(orderMoney);

            HotelHourRoomTypeSearch hotelHourRoomTypeSearch = new HotelHourRoomTypeSearch();
            hotelHourRoomTypeSearch.setRoomTypeId(bookingOrderRoomNum.getRoomTypeId());
            hotelHourRoomTypeSearch.setHourRoomInfoId(searchOrderInfoView.getRateId());
            List<HotelHourRoomType> hotelHourRoomTypes = hotelHourRoomTypeDao.selectBySearch(hotelHourRoomTypeSearch);

            if (hotelHourRoomTypes.size() > 0) {
                HotelHourRoomType hotelHourRoomType = hotelHourRoomTypes.get(0);
                searchOrderInfoView.setHourPrice(hotelHourRoomType.getHourPrice() == null ? hotelHourRoomType.getPrice() / hotelHourRoomType.getHourRoomCode() : hotelHourRoomType.getHourPrice());
            } else {
                searchOrderInfoView.setHourPrice(0);
            }
            Long time = new Date().getTime();
            searchOrderInfoView.setPaySn(time.toString());
            responseData.setData(searchOrderInfoView);

        } catch (Exception e) {

            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");

        }

        return responseData;
    }

    @Override
    public ResponseData searchBook(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /**
             *  1.添加预订主单信息
             */
            JSONObject bookSearch = JSONObject.fromObject(URLDecoder.decode(param.getString("bookSearch"), "utf-8"));
            bookSearch.put("createUserId", user.getSessionId());
            Object orderStatus = bookSearch.get("orderStatus");
            if (orderStatus != null && !"".equals(orderStatus.toString())) {
                int orderStatus1 = Integer.parseInt(orderStatus.toString());
                if (orderStatus1 == 7) {
                    bookSearch.put("orderStatus", null);
                    bookSearch.remove("orderStatus");
                    bookSearch.put("orderStatusIn", "3,4");
                }
            }
            List<BookingOrder> list = bookingOrderDao.selectBySearchJson(bookSearch);
            Map map = bookingOrderDao.selectBySearchJsonCount(bookSearch);
            map.put("list", list);

            HotelBaseInfoSearch hotelBaseInfoSearch = new HotelBaseInfoSearch();
            hotelBaseInfoSearch.setHotelGroupId(user.getHotelGroupId());
            List<HotelBaseInfo> hotelBaseInfos = hotelBaseInfoDao.selectBySearch(hotelBaseInfoSearch);

            Map<Integer, HotelBaseInfo> collect = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfo::getHid, a -> a, (k1, k2) -> k1));

            map.put("hotelMap", collect);
            responseData.setData(map);

        } catch (Exception e) {

            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");

        }

        return responseData;
    }


    @Override
    public ResponseData searchBookRoomById(BookingOrderRoomNumSearch bookingOrderRoomNumSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            final TbUserSession user = this.getTbUserSession(bookingOrderRoomNumSearch);

            Integer id = bookingOrderRoomNumSearch.getId();


            Page<BookingOrderRoomNumForDayView> bookingOrderRoomNumForDayViews = bookingOrderRoomNumDao.selectBySearchforDayView(bookingOrderRoomNumSearch);

            if (bookingOrderRoomNumForDayViews.size() < 1) {
                throw new Exception("未查询到订单信息");
            }

            BookingOrderRoomNumForDayView bookingOrderRoomNumForDayView = bookingOrderRoomNumForDayViews.get(0);

            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setBookingOrderRoomNumId(id);

            Page<RegistPerson> registPeople = registPersonDao.searchRegistPersonList(registPersonSearch);
            bookingOrderRoomNumForDayView.setRegistPeople(registPeople);

            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setBookingOrderRoomNumId(id);
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
            bookingOrderRoomNumForDayView.setPriceList(bookingOrderDailyPrices);

            JSONObject jsonObject = JSONObject.fromObject(bookingOrderRoomNumForDayView);

            RoomType roomType = roomTypeDao.selectById(bookingOrderRoomNumForDayView.getRoomTypeId());

            HotelBaseInfo hotelBaseInfo = hotelBaseInfoDao.selectById(roomType.getHid());

            if (!hotelBaseInfo.getHotelGroupId().equals(user.getHotelGroupId())) {
                throw new Exception("未查询到订单信息G");
            }

            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setBookingId(bookingOrderRoomNumForDayView.getBookingOrderId());
            accountSearch.setPayType(2);
            accountSearch.setIsCancel(0);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);


            jsonObject.put("roomTypeName", roomType.getRoomTypeName());
            jsonObject.put("hotelName", hotelBaseInfo.getHotelName());
            jsonObject.put("checkinTime", HotelUtils.parseDate2Str(bookingOrderRoomNumForDayView.getCheckinTime()));
            jsonObject.put("checkoutTime", HotelUtils.parseDate2Str(bookingOrderRoomNumForDayView.getCheckoutTime()));
            jsonObject.put("discount", roomType.getDiscount());

            // 是否需要押金 0 否 1 是
            Integer isDep = 1;

            if (roomType.getDiscount() == null || roomType.getDiscount() <= 0) {
                isDep = 0;
            } else if (accounts != null && accounts.size() > 1) {
                isDep = 0;
            }
            ;
            jsonObject.put("isDep", isDep);

            JSONArray jsonArray = new JSONArray();
            for (Account account : accounts) {

                JSONObject jo = new JSONObject();
                jo.put("price", account.getPrice());
                jo.put("accountId", account.getAccountId());
                jo.put("roomInfoId", account.getRoomInfoId());
                jo.put("roomNum", account.getRoomNum());
                jo.put("payCodeName", account.getPayCodeName());
                jo.put("remark", account.getRemark() == null ? "" : account.getRemark());

                jsonArray.add(jo);

            }

            jsonObject.put("accountList", jsonArray);


            responseData.setData(jsonObject);

        } catch (Exception e) {

            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");

        }

        return responseData;
    }

    @Override
    public ResponseData searchWxRegist(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            TbUserSession user = this.getTbUserSession(sessionToken);

            CardGroupInfoSearch cardGroupInfoSearch = new CardGroupInfoSearch();
            cardGroupInfoSearch.setHotelGroupId(user.getHotelGroupId());
            cardGroupInfoSearch.setOpenId(user.getSessionId());

            List<CardGroupInfo> cardGroupInfos = cardGroupInfoDao.selectBySearch(cardGroupInfoSearch);

            if (cardGroupInfos.size() < 1) {
                responseData.setResult(ER.ERR);
                responseData.setMsg("未查询到会员信息");
                return responseData;
            }

            CardGroupInfo cardGroupInfo = cardGroupInfos.get(0);

            // 查询单店集团信息
            CardInfoSearch cardInfoSearch = new CardInfoSearch();
            cardInfoSearch.setCardGroupId(cardGroupInfo.getId());
            cardInfoSearch.setHotelGroupId(user.getHotelGroupId());
            List<CardInfo> cardInfos = cardInfoDao.selectBySearch(cardInfoSearch);

            String memberIds = "-199";

            for (CardInfo cardInfo : cardInfos) {

                memberIds += ",";
                memberIds += cardInfo.getId();

            }
            RegistSearch registSearch = new RegistSearch();
            registSearch.setMemberIds(memberIds);

            Object state = param.get("state");
            if (state != null && !state.toString().equals("")) {
                registSearch.setState(Integer.parseInt(state.toString()));
            }

            List<Regist> regists = registDao.selectBySearch(registSearch);

            regists.sort(Comparator.comparing(Regist::getRegistId).reversed());

            String registIds = "";

            for (Regist regist : regists) {
                registIds += regist.getRegistId() + ",";
            }

            registIds = registIds.substring(0, registIds.length() - 1);

            HotelBaseInfoSearch hotelBaseInfoSearch = new HotelBaseInfoSearch();
            hotelBaseInfoSearch.setHotelGroupId(user.getHotelGroupId());
            Page<HotelBaseInfo> hotelBaseInfos = hotelBaseInfoDao.selectBySearch(hotelBaseInfoSearch);

            Map<Integer, HotelBaseInfo> hotelMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfo::getHid, a -> a, (k1, k2) -> k1));

            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistIds(registIds);

            Page<RegistPerson> registPeople = registPersonDao.searchRegistPersonList(registPersonSearch);

            Map<Integer, List<RegistPerson>> collect = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));

            JSONArray jsonArray = new JSONArray();


            for (Regist regist : regists) {

                JSONObject jo = new JSONObject();

                jo.put("hid", regist.getHid());
                jo.put("hotelGroupId", regist.getHotelGroupId());
                jo.put("roomInfoId", regist.getRoomNumId());
                jo.put("roomNum", regist.getRoomNum());
                jo.put("reType", 1);
                jo.put("registId", regist.getRegistId());
                jo.put("roomTypeName", regist.getRoomTypeName());
                jo.put("roomTypeId", regist.getRoomTypeId());
                String s = HotelUtils.parseDate2Str(regist.getCheckinTime());
                jo.put("checkinTime", s);
                String s1 = HotelUtils.parseDate2Str(regist.getCheckoutTime());
                jo.put("checkoutTime", s1);


                List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(s, s1);
                jo.put("dayCount", allDayListBetweenDate.size());
                jo.put("state", regist.getState());

                HotelBaseInfo hotelBaseInfo = hotelMap.get(regist.getHid());
                jo.put("hotelName", hotelBaseInfo.getHotelName());
                jo.put("address", hotelBaseInfo.getAddr() == null ? "" : hotelBaseInfo.getAddr());

                List<RegistPerson> registPeople1 = collect.get(regist.getRegistId());

                String name = "";

                JSONArray jrp = new JSONArray();


                for (RegistPerson rp : registPeople1) {
                    name += rp.getPersonName() + " ";
                    JSONObject oe = new JSONObject();
                    oe.put("name", rp.getPersonName());
                    oe.put("phone", rp.getPhone() == null ? "" : rp.getPhone());
                    oe.put("idCode", rp.getIdCode() == null ? "" : rp.getIdCode());
                    oe.put("img", rp.getCameraImage() == null ? "" : rp.getCameraImage());
                    jrp.add(oe);
                }

                jo.put("people", jrp);
                jo.put("peopleName", name);
                ;

                jsonArray.add(jo);

            }

            responseData.setData(jsonArray);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }

    /**
     * 更新缓存中每日房情的数据
     *
     * @param user
     * @param resultMap
     * @throws Exception
     */
    public void turnAlwaysCache(TbUserSession user, Map<String, Object> resultMap, HashOperations<String, Object, Object> userCahe) throws Exception {

        // 最底层 展示信息，已状态为key，value 未最基础信息
        // 状态 1.入住信息  2.维修/停用 3.预定信息(已排房) 4.预定信息(未排房)
        Map<Integer, JSONObject> stateMsg = new HashMap<>();

        // 第二层 已房间id为key，value 为 stateMsg
        HashMap<Integer, HashMap<Integer, JSONObject>> roomForStateMsg = new HashMap<>();

        // 最外层 已日期为key，value 为 roomForStateMsg
        // 上面两个变量只为注释，并没有实际意义
        HashMap<String, HashMap<String, HashMap<String, JSONObject>>> dateRoomStateMsg = new HashMap<>();

        // 房间使用情况
        //   key  roomId+yyyy-MM-dd
        HashMap<String, Integer> roomUseDetail = new HashMap<>();

        // 每日使用数据明细
        JSONObject dateNum = new JSONObject();

        //将所有查询数据库的数据 处理后添加到当前集合
        List<JSONObject> baseData = new ArrayList<>();

        // 记录 最小的开始时间和 最大的结束时间
        // 已这两个时间为准则遍历，减少循环次数
        Date minStartTime = new Date();
        Date maxEndTime = new Date();

        // 1.查询所有在住信息
        //   在住信息的状态为 1
        RegistSearch registSearch = new RegistSearch();
        registSearch.setHid(user.getHid());
        registSearch.setState(0);

        List<Regist> regists = registDao.selectBySearch(registSearch);

        //查询在住人
        HashMap<String, Object> map = new HashMap<>();
        map.put("hid", user.getHid());
        map.put("registState", 0);
        List<RegistPerson> registPeople = registPersonDao.searchCheckinPeople(map);
        Map<Integer, List<RegistPerson>> collect = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));

        for (Regist regist : regists) {
            Date checkinTime = regist.getCheckinTime();
            Date checkoutTime = regist.getCheckoutTime();


            JSONObject registObj = JSONObject.fromObject(regist);
            registObj.put("people", collect.get(regist.getRegistId()));
            dateBaseHandler(1, baseData, regist.getRoomNumId(), checkinTime, checkoutTime, minStartTime, maxEndTime, registObj);
        }

        // 2.查询当前有效的维修信息
        RoomRepairRecordSearch roomRepairRecordSearch = new RoomRepairRecordSearch();
        roomRepairRecordSearch.setHid(user.getHid());
        roomRepairRecordSearch.setState(0);
        List<RoomRepairRecord> roomRepairRecords = roomRepairRecordDao.selectBySearch(roomRepairRecordSearch);

        for (RoomRepairRecord roomRepairRecord : roomRepairRecords) {

            Date begintime = roomRepairRecord.getBegintime();
            Date endtime = roomRepairRecord.getEndtime();

            JSONObject registObj = JSONObject.fromObject(roomRepairRecord);
            dateBaseHandler(2, baseData, roomRepairRecord.getRoomId(), begintime, endtime, minStartTime, maxEndTime, registObj);

        }

        // 3.查询当前预定信息
        BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
        bookingOrderRoomNumSearch.setHid(user.getHid());
        bookingOrderRoomNumSearch.setOrderState(BOOK.STA_YX);
        bookingOrderRoomNumSearch.setIsCheckin(0);

        List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

        // 对预定信息进行分组
        //  已排房/未排房

        // 未排房信息
        HashMap<String, JSONArray> noRowRoomBookMsg = new HashMap<>();

        for (BookingOrderRoomNum bookingOrderRoomNum : bookingOrderRoomNums) {
            Date checkinTime = bookingOrderRoomNum.getCheckinTime();
            Date checkoutTime = bookingOrderRoomNum.getCheckoutTime();

            // 0 未分房  1 已分房
            Integer rowRoom = bookingOrderRoomNum.getRowRoom();
            JSONObject jsonObject = JSONObject.fromObject(bookingOrderRoomNum);
            int type = 3;

            if (rowRoom == 0) {
                jsonObject.put("roomNumId", 0);
                type = 4;
            }
            dateBaseHandler(type, baseData, bookingOrderRoomNum.getRoomNumId(), checkinTime, checkoutTime, minStartTime, maxEndTime, jsonObject);
        }

        // 4.对已经处理的baseData做处理
        // 对最大最小时间差做分组
        List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(minStartTime).substring(0, 10), HotelUtils.getDay(HotelUtils.parseDate2Str(maxEndTime), 1));

        for (String date : allDayListBetweenDate) {

            //日期格式 int类型  yyyyMMdd
            int dateInt = Integer.parseInt(date.replace("-", ""));

            // 每日房情 ，精确到房间
            HashMap<String, HashMap<String, JSONObject>> drsm = dateRoomStateMsg.get(date);
            if (drsm == null) {
                drsm = new HashMap<String, HashMap<String, JSONObject>>();
            }

            JSONArray noRoomList = noRowRoomBookMsg.get(date);
            if (noRoomList == null) {
                noRoomList = new JSONArray();
            }

            int checkInNum = 0;     //入住数
            int repairdNum = 0;     //维修数
            int bookRoomNum = 0;   //预定数
            int bookNoRoomNum = 0;  //预定未分房数

            for (JSONObject obj : baseData) {
                // 开始时间
                // 如果当前时间 小于 数据的开始时间，则进行下一条数据
                int beginTimeInt = obj.getInt("beginTimeInt");
                if (dateInt < beginTimeInt) {
                    continue;
                }

                // 结束时间
                // 如果结束时间 大于 数据的结束时间，则进行下一条数据
                int endTimeInt = obj.getInt("endTimeInt");
                if (dateInt > endTimeInt) {
                    continue;
                }

                int roomNumId = obj.getInt("roomNumId");
                int dataType = obj.getInt("dataType");

                //预定未分房
                if (dataType == 4) {
                    noRoomList.add(obj);
                    bookNoRoomNum++;
                    continue;
                }

                switch (dataType) {
                    case 1:
                        checkInNum++;
                        break;
                    case 2:
                        repairdNum++;
                        break;
                    case 3:
                        bookRoomNum++;
                        break;
                }

                //每日的使用情况
                String roomUseDetailKey = roomNumId + date;
                roomUseDetail.put(roomUseDetailKey, dataType);

                String roomNumIdStr = roomNumId + "";
                String dataTypeStr = dataType + "";
                HashMap<String, JSONObject> rfms = drsm.get(roomNumId);
                if (rfms == null) {
                    rfms = new HashMap<>();
                }
                rfms.put(dataTypeStr, JSONObject.fromObject(obj.toString()));
                rfms.get(dataTypeStr).put("isEndMark", 0);
                // 如果当前时间和离店时间一直，则添加预计结束标示
                if (dateInt == endTimeInt) {
                    rfms.get(dataTypeStr).put("isEndMark", 1);
                }
                drsm.put(roomNumIdStr, rfms);

            }

            JSONObject dayNum = new JSONObject();
            dayNum.put("checkInNum", checkInNum);
            dayNum.put("repairdNum", repairdNum);
            dayNum.put("bookRoomNum", bookRoomNum);
            dayNum.put("bookNoRoomNum", bookNoRoomNum);
            dateNum.put(date, dayNum);


            dateRoomStateMsg.put(date, drsm);
            noRowRoomBookMsg.put(date, noRoomList);

        }


        resultMap.put("dateRoomStateMsg", dateRoomStateMsg);
        resultMap.put("noRowRoomBookMsg", noRowRoomBookMsg);
        resultMap.put("turnAlwaysDateNum", dateNum);
        resultMap.put("roomUseDetail", roomUseDetail);

        String drms = ECache.TURNALWAYS_DATEROOMSTATE + "_" + user.getHid();
        userCahe.put(ECache.TURNALWAYS_DATEROOMSTATE, drms, JSONObject.fromObject(dateRoomStateMsg).toString());

        String nrrb = ECache.TURNALWAYS_NOROWROOMBOOK + "_" + user.getHid();
        userCahe.put(ECache.TURNALWAYS_NOROWROOMBOOK, nrrb, JSONObject.fromObject(noRowRoomBookMsg).toString());

        String tadn = ECache.TURNALWAYS_TURNALWATSDATENUM + "_" + user.getHid();
        userCahe.put(ECache.TURNALWAYS_TURNALWATSDATENUM, tadn, JSONObject.fromObject(dateNum).toString());

        // 每天的使用详情
        String tdsd = ECache.TURNALWAYS_DAYUSEDETAIL + "_" + user.getHid();
        userCahe.put(ECache.TURNALWAYS_DAYUSEDETAIL, tdsd, JSONObject.fromObject(roomUseDetail).toString());

    }

    /**
     * @param type      状态 1.入住信息  2.维修/停用 3.预定信息(已排房) 4.预定信息(未排房)
     * @param baseData  数据集合
     * @param roomNumId 房间id
     * @param beginTime 开始时间
     * @param endTime   结束时间
     */
    private void dateBaseHandler(int type, List<JSONObject> baseData, Integer roomNumId, Date beginTime, Date endTime, Date minStartTime, Date maxEndTime, JSONObject obj) {

        if (beginTime == null || endTime == null) {
            return;
        }

        // 获取每个的入住日期，取出日期差
        if (minStartTime.getTime() > beginTime.getTime()) {
            minStartTime.setTime(beginTime.getTime());
        }

        if (maxEndTime.getTime() < endTime.getTime()) {
            maxEndTime.setTime(endTime.getTime());
        }

        obj.put("createTime", "");
        obj.put("updateTime", "");
        obj.put("dataType", type);
        obj.put("beginTimeInt", Integer.parseInt(HotelUtils.parseDate2Str(beginTime).substring(0, 10).replace("-", "")));
        obj.put("endTimeInt", Integer.parseInt(HotelUtils.parseDate2Str(endTime).substring(0, 10).replace("-", "")));
        obj.put("roomNumId", roomNumId);
        baseData.add(obj);

    }

    public ResponseData searchRegistInfoForRoomInfo(BaseRequest baseRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = baseRequest.getSessionToken();
            TbUserSession tbUserSession = this.getTbUserSession(sessionToken);
            tbUserSession.setHid(baseRequest.getHid());
            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(tbUserSession.getHid());
            registSearch.setState(0);
            List<Regist> regists = registDao.selectBySearch(registSearch);
            JSONObject result = new JSONObject();
            for (int i = 0; i < regists.size(); i++) {
                result.put(regists.get(i).getRoomNumId(), HotelUtils.parseDate2Str(regists.get(i).getCheckoutTime()));
            }

            // 有效预订单
            BookingOrderRoomNumSearch bookingOrderSearch = new BookingOrderRoomNumSearch();
            bookingOrderSearch.setHid(tbUserSession.getHid());
            bookingOrderSearch.setOrderState(1);
            bookingOrderSearch.setIsCheckin(0);
            // 只查  离店时间大于今天的订单  都为有效订单
            ArrayList<Long> checkoutLong = new ArrayList<>();
            checkoutLong.add(new Date().getTime() / 1000);
            bookingOrderSearch.setCheckoutTime(checkoutLong);
            Page<BookingOrderRoomNumForDayView> bookingOrderRoomNumForDayViews = bookingOrderRoomNumDao.selectBySearchforDayView(bookingOrderSearch);
            JSONObject bookData = new JSONObject();
            for (int i = 0; i < bookingOrderRoomNumForDayViews.size(); i++) {
                bookData.put(bookingOrderRoomNumForDayViews.get(i).getRoomNumId(), true);
            }
            responseData.setData(result);
            responseData.setData1(bookData);
        } catch (Exception e) {
            log.error("",e);
        }
        return responseData;
    }

    @Override
    public ResponseData stayHours(StayHoursRequest stayHoursRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            TbUserSession user = this.getTbUserSession(stayHoursRequest);

            if (null == stayHoursRequest.getRegistId()) {
                responseData.setCode(-1);
                return responseData;
            }
            Integer registId = stayHoursRequest.getRegistId();
            Regist regist = registDao.selectById(registId);
            if (null == regist || regist.getState() != 0) {
                responseData.setCode(-1);
                return responseData;
            }
            user.setHid(user.getHid());
            Date checkoutTime = regist.getCheckoutTime();
            if (null == stayHoursRequest.getHours()) {
                responseData.setCode(-1);
                return responseData;
            }

            HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
            hotelBusinessDaySearch.setHid(user.getHid());
            HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);
            user.setBusinessDay(hotelBusinessDay.getBusinessDay());

            // 如果当前mainId 不为空，则先查账务是否存在
            Account account = null;
            if (null != stayHoursRequest.getMainId()) {
                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setHid(regist.getHid());
                accountSearch.setThirdAccoutId(stayHoursRequest.getMainId());
                List<Account> accounts = accountDao.selectBySearch(accountSearch);
                if (accounts.size() > 0) {
                    throw new Exception("已经续住，请勿重复续住");
                }
                String no = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);

                account = new Account();
                account.setAccountId(no);
                account.setPrice(stayHoursRequest.getPayMoney());

                account.setIsSale(0);

                account.setHid(regist.getHid());
                account.setHotelGroupId(regist.getHotelGroupId());

                account.setPayType(2);
                account.setPayClassId(12);
                account.setPayClassName("微信支付");
                account.setPayCodeId("9340");
                account.setPayCodeName("微信公众号支付");

                account.setUintPrice(stayHoursRequest.getPayMoney());
                account.setSaleNum(1);

                account.setRegistState(0);
                account.setIsCancel(0);

                account.setRoomInfoId(regist.getRoomNumId());
                account.setRoomTypeId(regist.getRoomTypeId());
                account.setRoomNum(regist.getRoomNum());
                account.setRegistId(registId);
                account.setBookingId(regist.getBookingOrderId());

                Date date = new Date();

                //用户信息
                account.setCreateTime(date);
                account.setCreateUserId(user.getUserId());
                account.setCreateUserName(user.getUserName());
                account.setUpdateTime(date);
                account.setUpdateUserId(user.getUserId());
                account.setUpdateUserName(user.getUserName());

                account.setClassId(user.getClassId());
                account.setUpdateCalssId(user.getClassId());

                account.setRemark("小程序续费" + stayHoursRequest.getHours() + "小时");

                // 营业日期
                account.setAccountYearMonth(user.getBusinessMonth());
                account.setAccountYear(user.getBusinessYear());
                account.setBusinessDay(user.getBusinessDay());
                account.setThirdAccoutId(stayHoursRequest.getMainId());
                account.setThirdRefundState(0);
                account.setRefundPrice(0);
                account.setThirdRefundState(0);
                account.setAccountType(1);
            }

            //  新的离店时间
            Date nowCheckoutTime = new Date(checkoutTime.getTime() + stayHoursRequest.getHours() * 60 * 60 * 1000);

            // 开始日期结束日期是否为同一天
            Boolean starEndOneDay = false;

            // 开始小时
            int inHours = regist.getCheckinTime().getHours();

            // 结束小时
            int outTours = regist.getCheckoutTime().getHours();

            HashMap<Integer, Boolean> hourStarMap = new HashMap<>();

            HashMap<Integer, Boolean> hourEndMap = new HashMap<>();

            String useStarHourStr = "";
            String useEndHourStr = "";

            // 开始时间
            Integer businessDayMin = HotelUtils.parseDate2Int(regist.getCheckinTime());

            // 结束时间
            Integer businessDayMax = HotelUtils.parseDate2Int(regist.getCheckoutTime());

            //  需要添加的钟点房使用
            ArrayList<HourRoomDayUse> addHourUse = new ArrayList<>();

            // 需要删除的钟点房使用详情
            ArrayList<HourRoomDayUse> upaHourUse = new ArrayList<>();

            HourRoomDayUseSearch hourRoomDayUseSearch = new HourRoomDayUseSearch();

            hourRoomDayUseSearch.setHid(user.getHid());
            hourRoomDayUseSearch.setBusinessDayMax(businessDayMax);
            hourRoomDayUseSearch.setBusinessDayMin(businessDayMin);

            // 每天使用情况
            Page<HourRoomDayUse> hourRoomDayUses = hourRoomDayUseDao.selectBySearch(hourRoomDayUseSearch);
            Map<Integer, List<HourRoomDayUse>> hourRoomMap = new HashMap<>();
            if (hourRoomDayUses.size() > 0) {

                hourRoomMap = hourRoomDayUses.stream().collect(Collectors.groupingBy(HourRoomDayUse::getRoomInfoId));

            }

            if (hourRoomDayUses.size() > 0) {

                hourRoomMap = hourRoomDayUses.stream().collect(Collectors.groupingBy(HourRoomDayUse::getRoomInfoId));

            }

            // 如果不是同一天，则把第二天日期也计算出来。
            if (!businessDayMin.equals(businessDayMax)) {
                starEndOneDay = true;

                for (int i = 0; i <= outTours; i++) {
                    hourEndMap.put(i, true);
                    useEndHourStr += i + ",";
                }
                outTours = 23;
                useEndHourStr = useEndHourStr.substring(0, useEndHourStr.length() - 1);
            }

            for (int i = inHours; i <= outTours; i++) {
                hourStarMap.put(i, true);
                useStarHourStr += i + ",";
            }
            useStarHourStr = useStarHourStr.substring(0, useStarHourStr.length() - 1);


            // 验证当前时段是否被租用
            List<HourRoomDayUse> roomHourUse = hourRoomMap.get(regist.getRoomNumId());

            // 说明当前时间没有
            if (roomHourUse == null || roomHourUse.size() < 1) {

                HourRoomDayUse hourRoomDayUse = getHourRoomDayUse(regist);
                hourRoomDayUse.setBusinessDay(businessDayMin);
                hourRoomDayUse.setUseMsg(useStarHourStr);

                addHourUse.add(hourRoomDayUse);

                // 如果是隔一天
                if (starEndOneDay) {

                    HourRoomDayUse hourRoomDayUseEnd = getHourRoomDayUse(regist);
                    hourRoomDayUseEnd.setBusinessDay(businessDayMax);
                    hourRoomDayUseEnd.setUseMsg(useEndHourStr);

                    addHourUse.add(hourRoomDayUseEnd);

                }

            } else {

                // 验证当天是否存在
                Map<Integer, HourRoomDayUse> hourRoomDayUseMap = roomHourUse.stream().collect(Collectors.toMap(HourRoomDayUse::getBusinessDay, a -> a, (k1, k2) -> k1));

                HourRoomDayUse hourRoomDayUse = hourRoomDayUseMap.get(businessDayMin);
                if (hourRoomDayUse == null) {

                    hourRoomDayUse = getHourRoomDayUse(regist);
                    hourRoomDayUse.setBusinessDay(businessDayMin);
                    hourRoomDayUse.setUseMsg(useStarHourStr);

                    addHourUse.add(hourRoomDayUse);

                } else {


                    String[] split = hourRoomDayUse.getUseMsg().split(",");
                    Arrays.sort(split);

                    Boolean noRoom = false;


                    for (int ion = 0; ion < split.length; ion++) {

                        int i1 = Integer.parseInt(split[ion]);

                        Boolean aBoolean = hourStarMap.get(i1);
                        if (aBoolean != null && aBoolean) {
                            noRoom = true;
                        }
                    }

                    String newUseMsg = hourRoomDayUse.getUseMsg() + "," + useStarHourStr;
                    hourRoomDayUse.setUseMsg(newUseMsg);

                    upaHourUse.add(hourRoomDayUse);

                }

                // 如果是隔一天
                if (starEndOneDay) {

                    HourRoomDayUse hourRoomDayUseEnd = hourRoomDayUseMap.get(businessDayMax);

                    if (hourRoomDayUseEnd == null) {
                        hourRoomDayUseEnd = getHourRoomDayUse(regist);
                        hourRoomDayUseEnd.setBusinessDay(businessDayMax);
                        hourRoomDayUseEnd.setUseMsg(useEndHourStr);

                        addHourUse.add(hourRoomDayUseEnd);
                    } else {

                        String[] split = hourRoomDayUseEnd.getUseMsg().split(",");
                        Arrays.sort(split);

                        Boolean noRoom = false;

                        String errMsg = "";

                        for (int ion = 0; ion < split.length; ion++) {

                            int i1 = Integer.parseInt(split[ion]);

                            Boolean aBoolean = hourStarMap.get(i1);
                            if (aBoolean != null && aBoolean) {
                                noRoom = true;
                                errMsg += split[ion];
                                errMsg += ",";
                            }
                        }

                        String newUseMsg = hourRoomDayUseEnd.getUseMsg() + "," + useEndHourStr;
                        hourRoomDayUseEnd.setUseMsg(newUseMsg);
                        upaHourUse.add(hourRoomDayUse);
                    }

                }
            }

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setRegistId(registId);
            bookingOrderRoomNumSearch.setHid(regist.getHid());
            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            // 修改的预订房型
            BookingOrderRoomNum upaBookRoom = null;
            BookingOrder upaBookOrder = null;
            if (bookingOrderRoomNums.size() > 0) {
                upaBookRoom = bookingOrderRoomNums.get(0);
                upaBookRoom.setCheckoutTime(nowCheckoutTime);

                upaBookOrder = bookingOrderDao.selectById(upaBookRoom.getBookingOrderId());
                upaBookOrder.setCheckoutTime(nowCheckoutTime);
            }


            regist.setCheckoutTime(nowCheckoutTime);

            // 调用事务处理
            wxServiceTransaction.stayHoursTranSaction(regist, account, addHourUse, upaHourUse, upaBookRoom, upaBookOrder);

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    //获取退房时间
                    int diff = (int) (regist.getCheckoutTime().getTime() - new Date().getTime()) / 1000 / 60;
                    //删除任务
                    JSONObject job = new JSONObject();
                    job.put("jobClassName", "com.pms.czabsnight.jobs.OnAccountJob");
                    job.put("jobGroupName", "OnAccountJob" + registId.toString());
                    baseService.delCornJob(job);
                    log.info("删除定时任务----【{}】",job.toString());
                    // 新增任务
                    Map<String, Object> data = new HashMap<>();
                    data.put("registId", registId.toString());
                    data.put("sessionToken", stayHoursRequest.getSessionToken());
                    data.put("reason", "钟点房到期取消");
                    JSONObject jsonObject = JobName.OnAccountJob(diff, data);
                    baseService.addCornJob(jsonObject);
                    log.info("新增定时任务----【{}】",jsonObject.toString());
                }
            });

            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    //如果是智能门锁则调用
                    SmartLockRequest smartLockRequest = new SmartLockRequest();
                    smartLockRequest.setSessionToken(user.getSessionId());
                    smartLockRequest.setCheckinTime(regist.getCheckinTime());
                    smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                    smartLockRequest.setRoomTypeId(regist.getRoomTypeId());
                    smartLockRequest.setRoomTypeName(regist.getRoomTypeName());
                    smartLockRequest.setRoomNumId(regist.getRoomNumId());
                    smartLockRequest.setRoomNum(regist.getRoomNum());
                    smartLockRequest.setHid(regist.getHid());
                    smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                    smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                    smartLockRequest.setCheckinType(regist.getCheckinType());
                    smartLockRequest.setLockNo(regist.getSessionToken());
                    RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                    registPersonSearch.setRegistId(regist.getRegistId());
                    registPersonSearch.setRegistState(0);
                    List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                    List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
                    for (int j = 0; j < registPeople.size(); j++) {
                        RegistPerson registPersonInfo = registPeople.get(j);
                        SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                        BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                        peoplesList.add(registPersonDetail);
                    }
                    smartLockRequest.setPeoples(peoplesList);
                    baseService.SmartLockStayOver(smartLockRequest);
                }
            });


        } catch (Exception e) {
            log.error("",e);
            responseData.setMsg("业务处理异常");
            responseData.setCode(-1);
        }
        return responseData;
    }

    public HourRoomDayUse getHourRoomDayUse(Regist regist) {
        HourRoomDayUse hourRoomDayUse = new HourRoomDayUse();
        hourRoomDayUse.setRoomNo(regist.getRoomNum());
        hourRoomDayUse.setRoomInfoId(regist.getRoomNumId());
        hourRoomDayUse.setRoomTypeId(regist.getRoomTypeId());
        hourRoomDayUse.setHid(regist.getHid());
        hourRoomDayUse.setHotelGroupId(regist.getHotelGroupId());
        return hourRoomDayUse;
    }

    // 会员支付
    @Override
    public ResponseData vipOrderPay(VipOrderPayRequest vipOrderPayRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            TbUserSession user = this.getTbUserSession(vipOrderPayRequest);

            BookingOrder book = bookingOrderDao.selectById(vipOrderPayRequest.getBookingOrderId());
            if (book == null || !book.getHotelGroupId().equals(user.getHotelGroupId())) {
                throw new Exception("订单信息不正确");
            }

            if (vipOrderPayRequest.getMoney() == null || vipOrderPayRequest.getMoney() <= 0) {
                throw new Exception("请输入正确的付款金额");
            }

            Regist regist = new Regist();

            if (vipOrderPayRequest.getType() == 1) {
                Integer payPrice = book.getPayPrice();
                if (payPrice != null && payPrice > 0) {
                    throw new Exception("订单已支付，请误重新支付");
                }
            } else {
                Integer hour = vipOrderPayRequest.getHour();
                if (hour == null || hour < 1) {
                    throw new Exception("续住时长不正确");
                }

                RegistSearch registSearch = new RegistSearch();
                registSearch.setHid(book.getHid());
                registSearch.setBookingOrderId(vipOrderPayRequest.getBookingOrderId());
                registSearch.setState(0);

                List<Regist> regists = registDao.selectBySearch(registSearch);

                if (regists.size() < 1) {
                    throw new Exception("未查到有效的在住信息");
                }
                regist = regists.get(0);

            }


            book.setTotalPrice(vipOrderPayRequest.getMoney());

            user.setHid(book.getHid());

            Integer totalPrice = vipOrderPayRequest.getMoney();

            JSONObject vipParam = new JSONObject();
            vipParam.put("cardId", book.getCardId());
            vipParam.put("type", 1);
            vipParam.put("money", totalPrice);
            vipParam.put(ER.SESSION_TOKEN, vipOrderPayRequest.getSessionToken());

            ResponseData responseData1 = memberService.memberConsumption(vipParam);

            int code = responseData1.getCode();
            if (code < 0) {
                throw new Exception(responseData1.getMsg());
            }

            Object data = responseData1.getData();
            Date date1 = new Date();
            Account account = new Account();
            String accountId = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
            account.setAccountId(accountId);
            account.setHid(user.getHid());
            account.setHotelGroupId(user.getHotelGroupId());
            account.setCreateUserId(user.getUserId());
            account.setCreateUserName(user.getUserName());
            account.setCreateTime(date1);
            account.setUpdateTime(date1);
            account.setUpdateUserId(user.getUserId());
            account.setUpdateUserName(user.getUserName());
            account.setIsCancel(0);
            account.setAccountYear(user.getBusinessYear());
            account.setAccountYearMonth(user.getBusinessMonth());
            account.setBusinessDay(user.getBusinessDay());
            account.setClassId(user.getClassId());
            account.setPrice(totalPrice);
            account.setSettleAccountTime(new Date());
            account.setRegistState(0);
            //消费-付款
            account.setPayType(2);
            account.setSaleNum(1);
            account.setUintPrice(account.getPrice());
            account.setPayClassId(5);
            account.setPayClassName("会员储值");
            account.setPayCodeId("9600");
            account.setPayCodeName("会员储值卡");
            account.setAccountType(1);

            account.setThirdAccoutId(data + "");
            account.setRefundPrice(0);
            account.setRegistPersonId(0);
            account.setRoomNum("");
            account.setRegistPersonName("");
            account.setRegistPersonId(0);
            account.setBookingId(book.getBookingOrderId());


            if (vipOrderPayRequest.getType() == 1) {

                book.setPayType(2);
                book.setPayPrice(totalPrice);
                bookingOrderDao.editBookingOrder(book);
                account.setRemark("小程序预订");
                // vip 支付
                accountDao.saveAccount(account);

            } else {

                bookingOrderDao.editBookingOrder(book);
                account.setRemark("小程序续住:" + vipOrderPayRequest.getHour() + "小时");
                account.setRegistId(regist.getRegistId());
                account.setRoomTypeId(regist.getRoomTypeId());
                account.setRoomInfoId(regist.getRoomNumId());
                account.setRoomNum(regist.getRoomNum());
                // vip 支付
                accountDao.saveAccount(account);


                // 查询在住订单
                StayHoursRequest stayHoursRequest = new StayHoursRequest();
                stayHoursRequest.setSessionToken(vipOrderPayRequest.getSessionToken());
                stayHoursRequest.setHours(vipOrderPayRequest.getHour());
                stayHoursRequest.setRegistId(regist.getRegistId());

                return this.stayHours(stayHoursRequest);

            }


        } catch (Exception e) {
            log.error("",e);
            responseData.setData(e.getMessage());
            responseData.setResult(ER.ERR);
        }
        return responseData;
    }
}
