package com.pms.czabsorders.service.machine.impl;

import com.pms.czabsorders.service.machine.LvyunInterfaceService;
import com.pms.czabsorders.service.machine.MachineRegistService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czaccount.service.alipay.AliPayService;
import com.pms.czaccount.service.wechat.WeChatPayService;
import com.pms.czhotelfoundation.service.hotelsetting.HotelSettingService;
import com.pms.czhotelfoundation.service.zimg.ZimgService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.ZimgUploadUtil;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.ERROR_MSG;
import com.pms.czpmsutils.constant.ET;
import com.pms.czpmsutils.constant.hotelsetting.HOTEL_SETTING;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.regist.REGI;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistCheckout;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.RegistStayover;
import com.pms.pmsorder.bean.search.RegistPersonSearch;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.dao.RegistCheckoutDao;
import com.pms.pmsorder.dao.RegistDao;
import com.pms.pmsorder.dao.RegistPersonDao;
import com.pms.pmsorder.dao.RegistStayoverDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.net.URLEncoder;
import java.util.*;

@Service
@Slf4j
public class MachineRegistServiceImpl extends BaseService implements MachineRegistService {

    /**返回结果的状态  SUCC/ERR 值:{@value}*/
    public  String RES = "Result";

    public  String SUCC = "Success";

    public  String ERR = "Failed";

    /**返回结果提示信息 可返回错误码或提示信息 值:@{value}*/
    public  String MSG = "Msg";

    @Autowired
    private RegistDao registDao;

    @Autowired
    private ZimgService zimgService;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private RegistStayoverDao registStayoverDao;

    @Autowired
    private RegistCheckoutDao registCheckoutDao;

    @Autowired
    private WeChatPayService weChatPayService;

    @Autowired
    private AliPayService aliPayService;

    @Autowired
    private HotelSettingService hotelSettingService;

    @Autowired
    private LvyunInterfaceService lvyunInterfaceService;

    @Override
    @Transactional
    public Map<String, Object> machineAddRegistForOtherPms(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(RES, SUCC);

        try {

            /*获取登录信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Integer integer = HotelUtils.parseDate2Int(new Date());
            user.setBusinessDay(integer);
            /*获取操作日志记录*/
            Oprecord oprecord = new Oprecord(user);
            ArrayList<Oprecord> oprecords = new ArrayList<>();

            /*支付状态 0:未支付 1:已支付 */
            int payType = param.getInt("payType");


            /**
             * 1.保存房单信息
             */
            JSONObject registMsg = param.getJSONObject(REGI.REGIST_MSG);

            Regist regist = new Regist();
            if(registMsg.get("Memo")!=null){
                regist.setRemark(registMsg.getString("Memo"));
            }
            regist.setRoomRateCodeName(registMsg.getString("PriceCode"));
            regist.setCheckinTime(new Date());
            regist.setCheckoutTime(HotelUtils.parseStr2Date(registMsg.getString("EndTime")));
            regist.setDayCount(registMsg.getInt("DayNum"));
            regist.setRoomTypeName(registMsg.get("RoomTypeID").toString());
            regist.setCheckinType(registMsg.getInt("CheckInType"));
            regist.setRoomNum(registMsg.getString("RoomNo"));
            if(registMsg.get("OrderID")!=null){
                regist.setOtherPmsOrderId(registMsg.getString("OrderID"));
            }
            if(registMsg.get("RegistID")!=null){
                regist.setOtherPmsRegistId(registMsg.getString("Regist" +
                        "ID"));
            }
            if(registMsg.get("vipNo")!=null){
                regist.setMemberCard(registMsg.getString("vipNo"));
            }

            regist.setMacRegist(1);
            regist.setMacContinue(0);
            regist.setMacCheckout(0);
            regist.setUpdateTime(new Date());
            regist.setClassId(user.getClassId());
            regist.setBusinessDay(user.getBusinessDay());
            regist.setRegistYear(user.getBusinessYear());
            regist.setRegistYearMonth(user.getBusinessMonth());
            regist.setState(payType==0?3:0);
            regist.setCheckinMode(1);
            regist.setMacRegist(1);
            regist.setBusinessDay(user.getBusinessDay());
            if(payType<0){
                regist.setOtherPmsRegistId("-1");
            }
            Object errorCode = param.get("errorCode");
            if(errorCode!=null&&!"".equals(errorCode.toString())){
                regist.setRemark(errorCode+"");
            }

            regist.setHid(user.getHid());
            String sn = HotelUtils.getHIDUUID32("R", user.getHid());
            regist.setSn(sn);
            regist.setCreateUserId(user.getUserId());
            boolean addRegistValue = registDao.insert(regist) > 0;
            if(!addRegistValue){
                throw new Exception(ERROR_MSG.REGIST_SAVE_ERR);
            }

            oprecord.setRegistId(regist.getRegistId());
            oprecord.setMainId(sn);
            oprecord.setRoomNum(regist.getRoomNum());
            oprecord.setDescription("保存登记信息成功");
            oprecords.add(oprecord);

            resultMap.put("registId",regist.getRegistId());

            /**
             * 2.入住人信息
             *
             */
            JSONArray guestList = registMsg.getJSONArray(REGI.GUEST_LIST);

            /*用来返回每个入住人的主键，身份证号为key,personId为value*/
            JSONObject registPersonMap = new JSONObject();

            /**
             * 上传图片信息
             */
            JSONObject imageData = new JSONObject();
            imageData.put(ER.SESSION_TOKEN,sessionToken);

            StringBuffer prName = new StringBuffer();

            for (int i = 0;i<guestList.size();i++){

                JSONObject guest = guestList.getJSONObject(i);

                RegistPerson person = new RegistPerson();
                person.setIdCode(guest.getString("Code"));
                if(guest.get("Birthday")!=null&&!"".equals(guest.getString("Birthday"))){
                    int birthday = Integer.parseInt(guest.getString("Birthday").replace("-", ""));
                    person.setBirthday(birthday);
                }
                person.setRegistId(regist.getRegistId());
                person.setIdType(1);
                person.setSex(0);
                if(guest.get("Sex")!=null){
                    person.setSex(guest.getString("Sex").endsWith("男")?0:1);
                }
                person.setPersonName(guest.getString("Name"));
                person.setIsOther(i==0?0:1);
                person.setHid(user.getHid());
                person.setAddress(guest.getString("Address"));
                String nation = guest.getString("Nation");
                if(nation.indexOf("族")<1){
                    nation+="族";
                }

                try {
                    person.setNation(HotelUtils.nationMap.getInt(nation));
                }catch (Exception e){
                    person.setNation(97);
                }

                if(registMsg.get("Phone")!=null&&i==0){
                    person.setPhone(registMsg.getString("Phone"));
                }

                /**
                 * 证件照,老版本 Image
                 */
                if(guest.get("Photo")!=null&&!"".equals(guest.getString("Photo"))){
                    imageData.put("data",guest.getString("Photo"));
                    String s = zimgService.uplaodImage(imageData);
                    person.setIdImage(ZimgUploadUtil.ZIMGUPLOADURL+"/"+s);
                }



                /**
                 * 身份证照  老版本
                 */
                if(guest.get("CameraPhoto")!=null&&!"".equals(guest.getString("CameraPhoto"))){
                    imageData.put("data",guest.getString("CameraPhoto"));
                    String s = zimgService.uplaodImage(imageData);
                    person.setCameraImage(ZimgUploadUtil.ZIMGUPLOADURL+"/"+s);
                }

                /**
                 * 身份证照  新版本
                 */
                if(guest.get("Image")!=null&&!"".equals(guest.getString("Image"))){
                    imageData.put("data",guest.getString("Image"));
                    String s = zimgService.uplaodImage(imageData);
                    person.setIdImage(ZimgUploadUtil.ZIMGUPLOADURL+"/"+s);
                }

                /**
                 * 证件照,新版本
                 */
                if(guest.get("CameraPicture")!=null&&!"".equals(guest.getString("CameraPicture"))){
                    imageData.put("data",guest.getString("CameraPicture"));
                    String s = zimgService.uplaodImage(imageData);
                    person.setCameraImage(ZimgUploadUtil.ZIMGUPLOADURL+"/"+s);
                }

                /**
                 * 相似度   0~1 之间  Similarity，相似度
                 */
                if(guest.get("Semblance")!=null&&!"".equals(guest.getString("Semblance"))){
                    person.setConfidence(guest.getString("Semblance"));
                }

                /**
                 * 成功或失败  0-失败 1-成功
                 */
                if(guest.get("FaceResult")!=null&&!"".equals(guest.getString("FaceResult"))){
                    try {
                        person.setFaceResult(guest.getInt("FaceResult"));
                    }
                    catch(Exception ex){
                        person.setFaceResult(0);
                    }
                }


                /**
                 * 公安网上传状态
                 */
                if(guest.get("GuestStatus")!=null&&!"".equals(guest.getString("GuestStatus"))){
                    try {
                        person.setGuestType(guest.getInt("GuestStatus"));
                    }
                    catch(Exception ex){
                        person.setFaceResult(0);
                    }
                }


                /**
                 * 公安网流水号
                 */
                if(guest.get("GuestNo")!=null&&!"".equals(guest.getString("GuestNo"))){
                    person.setGuestNo(guest.getString("GuestNo"));
                }

                /**
                 * 公安网唯一标识
                 */
                if(guest.get("GuestId")!=null&&!"".equals(guest.getString("GuestId"))){
                    person.setGuestId(guest.getString("GuestId"));
                }

                registPersonDao.insert(person);

                registPersonMap.put(guest.get("Code"),person.getRegistPersonId());

                prName.append(person.getPersonName());
                prName.append("，");


            }

            resultMap.put("guestMsg",registPersonMap);

            /**
             * 3.添加账务信息
             */

            //续住的信息
            RegistStayover registStayover = new RegistStayover();
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(HotelUtils.parseDate2Str(regist.getCheckinTime()).substring(0,10), HotelUtils.parseDate2Str(regist.getCheckoutTime()).substring(0,10));
            int dayCount = allDayListBetweenDate.size() ;

            JSONObject accountMsg = param.getJSONObject(REGI.ACCOUNT_MSG);

            resultMap.put("accountId","");
            if(accountMsg!=null&&!"null".equals(accountMsg.toString())&&accountMsg.size()>0){


                String a = HotelUtils.getHIDUUID32("A", user.getHid());


                int pt = accountMsg.getInt("PayType");

                Account account = new Account();
                account = getPayTypeMsg(account,pt);
                account.setAccountId(a);
                account.setHid(user.getHid());
                account.setCreateUserId(user.getUserId());
                account.setCreateUserName(user.getUserName());
                account.setCreateTime(new Date());
                account.setIsCancel(0);
                account.setAccountYear(user.getBusinessYear());
                account.setAccountYearMonth(user.getBusinessMonth());
                account.setClassId(user.getClassId());
                int price = (int)(accountMsg.getDouble("Money")*100);
                account.setPrice(price);
                account.setUintPrice(price);
                account.setPayType(2);
                account.setSaleNum(1);
                account.setRegistId(regist.getRegistId());
                account.setThirdRefundState(0);
                account.setRefundPrice(0);
                account.setRegistState(regist.getState());
                account.setThirdAccoutId(accountMsg.getString("TradeNo"));
                account.setUpdateTime(new Date());
                account.setUpdateUserId(user.getUserId());
                account.setUpdateUserName(user.getUserName());
                account.setBusinessDay(user.getBusinessDay());
                account.setAccountYear(user.getBusinessYear());
                account.setAccountYearMonth(user.getBusinessMonth());
                if(accountMsg.get("otherPmsAccountId")!=null){
                    account.setOtherPmsAccountId(accountMsg.getString("otherPmsAccountId"));
                }

                account.setIsCancel(payType==0?1:0);

                if(payType!=0){
                    Oprecord op = new Oprecord(user);
                    op.setRegistId(regist.getRegistId());
                    op.setMainId(sn);
                    op.setOccurTime(HotelUtils.currentTime());
                    op.setDescription(account.getPayCodeName()+":"+accountMsg.getDouble("Money")+"元");
                    oprecords.add(op);
                }

                //续住总价和平均房价
                if(dayCount==0){
                    registStayover.setNprice(price);
                } else {
                    registStayover.setNprice(price/dayCount);
                }

                registStayover.setTotalPrice(price);

                accountDao.saveAccount(account);

                resultMap.put("accountId",account.getAccountId());

            }

            /**
             * 判断是否是前台开房 自助机续住
             *
             * 1.是
             * 其他 否
             */
            Object stayTypeObj = param.get("stayType");
            if(stayTypeObj!=null&&!"".equals(stayTypeObj)){
                int stayType = Integer.parseInt(stayTypeObj.toString());
                if(stayType==1){

                    registStayover.setBookingOrderId(regist.getBookingOrderId());
                    registStayover.setRegistId(regist.getRegistId());


                    registStayover.setDayCount(dayCount);
                    registStayover.setStartTime(regist.getCheckinTime());
                    registStayover.setEndTime(regist.getCheckoutTime());
                    registStayover.setDescRemark("通过自助机续住");
                    registStayover.setState(1);
                    registStayover.setRoomNum(regist.getRoomNum());
                    registStayover.setRoomTypeId(regist.getRoomTypeId());
                    registStayover.setRoomTypeName(regist.getRoomTypeName());
                    registStayover.setHid(user.getHid());
                    registStayover.setHotelGroupId(user.getHotelGroupId());
                    registStayover.setClassId(user.getClassId());
                    registStayover.setUpdateTime(new Date());
                    registStayover.setCreateUserId(user.getUserId());
                    registStayover.setUpdateTime(new Date());
                    registStayover.setUpdateUserId(user.getUserId());
                    registStayover.setUpdateUserName(user.getUserName());
                    registStayover.setTypeState(0);
                    registStayoverDao.insert(registStayover);
                }
            }


            /**
             * 判断 是否是前台开房 自助机结账
             * 1 是
             * 其他 否
             */
            Object addCheckoutMsgObj = param.get("addCheckoutMsg");
            if(addCheckoutMsgObj!=null&&!"".equals(addCheckoutMsgObj.toString())){

                int addCheckoutMsg = Integer.parseInt(addCheckoutMsgObj.toString());

                RegistCheckout registCheckout = new RegistCheckout();
                registCheckout.setRegistId(regist.getRegistId());
                registCheckout.setHid(user.getHid());
                registCheckout.setHotelGroupId(user.getHotelGroupId());
                registCheckout.setState(0);
                registCheckout.setRoomNum(regist.getRoomNum());
                registCheckout.setRoomTypeId(regist.getRoomTypeId());
                registCheckout.setRoomTypeName(regist.getRoomTypeName());
                registCheckout.setCheckoutType(1);

                registCheckoutDao.insert(registCheckout);
            }

            this.addOprecords(oprecords);

            StringBuffer sb = new StringBuffer();
            sb.append("hid=");
            sb.append(user.getHid());
            sb.append("&type=2");

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("roomNum",regist.getRoomNum());
            jsonObject.put("name",prName.toString());
            String encode = URLEncoder.encode(jsonObject.toString(), "utf-8");
            sb.append("&content="+encode);

            HotelUtils.pushForHid(sb.toString());

            return resultMap;

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            resultMap.put(RES, ERR);
            resultMap.put(MSG, e.getMessage());
        }

        return  resultMap;
    }

    /**
     * 第三方PMS添加账务信息
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> machineAddAccountForOtherPms(JSONObject param) {

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(RES, SUCC);

        try {

            /*获取登录信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Integer integer = HotelUtils.parseDate2Int(new Date());
            user.setBusinessDay(integer);
            /*获取操作日志记录*/
            Oprecord oprecord = new Oprecord(user);
            ArrayList<Oprecord> oprecords = new ArrayList<>();

            /**
             * 1.查询房单信息 ，并修改离店时间
             */
            String otherPmsRegistId = param.getString("otherPmsRegistId");

            RegistSearch search = new RegistSearch();
            search.setHid(user.getHid());
            search.setOtherPmsRegistId(otherPmsRegistId);
            List<Regist> regists = registDao.selectBySearch(search);
            /**
             * 是否是PMS入住自助机续住
             *  0：否
             *  1：是
             */
            param.put("stayType",0);
            Regist regist = new Regist();

            if(regists.size()<1){
                // throw new Exception("未查到当前登记的房单信息");


                /**
                 * 1.保存房单信息
                 */
                JSONObject registMsg = param.getJSONObject(REGI.REGIST_MSG);

                if(registMsg.get("Memo")!=null){
                    regist.setRemark(registMsg.getString("Memo"));
                }
                regist.setRoomRateCodeName(registMsg.getString("PriceCode"));
                regist.setCheckinTime(new Date());
                regist.setCheckoutTime(HotelUtils.parseStr2Date(registMsg.getString("EndTime")));
                regist.setDayCount(registMsg.getInt("DayNum"));
                if(registMsg.get("RoomTypeName")!=null){
                    regist.setRoomTypeName(registMsg.get("RoomTypeName").toString());
                }else {
                    regist.setRoomTypeName(registMsg.get("RoomTypeID").toString());
                }
                if(registMsg.get("CheckInType")!=null&&!"".equals(registMsg.getString("CheckInType"))){
                    regist.setCheckinType(registMsg.getInt("CheckInType"));
                }else {
                    regist.setCheckinType(1);
                }
                regist.setRoomNum(registMsg.getString("RoomNo"));
                regist.setOtherPmsOrderId(otherPmsRegistId);
                if(registMsg.get("OrderID")!=null){
                    regist.setOtherPmsOrderId(registMsg.getString("OrderID"));
                }
                if(registMsg.get("vipNo")!=null){
                    regist.setMemberCard(registMsg.getString("vipNo"));
                }
                regist.setMacRegist(0);
                regist.setMacContinue(1);
                regist.setMacCheckout(0);
                regist.setUpdateTime(new Date());
                regist.setClassId(user.getClassId());
                regist.setBusinessDay(user.getBusinessDay());
                regist.setRegistYear(user.getBusinessYear());
                regist.setRegistYearMonth(user.getBusinessMonth());
                regist.setState(0);
                regist.setCheckinMode(1);
                regist.setMacRegist(1);
                Object errorCode = param.get("errorCode");
                if(errorCode!=null&&!"".equals(errorCode.toString())){
                    regist.setRemark(errorCode+"");
                }

                regist.setHid(user.getHid());
                String sn = HotelUtils.getHIDUUID32("R", user.getHid());
                regist.setSn(sn);
                regist.setCreateUserId(user.getUserId());
                boolean addRegistValue = registDao.insert(regist) > 0;

                if(!addRegistValue){
                    throw new Exception(ERROR_MSG.REGIST_SAVE_ERR);
                }

                JSONArray guestList = registMsg.getJSONArray("GuestList");

                for (int i = 0; i<guestList.size();i++){

                    JSONObject guest = guestList.getJSONObject(i);

                    RegistPerson registPerson = new RegistPerson();
                    registPerson.setRegistId(regist.getRegistId());
                    registPerson.setPersonName(guest.getString("Name"));
                    registPerson.setIdCode(guest.getString("Code"));
                    registPerson.setBirthday(guest.getInt("Birthday"));
                    registPerson.setPhone(guest.getString("Phone"));
                    registPerson.setSex(guest.getString("Sex").equals("女性")?0:1);
                    if(i>0){
                        registPerson.setIsOther(1);
                    }else {
                        registPerson.setIsOther(0);
                    }

                    registPersonDao.insert(registPerson);

                }

            }else {

                regist = regists.get(0);
                regist.setMacContinue(1);

                registDao.update(regist);

            }

            /**
             * 账务信息
             *  price 代表总支付费用
             */
            JSONObject accountMsg = param.getJSONObject(REGI.ACCOUNT_MSG);
            int price = (int)(accountMsg.getDouble("Money")*100);




            if(param.get("checkOutTime")!=null&&!param.getString("checkOutTime").equals("")){

                Date oldCheckOutTime = regist.getCheckoutTime();

                regist.setCheckoutTime(HotelUtils.parseStr2Date(param.getString("checkOutTime")));

                registDao.update(regist);

                oprecord.setRegistId(regist.getRegistId());
                oprecord.setOccurTime(HotelUtils.currentTime());
                oprecord.setDescription(user.getUserName()+"：修改离店时间为:"+param.getString("checkOutTime"));

                oprecords.add(oprecord);

                /**
                 * 添加续住信息
                 */
                RegistStayover registStayover = new RegistStayover();
                registStayover.setBookingOrderId(regist.getBookingOrderId());
                registStayover.setRegistId(regist.getRegistId());

                int dayCount = 1;

                registStayover.setRoomNum(regist.getRoomNum());
                registStayover.setRoomTypeId(regist.getRoomTypeId());
                registStayover.setRoomTypeName(regist.getRoomTypeName());
                registStayover.setDayCount(dayCount);
                registStayover.setStartTime(oldCheckOutTime);
                registStayover.setEndTime(regist.getCheckoutTime());
                registStayover.setDescRemark("通过自助机续住");
                registStayover.setState(1);
                registStayover.setNprice(price/dayCount);
                registStayover.setTotalPrice(price);
                registStayover.setHid(user.getHid());
                registStayover.setHotelGroupId(user.getHotelGroupId());
                registStayover.setBusinessDay(user.getBusinessDay());
                registStayover.setBusinessYear(user.getBusinessYear());
                registStayover.setBusinessMonth(user.getBusinessMonth());
                registStayover.setClassId(user.getClassId());
                registStayover.setUpdateTime(new Date());
                registStayover.setCreateUserId(user.getUserId());
                registStayover.setUpdateTime(new Date());
                registStayover.setUpdateUserId(user.getUserId());
                registStayover.setUpdateUserName(user.getUserName());
                registStayover.setTypeState(0);
                registStayoverDao.insert(registStayover);
            }

            /**
             * 2.添加账务信息
             */

            String a = HotelUtils.getHIDUUID32("A", user.getHid());

            int pt = accountMsg.getInt("PayType");

            Account account = new Account();
            account = getPayTypeMsg(account,pt);
            account.setAccountId(a);
            account.setPayType(2);
            account.setHid(user.getHid());
            account.setCreateUserId(user.getUserId());
            account.setCreateUserName(user.getUserName());
            account.setCreateTime(new Date());
            account.setIsCancel(0);
            account.setAccountYear(user.getBusinessYear());
            account.setAccountYearMonth(user.getBusinessMonth());
            account.setClassId(user.getClassId());

            account.setPrice(price);
            account.setUintPrice(price);
            account.setSaleNum(1);
            account.setRegistId(regist.getRegistId());
            account.setThirdRefundState(0);
            account.setRefundPrice(0);
            account.setRegistState(regist.getState());
            account.setThirdAccoutId(accountMsg.getString("TradeNo"));
            account.setUpdateTime(new Date());
            account.setUpdateUserId(user.getUserId());
            account.setUpdateUserName(user.getUserName());
            account.setBusinessDay(user.getBusinessDay());
            account.setAccountYear(user.getBusinessYear());
            account.setAccountYearMonth(user.getBusinessMonth());
            if(accountMsg.get("otherPmsAccountId")!=null){
                account.setOtherPmsAccountId(accountMsg.getString("otherPmsAccountId"));
            }

            accountDao.saveAccount(account);

            Oprecord op = new Oprecord(user);
            op.setRegistId(regist.getRegistId());
            op.setOccurTime(HotelUtils.currentTime());
            op.setDescription(user.getUserName()+"：添加账务:"+account.getPayCodeName()+":"+account.getPrice()+"元。");
            oprecords.add(op);

            resultMap.put("accountId",a);

            StringBuffer sb = new StringBuffer();
            sb.append("hid=");
            sb.append(user.getHid());
            sb.append("&type=2");

            HotelUtils.pushForHid(sb.toString());

            this.addOprecords(oprecords);

            return resultMap;

        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return  resultMap;
    }

    @Override
    public Map<String, Object> checkOutForOtherPms(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(RES, SUCC);

        try {

            /*获取登录信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Integer integer = HotelUtils.parseDate2Int(new Date());
            user.setBusinessDay(integer);
            /*获取操作日志记录*/
            Oprecord oprecord = new Oprecord(user);
            ArrayList<Oprecord> oprecords = new ArrayList<>();

            /**
             * 1.查询房单信息 ，修改房单状态未预结
             */
            String otherPmsRegistId = param.getString("otherPmsRegistId");

            RegistSearch search = new RegistSearch();
            search.setHid(user.getHid());
            search.setOtherPmsRegistId(otherPmsRegistId);
            search.setState(0);
            List<Regist> regists = registDao.selectBySearch(search);

            /**
             * 如果查不到原房单信息，则重新添加
             */
            Regist regist = new Regist();
            if(regists.size()<1){
                // throw new Exception("未查到当前登记的房单信息");
                /**
                 * 1.保存房单信息
                 */
                JSONObject registMsg = param.getJSONObject(REGI.REGIST_MSG);;

                if(registMsg.get("Memo")!=null){
                    regist.setRemark(registMsg.getString("Memo"));
                }
                regist.setRoomRateCodeName(registMsg.getString("PriceCode"));
                regist.setCheckinTime(HotelUtils.parseStr2Date(registMsg.getString("BeginTime")));
                regist.setCheckoutTime(HotelUtils.parseStr2Date(registMsg.getString("EndTime")));
                if(registMsg.get("DayNum")==null){
                    int size = HotelUtils.getAllDayListBetweenDate(registMsg.getString("BeginTime"), registMsg.getString("EndTime")).size();
                    regist.setDayCount(size);
                }else {
                    regist.setDayCount(registMsg.getInt("DayNum"));
                }

                if(registMsg.get("RoomType")!=null){
                    regist.setRoomTypeName(registMsg.get("RoomType").toString());
                }else {
                    regist.setRoomTypeName(registMsg.get("RoomTypeID").toString());
                }
                if(registMsg.get("CheckInType")!=null&&!"".equals(registMsg.getString("CheckInType"))){
                    regist.setCheckinType(registMsg.getInt("CheckInType"));
                }else {
                    regist.setCheckinType(1);
                }
                regist.setRoomNum(registMsg.getString("RoomNo"));
                regist.setOtherPmsOrderId(otherPmsRegistId);
                if(registMsg.get("OrderID")!=null){
                    regist.setOtherPmsOrderId(registMsg.getString("OrderID"));
                }
                if(registMsg.get("vipNo")!=null){
                    regist.setMemberCard(registMsg.getString("vipNo"));
                }
                regist.setUpdateTime(new Date());
                regist.setClassId(user.getClassId());
                regist.setBusinessDay(user.getBusinessDay());
                regist.setMacContinue(0);
                regist.setMacRegist(0);
                regist.setRegistYear(user.getBusinessYear());
                regist.setRegistYearMonth(user.getBusinessMonth());
                regist.setState(1);
                regist.setCheckinMode(1);
                regist.setMacRegist(1);
                Object errorCode = param.get("errorCode");
                if(errorCode!=null&&!"".equals(errorCode.toString())){
                    regist.setRemark(errorCode+"");
                }
                regist.setMacCheckout(1);
                regist.setHid(user.getHid());
                String sn = HotelUtils.getHIDUUID32("R", user.getHid());
                regist.setSn(sn);
                regist.setCreateUserId(user.getUserId());
                boolean addRegistValue = registDao.insert(regist) > 0;

                if(!addRegistValue){
                    throw new Exception(ERROR_MSG.REGIST_SAVE_ERR);
                }

                JSONArray guestList = registMsg.getJSONArray("GuestList");

                for (int i = 0; i<guestList.size();i++){

                    JSONObject guest = guestList.getJSONObject(i);

                    RegistPerson registPerson = new RegistPerson();
                    registPerson.setRegistId(regist.getRegistId());
                    registPerson.setHid(user.getHid());
                    registPerson.setPersonName(guest.getString("Name"));
                    registPerson.setIdCode(guest.getString("Code"));
                    if(guest.get("Birthday")!=null&&!guest.getString("Birthday").equals("")){
                        registPerson.setBirthday(guest.getInt("Birthday"));
                    }
                    registPerson.setPhone(guest.getString("Phone"));
                    registPerson.setSex(guest.getString("Sex").equals("女性")?0:1);
                    if(i>0){
                        registPerson.setIsOther(1);
                    }else {
                        registPerson.setIsOther(0);
                    }

                    registPersonDao.insert(registPerson);

                }

            }else {

                regist = regists.get(0);
                regist.setMacCheckout(1);

                registDao.update(regist);

            }

            regist.setCheckoutTime(new Date());
            regist.setCheckoutBusinessDay(user.getBusinessDay());
            regist.setCheckoutClassId(user.getClassId());
            regist.setCheckoutOperator(user.getUserName());
            regist.setState(ET.STATUS_PRE_END);
            regist.setCheckoutTime(new Date());

            registDao.update(regist);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription(user.getUserName()+"：申请预结。");
            oprecords.add(oprecord);



            StringBuffer sb = new StringBuffer();
            sb.append("hid=");
            sb.append(user.getHid());
            sb.append("&type=3");      //预结

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("roomNum",regist.getRoomNum());
            /**
             * 查询在住客人信息
             */
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setHid(user.getHid());
            registPersonSearch.setRegistId(regist.getRegistId());
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);

            StringBuffer prName = new StringBuffer();

            for (RegistPerson registPerson:registPeople){
                prName.append(registPerson.getPersonName());
                prName.append("，");
            }
            jsonObject.put("name",prName.toString());
            String encode = URLEncoder.encode(jsonObject.toString(), "utf-8");
            sb.append("&content="+encode);

            HotelUtils.pushForHid(sb.toString());

           this.addOprecords(oprecords);

            /**
             * 4.添加退房记录
             */
            RegistCheckout registCheckout = new RegistCheckout();
            registCheckout.setRegistId(regist.getRegistId());
            registCheckout.setHid(user.getHid());
            registCheckout.setHotelGroupId(user.getHotelGroupId());
            registCheckout.setState(0);
            registCheckout.setRoomNum(regist.getRoomNum());
            registCheckout.setRoomTypeId(regist.getRoomTypeId());
            registCheckout.setRoomTypeName(regist.getRoomTypeName());
            registCheckout.setCheckoutType(0);
            registCheckout.setCreateTime(new Date());
            registCheckout.setBusinessDay(user.getBusinessDay());
            registCheckout.setBusinessMonth(user.getBusinessMonth());
            registCheckout.setBusinessDay(user.getBusinessDay());
            registCheckout.setClassId(user.getClassId());
            registCheckout.setCreateUserId(user.getUserId());
            registCheckout.setCreateUserName(user.getUserName());
            registCheckoutDao.insert(registCheckout);

            return resultMap;

        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return  resultMap;
    }

    @Override
    public Map<String, Object> refundMoneyForOtherPmsAndPush(final JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(RES, SUCC);

        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            Integer integer = HotelUtils.parseDate2Int(new Date());
            user.setBusinessDay(integer);
            /*获取操作日志记录*/
            Oprecord oprecord = new Oprecord(user);
            ArrayList<Oprecord> oprecords = new ArrayList<>();

            //退款金额
            int refundMoney = param.getInt("refundMoney");

            /**
             * 2.查询账务信息
             */
            final Account account = accountDao.selectById(param.getString("accountId"));
            if(account==null){
                throw new Exception("未查到账务信息");
            }
            oprecord.setRegistId(account.getRegistId());
            oprecord.setMainId(account.getAccountId());

            String a = HotelUtils.getHIDUUID32("A", user.getHid());
            Account addRefund = new Account();
            addRefund.setAccountId(a);
            addRefund.setPayType(2);
            addRefund.setRegistId(account.getRegistId());
            addRefund.setBookingId(account.getBookingId());
            addRefund.setBusinessDay(user.getBusinessDay());
            addRefund.setOtherPmsAccountId(account.getAccountId());
            addRefund.setIsCancel(0);
            addRefund.setPayClassName(account.getPayClassName());
            addRefund.setPayClassId(account.getPayClassId());
            addRefund.setUpdateUserName(user.getUserName());
            addRefund.setRegistState(account.getRegistState());
            addRefund.setUpdateTime(new Date());
            addRefund.setUpdateUserName(user.getUserName());
            addRefund.setHid(account.getHid());
            addRefund.setHotelGroupId(account.getHotelGroupId());
            addRefund.setSaleNum(1);
            addRefund.setPrice(refundMoney*-1);
            addRefund.setUintPrice(refundMoney*-1);
            addRefund.setBusinessDay(user.getBusinessDay());
            addRefund.setAccountYear(user.getBusinessYear());
            addRefund.setAccountYearMonth(user.getBusinessMonth());
            addRefund.setCreateTime(new Date());
            addRefund.setCreateUserName(user.getUserName());
            addRefund.setRoomNum(account.getRoomNum());


            Map<String, Object> refundMap = new HashMap<>();

            HashMap<String, Object> refundParam = new HashMap<>();

            refundParam.put(ER.SESSION_TOKEN,sessionToken);
            refundParam.put("mainId",account.getThirdAccoutId());
            refundParam.put("refundMoney",refundMoney);


            // 9320 微信
            if(account.getPayCodeId().equals("9320")){

                oprecord.setDescription("微信退款:"+refundMoney/100.0+"元。");
                oprecords.add(oprecord);

                addRefund.setPayCodeId("9329");
                addRefund.setPayCodeName("微信退款");

                refundMap = weChatPayService.wechatRefund(refundParam);

            }else {

                oprecord.setDescription("支付宝退款:"+refundMoney/100.0+"元。");
                oprecords.add(oprecord);

                addRefund.setPayCodeId("9309");
                addRefund.setPayCodeName("支付宝退款");

                refundMap = aliPayService.alipayRefund(refundParam);

            }

            /**
             * 退款失败返回错误信息
             */
            if(refundMap.get(ER.RES).toString().equals(ER.ERR)){
                throw new Exception(refundMap.get(ER.MSG).toString());
            }

            /**
             * 退款成功进行入账
             */
            addRefund.setThirdRefundState(1);
            addRefund.setThirdAccoutId(refundMap.get("refundId")+"");
            accountDao.saveAccount(addRefund);

            /**
             * 修改原账务信息
             */
            account.setThirdRefundState(1);
            account.setRefundPrice(refundMoney*-1);
            accountDao.editAccount(account);

            this.addOprecords(oprecords);

            param.put("registId",account.getRegistId());
            param.put("accountId",account.getAccountId());
            /**
             * 查看是否需要向PMS推送数据*/

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {


            /*  获取设置信息，查看是否需要推送数据*/

                    try {
                        ResponseData allSetting = hotelSettingService.getAllSetting(param);
                        JSONObject jsonObject = JSONObject.fromObject(allSetting.getData());
                        JSONObject setJson = JSONObject.fromObject(jsonObject.get("json"));

                        int paramValue = setJson.getJSONObject(HOTEL_SETTING.REFUND_SEND_PMS).getInt("paramValue");

                        switch (paramValue){
                            case 1:
                               //  xiRuanService.refundForXiRuanPms(param);
                                break;
                            case 2:
                                lvyunInterfaceService.addAccount(param);
                                break;

                            default:
                                break;
                        }
                    } catch (Exception e){
                        log.error("",e);
                    }

                }
            });

            return resultMap;

        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return  resultMap;
    }

    /**
     * 根据第三方的PMS账单号查询PMS的房单号和可用的账务信息
     * @return
     */
    @Override
    public Map<String, Object> findRegistAndCanRefundAccountByOtherPmsId(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(RES, SUCC);
        try {

            /**
             * 1.获取登录信息
             */
            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /**
             * 2.查询上传人信息
             */
            String registID = param.getString("registId");
            RegistSearch registSearch = new RegistSearch();
            registSearch.setOtherPmsRegistId(registID);
            List<Regist> regists = registDao.selectBySearch(registSearch);

            if(regists==null||regists.size()<1){
                throw new Exception("RegistID有误未查到相应的信息");
            }

            /**
             * 3.查询本地的账务信息
             */
            Regist regist = regists.get(0);

            ArrayList<Account> canRefundList = new ArrayList<>();
            Double canRefundMoney = 0.0;

            //查询本地的消费信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setRegistId(regist.getRegistId());
            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            for (Account account:accounts){
                if(account.getPayType()==1){
                    continue;
                }
                if(account.getPrice()<=0){
                    continue;
                }
                if(account.getThirdRefundState()!=0){
                    continue;
                }
                if(!account.getPayClassId().equals(11)&&!account.getPayClassId().equals(12)){
                    continue;
                }
                canRefundList.add(account);
                canRefundMoney+=(account.getPrice()/100.0);
            }

            resultMap.put("registMsg",regist);
            resultMap.put("accountMsg",canRefundList);
            resultMap.put("canRefundMoney",canRefundMoney);

            return resultMap;

        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }

        return  resultMap;
    }

    /**
     * 根据 pt 获取对应的支付码
     *  1.现金-1，银行卡-2，会员卡-3，支付宝-8，微信-13，信用住-20
     * @param account
     * @param pt
     * @return
     */
    private Account getPayTypeMsg(Account account,Integer pt){

        switch (pt){
            case 1:
                break;
            case 2:
                break;
            case 8:
                account.setPayClassId(11);
                account.setPayClassName("支付宝");
                account.setPayCodeId("9300");
                account.setPayCodeName("支付宝扫码支付");
                break;
            case 13:
                account.setPayClassId(12);
                account.setPayClassName("微信支付");
                account.setPayCodeId("9320");
                account.setPayCodeName("微信扫码支付");
                break;
            default:
                break;

        }
        return account;
    }
}
