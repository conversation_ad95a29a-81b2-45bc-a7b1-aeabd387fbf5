package com.pms.czabsorders.service.mini;


import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.GetMobileGroupHotelListRequest;
import com.pms.czpmsutils.request.GetMobileHotelRoomListRequest;
import com.pms.czpmsutils.request.GetMobileHotelRoomTypeListRequest;
import com.pms.czpmsutils.request.MobileCreateBookingOrderRequest;

/**
 * 新版小程序、所有接口重新定义
 */
public interface MobileOrderService {
    /**
     * 获取小程序集团酒店信息
     *
     * @return
     */
    ResponseData getMobileGroupHotelList(GetMobileGroupHotelListRequest getMobileGroupHotelListRequest);

    ResponseData getMobileHotelRoomTypeList(GetMobileHotelRoomTypeListRequest getMobileHotelRoomTypeListRequest);

    ResponseData getMobileHotelRoomList(GetMobileHotelRoomListRequest getMobileHotelRoomListRequest);


    /**
     * 创建预订单，要根据不通的类型进行记录 1-微信小程序 2-微信公众号 3-抖音小程序 4-支付宝小程序 等
     *
     * @return
     */
    ResponseData mobileCreateBookingOrder(MobileCreateBookingOrderRequest mobileCreateBookingOrderRequest);

    ResponseData getMobileOrderList();

}
