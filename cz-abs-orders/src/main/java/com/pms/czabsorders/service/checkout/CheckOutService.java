package com.pms.czabsorders.service.checkout;

import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.CheckoutParam;

/**
 * 结账相关
 */
public interface CheckOutService {

    /**
     * 结账
     * @param checkoutParam
     * @return
     */
    public ResponseData checkOut(CheckoutParam checkoutParam);

    /**
     * 撤销入住
     * @param checkoutParam
     * @return
     */
    public ResponseData cancelRegistFunc(CheckoutParam checkoutParam);


    /**
     * 挂账
     * @param checkoutParam
     * @return
     */
    public ResponseData onAccount(CheckoutParam checkoutParam);

    /**
     * 结账参数
     * @param checkoutParam
     * @return
     */
    public ResponseData checkoutAccount(CheckoutParam checkoutParam);

    /**
     * 快速结账
     * @param checkoutParam
     * @return
     */
    public ResponseData fastCheckout(CheckoutParam checkoutParam);


}
