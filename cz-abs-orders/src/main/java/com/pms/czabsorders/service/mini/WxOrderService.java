package com.pms.czabsorders.service.mini;

import com.pms.czabsorders.bean.mini.VipOrderPayRequest;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.BaseRequest;
import com.pms.czpmsutils.request.CreateBookingOrderRequest;
import com.pms.czpmsutils.request.SearchOrderInfoRequest;
import com.pms.czpmsutils.request.StayHoursRequest;
import com.pms.pmsorder.bean.search.BookingOrderRoomNumSearch;
import net.sf.json.JSONObject;

public interface WxOrderService {
    /**
     * 新增预订单
     *
     * @param param
     * @return
     */
    public ResponseData addBook(JSONObject param);

    /**
     * 查询订单
     *
     * @param param
     * @return
     */
    public ResponseData searchBook(JSONObject param);

    /**
     * 微信创建订单新
     *
     * @param createBookingOrderRequest
     * @return
     */
    public ResponseData createBookingOrder(CreateBookingOrderRequest createBookingOrderRequest);

    public ResponseData searchOrderInfo(SearchOrderInfoRequest searchOrderInfoRequest);


    public ResponseData searchBookRoomById(BookingOrderRoomNumSearch bookingOrderRoomNumSearch);

    public ResponseData searchWxRegist(JSONObject param);

    public ResponseData searchRegistInfoForRoomInfo(BaseRequest baseRequest);

    public ResponseData stayHours(StayHoursRequest stayHoursRequest);

    public ResponseData vipOrderPay(VipOrderPayRequest vipOrderPayRequest);

}
