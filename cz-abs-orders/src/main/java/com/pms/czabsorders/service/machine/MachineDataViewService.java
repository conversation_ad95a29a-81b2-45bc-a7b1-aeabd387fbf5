package com.pms.czabsorders.service.machine;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.pms.czpmsutils.HotelUtils;
import com.pms.pmsorder.bean.machine.Hotel;
import com.pms.pmsorder.bean.machine.view.*;
import com.pms.pmsorder.dao.MachineAccountDao;
import com.pms.pmsorder.dao.MachineOrderDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Primary
@Slf4j
public class MachineDataViewService {

    @Resource
    private MachineOrderDao machineOrderDao;


    @Resource
    private MachineAccountDao machineAccountDao;

    public static final Long DAY = 24 * 60 * 60 * 1000L;

    @Resource
    private MachineHotelService machineHotelService;

    public List<TotalPayView> getMachinePayInfo(MachineUserInfoRequest request) {
        List<TotalPayView> machineUserInfo = machineAccountDao.getMachinePayInfo(request);
        Map<Integer, Hotel> allHotelCache = machineHotelService.getAllHotelCache();
        for (TotalPayView view : machineUserInfo) {
            Hotel hotel = allHotelCache.get(view.getHid());
            view.setName(hotel == null ? "" : hotel.getName());
        }
        return machineUserInfo;
    }


    public List<TotalView> getMachineUserInfo(MachineUserInfoRequest request) {
        List<TotalView> machineUserInfo = machineOrderDao.getMachineUserInfo(request);
        Map<Integer, Hotel> allHotelCache = machineHotelService.getAllHotelCache();
        for (TotalView view : machineUserInfo) {
            Hotel hotel = allHotelCache.get(view.getHid());
            view.setName(hotel == null ? "" : hotel.getName());
        }
        return machineUserInfo;
    }

    /**
     * 查询所有入住单的渠道来源占比
     *
     * @param
     * @return
     */
    public List<ResourceView> getResourceInfo(MachineUserInfoRequest request) {
        List<ResourceView> resourceInfo = machineOrderDao.getResourceInfo(request);

        for (ResourceView view : resourceInfo) {
            if (view.getId() == 1) {
                view.setItem("散客");
            } else if (view.getId() == 2) {
                view.setItem("会员");
            } else if (view.getId() == 3) {
                view.setItem("协议单位");
            } else if (view.getId() == 4) {
                view.setItem("旅行社");
            } else if (view.getId() == 5) {
                view.setItem("订房平台");
            }
        }
        return resourceInfo;
    }

    public Page<RegistView> getRegistInfo(MachineUserInfoRequest request) {
        Page<RegistView> getRegistInfo = machineOrderDao.getRegistInfo(request);
        Map<Integer, Hotel> allHotelCache = machineHotelService.getAllHotelCache();
        for (RegistView view : getRegistInfo) {
            if (view.getHid() != null) {
                Hotel hotel = allHotelCache.get(view.getHid());
                view.setHotelName(hotel == null ? "" : hotel.getName());
            }
        }
        return getRegistInfo;
    }

    public List<CheckinInfoView> getDayCheckinInfo(GetDayCheckinInfoRequest request) {
        List<CheckinInfoView> dayCheckinInfo = machineOrderDao.getDayCheckinInfo(request);
        log.info("dayCheckinInfo", JSONObject.toJSONString(dayCheckinInfo));
        for (CheckinInfoView view : dayCheckinInfo) {
            if (view.getCheckinMode() == null) {
                view.setCheckinMode(0);
            }
        }
        return dayCheckinInfo;
    }


    public List<BookingOrderView> getHotelBookingOrder(BookingOrderRequest bookingOrderRequest) {
        List<BookingOrderView> hotelBookingOrder = machineOrderDao.getHotelBookingOrder(bookingOrderRequest);
        Map<Integer, Hotel> allHotelCache = machineHotelService.getAllHotelCache();
        for (BookingOrderView view : hotelBookingOrder) {
            if (view.getHid() != null) {
                Hotel hotel = allHotelCache.get(view.getHid());
                view.setHotelName(hotel == null ? "" : hotel.getName());
            }
        }
        return hotelBookingOrder;
    }


    public ArrayList<XYView> getHotelMachineUserRate(MachineUserInfoRequest request) {
        List<TotalView> machineUserInfo = machineOrderDao.getHotelMachineUserRate(request);
        ArrayList<XYView> xyViews = new ArrayList<>();
        for (TotalView totalView : machineUserInfo) {
            xyViews.add(new XYView(totalView.getName(), totalView.getTotal().doubleValue()));
        }
        return xyViews;
    }

    public ArrayList<XYView> getHotelMachinePayCountInfo(MachineUserInfoRequest request) {
        List<TotalPayView> machineUserInfo = machineAccountDao.getHotelMachinePayCountInfo(request);
        ArrayList<XYView> xyViews = new ArrayList<>();
        for (TotalPayView view : machineUserInfo) {
            xyViews.add(new XYView(view.getName(), view.getTotal()));
        }
        return xyViews;
    }

    public Object getPayCount(MachineUserInfoRequest request) throws ParseException {
        TotalView totalView = machineAccountDao.getPayCount();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        int differentDays = HotelUtils.differentDaysByMillisecond(new Date(), sdf.parse("********"));
        HashMap<String, Object> rt = new HashMap<>();
        rt.put("payCount", ObjectUtil.isNotNull(totalView.getTotal()) ? totalView.getTotal() : 0);
        rt.put("dayPayCount", ObjectUtil.isNotNull(totalView.getTotal()) ? totalView.getTotal() / differentDays : 0);
        return rt;
    }


    public static String dayFormat(String day) {
        return day.substring(0, 4) + "-" + day.substring(4, 6) + "-" + day.substring(6, 8);
    }

    public Object getHotelMachineTotalUseCount(MachineUserInfoRequest request) throws ParseException {
        TotalView totalView = machineOrderDao.getHotelMachineTotalUseCount();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        int differentDays = HotelUtils.differentDaysByMillisecond(new Date(), sdf.parse("********"));

        //最近15天
        Date date = new Date(System.currentTimeMillis() - 15 * DAY);

        request.setStartDate(Integer.parseInt(sdf.format(date)));
        List<TotalView> totalViewList = machineOrderDao.getHotelMachineUseCountIn15(request);

        ArrayList<XYView> xyViews = new ArrayList<>();
        for (TotalView view : totalViewList) {
            xyViews.add(new XYView(dayFormat(view.getName()), view.getTotal().doubleValue()));
        }

        HashMap<String, Object> rt = new HashMap<>();
        rt.put("totalUseCount", totalView.getTotal());
        rt.put("dayUseCount", totalView.getTotal() / differentDays);
        rt.put("useList", xyViews);
        return rt;
    }

    public Object getHotelMachineTotalPayCount(MachineUserInfoRequest request) throws ParseException {
        TotalView totalView = machineAccountDao.getHotelMachineTotalPayCount();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        int differentDays = HotelUtils.differentDaysByMillisecond(new Date(), sdf.parse("********"));

        //最近15天
        Date date = new Date(System.currentTimeMillis() - 15 * DAY);
        request.setStartDate(Integer.parseInt(sdf.format(date)));
        List<TotalView> totalViewList = machineAccountDao.getHotelMachinePayCountIn15(request);

        ArrayList<XYView> xyViews = new ArrayList<>();
        for (TotalView view : totalViewList) {
            xyViews.add(new XYView(dayFormat(view.getName()), view.getTotal().doubleValue()));
        }
        HashMap<String, Object> rt = new HashMap<>();
        rt.put("totalPayCount", totalView.getTotal());
        rt.put("dayPayCount", totalView.getTotal() / differentDays);
        rt.put("payList", xyViews);
        return rt;
    }
}
