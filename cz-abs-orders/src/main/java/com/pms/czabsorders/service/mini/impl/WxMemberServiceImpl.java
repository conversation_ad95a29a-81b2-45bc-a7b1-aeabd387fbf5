package com.pms.czabsorders.service.mini.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.mini.UpdateWxMsg;
import com.pms.czabsorders.bean.mini.WechatPhoneParam;
import com.pms.czabsorders.service.mini.WxMemberService;
import com.pms.czabsorders.service.mini.transaction.WxServiceTransaction;
import com.pms.czhotelfoundation.bean.hotel.HotelBaseInfo;
import com.pms.czhotelfoundation.bean.hotel.HotelMiniproSetting;
import com.pms.czhotelfoundation.bean.hotel.search.HotelBaseInfoSearch;
import com.pms.czhotelfoundation.bean.hotel.search.HotelMiniproSettingSearch;
import com.pms.czhotelfoundation.bean.price.RoomRateCode;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSearch;
import com.pms.czhotelfoundation.dao.code.HotelBusinessDayDao;
import com.pms.czhotelfoundation.dao.hotel.HotelBaseInfoDao;
import com.pms.czhotelfoundation.dao.hotel.HotelMiniproSettingDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeDao;
import com.pms.czmembership.bean.CardNameVail;
import com.pms.czmembership.bean.member.*;
import com.pms.czmembership.bean.member.search.*;
import com.pms.czmembership.dao.member.*;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HclassUtits;
import com.pms.czpmsutils.constant.SMS_LOC;
import com.pms.czpmsutils.constant.hotelsetting.HOTEL_SETTING;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.AddCRechargeRequest;
import com.pms.czpmsutils.request.HotelSettingByParamId;
import com.pms.czpmsutils.request.SmsHotelSendRecordRequest;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.dao.RegistDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 微信会员查询信息
 */
@Service
@Primary
@Slf4j
public class WxMemberServiceImpl extends BaseService implements WxMemberService {

    @Autowired
    private CardGroupInfoDao cardGroupInfoDao;

    @Autowired
    private CardInfoDao cardInfoDao;

    @Autowired
    private CardGroupLevelDao cardGroupLevelDao;

    @Autowired
    private RoomRateCodeDao roomRateCodeDao;

    @Autowired
    private HotelBaseInfoDao hotelBaseInfoDao;

    @Autowired
    private CardTypeDao cardTypeDao;

    @Autowired
    private CardRechargeDao cardRechargeDao;

    @Autowired
    private CardLevelDao cardLevelDao;

    @Autowired
    private CardOperationRecordDao cardOperationRecordDao;

    @Autowired
    private WxServiceTransaction wxServiceTransaction;

    @Autowired
    private HotelBusinessDayDao hotelBusinessDayDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private HotelMiniproSettingDao hotelMiniproSettingDao;


    @Autowired
    private CardNameVailDao cardNameVailDao;

    @Autowired
    private CardRechargePlanDetailsDao cardRechargePlanDetailsDao;

    @Autowired
    private CardRechargePointDao cardRechargePointDao;

    private BaseService baseService = this;

    @Override
    public ResponseData findWxVip(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            TbUserSession user = new TbUserSession();
            try {
                user = this.getTbUserSession(sessionToken);
            } catch (Exception e) {
                responseData.setResult(ER.ERR);
                responseData.setMsg("未查询到会员信息");
                return responseData;
            }

            // 查询集团酒店信息
            CardGroupInfoSearch cardGroupInfoSearch = new CardGroupInfoSearch();
            cardGroupInfoSearch.setHotelGroupId(user.getHotelGroupId());
            cardGroupInfoSearch.setOpenId(user.getSessionId());

            List<CardGroupInfo> cardGroupInfos = cardGroupInfoDao.selectBySearch(cardGroupInfoSearch);

            if (cardGroupInfos.size() < 1) {
                responseData.setResult(ER.ERR);
                responseData.setMsg("未查询到会员信息");
                return responseData;
            }

            CardGroupInfo cardGroupInfo = cardGroupInfos.get(0);

            // 查询单店集团信息
            CardInfoSearch cardInfoSearch = new CardInfoSearch();
            cardInfoSearch.setCardGroupId(cardGroupInfo.getId());
            cardInfoSearch.setHotelGroupId(user.getHotelGroupId());
            List<CardInfo> cardInfos = cardInfoDao.selectBySearch(cardInfoSearch);


            Map<String, List<CardInfo>> collect = cardInfos.stream().collect(Collectors.groupingBy(CardInfo::getHidStr));

            JSONObject jsonObject = JSONObject.fromObject(cardGroupInfo);
            jsonObject.put("hidVipMsg", JSONObject.fromObject(collect));

            HotelBaseInfoSearch hotelBaseInfoSearch = new HotelBaseInfoSearch();
            hotelBaseInfoSearch.setHotelGroupId(user.getHotelGroupId());

            Page<HotelBaseInfo> hotelBaseInfos = hotelBaseInfoDao.selectBySearch(hotelBaseInfoSearch);

            HashMap<String, JSONObject> phoneMap = new HashMap<>();

            for (HotelBaseInfo hotelBaseInfo : hotelBaseInfos) {

                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("phone", hotelBaseInfo.getContactPhone());
                jsonObject1.put("addr", hotelBaseInfo.getAddr());
                jsonObject1.put("hotelName", hotelBaseInfo.getHotelName());
                phoneMap.put(hotelBaseInfo.getHid() + "", jsonObject1);

            }
            jsonObject.put("phoneMap", phoneMap);

            responseData.setData(jsonObject);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }


    @Override
    public ResponseData getWxPhone(WechatPhoneParam param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            log.info("param={}",JSONObject.fromObject(param).toString());

            AESForWeixinGetPhoneNumber aes = new AESForWeixinGetPhoneNumber(param.getEncrypdata(), param.getSessionkey(), param.getIvdata());
            JSONObject decrypt = aes.decrypt();

            log.info("手机号" + decrypt);

            String phoneNumber = decrypt.getString("phoneNumber");

            responseData.setData(phoneNumber);

        } catch (Exception e) {

            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
    /**
     * 注册微信会员
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData registWxVip(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        // 集团id
        int groupId = param.getInt("groupId");

        param.put("IP", "");
        // 微信openid
        String openId = param.getString("openId") + "-" + groupId;
        try {

            // 集团级别id
            int cardLevelId = param.getInt("cardLevelId");
            CardGroupLevel cardGroupLevel = cardGroupLevelDao.selectById(cardLevelId);
            CardLevelSearch cardLevelSearch = new CardLevelSearch();
            cardLevelSearch.setHotelGroupId(groupId);
            cardLevelSearch.setCardGroupLevelId(cardLevelId);
            List<CardLevel> cardLevels = cardLevelDao.selectBySearch(cardLevelSearch);

            Map<String, CardLevel> cardLevelsMap = cardLevels.stream().collect(Collectors.toMap(CardLevel::getHidStr, a -> a, (k1, k2) -> k1));


            // 查询集团房价码
            RoomRateCodeSearch roomRateCodeSearch = new RoomRateCodeSearch();
            roomRateCodeSearch.setHotelGroupId(groupId);
            roomRateCodeSearch.setRataGroupId(cardGroupLevel.getPriceCodeId());

            List<RoomRateCode> roomRateCodes = roomRateCodeDao.selectBySearch(roomRateCodeSearch);

            Map<String, RoomRateCode> priceCodeMap = roomRateCodes.stream().collect(Collectors.toMap(RoomRateCode::getHidStr, a -> a, (k1, k2) -> k1));

            // 查询当前集团
            HotelBaseInfoSearch hotelBaseInfoSearch = new HotelBaseInfoSearch();
            hotelBaseInfoSearch.setHotelGroupId(groupId);
            List<HotelBaseInfo> hotelBaseInfos = hotelBaseInfoDao.selectBySearch(hotelBaseInfoSearch);


            //
            CardGroupInfoSearch cardInfoSearch = new CardGroupInfoSearch();
            cardInfoSearch.setOpenId(openId);

            List<CardGroupInfo> cardGroupInfos = cardGroupInfoDao.selectBySearch(cardInfoSearch);
            if (cardGroupInfos.size() > 0) {
                return responseData;
            }

            // 微信信息
            JSONObject userInfo = JSONObject.fromObject(URLDecoder.decode(param.getString("userInfo"), "utf-8"));

            String nickName = EmojiFilter.filterEmoji(userInfo.getString("nickName"));

            CardNameVail cardNameVail = new CardNameVail();
            cardNameVail.setCardName(nickName);
            try {
                cardNameVailDao.insert(cardNameVail);
            } catch (Exception e) {
                nickName = "微信用户";
                userInfo.put("nickName", "微信用户");
            }

            String avatarUrl = userInfo.getString("avatarUrl");

            int gender = userInfo.getInt("gender");


            TbUserSession user = new TbUserSession(1);

            // 获取会员卡号
            String no = OrderNumUtils.getNo(groupId, user, OrderNumUtils.WXVIP, this.stringRedisTemplate);


            Date date = new Date();

            String phone = "";

            if (param.get("phone") != null) {
                phone = param.getString("phone");
            }

            /**
             * 1.注册会员
             */
            CardGroupInfo cardGroupInfo = new CardGroupInfo();
            cardGroupInfo.setHotelGroupId(groupId);
            cardGroupInfo.setHid(groupId);
            cardGroupInfo.setCardNo(no);
            cardGroupInfo.setOpenId(openId);
            cardGroupInfo.setSta("I");
            cardGroupInfo.setCardTypeId(cardGroupLevel.getCardTypeId());
            cardGroupInfo.setCardType(cardGroupLevel.getCardType());
            cardGroupInfo.setCardLevel(cardGroupLevel.getCode());
            cardGroupInfo.setCardName(nickName);
            cardGroupInfo.setCardLevelId(cardGroupLevel.getId());
            cardGroupInfo.setCardSrc("WXXCX");
            cardGroupInfo.setPriceCode(cardGroupLevel.getPriceCode());
            cardGroupInfo.setPriceCodeId(cardGroupLevel.getPriceCodeId());
            cardGroupInfo.setPointPay(0);
            cardGroupInfo.setPointCharge(0);
            cardGroupInfo.setPointLastNum(0);
            cardGroupInfo.setCode3(avatarUrl);
            cardGroupInfo.setPointLastNumLink(0);
            cardGroupInfo.setLargessBalance(0);
            cardGroupInfo.setSale(0);
            cardGroupInfo.setCardSex(gender);
            cardGroupInfo.setPay(0);
            cardGroupInfo.setFreeze(0);
            cardGroupInfo.setLargessBalance(0);
            cardGroupInfo.setBalance(0);
            cardGroupInfo.setCredit(100);
            cardGroupInfo.setBusinessDay(user.getBusinessDay());
            cardGroupInfo.setYear(user.getBusinessYear());
            cardGroupInfo.setYearMonth(user.getBusinessMonth());
            cardGroupInfo.setUpdateTime(date);
            cardGroupInfo.setUpdateUserId(openId);
            cardGroupInfo.setUpdateUserName(nickName);
            cardGroupInfo.setCreateTime(date);
            cardGroupInfo.setCreateUserId(openId);
            cardGroupInfo.setCreateUserName(nickName);
            cardGroupInfo.setCardPhone(phone);

            TbUserSession session = new TbUserSession();
            session.setSessionId(openId);
            session.setUserName(nickName);
            session.setHid(groupId);
            session.setHotelGroupId(groupId);
            session.setHotelName("");
            session.setClassId(HclassUtits.WX_MINIPROGRAM);
            session.setIp(param.getString("IP"));
            session.setSessionType(3);

            ArrayList<CardInfo> cardInfos = new ArrayList<>();
            ArrayList<CardOperationRecord> cardOperationRecords = new ArrayList<>();

            for (HotelBaseInfo hotelBaseInfo : hotelBaseInfos) {

                // 分批次添加集团会员
                CardInfo c = (CardInfo) JSONObject.toBean(JSONObject.fromObject(cardGroupInfo), CardInfo.class);

                RoomRateCode roomRateCode = priceCodeMap.get(hotelBaseInfo.getHid().toString());
                CardLevel cardLevel = cardLevelsMap.get(hotelBaseInfo.getHid().toString());
                c.setId(null);
                c.setCardName(nickName);
                c.setOpenId(openId);
                c.setCardSrc("WXXCX");
                c.setHid(hotelBaseInfo.getHid());
                c.setPriceCodeId(roomRateCode.getRateId());
                c.setCardTypeId(cardLevel.getCardTypeId());
                c.setCardType(cardLevel.getCardType());
                c.setCardLevelId(cardLevel.getId());
                c.setCardLevel(cardLevel.getCode());
                c.setCardGroupTypeId(cardLevel.getCardGroupTypeId());
                c.setCardGroupLevelId(cardLevel.getCardGroupLevelId());
                c.setCode3(avatarUrl);
                c.setCardSex(gender);
                c.setCardPhone(phone);

                cardInfos.add(c);

                /**
                 * 3.添加会员注册日志
                 */
                CardOperationRecord cor = new CardOperationRecord();
                cor.setCardId(c.getId());
                cor.setCardNo(c.getCardNo());
                cor.setType(1);
                cor.setLastData("");
                cor.setAfterData(JSONObject.fromObject(c).toString());
                cor.setDesc("注册会员");
                cor.setMoney(0);
                cor.setBusinessDay(user.getBusinessDay());
                cor.setHid(c.getHid());
                cor.setHotelGroupId(c.getHotelGroupId());
                cor.setYear(user.getBusinessYear());
                cor.setYearMonth(user.getBusinessMonth());
                cor.setCreateTime(date);
                cor.setCreateUserId(openId);
                cor.setCreateUserName(nickName);
                cardOperationRecords.add(cor);

            }

            wxServiceTransaction.registVxVip(cardGroupInfo, cardInfos, cardOperationRecords, session);

        } catch (Exception e) {

            TbUserSession userSession = new TbUserSession();
            userSession.setSessionId(openId);
            this.deleteTbUserSession(userSession);
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    public ResponseData updateWxMsg(UpdateWxMsg param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getSessionToken();
            TbUserSession user = new TbUserSession();
            try {
                user = this.getTbUserSession(sessionToken);
            } catch (Exception e) {
                responseData.setResult(ER.ERR);
                responseData.setMsg("未查询到会员信息");
                return responseData;
            }

            param.setCardName(EmojiFilter.filterEmoji(param.getCardName()));
            log.info("param.getCardName()={}",param.getCardName());

            // 查询集团酒店信息
            CardGroupInfoSearch cardGroupInfoSearch = new CardGroupInfoSearch();
            cardGroupInfoSearch.setHotelGroupId(user.getHotelGroupId());
            cardGroupInfoSearch.setOpenId(user.getSessionId());

            List<CardGroupInfo> cardGroupInfos = cardGroupInfoDao.selectBySearch(cardGroupInfoSearch);

            if (cardGroupInfos.size() < 1) {
                responseData.setResult(ER.ERR);
                responseData.setMsg("未查询到会员信息");
                return responseData;
            }

            CardGroupInfo cardGroupInfo = cardGroupInfos.get(0);

            // 查询单店集团信息
            CardInfoSearch cardInfoSearch = new CardInfoSearch();
            cardInfoSearch.setCardGroupId(cardGroupInfo.getId());
            cardInfoSearch.setHotelGroupId(user.getHotelGroupId());
            List<CardInfo> cardInfos = cardInfoDao.selectBySearch(cardInfoSearch);

            // 1.想修改集团信息
            cardGroupInfo.setCardName(param.getCardName());
            cardGroupInfo.setCardSex(param.getCardSex());
            cardGroupInfo.setCode3(param.getCardTx());
            cardGroupInfo.setCardPhone(param.getCardPhone());

            ArrayList<CardInfo> upaCardInfos = new ArrayList<>();

            for (CardInfo cardInfo : cardInfos) {
                cardInfo.setCardName(param.getCardName());
                cardInfo.setCardSex(param.getCardSex());
                cardInfo.setCode3(param.getCardTx());
                cardInfo.setCardPhone(param.getCardPhone());
                upaCardInfos.add(cardInfo);
            }

            wxServiceTransaction.upaWxMemberMsg(cardGroupInfo, upaCardInfos);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }

    @Override
    public ResponseData searchCheckIn(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            TbUserSession user = this.getTbUserSession(sessionToken);

            CardGroupInfoSearch cardGroupInfoSearch = new CardGroupInfoSearch();
            cardGroupInfoSearch.setHotelGroupId(user.getHotelGroupId());
            cardGroupInfoSearch.setOpenId(user.getSessionId());

            List<CardGroupInfo> cardGroupInfos = cardGroupInfoDao.selectBySearch(cardGroupInfoSearch);

            if (cardGroupInfos.size() < 1) {
                responseData.setResult(ER.ERR);
                responseData.setMsg("未查询到会员信息");
                return responseData;
            }

            CardGroupInfo cardGroupInfo = cardGroupInfos.get(0);

            // 查询单店集团信息
            CardInfoSearch cardInfoSearch = new CardInfoSearch();
            cardInfoSearch.setCardGroupId(cardGroupInfo.getId());
            cardInfoSearch.setHotelGroupId(user.getHotelGroupId());
            List<CardInfo> cardInfos = cardInfoDao.selectBySearch(cardInfoSearch);

            String memberIds = "-231231";

            for (CardInfo cardInfo : cardInfos) {

                memberIds += ",";
                memberIds += cardInfo.getId();

            }

            RegistSearch registSearch = new RegistSearch();
            registSearch.setMemberIds(memberIds);
            registSearch.setState(0);

            List<Regist> regists = registDao.selectBySearch(registSearch);

            JSONArray jsonArray = new JSONArray();

            HotelMiniproSettingSearch hotelMiniproSettingSearch = new HotelMiniproSettingSearch();
            hotelMiniproSettingSearch.setHotelGroupId(user.getHotelGroupId());
            Page<HotelMiniproSetting> hotelMiniproSettings = hotelMiniproSettingDao.selectBySearch(hotelMiniproSettingSearch);

            Map<Integer, HotelMiniproSetting> wxsetMap = hotelMiniproSettings.stream().collect(Collectors.toMap(HotelMiniproSetting::getHid, a -> a, (k1, k2) -> k1));


            for (Regist regist : regists) {

                HotelMiniproSetting hotelMiniproSetting = wxsetMap.get(regist.getHid());

                // 值小于1 说明是0 0代表不开启
                if (hotelMiniproSetting != null && hotelMiniproSetting.getTabW() < 1) {

                    continue;

                }


                JSONObject jo = new JSONObject();

                jo.put("hid", regist.getHid());
                jo.put("hotelGroupId", regist.getHotelGroupId());
                jo.put("roomInfoId", regist.getRoomNumId());
                jo.put("roomNum", regist.getRoomNum());
                jo.put("reType", 1);
                jo.put("registId", regist.getRegistId());

                HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
                hotelSettingByParamId.setHid(regist.getHid());
                hotelSettingByParamId.setParamId(HOTEL_SETTING.STOCK_CALSTOCK);

                Object hotelSettingByParamId1 = this.findHotelSettingByParamId(hotelSettingByParamId);

                jo.put("hotelIsCheckNum", hotelSettingByParamId1);

                jsonArray.add(jo);

            }

            responseData.setData(jsonArray);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }

    @Override
    public ResponseData getBindVipMsg(CardGroupUrlSearch cardGroupUrlSearch) {

        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            JSONObject res = new JSONObject();

            // 获取用户id信息
            CardGroupInfo cardInfo = cardGroupInfoDao.selectById(cardGroupUrlSearch.getCardGroupId());

            if (cardInfo == null) {
                throw new Exception("未查询到会员信息");
            }
            String openId = cardInfo.getOpenId();
            String crc = cardInfo.getCrc();
            if ("we".equals(crc) || (openId != null && openId.length() > 20)) {
                throw new Exception("已经绑定过微信");
            }

            res.put("base", cardInfo);

            String copenid = cardGroupUrlSearch.getOpenId() + "-" + cardInfo.getHotelGroupId();

            CardGroupInfoSearch cardGroupInfoSearch = new CardGroupInfoSearch();
            cardGroupInfoSearch.setOpenId(copenid);
            cardGroupInfoSearch.setHotelGroupId(cardInfo.getHotelGroupId());

            List<CardGroupInfo> cardGroupInfos = cardGroupInfoDao.selectBySearch(cardGroupInfoSearch);
            if (cardGroupInfos.size() > 0) {
                CardGroupInfo cardGroupInfo = cardGroupInfos.get(0);
                res.put("wres", 1);
                res.put("wvip", cardGroupInfo);
            } else {
                res.put("wres", 0);
            }

            responseData.setData(res);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }

    /**
     * 绑定会员信息
     * 1.合并储值，消费，积分，冻结
     * 2.合并储值记录、消费记录
     * 3.同步有效的订单信息
     * 4.更新token
     * 5.添加操作记录
     *
     * @param cardGroupUrlSearch
     * @return
     */
    @Override
    @Transactional
    public ResponseData bindVip(CardGroupUrlSearch cardGroupUrlSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            JSONObject res = new JSONObject();

            // 1.查询会员信息
            CardGroupInfo cardInfo = cardGroupInfoDao.selectById(cardGroupUrlSearch.getCardGroupId());

            if (cardInfo == null) {
                throw new Exception("未查询到会员信息");
            }
            String openId = cardInfo.getOpenId();
            String crc = cardInfo.getCrc();
            if ("we".equals(crc) || (openId != null && openId.length() > 20)) {
                throw new Exception("已经绑定过微信");
            }

            String copenid = cardGroupUrlSearch.getOpenId() + "-" + cardInfo.getHotelGroupId();

            CardGroupInfoSearch cardGroupInfoSearch = new CardGroupInfoSearch();
            cardGroupInfoSearch.setOpenId(copenid);
            cardGroupInfoSearch.setHotelGroupId(cardInfo.getHotelGroupId());
            List<CardInfo> cardInfos = new ArrayList<>();
            CardGroupInfo cardGroupInfo = null;
            Map<Integer, CardInfo> wvipMap = new HashMap<>();
            List<CardGroupInfo> cardGroupInfos = cardGroupInfoDao.selectBySearch(cardGroupInfoSearch);
            if (cardGroupInfos.size() > 0) {
                cardGroupInfo = cardGroupInfos.get(0);
                CardInfoSearch wxcardInfoSearch = new CardInfoSearch();
                wxcardInfoSearch.setCardGroupId(cardGroupInfo.getId());
                //  查询酒店会员信息
                CardInfoSearch cardInfoSearch = new CardInfoSearch();
                cardInfoSearch.setCardGroupId(cardGroupInfo.getId());
                List<CardInfo> wxCardInfos = cardInfoDao.selectBySearch(cardInfoSearch);
                wvipMap = wxCardInfos.stream().collect(Collectors.toMap(CardInfo::getHid, a -> a, (k1, k2) -> k2));

            } else {
                TbUserSession userSession = new TbUserSession();
                userSession.setSessionId(copenid);
                userSession.setUserId(cardInfo.getId().toString());
                userSession.setSessionId(openId);
                userSession.setUserName(cardInfo.getCardName());
                userSession.setHid(cardInfo.getHid());
                userSession.setHotelGroupId(cardInfo.getHotelGroupId());
                userSession.setHotelName("");
                userSession.setClassId(HclassUtits.WX_MINIPROGRAM);
                userSession.setIp("http://127.0.0.1");
                userSession.setSessionType(3);
                this.addTbUserSession(userSession);
            }

            // 2.组装会员数据
            cardInfo.setCrc("we");
            cardInfo.setOpenId(copenid);
            if (cardGroupInfo != null) {
                cardInfo.setBalance(cardInfo.getBalance() + cardGroupInfo.getBalance());
                cardInfo.setLargessBalance(cardInfo.getLargessBalance() + cardGroupInfo.getLargessBalance());
                cardInfo.setFreeze(cardInfo.getFreeze() + cardGroupInfo.getFreeze());
                cardInfo.setLargessFreeze(cardInfo.getLargessFreeze() + cardGroupInfo.getLargessFreeze());
                cardInfo.setPointCharge(cardInfo.getPointCharge() + cardGroupInfo.getPointCharge());
                cardInfo.setPointPay(cardInfo.getPointPay() + cardGroupInfo.getPointPay());
                cardGroupInfo.setHid(cardGroupInfo.getHid() * -1);
                cardGroupInfo.setHotelGroupId(cardGroupInfo.getHotelGroupId() * -1);
                cardGroupInfo.setCardNo("-" + cardGroupInfo.getCardNo());
                cardGroupInfo.setOpenId("-" + cardGroupInfo.getOpenId());
                cardGroupInfoDao.update(cardGroupInfo);
            }

            cardGroupInfoDao.update(cardInfo);


            ArrayList<CardInfo> upaList = new ArrayList<>();

            // 子会员数据
            for (CardInfo cf : cardInfos) {

                CardInfo cd = wvipMap.get(cf.getHid());

                cf.setOpenId(copenid);

                if (cd != null) {
                    cd.setOpenId("-" + cd.getOpenId());
                    cd.setHid(cd.getHid() * -1);
                    cd.setHotelGroupId(cd.getHotelGroupId() * -1);
                    upaList.add(cd);
                    cf.setBalance(cf.getBalance() + cd.getBalance() - cardInfo.getBalance());
                    cf.setLargessBalance(cf.getLargessBalance() + cd.getLargessBalance() - cardInfo.getLargessBalance());
                    cf.setFreeze(cf.getFreeze() + cd.getFreeze() - cardInfo.getFreeze());
                    cf.setLargessFreeze(cf.getLargessFreeze() + cd.getLargessFreeze() - cardInfo.getLargessFreeze());
                    cf.setPointCharge(cf.getPointCharge() + cd.getPointCharge() - cardInfo.getPointCharge());
                    cf.setPointPay(cf.getPointPay() + cd.getPointPay() - cardInfo.getPointPay());

                }
                upaList.add(cf);
            }

            cardInfoDao.updateList(upaList);


        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }

    @Override
    public ResponseData selectRechargePlan(CardRechargePlanDetailsSearch cardRechargePlanDetailsSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            TbUserSession tbUserSession = this.getTbUserSession(cardRechargePlanDetailsSearch);
            CardGroupInfo cardGroupInfo = cardGroupInfoDao.selectById(cardRechargePlanDetailsSearch.getCardGroupInfoId());

            CardRechargePlanDetailsSearch cdrsa = new CardRechargePlanDetailsSearch();
            cdrsa.setCardGroupLevelId(cardGroupInfo.getCardLevelId());
            cdrsa.setHotelGroupId(cardGroupInfo.getHotelGroupId());

            Page<Map<String, Object>> maps = cardRechargePlanDetailsDao.selectBySearchAndPlan(cdrsa);

            responseData.setData(maps);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            responseData.setCode(1);
        }
        return responseData;
    }

    @Override
    public ResponseData addCRecharge(AddCRechargeRequest addCRechargeRequest) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            TbUserSession user = this.getTbUserSession(addCRechargeRequest);

            if (null == addCRechargeRequest.getPayCode() || addCRechargeRequest.getPayCode().equals("")) {
                throw new Exception("支付方式不明确");
            }
            if (null == addCRechargeRequest.getPayMoney()) {
                throw new Exception("支付金额不明确");
            }
            CardGroupInfo cardGroupInfo = cardGroupInfoDao.selectById(addCRechargeRequest.getCardGroupId());
            if (cardGroupInfo == null || !cardGroupInfo.getHotelGroupId().equals(user.getHotelGroupId())) {
                throw new Exception("未查到对应的会员信息");
            }
            CardInfoSearch cardInfoSearch = new CardInfoSearch();
            cardInfoSearch.setHid(cardGroupInfo.getHid());
            cardInfoSearch.setCardGroupId(cardGroupInfo.getId());
            List<CardInfo> cardInfos = cardInfoDao.selectBySearch(cardInfoSearch);
            CardInfo cardInfo = cardInfos.get(0);
            Date date = new Date();
            JSONObject beforData = new JSONObject();
            beforData.put("balance", cardInfo.getBalance());
            beforData.put("largessBalance", cardInfo.getLargessBalance());
            beforData.put("freeze", cardInfo.getFreeze());
            beforData.put("groupBalance", cardGroupInfo.getBalance());
            beforData.put("groupLargessBalance", cardGroupInfo.getLargessBalance());
            beforData.put("groupFreeze", cardGroupInfo.getFreeze());
            beforData.put("pointCharge", cardGroupInfo.getPointCharge());
            // 充值金额、赠送金额
            int money = addCRechargeRequest.getPayMoney();
            int giveMoney = addCRechargeRequest.getAddLargessBalance();
            // 赠送的积分
            int givePoint = 0;
            if (null != addCRechargeRequest.getGivePoint()) {
                givePoint = addCRechargeRequest.getGivePoint();
            }
            // 添加会员充值记录
            CardRecharge cardRecharge = new CardRecharge();
            cardRecharge.setCardGroupId(cardGroupInfo.getId());
            cardRecharge.setHid(user.getHid());
            cardRecharge.setHotelGroupId(user.getHotelGroupId());
            cardRecharge.setCardId(cardInfo.getId());
            cardRecharge.setCardGroupId(cardGroupInfo.getId());
            cardRecharge.setCardNo(cardInfo.getCardNo());
            cardRecharge.setCardLevelId(cardInfo.getCardLevelId());
            cardRecharge.setCardGroupLevelId(cardGroupInfo.getCardLevelId());
            cardRecharge.setCardTypeId(cardInfo.getCardTypeId());
            cardRecharge.setCardGroupTypeId(cardGroupInfo.getCardTypeId());
            cardRecharge.setGroupType(addCRechargeRequest.getGroupType());
            cardRecharge.setSta("I");
            cardRecharge.setPayMoney(money);
            if (addCRechargeRequest.getPayCode().equals("9340")) {
                cardRecharge.setPayClassId(1);
                cardRecharge.setPayClassName("微信支付");
                cardRecharge.setPayModelId("9340");
                cardRecharge.setPayClassName("微信公众号支付");
            }
            cardRecharge.setAddBalance(money);
            cardRecharge.setAddLargessBalance(addCRechargeRequest.getAddLargessBalance());
            cardRecharge.setLastBalance(cardInfo.getBalance());
            cardRecharge.setLastLargessBalance(cardInfo.getLargessBalance());

            if (null != addCRechargeRequest.getThirdAccoutId() && !addCRechargeRequest.getThirdAccoutId().equals("")) {
                cardRecharge.setThirdAccoutId(addCRechargeRequest.getThirdAccoutId());
                cardRecharge.setThirdRefundState(0);
                cardRecharge.setRefundPrice(0);
                cardRechargeDao.saveCardRechargeMain(addCRechargeRequest.getThirdAccoutId());
            }
            if (addCRechargeRequest.getGroupType() == 1) {
                cardRecharge.setLastBalance(cardGroupInfo.getBalance());
                cardRecharge.setLastLargessBalance(cardGroupInfo.getLargessBalance());
            }
            cardRecharge.setType(1);
            cardRecharge.setBusinessDay(user.getBusinessDay());
            cardRecharge.setYear(user.getBusinessYear());
            cardRecharge.setMonth(user.getBusinessMonth());
            cardRecharge.setClassId(user.getClassId());
            cardRecharge.setCreateTime(date);
            cardRecharge.setCreateUserId(user.getUserId());
            cardRecharge.setCreateUserName(user.getUserName());

            Integer integer = cardRechargeDao.saveCardRecharge(cardRecharge);

            if (integer < 1) {
                throw new Exception("添加充值记录失败");
            }

            // 积分大于0  添加积分操作记录
            if (givePoint > 0) {
                CardRechargePoint cardRechargePoint = new CardRechargePoint();
                cardRechargePoint.setHid(user.getHid());
                cardRechargePoint.setHotelGroupId(user.getHotelGroupId());
                cardRechargePoint.setCardId(cardInfo.getId());
                cardRechargePoint.setCardGroupId(cardGroupInfo.getId());
                cardRechargePoint.setCardNo(cardInfo.getCardNo());
                cardRechargePoint.setSta("I");
                cardRechargePoint.setMoney(givePoint);
                cardRechargePoint.setReType(3);
                cardRechargePoint.setYear(user.getBusinessYear());
                cardRechargePoint.setBusinessDay(user.getBusinessDay());
                cardRechargePoint.setMonth(user.getBusinessMonth());
                cardRechargePoint.setCreateTime(date);
                cardRechargePoint.setClassId(user.getClassId());
                cardRechargePoint.setCreateUserId(user.getUserId());
                cardRechargePoint.setCreateUserName(user.getUserName());
                cardRechargePoint.setCardRechargeId(cardRecharge.getId());

                cardRechargePointDao.insert(cardRechargePoint);

            }

            /**
             * 更改会员余额
             */

            Integer sumMoney = 0;

            int upaIndex = 0;
            if (addCRechargeRequest.getGroupType() == 1) {

                cardGroupInfo.setBalance(cardGroupInfo.getBalance() + money);
                cardGroupInfo.setLargessBalance(cardGroupInfo.getLargessBalance() + giveMoney);
                cardGroupInfo.setPointCharge(cardGroupInfo.getPointCharge() + givePoint);

                sumMoney += cardGroupInfo.getBalance();
                sumMoney += cardGroupInfo.getLargessBalance();

                upaIndex = cardGroupInfoDao.update(cardGroupInfo);

            } else {

                cardInfo.setBalance(cardInfo.getBalance() + money);
                cardInfo.setLargessBalance(cardInfo.getLargessBalance() + giveMoney);
                cardInfo.setUpdateTime(new Date());
                cardInfo.setUpdateUserId(user.getUserId());
                cardInfo.setUpdateUserName(user.getUserName());
                cardInfo.setPointCharge(cardInfo.getPointCharge() + givePoint);

                sumMoney += cardInfo.getBalance();
                sumMoney += cardInfo.getLargessBalance();

                upaIndex = cardInfoDao.update(cardInfo);
            }


            if (upaIndex < 1) {
                throw new Exception("修改会员余额失败。");
            }

            /**
             * 添加会员操作日志
             */
            CardOperationRecord cardOperationRecord = new CardOperationRecord();
            cardOperationRecord.setCardId(cardInfo.getId());
            cardOperationRecord.setCardGroupId(cardInfo.getCardGroupId());
            cardOperationRecord.setCardNo(cardInfo.getCardNo());
            cardOperationRecord.setHid(user.getHid());
            cardOperationRecord.setHotelGroupId(user.getHotelGroupId());
            cardOperationRecord.setType(2);
            cardOperationRecord.setLastData(beforData.toString());
            cardOperationRecord.setAfterData(beforData.toString());
            cardOperationRecord.setDesc(cardInfo.getCardNo() + " 充值：" + money / 100.0 + " 元");
            cardOperationRecord.setMoney(money);
            cardOperationRecord.setBusinessDay(user.getBusinessDay());
            cardOperationRecord.setYearMonth(user.getBusinessMonth());
            cardOperationRecord.setYear(user.getBusinessYear());
            cardOperationRecord.setCreateUserId(user.getUserId());
            cardOperationRecord.setCreateUserName(user.getUserName());
            cardOperationRecord.setCreateTime(date);
            cardOperationRecordDao.saveCardOperationRecord(cardOperationRecord);

            String cardPhone = cardInfo.getCardPhone();
            final ArrayList<String> strings = new ArrayList<>();
            strings.add(cardInfo.getCardLevel());
            strings.add(user.getHotelName());
            strings.add(HotelUtils.getMoney(money / 100.0));
            strings.add(HotelUtils.getMoney(giveMoney / 100.0));
            strings.add(HotelUtils.getMoney(sumMoney / 100.0));

            if (cardPhone != null && cardPhone.length() == 11) {
                HotelUtils.cachedThreadPool.execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                            smsHotelSendRecordRequest.setLocationId(SMS_LOC.MEMBER_CZ);
                            smsHotelSendRecordRequest.setSessionToken(addCRechargeRequest.getSessionToken());
                            smsHotelSendRecordRequest.setPhoneNumber(cardPhone);
                            smsHotelSendRecordRequest.setParams(strings);
                            baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);
                        } catch (Exception e) {
                            log.error("",e);
                        }
                    }
                });
            }


        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
