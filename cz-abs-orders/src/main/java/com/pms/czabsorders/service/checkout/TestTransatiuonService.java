package com.pms.czabsorders.service.checkout;


import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.BookingOrderRoomType;
import com.pms.pmsorder.dao.BookingOrderDao;
import com.pms.pmsorder.dao.BookingOrderRoomTypeDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TestTransatiuonService {
    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private AccountDao accountDao;

    @Transactional
    public String testService() {
        BookingOrder bookingOrder = new BookingOrder();
        bookingOrderDao.saveBookingOrder(bookingOrder);

        BookingOrderRoomType bookingOrderRoomType = new BookingOrderRoomType();
        bookingOrderRoomTypeDao.saveBookingOrderRoomType(bookingOrderRoomType);

        Account account = new Account();
        account.setAccountId("test_account_id"+System.currentTimeMillis());
        account.setIsCancel(0);
        account.setRegistPersonId(100);
        accountDao.saveAccount(account);


        return "ok";
    }

}
