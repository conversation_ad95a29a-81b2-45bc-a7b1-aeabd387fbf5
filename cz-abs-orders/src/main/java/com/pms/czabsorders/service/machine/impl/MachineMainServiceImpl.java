package com.pms.czabsorders.service.machine.impl;


import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.pms.czabsorders.service.machine.MachineMainService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.ERROR_MSG;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.machine.MachineCreateNum;
import com.pms.pmsorder.bean.machine.MachineMain;
import com.pms.pmsorder.bean.machine.MachineUpdateRecord;
import com.pms.pmsorder.bean.machine.search.MachineCreateNumSearch;
import com.pms.pmsorder.bean.machine.search.MachineMainSearch;
import com.pms.pmsorder.bean.search.RegistPersonSearch;
import com.pms.pmsorder.bean.search.RegistSearch;
import com.pms.pmsorder.dao.*;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Primary
@Slf4j
public class MachineMainServiceImpl extends BaseService implements MachineMainService {

    @Autowired
    private MachineMainDao machineMainDao;

    @Autowired
    private MachineServiceImpl machineService;

    @Autowired
    private MachineUpdateRecordDao machineUpdateRecordDao;

    @Autowired
    private MachineCreateNumDao machineCreateNumDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private RegistPersonDao registPersonDao;


    @Override
    public ResponseData addOrUpdateMachine(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Object createType = param.get("createType");
            // 说明创建的是刷脸吃早餐机器
            if (createType != null && Integer.parseInt(createType.toString()) == 1) {
                ResponseData responseData1 = addBareaFastMachine(param);
                return responseData1;
            }
            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if (!user.getSessionType().equals(2)) {
                throw new Exception("非管理员不能操作自助机");
            }
            /**
             * 取出自助机参数
             */
            String machineParam = param.getString("machineParam");
            JSONObject jsonObject = JSONObject.fromObject(machineParam);
            MachineMain machineMain = (MachineMain) JSONObject.toBean(jsonObject, MachineMain.class);
            Object expireData = jsonObject.get("expireData");
            if (expireData != null) {
                machineMain.setExpireData(HotelUtils.parseStr2Date(expireData.toString()));
            }

            Date date = new Date();
            /**
             * 判断修改或者是添加自助机
             *   id 为空 或id < 0 说明是添加
             */
            int updateValue = 0;
            Oprecord oprecord = new Oprecord(user);
            //生成唯一token值
            final String uuid = HotelUtils.getUUID();
            if (machineMain.getId() == null || machineMain.getId() < 0) {
                machineMain.setCreateDate(date);
                machineMain.setCreateUser(user.getUserName());
                machineMain.setUuid(uuid);
                machineMain.setServiceState(1);
                machineMain.setState(1);
                oprecord.setDescription("创建自助机:" + machineParam);
                updateValue = machineMainDao.insert(machineMain);
                if (updateValue > 0) {

                    /**
                     * 添加接口验证
                     */
                    TbUserSession userSession = new TbUserSession();
                    userSession.setHotelGroupId(machineMain.getHotelGroupId());
                    userSession.setHid(machineMain.getHid());
                    userSession.setClassId(6);
                    userSession.setUserId(uuid);
                    userSession.setUserName(machineMain.getMachineName());
                    userSession.setState(1);
                    userSession.setSessionId(uuid);
                    userSession.setSessionType(4);
                    this.addTbUserSession(userSession);
                }

                MachineUpdateRecord machineUpdateRecord = new MachineUpdateRecord();
                machineUpdateRecord.setHid(user.getHid());
                machineUpdateRecord.setMachineExpireData(machineMain.getExpireData());
                machineUpdateRecord.setMachineUuid(uuid);
                machineUpdateRecord.setUpdateTime(new Date());
                machineUpdateRecord.setUserName(user.getUserName());
                machineUpdateRecord.setMemo(machineParam);
                machineUpdateRecordDao.insert(machineUpdateRecord);


            } else {
                oprecord.setDescription("修改自助机:" + machineParam);
                updateValue = machineMainDao.update(machineMain);
            }
            if (updateValue < 1) {
                throw new Exception("操作失败");
            }

            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("uuid", machineMain.getUuid());
            machineService.updateMachineSettingToCache(jsonObject1);

            /**
             * 创建接口验证
             */
            this.addOprecords(oprecord);
            responseData.setMsg("添加成功");
        } catch (Exception e) {
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }

    public ResponseData addBareaFastMachine(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            /**
             * 取出自助机参数
             */
            String machineParam = param.getString("machineParam");
            JSONObject jsonObject = JSONObject.fromObject(machineParam);
            MachineMain machineMain = (MachineMain) JSONObject.toBean(jsonObject, MachineMain.class);
            Object expireData = jsonObject.get("expireData");
            if (expireData != null) {
                machineMain.setExpireData(HotelUtils.parseStr2Date(expireData.toString()));
            } else {
                long time = new Date().getTime();
                Date date = new Date(time + 8640000 * 3650);
                machineMain.setExpireData(date);
            }

            Date date = new Date();
            /**
             * 判断修改或者是添加自助机
             *   id 为空 或id < 0 说明是添加
             */
            int updateValue = 0;
            Oprecord oprecord = new Oprecord(user);
            //生成唯一token值
            final String uuid = HotelUtils.getUUID();
            machineMain.setCreateDate(date);
            machineMain.setCreateUser(user.getUserName());
            machineMain.setUuid(uuid);
            machineMain.setServiceState(1);
            machineMain.setState(1);
            oprecord.setDescription("创建早餐刷脸自助机:" + machineParam);

            Integer machineType = machineMain.getMachineType();

            MachineCreateNumSearch machineCreateNumSearch = new MachineCreateNumSearch();
            machineCreateNumSearch.setHid(user.getHid());
            machineCreateNumSearch.setMachineType(machineType);
            Page<MachineCreateNum> machineCreateNums = machineCreateNumDao.selectBySearch(machineCreateNumSearch);

            if (machineCreateNums.size() < 1) {
                responseData.setResult(ER.ERR);
                responseData.setMsg("当前酒店未有创建权限");
                return responseData;
            }
            MachineCreateNum machineCreateNum = machineCreateNums.get(0);
            if (machineCreateNum.getCreateNum() >= machineCreateNum.getSumNum()) {
                responseData.setResult(ER.ERR);
                responseData.setMsg("当前酒店早餐机已达到最大创建数量");
                return responseData;
            }

            updateValue = machineMainDao.insert(machineMain);
            if (updateValue > 0) {

                /**
                 * 添加接口验证
                 */
                TbUserSession userSession = new TbUserSession();
                userSession.setHotelGroupId(machineMain.getHotelGroupId());
                userSession.setHid(machineMain.getHid());
                userSession.setClassId(6);
                userSession.setUserId(uuid);
                userSession.setUserName(machineMain.getMachineName());
                userSession.setState(1);
                userSession.setSessionId(uuid);
                userSession.setSessionType(4);
                this.addTbUserSession(userSession);
            }

            MachineUpdateRecord machineUpdateRecord = new MachineUpdateRecord();
            machineUpdateRecord.setHid(user.getHid());
            machineUpdateRecord.setMachineExpireData(machineMain.getExpireData());
            machineUpdateRecord.setMachineUuid(uuid);
            machineUpdateRecord.setUpdateTime(new Date());
            machineUpdateRecord.setUserName(user.getUserName());
            machineUpdateRecord.setMemo(machineParam);
            machineUpdateRecordDao.insert(machineUpdateRecord);


            // 更改创建数量
            machineCreateNum.setCreateNum(machineCreateNum.getCreateNum() + 1);
            machineCreateNumDao.update(machineCreateNum);
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("uuid", machineMain.getUuid());
            machineService.updateMachineSettingToCache(jsonObject1);

            /**
             * 创建接口验证
             */
            this.addOprecords(oprecord);
            responseData.setMsg("添加成功");
        } catch (Exception e) {
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }

    /**
     * 查询所有自助机
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> findAllMachine(JSONObject param) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.put(ER.RES, ER.SUCC);

        try {

            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);


           /* final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();

            Object o = userCahe.get(ECache.MACHINE_ALL, ECache.MACHINE_ALL);
*/
          /*  if(o==null||"{}".equals(o.toString())){
                List<MachineMain> machineMains = updateAllMachineForCache();
                jsonObject.put("data",machineMains);
            }else {
                jsonObject.put("data",JSONArray.fromObject(o));
            }*/

            List<MachineMain> machineMains = updateAllMachineForCache();
            jsonObject.put("data", machineMains);

            return jsonObject;

        } catch (Exception e) {
            jsonObject.put(ER.RES, ER.ERR);
            jsonObject.put(ER.MSG, e.getMessage());
            log.error("",e);

        }

        return jsonObject;
    }


    /**
     * 更新缓存中的自助机信息
     *
     * @return
     */
    @Override
    public List<MachineMain> updateAllMachineForCache() {

        MachineMainSearch machineMainSearch = new MachineMainSearch();
        List<MachineMain> machineMains = machineMainDao.selectBySearch(machineMainSearch);

      /*  final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();

        userCahe.put(ECache.MACHINE_ALL,ECache.MACHINE_ALL, JSONArray.fromObject(machineMains).toString());*/

        return machineMains;

    }

    @Override
    public ResponseData getHotelMachine(MachineMainSearch machineMainSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(machineMainSearch.getSessionToken());
            machineMainSearch.setHid(user.getHid());
            Page<MachineMain> machineMains = machineMainDao.selectBySearch(machineMainSearch);
            responseData.setData(machineMains);
        } catch (Exception e) {
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }

    @Override
    public ResponseData updateHotelMachine(MachineMain machineMain) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(machineMain.getSessionToken());
            Integer result = 0;
            /**
             * 是电脑的时候把所有的mac都改成大写
             */
            if (machineMain.getMachineType()  ==  7 && StrUtil.isNotEmpty(machineMain.getMacUuid())){
                String macUuid = machineMain.getMacUuid();
                machineMain.setMacUuid(macUuid.toUpperCase());
            }
            if (null == machineMain.getId() || machineMain.getId() < 0) {
                machineMain.setCreateDate(new Date());
                machineMain.setCreateUser(user.getUserName());
                //生成唯一token值
                final String uuid = HotelUtils.getUUID();
                machineMain.setUuid(uuid);
                machineMain.setServiceState(1);
                machineMain.setState(1);
                machineMain.setHid(user.getHid());
                machineMain.setHotelGroupId(user.getHotelGroupId());
                machineMain.setHotelGroupName(user.getHotelName());
                machineMain.setHotelName(user.getHotelName());
                long time = new Date().getTime();
                Date date = new Date(time + 8640000 * 3650);
                machineMain.setExpireData(date);
                machineMain.setEnvironmentalState(1);
                if (null == machineMain.getCountDown()){
                    machineMain.setCountDown(120);
                }
                result = machineMainDao.insert(machineMain);
                if (result < 1) {
                    throw new Exception("添加失败");
                }
                /**
                 * 添加接口验证
                 */
                TbUserSession userSession = new TbUserSession();
                userSession.setHotelGroupId(machineMain.getHotelGroupId());
                userSession.setHid(machineMain.getHid());
                userSession.setClassId(6);
                userSession.setUserId(uuid);
                userSession.setUserName(machineMain.getMachineName());
                userSession.setState(1);
                userSession.setSessionId(uuid);
                userSession.setSessionType(4);
                this.addTbUserSession(userSession);
            } else {
                result = machineMainDao.update(machineMain);
            }
        } catch (Exception e) {
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return responseData;
    }

    @Override
    public ResponseData searchRegistInfoByMac(MachineMainSearch machineMainSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (null ==  machineMainSearch.getMacUuid()){
                throw new Exception("MAC地址不能空");
            }
            Page<MachineMain> machineMains = machineMainDao.selectBySearch(machineMainSearch);
            if (null == machineMains || machineMains.size()!=1){
                throw new Exception("未查询到机器信息");
            }
            MachineMain machineMain = machineMains.get(0);
            if (null == machineMain.getRoomInfoId()){
                throw new Exception("当前电脑未绑定房间信息");
            }
            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(machineMain.getHid());
            registSearch.setRoomNumId(machineMain.getRoomInfoId());
            registSearch.setState(0);
            List<Regist> regists = registDao.selectBySearch(registSearch);
            if (regists.size() != 1) {
                throw new Exception(ERROR_MSG.REGIST_MSG_NULL);
            }
                Regist regist = regists.get(0);
            /**
             * 3.查询入住人信息
             */
            RegistPersonSearch personSearch = new RegistPersonSearch();
            personSearch.setRegistId(regist.getRegistId());
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(personSearch);
            resultMap.put("registMsg", regist);
            resultMap.put("personMsg", registPeople);
            responseData.setData(resultMap);
        }catch (Exception e){
            responseData.setMsg("业务处理异常");
            responseData.setResult(ER.ERR);
            log.error("",e);
        }
        return  responseData;
    }

}
