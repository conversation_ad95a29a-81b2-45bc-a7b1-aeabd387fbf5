package com.pms.czabsorders.service.order.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.AddBookDTO;
import com.pms.czabsorders.bean.BookMsgForOtherPlatformDTO;
import com.pms.czabsorders.bean.BookingOtherPlat;
import com.pms.czabsorders.bean.CancelBookForOtherPlatPlatformDTO;
import com.pms.czabsorders.service.order.ItOrderService;
import com.pms.czabsorders.service.order.OrderService;
import com.pms.czabsorders.service.order.transaction.BookTransactionService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountCancel;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountCancelDao;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.price.RoomRateCode;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSearch;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomType;
import com.pms.czhotelfoundation.bean.room.search.RoomAuxiliaryRelationSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomTypeSearch;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeSpecificDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.RSAUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.BookingOrderRoomNum;
import com.pms.pmsorder.bean.BookingOrderRoomType;
import com.pms.pmsorder.bean.request.BookingOrderPageRequest;
import com.pms.pmsorder.bean.search.BookingOrderRoomNumSearch;
import com.pms.pmsorder.bean.search.BookingOrderRoomTypeSearch;
import com.pms.pmsorder.bean.search.BookingOrderSearch;
import com.pms.pmsorder.dao.BookingOrderDao;
import com.pms.pmsorder.dao.BookingOrderRoomNumDao;
import com.pms.pmsorder.dao.BookingOrderRoomTypeDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ItOrderServiceImpl extends BaseService implements ItOrderService {
    @Autowired
    private RoomRateCodeDao roomRateCodeDao;

    @Autowired
    private RoomRateCodeSpecificDao roomRateCodeSpecificDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private RoomTypeDao roomTypeDao;

    @Autowired
    private OrderService wxOrderService;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookTransactionService wxServiceTransaction;

    @Autowired
    private AccountCancelDao accountCancelDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    private BaseService baseService = this;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Override
    public ResponseData addBookForOtherPlatPlatform(AddBookDTO addBookRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken =  RSAUtils.getStringDecrypt(addBookRequest.getHotelId()); ;
            final TbUserSession user = this.getTbUserSession(sessionToken);

            RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
            roomInfoSearch.setHid(user.getHid());
            roomInfoSearch.setState(1);

            List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);

            Map<String, RoomInfo> roomInfoMap = roomInfos.stream().collect(Collectors.toMap(RoomInfo::getRoomNum, a -> a, (k1, k2) -> k1));

            RoomTypeSearch roomTypeSearch = new RoomTypeSearch();
            roomTypeSearch.setHid(user.getHid());

            Page<RoomType> roomTypes = roomTypeDao.selectBySearch(roomTypeSearch);
            Map<Integer, RoomType> roomTypeMap = roomTypes.stream().collect(Collectors.toMap(RoomType::getRoomTypeId, a -> a, (k1, k2) -> k1));

            RoomRateCodeSearch roomRateCodeSearch = new RoomRateCodeSearch();
            roomRateCodeSearch.setHid(user.getHid());
            List<RoomRateCode> roomRateCodes = roomRateCodeDao.selectBySearch(roomRateCodeSearch);

            Map<Integer, RoomRateCode> rateCodeMap = roomRateCodes.stream().collect(Collectors.toMap(RoomRateCode::getRateId, a -> a, (k1, k2) -> k1));


            String bookSummary = new String();

            List<AddBookDTO.RoomType> roomTypeList = addBookRequest.getRoomTypeList();
            addBookRequest.setIsGroup(1);
            addBookRequest.setBookingPhone(addBookRequest.getBookingPhone());
            if(addBookRequest.getPhone()!=null&&addBookRequest.getPhone().length()==11){
                addBookRequest.setBookingPhone(addBookRequest.getPhone());
            }

            List<AddBookDTO.RoomType> newRoomTypeList = new ArrayList<>();

            int num = 0;

            for(AddBookDTO.RoomType art: roomTypeList){

                RoomType roomType = roomTypeMap.get(art.getRoomTypeId());
                art.setRoomTypeName(roomType.getRoomTypeName());

                if(art.getRateId()<1){
                    art.setRateId(addBookRequest.getRateCodeId());
                    art.setRateCode(rateCodeMap.get(addBookRequest.getRateCodeId()).getRateCode());
                }

                bookSummary+=art.getRoomTypeName();
                bookSummary+="*";
                bookSummary+=art.getNum();
                bookSummary+="间。";
                num+=art.getNum();

                List<AddBookDTO.Room> roomList = art.getRoomList();
                if(roomList==null){
                    roomList = new ArrayList<>();
                }
                List<AddBookDTO.Room> newRoomList = new ArrayList<>();
                for(AddBookDTO.Room arm:roomList){
                    RoomInfo roomInfo = roomInfoMap.get(arm.getRoomNum());
                    arm.setRoomInfoId(roomInfo.getRoomInfoId());
                    newRoomList.add(arm);
                }

                art.setRoomList(newRoomList);
                newRoomTypeList.add(art);
            }

            addBookRequest.setRoomTypeList(newRoomTypeList);
            addBookRequest.setRoomTypeSummary(bookSummary);
            addBookRequest.setRoomCount(num);

            String encode = URLEncoder.encode(JSONObject.fromObject(addBookRequest).toString(), "utf-8");

            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ER.SESSION_TOKEN,sessionToken);
            jsonObject.put("bookData",encode);
            jsonObject.put("fromType",addBookRequest.getFromType());
            JSONObject param = wxOrderService.addBookNew(jsonObject);
            if(!param.getString(ER.RES).equals(ER.SUCC)){
                throw new Exception(param.getString(ER.MSG));
            }
            responseData.setData(param.get(ER.RES_DATA));

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData cancelBookForOtherPlatPlatform(CancelBookForOtherPlatPlatformDTO cancelBookForOtherPlatPlatformRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken =    RSAUtils.getStringDecrypt(cancelBookForOtherPlatPlatformRequest.getHotelId()); ;
            final TbUserSession user = this.getTbUserSession(sessionToken);

            BookingOrderSearch bookingOrderSearch = new BookingOrderSearch();
            bookingOrderSearch.setHid(user.getHid());
            Integer type = cancelBookForOtherPlatPlatformRequest.getType();
            if(type==2){
                bookingOrderSearch.setThirdPlatformOrderCode(cancelBookForOtherPlatPlatformRequest.getSn());
            }else {
                bookingOrderSearch.setBookingOrderId(cancelBookForOtherPlatPlatformRequest.getBookId());
            }


            List<BookingOrder> bookingOrders1 = bookingOrderDao.selectBySearch(bookingOrderSearch);
            BookingOrder bookingOrder = null;
            //1.查询预订单
            if(bookingOrders1.size()<1){
                throw new Exception("此订单有误");
            }
            bookingOrder = bookingOrders1.get(0);
            if (!user.getHid().equals(bookingOrder.getHid())) {
                throw new Exception("此订单有误");
            }
            if (bookingOrder.getOrderStatus() != BOOK.STA_YX && bookingOrder.getOrderStatus() != BOOK.STA_NS) {
                throw new Exception("当前订单状态不允许取消");
            }


            int bookingOrderId = bookingOrder.getBookingOrderId();


            bookingOrder.setCancelBookingLation(cancelBookForOtherPlatPlatformRequest.getReason());
            //查询当前订单的账务信息，如果账不平则不允许取消
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setBookingId(bookingOrderId);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            int cash = 0;
            int cost = 0;
            for (int i = 0; i < accounts.size(); i++) {
                Account account = accounts.get(i);

                Account upaAccount = new Account();
                upaAccount.setAccountId(account.getAccountId());
                upaAccount.setIsCancel(3);
                upaAccount.setRegistState(1);

                accountDao.editAccount(upaAccount);

                AccountCancel accountCancel = new AccountCancel();
                accountCancel.setAccountId(account.getAccountId());
                accountCancel.setPrice(account.getPrice());
                accountCancel.setPayType(account.getPayType());
                accountCancel.setHid(account.getHid());
                accountCancel.setHotelGroupId(account.getHotelGroupId());
                accountCancel.setPayClassId(account.getPayClassId());
                accountCancel.setPayClassName(account.getPayClassName());
                accountCancel.setPayCodeId(Integer.parseInt(account.getPayCodeId()));
                accountCancel.setPayCodeName(account.getPayCodeName());
                accountCancel.setRoomInfoId(account.getRoomInfoId());
                accountCancel.setRoomNum(account.getRoomNum());
                accountCancel.setIsSale(account.getIsSale());
                accountCancel.setBusinessDay(account.getBusinessDay());
                accountCancel.setClassId(user.getClassId());
                accountCancel.setCreateTime(new Date());
                accountCancel.setCreateUserId(user.getUserId());
                accountCancel.setCreateUserName(user.getUserName());
                accountCancel.setBookingId(account.getBookingId());
                accountCancel.setRegistId(account.getRegistId());
                accountCancel.setCancelType(2);
                accountCancel.setRemark(account.getRemark());
                accountCancel.setReason(cancelBookForOtherPlatPlatformRequest.getReason());

                accountCancelDao.insert(accountCancel);

            }

            //2.查询预订房型
            BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
            bookingOrderRoomTypeSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
            List<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);

            //3.查询预订房间
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingOrderId);
            List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            //4.查询当前预订单的辅助房态
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setBookingOrderId(bookingOrderId);
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

            ArrayList<BookingOrder> bookingOrders = new ArrayList<>();
            bookingOrders.add(bookingOrder);
            wxServiceTransaction.cancelOrderService(bookingOrders, bookingOrderRoomTypes, bookingOrderRoomNums, user, roomAuxiliaryRelations);
            // 更新房间缓存
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        baseService.turnAlways(user);
                        baseService.push(user.getHotelGroupId(), user.getHid(), 9, new HashMap<String, String>(), new HashMap<String, String>(), true, true);

                    } catch (Exception e) {

                    }
                }
            });

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData searchBookMsgForOtherPlatform(BookMsgForOtherPlatformDTO bookMsgForOtherPlatform){
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken =  RSAUtils.getStringDecrypt(bookMsgForOtherPlatform.getHotelId()); ;
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Integer bookId = bookMsgForOtherPlatform.getBookId();

            BookingOrder bookingOrder = bookingOrderDao.selectById(bookId);

            // 预订基础信息
            JSONObject bookJson = JSONObject.fromObject(bookingOrder);
            bookJson.put("acceptTime",HotelUtils.parseDate2Str(bookingOrder.getAcceptTime()));
            BookingOtherPlat bookingOtherPlat = (BookingOtherPlat) JSONObject.toBean(bookJson, BookingOtherPlat.class);
            if(bookingOtherPlat==null||!bookingOrder.getHid().equals(user.getHid())){
                throw new Exception("未查到订单信息");
            }
            bookingOtherPlat.setCheckinTime(HotelUtils.parseDate2Str(bookingOrder.getCheckinTime()));
            bookingOtherPlat.setCheckoutTime(HotelUtils.parseDate2Str(bookingOrder.getCheckoutTime()));
            // 预订房型
            BookingOrderRoomTypeSearch bookingOrderRoomTypeSearch = new BookingOrderRoomTypeSearch();
            bookingOrderRoomTypeSearch.setHid(user.getHid());
            bookingOrderRoomTypeSearch.setBookingOrderId(bookId);

            Page<BookingOrderRoomType> bookingOrderRoomTypes = bookingOrderRoomTypeDao.selectBySearch(bookingOrderRoomTypeSearch);

            // 预订房间
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setHid(user.getHid());
            bookingOrderRoomNumSearch.setBookingOrderId(bookId);

            Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
            Map<Integer, List<BookingOrderRoomNum>> roomMap = bookingOrderRoomNums.stream().collect(Collectors.groupingBy(BookingOrderRoomNum::getBookingOrderRoomTypeId));

            ArrayList<BookingOtherPlat.RoomType> roomTypes = new ArrayList<>();

            for (BookingOrderRoomType rt:bookingOrderRoomTypes){

                BookingOtherPlat.RoomType rtp = new BookingOtherPlat.RoomType();

                rtp.setOrderState(rt.getOrderState());
                rtp.setState(rt.getState());
                rtp.setId(rt.getId());
                rtp.setRoomTypeId(rt.getRoomTypeId());
                rtp.setRoomTypeNum(rt.getRoomTypeNum());
                rtp.setHasRoomNum(rt.getHasRoomNum());
                rtp.setPriceCodeId(rt.getPriceCodeId());
                rtp.setPriceCode(rt.getPriceCode());

                ArrayList<BookingOtherPlat.Room> rooms = new ArrayList<>();

                List<BookingOrderRoomNum> brns = roomMap.get(rt.getId());
                if(brns!=null){

                    for(BookingOrderRoomNum born:brns){

                        BookingOtherPlat.Room room = new BookingOtherPlat.Room();
                        room.setId(born.getId());
                        room.setBookingOrderRoomTypeId(born.getBookingOrderRoomTypeId());
                        room.setBookingOrderId(born.getBookingOrderId());
                        room.setHid(born.getHid());
                        room.setHotelGroupId(born.getHotelGroupId());
                        room.setRoomCode(born.getRoomCode());
                        room.setRoomTypeId(born.getRoomTypeId());
                        room.setRoomNumId(born.getRoomNumId());
                        room.setOrderState(born.getOrderState());
                        room.setRegistId(born.getRegistId());
                        room.setRoomNum(born.getRoomNum());
                        room.setIsCheckin(born.getIsCheckin());
                        room.setIsCheckout(born.getIsCheckout());
                        room.setCheckinTime(HotelUtils.parseDate2Str(born.getCheckinTime()));
                        room.setCheckoutTime(HotelUtils.parseDate2Str(born.getCheckoutTime()));
                        room.setRowRoom(born.getRowRoom());

                        rooms.add(room);

                    }

                }

                rtp.setRooms(rooms);

                roomTypes.add(rtp);

            }

            bookingOtherPlat.setRoomTypeList(roomTypes);
            responseData.setData(bookingOtherPlat);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData searchBooking(BookingOrderPageRequest bookingOrderPageRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            final TbUserSession user = this.getTbUserSession(RSAUtils.getStringDecrypt(bookingOrderPageRequest.getHotelId()));

            bookingOrderPageRequest.setHid(user.getHid());

            Page<BookingOrder> bookingOrders = bookingOrderDao.selectPageByRequest(bookingOrderPageRequest);

            responseData.setData(bookingOrders);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
