package com.pms.czabsorders.service.order.transaction.impl;

import com.pms.czabsorders.service.order.transaction.BookTransactionService;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliary;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.search.RoomAuxiliaryRelationSearch;
import com.pms.czhotelfoundation.dao.HourRoomDayUseDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.enums.HmhOrderStatusEnum;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.search.BookingOrderDailyPriceSearch;
import com.pms.pmsorder.dao.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Primary
@Slf4j
public class BookTransactionServiceImpl extends BaseService implements BookTransactionService {

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private RegistGroupDao registGroupDao;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private HourRoomDayUseDao hourRoomDayUseDao;

    /**
     * 添加预订单
     *
     * @param bookingOrder
     * @param registGroup
     * @param bookingOrderRoomTypes
     * @param roomAuxiliaryRelations
     * @param oprecords
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public Integer addBookService(BookingOrder bookingOrder, RegistGroup registGroup, List<BookingOrderRoomType> bookingOrderRoomTypes, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, ArrayList<Oprecord> oprecords, BookingOrderConfig bookingOrderConfig) throws Exception {

        bookingOrder.setBookingNamePyjp(HotelUtils.hypyToFirstChar(bookingOrder.getBookingName()));
        // 1.添加预订单
        Integer integer = bookingOrderDao.saveBookingOrder(bookingOrder);

        if (integer < 1) {
            throw new Exception("添加预订单失败");
        }

        // 添加团队
        if (registGroup != null) {
            registGroup.setBookingOrderId(bookingOrder.getBookingOrderId());
            Integer insert = registGroupDao.insert(registGroup);
            if (insert < 1) {
                throw new Exception("添加团队信息失败");
            }
        }

        ArrayList<BookingOrderDailyPrice> addPrice = new ArrayList<>();

        // 添加预订房型
        for (BookingOrderRoomType bort : bookingOrderRoomTypes) {

            bort.setBookingOrderId(bookingOrder.getBookingOrderId());
            Integer integer1 = bookingOrderRoomTypeDao.saveBookingOrderRoomType(bort);

            if (integer1 < 1) {
                throw new Exception("添加预订房型：" + bort.getRoomTypeId() + "失败");
            }

            // 添加预订房型价格
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bort.getBookingOrderDailyPrices();

            for (BookingOrderDailyPrice bookingOrderDailyPrice : bookingOrderDailyPrices) {

                bookingOrderDailyPrice.setBookingOrderId(bookingOrder.getBookingOrderId());

                Integer integer2 = bookingOrderDailyPriceDao.saveBookingOrderDailyPrice(bookingOrderDailyPrice);

                if (integer2 < 1) {
                    throw new Exception("添加预订房型价格：" + bort.getRoomTypeId() + "---" + bookingOrderDailyPrice.getDailyTime() + "失败");
                }

            }

            // 添加预订房间
            List<BookingOrderRoomNum> bookingOrderRoomNums = bort.getBookingOrderRoomNums();

            for (BookingOrderRoomNum born : bookingOrderRoomNums) {

                born.setBookingOrderId(bookingOrder.getBookingOrderId());
                born.setBookingOrderRoomTypeId(bort.getId());

                Integer integer2 = bookingOrderRoomNumDao.saveBookingOrderRoomNum(born);

                if (integer2 < 1) {
                    throw new Exception("添加预订房房间：" + born.getRoomNum() + "失败");
                }

                Integer rowRoom = born.getRowRoom();

                ArrayList<BookingOrderDailyPrice> bodpRoom = born.getBookingOrderDailyPrices();

                for (BookingOrderDailyPrice bookingOrderDailyPrice : bodpRoom) {


                    BookingOrderDailyPrice addPriceObj = new BookingOrderDailyPrice();
                    BeanUtils.copyProperties(bookingOrderDailyPrice, addPriceObj);

                    addPriceObj.setBookingOrderId(bookingOrder.getBookingOrderId());
                    addPriceObj.setBookingOrderRoomNumId(born.getId());
                    addPriceObj.setRoomNumId(born.getRoomNumId());

                    addPrice.add(addPriceObj);
                }

            }


        }

        bookingOrderDailyPriceDao.addPriceList(addPrice);

        // 添加辅助房态
        for (RoomAuxiliaryRelation roomAuxiliary : roomAuxiliaryRelations) {
            roomAuxiliary.setBookingOrderId(bookingOrder.getBookingOrderId());
            roomAuxiliaryRelationDao.saveRoomAuxiliaryRelation(roomAuxiliary);
        }

        bookingOrderConfig.setBookingOrderId(bookingOrder.getBookingOrderId());
        Integer saveBookingOrderConfig = bookingOrderConfigDao.saveBookingOrderConfig(bookingOrderConfig);
        if (saveBookingOrderConfig < 1) {
            throw new Exception("添加预订设置失败");
        }

        for (int i = 0; i < oprecords.size(); i++) {
            oprecords.get(i).setBookingOrderId(bookingOrder.getBookingOrderId());
        }

        return bookingOrder.getBookingOrderId();
    }


    @Override
    public Integer addBookHourService(BookingOrder bookingOrder, RegistGroup registGroup, List<BookingOrderRoomType> bookingOrderRoomTypes, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, ArrayList<Oprecord> oprecords, BookingOrderConfig bookingOrderConfig, ArrayList<HourRoomDayUse> addHourUse, ArrayList<HourRoomDayUse> upaHourUse) throws Exception {
        bookingOrder.setBookingNamePyjp(HotelUtils.hypyToFirstChar(bookingOrder.getBookingName()));
        // 1.添加预订单
        Integer integer = bookingOrderDao.saveBookingOrder(bookingOrder);

        if (integer < 1) {
            throw new Exception("添加预订单失败");
        }

        // 添加团队
        if (registGroup != null) {
            registGroup.setBookingOrderId(bookingOrder.getBookingOrderId());
            Integer insert = registGroupDao.insert(registGroup);
            if (insert < 1) {
                throw new Exception("添加团队信息失败");
            }
        }

        ArrayList<BookingOrderDailyPrice> addPrice = new ArrayList<>();

        // 添加预订房型
        for (BookingOrderRoomType bort : bookingOrderRoomTypes) {

            bort.setBookingOrderId(bookingOrder.getBookingOrderId());
            Integer integer1 = bookingOrderRoomTypeDao.saveBookingOrderRoomType(bort);

            if (integer1 < 1) {
                throw new Exception("添加预订房型：" + bort.getRoomTypeId() + "失败");
            }

            // 添加预订房型价格
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bort.getBookingOrderDailyPrices();

            for (BookingOrderDailyPrice bookingOrderDailyPrice : bookingOrderDailyPrices) {

                bookingOrderDailyPrice.setBookingOrderId(bookingOrder.getBookingOrderId());

                Integer integer2 = bookingOrderDailyPriceDao.saveBookingOrderDailyPrice(bookingOrderDailyPrice);

                if (integer2 < 1) {
                    throw new Exception("添加预订房型价格：" + bort.getRoomTypeId() + "---" + bookingOrderDailyPrice.getDailyTime() + "失败");
                }

            }

            // 添加预订房间
            List<BookingOrderRoomNum> bookingOrderRoomNums = bort.getBookingOrderRoomNums();

            for (BookingOrderRoomNum born : bookingOrderRoomNums) {

                born.setBookingOrderId(bookingOrder.getBookingOrderId());
                born.setBookingOrderRoomTypeId(bort.getId());

                Integer integer2 = bookingOrderRoomNumDao.saveBookingOrderRoomNum(born);

                if (integer2 < 1) {
                    throw new Exception("添加预订房房间：" + born.getRoomNum() + "失败");
                }

                Integer rowRoom = born.getRowRoom();

                ArrayList<BookingOrderDailyPrice> bodpRoom = born.getBookingOrderDailyPrices();

                for (BookingOrderDailyPrice bookingOrderDailyPrice : bodpRoom) {


                    BookingOrderDailyPrice addPriceObj = new BookingOrderDailyPrice();
                    BeanUtils.copyProperties(bookingOrderDailyPrice, addPriceObj);

                    addPriceObj.setBookingOrderId(bookingOrder.getBookingOrderId());
                    addPriceObj.setBookingOrderRoomNumId(born.getId());
                    addPriceObj.setRoomNumId(born.getRoomNumId());

                    addPrice.add(addPriceObj);
                }

            }


        }

        bookingOrderDailyPriceDao.addPriceList(addPrice);

        // 添加辅助房态
        for (RoomAuxiliaryRelation roomAuxiliary : roomAuxiliaryRelations) {
            roomAuxiliary.setBookingOrderId(bookingOrder.getBookingOrderId());
            roomAuxiliaryRelationDao.saveRoomAuxiliaryRelation(roomAuxiliary);
        }

        bookingOrderConfig.setBookingOrderId(bookingOrder.getBookingOrderId());
        Integer saveBookingOrderConfig = bookingOrderConfigDao.saveBookingOrderConfig(bookingOrderConfig);
        if (saveBookingOrderConfig < 1) {
            throw new Exception("添加预订设置失败");
        }

        if (addHourUse.size() > 0) {

            for (HourRoomDayUse hourRoomDayUse : addHourUse) {
                if (hourRoomDayUse.getRoomInfoId() == null || hourRoomDayUse.getRoomNo() == null) {
                    continue;
                }
                hourRoomDayUseDao.insert(hourRoomDayUse);

            }

        }

        if (upaHourUse.size() > 0) {
            for (HourRoomDayUse hourRoomDayUse : upaHourUse) {
                String useMsg = hourRoomDayUse.getUseMsg();
                if (useMsg.length() < 1) {
                    hourRoomDayUseDao.delete(hourRoomDayUse.getId());
                } else {
                    hourRoomDayUseDao.update(hourRoomDayUse);
                }
            }
        }

        return bookingOrder.getBookingOrderId();
    }

    /**
     * 修改预订房型
     *
     * @param updateRoomType
     * @param addRoomType
     * @param deleteRoomType
     * @param addRooms
     * @param deleteRooms
     * @param addPrices
     * @param deletePrices
     * @param addAuxi
     * @param deleteAuxi
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public void updateBookService(BookingOrder bookingOrder, List<BookingOrderRoomType> updateRoomType, List<BookingOrderRoomType> addRoomType, List<BookingOrderRoomType> deleteRoomType,
                                  List<BookingOrderRoomNum> addRooms, List<BookingOrderRoomNum> deleteRooms, List<BookingOrderDailyPrice> addPrices, List<BookingOrderDailyPrice> deletePrices, List<RoomAuxiliaryRelation> addAuxi,
                                  List<RoomAuxiliaryRelation> deleteAuxi, List<BookingOrderRoomNum> updateRoomNums) throws Exception {

        Date date = new Date();
        if (bookingOrder.getBookingPhone() != null) {
            if (bookingOrder.getBookingPhone().equals("null")) {
                bookingOrder.setBookingPhone("");
            }
        }
        Integer integer3 = bookingOrderDao.editBookingOrder(bookingOrder);

        if (integer3 < 1) {
            throw new Exception("修改预订主单失败");
        }

        // 1.修改预订房型
        for (BookingOrderRoomType rt : updateRoomType) {
            Integer integer = bookingOrderRoomTypeDao.editBookingOrderRoomType(rt);
            if (integer < 1) {
                throw new Exception("排房房型失败：" );
            }
        }

        log.info("修改主单----------:" + (new Date().getTime() - date.getTime()));


        // 7.删除房间价格
        if (deletePrices.size() > 0) {
            bookingOrderDailyPriceDao.deletePriceList(deletePrices);
        }

        // 2.添加预订房型
        for (BookingOrderRoomType rt : addRoomType) {

            Integer integer = bookingOrderRoomTypeDao.saveBookingOrderRoomType(rt);

            if (integer < 1) {
                throw new Exception("新增房型失败：" );
            }

            List<BookingOrderDailyPrice> bookingOrderDailyPrices1 = rt.getBookingOrderDailyPrices();
            addPrices.addAll(bookingOrderDailyPrices1);
          /*  for(BookingOrderDailyPrice bodp:bookingOrderDailyPrices1){

                Integer integer2 = bookingOrderDailyPriceDao.saveBookingOrderDailyPrice(bodp);

            }*/
            // 添加预订房间
            List<BookingOrderRoomNum> bookingOrderRoomNums = rt.getBookingOrderRoomNums();

            for (BookingOrderRoomNum borm : bookingOrderRoomNums) {

                borm.setBookingOrderRoomTypeId(rt.getId());

                Integer integer1 = bookingOrderRoomNumDao.saveBookingOrderRoomNum(borm);

                if (integer1 < 1) {
                    throw new Exception("新增排房失败：" );
                }

                // 添加预订价格
                ArrayList<BookingOrderDailyPrice> bookingOrderDailyPrices = borm.getBookingOrderDailyPrices();
                if (bookingOrderDailyPrices == null) {
                    continue;
                }


                for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {
                    BookingOrderDailyPrice bookingOrderDailyPrice = new BookingOrderDailyPrice();
                    bookingOrderDailyPrice.setId(null);
                    bookingOrderDailyPrice.setBookingOrderId(bodp.getBookingOrderId());
                    bookingOrderDailyPrice.setBookingOrderRoomNumId(borm.getId());
                    bookingOrderDailyPrice.setHid(bodp.getHid());
                    bookingOrderDailyPrice.setHotelGroupId(bodp.getHotelGroupId());
                    bookingOrderDailyPrice.setPrice(bodp.getPrice());
                    bookingOrderDailyPrice.setDailyTime(bodp.getDailyTime());
                    bookingOrderDailyPrice.setRoomTypeId(bodp.getRoomTypeId());
                    bookingOrderDailyPrice.setRoomNumId(0);
                    bookingOrderDailyPrice.setDailyState(1);
                    bookingOrderDailyPrice.setIsStayover(0);
                    bookingOrderDailyPrice.setCreateTime(date);
                    bookingOrderDailyPrice.setCreateUserId(bodp.getCreateUserId());
                    bookingOrderDailyPrice.setCreateUserName(bodp.getCreateUserName());
                    bookingOrderDailyPrice.setUpdateTime(date);
                    bookingOrderDailyPrice.setUpdateUserId(bodp.getUpdateUserId());
                    bookingOrderDailyPrice.setUpdateUserName(bodp.getUpdateUserName());

                    bookingOrderDailyPrice.setBookingOrderRoomNumId(borm.getId());
                    bookingOrderDailyPrice.setRoomNumId(borm.getRoomNumId());
                    addPrices.add(bookingOrderDailyPrice);
                }

            }

        }


        // 3.删除预订房型
        for (BookingOrderRoomType rt : deleteRoomType) {

            rt.setOrderState(BOOK.STA_YQX);
            Integer integer = bookingOrderRoomTypeDao.editBookingOrderRoomType(rt);
            if (integer < 1) {
                throw new Exception("取消预订房型失败：" );
            }

        }

        // 4.新增预订房间
        for (BookingOrderRoomNum born : addRooms) {
            Integer integer = bookingOrderRoomNumDao.saveBookingOrderRoomNum(born);
            if (integer < 1) {
                throw new Exception("新增排房失败：");
            }

            if (born.getRoomNumId() == 0) {
                continue;
            }

            // 添加预订价格
            ArrayList<BookingOrderDailyPrice> bookingOrderDailyPrices = born.getBookingOrderDailyPrices();

            for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {
                BookingOrderDailyPrice bookingOrderDailyPrice = new BookingOrderDailyPrice();
                bookingOrderDailyPrice.setBookingOrderId(bodp.getBookingOrderId());
                bookingOrderDailyPrice.setBookingOrderRoomNumId(born.getId());
                bookingOrderDailyPrice.setHid(bodp.getHid());
                bookingOrderDailyPrice.setHotelGroupId(bodp.getHotelGroupId());
                bookingOrderDailyPrice.setPrice(bodp.getPrice());
                bookingOrderDailyPrice.setDailyTime(bodp.getDailyTime());
                bookingOrderDailyPrice.setRoomTypeId(bodp.getRoomTypeId());
                bookingOrderDailyPrice.setRoomNumId(0);
                bookingOrderDailyPrice.setDailyState(1);
                bookingOrderDailyPrice.setIsStayover(0);
                bookingOrderDailyPrice.setCreateTime(date);
                bookingOrderDailyPrice.setCreateUserId(bodp.getCreateUserId());
                bookingOrderDailyPrice.setCreateUserName(bodp.getCreateUserName());
                bookingOrderDailyPrice.setUpdateTime(date);
                bookingOrderDailyPrice.setUpdateUserId(bodp.getUpdateUserId());
                bookingOrderDailyPrice.setUpdateUserName(bodp.getUpdateUserName());
                bookingOrderDailyPrice.setBookingOrderRoomNumId(born.getId());
                bookingOrderDailyPrice.setRoomNumId(born.getRoomNumId());
                addPrices.add(bookingOrderDailyPrice);
            }

        }

        // 6.添加房间价格
        bookingOrderDailyPriceDao.addPriceList(addPrices);


        // 5.取消预订房间
        if (deleteRooms.size() > 0) {
            bookingOrderRoomNumDao.deleteBookingOrderRoomNumList(deleteRooms);
        }

        /*for(BookingOrderRoomNum born:deleteRooms){
            Integer integer = bookingOrderRoomNumDao.deleteBookingOrderRoomNum(born.getId());
        }*/


        // 8.添加辅助房态
        if (addAuxi.size() > 0) {
            roomAuxiliaryRelationDao.addRoomAuxiliaryRelationList(addAuxi);
        }

       /* for(RoomAuxiliaryRelation rar:addAuxi){
            roomAuxiliaryRelationDao.saveRoomAuxiliaryRelation(rar);
        }*/

        // 9.删除辅助房态
        if (deleteAuxi.size() > 0) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(deleteAuxi);
        }

        // 修改预订房间  时间
        if (updateRoomNums.size() > 0) {
            HashMap<String, List> stringListHashMap = new HashMap<>();
            stringListHashMap.put("list", updateRoomNums);
            bookingOrderRoomNumDao.updateBookingOrderRoomNumList(stringListHashMap);
        }


    }


    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public void addBookRoomList(ArrayList<RoomAuxiliaryRelation> deleteRoomAuRea, ArrayList<RoomAuxiliaryRelation> addRoomAuRea, ArrayList<RoomAuxiliaryRelation> upaRoomAuRea,
                                ArrayList<BookingOrderDailyPrice> deletePrices, ArrayList<BookingOrderDailyPrice> addPrices, ArrayList<BookingOrderDailyPrice> updaPrices,
                                ArrayList<BookingOrderRoomNum> upaRooms, Map<Integer, BookingOrderRoomType> roomTypeMap) throws Exception {

        Set<Integer> integers = roomTypeMap.keySet();
        for (Integer key : integers) {

            BookingOrderRoomType bookingOrderRoomType = roomTypeMap.get(key);

            Integer integer = bookingOrderRoomTypeDao.editBookingOrderRoomType(bookingOrderRoomType);

            if (integer < 0) {
                throw new Exception("修改预订房型：" + bookingOrderRoomType.getRoomTypeId() + " 失败");
            }

        }

        if (upaRooms.size() > 0) {
            HashMap<String, List> stringListHashMap = new HashMap<>();
            stringListHashMap.put("list", upaRooms);
            bookingOrderRoomNumDao.updateBookingOrderRoomNumList(stringListHashMap);
        }

        // 删除辅助房态
        if (deleteRoomAuRea.size() > 0) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(deleteRoomAuRea);
        }
        // 添加辅助房态
        if (addRoomAuRea.size() > 0) {
            roomAuxiliaryRelationDao.addRoomAuxiliaryRelationList(addRoomAuRea);
        }
        // 修改辅助房态
        if (upaRoomAuRea.size() > 0) {
            roomAuxiliaryRelationDao.editRoomAuxiliaryRelationList(upaRoomAuRea);
        }


        // 删除房价
        if (deletePrices.size() > 0) {
            bookingOrderDailyPriceDao.deletePriceList(deletePrices);
        }
        if (addPrices.size() > 0) {
            bookingOrderDailyPriceDao.addPriceList(addPrices);
        }
        if (updaPrices.size() > 0) {
            bookingOrderDailyPriceDao.updatePriceList(updaPrices);
        }


    }


    /**
     * 删除预订房间
     *
     * @param bookingOrderRoomNum
     */
    @Override
    public void deleteBookRoomNum(BookingOrderRoomNum bookingOrderRoomNum) throws Exception {

        Integer integer = bookingOrderRoomNumDao.deleteBookingOrderRoomNum(bookingOrderRoomNum.getId());

        if (integer < 1) {
            throw new Exception("取消预订房间失败:" + bookingOrderRoomNum.getRoomNumId());
        }

        /**
         * 查询辅助房态
         */
        RoomAuxiliaryRelationSearch roomAuxiliarySearch = new RoomAuxiliaryRelationSearch();
        roomAuxiliarySearch.setBookingOrderId(bookingOrderRoomNum.getBookingOrderId());
        roomAuxiliarySearch.setRoomId(bookingOrderRoomNum.getRoomNumId());
        List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliarySearch);

        //删除当前相关的辅助房态
        for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRoomAuxiliaryId());
        }

        BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
        bookingOrderDailyPriceSearch.setBookingOrderRoomNumId(bookingOrderRoomNum.getId());

        List<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

        for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {
            bookingOrderDailyPriceDao.deleteBookingOrderDailyPrice(bodp.getId());
        }

    }

    /**
     * 排房的数据库操作
     *
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void addBookOrderRoomService(RoomInfo roomInfo, BookingOrderRoomNum bookingOrderRoomNum, TbUserSession user, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, RoomAuxiliary auxiliary, BookingOrderRoomType bookingOrderRoomType, List<BookingOrderDailyPrice> bookingOrderDailyPrices, List<BookingOrderDailyPrice> addBookingOrderDailyPrices) throws Exception {

        Oprecord oprecord = new Oprecord(user);
        oprecord.setBookingOrderId(bookingOrderRoomNum.getBookingOrderId());
        oprecord.setOccurTime(HotelUtils.currentTime());
        oprecord.setSourceValue(bookingOrderRoomNum.getId() + "");
        oprecord.setChangedValue("bookroomid:" + bookingOrderRoomNum.getId() + "");

        if (roomInfo.getRoomInfoId() == 0) {
            /*
             * 如果原排房id为空
             *      则跳出
             */
            if (bookingOrderRoomNum.getRoomNumId() == null || bookingOrderRoomNum.getRoomNumId() == 0) {
                return;
            }

            oprecord.setDescription("取消分房 : " + bookingOrderRoomNum.getRoomNum() + " ");

            bookingOrderRoomNum.setRowRoom(0);
            bookingOrderRoomNum.setRoomNumId(0);
            bookingOrderRoomNum.setRoomNum("0");

            Integer integer = bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNum);

            if (integer < 1) {
                throw new Exception("取消排房失败");
            }
            //删除当前相关的辅助房态
            for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {
                roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());
            }

        /*    //修改排房数量
            bookingOrderRoomType.setHasRoomNum(bookingOrderRoomType.getHasRoomNum() - 1);
            bookingOrderRoomTypeDao.editBookingOrderRoomType(bookingOrderRoomType);*/
            this.addOprecords(oprecord);


            /**
             * 取消排房，删除之前的每日房价
             */
/*
            for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
                BookingOrderDailyPrice bookingOrderDailyPrice = bookingOrderDailyPrices.get(i);
                integer = bookingOrderDailyPriceDao.deleteBookingOrderDailyPrice(bookingOrderDailyPrice.getId());
                if (integer < 1){
                    throw new Exception("删除之前排房的每日房价失败");
                }
            }*/
            return;
        }

        Boolean newRoom = false;
        //排房
        if (bookingOrderRoomNum.getRowRoom() == 0) {

            newRoom = true;

            //更改预订房型
      /*      bookingOrderRoomType.setHasRoomNum(bookingOrderRoomType.getHasRoomNum() + 1);
            bookingOrderRoomTypeDao.editBookingOrderRoomType(bookingOrderRoomType);*/

        }

        /**
         * 4.更改排房
         */
        oprecord.setDescription("将房间 " + bookingOrderRoomNum.getRoomNum() + " 改为:" + roomInfo.getRoomNum());
        bookingOrderRoomNum.setRowRoom(1);
        bookingOrderRoomNum.setRoomNumId(roomInfo.getRoomInfoId());
        bookingOrderRoomNum.setRoomNum(roomInfo.getRoomNum());
        bookingOrderRoomNum.setRoomTypeId(roomInfo.getRoomTypeId());
        bookingOrderRoomNum.setOrderState(1);

        Integer integer = bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNum);


        if (integer < 1) {
            throw new Exception("取消排房失败");
        }

        /**
         * 5.更新预订每日房价记录
         */

        for (int i = 0; i < addBookingOrderDailyPrices.size(); i++) {
            BookingOrderDailyPrice bookingOrderDailyPrice = addBookingOrderDailyPrices.get(i);
            bookingOrderDailyPrice.setRoomTypeId(bookingOrderRoomNum.getRoomTypeId());
            bookingOrderDailyPrice.setRoomNumId(bookingOrderRoomNum.getRoomNumId());
            integer = bookingOrderDailyPriceDao.editBookingOrderDailyPrice(bookingOrderDailyPrice);

            if (integer < 1) {
                throw new Exception("修改每日房价失败");
            }
        }


    }

    /**
     * 取消订单
     *
     * @param bookingOrders
     * @param bookingOrderRoomTypes
     * @param bookingOrderRoomNums
     * @param user
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public void cancelOrderService(List<BookingOrder> bookingOrders, List<BookingOrderRoomType> bookingOrderRoomTypes, List<BookingOrderRoomNum> bookingOrderRoomNums, TbUserSession user, List<RoomAuxiliaryRelation> roomAuxiliaryRelations) throws Exception {

        ArrayList<Oprecord> oprecords = new ArrayList<>();

        for (BookingOrder bookingOrder : bookingOrders) {
            Oprecord oprecord = new Oprecord(user);
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setSourceValue(bookingOrder.getOrderStatus() + "");
            oprecord.setChangedValue(BOOK.STA_YQX + "");
            oprecord.setMainId(bookingOrder.getSn());
            bookingOrder.setOrderStatus(BOOK.STA_YQX);
            bookingOrder.setUpdateCalssId(user.getClassId());
            bookingOrder.setUpdateUserId(user.getUserId());
            bookingOrder.setUpdateUserName(user.getUserName());
            //1.取消预订主单
            Integer boState = bookingOrderDao.editBookingOrder(bookingOrder);
            if (boState < 1) {
                throw new Exception("取消预订失败。bid:" + bookingOrder.getBookingOrderId());
            }

            oprecord.setDescription("将订单:" + bookingOrder.getSn() + " 改为取消状态");
            oprecords.add(oprecord);
        }

        //2.取消预订房型
        for (BookingOrderRoomType bookingOrderRoomType : bookingOrderRoomTypes) {
            bookingOrderRoomType.setOrderState(BOOK.STA_YQX);
            Integer brtState = bookingOrderRoomTypeDao.editBookingOrderRoomType(bookingOrderRoomType);
            if (brtState < 1) {
                throw new Exception("取消预定房型失败。bid:" + bookingOrderRoomType.getBookingOrderId() + ",brid:" + bookingOrderRoomType.getId());
            }
        }

        //3.取消预订房间
        for (BookingOrderRoomNum bookingOrderRoomNum : bookingOrderRoomNums) {
            bookingOrderRoomNum.setOrderState(BOOK.STA_YQX);
            Integer brnState = bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNum);
            if (brnState < 1) {
                throw new Exception("取消预定房间失败。bid:" + bookingOrderRoomNum.getBookingOrderId() + ",brid:" + bookingOrderRoomNum.getId() + ",brnid:" + bookingOrderRoomNum.getId());
            }
        }

        //4.取消辅助房态
        for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {
            Integer integer = roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());
            if (integer < 1) {
                throw new Exception("取消辅助房态失败:" + roomAuxiliaryRelation.getRelationId());
            }
        }


        this.addOprecords(oprecords);

    }

    @Override
    public void cancelHourOrderService(List<BookingOrder> bookingOrders, List<BookingOrderRoomType> bookingOrderRoomTypes, List<BookingOrderRoomNum> bookingOrderRoomNums, TbUserSession user, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, ArrayList<HourRoomDayUse> upaHourUse, ArrayList<HourRoomDayUse> delHourUse) throws Exception {
        ArrayList<Oprecord> oprecords = new ArrayList<>();

        for (BookingOrder bookingOrder : bookingOrders) {
            Oprecord oprecord = new Oprecord(user);
            oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setSourceValue(bookingOrder.getOrderStatus() + "");
            oprecord.setChangedValue(BOOK.STA_YQX + "");
            oprecord.setMainId(bookingOrder.getSn());
            bookingOrder.setOrderStatus(BOOK.STA_YQX);
            bookingOrder.setUpdateCalssId(user.getClassId());
            bookingOrder.setUpdateUserId(user.getUserId());
            bookingOrder.setUpdateUserName(user.getUserName());
            bookingOrder.setOtaStatus(HmhOrderStatusEnum.ORDER_CANCELLED.getType());
            //1.取消预订主单
            Integer boState = bookingOrderDao.editBookingOrder(bookingOrder);
            if (boState < 1) {
                throw new Exception("取消预订失败。bid:" + bookingOrder.getBookingOrderId());
            }

            oprecord.setDescription("将订单:" + bookingOrder.getSn() + " 改为取消状态");
            oprecords.add(oprecord);
        }

        //2.取消预订房型
        for (BookingOrderRoomType bookingOrderRoomType : bookingOrderRoomTypes) {
            bookingOrderRoomType.setOrderState(BOOK.STA_YQX);
            Integer brtState = bookingOrderRoomTypeDao.editBookingOrderRoomType(bookingOrderRoomType);
            if (brtState < 1) {
                throw new Exception("取消预定房型失败。bid:" + bookingOrderRoomType.getBookingOrderId() + ",brid:" + bookingOrderRoomType.getId());
            }
        }

        //3.取消预订房间
        for (BookingOrderRoomNum bookingOrderRoomNum : bookingOrderRoomNums) {
            bookingOrderRoomNum.setOrderState(BOOK.STA_YQX);
            Integer brnState = bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNum);
            if (brnState < 1) {
                throw new Exception("取消预定房间失败。bid:" + bookingOrderRoomNum.getBookingOrderId() + ",brid:" + bookingOrderRoomNum.getId() + ",brnid:" + bookingOrderRoomNum.getId());
            }
        }

        //4.取消辅助房态
        for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {
            Integer integer = roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());
            if (integer < 1) {
                throw new Exception("取消辅助房态失败:" + roomAuxiliaryRelation.getRelationId());
            }
        }


        if (upaHourUse.size() > 0) {
            for (HourRoomDayUse hourRoomDayUse : upaHourUse) {
                hourRoomDayUseDao.update(hourRoomDayUse);
            }
        }
        if (delHourUse.size() > 0) {

            for (int i = 0; i < delHourUse.size(); i++) {
                hourRoomDayUseDao.delete(delHourUse.get(i).getId());
            }

        }

        this.addOprecords(oprecords);
    }

    /**
     * 恢复已取消的预订
     *
     * @param bookingOrder
     * @param bookingOrderRoomTypes
     * @param bookingOrderRoomNums
     * @param user
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    @Override
    public void orderRecoveryService(BookingOrder bookingOrder, List<BookingOrderRoomType> bookingOrderRoomTypes, List<BookingOrderRoomNum> bookingOrderRoomNums, TbUserSession user, RoomAuxiliary auxiliary) throws Exception {
        Oprecord oprecord = new Oprecord(user);
        oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
        oprecord.setOccurTime(HotelUtils.currentTime());
        oprecord.setSourceValue(bookingOrder.getOrderStatus() + "");
        oprecord.setChangedValue(BOOK.STA_YX + "");
        oprecord.setMainId(bookingOrder.getSn());

        //1.恢复预订主单
        bookingOrder.setOrderStatus(BOOK.STA_YX);
        Integer boState = bookingOrderDao.editBookingOrder(bookingOrder);
        if (boState < 1) {
            throw new Exception("恢复预订失败。bid:" + bookingOrder.getBookingOrderId());
        }

        //2.恢复预订房型
        for (BookingOrderRoomType bookingOrderRoomType : bookingOrderRoomTypes) {
            bookingOrderRoomType.setOrderState(BOOK.STA_YX);
            Integer brtState = bookingOrderRoomTypeDao.editBookingOrderRoomType(bookingOrderRoomType);
            if (brtState < 1) {
                throw new Exception("恢复预定房型失败。bid:" + bookingOrder.getBookingOrderId() + ",brid:" + bookingOrderRoomType.getId());
            }
        }

        //3.恢复预订房间
        RoomAuxiliaryRelation rar = new RoomAuxiliaryRelation();
        rar.setHid(user.getHid());
        rar.setHotelGroupId(user.getHotelGroupId());
        rar.setRoomAuxiliaryId(auxiliary.getRoomAuxiliaryId());
        rar.setSort(auxiliary.getSort());

        for (BookingOrderRoomNum bookingOrderRoomNum : bookingOrderRoomNums) {
            bookingOrderRoomNum.setOrderState(BOOK.STA_YX);
            Integer brnState = bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNum);
            if (brnState < 1) {
                throw new Exception("恢复预定房间失败。bid:" + bookingOrder.getBookingOrderId() + ",brid:" + bookingOrderRoomNum.getId() + ",brnid:" + bookingOrderRoomNum.getId());
            }

            //没有分房则不添加辅助房态
            if (bookingOrderRoomNum.getRowRoom() == 0) {
                continue;
            }
            rar.setRelationId(null);
            rar.setRoomId(bookingOrderRoomNum.getRoomNumId());
            rar.setRoomNum(bookingOrderRoomNum.getRoomNum());
            rar.setBookingOrderId(bookingOrderRoomNum.getBookingOrderId());

            Integer integer = roomAuxiliaryRelationDao.saveRoomAuxiliaryRelation(rar);

            if (integer < 1) {
                throw new Exception("添加辅助房态失败。bid:" + bookingOrder.getBookingOrderId() + ",brid:" + bookingOrderRoomNum.getId() + ",brnid:" + bookingOrderRoomNum.getId());
            }

        }
        oprecord.setDescription("将订单:" + bookingOrder.getSn() + " 改为有效状态");
        this.addOprecords(oprecord);
    }


    /**
     * 取消预订分房
     *
     * @param bookingOrder
     * @param bookingOrderRoomNums
     * @param user
     * @param roomAuxiliaryRelations
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void cancelBookingRoomNoTran(BookingOrder bookingOrder, List<BookingOrderRoomNum> bookingOrderRoomNums, TbUserSession user, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, List<BookingOrderDailyPrice> bookingOrderDailyPrices) throws Exception {
        Oprecord oprecord = new Oprecord(user);
        oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
        oprecord.setOccurTime(HotelUtils.currentTime());
        oprecord.setChangedValue("取消预订分房");
        oprecord.setMainId(bookingOrder.getSn());


        for (int i = 0; i < bookingOrderRoomNums.size(); i++) {
            Integer integer = bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNums.get(i));
            if (integer < 1) {
                throw new Exception("修改预订排房信息失败");
            }
        }

        //新版本取消了预订房型信息
//        for (int i = 0; i < bookingOrderRoomTypes.size(); i++) {
//            Integer integer = bookingOrderRoomTypeDao.editBookingOrderRoomType(bookingOrderRoomTypes.get(i));
//            if (integer < 1){
//                throw new Exception("修改预订房型信息失败");
//            }
//        }

        for (int i = 0; i < roomAuxiliaryRelations.size(); i++) {
            Integer integer = roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelations.get(i).getRelationId());
            if (integer < 1) {
                throw new Exception("删除辅助房态失败");
            }
        }

        for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
            Integer integer = bookingOrderDailyPriceDao.deleteBookingOrderDailyPrice(bookingOrderDailyPrices.get(i).getId());
            if (integer < 1) {
                throw new Exception("删除之前的每日房价失败");
            }
        }

        oprecord.setDescription("取消预订分房");
        this.addOprecords(oprecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void updateBookRoomFunc(List<BookingOrderRoomNum> upaRooms, List<BookingOrderDailyPrice> addPrices, List<BookingOrderDailyPrice> deletePrices, List<BookingOrderDailyPrice> upaPrices) throws Exception {

        if (upaRooms.size() > 0) {
            HashMap<String, List> stringListHashMap = new HashMap<>();
            stringListHashMap.put("list", upaRooms);
            bookingOrderRoomNumDao.updateBookingOrderRoomNumList(stringListHashMap);
        }

        if (addPrices.size() > 0) {
            bookingOrderDailyPriceDao.addPriceList(addPrices);
        }
        if (deletePrices.size() > 0) {
            bookingOrderDailyPriceDao.deletePriceList(deletePrices);
        }

        if (upaPrices.size() > 0) {
            bookingOrderDailyPriceDao.updatePriceList(upaPrices);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, transactionManager = "transactionManager")
    public void updatePriceList(List<BookingOrderDailyPrice> prices) throws Exception {
        if (prices.size() > 0) {
            bookingOrderDailyPriceDao.updatePriceList(prices);
        }
    }
}
