package com.pms.czabsorders.service.mini.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.mini.AddGoodsParam;
import com.pms.czabsorders.bean.mini.WxShoppingCardRequest;
import com.pms.czabsorders.bean.mini.WxShoppingSearch;
import com.pms.czabsorders.service.mini.WxGoodsService;
import com.pms.czabsorders.service.mini.WxMiniprogramsService;
import com.pms.czabsorders.service.mini.WxOrderService;
import com.pms.czabsorders.service.mini.transaction.WxServiceTransaction;
import com.pms.czaccount.bean.PayAgentInfo;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.pay.SysWechatPay;
import com.pms.czaccount.bean.pay.WechatMiniprograms;
import com.pms.czaccount.bean.pay.search.SysWechatPaySearch;
import com.pms.czaccount.bean.pay.search.WechatMiniprogramsSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czaccount.dao.pay.PayAgentInfoDao;
import com.pms.czaccount.dao.pay.SysWechatPayDao;
import com.pms.czaccount.dao.pay.WechatMiniprogramsDao;
import com.pms.czhotelfoundation.bean.code.HotelBusinessDay;
import com.pms.czhotelfoundation.bean.code.search.HotelBusinessDaySearch;
import com.pms.czhotelfoundation.bean.price.RoomRateCode;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSearch;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.dao.code.HotelBusinessDayDao;
import com.pms.czhotelfoundation.dao.hotel.HotelMiniproSettingDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czmembership.bean.member.*;
import com.pms.czmembership.bean.member.search.CardGroupInfoSearch;
import com.pms.czmembership.bean.member.search.CardGroupLevelSearch;
import com.pms.czmembership.bean.member.search.CardLevelSearch;
import com.pms.czmembership.bean.member.search.CardRechargeSearch;
import com.pms.czmembership.dao.member.*;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.SMS_LOC;
import com.pms.czpmsutils.constant.paymsg.PAY_RES;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.SmsHotelSendRecordRequest;
import com.pms.czpmsutils.request.StayHoursRequest;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.BookingOrderConfig;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.bean.search.BookingOrderConfigSearch;
import com.pms.pmsorder.dao.BookingOrderConfigDao;
import com.pms.pmsorder.dao.BookingOrderDao;
import com.pms.pmsorder.dao.RegistDao;
import com.pms.pmswarehouse.bean.GoodsShoppingcar;
import com.pms.pmswarehouse.bean.GoodsShoppingcarDetails;
import com.pms.pmswarehouse.bean.search.GoodsShoppingcarDetailsSearch;
import com.pms.pmswarehouse.bean.search.GoodsShoppingcarSearch;
import com.pms.pmswarehouse.dao.GoodsShoppingOrderDao;
import com.pms.pmswarehouse.dao.GoodsShoppingcarDao;
import com.pms.pmswarehouse.dao.GoodsShoppingcarDetailsDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class WxMiniprogramsServiceImpl extends BaseService implements WxMiniprogramsService {

    String jscode2session = "https://api.weixin.qq.com/sns/jscode2session?";


    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private WechatMiniprogramsDao wechatMiniprogramsDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private SysWechatPayDao sysWechatPayDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private HotelBusinessDayDao hotelBusinessDayDao;

    private BaseService baseService = this;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private GoodsShoppingcarDao goodsShoppingcarDao;

    @Autowired
    private GoodsShoppingcarDetailsDao goodsShoppingcarDetailsDao;

    @Autowired
    private GoodsShoppingOrderDao goodsShoppingOrderDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private WxServiceTransaction wxServiceTransaction;

    @Autowired
    private WxGoodsService wxGoodsService;

    @Autowired
    private PayAgentInfoDao agentInfoDao;

    private WxMiniprogramsServiceImpl wxMiniprogramsService = this;

    @Autowired
    private HotelMiniproSettingDao hotelMiniproSettingDao;

    @Autowired
    private CardGroupInfoDao cardGroupInfoDao;

    @Autowired
    private CardInfoDao cardInfoDao;

    @Autowired
    private CardRechargeDao cardRechargeDao;

    @Autowired
    private WxOrderService wxOrderService;

    @Autowired
    private CardTypeDao cardTypeDao;

    @Autowired
    private CardGroupTypeDao cardGroupTypeDao;

    @Autowired
    private CardLevelDao cardLevelDao;

    @Autowired
    private CardGroupLevelDao cardGroupLevelDao;

    @Autowired
    private RoomRateCodeDao roomRateCodeDao;

    /**
     * 获取
     * @param param
     * @return
     */
    @Override
    public ResponseData jscode2session(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {


            // 集团id
            int groupId = param.getInt("groupId");

            // 微信openid
            String code = param.getString("code");

            /**
             * 1.查询酒店小程序信息
             */
            WechatMiniprogramsSearch wechatMiniprogramsSearch = new WechatMiniprogramsSearch();
            wechatMiniprogramsSearch.setHotelGroupId(groupId);
            if(param.get("hid")!=null){
                wechatMiniprogramsSearch.setHid(param.getInt("hid"));
            }

            List<WechatMiniprograms> wechatMiniprograms = wechatMiniprogramsDao.selectBySearch(wechatMiniprogramsSearch);

            WechatMiniprograms wechatMiniprograms1 = wechatMiniprograms.get(0);

            String url = jscode2session+"appid="+wechatMiniprograms1.getAppid()+"&secret="+wechatMiniprograms1.getAppsecret()+"&js_code="+code+"&grant_type=authorization_code";

            logger.info("获取openid参数");
            logger.info(param.toString());

            String s = HttpRequest.sendGet(url, "");
            logger.info("返回");
            logger.info(s);
            responseData.setData(JSONObject.fromObject(s));

        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    /**
     * 小程序统一下单接口
     * @param param
     * @return
     */
    @Override
    public ResponseData getWxUnifiedorder(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if(user.getSessionType()==3){
                user.setHid(param.getInt("hid"));
                HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
                hotelBusinessDaySearch.setHid(user.getHid());
                HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);
                user.setBusinessDay(hotelBusinessDay.getBusinessDay());
            }

            //金额
            int money = param.getInt("money");
            // 获取统一下单接口
            final int bookId = param.getInt("bookId");
            String bookNo = param.getString("bookNo");

            final String mainId = "WX-"+bookId+"-"+bookNo;


            try {
                // 商品下单 并且金额为0，则直接下单
                int goodsType = param.getInt("goodsType");
                if(goodsType==2&&money==0){
                    AddGoodsParam addGoodsParam = new AddGoodsParam();
                    addGoodsParam.setId(bookId);
                    addGoodsParam.setSn(mainId);
                    addGoodsParam.setMainId(mainId);

                   return wxGoodsService.addGoodsToRegist(addGoodsParam,user);

                }
            }catch (Exception e){
                log.error("",e);
            }

            /**
             *  1.判断支付的小程序主体是集团还是酒店
             *      1.集团 2.酒店
             */
            int htype = param.getInt("htype");
            WechatMiniprogramsSearch wechatMiniprogramsSearch = new WechatMiniprogramsSearch();
            if(htype==1){
                wechatMiniprogramsSearch.setHid(0);
                wechatMiniprogramsSearch.setHotelGroupId(user.getHotelGroupId());
            }else {
                wechatMiniprogramsSearch.setHid(user.getHid());
            }

            List<WechatMiniprograms> wechatMiniprograms1 = wechatMiniprogramsDao.selectBySearch(wechatMiniprogramsSearch);
            if(wechatMiniprograms1.size()!=1){
                throw new Exception("查询支付信息失败");
            }

            WechatMiniprograms wechatMiniprograms = wechatMiniprograms1.get(0);




            String appid = wechatMiniprograms.getAppid();
            String mchId = wechatMiniprograms.getMchId();
            String nonceStr = TenpayUtil.CreateNoncestr();
            String key = wechatMiniprograms.getApiKey();

            // 酒店信息
            int hid = param.getInt("hid");

            PayAgentInfo payAgentInfo = agentInfoDao.selectById(hid);


            SortedMap<Object, Object> addOrder = new TreeMap<Object, Object>();


            String openId = param.getString("openId");



            // 服务商  不是
            if(payAgentInfo!=null&&payAgentInfo.getWeiChatPaySubMchId()!=null&&payAgentInfo.getWeiChatPaySubMchId().length()>5){
                addOrder.put("mch_id", wechatMiniprograms.getMchId());
                addOrder.put("appid",wechatMiniprograms.getAppid());
                key = wechatMiniprograms.getApiKey();
                addOrder.put("sub_mch_id",payAgentInfo.getWeiChatPaySubMchId());
                addOrder.put("openid",openId);
            }else {
                addOrder.put("openid",openId);
                addOrder.put("appid", wechatMiniprograms.getAppid());
                addOrder.put("mch_id", mchId);
            }

            addOrder.put("nonce_str", nonceStr);
            addOrder.put("device_info","web");
            addOrder.put("attach",mainId);
            if(param.get("attach")!=null){
                addOrder.put("attach",param.get("attach"));
            }
            if(param.get("notifyUrl")!=null&&!"".equals(param.getString("notifyUrl"))){
                wechatMiniprograms.setNotifyUrl(param.getString("notifyUrl"));
            }
            addOrder.put("notify_url",wechatMiniprograms.getNotifyUrl());
            String body = "add order";
            if(param.get("body")!=null&&!"".equals(param.getString("body"))){
                body = URLDecoder.decode(param.getString("body"), "utf-8").trim();
            }

            if(body.length()>61){
                body = body.substring(0,60)+"...";
            }

            addOrder.put("body",body);

            //商户订单号
            addOrder.put("out_trade_no",mainId);


            addOrder.put("total_fee",money);
            addOrder.put("spbill_create_ip",param.get("IP"));

            if(param.get("tradeType")==null){
                throw new Exception("交易类型不能为空");
            }

            int trade_type = param.getInt("tradeType");

            switch (trade_type){
                case 1:
                    addOrder.put("trade_type","JSAPI");

                    break;
                default:
                    throw new Exception("请传入正确的交易类型");
            }

            String sign = TenpayUtil.createSign2("UTF-8", addOrder, key);
            addOrder.put("sign",sign);
            String xml = TenpayUtil.creatPay2returnXML(addOrder);
            log.info("xml={}",xml);
            String result = HttpRequest.sendPost("https://api.mch.weixin.qq.com/pay/unifiedorder", xml);
            JSONObject rp = JSONObject.fromObject(XMLUtil.doXMLParse(result));
            if(!rp.getString("return_code").equals("SUCCESS")){
                throw new  Exception(rp.get("return_msg").toString());
            }

            /**
             *  对获取出来的微信订单信息进行签名
             */
            SortedMap<Object, Object> prepaySign = new TreeMap<>();

            prepaySign.put("appId",appid);
            prepaySign.put("timeStamp", System.currentTimeMillis()/1000);
            prepaySign.put("nonceStr",TenpayUtil.CreateNoncestr());
            prepaySign.put("package","prepay_id="+rp.get("prepay_id"));
            prepaySign.put("signType","MD5");
            String signPre = TenpayUtil.createSign2("UTF-8", prepaySign, key);
            prepaySign.put("paySign",signPre);
            prepaySign.put("mainId",mainId);

            responseData.setData(prepaySign);

            final Object goodsType = param.get("goodsType");

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {

                        if(goodsType==null){
                            return;
                        }

                        Thread.sleep(1000*120);

                        // 1.订单 2.商品 3-会员充值
                        int i = Integer.parseInt(goodsType.toString());

                        JSONObject pss = new JSONObject();

                        pss.put("htype",1);
                        pss.put("notifyTag",i);
                        pss.put("hid",hid);
                        pss.put("bookId",bookId);
                        pss.put("mainId",mainId);
                        pss.put(ER.SESSION_TOKEN,param.get(ER.SESSION_TOKEN));

                        ResponseData weChatPayResult = wxMiniprogramsService.getWeChatPayResult(pss);
                        if(weChatPayResult.getCode()>0){
                            baseService.turnAlways(user);
                            HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                            return;
                        }

                        Thread.sleep(1000*180);

                         weChatPayResult = wxMiniprogramsService.getWeChatPayResult(pss);

                        // 小于0说明未支付  取消订单
                        if(weChatPayResult.getCode()>0){
                            return;
                        }



                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");

        }

        return responseData;
    }


    @Override
    public ResponseData getWeChatPayResult(JSONObject map) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = map.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if(user.getSessionType()==3){
                user.setHid(map.getInt("hid"));
                HotelBusinessDaySearch hotelBusinessDaySearch = new HotelBusinessDaySearch();
                hotelBusinessDaySearch.setHid(user.getHid());
                HotelBusinessDay hotelBusinessDay = hotelBusinessDayDao.searchBussinessDay(hotelBusinessDaySearch);
                user.setBusinessDay(hotelBusinessDay.getBusinessDay());
            }

            /**
             *  1.判断支付的小程序主体是集团还是酒店
             *      1.集团 2.酒店
             */
            int htype = map.getInt("htype");

            //回调方法类型
            int notifyTag = map.getInt("notifyTag");


            WechatMiniprogramsSearch wechatMiniprogramsSearch = new WechatMiniprogramsSearch();
            if(htype==1){
                wechatMiniprogramsSearch.setHid(0);
                wechatMiniprogramsSearch.setHotelGroupId(user.getHotelGroupId());
            }else {
                wechatMiniprogramsSearch.setHid(user.getHid());
            }

            List<WechatMiniprograms> wechatMiniprograms1 = wechatMiniprogramsDao.selectBySearch(wechatMiniprogramsSearch);
            if(wechatMiniprograms1.size()!=1){
                throw new Exception("查询支付信息失败");
            }
            WechatMiniprograms wechatMiniprograms = wechatMiniprograms1.get(0);

            /*1.查询微信订单状态*/
            String appid = wechatMiniprograms.getAppid();
            String mchId = wechatMiniprograms.getMchId();
            String nonceStr = TenpayUtil.CreateNoncestr();
            String key = wechatMiniprograms.getApiKey();

            SortedMap<Object, Object> json1 = new TreeMap<Object, Object>();
            json1.put("appid", appid);
            json1.put("mch_id", mchId);
            json1.put("nonce_str", nonceStr);

            // 服务商  是
            int hid = map.getInt("hid");
            user.setHid(hid);

            PayAgentInfo payAgentInfo = agentInfoDao.selectById(hid);
            if(payAgentInfo!=null&&payAgentInfo.getWeiChatPaySubMchId()!=null&&payAgentInfo.getWeiChatPaySubMchId().length()>5){

                json1.put("mch_id", wechatMiniprograms.getMchId());
                json1.put("appid",wechatMiniprograms.getAppid());
                key = wechatMiniprograms.getApiKey();
                json1.put("sub_mch_id",payAgentInfo.getWeiChatPaySubMchId());


            }

            /*先查询当前订单是否存在，不存在插入到数据库*/
            SysWechatPaySearch sysWechatPaySearch = new SysWechatPaySearch();
            sysWechatPaySearch.setHid(user.getHid());

            if (map.containsKey("mainId")) {
                json1.put("out_trade_no", map.get("mainId"));
                sysWechatPaySearch.setMainId(map.get("mainId").toString());
            }

            if (map.containsKey("transactionId")) {
                json1.put("transaction_id", map.get("transactionId"));
                sysWechatPaySearch.setTransactionId(map.get("transactionId").toString());
            }

            List<SysWechatPay> sysWechatPays = sysWechatPayDao.selectBySearch(sysWechatPaySearch);

            if(sysWechatPays.size()>0){
                SysWechatPay sysWechatPay = sysWechatPays.get(0);
                if(sysWechatPay.getPayStatus().equals(PAY_RES.STATE_SUCC)){
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("mainId",sysWechatPay.getMainId());
                    jsonObject.put("money",sysWechatPay.getMoney());
                    responseData.setData(jsonObject);
                    if(notifyTag==3){
                        CardRechargeSearch cardRechargeSearch = new CardRechargeSearch();
                        cardRechargeSearch.setSessionToken(sessionToken);
                        cardRechargeSearch.setThirdAccoutId( map.get("mainId").toString());
                        this.paySuccRecharge(cardRechargeSearch);
                    }
                    return  responseData;
                }

            }

            String sign = TenpayUtil.createSign2("UTF-8", json1, key);
            json1.put("sign",sign);
            String xml = TenpayUtil.creatPay2returnXML(json1);
            String result = HttpRequest.sendPost("https://api.mch.weixin.qq.com/pay/orderquery", xml);

            //转化结果
            JSONObject wxResult = JSONObject.fromObject(XMLUtil.doXMLParse(result));

            JSONObject resultMap = new JSONObject();
            SysWechatPay wechatPay = new SysWechatPay();
            if (wxResult.containsKey("return_code")) {
                if ("SUCCESS".equals(wxResult.get("return_code"))) {
                    if (wxResult.containsKey("result_code") && wxResult.get("result_code").equals("FAIL")) {
                        resultMap.put("message", wxResult.get("err_code_des"));
                        resultMap.put("errcode", wxResult.get("err_code"));
                        resultMap.put(ER.RES, ER.ERR);
                        responseData.setData(resultMap);
                    } else {

                        wechatPay.setHid(user.getHid());
                        if (map.containsKey("UUID")) {
                            wechatPay.setMachineUuid(map.get("UUID").toString());
                        }
                        wechatPay.setMainId(wxResult.getString("out_trade_no"));
                        wechatPay.setMoney(Double.parseDouble(wxResult.get("total_fee").toString()) / 100);
                        wechatPay.setMoneyFen(wxResult.getInt("total_fee"));
                        if (map.containsKey("source")) {
                            wechatPay.setSource(map.get("source").toString());
                        }
                        if (map.containsKey("desc")) {
                            wechatPay.setOrderDesc(map.get("desc").toString());
                        }
                        wechatPay.setPayStatus(1);
                        if (wxResult.containsKey("is_subscribe")) {
                            wechatPay.setIsSubscribe(wxResult.getString("is_subscribe"));
                        }
                        if (wxResult.containsKey("appid")) {
                            wechatPay.setAppId(wxResult.getString("appid"));
                        }
                        if (wxResult.containsKey("transaction_id")) {
                            wechatPay.setTransactionId(wxResult.getString("transaction_id"));
                        }
                        if (wxResult.containsKey("trade_type")) {
                            wechatPay.setTradeType(wxResult.getString("trade_type"));
                        }
                        if (wxResult.containsKey("mch_id")) {
                            wechatPay.setMchId(wxResult.getString("mch_id"));
                        }
                        if (wxResult.containsKey("time_end")) {

                            long time_end = wxResult.getLong("time_end");
                            Long yearL = time_end / 10000000000L;
                            Long monthL = time_end / 100000000L % 100;
                            Long dayL = time_end / 1000000L % 100;
                            Long hourL = time_end / 10000L % 100;
                            Long minL = time_end / 100L % 100;
                            Long sL = time_end % 100;
                            int year = yearL.intValue();
                            int month = monthL.intValue();
                            int day = dayL.intValue();
                            int hour = hourL.intValue();
                            int min = minL.intValue();
                            int s = sL.intValue();
                            StringBuilder sb = new StringBuilder();
                            sb.append(year);
                            sb.append("-");
                            if (month < 10) {
                                sb.append("0");
                            }
                            sb.append(month);
                            sb.append("-");
                            if (day < 10) {
                                sb.append("0");
                            }
                            sb.append(day);
                            sb.append(" ");
                            if (hour < 10) {
                                sb.append("0");
                            }
                            sb.append(hour);
                            sb.append(":");
                            if (min < 10) {
                                sb.append("0");
                            }
                            sb.append(min);
                            sb.append(":");
                            if (s < 10) {
                                sb.append("0");
                            }
                            sb.append(s);
                            wechatPay.setTimeEnd(HotelUtils.parseStr2Date(sb.toString()));
                        }
                        if (wxResult.containsKey("openid")) {
                            wechatPay.setOpenId(wxResult.getString("openid"));
                        }
                        if (wxResult.containsKey("bank_type")) {
                            wechatPay.setBankType(wxResult.getString("bank_type"));
                        }
                        if (wxResult.containsKey("cash_fee")) {
                            wechatPay.setCashFee(wxResult.getInt("cash_fee"));
                        }
                        if (wxResult.containsKey("total_fee")) {
                            int cashFee = wxResult.getInt("total_fee") - wxResult.getInt("cash_fee");
                            wechatPay.setCashFee(cashFee);
                        }
                        wechatPay.setUpdateTime(new Date());
                        wechatPay.setPayTime(new Date());
                        wechatPay.setRefundMoney(0);


                        Integer insertValue = sysWechatPayDao.saveSysWechatPay(wechatPay);

                        if (insertValue < 1) {
                            throw new Exception(PAY_RES.SAVE_PAY_FAILD);
                        }

                        JSONObject jsonObject = new JSONObject();

                        jsonObject.put(ER.MSG, PAY_RES.PAY_SUCC_MSG);
                        jsonObject.put("mainId", map.get("mainId"));
                        jsonObject.put("money", wxResult.getInt("total_fee") / 100.0);
                        responseData.setData(jsonObject);

                        // 1. 预订单 2.商品订单 3.会员储值 4.小程序会员续住
                        switch (notifyTag){
                            case 1:
                                this.addBookPay(user,map.getInt("bookId"),wechatPay);
                                break;
                            case 2:

                                AddGoodsParam addGoodsParam = new AddGoodsParam();
                                addGoodsParam.setId(map.getInt("bookId"));
                                addGoodsParam.setSn(wechatPay.getMainId());
                                addGoodsParam.setMainId(map.getString("mainId"));
                                user.setHid(hid);
                                wxGoodsService.addGoodsToRegist(addGoodsParam,user);

                                break;
                            case 3:
                                CardRechargeSearch cardRechargeSearch = new CardRechargeSearch();
                                cardRechargeSearch.setSessionToken(sessionToken);
                                cardRechargeSearch.setThirdAccoutId(wechatPay.getMainId());
                                this.paySuccRecharge(cardRechargeSearch);
                                break;
                            case 4:
                                String mainId = map.getString("mainId");
                                String[] split = mainId.split("-");
                                // registId
                                int registId = Integer.parseInt(split[1]);
                                // hour
                                int hour = Integer.parseInt(split[3]);

                                StayHoursRequest stayHoursRequest = new StayHoursRequest();
                                stayHoursRequest.setHid(user.getHid());
                                stayHoursRequest.setRegistId(registId);
                                stayHoursRequest.setHours(hour);
                                stayHoursRequest.setMainId(mainId);
                                stayHoursRequest.setSessionToken(sessionToken);
                                stayHoursRequest.setPayMoney(wechatPay.getMoneyFen());
                                wxOrderService.stayHours(stayHoursRequest);
                                break;
                        }

                    }
                } else {
                    throw new Exception(wxResult.getString("err_code_des"));
                }

            }

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");

        }

        return responseData;
    }

    /**
     * 支付成功后添加订单支付信息
     * @param user
     * @param bookingId
     * @param sysWechatPay
     */
    public void addBookPay(TbUserSession user,Integer bookingId,SysWechatPay sysWechatPay){
        try {
            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingId);

            String no = OrderNumUtils.getNo(user.getHid(), user,OrderNumUtils.ACCOUNT, this.stringRedisTemplate);

            Account account = new Account(user);
            account.setIsCancel(0);
            account.setAccountYear(user.getBusinessYear());
            account.setAccountYearMonth(user.getBusinessMonth());
            account.setBusinessDay(user.getBusinessDay());
            account.setClassId(user.getClassId());
            account.setSettleAccountTime(new Date());
            //设置账务关联人id为0
            account.setRegistPersonId(0);
            account.setRegistPersonName("");
            account.setThirdRefundState(0);
            account.setAccountId(no);
            account.setBookingId(bookingId);
            account.setPrice(sysWechatPay.getMoneyFen());
            account.setPayType(2);
            account.setPayClassId(12);
            account.setPayClassName("微信支付");
            account.setPayCodeId("9340");
            account.setPayCodeName("微信公众号支付");
            account.setIsSale(1);
            account.setUintPrice(sysWechatPay.getMoneyFen());
            account.setSaleNum(1);
            account.setRegistState(0);
            account.setSettleAccountTime(new Date());
            account.setThirdAccoutId(sysWechatPay.getMainId());
            account.setRefundPrice(0);
            account.setThirdRefundState(0);
            account.setAccountType(1);

            try {
                accountDao.saveAccount(account);
            }catch (Exception e){
                String uuid = HotelUtils.getUUID();
                account.setAccountId(uuid);
                accountDao.saveAccount(account);
            }


            bookingOrder.setPayPrice(sysWechatPay.getMoneyFen());
            bookingOrderDao.editBookingOrder(bookingOrder);


            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setBookingOrderId(bookingId);

            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);

            if(bookingOrderConfigs!=null&&bookingOrderConfigs.size()>0){
                BookingOrderConfig bookingOrderConfig = bookingOrderConfigs.get(0);

                bookingOrderConfig.setNoPrice(1);

                bookingOrderConfigDao.editBookingOrderConfig(bookingOrderConfig);

            }


            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {

                        ArrayList<String> params = new ArrayList<>();
                        params.add(bookingOrder.getBookingName());
                        params.add(bookingOrder.getRoomTypeSummary());
                        params.add(bookingOrder.getSn());
                        params.add(HotelUtils.parseDate2Str(bookingOrder.getCheckinTime()).substring(0,10)+" 14:00");
                        params.add(HotelUtils.parseDate2Str(bookingOrder.getCheckoutTime()).substring(0,16));
                        params.add(user.getHotelName());
                        final JSONObject smsParam = new JSONObject();
                        smsParam.put(SMS_LOC.PHONE,bookingOrder.getBookingPhone());
                        smsParam.put(SMS_LOC.PARAM,params);

                        HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
                        baseService.turnAlways(user);
                        String sessionToken = user.getSessionId();
                        SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                        smsHotelSendRecordRequest.setLocationId(SMS_LOC.ORDER_CREATE);
                        smsHotelSendRecordRequest.setSessionToken(sessionToken);
                        smsHotelSendRecordRequest.setPhoneNumber(smsParam.getString(SMS_LOC.PHONE));
                        ArrayList<String> list = (ArrayList<String>) JSONArray.toList(smsParam.getJSONArray(SMS_LOC.PARAM), String.class);
                        smsHotelSendRecordRequest.setParams(list);
                        baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);

                        HashMap<String, String> filed = new HashMap<>();
                        filed.put("sn", bookingOrder.getSn());
                        filed.put("rtdesc", bookingOrder.getRoomTypeSummary());
                        HashMap<String, String> dataMap = new HashMap<>();
                        dataMap.put("bookId", bookingOrder.getBookingOrderId() + "");
                        HashMap<String, String> onClickCbData = new HashMap<>();
                        onClickCbData.put("bookingOrderId", bookingOrder.getBookingOrderId().toString());
                        baseService.push(user.getHotelGroupId(), user.getHid(), 20, filed, dataMap, true, true, onClickCbData);

                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });
        }catch (Exception e){
            log.error("",e);
        }
    }



    @Override
    public ResponseData searchWxShoppingCar(WxShoppingSearch wxShoppingSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            TbUserSession user = this.getTbUserSession(wxShoppingSearch.getSessionToken());
            user.setHid(wxShoppingSearch.getHid());

            GoodsShoppingcarSearch gss = new GoodsShoppingcarSearch();
            gss.setHid(wxShoppingSearch.getHid());
            gss.setGoodType(wxShoppingSearch.getGoodType());
            gss.setOpenId(wxShoppingSearch.getSessionToken());
            gss.setState(0);
            if(gss.getGoodType()==1){

                Regist regist = registDao.selectById(wxShoppingSearch.getRegistId());
                if(regist.getState()!=0){

                }

            }else {
                gss.setRoomNumId(wxShoppingSearch.getRoomNumId());
            }

        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData addWxShoppingCar(WxShoppingCardRequest wxShoppingCardRequest) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            final TbUserSession user = this.getTbUserSession(wxShoppingCardRequest);
            List<WxShoppingCardRequest.GoodInfo> goodList = wxShoppingCardRequest.getGoodList();

            if(goodList==null||goodList.size()<1){
                throw new Exception("请选择商品");
            }


            GoodsShoppingcarSearch gss = new GoodsShoppingcarSearch();
            gss.setHid(wxShoppingCardRequest.getHid());
            gss.setGoodType(wxShoppingCardRequest.getGoodType());
            gss.setOpenId(wxShoppingCardRequest.getSessionToken());
            gss.setState(0);
            gss.setCardGroupId(wxShoppingCardRequest.getCardGroupId());
            if(gss.getGoodType()==1){
                gss.setRegistId(wxShoppingCardRequest.getRegistId());
            }else {
                gss.setRoomNumId(wxShoppingCardRequest.getRoomNumId());
            }

            GoodsShoppingcar delGoodshoppingcar = null;

            ArrayList<GoodsShoppingcarDetails> delDetails = new ArrayList<>();

            Page<GoodsShoppingcar> goodsShoppingcars = goodsShoppingcarDao.selectBySearch(gss);

            if(goodsShoppingcars.size()>0){

                delGoodshoppingcar =  goodsShoppingcars.get(0);

                GoodsShoppingcarDetailsSearch goodsShoppingcarDetailsSearch = new GoodsShoppingcarDetailsSearch();
                goodsShoppingcarDetailsSearch.setGoodsShoppingId(delGoodshoppingcar.getId());

                delDetails = goodsShoppingcarDetailsDao.selectBySearch(goodsShoppingcarDetailsSearch);

            }

            /**
             *  对支付记录进行添加
             */
            String no = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.WGOODS, stringRedisTemplate);
            GoodsShoppingcar goodsShoppingcar = new GoodsShoppingcar();
            goodsShoppingcar.setHid(user.getHid());
            goodsShoppingcar.setSn(no);
            goodsShoppingcar.setRegistId(wxShoppingCardRequest.getRegistId());
            goodsShoppingcar.setGoodType(wxShoppingCardRequest.getGoodType());
            goodsShoppingcar.setCardId(wxShoppingCardRequest.getCardId());
            goodsShoppingcar.setCardGroupId(wxShoppingCardRequest.getCardGroupId());
            goodsShoppingcar.setCardNo(wxShoppingCardRequest.getCardNo());
            if(goodsShoppingcar.getGoodType()==1){
                Regist regist = registDao.selectById(wxShoppingCardRequest.getRegistId());
                if(regist.getState()!=0){
                    throw new Exception("请重新选择房间");
                }
                goodsShoppingcar.setRoomNumId(regist.getRoomNumId());
                goodsShoppingcar.setRoomNum(regist.getRoomNum());
                goodsShoppingcar.setRegistId(regist.getRegistId());
            }else {
                RoomInfo roomInfo = roomInfoDao.selectById(wxShoppingCardRequest.getRoomNumId());
                goodsShoppingcar.setRoomNumId(roomInfo.getRoomInfoId());
                goodsShoppingcar.setRoomNum(roomInfo.getRoomNum());
            }

            goodsShoppingcar.setHotelGroupId(user.getHotelGroupId());
            goodsShoppingcar.setOpenId(user.getSessionId());
            goodsShoppingcar.setState(0);
            goodsShoppingcar.setBusinessDay(user.getBusinessDay());
            goodsShoppingcar.setCreateTime(new Date());
            goodsShoppingcar.setCreateUserId(user.getUserId());
            goodsShoppingcar.setCreateUserName(user.getUserName());
            goodsShoppingcar.setSumPrice(wxShoppingCardRequest.getSumPrice());
            goodsShoppingcar.setStcokCheck(wxShoppingCardRequest.getHotelIsCheckGoodsNum());

            wxServiceTransaction.addWxShoppingCar(delGoodshoppingcar,delDetails,goodsShoppingcar,wxShoppingCardRequest);

            responseData.setData(goodsShoppingcar);

        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData addWxShoppingCarOther(WxShoppingCardRequest wxShoppingCardRequest) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            TbUserSession user = this.getTbUserSession(RSAUtils.getStringDecrypt(wxShoppingCardRequest.getHotelId()));
            user.setHid(wxShoppingCardRequest.getHid());

            List<WxShoppingCardRequest.GoodInfo> goodList = wxShoppingCardRequest.getGoodList();

            if(goodList==null||goodList.size()<1){
                throw new Exception("请选择商品");
            }


            GoodsShoppingcarSearch gss = new GoodsShoppingcarSearch();
            gss.setHid(wxShoppingCardRequest.getHid());
            gss.setGoodType(wxShoppingCardRequest.getGoodType());
            gss.setOpenId(wxShoppingCardRequest.getSessionToken());
            gss.setState(0);
            gss.setCardGroupId(wxShoppingCardRequest.getCardGroupId());
            if(gss.getGoodType()!=null&&gss.getGoodType()==1){
                gss.setRegistId(wxShoppingCardRequest.getRegistId());
            }else {
                gss.setRoomNumId(wxShoppingCardRequest.getRoomNumId());
            }

            GoodsShoppingcar delGoodshoppingcar = null;

            ArrayList<GoodsShoppingcarDetails> delDetails = new ArrayList<>();

            Page<GoodsShoppingcar> goodsShoppingcars = goodsShoppingcarDao.selectBySearch(gss);

            if(goodsShoppingcars.size()>0){

                delGoodshoppingcar =  goodsShoppingcars.get(0);

                GoodsShoppingcarDetailsSearch goodsShoppingcarDetailsSearch = new GoodsShoppingcarDetailsSearch();
                goodsShoppingcarDetailsSearch.setGoodsShoppingId(delGoodshoppingcar.getId());

                delDetails = goodsShoppingcarDetailsDao.selectBySearch(goodsShoppingcarDetailsSearch);

            }

            /**
             *  对支付记录进行添加
             */
            String no = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.WGOODS, stringRedisTemplate);
            GoodsShoppingcar goodsShoppingcar = new GoodsShoppingcar();
            goodsShoppingcar.setHid(user.getHid());
            goodsShoppingcar.setSn(no);
            goodsShoppingcar.setRegistId(wxShoppingCardRequest.getRegistId());
            goodsShoppingcar.setGoodType(wxShoppingCardRequest.getGoodType());
            goodsShoppingcar.setCardId(wxShoppingCardRequest.getCardId());
            goodsShoppingcar.setCardGroupId(wxShoppingCardRequest.getCardGroupId());
            goodsShoppingcar.setCardNo(wxShoppingCardRequest.getCardNo());
            if(goodsShoppingcar.getGoodType()==1){
                Regist regist = registDao.selectById(wxShoppingCardRequest.getRegistId());
                if(regist.getState()!=0){
                    throw new Exception("请重新选择房间");
                }
                goodsShoppingcar.setRoomNumId(regist.getRoomNumId());
                goodsShoppingcar.setRoomNum(regist.getRoomNum());
                goodsShoppingcar.setRegistId(regist.getRegistId());
            }else {
                RoomInfo roomInfo = roomInfoDao.selectById(wxShoppingCardRequest.getRoomNumId());
                goodsShoppingcar.setRoomNumId(roomInfo.getRoomInfoId());
                goodsShoppingcar.setRoomNum(roomInfo.getRoomNum());
            }

            goodsShoppingcar.setHotelGroupId(user.getHotelGroupId());
            goodsShoppingcar.setOpenId(user.getSessionId());
            goodsShoppingcar.setState(0);
            goodsShoppingcar.setBusinessDay(user.getBusinessDay());
            goodsShoppingcar.setCreateTime(new Date());
            goodsShoppingcar.setCreateUserId(user.getUserId());
            goodsShoppingcar.setCreateUserName(user.getUserName());
            goodsShoppingcar.setSumPrice(wxShoppingCardRequest.getSumPrice());
            goodsShoppingcar.setStcokCheck(wxShoppingCardRequest.getHotelIsCheckGoodsNum());

            wxServiceTransaction.addWxShoppingCar(delGoodshoppingcar,delDetails,goodsShoppingcar,wxShoppingCardRequest);

            responseData.setData(goodsShoppingcar);

        }catch (Exception e){
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData paySuccRecharge(CardRechargeSearch cardRechargeSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            TbUserSession user = this.getTbUserSession(cardRechargeSearch);

            CardRechargeSearch crsh = new CardRechargeSearch();
            crsh.setHotelGroupId(user.getHotelGroupId());
            crsh.setThirdAccoutId(cardRechargeSearch.getThirdAccoutId());

            Page<CardRecharge> cardRecharges = cardRechargeDao.selectBySearch(crsh);

            if(cardRecharges==null||cardRecharges.size()!=1){
                throw new Exception("未查询到对应的支付信息");
            }

            CardRecharge cardRecharge = cardRecharges.get(0);
            if(!cardRecharge.getSta().equals("R")){
                responseData.setData(cardRecharge);
                return  responseData;
            }

            CardGroupInfo cardGroupInfo = cardGroupInfoDao.selectById(cardRecharge.getCardGroupId());

            cardRecharge.setLastBalance(cardGroupInfo.getBalance());
            cardRecharge.setLastLargessBalance(cardGroupInfo.getLargessBalance());
            cardRecharge.setSta("I");
            cardRecharge.setGroupType(1);

            cardGroupInfo.setBalance(cardGroupInfo.getBalance()+cardRecharge.getAddBalance());
            cardGroupInfo.setLargessBalance(cardGroupInfo.getLargessBalance()+cardRecharge.getAddLargessBalance());

            cardRechargeDao.editCardRecharge(cardRecharge);
            cardGroupInfoDao.update(cardGroupInfo);

        }catch (Exception e){
            log.error("",e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData copyVipLevel(CardRechargeSearch cardRechargeSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {

            Integer cardGroupTypeId = 144;

            ArrayList<Integer> hidList = new ArrayList<>();
            hidList.add(2388);
            hidList.add(2389);
            hidList.add(2390);
            hidList.add(2392);
            hidList.add(2393);
            hidList.add(2394);
            hidList.add(2395);
            hidList.add(2396);
            hidList.add(2397);
            hidList.add(2398);
            hidList.add(2399);

            HashMap<Integer, Integer> typeIdMap = new HashMap<>();


            // 同步会员级别
            CardGroupType cardGroupType = cardGroupTypeDao.selectById(cardGroupTypeId);

            for(Integer hid:hidList){
                CardType cardType = (CardType) JSONObject.toBean(JSONObject.fromObject(cardGroupType), CardType.class);
                cardType.setId(null);
                cardType.setHid(hid);
                cardType.setCardGroupTypeId(cardGroupTypeId);
                cardTypeDao.saveCardType(cardType);

                typeIdMap.put(hid,cardType.getId());
            }

           // 同步会员级别信息
            CardGroupLevelSearch cardGroupLevelSearch = new CardGroupLevelSearch();
            cardGroupLevelSearch.setCardTypeId(cardGroupTypeId);
            List<CardGroupLevel> cardGroupLevels = cardGroupLevelDao.selectBySearch(cardGroupLevelSearch);


            for(Integer hid:hidList){

                RoomRateCodeSearch roomRateCodeSearch = new RoomRateCodeSearch();
                roomRateCodeSearch.setHid(hid);
                Page<RoomRateCode> roomRateCodes = roomRateCodeDao.selectBySearch(roomRateCodeSearch);
                RoomRateCode roomRateCode = roomRateCodes.get(0);

                Integer integer = typeIdMap.get(hid);

                for(CardGroupLevel cardGroupLevel :cardGroupLevels){

                    CardLevel cardLevel = (CardLevel) JSONObject.toBean(JSONObject.fromObject(cardGroupLevel), CardLevel.class);
                    cardLevel.setId(null);
                    cardLevel.setHid(hid);
                    cardLevel.setPriceCodeId(roomRateCode.getRateId());
                    cardLevel.setPriceCode(roomRateCode.getRateCode());
                    cardLevel.setCardTypeId(integer);
                    cardLevel.setCardGroupTypeId(cardGroupTypeId);
                    cardLevel.setCardGroupLevelId(cardGroupLevel.getId());
                    cardLevelDao.saveCardLevel(cardLevel);

                }
            }



        }catch (Exception e){
            log.error("",e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData copyVipInfo(CardRechargeSearch cardRechargeSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);

        try {


            ArrayList<Integer> hidList = new ArrayList<>();
/*            hidList.add(2388);
            hidList.add(2389);
            hidList.add(2390);
            hidList.add(2392);
            hidList.add(2393);
            hidList.add(2394);
            hidList.add(2395);
            hidList.add(2396);
            hidList.add(2397);
            hidList.add(2398);
            hidList.add(2399);*/


            CardGroupInfoSearch cardGroupInfoSearch = new CardGroupInfoSearch();
            cardGroupInfoSearch.setHotelGroupId(2207);
            List<CardGroupInfo> cardGroupInfos = cardGroupInfoDao.selectBySearch(cardGroupInfoSearch);

            for(Integer hid :hidList){

                ArrayList<CardInfo> cardInfos = new ArrayList<>();

                CardLevelSearch cardLevelSearch = new CardLevelSearch();
                cardLevelSearch.setHid(hid);
                List<CardLevel> cardLevels = cardLevelDao.selectBySearch(cardLevelSearch);

                Map<Integer, CardLevel> collect = cardLevels.stream().collect(Collectors.toMap(CardLevel::getCardGroupLevelId, a -> a, (k1, k2) -> k2));


                for(CardGroupInfo cardGroupInfo:cardGroupInfos){

                    CardLevel cardLevel = collect.get(cardGroupInfo.getCardLevelId());
                    if(cardLevel==null){
                        cardLevel = cardLevels.get(0);
                    }

                    CardInfo cardInfo = (CardInfo) JSONObject.toBean(JSONObject.fromObject(cardGroupInfo), CardInfo.class);
                    cardInfo.setId(null);
                    cardInfo.setHid(hid);
                    cardInfo.setCardLevelId(cardLevel.getId());
                    cardInfo.setCardGroupLevelId(cardLevel.getCardGroupLevelId());
                    cardInfo.setCardType(cardLevel.getCardType());
                    cardInfo.setCardTypeId(cardLevel.getCardTypeId());
                    cardInfo.setCardGroupTypeId(cardLevel.getCardGroupTypeId());
                    cardInfo.setCardGroupId(cardGroupInfo.getId());
                    cardInfo.setHotelGroupId(cardGroupInfo.getHotelGroupId());
                    cardInfo.setPriceCode(cardLevel.getPriceCode());
                    cardInfo.setPriceCodeId(cardLevel.getPriceCodeId());

                    cardInfos.add(cardInfo);


                }
                cardInfoDao.insertList(cardInfos);
            }


        }catch (Exception e){
            log.error("",e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
