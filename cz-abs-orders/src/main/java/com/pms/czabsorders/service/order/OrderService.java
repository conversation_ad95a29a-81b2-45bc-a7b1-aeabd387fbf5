package com.pms.czabsorders.service.order;

import com.pms.czabsorders.bean.*;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.*;
import com.pms.pmsorder.bean.request.OtaHotelInfoRequest;
import com.pms.pmsorder.bean.request.OtaRoomTypeRequest;
import com.pms.pmsorder.bean.search.OtaHotelInfoSearch;
import com.pms.pmsorder.bean.search.OtaRoomTypeSearch;
import net.sf.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface OrderService {



    /**
     * 新增预订单-new
     * @param param
     * @return
     */
    public JSONObject addBookNew(JSONObject param);

    /**
     * 钟点房
     * @param param
     * @return
     */
    public ResponseData createBookingOrder(CreateBookingOrderRequest createBookingOrderRequest);

    /**
     *  修改预订房型
     * @param bookUpdateRoomType
     * @return
     */
    public ResponseData updateBookRoomType(BookUpdateRoomTypeTwo bookUpdateRoomType);


    /**
     *  保存预订房间
     * @param param
     * @return
     */
    public JSONObject addBookOrderRoom(JSONObject param);

    /**
     *  新增保存订单房间
     * @param param
     * @return
     */
    public ResponseData addBookOrderRoomNew(JSONObject param);

    /**
     * 批量排房或者批量取消排房
     * @param param
     * @return
     */
    public ResponseData addBookOrderRoomAll(JSONObject param);


    /**
     * 取消预订单
     * @param param
     * @return
     */
    public ResponseData cancelBook(JSONObject param);

    /**
     * 恢复订单
     * @param param
     * @return
     */
    public ResponseData orderRecovery(JSONObject param);

    /**
     * 修改价格
     * @param param
     * @return
     */
    public JSONObject updateDayPrice(JSONObject param);

    public ResponseData getTodaySituation(BaseRequest baseRequest);

    /**
     * 修改订单
     * @param param
     * @return
     */
    public JSONObject updateBooking(JSONObject param);

    /**
     * 取消预订单排房
     * @param param
     * @return
     */
    public ResponseData cancelBookingRoomNo(JSONObject param);

    /**
     * 添加预订入住人
     * @param param
     * @return
     */
    public JSONObject addPersonForBookingRoom(JSONObject param);

    /**
     * 添加预订入住人
     * @param param
     * @return
     */
    public HashMap<Integer, List<Integer>> addPersonForBookingRoom2(BookingRequest bookingRequest) throws Exception;

    /**
     * 删除预订入住人
     * @param bookingRequest
     * @return
     */
    public void delPersonForBookingRoom(BookingRequest bookingRequest) throws Exception;

    /**
     * 修改，预订或者登记单 辅助设置
     * @param param
     * @return
     */
    public Map<String ,Object> updateBookingOrderConfig(JSONObject param);


    /**
     * 修改预订主单信息
     * @param updateBookingOrderInfoRequest
     * @return
     */
    public ResponseData updateBookingOrderInfo(UpdateBookingOrderInfoRequest updateBookingOrderInfoRequest);


    public ResponseData accountBack(JSONObject param);

    // 根据 registId获取用户名字
    public String getRegistPeopleName(Integer registId);

    /**
     * 当日预计营收统计
     * @param param
     * @return
     */
    public ResponseData dayRevenue(JSONObject param);

    ResponseData hotelLiveInfo(JSONObject param);


    public ResponseData searchOtaRoomType(OtaRoomTypeSearch otaRoomTypeSearch);

    public ResponseData updateOtaRoomType(OtaRoomTypeRequest otaRoomTypeRequest);

    public ResponseData searchOtaHotelInfo(OtaHotelInfoSearch otaHotelInfoSearch);

    public ResponseData updateOtaHotelInfo(OtaHotelInfoRequest otaHotelInfoRequest);


    // 新增预订单房间
    public ResponseData addBookRoomFunc(AddBookRoomRequest addBookRoomRequest);

    // 删除预订房间
    public ResponseData delBookRoomFunc(DelBookRoomRequest delBookRoomRequest);

    // 修改预订房间
    public ResponseData upaBookRoomFunc(UpaBookRoomRequest upaBookRoomRequest);

    // 排房
    public ResponseData planRoomFunc(PlanBookRoomRequest planBookRoomRequest);

    // 批量修改价格或者早餐
    public ResponseData updatePriceOrBreakFunc(UpdatePriceOrBreak updatePriceOrBreak);

    public ResponseData copyPrice();


    public ResponseData getHotelTodayComingBookingList(TodayComingRequest todayComingRequest);

    // 查询当前酒店未结账，或者未入住的订单信息
    public ResponseData searchRegistAndBook(JSONObject param);

    public ResponseData getOrderListPriceListAndAccountList(GetOrderListPriceListAndAccountListRequest getOrderListPriceListAndAccountListRequest);

}
