package com.pms.czabsorders.service.machine;

import com.pms.czpmsutils.ResponseData;
import com.pms.pmsorder.bean.machine.MachineMain;
import com.pms.pmsorder.bean.machine.search.MachineMainSearch;
import net.sf.json.JSONObject;

import java.util.List;
import java.util.Map;

public interface MachineMainService {
    /**
     * 修改或添加自助机
     * @param param
     * @return
     */
    public ResponseData addOrUpdateMachine(JSONObject param);


    /**
     * 查询所有自助机
     * @param param
     * @return
     */
    public Map<String,Object> findAllMachine(JSONObject param);

    /**
     * 更新缓存中的自助机信息
     * @return
     */
    public List<MachineMain> updateAllMachineForCache();

    /**
     * 查询当前酒店下面的电脑
     * @param machineMainSearch
     * @return
     */
    public ResponseData getHotelMachine(MachineMainSearch machineMainSearch);

    /**
     * 酒店添加电脑
     * @param machineMain
     * @return
     */
    public ResponseData updateHotelMachine(MachineMain machineMain);

    /**
     * 通过自助机的mac地址查询当前房间在住信息
     * @param machineMainSearch
     * @return
     */
    public ResponseData searchRegistInfoByMac(MachineMainSearch machineMainSearch );

}
