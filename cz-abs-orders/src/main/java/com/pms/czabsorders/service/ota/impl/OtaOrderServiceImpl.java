package com.pms.czabsorders.service.ota.impl;

import com.alibaba.excel.util.StringUtils;
import com.pms.czabsorders.bean.AddBookDTO;
import com.pms.czabsorders.bean.DelBookRoomRequest;
import com.pms.czabsorders.bean.UpaBookRoomRequest;
import com.pms.czabsorders.service.order.OrderService;
import com.pms.czabsorders.service.ota.OtaOrderService;
import com.pms.czabsorders.service.turnAlways.TurnAlwaysService;
import com.pms.czhotelfoundation.bean.ota.*;
import com.pms.czhotelfoundation.bean.price.RoomRateCode;
import com.pms.czhotelfoundation.bean.room.RoomType;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czmembership.bean.company.HotelCompanyAccount;
import com.pms.czmembership.bean.company.HotelCompanyInfo;
import com.pms.czmembership.bean.company.search.HotelCompanyAccountSearch;
import com.pms.czmembership.bean.company.search.HotelCompanyInfoSearch;
import com.pms.czmembership.dao.company.HotelCompanyAccountDao;
import com.pms.czmembership.dao.company.HotelCompanyInfoDao;
import com.pms.czpmsutils.DateUtil;
import com.pms.czpmsutils.OTA_TYPE;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.StringUtil;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.BookingRequest;
import com.pms.czpmsutils.request.CreateBookingOrderRequest;
import com.pms.pmsorder.bean.BookingOrder;
import com.pms.pmsorder.bean.BookingOrderDailyPrice;
import com.pms.pmsorder.bean.BookingOrderRoomNum;
import com.pms.pmsorder.bean.search.BookingOrderDailyPriceSearch;
import com.pms.pmsorder.bean.search.BookingOrderRoomNumSearch;
import com.pms.pmsorder.bean.search.BookingOrderSearch;
import com.pms.pmsorder.dao.BookingOrderDailyPriceDao;
import com.pms.pmsorder.dao.BookingOrderDao;
import com.pms.pmsorder.service.BookingOrderService;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class OtaOrderServiceImpl extends BaseService implements OtaOrderService {

    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderDailyPriceDao dailyPriceDao;

    @Autowired
    private RoomRateCodeDao roomRateCodeDao;

    @Autowired
    private HotelCompanyAccountDao hotelCompanyAccountDao;

    @Autowired
    private HotelCompanyInfoDao hotelCompanyInfoDao;

    @Autowired
    private RoomTypeDao roomTypeDao;

    @Autowired
    private OrderService orderService;

    @Autowired
    private BookingOrderService bookingOrderService;

    @Autowired
    private TurnAlwaysService turnAlwaysService;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Override
    public OtaPmsOrderInfo getOtaOrderInfoBySn(String orderSn) {
        log.info("getOtaOrderInfoBySn orderSn:{}", orderSn);
        BookingOrderSearch bookingOrderSearch = new BookingOrderSearch();
        bookingOrderSearch.setSn(orderSn);
        List<BookingOrder> order = bookingOrderDao.selectBySearch(bookingOrderSearch);
        if (order == null || order.size() == 0) {
            return null;
        }
        BookingOrder bookingOrder = order.get(0);
        BookingOrderDailyPriceSearch dailyPriceSearch = new BookingOrderDailyPriceSearch();
        dailyPriceSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
        List<BookingOrderDailyPrice> dailyPriceList = dailyPriceDao.selectBySearch(dailyPriceSearch);
        OtaPmsOrderInfo otaOrderInfo = new OtaPmsOrderInfo();
        BeanUtils.copyProperties(bookingOrder, otaOrderInfo);
        List<OtaOrderDailyPrice> otaOrderDailyPriceList = new ArrayList<>();
        if (!dailyPriceList.isEmpty()) {
            dailyPriceList = dailyPriceList
                    .stream()
                    .filter(item -> item.getBookingOrderRoomNumId()!=0)
                    .collect(Collectors.toList());
            for (BookingOrderDailyPrice bookingOrderDailyPrice : dailyPriceList) {
                OtaOrderDailyPrice otaOrderDailyPrice = new OtaOrderDailyPrice();
                BeanUtils.copyProperties(bookingOrderDailyPrice, otaOrderDailyPrice);
                otaOrderDailyPrice.setPriceDate(DateUtil.convertIntDateToStringDate(bookingOrderDailyPrice.getDailyTime()));
                otaOrderDailyPriceList.add(otaOrderDailyPrice);
            }
        }
        otaOrderInfo.setDailyPriceList(otaOrderDailyPriceList);
        log.info("getOtaOrderInfoBySn orderSn:{} , otaOrder:{}", orderSn,otaOrderInfo);
        return otaOrderInfo;
    }

    @Override
    public ResponseData createOtaOrder(OtaPmsOrderInfo orderInfo) {
        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
        //获取酒店信息
        try {
            TbUserSession userSession = this.getOtaTbUserSession(orderInfo.getHid());
            JSONObject addBookRes = null;
            if (orderInfo.getOrderType() == 1){
                JSONObject addBookParam = geNewBookParamFromOta(orderInfo,userSession);
                addBookRes = orderService.addBookNew(addBookParam);
            }else{
                CreateBookingOrderRequest addBookParam = geNewHourBookParamFromOta(orderInfo);
                ResponseData hourBookRes = orderService.createBookingOrder(addBookParam);
                addBookRes = JSONObject.fromObject(hourBookRes);
            }
            Integer orderId = 0;
            String orderSn = "";
            if(addBookRes == null || addBookRes.get(ER.RES).equals(ER.ERR)){
                throw new Exception(addBookRes.getString("订单创建失败"));
            }else{
                 orderId = addBookRes.getInt(ER.RES_DATA);
                 orderSn = addBookRes.getString("orderSn");
                //查询所有预房间 按照日期分组
                BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                bookingOrderRoomNumSearch.setBookingOrderId(orderId);
                bookingOrderRoomNumSearch.setSessionToken(userSession.getSessionId());
                ResponseData roomRes = bookingOrderService.serachBookingRoomForDay(bookingOrderRoomNumSearch);
//                JSONObject cancelBookParam = new JSONObject();
//                cancelBookParam.put("bookingOrderId",orderId);
//                cancelBookParam.put("sessionToken",userSession.getSessionId());
//                cancelBookParam.put("reason","无法获取预定房间信息，导致分配住客失败");
                if(roomRes.getResult().equals(ER.ERR)){
//                    orderService.cancelBook(cancelBookParam);
                    throw new Exception("无法获取预定房间信息，导致分配住客失败");
                }else{
                    List<BookingOrderRoomNum> bookingOrderRoomNumList = (List<BookingOrderRoomNum>) roomRes.getData();
                    for (BookingOrderRoomNum bookingOrderRoomNum : bookingOrderRoomNumList) {
                        BookingRequest bookingRequest = new BookingRequest();
                        bookingRequest.setBookingOrderId(bookingOrderRoomNum.getBookingOrderId());
                        for (GustInfo gustInfo :  orderInfo.getGustList()){
                            BookingRequest.Person person = new BookingRequest.Person();
                            person.setBookingOrderRoomNumId(bookingOrderRoomNum.getId());
                            person.setIdType(Integer.parseInt(gustInfo.getIdType()));
                            person.setPersonName(gustInfo.getName());
                            person.setIdType(Integer.parseInt(gustInfo.getIdType()));
                            person.setRoomNum("0");
                            person.setRoomTypeId(bookingOrderRoomNum.getRoomTypeId());
                        }
                        orderService.addPersonForBookingRoom2(bookingRequest);
                    }
                }
            }
            transactionManager.commit(status); // 提交事务
            ResponseData  responseData= ResponseData.newSuccessData();
            JSONObject resp = new JSONObject();
            resp.put("orderId", orderId);
            resp.put("orderSn",  orderSn);
            responseData.setData(resp);
            return responseData;
        } catch (Exception e) {
            transactionManager.rollback(status); // 回滚事务
            log.info("OTA 订单创建失败，回滚事务，异常信息{}",e);
            return  ResponseData.fail("OTA 订单创建失败");
        }
    }

    @Override
    public ResponseData updateOtaOrderStatus(OtaOrderStatusUpdate  statusUpdate) {
        try {
            bookingOrderDao.updateOtaStatus(statusUpdate.getOrderId(),statusUpdate.getStatus(),statusUpdate.getOtaStatus());
            Map<String, Object> resultMap = new HashMap<String, Object>();
            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
             TbUserSession user = null;
            if(StringUtils.isNotBlank(statusUpdate.getSessionToken())){
                user = this.getTbUserSession(statusUpdate.getSessionToken());
            }else{
                user = this.getOtaTbUserSession(statusUpdate.getHid());
            }
            turnAlwaysService.turnAlwaysCache(user, resultMap, userCahe);
            return ResponseData.success();
        } catch (Exception e) {
            log.info("OTA 订单状态更新失败，异常信息{}",e);
            return  ResponseData.fail("OTA 订单状态更新失败");
        }
    }

    @Override
    public ResponseData upaBookRoomNightAndNumFunc(OtaOrderRoomUpdateInfo orderRoomUpdateInfo) {
        log.info("upaBookRoomNightFunc orderRoomUpdateInfo:{}",orderRoomUpdateInfo);
        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());
        try{
            List<OtaOrderRoomUpdateInfo.BookingOrderRoomNumUpdateInfo> updateInfoList = orderRoomUpdateInfo.getBookingOrderRoomNumUpdateInfos();
            for (OtaOrderRoomUpdateInfo.BookingOrderRoomNumUpdateInfo updateInfo : updateInfoList) {
                UpaBookRoomRequest  upaBookRoomRequest = new UpaBookRoomRequest();
                upaBookRoomRequest.setBookingOrderId(orderRoomUpdateInfo.getBookingOrderId());
                upaBookRoomRequest.setSessionToken(orderRoomUpdateInfo.getSessionToken());
                upaBookRoomRequest.setStartTime(updateInfo.getStartTime());
                upaBookRoomRequest.setEndTime(updateInfo.getEndTime());
                List<BookingOrderRoomNumSearch> ids = new ArrayList<>();
                for (Integer id : updateInfo.getIds()) {
                    BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                    bookingOrderRoomNumSearch.setId(id);
                    ids.add(bookingOrderRoomNumSearch);
                }
                upaBookRoomRequest.setIds(ids);
                log.info("OTA 订单修改间夜信息请求对象{}",upaBookRoomRequest);
                ResponseData upRoomResp = orderService.upaBookRoomFunc(upaBookRoomRequest);
                log.info("OTA 订单修改间夜信息响应{}",upRoomResp);
                if(upRoomResp.getResult().equals(ER.ERR)){
                    log.info("OTA 订单修改间夜信息失败，回滚事务，失败信息{}",upRoomResp);
                    transactionManager.rollback(status);
                    return  ResponseData.fail("OTA 订单修改间夜信息失败");
                }
            }
            if (!orderRoomUpdateInfo.getDelBookOrderRoomNumIdList().isEmpty()){
                DelBookRoomRequest delBookRoomRequest = new DelBookRoomRequest();
                delBookRoomRequest.setSessionToken(orderRoomUpdateInfo.getSessionToken());
                delBookRoomRequest.setBookingOrderId(orderRoomUpdateInfo.getBookingOrderId());
                List<BookingOrderRoomNumSearch> bookingOrderRoomNumSearches = new ArrayList<>();
                for (Integer id : orderRoomUpdateInfo.getDelBookOrderRoomNumIdList()){
                    BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                    bookingOrderRoomNumSearch.setId(id);
                    bookingOrderRoomNumSearches.add(bookingOrderRoomNumSearch);
                }
                delBookRoomRequest.setBookingOrderRoomNumSearches(bookingOrderRoomNumSearches);
                log.info("OTA 订单删除间夜信息请求对象{}",delBookRoomRequest);
                ResponseData delResponseData = orderService.delBookRoomFunc(delBookRoomRequest);
                log.info("OTA 订单删除间夜信息响应对象{}",delResponseData);
                if(delResponseData.getResult().equals(ER.ERR)){
                    log.info("OTA删除间夜信息失败，回滚事务，失败信息{}",delResponseData);
                    transactionManager.rollback(status);
                    return  ResponseData.fail("OTA 订单修改间夜信息失败");
                }
            }
            transactionManager.commit(status); // 提交事务
            ResponseData  responseData= ResponseData.newSuccessData();
            JSONObject resp = new JSONObject();
            resp.put("orderId", orderRoomUpdateInfo.getBookingOrderId());
            responseData.setData(resp);
            return responseData;
        }catch (Exception e){
            transactionManager.rollback(status);
            log.info("OTA 订单修改间夜信息失败，回滚事务，异常信息{}",e);
            return  ResponseData.fail("OTA 订单修改间夜信息失败");
        }
    }

    @Override
    public  List<OtaOrderDailyPrice> getDailyPriceListByOrderList(List<Integer> orderIdList) {
        List<BookingOrderDailyPrice> dailyPriceList=     dailyPriceDao.selectByBookingOrderIds(orderIdList);
        List<OtaOrderDailyPrice> otaOrderDailyPriceList = new ArrayList<>();
        if (!dailyPriceList.isEmpty()) {
            dailyPriceList = dailyPriceList
                    .stream()
                    .filter(item -> item.getBookingOrderRoomNumId()!=0)
                    .collect(Collectors.toList());
            for (BookingOrderDailyPrice bookingOrderDailyPrice : dailyPriceList) {
                OtaOrderDailyPrice otaOrderDailyPrice = new OtaOrderDailyPrice();
                BeanUtils.copyProperties(bookingOrderDailyPrice, otaOrderDailyPrice);
                otaOrderDailyPrice.setPriceDate(DateUtil.convertIntDateToStringDate(bookingOrderDailyPrice.getDailyTime()));
                otaOrderDailyPriceList.add(otaOrderDailyPrice);
            }
        }
        return otaOrderDailyPriceList;
    }

    private JSONObject geNewBookParamFromOta(OtaPmsOrderInfo orderInfo, TbUserSession userSession) {
        try {
            JSONObject addBookParam = new JSONObject();
            log.info("OTA 订单请求对象转PMS预定请求对象开始，otaOrderInfo{}",orderInfo);
            //获取ota用户信息
            //初始化PMS预订单对象
            AddBookDTO addBookDTO = new AddBookDTO();
            //预定人信息
            addBookDTO.setBookingName(orderInfo.getBookingName());
            addBookDTO.setBookingPhone(orderInfo.getBookingPhone());
            //下游业务已经对入住类型进行了判断
            addBookDTO.setCheckInType(orderInfo.getType());
            //入住时间
            addBookDTO.setCheckinTime(DateUtil.formatDate(orderInfo.getCheckinTime(),DateUtil.DATE_D));
            addBookDTO.setCheckoutTime(DateUtil.formatDateWithTime(orderInfo.getCheckinTime(),DateUtil.DATE_D,"13:00"));
            //下游业务将订单来源设置为了华美会
            addBookDTO.setFromType(orderInfo.getFromType());
            addBookDTO.setGroupName("");
            addBookDTO.setIsGroup(0);
            addBookDTO.setKeepTime(orderInfo.getKeepTime());
            //客源类型应为5订房平台
            addBookDTO.setResourceId(orderInfo.getResourceId());
            addBookDTO.setRemark(orderInfo.getRemark());
            addBookDTO.setThirdPlatformOrderCode(orderInfo.getThirdPlatformOrderCode());
            addBookDTO.setPriceSecrecy(0);
            addBookDTO.setInfoSecrecy(0);
            addBookDTO.setAutoCheckIn(1);
            addBookDTO.setNoDposit(0);
            addBookDTO.setNoPrice(1);
            addBookDTO.setContinueRes(1);
            addBookDTO.setAutoAr(1);
            addBookDTO.setRateCodeId(orderInfo.getRateCodeId());
            //查询房价ID设置
            RoomRateCode roomRateCode = roomRateCodeDao.selectById(orderInfo.getRateCodeId());
            addBookDTO.setRateCodeName(roomRateCode.getRateCodeName());
            //查询订房平台
            HotelCompanyInfoSearch hotelCompanyInfoSearch = new HotelCompanyInfoSearch();
            hotelCompanyInfoSearch.setHid(orderInfo.getHid());
            hotelCompanyInfoSearch.setCompanyCode(OTA_TYPE.getCode(orderInfo.getFromType()));
            HotelCompanyInfo hotelCompanyInfo = hotelCompanyInfoDao.selectBySearch(hotelCompanyInfoSearch).get(0);
            AddBookDTO.ArMsg arMsg = new AddBookDTO.ArMsg();
            arMsg.setArId(hotelCompanyInfo.getId());
            arMsg.setArName(hotelCompanyInfo.getCompanyName());
            //查询订房平台账户
            HotelCompanyAccountSearch hotelCompanyAccountSearch = new HotelCompanyAccountSearch();
            hotelCompanyAccountSearch.setHid(orderInfo.getHid());
            hotelCompanyAccountSearch.setHotelCompanyId(hotelCompanyInfo.getId());
            HotelCompanyAccount hotelCompanyAccount = hotelCompanyAccountDao.selectBySearch(hotelCompanyAccountSearch).get(0);
            AddBookDTO.ArAntMsg arAntMsg = new AddBookDTO.ArAntMsg();
            arAntMsg.setId(hotelCompanyAccount.getId());
            //设置预定房间数量
            addBookDTO.setRoomCount(orderInfo.getRoomCount());
            List<AddBookDTO.RoomType> roomTypeList = new ArrayList<>();
            List<Integer> roomTypeIdList  = orderInfo.getBookRoomTypeInfoList()
                    .stream()
                    .map(item->item.getRoomTypeId())
                    .collect(Collectors.toList());
            //查询房型信息
            Map<Integer,RoomType> roomTypeMap = roomTypeDao.selectByIdList(roomTypeIdList)
                    .stream()
                    .collect(Collectors.toMap(RoomType::getRoomTypeId, roomType -> roomType));
            for (OtaBookRoomTypeInfo roomTypeInfo : orderInfo.getBookRoomTypeInfoList()) {
                AddBookDTO.RoomType roomTypeDTO = new AddBookDTO.RoomType();
                roomTypeDTO.setRoomTypeId(roomTypeInfo.getRoomTypeId());
                RoomType roomType = roomTypeMap.get(roomTypeInfo.getRoomTypeId());
                roomTypeDTO.setRoomTypeName(roomType.getRoomTypeName());
                roomTypeDTO.setNum(roomTypeInfo.getNum());
                List<AddBookDTO.Price> priceList = new ArrayList<>();
                for (OtaOrderDailyPrice dailyPrice : roomTypeInfo.getPriceList()){
                    AddBookDTO.Price price = new AddBookDTO.Price();
                    price.setDate(dailyPrice.getPriceDate());
                    price.setPrice(dailyPrice.getPrice());
                    priceList.add(price);
                }
                roomTypeDTO.setRoomList(new ArrayList<>());
                roomTypeList.add(roomTypeDTO);
            }
            //替换最后一个，为句号
            addBookDTO.setRoomTypeSummary(orderInfo.getRoomTypeSummary());
            addBookDTO.setRoomTypeList(roomTypeList);
            addBookParam.put("bookData", addBookDTO);
            addBookParam.put("sessionToken", userSession);
            log.info("OTA 订单请求对象转PMS预定请求对象成功，addBookParam{}",addBookParam);
            return  addBookParam;
        } catch (Exception e) {
            log.error("OTA 订单请求对象转PMS预定请求对象失败，otaOrderInfo{}",orderInfo);
            throw new RuntimeException("OTA 订单请求对象转PMS预定请求对象失败");
        }
    }

    private CreateBookingOrderRequest geNewHourBookParamFromOta(OtaPmsOrderInfo orderInfo){
        try {
            JSONObject addBookParam = new JSONObject();
            log.info("OTA 订单请求对象转PMS钟点房预定请求对象开始，otaOrderInfo{}",orderInfo);
            CreateBookingOrderRequest createBookingOrderRequest = new CreateBookingOrderRequest();
            //获取ota用户信息
            //初始化PMS预订单对象
            //预定人信息
            createBookingOrderRequest.setBookingName(orderInfo.getBookingName());
            createBookingOrderRequest.setBookingPhone(orderInfo.getBookingPhone());
            //下游业务已经对入住类型进行了判断
            createBookingOrderRequest.setCheckInType(orderInfo.getType());
            //入住时间
            createBookingOrderRequest.setCheckinTime(orderInfo.getCheckinTime());
            createBookingOrderRequest.setCheckoutTime(orderInfo.getCheckinTime());
            //下游业务将订单来源设置为了华美会
            createBookingOrderRequest.setFromType(orderInfo.getFromType());
            createBookingOrderRequest.setGroupName("");
            createBookingOrderRequest.setIsGroup(0);
            createBookingOrderRequest.setKeepTime(orderInfo.getKeepTime());
            //客源类型应为5订房平台
            createBookingOrderRequest.setResourceId(orderInfo.getResourceId());
            createBookingOrderRequest.setRemark(orderInfo.getRemark());
            createBookingOrderRequest.setThirdPlatformOrderCode(orderInfo.getThirdPlatformOrderCode());
            createBookingOrderRequest.setPriceSecrecy(0);
            createBookingOrderRequest.setInfoSecrecy(0);
            createBookingOrderRequest.setAutoCheckIn(1);
            createBookingOrderRequest.setNoDposit(0);
            createBookingOrderRequest.setNoPrice(1);
            createBookingOrderRequest.setContinueRes(1);
            createBookingOrderRequest.setAutoAr(1);
            createBookingOrderRequest.setRateCodeId(orderInfo.getRateCodeId());
            //查询房价ID设置
            //todo 钟点房的时候，默认一个房价，关联产品的时候，入参默认新增一个房价
            RoomRateCode roomRateCode = roomRateCodeDao.selectById(orderInfo.getRateCodeId());
            createBookingOrderRequest.setRateCodeName(roomRateCode.getRateCodeName());
            //查询订房平台
            HotelCompanyInfoSearch hotelCompanyInfoSearch = new HotelCompanyInfoSearch();
            hotelCompanyInfoSearch.setHid(orderInfo.getHid());
            hotelCompanyInfoSearch.setCompanyCode(OTA_TYPE.getCode(orderInfo.getFromType()));
            HotelCompanyInfo hotelCompanyInfo = hotelCompanyInfoDao.selectBySearch(hotelCompanyInfoSearch).get(0);
            AddBookDTO.ArMsg arMsg = new AddBookDTO.ArMsg();
            arMsg.setArId(hotelCompanyInfo.getId());
            arMsg.setArName(hotelCompanyInfo.getCompanyName());
            //查询订房平台账户
            HotelCompanyAccountSearch hotelCompanyAccountSearch = new HotelCompanyAccountSearch();
            hotelCompanyAccountSearch.setHid(orderInfo.getHid());
            hotelCompanyAccountSearch.setHotelCompanyId(hotelCompanyInfo.getId());
            HotelCompanyAccount hotelCompanyAccount = hotelCompanyAccountDao.selectBySearch(hotelCompanyAccountSearch).get(0);
            AddBookDTO.ArAntMsg arAntMsg = new AddBookDTO.ArAntMsg();
            arAntMsg.setId(hotelCompanyAccount.getId());
            //设置预定房间数量
            createBookingOrderRequest.setRoomCount(orderInfo.getRoomCount());
            List<CreateBookingOrderRequest.roomType> roomTypeList = new ArrayList<>();
            List<Integer> roomTypeIdList  = orderInfo.getBookRoomTypeInfoList()
                    .stream()
                    .map(item->item.getRoomTypeId())
                    .collect(Collectors.toList());
            //查询房型信息
            Map<Integer,RoomType> roomTypeMap = roomTypeDao.selectByIdList(roomTypeIdList)
                    .stream()
                    .collect(Collectors.toMap(RoomType::getRoomTypeId, roomType -> roomType));
            for (OtaBookRoomTypeInfo roomTypeInfo : orderInfo.getBookRoomTypeInfoList()) {
                CreateBookingOrderRequest.roomType roomTypeDTO = new CreateBookingOrderRequest.roomType();
                roomTypeDTO.setRoomTypeId(roomTypeInfo.getRoomTypeId());
                RoomType roomType = roomTypeMap.get(roomTypeInfo.getRoomTypeId());
                roomTypeDTO.setRoomTypeName(roomType.getRoomTypeName());
                roomTypeDTO.setNum(roomTypeInfo.getNum());
                List<AddBookDTO.Price> priceList = new ArrayList<>();
                for (OtaOrderDailyPrice dailyPrice : roomTypeInfo.getPriceList()){
                    AddBookDTO.Price price = new AddBookDTO.Price();
                    price.setDate(dailyPrice.getPriceDate());
                    price.setPrice(dailyPrice.getPrice());
                    priceList.add(price);
                }
                roomTypeDTO.setRoomList(new ArrayList<>());
                roomTypeList.add(roomTypeDTO);
            }
            //替换最后一个，为句号
            createBookingOrderRequest.setRoomTypeSummary(orderInfo.getRoomTypeSummary());
            createBookingOrderRequest.setRoomTypeList(roomTypeList);
            log.info("OTA 订单请求对象转PMS钟点房请求对象成功，addBookParam{}",addBookParam);
            return createBookingOrderRequest;
        } catch (Exception e) {
            log.error("OTA 订单请求对象转PMS钟点房预定请求对象失败，otaOrderInfo{}",orderInfo);
            throw new RuntimeException("OTA 订单请求对象转PM钟点房S预定请求对象失败");
        }


    }


}
