package com.pms.czabsorders.service.turnAlways;

import com.pms.czabsorders.bean.FindAvailableHourRoomRequest;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomInfoResult;
import com.pms.czhotelfoundation.bean.search.HourRoomDayUseSearch;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.user.TbUserSession;
import net.sf.json.JSONObject;
import org.springframework.data.redis.core.HashOperations;

import java.util.List;
import java.util.Map;

/**
 * 未来房情
 */
public interface TurnAlwaysService {
    /**
     * 查询未来房情
     * @return
     */
    public Map<String, Object> turnAlways(JSONObject param);

    /**
     * 查询日期段之内的可用的房间信息
     *      type
     *          1.在住
     *          2.预定
     * @param param
     * @return
     */
    public List<RoomInfoResult> canUseRoom(TbUserSession user, JSONObject param) throws Exception;


    /**
     * 查询可用的房间信息
     * @param param
     * @return
     */
    public Map<String,Object> findavailableRoom(JSONObject param);

    public ResponseData findAvailableHourRoom(FindAvailableHourRoomRequest findAvailableHourRoomRequest);

    /**
     * 查询房型每日的使用记录
     * @param param
     * @return
     */
    public ResponseData findRoomTypeUseData(JSONObject param);

    public void turnAlwaysCache(TbUserSession user, Map<String, Object> resultMap, HashOperations<String, Object, Object> userCahe) throws Exception;

    public void turnAlwaysCacheFunc(TbUserSession user);

    public List<RoomInfo> updateRoomListForCache(final TbUserSession user);

    public Map<String,Object> findGroupHotelRoom(JSONObject param);

    // 查询钟点房每日房情
    public ResponseData hourTurnAlways(HourRoomDayUseSearch hourRoomDayUseSearch);

}
