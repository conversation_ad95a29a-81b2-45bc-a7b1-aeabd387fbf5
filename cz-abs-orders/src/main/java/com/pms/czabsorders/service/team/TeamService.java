package com.pms.czabsorders.service.team;


import com.pms.czabsorders.bean.TeamInfo;
import com.pms.czpmsutils.ResponseData;
import com.pms.pmsorder.bean.request.RegistGroupRequest;
import net.sf.json.JSONObject;

import java.util.Map;

/**
 * 关于团队/联房的处理
 */
public interface TeamService {


    /**
     * 解除团队
     * @param param
     * @return
     */
    public Map<String,Object> relieveTeam(JSONObject param);


    public Map<String,Object> mergeTeam(JSONObject param);

    /**
     * 设置主账房
     * @param param
     * @return
     */
    public ResponseData setMainRoom(JSONObject param);

    /**
     * 修改团队信息
     * @param param
     * @return
     */
    public ResponseData updateTeamInfo(TeamInfo teamInfo);

    /**
     * 团队挂账
     * @param teamInfo
     * @return
     */
    public ResponseData teamOnAccount(TeamInfo teamInfo);

    /**
     * 查询所有团队
     * @param registGroupSearch
     * @return
     */
    public ResponseData selectAllTeam(RegistGroupRequest registGroupSearch);

    public ResponseData searchTeamDetails(RegistGroupRequest registGroupSearch);

}
