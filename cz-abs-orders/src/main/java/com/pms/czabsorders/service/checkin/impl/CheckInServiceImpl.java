package com.pms.czabsorders.service.checkin.impl;


import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.*;
import com.pms.czabsorders.feign.OtaFeign;
import com.pms.czabsorders.service.account.AccountService;
import com.pms.czabsorders.service.checkin.CheckInService;
import com.pms.czabsorders.service.checkin.transaction.CheckInTransactionService;
import com.pms.czabsorders.service.ota.OtaChangePushUtil;
import com.pms.czabsorders.service.turnAlways.TurnAlwaysService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.AccountThirdPayRecode;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.hotel.HourRoomInfo;
import com.pms.czhotelfoundation.bean.price.RoomRateCode;
import com.pms.czhotelfoundation.bean.price.RoomRateCodeSpecific;
import com.pms.czhotelfoundation.bean.price.search.RoomRateCodeSpecificSearch;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomType;
import com.pms.czhotelfoundation.bean.room.search.RoomAuxiliaryRelationSearch;
import com.pms.czhotelfoundation.bean.search.HourRoomDayUseSearch;
import com.pms.czhotelfoundation.dao.FaceSetDao;
import com.pms.czhotelfoundation.dao.HourRoomDayUseDao;
import com.pms.czhotelfoundation.dao.code.HotelBusinessDayDao;
import com.pms.czhotelfoundation.dao.hotel.HourRoomInfoDao;
import com.pms.czhotelfoundation.dao.price.RoomRateCodeSpecificDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomCheckRecordDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czhotelfoundation.service.room.RoomService;
import com.pms.czhotelfoundation.service.room.transaction.RoomTransactionService;
import com.pms.czhotelfoundation.service.zimg.ZimgService;
import com.pms.czmembership.bean.member.CardInfo;
import com.pms.czmembership.bean.member.CardMemberLevel;
import com.pms.czmembership.bean.member.search.CardMemberLevelSearch;
import com.pms.czmembership.dao.member.CardInfoDao;
import com.pms.czmembership.dao.member.CardMemberLevelDao;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.conf.HotelIotPlatConfig;
import com.pms.czpmsutils.constant.*;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.hotelsetting.HOTEL_SETTING;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.regist.CHECK_IN_TYPE;
import com.pms.czpmsutils.constant.room.ROOM_AUXILIARY;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.OprecordInfoRequest;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.enums.HmhOrderStatusEnum;
import com.pms.czpmsutils.request.*;
import com.pms.czpmsutils.thirdauth.HotelIotStrategy;
import com.pms.czpmsutils.view.AESUtil;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.request.GetHotelDataInfoParam;
import com.pms.pmsorder.bean.search.*;
import com.pms.pmsorder.bean.view.ResourceView;
import com.pms.pmsorder.dao.*;
import com.pms.pmsorder.service.RegistService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 预订单入住相关的 逻辑
 */
@Slf4j
@Service
@Primary
public class CheckInServiceImpl extends BaseService implements CheckInService {

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private RegistDao registDao;

    @Autowired
    private ZimgService zimgService;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private RoomTypeDao roomTypeDao;


    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private RegistGroupDao registGroupDao;

    @Autowired
    private RoomTransactionService roomTransactionService;

    @Autowired
    private RoomService roomService;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private CheckInTransactionService checkInTransactionService;

    @Autowired
    private RegistService registService;

    @Autowired
    private TurnAlwaysService turnAlwaysService;

    @Autowired
    private OtaChangePushUtil otaChangePushService;

    private BaseService baseService = this;

    @Autowired
    private AccountService accountService;

    @Autowired
    private RoomRateCodeSpecificDao roomRateCodeSpecificDao;

    @Autowired
    private HotelBusinessDayDao hotelBusinessDayDao;

    @Autowired
    private RoomCheckRecordDao roomCheckRecordDao;

    @Autowired
    private FaceSetDao faceSetDao;

    @Autowired
    private PmsMainDao pmsMainDao;

    @Autowired
    private PersonInfoDao personInfoDao;

    @Autowired
    private HourRoomInfoDao hourRoomInfoDao;

    @Autowired
    private HourRoomDayUseDao hourRoomDayUseDao;

    @Resource
    private HotelIotPlatConfig iotPlatConfig;

    @Resource
    private WebClientUtil webClientUtil;

    @Resource
    private CardMemberLevelDao cardMemberLevelDao;

    @Resource
    private CardInfoDao cardInfoDao;
    @Autowired
    private OtaFeign otaFeign;

    /**
     * 入住---团队、散客、联房
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData blendCheckIn(JSONObject param) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {

            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            //日志记录
            ArrayList<OprecordInfoRequest> oprecordInfoRequests = new ArrayList<>();

            // 预订单参数
            String checkInParam = URLDecoder.decode(param.getString("checkInParam"), "utf-8");
            final JSONObject checkInJson = JSONObject.fromObject(checkInParam);

            JSONArray roomList = checkInJson.getJSONArray("roomList");

            // 房价码
            int rateId = checkInJson.getInt("rateId");
            String rateCode = checkInJson.getString("rateCode");

            RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
            roomRateCodeSpecificSearch.setHid(user.getHid());
            roomRateCodeSpecificSearch.setRateId(rateId);
            List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);
            Map<Integer, RoomRateCodeSpecific> rateMap = roomRateCodeSpecifics.stream().collect(Collectors.toMap(RoomRateCodeSpecific::getRoomTypeId, a -> a, (k1, k2) -> k1));

            // 离店日期
            Date endDate = HotelUtils.parseStr2Date(checkInJson.getString("endTime") + " " + checkInJson.getString("keepTime") + ":00");
            //入住天数
            int dayCount = HotelUtils.getAllDayListBetweenDate(checkInJson.getString("startTime"), checkInJson.getString("endTime")).size();

            // 辅助房态集合
            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelations = new ArrayList<>();

            // 1.创建最初的regist

            Regist regist = new Regist();
            regist.setHid(user.getHid());
            regist.setHotelGroupId(user.getHotelGroupId());
            regist.setClassId(user.getClassId());
            regist.setCreateTime(new Date());
            regist.setCreateUserId(user.getUserId());
            regist.setCreateUserName(user.getUserName());
            regist.setBusinessDay(user.getBusinessDay());
            regist.setClassId(user.getClassId());
            regist.setRoomRateCodeId(rateId);
            regist.setRoomRateCodeName(rateCode);
            regist.setDayCount(dayCount);
            regist.setCheckinTime(new Date());
            regist.setCheckoutTime(endDate);
            regist.setRemark("");
            if (checkInJson.containsKey("saleId") && checkInJson.get("saleId") != null) {
                regist.setSaleId(checkInJson.getInt("saleId"));
            }

            regist.setRegistYear(user.getBusinessYear());
            regist.setRegistYearMonth(user.getBusinessMonth());

            if (checkInJson.get("remark") != null) {
                regist.setRemark(checkInJson.getString("remark"));
            }

            regist.setAutoCheckout(0);
            regist.setCheckinType(checkInJson.getInt("checkInType"));

            // 入住方式 0前台 1自助机
            regist.setCheckinMode(0);
            if (checkInJson.get("checkInModel") != null) {
                regist.setCheckinMode(checkInJson.getInt("checkInModel"));
            }

            int resourceId = checkInJson.getInt("resourceId");
            regist.setResourceId(resourceId);

            if (checkInJson.get("resourceName") != null) {
                regist.setResourceName(checkInJson.getString("resourceName"));
            }

            // 1.散客 2.会员
            if (resourceId == 2) {
                JSONObject memberInfo = checkInJson.getJSONObject("vipMsg");
                regist.setMemberId(memberInfo.getInt("cardId"));
                regist.setMemberCard(memberInfo.getString("cardNo"));

              /*  RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                roomAuxiliaryRelation.setHid(user.getHid());
                roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.VIP);
                roomAuxiliaryRelations.add(roomAuxiliaryRelation);*/

            }
            if (resourceId == 3 || resourceId == 4 || resourceId == 5) {
                JSONObject arMsg = checkInJson.getJSONObject("arMsg");
                regist.setCompanyId(arMsg.getInt("arId"));
                regist.setCompayName(arMsg.getString("arName"));
                if (checkInJson.get("arAntMsg") != null && !"".equals(checkInJson.getString("arAntMsg"))) {
                    regist.setCompanyAccountId(checkInJson.getJSONObject("arAntMsg").getInt("id"));
                }

            }

            Integer checkinType = regist.getCheckinType();
            if (checkinType == CHECK_IN_TYPE.CIT_HOURDAY) {

                RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                roomAuxiliaryRelation.setHid(user.getHid());
                roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.HOUR_ROOM);
                roomAuxiliaryRelations.add(roomAuxiliaryRelation);

            }


            if (checkInJson.get("macCheckIn") != null && checkInJson.getBoolean("macCheckIn")) {
                regist.setCheckinMode(1);
                RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                roomAuxiliaryRelation.setHid(user.getHid());
                roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.MACHINE);
                roomAuxiliaryRelations.add(roomAuxiliaryRelation);
            }

            regist.setState(0);
            // 2.检测不是团队团队
            int isGroup = 0;
            if (checkInJson.get("isGroup") != null) {
                isGroup = checkInJson.getInt("isGroup");
            }
            boolean isTeam = !(isGroup == 0 && roomList.size() == 1);
            RegistGroup registGroup = null;
            //没有设置团队，以及联房
            if (isTeam) {
                registGroup = new RegistGroup();
                registGroup.setRegistGroupId(0);
                registGroup.setHid(user.getHid());
                registGroup.setHotelGroupId(user.getHotelGroupId());
                registGroup.setClassId(user.getClassId());
                registGroup.setCreateTime(new Date());
                registGroup.setCreateUserId(user.getUserId());
                registGroup.setCreateUserName(user.getUserName());
                registGroup.setRoomRateCodeId(rateId);
                registGroup.setRoomRateCodeName(rateCode);
                registGroup.setGroupType(9);
                registGroup.setBusinessDay(user.getBusinessDay());
                registGroup.setMemberCard(regist.getMemberCard());
                registGroup.setMemberId(regist.getMemberId());
                registGroup.setCompanyId(regist.getCompanyId());
                registGroup.setCompayName(regist.getCompayName());
                registGroup.setSumRooms(roomList.size());
                if (isGroup == 1) {
                    Object groupType = checkInJson.get("groupType");
                    if (groupType != null) {
                        registGroup.setGroupType(Integer.parseInt(groupType.toString()));
                    }
                    registGroup.setGroupName(checkInJson.getString("groupName"));
                    registGroup.setState(1);
                    String g = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.GROUP, stringRedisTemplate);
                    registGroup.setSn(g);
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.TEAM);
                    roomAuxiliaryRelations.add(roomAuxiliaryRelation);
                } else if (roomList.size() > 1) { // roomList 房间数大于1 则联房
                    registGroup.setGroupType(9);
                    registGroup.setState(1);
                    String g = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.GROUP, stringRedisTemplate);
                    registGroup.setGroupName("联房" + g.substring(g.length() - 4, g.length()));
                    registGroup.setSn(g);
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.JOINT_HOUSING);
                    roomAuxiliaryRelations.add(roomAuxiliaryRelation);

                }
            }

            // 3.配置信息
            BookingOrderConfig bookingOrderConfig = new BookingOrderConfig();
            bookingOrderConfig.setPriceSecrecy(checkInJson.getBoolean("priceSecrecy") ? 1 : 0);
            bookingOrderConfig.setInfoSecrecy(checkInJson.getBoolean("infoSecrecy") ? 1 : 0);
            bookingOrderConfig.setAutoCheckin(checkInJson.getBoolean("autoCheckIn") ? 1 : 0);
            bookingOrderConfig.setNoDeposit(checkInJson.getBoolean("noDposit") ? 1 : 0);
            bookingOrderConfig.setNoPrice(checkInJson.getBoolean("noPrice") ? 1 : 0);
            bookingOrderConfig.setContinueRes(checkInJson.getBoolean("continueRes") ? 1 : 0);
            if (checkInJson.get("autoAr") != null) {
                bookingOrderConfig.setAutoAr(checkInJson.getBoolean("autoAr") ? 1 : 0);
            }

            // 叫早
            int morningCall = 0;
            if (null != checkInJson.get("morningCall") && !"".equals(checkInJson.getString("morningCall"))) {
                morningCall = checkInJson.getBoolean("morningCall") ? 1 : 0;
            }
            bookingOrderConfig.setMorningCall(morningCall);


            // 4.遍历房型
            // 所有入住人信息

            // 需要删除的辅助房态
            ArrayList<RoomAuxiliaryRelation> deleteRelations = new ArrayList<>();

            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setHid(user.getHid());


            int pNum = 0;

            Date date = new Date();

            StringBuilder roomBuilder = new StringBuilder();


            // 需要保存的登记单信息
            final ArrayList<CheckInRegist> checkInRegists = new ArrayList<>();

            BookingOrderDailyPrice bodp = new BookingOrderDailyPrice();

            RegistPerson registPerson = new RegistPerson();


            Map<Integer, List<HourRoomDayUse>> hourRoomMap = new HashMap<>();


            // 开始日期结束日期是否为同一天
            Boolean starEndOneDay = false;

            // 开始小时
            int inHours = regist.getCheckinTime().getHours();

            // 结束小时
            int outTours = regist.getCheckoutTime().getHours();

            HashMap<Integer, Boolean> hourStarMap = new HashMap<>();

            HashMap<Integer, Boolean> hourEndMap = new HashMap<>();

            String useStarHourStr = "";
            String useEndHourStr = "";

            // 开始时间
            Integer businessDayMin = HotelUtils.parseDate2Int(regist.getCheckinTime());

            // 结束时间
            Integer businessDayMax = HotelUtils.parseDate2Int(regist.getCheckoutTime());

            //  需要添加的钟点房使用
            ArrayList<HourRoomDayUse> addHourUse = new ArrayList<>();

            // 需要删除的钟点房使用详情
            ArrayList<HourRoomDayUse> upaHourUse = new ArrayList<>();

            // 钟点房验证
            if (checkinType == 2) {
                // 钟点房验证
                HourRoomDayUseSearch hourRoomDayUseSearch = new HourRoomDayUseSearch();

                hourRoomDayUseSearch.setHid(user.getHid());
                hourRoomDayUseSearch.setBusinessDayMax(businessDayMax);
                hourRoomDayUseSearch.setBusinessDayMin(businessDayMin);

                // 每天使用情况
                Page<HourRoomDayUse> hourRoomDayUses = hourRoomDayUseDao.selectBySearch(hourRoomDayUseSearch);

                if (hourRoomDayUses.size() > 0) {

                    hourRoomMap = hourRoomDayUses.stream().collect(Collectors.groupingBy(HourRoomDayUse::getRoomInfoId));

                }

                // 如果不是同一天，则把第二天日期也计算出来。
                if (!businessDayMin.equals(businessDayMax)) {
                    starEndOneDay = true;

                    for (int i = 0; i <= outTours; i++) {
                        hourEndMap.put(i, true);
                        useEndHourStr += i + ",";
                    }
                    outTours = 23;
                    useEndHourStr = useEndHourStr.substring(0, useEndHourStr.length() - 1);
                }

                for (int i = inHours; i <= outTours; i++) {
                    hourStarMap.put(i, true);
                    useStarHourStr += i + ",";
                }
                useStarHourStr = useStarHourStr.substring(0, useStarHourStr.length() - 1);
            }

            for (int i = 0; i < roomList.size(); i++) {

                JSONObject rlo = roomList.getJSONObject(i);

                CheckInRegist checkInRegist = new CheckInRegist();

                // 1 添加登记单
                String regStr = JSONObject.fromObject(regist).toString();
                Regist roomRegist = (Regist) JSONObject.toBean(JSONObject.fromObject(regStr), Regist.class);
                roomRegist.setRoomNum(rlo.getString("roomNum"));
                roomRegist.setRoomNumId(rlo.getInt("roomInfoId"));
                /**
                 * 处理智能门锁编码的问题
                 */
                String lockNum = rlo.containsKey("lockNum") && rlo.get("lockNum") != null ? rlo.getString("lockNum") : "";
                roomRegist.setSessionToken(lockNum);
                roomBuilder.append(roomRegist.getRoomNum());
                roomBuilder.append(",");


                RoomInfo roomInfo = roomInfoDao.selectById(roomRegist.getRoomNumId());
                if (roomInfo.getRoomNumState() != 1) {
                    turnAlwaysService.updateRoomListForCache(user);
                    throw new Exception(roomInfo.getRoomNum() + "房间状态不可用");
                }

                roomRegist.setRoomTypeId(rlo.getInt("roomTypeId"));
                roomRegist.setRoomTypeName(rlo.getString("roomTypeName"));
                roomRegist.setIsMainRoom(0);

                String no = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.REGIST, this.stringRedisTemplate);

                roomRegist.setSn(no);

                if (i == 0 && roomList.size() > 1) {
                    roomRegist.setIsMainRoom(1);
                } else if (i == 0 && isTeam) {
                    roomRegist.setIsMainRoom(1);
                }

                checkInRegist.setRegist(roomRegist);

                // 2 添加 配置
                checkInRegist.setBookingOrderConfig(bookingOrderConfig);

                // 3 添加价格
                JSONArray priceList = rlo.getJSONArray("priceList");

                // 房型添加的房价
                ArrayList<BookingOrderDailyPrice> rtPriceList = new ArrayList<>();

                RoomRateCodeSpecific roomRateCodeSpecific = rateMap.get(roomRegist.getRoomTypeId());
                if (roomRateCodeSpecific == null) {
                    roomRateCodeSpecific = new RoomRateCodeSpecific();
                }

                for (int pl = 0; pl < priceList.size(); pl++) {
                    JSONObject plObj = priceList.getJSONObject(pl);
                    bodp = new BookingOrderDailyPrice();
                    bodp.setBookingOrderRoomNumId(0);
                    bodp.setHid(user.getHid());
                    bodp.setHotelGroupId(user.getHotelGroupId());
                    bodp.setPrice(plObj.getInt("price"));
                    bodp.setDailyTime(Integer.parseInt(plObj.getString("date").replace("-", "")));
                    bodp.setRoomTypeId(roomRegist.getRoomTypeId());
                    bodp.setRoomNumId(0);
                    bodp.setBreakNum(roomRateCodeSpecific.getBreakfastNum());
                    bodp.setDailyState(1);
                    bodp.setIsStayover(0);
                    bodp.setCreateTime(date);
                    bodp.setCreateUserId(user.getUserId());
                    bodp.setCreateUserName(user.getUserName());
                    bodp.setUpdateTime(date);
                    bodp.setUpdateUserId(user.getUserId());
                    bodp.setUpdateUserName(user.getUserName());
                    rtPriceList.add(bodp);
                }

                checkInRegist.setBookingOrderDailyPrices(rtPriceList);

                // 4 添加入住人

                JSONArray guestList = JSONArray.fromObject(rlo.get("guestList"));
                List<RegistPerson> registPeople = this.addCheckinGuest(guestList, user, roomRegist);
                checkInRegist.setRegistPeople(registPeople);

                pNum += registPeople.size();

                // 添加辅助房态

                checkInRegist.setRoomAuxiliaryRelations(roomAuxiliaryRelations);


                checkInRegists.add(checkInRegist);

                // 要删除的辅助房态 只删除预订中的辅助房态，且预订单号相同
                roomAuxiliaryRelationSearch.setRoomId(roomRegist.getRegistId());
                List<RoomAuxiliaryRelation> roomAuxiliaryRelations1 = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

                for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations1) {

                    if (roomAuxiliaryRelation.getRoomAuxiliaryId() > ROOM_AUXILIARY.BOOK_ARRIVALS) {
                        continue;
                    }

                    if (regist.getBookingOrderId() == null || roomAuxiliaryRelation.getBookingOrderId() == null) {
                        continue;
                    }

                    if (!regist.getBookingOrderId().equals(roomAuxiliaryRelation.getBookingOrderId())) {
                        continue;
                    }

                    deleteRelations.add(roomAuxiliaryRelation);

                }

                Integer checkinType1 = roomRegist.getCheckinType();

                // 不等于钟点房，则跳出循环
                if (checkinType1 != 2) {
                    continue;
                }


                // 验证当前时段是否被租用
                List<HourRoomDayUse> roomHourUse = hourRoomMap.get(roomInfo.getRoomInfoId());

                // 说明当前时间没有
                if (roomHourUse == null || roomHourUse.size() < 1) {

                    HourRoomDayUse hourRoomDayUse = getHourRoomDayUse(roomRegist);
                    hourRoomDayUse.setBusinessDay(businessDayMin);
                    hourRoomDayUse.setUseMsg(useStarHourStr);

                    addHourUse.add(hourRoomDayUse);

                    // 如果是隔一天
                    if (starEndOneDay) {

                        HourRoomDayUse hourRoomDayUseEnd = getHourRoomDayUse(roomRegist);
                        hourRoomDayUseEnd.setBusinessDay(businessDayMax);
                        hourRoomDayUseEnd.setUseMsg(useEndHourStr);

                        addHourUse.add(hourRoomDayUseEnd);

                    }

                } else {

                    // 验证当天是否存在
                    Map<Integer, HourRoomDayUse> hourRoomDayUseMap = roomHourUse.stream().collect(Collectors.toMap(HourRoomDayUse::getBusinessDay, a -> a, (k1, k2) -> k1));

                    HourRoomDayUse hourRoomDayUse = hourRoomDayUseMap.get(businessDayMin);
                    if (hourRoomDayUse == null) {

                        hourRoomDayUse = getHourRoomDayUse(regist);
                        hourRoomDayUse.setBusinessDay(businessDayMin);
                        hourRoomDayUse.setUseMsg(useStarHourStr);

                        addHourUse.add(hourRoomDayUse);

                    } else {


                        String[] split = hourRoomDayUse.getUseMsg().split(",");
                        Arrays.sort(split);

                        Boolean noRoom = false;

                        String errMsg = "";

                        for (int ion = 0; ion < split.length; ion++) {

                            int i1 = Integer.parseInt(split[ion]);

                            Boolean aBoolean = hourStarMap.get(i1);
                            if (aBoolean != null && aBoolean) {
                                noRoom = true;
                                errMsg += split[ion];
                                errMsg += ",";
                            }
                        }

                        if (noRoom) {
                            // throw new Exception(regist.getRoomNum() + businessDayMin + "：" + errMsg + "时段不可以");
                        }

                        String newUseMsg = hourRoomDayUse.getUseMsg() + "," + useStarHourStr;
                        hourRoomDayUse.setUseMsg(newUseMsg);

                        upaHourUse.add(hourRoomDayUse);

                    }


                    // 如果是隔一天
                    if (starEndOneDay) {

                        HourRoomDayUse hourRoomDayUseEnd = hourRoomDayUseMap.get(businessDayMax);

                        if (hourRoomDayUseEnd == null) {
                            hourRoomDayUseEnd = getHourRoomDayUse(regist);
                            hourRoomDayUseEnd.setBusinessDay(businessDayMax);
                            hourRoomDayUseEnd.setUseMsg(useEndHourStr);

                            addHourUse.add(hourRoomDayUseEnd);
                        } else {

                            String[] split = hourRoomDayUseEnd.getUseMsg().split(",");
                            Arrays.sort(split);

                            Boolean noRoom = false;

                            String errMsg = "";

                            for (int ion = 0; ion < split.length; ion++) {

                                int i1 = Integer.parseInt(split[ion]);

                                Boolean aBoolean = hourStarMap.get(i1);
                                if (aBoolean != null && aBoolean) {
                                    noRoom = true;
                                    errMsg += split[ion];
                                    errMsg += ",";
                                }
                            }

                            if (noRoom) {
                                // throw new Exception(regist.getRoomNum() + businessDayMin + "：" + errMsg + "时段被预订");
                            }

                            String newUseMsg = hourRoomDayUseEnd.getUseMsg() + "," + useEndHourStr;
                            hourRoomDayUseEnd.setUseMsg(newUseMsg);

                            upaHourUse.add(hourRoomDayUse);


                        }

                    }
                }
            }

            if (isTeam) {
                registGroup.setSumPersonNum(pNum);
            }

            JSONArray accountList = checkInJson.getJSONArray("accountList");

            int sumMoney = 0;

            ArrayList<Account> accounts = new ArrayList<>();

            for (int i = 0; i < accountList.size(); i++) {

                JSONObject anc = accountList.getJSONObject(i);

                int money = anc.getInt("money");
                sumMoney += money;

                JSONObject selectedPayTypeInfo = anc.getJSONObject("selectedPayTypeInfo");

                String a = HotelUtils.getHIDUUID32("A", user.getHid());
                Account account = new Account();
                account.setAccountId(a);
                account.setHid(user.getHid());
                account.setCreateUserId(user.getUserId());
                account.setCreateUserName(user.getUserName());
                account.setCreateTime(new Date());
                account.setIsCancel(0);
                account.setAccountYear(user.getBusinessYear());
                account.setAccountYearMonth(user.getBusinessMonth());
                account.setBusinessDay(user.getBusinessDay());
                account.setClassId(user.getClassId());
                account.setPrice(money);
                account.setSettleAccountTime(new Date());

                account.setThirdRefundState(0);
                //类型 ：消费、付款
                int payType = anc.getInt("costType");
                account.setPayType(payType);

                if (anc.get("saleNum") != null) {
                    account.setSaleNum(anc.getInt("saleNum"));
                } else {
                    account.setSaleNum(1);
                }
                account.setUintPrice(money / account.getSaleNum());
                //费用码
                int costClassId = selectedPayTypeInfo.getInt("pId");
                String costClassName = selectedPayTypeInfo.getString("pName");
                String costCodeId = selectedPayTypeInfo.getString("costNum");
                String costCodeName = selectedPayTypeInfo.getString("name");

                account.setPayClassId(costClassId);
                account.setPayClassName(costClassName);
                account.setPayCodeId(costCodeId);
                account.setPayCodeName(costCodeName);

                Object accountType = anc.get("accountType");
                if (accountType != null) {
                    account.setAccountType(Integer.parseInt(accountType.toString()));
                }


                /**
                 * 根据不同的费用码判断进行不同的操作
                 *  微信/支付宝 获取 扫码账单
                 */
                switch (costCodeId) {
                    case "9320": //微信扫码支付

                        account.setThirdRefundState(-1);
                        if (anc.get("thirdRefundState") != null) {
                            account.setThirdRefundState(anc.getInt("thirdRefundState"));
                        }
                        String wxmainId = "";
                        if (anc.get("mainId") != null) {
                            wxmainId = anc.get("mainId").toString();
                        }
                        account.setThirdAccoutId(wxmainId);
                        break;

                    case "9300": //支付宝扫码支付
                        account.setThirdRefundState(-1);
                        if (anc.get("thirdRefundState") != null) {
                            account.setThirdRefundState(anc.getInt("thirdRefundState"));
                        }
                        String alimainId = "";
                        if (anc.get("mainId") != null) {
                            alimainId = anc.get("mainId").toString();
                        }
                        account.setThirdAccoutId(alimainId);
                        break;

                    case "9100": //银行卡预授权

                        AccountThirdPayRecode accountThirdPayRecode = new AccountThirdPayRecode();
                        String at = HotelUtils.getHIDUUID32("AT", user.getHid());
                        accountThirdPayRecode.setHid(user.getHid());
                        accountThirdPayRecode.setAccountId(account.getAccountId());
                        accountThirdPayRecode.setHotelGroupId(user.getHotelGroupId());
                        accountThirdPayRecode.setAccountThirdId(at);
                        //款台号
                        accountThirdPayRecode.setCounterId(HotelUtils.validaStr("counterId"));
                        //操作员号
                        accountThirdPayRecode.setOperatorId(HotelUtils.validaStr("operatorId"));
                        //交易编号
                        accountThirdPayRecode.setTransType(HotelUtils.validaStr("transType"));
                        //金额
                        accountThirdPayRecode.setAmount(account.getPrice());
                        //48域附加信息
                        accountThirdPayRecode.setMemo(HotelUtils.validaStr(anc.get("memo")));
                        //三个校验字符串
                        accountThirdPayRecode.setLrc(HotelUtils.validaStr(anc.get("lrc")));
                        //终端流水号
                        accountThirdPayRecode.setTrace(HotelUtils.validaStr(anc.get("trace")));
                        //银行id
                        accountThirdPayRecode.setBarkId(HotelUtils.validaStr(anc.get("barkId")));
                        //批次号
                        accountThirdPayRecode.setBatch(HotelUtils.validaStr(anc.get("batch")));
                        //交易日期 yyyyMMdd
                        accountThirdPayRecode.setTransDate(HotelUtils.validaStr(anc.get("transDate")));
                        //交易时间 hhmmss
                        accountThirdPayRecode.setTransTime(HotelUtils.validaStr(anc.get("transTime")));
                        //系统参考号
                        accountThirdPayRecode.setRef(HotelUtils.validaStr(anc.get("ref")));
                        //授权号
                        accountThirdPayRecode.setAuth(HotelUtils.validaStr(anc.get("auth")));
                        //商户号
                        accountThirdPayRecode.setMid(HotelUtils.validaStr(anc.get("mId")));
                        //终端号
                        accountThirdPayRecode.setTid(HotelUtils.validaStr(anc.get("tId")));
                        //有效期
                        accountThirdPayRecode.setEffectiveDays(HotelUtils.validaStr(anc.get("effectiveDays")));
                        //预授权
                        accountThirdPayRecode.setPayType(3);
                        //营业日
                        accountThirdPayRecode.setBusinessDay(user.getBusinessDay());
                        //日期
                        accountThirdPayRecode.setCreateTime(date);
                        accountThirdPayRecode.setClassId(user.getClassId());
                        accountThirdPayRecode.setCreateUserId(user.getUserId());
                        accountThirdPayRecode.setCreateUserName(user.getUserName());

                        account.setAccountThirdPayRecode(accountThirdPayRecode);
                        break;

                    case "9600": //会员储值卡

                 /*   int cardId = param.getInt("cardId");
                    CardInfo cardInfo = cardInfoDao.selectById(cardId);

                    if(cardInfo==null){
                        throw new Exception("未查到相关的会员信息");
                    }
                    Integer cardConsuptionRecordId = memberTransactionService.cardConsuptionRecord(user, cardInfo, account, account.getRoomNum() + "：会员支付");
                    account.setThirdAccoutId(cardConsuptionRecordId.toString());
                    account.setMemberId(cardId);*/

                        break;
                }
                //备注
                if (param.get("remark") != null) {
                    String remark = URLDecoder.decode(param.getString("remark"), "utf-8");
                    account.setRemark(remark);
                }

                //理由
                account.setReason(HotelUtils.validaStr(param.get("reason")));
                account.setRefundPrice(0);

                accounts.add(account);

            }


            final JSONArray jsonArray = checkInTransactionService.blendCheckInTransaction(registGroup, checkInRegists, accounts, sumMoney, user, deleteRelations, addHourUse, upaHourUse);

            final String s = roomBuilder.toString();

            // 更新房间缓存
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    try {
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();

                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        HashMap<String, String> filedMap = new HashMap<>();
                        filedMap.put("roomList", s);

                        int pcId = 3;
                        if (checkInJson.get("macCheckIn") != null && checkInJson.getBoolean("macCheckIn")) {
                            pcId = 7;
                        }

                        baseService.push(user.getHotelGroupId(), user.getHid(), pcId, filedMap, new HashMap<String, String>(), true, true);

                        HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
                        hotelSettingByParamId.setHid(user.getHid());
                        hotelSettingByParamId.setParamId(HOTEL_SETTING.AUTO_ADD_ROOMPRICE);
                        // 登记后多久产生房费
                        Object minObj = baseService.findHotelSettingByParamId(hotelSettingByParamId);
                        if (minObj == null) {
                            return;
                        }
                        int min = Integer.parseInt(minObj.toString());

                        if (min < 1) {
                            return;
                        }

                        String registIds = "";


                        JSONArray hourInfoList = new JSONArray();
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject robj = jsonArray.getJSONObject(i);
                            registIds += robj.get("registId");
                            registIds += ",";

                            if (robj.containsKey("checkinType") && robj.getString("checkinType").equals("2")) {
                                JSONObject hourData = new JSONObject();
                                hourData.put("registId", robj.getInt("registId"));
                                hourData.put("endTime", robj.getString("endTime"));
                                hourInfoList.add(hourData);
                            }

                        }
                        registIds = registIds.substring(0, registIds.length() - 1);
                        Map<String, Object> data = new HashMap<>();
                        data.put("registIds", registIds);
                        JSONObject addRoomPriceJobParam = JobName.getAddRoomPriceJobParam(min, data);
                        baseService.addCornJob(addRoomPriceJobParam);

                        /**
                         * 如果是钟点房，则创建到期提醒定时任务
                         */
                        for (int i = 0; i < hourInfoList.size(); i++) {
                            Map<String, Object> param = new HashMap<>();
                            param.put("registId", hourInfoList.getJSONObject(i).getInt("registId"));
                            LocalDateTime currentTime = LocalDateTime.now();
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            LocalDateTime otherTime = LocalDateTime.parse(hourInfoList.getJSONObject(i).getString("endTime"), formatter);
                            Duration duration = Duration.between(currentTime, otherTime);
                            long minutesDifference = Math.abs(duration.toMinutes()) - 15;
                            JSONObject hourRoomExpReminderJob = JobName.getHourRoomExpReminderJob(minutesDifference, param);
                            baseService.addCornJob(hourRoomExpReminderJob);
                        }
                    } catch (Exception e) {
                        log.error("",e);
                    }

                }
            });

            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    ArrayList<RoomSmartRequest> roomSmartRequests = new ArrayList<>();
                    for (int i = 0; i < checkInRegists.size(); i++) {
                        CheckInRegist checkInRegist = checkInRegists.get(i);
                        //如果是智能门锁则调用
                        SmartLockRequest smartLockRequest = new SmartLockRequest();
                        smartLockRequest.setSessionToken(sessionToken);
                        smartLockRequest.setCheckinTime(checkInRegist.getRegist().getCheckinTime());
                        smartLockRequest.setCheckoutTime(checkInRegist.getRegist().getCheckoutTime());
                        smartLockRequest.setRoomTypeId(checkInRegist.getRegist().getRoomTypeId());
                        smartLockRequest.setRoomTypeName(checkInRegist.getRegist().getRoomTypeName());
                        smartLockRequest.setRoomNumId(checkInRegist.getRegist().getRoomNumId());
                        smartLockRequest.setRoomNum(checkInRegist.getRegist().getRoomNum());
                        smartLockRequest.setHid(checkInRegist.getRegist().getHid());
                        smartLockRequest.setHotelGroupId(checkInRegist.getRegist().getHotelGroupId());
                        smartLockRequest.setBreakfastNum(checkInRegist.getRegist().getBreakfastNum());
                        smartLockRequest.setCheckinType(checkInRegist.getRegist().getCheckinType());
                        smartLockRequest.setLockNo(checkInRegist.getRegist().getSessionToken());
                        List<RegistPerson> registPeople = checkInRegist.getRegistPeople();
                        List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                        for (int j = 0; j < registPeople.size(); j++) {
                            RegistPerson registPersonInfo = registPeople.get(j);
                            SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                            BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                            peoplesList.add(registPersonDetail);

                            RoomSmartRequest roomSmartRequest = new RoomSmartRequest();
                            roomSmartRequest.setRoomNo(checkInRegist.getRegist().getRoomNum());
                            roomSmartRequest.setSessionToken(sessionToken);
                            roomSmartRequest.setCheckInTime(HotelUtils.parseDate2Str(checkInRegist.getRegist().getCheckinTime()));
                            roomSmartRequest.setHid(user.getHid());
                            roomSmartRequest.setCheckOutTime(HotelUtils.parseDate2Str(checkInRegist.getRegist().getCheckoutTime()));
                            roomSmartRequest.setName(registPersonInfo.getPersonName());
                            roomSmartRequest.setIdCode(registPersonInfo.getIdCode());
                            roomSmartRequest.setPhone(registPersonInfo.getPhone().trim());
                            if (roomSmartRequest.getPhone() == null || roomSmartRequest.getPhone().length() != 11) {
                                continue;
                            }
                            roomSmartRequests.add(roomSmartRequest);
                        }
                        if (roomSmartRequests.size() > 0) {
                            try {
                                baseService.smartRoomCheckIn(roomSmartRequests);
                            } catch (Exception e) {
                                log.error("",e);
                            }
                        }
                        smartLockRequest.setPeoples(peoplesList);
                        baseService.SmartLockCheckin(smartLockRequest);
                    }

                }
            });

            //2022-02-09 添加会员消费撤销短信提醒
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {


                        JSONObject postData = new JSONObject();
                        postData.put("sessionToken", sessionToken);
                        JSONObject hotelBaseInfo = JSONObject.fromObject(baseService.getHotelBaseInfo(postData));
                        JSONObject data = hotelBaseInfo.getJSONObject("data");
                        String hotelName = data.getString("hotelName");
                        String telephone = data.getString("telephone");
                        String addr = data.getString("addr");
                        //尊敬的{1}，您好，欢迎您入住{2}，您入住的房间号是{3}，退房时间为{4},如有疑问请拨打酒店电话：{5}
                        for (int i = 0; i < checkInRegists.size(); i++) {
                            CheckInRegist checkInRegist = checkInRegists.get(i);
                            if (checkInRegist != null && checkInRegist.getRegistPeople().get(0).getPhone() != null && checkInRegist.getRegistPeople().get(0).getPhone().length() == 11) {
                                final ArrayList<String> strings = new ArrayList<>();
                                strings.add(checkInRegist.getRegistPeople().get(0).getPersonName());
                                strings.add(hotelName);
                                strings.add(checkInRegist.getRegist().getRoomNum());
                                strings.add(HotelUtils.parseDate2Str(checkInRegist.getRegist().getCheckoutTime()));
                                strings.add(telephone);
                                SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                                smsHotelSendRecordRequest.setLocationId(SMS_LOC.CHECK_IN);
                                smsHotelSendRecordRequest.setSessionToken(user.getSessionId());
                                smsHotelSendRecordRequest.setPhoneNumber(checkInRegist.getRegistPeople().get(0).getPhone());
                                smsHotelSendRecordRequest.setParams(strings);
                                baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);
                            }
                        }

                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

            for (int i = 0; i < checkInRegists.size(); i++) {
                OprecordInfoRequest oprecordInfoRequest = new OprecordInfoRequest();
                JSONObject data = new JSONObject();
                data.put("guestName", checkInRegists.get(i).getRegistPeople().get(0).getPersonName());
                data.put("time", HotelUtils.currentTime());
                oprecordInfoRequest.setSessionToken(sessionToken);
                oprecordInfoRequest.setHid(user.getHid());
                oprecordInfoRequest.setRoomNum(checkInRegists.get(i).getRegist().getRoomNum());
                oprecordInfoRequest.setRegistId(checkInRegists.get(i).getRegist().getRegistId());
                oprecordInfoRequest.setOprecordTemplateId(2);
                oprecordInfoRequest.setBusinessId2(data.toString());
                oprecordInfoRequest.setData(data);
                oprecordInfoRequests.add(oprecordInfoRequest);
            }
            this.baseService.addOprecordReqs(oprecordInfoRequests);

            responseData.setData(jsonArray);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public HourRoomDayUse getHourRoomDayUse(Regist regist) {
        HourRoomDayUse hourRoomDayUse = new HourRoomDayUse();
        hourRoomDayUse.setRoomNo(regist.getRoomNum());
        hourRoomDayUse.setRoomInfoId(regist.getRoomNumId());
        hourRoomDayUse.setRoomTypeId(regist.getRoomTypeId());
        hourRoomDayUse.setHid(regist.getHid());
        hourRoomDayUse.setHotelGroupId(regist.getHotelGroupId());
        return hourRoomDayUse;
    }

    public ResponseData upaPersonAddress(RegistPersonSearch registSearch) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        Page<RegistPerson> registPeople = registPersonDao.searchRegistPersonList(registSearch);

        try {
            ArrayList<RegistPerson> upaReg = new ArrayList<>();

            for (RegistPerson rp : registPeople) {

                String idCode = rp.getIdCode();

                if (rp.getProvice() != null) {
                    continue;
                }

                // 省市县获取
                AddressParam addressParam = new AddressParam(idCode);
                rp.setCity(addressParam.getCity());
                rp.setArea(addressParam.getArea());
                rp.setProvice(addressParam.getProvice());
                upaReg.add(rp);
            }

            registPersonDao.updatePeople(upaReg);

        } catch (Exception e) {
            log.error("",e);
        }
        return responseData;
    }

    @Override
    public ResponseData checkout(CheckOutRequest request) throws Exception {
        ResponseData responseData = new ResponseData(ER.SUCC);
        TbUserSession user = getTbUserSession(request);
        if (request.getRegistId() < 1) {
            throw new Exception("订单编号不能空");
        }

        if (request.getCheckoutType() < 1) {
            throw new Exception("退房方式不能空");
        }

        Regist regist = registDao.selectById(request.getRegistId());

        if (regist.getState() == 1 || regist.getState() == 3) {
            throw new Exception("当前订单状态不能结账");
        }

        if (request.getCheckoutType() == 1) {

        }
        //房间部分客人结账
        else if (request.getCheckoutType() == 2) {

            ArrayList<Account> accountList = new ArrayList<>();
            ArrayList<RegistPerson> registPeopleList = new ArrayList<>();

            if (request.getPersons() == null || request.getPersons().size() < 1) {
                throw new Exception("需要结账的宾客信息不能空");
            }

            int isOtherGuest = 0;

            int cash = 0;
            int cost = 0;

            for (int i = 0; i < request.getPersons().size(); i++) {
                int registPersonId = request.getPersons().get(i);
                //判断账务是否持平
                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setRegistPersonId(registPersonId);
                List<Account> accounts = accountDao.selectBySearch(accountSearch);
                for (int j = 0; j < accounts.size(); j++) {
                    Account account = accounts.get(j);
                    if (account.getRegistState() != 0 || account.getIsCancel() != 0) {
                        continue;
                    }
                    if (account.getPayType() == 1) {
                        cost += account.getPrice();
                    }
                    if (account.getPayType() == 2) {
                        cash += account.getPrice();
                    }
                    accountList.add(account);
                }
                RegistPerson registPerson = registPersonDao.selectById(registPersonId);
                if (registPerson.getRegistState() != 0) {
                    throw new Exception("当前宾客状态异常");
                }
                Integer isOther = registPerson.getIsOther();
                if (isOther == 0) {
                    isOtherGuest++;
                }
                registPeopleList.add(registPerson);
            }

            if (cash != cost) {
                throw new Exception("账务不平无法办理退房");
            }

            RegistPerson registPerson = null;

            if (isOtherGuest > 0) {
                RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                registPersonSearch.setRegistId(request.getRegistId());
                registPersonSearch.setRegistState(0);
                List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
                for (int i = 0; i < request.getPersons().size(); i++) {
                    for (int j = 0; j < registPeople.size(); j++) {
                        if (request.getPersons().get(i) != registPeople.get(j).getRegistPersonId()) {
                            registPerson = registPeople.get(j);
                        }
                    }
                }
            }
            checkInTransactionService.personCheckout(accountList, registPeopleList, registPerson, user.getHid(), user.getPhone());
        }

        /**
         * 会员退房会员等级升级判断
         */
        HotelUtils.cachedThreadPool.execute(() -> {
            try {
                Integer cardInfoId = regist.getMemberId();
                if(null == cardInfoId || 0 == cardInfoId){
                    log.warn("散客入住，不进行会员等级升级判断");
                }else{
                    RegistSearch registSearch = new RegistSearch();
                    registSearch.setMemberId(regist.getMemberId());
                    List<Regist> regists = registDao.selectBySearch(registSearch);
                    if(!CollectionUtils.isEmpty(regists)){
                        Integer dayCount = 0;
                        for(Regist r:regists){
                            dayCount += r.getDayCount();
                        }
                        if(dayCount > 0){
                            CardMemberLevelSearch memberLevelSearch = new CardMemberLevelSearch();
                            memberLevelSearch.setHid(user.getHid());
                            memberLevelSearch.setHotelGroupId(user.getHotelGroupId());
                            memberLevelSearch.setConditionType(1);
                            List<CardMemberLevel> cardMemberLevels = cardMemberLevelDao.selectBySearch(memberLevelSearch);
                            if(CollectionUtils.isEmpty(cardMemberLevels)){
                                log.warn("会员间夜数升级策略未设置");
                            }else{
                                Collections.sort(cardMemberLevels, (c1, c2) -> {
                                    return c2.getConditionValue().compareTo(c1.getConditionValue()); // 降序排列
                                });
                                for(CardMemberLevel memberLevel:cardMemberLevels){
                                    if(dayCount >= memberLevel.getConditionValue() * 100){
                                        CardInfo cardInfo = cardInfoDao.selectById(regist.getMemberId());
                                        log.info("会员间夜数满足条件，自动升级或降级会员等级：{}->{}",cardInfo.getCardLevel(),memberLevel.getLevelName());
                                        cardInfo.setCardLevelId(memberLevel.getId());
                                        cardInfo.setCardLevel(memberLevel.getLevelName());
                                        cardInfoDao.update(cardInfo);
                                        break;
                                    }
                                }
                            }
                        }
                        AccountSearch accountSearch = new AccountSearch();
                        accountSearch.setRegistIds(String.join(",", regists.stream().map(r -> r.getRegistId()+"").collect(Collectors.toList())));
                        accountSearch.setPayType(1);
                        List<Account> accounts = accountDao.selectBySearch(accountSearch);
                        Integer priceCount = 0;
                        for(Account a:accounts){
                            priceCount += a.getPrice();
                        }
                        if(priceCount > 0){
                            CardMemberLevelSearch memberLevelSearch = new CardMemberLevelSearch();
                            memberLevelSearch.setHid(user.getHid());
                            memberLevelSearch.setHotelGroupId(user.getHotelGroupId());
                            memberLevelSearch.setConditionType(2);
                            List<CardMemberLevel> cardMemberLevels = cardMemberLevelDao.selectBySearch(memberLevelSearch);
                            if(CollectionUtils.isEmpty(cardMemberLevels)){
                                log.warn("会员消费升级策略未设置");
                            }else{
                                Collections.sort(cardMemberLevels, (c1, c2) -> {
                                    return c2.getConditionValue().compareTo(c1.getConditionValue()); // 降序排列
                                });
                                for(CardMemberLevel memberLevel:cardMemberLevels){
                                    if(priceCount >= memberLevel.getConditionValue() * 100){
                                        CardInfo cardInfo = cardInfoDao.selectById(regist.getMemberId());
                                        log.info("会员消费满足条件，自动升级或降级会员等级：{}->{}",cardInfo.getCardLevel(),memberLevel.getLevelName());
                                        cardInfo.setCardLevelId(memberLevel.getId());
                                        cardInfo.setCardLevel(memberLevel.getLevelName());
                                        cardInfoDao.update(cardInfo);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return responseData;
    }


    /**
     * 查询在住详情
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> searchCheckinDetails(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(ER.RES, ER.SUCC);

        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setState(0);
            if (param.get("teamCodeId") != null) {
                registSearch.setState(null);
                registSearch.setTeamCodeId(param.getInt("teamCodeId"));
            }

            //查询所有在住单信息
            List<Regist> regists = registDao.selectBySearch(registSearch);

            HashMap<String, Object> map = new HashMap<>();
            map.put("hid", user.getHid());
            map.put("registState", 0);
            if (param.get("teamCodeId") != null) {
                map.put("registState", null);
                map.put("teamCodeId", param.get("teamCodeId"));
            }

            //查询在住人
            List<RegistPerson> registPeople = registPersonDao.searchCheckinPeople(map);
            Map<Integer, List<RegistPerson>> collect = registPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));
            Integer peopleCount = 0;
            for (Regist regist : regists) {
                List<RegistPerson> registPeople1 = collect.get(regist.getRegistId());
                if (registPeople1 == null) {
                    regist.setPeoples(new ArrayList<>());
                    peopleCount += 0;
                    continue;
                }
                regist.setPeoples(collect.get(regist.getRegistId()));
                peopleCount += collect.get(regist.getRegistId()).size();
            }

            Map<Integer, Regist> checkinMsg = regists.stream().collect(Collectors.toMap(Regist::getRoomNumId, a -> a, (k1, k2) -> k1));

            resultMap.put("data", checkinMsg);
            resultMap.put("count", regists.size());
            resultMap.put("peopleCount", peopleCount);
            resultMap.put("list", regists);

        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    /**
     * 预定转入住
     *
     * @param param
     * @return
     */
    @Override
    public ResponseData bookingCheckIn(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Date dates = new Date();
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            JSONObject bookParam = JSONObject.fromObject(param.getString("bookParam"));

            // 验证如果登陆类型是小程序，则hid根据传入的参数取值
            if (user.getSessionType() == 3) {
                Object hid = bookParam.get("hid");
                if (hid != null && !hid.toString().equals("")) {
                    user.setHid(Integer.parseInt(hid.toString()));
                }
            }

            // 查询所有客源类型


            // 需要修改的数据
            // 需要添加的房单集合
            HashMap<Integer, Regist> checkInRegistMap = new HashMap<>();

            // 需要修改的预定房型
            ArrayList<BookingOrderRoomNum> bookingOrderRoomNumList = new ArrayList<>();

            // 需要修改的房间信息
            ArrayList<RoomInfo> roomInfoList = new ArrayList<>();

            // 入住人信息 key:roomNumId  value:入住人集合
            HashMap<Integer, List<RegistPerson>> registPersonMap = new HashMap<>();

            // 登记的设置信息
            HashMap<Integer, BookingOrderConfig> bookingOrderConfigMap = new HashMap<>();

            // 要删除的辅助房态信息
            ArrayList<RoomAuxiliaryRelation> deleteRoomAuxiliaryRelationList = new ArrayList<>();

            // 要添加的辅助房态信息
            HashMap<Integer, List<RoomAuxiliaryRelation>> addRoomAuxiliaryRelation = new HashMap<>();

            // 1.获取预定单信息，查看当前预订单状态是否有效
            // 预订单id
            int bookingId = bookParam.getInt("bookingOrderId");

            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingId);

            if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())) {
                throw new Exception("未查到有效的预订单信息");
            }

            // 预订单设置信息
            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setBookingOrderId(bookingId);
            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);

            if (bookingOrderConfigs.size() < 1) {
                throw new Exception("未查到相应的预定设置信息");
            }

            BookingOrderConfig bookingOrderConfig = bookingOrderConfigs.get(0);
            bookingOrderConfig.setId(null);


            // 2.获取预定排房的房间
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingId);
            List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            if (bookingOrderRoomNums.size() < 1) {
                throw new Exception("所选房间信息有误。预定编号:" + bookingOrder.getSn());
            }

            // 将预定房间集合转为map对象
            int checkNum = 0;
            Map<Integer, BookingOrderRoomNum> orderRoomNumMap = new HashMap<>();
            for (BookingOrderRoomNum bookingOrderRoomNum : bookingOrderRoomNums) {
                orderRoomNumMap.put(bookingOrderRoomNum.getId(), bookingOrderRoomNum);
                if (bookingOrderRoomNum.getIsCheckin() == 0) {
                    continue;
                }
                checkNum++;
            }

            //每日房价查询
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setBookingOrderId(bookingId);
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, List<BookingOrderDailyPrice>> dayPriceMap = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getBookingOrderRoomNumId));

            // 辅助房态查询条件
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setBookingOrderId(bookingId);

            // 3.获取要入住的房间
            JSONArray checkInRoomList = bookParam.getJSONArray("checkInRoomList");
            if (checkInRoomList.size() == 0) {
                new Exception("请传入入住房间");
            }

            // 判断预订单状态 部分入住还是全部入住
            // 如果已入住的房间加 当前要入住的房间数量 不等于 查询出的总房间入住数  则说明为部分入住
            if (checkNum + checkInRoomList.size() != bookingOrderRoomNums.size()) {
                bookingOrder.setOrderStatus(BOOK.STA_BFRZ);
            } else {
                bookingOrder.setOrderStatus(BOOK.STA_QBRZ);
            }

            bookingOrder.setOtaStatus(HmhOrderStatusEnum.ALREADY_CHECKED_IN.getType());

            // 4.查询团队信息
            RegistGroup registGroup = new RegistGroup();
            registGroup.setGroupType(9);
            //添加默认信息
            registGroup.setSumSales(0);
            registGroup.setSumPay(0);
            Date date = new Date();

            // 如果预定房间为1 ，则不需要添加团队信息
            if (bookingOrderRoomNums.size() == 1) {
                registGroup.setTeamType(-1);
            } else {
                RegistGroupSearch registGroupSearch = new RegistGroupSearch();
                registGroupSearch.setBookingOrderId(bookingId);
                List<RegistGroup> registGroups = registGroupDao.selectBySearch(registGroupSearch);
                if (registGroups.size() > 0) {
                    registGroup = registGroups.get(0);

                    RegistSearch registSearch = new RegistSearch();
                    registSearch.setHid(user.getHid());
                    registSearch.setBookingOrderId(bookingId);
                    registSearch.setIsMainRoom(1);
                    List<Regist> regists = registDao.selectBySearch(registSearch);

                    if (regists.size() < 1) {
                        registGroup.setTeamType(-1);
                    } else {
                        registGroup.setTeamType(registGroup.getRegistGroupId());
                    }

                } else {
                    registGroup.setTeamType(-2);
                    String sn = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.GROUP, stringRedisTemplate);
                    registGroup.setSn(sn);
                    registGroup.setPayType(1);
                    registGroup.setGroupType(9);
                    registGroup.setGroupName(bookingOrder.getSn());
                    registGroup.setBookingOrderId(bookingOrder.getBookingOrderId());
                    registGroup.setSumRooms(bookingOrderRoomNums.size());
                    registGroup.setRemark("预订转入住创建");
                    registGroup.setState(1);
                    registGroup.setHid(user.getHid());
                    registGroup.setHotelGroupId(user.getHotelGroupId());
                    registGroup.setBusinessDay(user.getBusinessDay());
                    registGroup.setClassId(user.getClassId());
                    registGroup.setCreateTime(date);
                    registGroup.setCreateUserId(user.getUserId());
                    registGroup.setCreateUserName(user.getUserName());
                    registGroup.setCompanyId(bookingOrder.getCompanyId());
                    registGroup.setCompayName(bookingOrder.getCompanyName());
                    registGroup.setMemberId(bookingOrder.getCardId());
                    registGroup.setMemberCard(bookingOrder.getCardNo());
                }
            }

            Integer rateId = 0;
            String rateCode = "";

            StringBuilder roomBuilder = new StringBuilder();


            Boolean isMachineCheck = false;
            if (bookParam.get("macCheckIn") != null && bookParam.getBoolean("macCheckIn")) {
                isMachineCheck = true;
            }

            // 房价信息
            HashMap<String, RoomRateCodeSpecific> specificHashMap = new HashMap<>();

            /**
             * 预订转入住组装数据
             */
            final ArrayList<CheckInRegist> checkInRegists = new ArrayList<>();

            //封装房型价态量信息
            OpenDailyPriceReqDto openDailyPriceReqDto = new OpenDailyPriceReqDto();
            openDailyPriceReqDto.setHotelId(bookingOrder.getHid());
            openDailyPriceReqDto.setRoomTypeIds(new ArrayList<>());
            for (int i = 0; i < checkInRoomList.size(); i++) {
                JSONObject roomObj = checkInRoomList.getJSONObject(i);
                int id = roomObj.getInt("bookingRoomId");
                int roomNumId = roomObj.getInt("roomId");
                JSONArray guestList = roomObj.getJSONArray("guestList");

                BookingOrderRoomNum bookingOrderRoomNum = orderRoomNumMap.get(id);

                // 1.将预定房间信息改为 已入住装
                if (bookingOrderRoomNum == null || bookingOrderRoomNum.getIsCheckin() > 0 || bookingOrderRoomNum.getRoomNumId() < 1) {
                    throw new Exception("未查询到有效的分房信息，房间号:" + bookingOrderRoomNum.getRoomNum() + ",编号:" + bookingOrderRoomNum.getId());
                }

                bookingOrderRoomNum.setIsCheckin(1);
                bookingOrderRoomNumList.add(bookingOrderRoomNum);

                // 2.将房间改为住净
                RoomInfo roomInfo = roomInfoDao.selectById(roomNumId);
                if (roomInfo == null || !roomInfo.getHid().equals(user.getHid())) {
                    throw new Exception("未查到相应的房间信息，房间号:" + roomInfo.getRoomNum() + ",编号:" + roomInfo.getRoomInfoId());
                }

                if (roomInfo.getRoomNumState() != 1) {
                    turnAlwaysService.updateRoomListForCache(user);
                    throw new Exception(roomInfo.getRoomNum() + "房间状态不可用");
                }

                roomInfoList.add(roomInfo);
                openDailyPriceReqDto.getRoomTypeIds().add(roomInfo.getRoomTypeId());

                // 3.添加设置信息
                BookingOrderConfig bookingOrderConfig1 = new BookingOrderConfig();
                bookingOrderConfig1.setAutoAr(bookingOrderConfig.getAutoAr());
                bookingOrderConfig1.setAutoCheckin(bookingOrderConfig.getAutoCheckin());
                bookingOrderConfig1.setInfoSecrecy(bookingOrderConfig.getInfoSecrecy());
                bookingOrderConfig1.setMorningCall(bookingOrderConfig.getMorningCall());
                bookingOrderConfig1.setNoDeposit(bookingOrderConfig.getNoDeposit());
                bookingOrderConfig1.setNoPrice(bookingOrderConfig.getNoPrice());
                bookingOrderConfig1.setContinueRes(bookingOrderConfig.getContinueRes());
                bookingOrderConfig1.setPriceSecrecy(bookingOrderConfig.getInfoSecrecy());
                bookingOrderConfig1.setAvePrice(bookingOrderConfig.getAvePrice());
                bookingOrderConfigMap.put(roomNumId, bookingOrderConfig1);

                // 4.添加登记信息
                Regist regist = new Regist();
                regist.setSumSale(0);
                regist.setSumPay(0);
                regist.setHid(user.getHid());
                regist.setHotelGroupId(user.getHotelGroupId());
                regist.setRoomNumId(roomNumId);
                regist.setRoomNum(roomInfo.getRoomNum());
                regist.setRoomTypeId(roomInfo.getRoomTypeId());
                regist.setRoomTypeName(roomInfo.getRoomTypeName());
                regist.setBookingOrderId(bookingId);
                regist.setDayCount(bookingOrder.getDayCount());
                regist.setCheckinMode(0);

                roomBuilder.append(regist.getRoomNum());
                roomBuilder.append(",");

                //生成流水号
                String sn = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.REGIST, stringRedisTemplate);
                regist.setSn(sn);

                //协议单位
                regist.setCompanyId(bookingOrder.getCompanyId());
                regist.setCompayName(bookingOrder.getCompanyName());
                regist.setCompanyAccountId(bookingOrder.getCompanyAccountId());

                //会员
                regist.setMemberCard(bookingOrder.getCardNo());
                regist.setMemberId(bookingOrder.getCardId());
                Boolean isVip = false;
                if (bookingOrder.getCardId() != null && bookingOrder.getCardId() > 0) {
                    isVip = true;
                }

                //房价码
                rateId = bookingOrderRoomNum.getRateCodeId();

                rateCode = bookingOrderRoomNum.getRateCode();
                if (rateId == null) {
                    rateId = bookingOrder.getRateCodeId();
                }
                if (rateCode == null) {
                    rateCode = bookingOrder.getRateCodeName();
                }
                regist.setRoomRateCodeId(rateId);
                regist.setRoomRateCodeName(rateCode);

                //入住时间
                regist.setCheckinTime(new Date());
                //更改成小定单的时间
                regist.setCheckoutTime(HotelUtils.parseStr2Date(roomObj.getString("checkoutTime")));
                regist.setCheckoutBusinessDay(HotelUtils.parseDate2Int(regist.getCheckoutTime()));

                //备注
                if (bookingOrder.getRemark() != null) {
                    regist.setRemark(bookingOrder.getRemark());
                }
                //入住类型
                regist.setCheckinType(1);

                //客源
                regist.setResourceId(bookingOrder.getResourceId());
                regist.setResourceName(SYS_RESOURCE.SYS_RESOURCE_MAP.get(bookingOrder.getResourceId()));

                //入住人数
                regist.setPersonCount(guestList.size());

                //是否客人自动结账
                regist.setAutoCheckout(0);


                //杂项
                regist.setBusinessDay(user.getBusinessDay());
                regist.setRegistYear(user.getBusinessYear());
                regist.setRegistYearMonth(user.getBusinessMonth());
                regist.setClassId(user.getClassId());
                regist.setCreateTime(new Date());
                regist.setCreateUserId(user.getUserId());
                regist.setUpdateTime(new Date());
                regist.setUpdateUserId(user.getUserId());
                regist.setIsCreateNightCost(0);
                regist.setRegistGroupId(0);
                regist.setTeamCodeId(0);
                regist.setIsMainRoom(1);
                regist.setSessionToken(user.getSessionId());
                regist.setState(0);

                // 查询房价信息
                String sket = regist.getRoomTypeId() + "" + regist.getRoomRateCodeId();

                RoomRateCodeSpecific roomRateCodeSpecific = specificHashMap.get(sket);
                if (roomRateCodeSpecific == null) {

                    RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
                    roomRateCodeSpecificSearch.setHid(user.getHid());
                    roomRateCodeSpecificSearch.setRateId(regist.getRoomRateCodeId());
                    roomRateCodeSpecificSearch.setRoomTypeId(regist.getRoomTypeId());

                    List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);

                    if (roomRateCodeSpecifics.size() < 1) {
                        throw new Exception(regist.getRoomTypeName() + " 房价码： " + regist.getRoomRateCodeName() + "未保存");
                    }

                    roomRateCodeSpecific = roomRateCodeSpecifics.get(0);

                    specificHashMap.put(sket, roomRateCodeSpecific);

                }

                regist.setBreakfastNum(roomRateCodeSpecific.getBreakfastNum());

                checkInRegistMap.put(roomNumId, regist);

                // 5.入住人的添加
                List<RegistPerson> registPeople = checkInTransactionService.addCheckinGuest(guestList, user, regist, 0);
                registPersonMap.put(roomNumId, registPeople);

                // 组装入住数据
                CheckInRegist checkInRegist = new CheckInRegist();
                checkInRegist.setRegist(regist);
                checkInRegist.setRegistPeople(registPeople);
                checkInRegist.setBookingOrderDailyPrices(bookingOrderDailyPrices);

                checkInRegists.add(checkInRegist);

                // 6.需要删除的辅助房态
                roomAuxiliaryRelationSearch.setRoomId(roomNumId);
                List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);
                deleteRoomAuxiliaryRelationList.addAll(roomAuxiliaryRelations);

                // 7.需要添加的辅助房态
                ArrayList<RoomAuxiliaryRelation> rarl = new ArrayList<>();


                //信息保密
                if (bookingOrderConfig.getInfoSecrecy() == 1) {
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);

                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.SECRECY);
                    roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.SECRECY);

                    rarl.add(roomAuxiliaryRelation);
                }

                if (isMachineCheck) {
                    regist.setCheckinMode(1);
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);

                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.MACHINE);
                    roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.MACHINE);

                    rarl.add(roomAuxiliaryRelation);
                }
/*

                //会员
                if (bookingOrder.getResourceId().equals(SYS_RESOURCE.RES_HY)) {
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);

                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.VIP);
                    roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.VIP);

                    rarl.add(roomAuxiliaryRelation);
                }
*/

                //团队
                if (bookingOrderRoomNums.size() > 1) {
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);

                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.TEAM);
                    roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.TEAM);
                    if (registGroup.getGroupType() == 9) {
                        roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.JOINT_HOUSING);
                        roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.JOINT_HOUSING);
                    }

                    rarl.add(roomAuxiliaryRelation);
                }

                addRoomAuxiliaryRelation.put(roomNumId, rarl);

            }

            // 5.查询预定账务信息
            int sumPay = 0;
            int sumSale = 0;
            List<Account> accounts = new ArrayList<>();
            if (registGroup.getRegistGroupId() == null || registGroup.getRegistGroupId() < 1) {
                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setBookingId(bookingId);
                accounts = accountDao.selectBySearch(accountSearch);

                for (Account account : accounts) {

                    if (account.getPayType() == 1) {
                        sumSale += account.getPrice();
                    } else {
                        sumPay += account.getPrice();
                    }

                }

                registGroup.setSumPay(sumPay);
                registGroup.setSumSales(sumSale);
            }

            registGroup.setRoomRateCodeId(rateId);
            registGroup.setRoomRateCodeName(rateCode);
            // 6.调用预定入住事务接口
            final Map<Integer, Integer> registMap = checkInTransactionService.bookingCheckInTransactionNew
                    (
                            bookingOrder, bookingOrderRoomNumList, checkInRegistMap, roomInfoList, registPersonMap,
                            bookingOrderConfigMap, deleteRoomAuxiliaryRelationList, addRoomAuxiliaryRelation,
                            registGroup, accounts, user, dayPriceMap
                    );

            final String s = roomBuilder.toString();

            responseData.setData(registMap);

            //推送OTA订单状态信息,以及价量态信息
            otaChangePushService.pushOrderAndNumChange(bookingOrder, bookingOrderRoomNumList);

           /*
            Integer k = 0;
            for (Integer i :integers){
                k = i;
                break;
            }
            final Regist regist = checkInRegistMap.get(k);*/

            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    try {
                        //更新房间缓存
                        List<RoomInfo> roomInfos = roomService.updateRoomListForCache(user);
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);

                        HashMap<String, String> filedMap = new HashMap<>();
                        filedMap.put("roomList", s);

                        HashMap<String, String> regMap = new HashMap<>();
                        //regMap.put("registId",""+regist.getRegistId());
                        int pcId = 3;
                        if (bookParam.get("macCheckIn") != null && bookParam.getBoolean("macCheckIn")) {
                            pcId = 18;
                        }
                        baseService.push(user.getHotelGroupId(), user.getHid(), pcId, filedMap, regMap, true, true);

                        HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
                        hotelSettingByParamId.setHid(user.getHid());
                        hotelSettingByParamId.setParamId(HOTEL_SETTING.AUTO_ADD_ROOMPRICE);
                        // 登记后多久产生房费
                        Object minObj = baseService.findHotelSettingByParamId(hotelSettingByParamId);
                        if (minObj == null) {
                            return;
                        }
                        int min = Integer.parseInt(minObj.toString());

                        if (min < 1) {
                            return;
                        }


                        Set<Integer> integers = registMap.keySet();

                        String registIds = "";

                        for (Integer key : integers) {
                            registIds += registMap.get(key);
                            registIds += ",";
                        }

                        registIds = registIds.substring(0, registIds.length() - 1);
                        Map<String, Object> data = new HashMap<>();
                        data.put("registIds", registIds);
                        JSONObject addRoomPriceJobParam = JobName.getAddRoomPriceJobParam(min, data);
                        baseService.addCornJob(addRoomPriceJobParam);

                    } catch (Exception e) {

                    }

                }
            });

            //智能门锁推送
            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    ArrayList<RoomSmartRequest> roomSmartRequests = new ArrayList<>();
                    for (int i = 0; i < checkInRegists.size(); i++) {
                        CheckInRegist checkInRegist = checkInRegists.get(i);
                        //如果是智能门锁则调用
                        SmartLockRequest smartLockRequest = new SmartLockRequest();
                        smartLockRequest.setSessionToken(sessionToken);
                        smartLockRequest.setCheckinTime(checkInRegist.getRegist().getCheckinTime());
                        smartLockRequest.setCheckoutTime(checkInRegist.getRegist().getCheckoutTime());
                        smartLockRequest.setRoomTypeId(checkInRegist.getRegist().getRoomTypeId());
                        smartLockRequest.setRoomTypeName(checkInRegist.getRegist().getRoomTypeName());
                        smartLockRequest.setRoomNumId(checkInRegist.getRegist().getRoomNumId());
                        smartLockRequest.setRoomNum(checkInRegist.getRegist().getRoomNum());
                        smartLockRequest.setHid(checkInRegist.getRegist().getHid());
                        smartLockRequest.setHotelGroupId(checkInRegist.getRegist().getHotelGroupId());
                        smartLockRequest.setBreakfastNum(checkInRegist.getRegist().getBreakfastNum());
                        smartLockRequest.setCheckinType(checkInRegist.getRegist().getCheckinType());
                        smartLockRequest.setLockNo(checkInRegist.getRegist().getSessionToken());
                        List<RegistPerson> registPeople = checkInRegist.getRegistPeople();
                        List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                        for (int j = 0; j < registPeople.size(); j++) {
                            RegistPerson registPersonInfo = registPeople.get(j);
                            SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                            BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                            peoplesList.add(registPersonDetail);

                            RoomSmartRequest roomSmartRequest = new RoomSmartRequest();
                            roomSmartRequest.setRoomNo(checkInRegist.getRegist().getRoomNum());
                            roomSmartRequest.setSessionToken(sessionToken);
                            roomSmartRequest.setCheckInTime(HotelUtils.parseDate2Str(checkInRegist.getRegist().getCheckinTime()));
                            roomSmartRequest.setHid(user.getHid());
                            roomSmartRequest.setCheckOutTime(HotelUtils.parseDate2Str(checkInRegist.getRegist().getCheckoutTime()));
                            roomSmartRequest.setName(registPersonInfo.getPersonName());
                            roomSmartRequest.setIdCode(registPersonInfo.getIdCode());
                            if (null != registPersonInfo.getPhone()) {
                                roomSmartRequest.setPhone(registPersonInfo.getPhone().trim());
                            }
                            if (roomSmartRequest.getPhone() == null || roomSmartRequest.getPhone().length() != 11) {
                                continue;
                            }
                            roomSmartRequests.add(roomSmartRequest);
                        }
                        if (roomSmartRequests.size() > 0) {
                            try {
                                baseService.smartRoomCheckIn(roomSmartRequests);
                            } catch (Exception e) {
                                log.error("",e);
                            }
                        }
                        smartLockRequest.setPeoples(peoplesList);
                        baseService.SmartLockCheckin(smartLockRequest);
                    }

                }
            });
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    public ResponseData bookingOrderCheckIn(BookingOrderCheckInRequest bookingOrderCheckInRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Date dates = new Date();
            /*1.获取用户信息*/
            String sessionToken = bookingOrderCheckInRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (ObjectUtil.isNotNull(bookingOrderCheckInRequest.getHid())){
                user.setHid(bookingOrderCheckInRequest.getHid());
            }

            // 需要修改的数据
            // 需要添加的房单集合
            HashMap<Integer, Regist> checkInRegistMap = new HashMap<>();

            // 需要修改的预定房型
            ArrayList<BookingOrderRoomNum> bookingOrderRoomNumList = new ArrayList<>();

            // 需要修改的房间信息
            ArrayList<RoomInfo> roomInfoList = new ArrayList<>();

            // 入住人信息 key:roomNumId  value:入住人集合
            HashMap<Integer, List<RegistPerson>> registPersonMap = new HashMap<>();

            // 登记的设置信息
            HashMap<Integer, BookingOrderConfig> bookingOrderConfigMap = new HashMap<>();

            // 要删除的辅助房态信息
            ArrayList<RoomAuxiliaryRelation> deleteRoomAuxiliaryRelationList = new ArrayList<>();

            // 要添加的辅助房态信息
            HashMap<Integer, List<RoomAuxiliaryRelation>> addRoomAuxiliaryRelation = new HashMap<>();

            // 1.获取预定单信息，查看当前预订单状态是否有效
            // 预订单id
            int bookingId = bookingOrderCheckInRequest.getBookingOrderId();

            BookingOrder bookingOrder = bookingOrderDao.selectById(bookingId);

            if (bookingOrder == null || !bookingOrder.getHid().equals(user.getHid())) {
                throw new Exception("未查到有效的预订单信息");
            }

            // 预订单设置信息
            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setBookingOrderId(bookingId);
            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);

            if (bookingOrderConfigs.size() < 1) {
                throw new Exception("未查到相应的预定设置信息");
            }

            BookingOrderConfig bookingOrderConfig = bookingOrderConfigs.get(0);
            bookingOrderConfig.setId(null);


            // 2.获取预定排房的房间
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setBookingOrderId(bookingId);
            List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

            if (bookingOrderRoomNums.size() < 1) {
                throw new Exception("所选房间信息有误。预定编号:" + bookingOrder.getSn());
            }

            // 将预定房间集合转为map对象
            int checkNum = 0;
            Map<Integer, BookingOrderRoomNum> orderRoomNumMap = new HashMap<>();
            for (BookingOrderRoomNum bookingOrderRoomNum : bookingOrderRoomNums) {
                orderRoomNumMap.put(bookingOrderRoomNum.getId(), bookingOrderRoomNum);
                if (bookingOrderRoomNum.getIsCheckin() == 0) {
                    continue;
                }
                checkNum++;
            }

            //每日房价查询
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setBookingOrderId(bookingId);
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            Map<Integer, List<BookingOrderDailyPrice>> dayPriceMap = bookingOrderDailyPrices.stream().collect(Collectors.groupingBy(BookingOrderDailyPrice::getBookingOrderRoomNumId));

            // 辅助房态查询条件
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setBookingOrderId(bookingId);

            // 3.获取要入住的房间

            List<BookingOrderCheckInRequest.CheckinParam> checkInRoomList = bookingOrderCheckInRequest.getCheckInRoomList();
            if (checkInRoomList.size() == 0) {
                new Exception(HOTEL_CONST.ROOM_NO_IS_NULL);
            }


            // 判断预订单状态 部分入住还是全部入住
            // 如果已入住的房间加 当前要入住的房间数量 不等于 查询出的总房间入住数  则说明为部分入住
            if (checkNum + checkInRoomList.size() != bookingOrderRoomNums.size()) {
                bookingOrder.setOrderStatus(BOOK.STA_BFRZ);
            } else {
                bookingOrder.setOrderStatus(BOOK.STA_QBRZ);
            }


            // 4.查询团队信息
            RegistGroup registGroup = new RegistGroup();
            registGroup.setGroupType(9);
            //添加默认信息
            registGroup.setSumSales(0);
            registGroup.setSumPay(0);
            Date date = new Date();
            long nowLong = new Date().getTime();
            // 如果预定房间为1 ，则不需要添加团队信息
            if (bookingOrderRoomNums.size() == 1) {
                registGroup.setTeamType(-1);
            } else {
                RegistGroupSearch registGroupSearch = new RegistGroupSearch();
                registGroupSearch.setBookingOrderId(bookingId);
                List<RegistGroup> registGroups = registGroupDao.selectBySearch(registGroupSearch);
                if (registGroups.size() > 0) {
                    registGroup = registGroups.get(0);

                    RegistSearch registSearch = new RegistSearch();
                    registSearch.setHid(user.getHid());
                    registSearch.setBookingOrderId(bookingId);
                    registSearch.setIsMainRoom(1);
                    List<Regist> regists = registDao.selectBySearch(registSearch);

                    if (regists.size() < 1) {
                        registGroup.setTeamType(-1);
                    } else {
                        registGroup.setTeamType(registGroup.getRegistGroupId());
                    }

                } else {
                    registGroup.setTeamType(-2);
                    String sn = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.GROUP, stringRedisTemplate);
                    registGroup.setSn(sn);
                    registGroup.setPayType(1);
                    registGroup.setGroupType(9);
                    registGroup.setGroupName(bookingOrder.getSn());
                    registGroup.setBookingOrderId(bookingOrder.getBookingOrderId());
                    registGroup.setSumRooms(bookingOrderRoomNums.size());
                    registGroup.setRemark("预订转入住创建");
                    registGroup.setState(1);
                    registGroup.setHid(user.getHid());
                    registGroup.setHotelGroupId(user.getHotelGroupId());
                    registGroup.setBusinessDay(user.getBusinessDay());
                    registGroup.setClassId(user.getClassId());
                    registGroup.setCreateTime(date);
                    registGroup.setCreateUserId(user.getUserId());
                    registGroup.setCreateUserName(user.getUserName());
                    registGroup.setCompanyId(bookingOrder.getCompanyId());
                    registGroup.setCompayName(bookingOrder.getCompanyName());
                    registGroup.setMemberId(bookingOrder.getCardId());
                    registGroup.setMemberCard(bookingOrder.getCardNo());
                }
            }
            Integer rateId = 0;
            String rateCode = "";
            StringBuilder roomBuilder = new StringBuilder();
            Boolean isMachineCheck = false;
            if (bookingOrderCheckInRequest.isMacCheckIn()) {
                isMachineCheck = true;
            }

            // 房价信息
            HashMap<String, RoomRateCodeSpecific> specificHashMap = new HashMap<>();

            /**
             * 预订转入住组装数据
             */
            final ArrayList<CheckInRegist> checkInRegists = new ArrayList<>();


            for (int i = 0; i < checkInRoomList.size(); i++) {
                BookingOrderCheckInRequest.CheckinParam roomObj = checkInRoomList.get(i);
                int id = roomObj.getBookingRoomId();
                int roomNumId = roomObj.getRoomId();
                List<BookingOrderCheckInRequest.CheckinParam.GuestInfo> guestList = roomObj.getGuestList();
                BookingOrderRoomNum bookingOrderRoomNum = orderRoomNumMap.get(id);
                // 1.将预定房间信息改为 已入住装
                if (bookingOrderRoomNum == null || bookingOrderRoomNum.getIsCheckin() > 0 || bookingOrderRoomNum.getRoomNumId() < 1) {
                    throw new Exception("未查询到有效的分房信息，房间号:" + bookingOrderRoomNum.getRoomNum() + ",编号:" + bookingOrderRoomNum.getId());
                }

                bookingOrderRoomNum.setIsCheckin(1);
                bookingOrderRoomNumList.add(bookingOrderRoomNum);

                // 2.将房间改为住净
                RoomInfo roomInfo = roomInfoDao.selectById(roomNumId);
                if (roomInfo == null || !roomInfo.getHid().equals(user.getHid())) {
                    throw new Exception("未查到相应的房间信息，房间号:" + roomInfo.getRoomNum() + ",编号:" + roomInfo.getRoomInfoId());
                }

                if (roomInfo.getRoomNumState() != 1) {
                    turnAlwaysService.updateRoomListForCache(user);
                    throw new Exception(roomInfo.getRoomNum() + "房间状态不可用");
                }

                roomInfoList.add(roomInfo);

                // 3.添加设置信息
                BookingOrderConfig bookingOrderConfig1 = new BookingOrderConfig();
                bookingOrderConfig1.setAutoAr(bookingOrderConfig.getAutoAr());
                bookingOrderConfig1.setAutoCheckin(bookingOrderConfig.getAutoCheckin());
                bookingOrderConfig1.setInfoSecrecy(bookingOrderConfig.getInfoSecrecy());
                bookingOrderConfig1.setMorningCall(bookingOrderConfig.getMorningCall());
                bookingOrderConfig1.setNoDeposit(bookingOrderConfig.getNoDeposit());
                bookingOrderConfig1.setNoPrice(bookingOrderConfig.getNoPrice());
                bookingOrderConfig1.setContinueRes(bookingOrderConfig.getContinueRes());
                bookingOrderConfig1.setPriceSecrecy(bookingOrderConfig.getInfoSecrecy());
                bookingOrderConfig1.setAvePrice(bookingOrderConfig.getAvePrice());
                bookingOrderConfigMap.put(roomNumId, bookingOrderConfig1);

                // 4.添加登记信息
                Regist regist = new Regist();
                regist.setSumSale(0);
                regist.setSumPay(0);
                regist.setHid(user.getHid());
                regist.setHotelGroupId(user.getHotelGroupId());
                regist.setRoomNumId(roomNumId);
                regist.setRoomNum(roomInfo.getRoomNum());
                regist.setRoomTypeId(roomInfo.getRoomTypeId());
                regist.setRoomTypeName(roomInfo.getRoomTypeName());
                regist.setBookingOrderId(bookingId);
                regist.setDayCount(bookingOrder.getDayCount());
                regist.setCheckinMode(0);

                roomBuilder.append(regist.getRoomNum());
                roomBuilder.append(",");

                //生成流水号
                String sn = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.REGIST, stringRedisTemplate);
                regist.setSn(sn);

                //协议单位
                regist.setCompanyId(bookingOrder.getCompanyId());
                regist.setCompayName(bookingOrder.getCompanyName());
                regist.setCompanyAccountId(bookingOrder.getCompanyAccountId());

                //会员
                regist.setMemberCard(bookingOrder.getCardNo());
                regist.setMemberId(bookingOrder.getCardId());
                Boolean isVip = false;
                if (bookingOrder.getCardId() != null && bookingOrder.getCardId() > 0) {
                    isVip = true;
                }

                //房价码
                rateId = bookingOrderRoomNum.getRateCodeId();

                rateCode = bookingOrderRoomNum.getRateCode();
                if (rateId == null) {
                    rateId = bookingOrder.getRateCodeId();
                }
                if (rateCode == null) {
                    rateCode = bookingOrder.getRateCodeName();
                }
                regist.setRoomRateCodeId(rateId);
                regist.setRoomRateCodeName(rateCode);
                //入住时间
                regist.setCheckinTime(date);
                Integer roomRateCodeId = regist.getRoomRateCodeId();
                HourRoomInfo hourRoomInfo = hourRoomInfoDao.selectById(roomRateCodeId);
                Integer hourRoomCode = hourRoomInfo.getHourRoomCode();
                String checkOutTime = HotelUtils.parseDate2Str(new Date(date.getTime() + hourRoomCode * 60 * 60 * 1000));
                //更改成小定单的时间
                regist.setCheckoutTime(HotelUtils.parseStr2Date(checkOutTime));

                regist.setCheckoutBusinessDay(HotelUtils.parseDate2Int(regist.getCheckoutTime()));

                //备注
                if (bookingOrder.getRemark() != null) {
                    regist.setRemark(bookingOrder.getRemark());
                }
                //入住类型
                regist.setCheckinType(2);

                //客源
                regist.setResourceId(bookingOrder.getResourceId());
                regist.setResourceName(SYS_RESOURCE.SYS_RESOURCE_MAP.get(bookingOrder.getResourceId()));

                //入住人数
                regist.setPersonCount(guestList.size());

                //是否客人自动结账
                regist.setAutoCheckout(0);

                // 钟点房，重新计算入住离店时间
                if (regist.getCheckinType() == 2) {

                    Long checkinTime = bookingOrderRoomNum.getCheckinTime().getTime();

                    // 如果当前入住时间，小于预订入住时间，则重新计算离店时间
                    if (checkinTime > nowLong) {
                        long diff = checkinTime - nowLong;
                        Date newCheckout = new Date(bookingOrderRoomNum.getCheckoutTime().getTime() - diff);
                        regist.setCheckoutTime(newCheckout);
                    }

                }

                //杂项
                regist.setBusinessDay(user.getBusinessDay());
                regist.setRegistYear(user.getBusinessYear());
                regist.setRegistYearMonth(user.getBusinessMonth());
                regist.setClassId(user.getClassId());
                regist.setCreateTime(new Date());
                regist.setCreateUserId(user.getUserId());
                regist.setUpdateTime(new Date());
                regist.setUpdateUserId(user.getUserId());
                regist.setIsCreateNightCost(0);
                regist.setRegistGroupId(0);
                regist.setTeamCodeId(0);
                regist.setIsMainRoom(1);
                regist.setSessionToken(user.getSessionId());
                regist.setState(0);

                // 查询房价信息
//                String sket = regist.getRoomTypeId() + "" + regist.getRoomRateCodeId();
//
//                RoomRateCodeSpecific roomRateCodeSpecific = specificHashMap.get(sket);
//                if (roomRateCodeSpecific == null) {
//
//                    RoomRateCodeSpecificSearch roomRateCodeSpecificSearch = new RoomRateCodeSpecificSearch();
//                    roomRateCodeSpecificSearch.setHid(user.getHid());
//                    roomRateCodeSpecificSearch.setRateId(regist.getRoomRateCodeId());
//                    roomRateCodeSpecificSearch.setRoomTypeId(regist.getRoomTypeId());
//
//                    List<RoomRateCodeSpecific> roomRateCodeSpecifics = roomRateCodeSpecificDao.selectBySearch(roomRateCodeSpecificSearch);
//
//                    if (roomRateCodeSpecifics.size() < 1) {
//                        throw new Exception(regist.getRoomTypeName() + " 房价码： " + regist.getRoomRateCodeName() + "未保存");
//                    }
//
//                    roomRateCodeSpecific = roomRateCodeSpecifics.get(0);
//
//                    specificHashMap.put(sket, roomRateCodeSpecific);
//
//                }

                regist.setBreakfastNum(0);

                checkInRegistMap.put(roomNumId, regist);

                // 5.入住人的添加
                List<RegistPerson> registPeople = checkInTransactionService.addCheckinGuest(JSONArray.fromObject(guestList), user, regist, 0);
                registPersonMap.put(roomNumId, registPeople);

                // 组装入住数据
                CheckInRegist checkInRegist = new CheckInRegist();
                checkInRegist.setRegist(regist);
                checkInRegist.setRegistPeople(registPeople);
                checkInRegist.setBookingOrderDailyPrices(bookingOrderDailyPrices);

                checkInRegists.add(checkInRegist);

                // 6.需要删除的辅助房态
                roomAuxiliaryRelationSearch.setRoomId(roomNumId);
                List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);
                deleteRoomAuxiliaryRelationList.addAll(roomAuxiliaryRelations);

                // 7.需要添加的辅助房态
                ArrayList<RoomAuxiliaryRelation> rarl = new ArrayList<>();


                //信息保密
                if (bookingOrderConfig.getInfoSecrecy() == 1) {
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);

                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.SECRECY);
                    roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.SECRECY);
                    rarl.add(roomAuxiliaryRelation);
                }

                if (true) {
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.HOUR_ROOM);
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);
                    rarl.add(roomAuxiliaryRelation);
                }

                if (isMachineCheck) {
                    regist.setCheckinMode(1);
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);

                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.MACHINE);
                    roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.MACHINE);

                    rarl.add(roomAuxiliaryRelation);
                }
/*

                //会员
                if (bookingOrder.getResourceId().equals(SYS_RESOURCE.RES_HY)) {
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);

                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.VIP);
                    roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.VIP);

                    rarl.add(roomAuxiliaryRelation);
                }
*/

                //团队
                if (bookingOrderRoomNums.size() > 1) {
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(user.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(user.getHotelGroupId());
                    roomAuxiliaryRelation.setRoomId(roomNumId);
                    roomAuxiliaryRelation.setRoomNum(roomInfo.getRoomNum());
                    roomAuxiliaryRelation.setRelationId(null);

                    roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.TEAM);
                    roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.TEAM);
                    if (registGroup.getGroupType() == 9) {
                        roomAuxiliaryRelation.setRoomAuxiliaryId(ROOM_AUXILIARY.JOINT_HOUSING);
                        roomAuxiliaryRelation.setSort(ROOM_AUXILIARY.JOINT_HOUSING);
                    }

                    rarl.add(roomAuxiliaryRelation);
                }

                addRoomAuxiliaryRelation.put(roomNumId, rarl);

            }

            // 5.查询预定账务信息
            int sumPay = 0;
            int sumSale = 0;
            List<Account> accounts = new ArrayList<>();
            if (registGroup.getRegistGroupId() == null || registGroup.getRegistGroupId() < 1) {
                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setBookingId(bookingId);
                accounts = accountDao.selectBySearch(accountSearch);

                for (Account account : accounts) {

                    if (account.getPayType() == 1) {
                        sumSale += account.getPrice();
                    } else {
                        sumPay += account.getPrice();
                    }

                }

                registGroup.setSumPay(sumPay);
                registGroup.setSumSales(sumSale);
            }

            registGroup.setRoomRateCodeId(rateId);
            registGroup.setRoomRateCodeName(rateCode);
            // 6.调用预定入住事务接口
            final Map<Integer, Integer> registMap = checkInTransactionService.bookingCheckInTransactionNew(bookingOrder, bookingOrderRoomNumList, checkInRegistMap, roomInfoList, registPersonMap,
                    bookingOrderConfigMap, deleteRoomAuxiliaryRelationList, addRoomAuxiliaryRelation, registGroup, accounts, user, dayPriceMap);

            final String s = roomBuilder.toString();

            responseData.setData(registMap);


//            final Integer registIdValue = registId;

           /*
            Integer k = 0;
            for (Integer i :integers){
                k = i;
                break;
            }
            final Regist regist = checkInRegistMap.get(k);*/

            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    try {
                        //更新房间缓存
                        List<RoomInfo> roomInfos = roomService.updateRoomListForCache(user);
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);

                        HashMap<String, String> filedMap = new HashMap<>();
                        filedMap.put("roomList", s);

                        HashMap<String, String> regMap = new HashMap<>();
                        //regMap.put("registId",""+regist.getRegistId());
                        int pcId = 3;
                        if (bookingOrderCheckInRequest.isMacCheckIn()) {
                            pcId = 18;
                        }
                        baseService.push(user.getHotelGroupId(), user.getHid(), pcId, filedMap, regMap, true, true);

                        HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
                        hotelSettingByParamId.setHid(user.getHid());
                        hotelSettingByParamId.setParamId(HOTEL_SETTING.AUTO_ADD_ROOMPRICE);
                        // 登记后多久产生房费
                        Object minObj = baseService.findHotelSettingByParamId(hotelSettingByParamId);
                        if (minObj == null) {
                            return;
                        }
                        int min = Integer.parseInt(minObj.toString());

                        if (min < 1) {
                            return;
                        }
                        Set<Integer> integers = registMap.keySet();

                        String registIds = "";

                        for (Integer key : integers) {
                            registIds += registMap.get(key);
                            registIds += ",";
                        }

                        registIds = registIds.substring(0, registIds.length() - 1);
                        Map<String, Object> data = new HashMap<>();
                        data.put("registIds", registIds);
                        JSONObject addRoomPriceJobParam = JobName.getAddRoomPriceJobParam(min, data);
                        baseService.addCornJob(addRoomPriceJobParam);

                    } catch (Exception e) {

                    }

                }
            });

            //智能门锁推送
            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    ArrayList<RoomSmartRequest> roomSmartRequests = new ArrayList<>();
                    for (int i = 0; i < checkInRegists.size(); i++) {
                        CheckInRegist checkInRegist = checkInRegists.get(i);
                        //如果是智能门锁则调用
                        SmartLockRequest smartLockRequest = new SmartLockRequest();
                        smartLockRequest.setSessionToken(sessionToken);
                        smartLockRequest.setCheckinTime(checkInRegist.getRegist().getCheckinTime());
                        smartLockRequest.setCheckoutTime(checkInRegist.getRegist().getCheckoutTime());
                        smartLockRequest.setRoomTypeId(checkInRegist.getRegist().getRoomTypeId());
                        smartLockRequest.setRoomTypeName(checkInRegist.getRegist().getRoomTypeName());
                        smartLockRequest.setRoomNumId(checkInRegist.getRegist().getRoomNumId());
                        smartLockRequest.setRoomNum(checkInRegist.getRegist().getRoomNum());
                        smartLockRequest.setHid(checkInRegist.getRegist().getHid());
                        smartLockRequest.setHotelGroupId(checkInRegist.getRegist().getHotelGroupId());
                        smartLockRequest.setBreakfastNum(checkInRegist.getRegist().getBreakfastNum());
                        smartLockRequest.setCheckinType(checkInRegist.getRegist().getCheckinType());
                        smartLockRequest.setLockNo(checkInRegist.getRegist().getSessionToken());
                        List<RegistPerson> registPeople = checkInRegist.getRegistPeople();
                        List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                        for (int j = 0; j < registPeople.size(); j++) {
                            RegistPerson registPersonInfo = registPeople.get(j);
                            SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                            BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                            peoplesList.add(registPersonDetail);

                            RoomSmartRequest roomSmartRequest = new RoomSmartRequest();
                            roomSmartRequest.setRoomNo(checkInRegist.getRegist().getRoomNum());
                            roomSmartRequest.setSessionToken(sessionToken);
                            roomSmartRequest.setCheckInTime(HotelUtils.parseDate2Str(checkInRegist.getRegist().getCheckinTime()));
                            roomSmartRequest.setHid(user.getHid());
                            roomSmartRequest.setCheckOutTime(HotelUtils.parseDate2Str(checkInRegist.getRegist().getCheckoutTime()));
                            roomSmartRequest.setName(registPersonInfo.getPersonName());
                            roomSmartRequest.setIdCode(registPersonInfo.getIdCode());
                            roomSmartRequest.setPhone(registPersonInfo.getPhone().trim());
                            if (roomSmartRequest.getPhone() == null || roomSmartRequest.getPhone().length() != 11) {
                                continue;
                            }
                            roomSmartRequests.add(roomSmartRequest);
                        }
                        if (roomSmartRequests.size() > 0) {
                            try {
                                baseService.smartRoomCheckIn(roomSmartRequests);
                            } catch (Exception e) {
                                log.error("",e);
                            }
                        }
                        smartLockRequest.setPeoples(peoplesList);
                        baseService.SmartLockCheckin(smartLockRequest);
                    }

                }
            });

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    //判断是否是小程序进行的入住，如果是的则到期取消
                    if (bookingOrderCheckInRequest.getFromType() == 25) {
                        log.info("创建定时任务====钟点房到期取消");
                        Integer registId = 0;
                        for (Map.Entry<Integer, Integer> entry : registMap.entrySet()) {
                            registId = entry.getValue();
                        }
                        Map<String, Object> data = new HashMap<>();
                        data.put("registId", registId.toString());
                        data.put("sessionToken", sessionToken);
                        data.put("reason", "钟点房到期取消");

                        int diff = 0;
                        try {
                            //获取退房时间
                            String checkoutTime = checkInRoomList.get(0).getCheckoutTime();
                            diff = (int) (HotelUtils.parseStr2Date(checkoutTime).getTime() - new Date().getTime()) / 1000 / 60;
                        } catch (Exception e) {

                        }
                        JSONObject jsonObject = JobName.OnAccountJob(diff, data);
                        baseService.addCornJob(jsonObject);
                    }
                }
            });


        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 用于入住成功后，添加其他宾客信息
     *
     * @param param
     * @return
     */
    @Override
    public Map<String, Object> addGuest(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            //操作日志
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);

            if (param.get("registId") == null) {
                throw new Exception("登记单号不能空");
            }
            int registId = param.getInt("registId");
            if (param.get("guestList") == null) {
                throw new Exception("宾客信息不能空");
            }
            JSONArray guestList = JSONArray.fromObject(URLDecoder.decode(param.getString("guestList"), "utf-8"));
            if (guestList.size() < 1) {
                throw new Exception("宾客信息不能空");
            }

            Regist regist = registDao.selectById(registId);
            if (regist.getState() != 0) {
                throw new Exception("当前订单不允许添加宾客信息");
            }
            addCheckinGuest(guestList, user, regist, oprecords, oprecord);
            this.addOprecords(oprecords);
        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> updateGuest(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            //操作日志
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);

            if (param.get("guestInfo") == null) {
                throw new Exception("宾客信息不能空");
            }

            String guestInfoData = URLDecoder.decode(param.getString("guestInfo"), "utf-8");
            RegistPerson registPerson = (RegistPerson) JSONObject.toBean(JSONObject.fromObject(guestInfoData), RegistPerson.class);

            if (registPerson.getRegistId() == null) {
                throw new Exception("登记单号不能空");
            }
            checkInTransactionService.updateGuestInfoTransaction(registPerson, null, user, oprecords, oprecord);
            this.addOprecords(oprecords);

        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    @Override
    public ResponseData changeRoom(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            //操作日志
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);


            ArrayList<RoomInfo> roomInfos = new ArrayList<>();

            if (param.get("registId") == null) {
                throw new Exception("登记单号不能空");
            }
            int registId = param.getInt("registId");

            Regist regist = registDao.selectById(registId);

            if (regist.getState() != 0) {
                throw new Exception("当前房间状态不允许换房");
            }

            if (param.get("roomInfo") == null) {
                throw new Exception("换房的房间信息不能空");
            }


            Integer roomNumId = regist.getRoomNumId();
            RoomInfo roomInfoOld = roomInfoDao.selectById(roomNumId);
            roomInfoOld.setRoomNumState(2);
            roomInfos.add(roomInfoOld);

            final String roomOldNum = roomInfoOld.getRoomNum();

            /**
             * 查询是否有预订排房信息
             */

            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setRoomNumId(roomNumId);
            bookingOrderRoomNumSearch.setBookingOrderId(regist.getBookingOrderId());
            bookingOrderRoomNumSearch.setRegistId(registId);

            List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);


            /**
             * 查询之前这个房间的辅助房态
             */

            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setHid(user.getHid());
            roomAuxiliaryRelationSearch.setRoomId(regist.getRoomNumId());
            roomAuxiliaryRelationSearch.setRegistId(registId);

            String roomInfoData = URLDecoder.decode(param.getString("roomInfo"), "utf-8");
            RoomInfo roomInfo = (RoomInfo) JSONObject.toBean(JSONObject.fromObject(roomInfoData), RoomInfo.class);

            String nowRoomNum = roomInfo.getRoomNum();


            regist.setRoomNum(roomInfo.getRoomNum());
            regist.setRoomNumId(roomInfo.getRoomInfoId());
            roomInfo.setRoomNumState(3);
            roomInfos.add(roomInfo);
            BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
            if (bookingOrderRoomNums.size() == 1) {
                bookingOrderRoomNum = bookingOrderRoomNums.get(0);
                bookingOrderRoomNum.setRoomNum(roomInfo.getRoomNum());
                bookingOrderRoomNum.setRoomNumId(roomInfo.getRoomInfoId());
            }
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);
            for (int i = 0; i < roomAuxiliaryRelations.size(); i++) {
                roomAuxiliaryRelations.get(i).setRoomId(roomInfo.getRoomInfoId());
                roomAuxiliaryRelations.get(i).setRoomNum(roomInfo.getRoomNum());
            }
            /**
             * 更新每日房价中的之前的房间编号
             */


            Integer countDayPrice = 0;
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setRegistId(registId);
            bookingOrderDailyPriceSearch.setRoomNumId(roomInfoOld.getRoomInfoId());
            List<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
            for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
                bookingOrderDailyPrices.get(i).setRoomNumId(roomInfo.getRoomInfoId());
                bookingOrderDailyPrices.get(i).setRoomTypeId(roomInfo.getRoomTypeId());
                countDayPrice += bookingOrderDailyPrices.get(i).getPrice();
            }

            /**
             * 查询当前订单在主人信息
             */
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(registId);
            registPersonSearch.setRegistState(0);
            List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
            for (int i = 0; i < registPersonList.size(); i++) {
                RegistPerson registPerson = registPersonList.get(i);
                registPerson.setRoomNumId(roomInfo.getRoomInfoId());
                registPerson.setRoomNum(roomInfo.getRoomNum());
            }

            /**
             * 2021-03-28 插入换房记录表
             */

            RegistChangeRecord registChangeRecord = new RegistChangeRecord();
            BaseInfoUtils.setBaseInfo(user, registChangeRecord);
            //换房
            registChangeRecord.setType(1);
            registChangeRecord.setCheckinTime(regist.getCheckinTime());
            registChangeRecord.setCheckoutTime(regist.getCheckoutTime());
            registChangeRecord.setOldRoomId(roomInfoOld.getRoomInfoId());
            registChangeRecord.setOldRoomNum(roomInfoOld.getRoomNum());
            registChangeRecord.setNewRoomId(roomInfo.getRoomInfoId());
            registChangeRecord.setNewRoomNum(roomInfo.getRoomNum());
            registChangeRecord.setOldRoomTypeId(roomInfoOld.getRoomTypeId());
            registChangeRecord.setOldRoomTypeName(roomInfoOld.getRoomTypeName());
            registChangeRecord.setNewRoomTypeId(roomInfo.getRoomTypeId());
            registChangeRecord.setNewRoomTypeName(roomInfo.getRoomTypeName());
            registChangeRecord.setRegistId(regist.getRegistId());
            registChangeRecord.setOldRoomPrice(countDayPrice);
            registChangeRecord.setNewRoomPrice(countDayPrice);
            registChangeRecord.setRemark(param.getString("remark"));
            checkInTransactionService.changeRoomTransaction(regist, registPersonList, roomInfos, roomAuxiliaryRelations, bookingOrderDailyPrices, bookingOrderRoomNum, registChangeRecord, user, oprecords, oprecord);
            this.addOprecords(oprecords);

            final String roomNo = nowRoomNum;
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        //更新房间缓存
                        List<RoomInfo> roomInfos = roomService.updateRoomListForCache(user);
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);

                        HashMap<String, String> filedMap = new HashMap<>();
                        filedMap.put("aroom", roomOldNum);
                        filedMap.put("broom", nowRoomNum);
                        baseService.push(user.getHotelGroupId(), user.getHid(), 6, filedMap, new HashMap<String, String>(), true, true);

                    } catch (Exception e) {
                        log.error("",e);
                    }

                }
            });


            //推送智能門鎖的數據
            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    //如果是智能门锁则调用
                    SmartLockRequest smartLockRequest = new SmartLockRequest();
                    smartLockRequest.setSessionToken(sessionToken);
                    smartLockRequest.setOldRoomNo(roomOldNum);
                    smartLockRequest.setCheckinTime(regist.getCheckinTime());
                    smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                    smartLockRequest.setRoomTypeId(roomInfo.getRoomTypeId());
                    smartLockRequest.setRoomTypeName(roomInfo.getRoomTypeName());
                    smartLockRequest.setRoomNumId(roomInfo.getRoomInfoId());
                    smartLockRequest.setRoomNum(roomInfo.getRoomNum());
                    smartLockRequest.setHid(regist.getHid());
                    smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                    smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                    smartLockRequest.setCheckinType(regist.getCheckinType());
                    smartLockRequest.setLockNo(regist.getSessionToken());
                    List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                    for (int j = 0; j < registPersonList.size(); j++) {
                        RegistPerson registPersonInfo = registPersonList.get(j);
                        SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                        BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                        peoplesList.add(registPersonDetail);
                    }

                    smartLockRequest.setPeoples(peoplesList);
                    baseService.SmartLockChangeRoom(smartLockRequest);
                }
            });


            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        //2022-02-09 添加会员消费撤销短信提醒
                        RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                        registPersonSearch.setRegistId(regist.getRegistId());
                        List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
                        if (registPersonList != null && registPersonList.get(0).getPhone() != null && registPersonList.get(0).getPhone().length() == 11) {
                            JSONObject postData = new JSONObject();
                            postData.put("sessionToken", sessionToken);
                            JSONObject hotelBaseInfo = JSONObject.fromObject(baseService.getHotelBaseInfo(postData));
                            JSONObject data = hotelBaseInfo.getJSONObject("data");
                            String hotelName = data.getString("hotelName");
                            String telephone = data.getString("telephone");
                            String addr = data.getString("addr");
                            //尊敬的{1}，您好，欢迎您入住{2}，您入住的房间号{3}，现已换到{4}，如有疑问请拨打酒店电话：{5}
                            final ArrayList<String> strings = new ArrayList<>();
                            strings.add(registPersonList.get(0).getPersonName());
                            strings.add(hotelName);
                            strings.add(roomInfoOld.getRoomNum());
                            strings.add(roomInfo.getRoomNum());
                            strings.add(telephone);
                            SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                            smsHotelSendRecordRequest.setLocationId(SMS_LOC.CHANGE_ROOM);
                            smsHotelSendRecordRequest.setSessionToken(user.getSessionId());
                            smsHotelSendRecordRequest.setPhoneNumber(registPersonList.get(0).getPhone());
                            smsHotelSendRecordRequest.setParams(strings);
                            baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);
                        }
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

            /**
             * 判断是否推送公安系统
             */
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
                        hotelSettingByParamId.setParentId(HOTEL_CONST.HOTEL_POLICE);
                        hotelSettingByParamId.setHid(user.getHid());
                        JSONObject hotelPrintSettingMap = findHotelPrintSettingMap(hotelSettingByParamId);
                        if (hotelPrintSettingMap.getInt(HOTEL_CONST.HOTEL_POLICE_IS_UPDATE) != 1) {
                            return;
                        }
                        log.info("hotelPrintSettingMap={}",hotelPrintSettingMap.toString());
                        String factory = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_POLICE_FACTORY);
                        String hotelCode = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_POLICE_HOTEL_CODE);
                        String url = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_POLICE_URL);
                        String key = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_POLICE_KEY);
                        String code = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_POLICE_CODE);
                        String userName = hotelPrintSettingMap.containsKey(HOTEL_CONST.HOTEL_POLICE_USER_NAME) ? hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_POLICE_USER_NAME) : "";
                        GuestModel guestModel = new GuestModel();
                        guestModel.setFactory(factory);
                        guestModel.setUrl(url);
                        guestModel.setKey(key);
                        guestModel.setCode(code);
                        guestModel.setHotelCode(hotelCode);
                        guestModel.setOperators(userName);
                        guestModel.setNewRoomNo(roomNo);
                        for (int j = 0; j < registPersonList.size(); j++) {
                            RegistPerson registPersonInfo = registPersonList.get(j);
                            guestModel.setName(registPersonInfo.getPersonName());
                            guestModel.setSex(registPersonInfo.getSex() == 0 ? "男" : "女");
                            guestModel.setiDCode(registPersonInfo.getIdCode());
                            guestModel.setAddress(registPersonInfo.getAddress());
                            guestModel.setGuestId(registPersonInfo.getGuestId());
                            guestModel.setGuestNo(registPersonInfo.getGuestNo());
                            baseService.PoliceChangeRoom(guestModel);
                        }
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });


        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updateRoomType(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            //操作日志
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);
            ArrayList<RoomInfo> roomInfos = new ArrayList<>();
            if (param.get("registId") == null) {
                throw new Exception("登记单号不能空");
            }
            int registId = param.getInt("registId");
            Regist regist = registDao.selectById(registId);
            if (regist.getState() != 0) {
                throw new Exception("当前房间状态不允许换房");
            }
            if (param.get("updateRoomTypeData") == null) {
                throw new Exception("房型升级数据不能空");
            }
            String roomInfoData = URLDecoder.decode(param.getString("updateRoomTypeData"), "utf-8");
            JSONObject updateRoomTypeData = JSONObject.fromObject(roomInfoData);
            if (updateRoomTypeData.get("reasonInfo") == null) {
                throw new Exception("房型升级是由不能空");
            }
            if (updateRoomTypeData.get("roomTypeInfo") == null) {
                throw new Exception("将升级的房型信息不能空");
            }
            if (updateRoomTypeData.get("roomInfo") == null) {
                throw new Exception("将升级的房间信息不能空");
            }
            Integer roomNumId = regist.getRoomNumId();
            RoomInfo roomInfoOld = roomInfoDao.selectById(roomNumId);
            roomInfoOld.setRoomNumState(2);
            roomInfos.add(roomInfoOld);
            final String roomOldNum = roomInfoOld.getRoomNum();
            /**
             * 查询之前这个房间的辅助房态
             */
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setHid(user.getHid());
            roomAuxiliaryRelationSearch.setRoomId(regist.getRoomNumId());
            roomAuxiliaryRelationSearch.setRegistId(registId);

            /**
             * 获取新的房间号信息
             */
            RoomInfo roomInfo = (RoomInfo) JSONObject.toBean(JSONObject.fromObject(updateRoomTypeData.getString("roomInfo")), RoomInfo.class);
            regist.setRoomNum(roomInfo.getRoomNum());
            regist.setRoomNumId(roomInfo.getRoomInfoId());
            roomInfo.setRoomNumState(3);
            roomInfos.add(roomInfo);

            final String nowRoomNum = roomInfo.getRoomNum();
            Integer countDayPrice = 0;

            /**
             * 查询之前房间的每日房价信息
             */
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setRegistId(registId);
            List<BookingOrderDailyPrice> oldbookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
            for (int i = 0; i < oldbookingOrderDailyPrices.size(); i++) {
                countDayPrice += oldbookingOrderDailyPrices.get(i).getPrice();

            }
            /**
             * 获取新的房型信息
             */
            RoomType roomType = (RoomType) JSONObject.toBean(JSONObject.fromObject(updateRoomTypeData.getString("roomTypeInfo")), RoomType.class);
            regist.setRoomTypeId(roomType.getRoomTypeId());
            regist.setRoomTypeName(roomType.getRoomTypeName());


            ArrayList<BookingOrderDailyPrice> bookingOrderDailyPrices = new ArrayList<>();

            Integer NewDayPriceCount = 0;
            /**
             * 如果不是原价房型升级
             */
            if (!updateRoomTypeData.getBoolean("isCostPrice")) {
                RoomRateCode roomRateCode = (RoomRateCode) JSONObject.toBean(JSONObject.fromObject(updateRoomTypeData.getString("rateCodeInfo")), RoomRateCode.class);
                regist.setRoomRateCodeName(roomRateCode.getRateCode());
                regist.setRoomRateCodeId(roomRateCode.getRateId());
                String dayPriceInfo = updateRoomTypeData.getString("dayPriceInfo");
                JSONArray dayPrices = JSONArray.fromObject(dayPriceInfo);
                if (dayPrices.size() < 1) {
                    throw new Exception("房价信息不能空");
                }
                for (int i = 0; i < dayPrices.size(); i++) {
                    BookingOrderDailyPrice bookingOrderDailyPrice = (BookingOrderDailyPrice) JSONObject.toBean(dayPrices.getJSONObject(i), BookingOrderDailyPrice.class);
                    bookingOrderDailyPrice.setRoomNumId(roomInfo.getRoomInfoId());
                    bookingOrderDailyPrice.setRoomTypeId(roomInfo.getRoomTypeId());
                    bookingOrderDailyPrices.add(bookingOrderDailyPrice);
                    NewDayPriceCount += bookingOrderDailyPrice.getPrice();
                }
            } else {
                for (int i = 0; i < oldbookingOrderDailyPrices.size(); i++) {
                    BookingOrderDailyPrice bookingOrderDailyPrice = oldbookingOrderDailyPrices.get(i);
                    bookingOrderDailyPrice.setRoomNumId(roomInfo.getRoomInfoId());
                    bookingOrderDailyPrice.setRoomTypeId(roomInfo.getRoomTypeId());
                    bookingOrderDailyPrices.add(bookingOrderDailyPrice);
                }
                NewDayPriceCount = countDayPrice;
            }
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);
            for (int i = 0; i < roomAuxiliaryRelations.size(); i++) {
                roomAuxiliaryRelations.get(i).setRoomId(roomInfo.getRoomInfoId());
                roomAuxiliaryRelations.get(i).setRoomNum(roomInfo.getRoomNum());
            }
            /**
             * 查询是否有预订排房信息
             */
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            bookingOrderRoomNumSearch.setRoomNumId(roomNumId);
            bookingOrderRoomNumSearch.setBookingOrderId(regist.getBookingOrderId());
            bookingOrderRoomNumSearch.setRegistId(registId);
            List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
            BookingOrderRoomNum bookingOrderRoomNum = new BookingOrderRoomNum();
            if (bookingOrderRoomNums.size() == 1) {
                bookingOrderRoomNum = bookingOrderRoomNums.get(0);
                bookingOrderRoomNum.setRoomNum(roomInfo.getRoomNum());
                bookingOrderRoomNum.setRoomNumId(roomInfo.getRoomInfoId());
                bookingOrderRoomNum.setRoomTypeId(roomInfo.getRoomTypeId());
            }


            /**
             * 查询当前订单在主人信息
             */
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(registId);
            registPersonSearch.setRegistState(0);
            List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
            for (int i = 0; i < registPersonList.size(); i++) {
                RegistPerson registPerson = registPersonList.get(i);
                registPerson.setRoomNumId(roomInfo.getRoomInfoId());
                registPerson.setRoomNum(roomInfo.getRoomNum());
            }





            /**
             * 2021-03-28 插入换房记录表
             */

            RegistChangeRecord registChangeRecord = new RegistChangeRecord();
            BaseInfoUtils.setBaseInfo(user, registChangeRecord);
            //房型升级
            registChangeRecord.setType(2);
            registChangeRecord.setCheckinTime(regist.getCheckinTime());
            registChangeRecord.setCheckoutTime(regist.getCheckoutTime());
            registChangeRecord.setOldRoomId(roomInfoOld.getRoomInfoId());
            registChangeRecord.setOldRoomNum(roomInfoOld.getRoomNum());
            registChangeRecord.setNewRoomId(roomInfo.getRoomInfoId());
            registChangeRecord.setNewRoomNum(roomInfo.getRoomNum());
            registChangeRecord.setOldRoomTypeId(roomInfoOld.getRoomTypeId());
            registChangeRecord.setOldRoomTypeName(roomInfoOld.getRoomTypeName());
            registChangeRecord.setNewRoomTypeId(roomInfo.getRoomTypeId());
            registChangeRecord.setNewRoomTypeName(roomInfo.getRoomTypeName());
            registChangeRecord.setRegistId(regist.getRegistId());
            registChangeRecord.setOldRoomPrice(countDayPrice);
            registChangeRecord.setNewRoomPrice(NewDayPriceCount);
            registChangeRecord.setRemark(updateRoomTypeData.get("reasonInfo").toString());

            checkInTransactionService.updateRoomTypeTransaction(regist, registPersonList, roomInfos, roomAuxiliaryRelations, bookingOrderDailyPrices, bookingOrderRoomNum, registChangeRecord, user, oprecords, oprecord);


            /**
             * 数据推送iot酒店
             **/
            IotHotelRoomExchange roomExchange = new IotHotelRoomExchange();
            roomExchange.setCode(regist.getRegistId() + "");
            roomExchange.setPmsRoomId(roomInfo.getRoomInfoId() + "");
            HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
            Map<String, String> authParamMap = new HashMap<>();
            authParamMap.put("eid", user.getHid().toString());
            authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
            authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
            authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
            try {
                authParamMap.put("phone", AESUtil.encryptAesCbc(user.getPhone(),iotPlatConfig.getSassTokenSecret()));
            } catch (Exception e) {
                log.error("手机号加密失败");
            }
            hotelIotStrategy.initStrategy(authParamMap);

            webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.EXCHANGE,
                    roomExchange,
                    hotelIotStrategy);


            this.addOprecords(oprecords);
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        //更新房间缓存
                        List<RoomInfo> roomInfos = roomService.updateRoomListForCache(user);
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);

                        HashMap<String, String> filedMap = new HashMap<>();
                        filedMap.put("aroom", roomOldNum);
                        filedMap.put("broom", nowRoomNum);
                        baseService.push(user.getHotelGroupId(), user.getHid(), 6, filedMap, new HashMap<String, String>(), true, true);

                    } catch (Exception e) {

                    }

                }
            });

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        //2022-02-09 添加会员消费撤销短信提醒
                        RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                        registPersonSearch.setRegistId(regist.getRegistId());
                        List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
                        if (registPersonList != null && registPersonList.get(0).getPhone() != null && registPersonList.get(0).getPhone().length() == 11) {
                            JSONObject postData = new JSONObject();
                            postData.put("sessionToken", sessionToken);
                            JSONObject hotelBaseInfo = JSONObject.fromObject(baseService.getHotelBaseInfo(postData));
                            JSONObject data = hotelBaseInfo.getJSONObject("data");
                            String hotelName = data.getString("hotelName");
                            String telephone = data.getString("telephone");
                            String addr = data.getString("addr");
                            //尊敬的{1}，您好，欢迎您入住{2}，您入住的{3}，房间号{4}，现已升级到{5}，新的房间号为{6}，如有疑问请拨打酒店电话：{7}
                            final ArrayList<String> strings = new ArrayList<>();
                            strings.add(registPersonList.get(0).getPersonName());
                            strings.add(hotelName);
                            strings.add(roomInfoOld.getRoomTypeName());
                            strings.add(roomInfoOld.getRoomNum());
                            strings.add(roomInfo.getRoomTypeName());
                            strings.add(roomInfo.getRoomNum());
                            strings.add(telephone);
                            SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                            smsHotelSendRecordRequest.setLocationId(SMS_LOC.ROOM_TYPE_UP);
                            smsHotelSendRecordRequest.setSessionToken(user.getSessionId());
                            smsHotelSendRecordRequest.setPhoneNumber(registPersonList.get(0).getPhone());
                            smsHotelSendRecordRequest.setParams(strings);
                            baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);
                        }
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

            //推送智能門鎖的數據
            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    //如果是智能门锁则调用
                    SmartLockRequest smartLockRequest = new SmartLockRequest();
                    smartLockRequest.setSessionToken(sessionToken);
                    smartLockRequest.setOldRoomNo(roomOldNum);
                    smartLockRequest.setCheckinTime(regist.getCheckinTime());
                    smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                    smartLockRequest.setRoomTypeId(roomInfo.getRoomTypeId());
                    smartLockRequest.setRoomTypeName(roomInfo.getRoomTypeName());
                    smartLockRequest.setRoomNumId(roomInfo.getRoomInfoId());
                    smartLockRequest.setRoomNum(roomInfo.getRoomNum());
                    smartLockRequest.setHid(regist.getHid());
                    smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                    smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                    smartLockRequest.setCheckinType(regist.getCheckinType());
                    smartLockRequest.setLockNo(regist.getSessionToken());
                    List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                    for (int j = 0; j < registPersonList.size(); j++) {
                        RegistPerson registPersonInfo = registPersonList.get(j);
                        SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                        BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                        peoplesList.add(registPersonDetail);
                    }

                    smartLockRequest.setPeoples(peoplesList);
                    baseService.SmartLockChangeRoom(smartLockRequest);
                }
            });

            /**
             * 判断是否推送公安系统
             */
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
                        hotelSettingByParamId.setParentId(HOTEL_CONST.HOTEL_POLICE);
                        hotelSettingByParamId.setHid(user.getHid());
                        JSONObject hotelPrintSettingMap = findHotelPrintSettingMap(hotelSettingByParamId);
                        if (hotelPrintSettingMap.getInt(HOTEL_CONST.HOTEL_POLICE_IS_UPDATE) != 1) {
                            return;
                        }
                        String factory = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_POLICE_FACTORY);
                        String hotelCode = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_POLICE_HOTEL_CODE);
                        String url = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_POLICE_URL);
                        String key = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_POLICE_KEY);
                        String code = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_POLICE_CODE);
                        String userName = hotelPrintSettingMap.containsKey(HOTEL_CONST.HOTEL_POLICE_USER_NAME) ? hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_POLICE_USER_NAME) : "";
                        GuestModel guestModel = new GuestModel();
                        guestModel.setFactory(factory);
                        guestModel.setUrl(url);
                        guestModel.setKey(key);
                        guestModel.setCode(code);
                        guestModel.setHotelCode(hotelCode);
                        guestModel.setOperators(userName);
                        guestModel.setNewRoomNo(nowRoomNum);
                        for (int j = 0; j < registPersonList.size(); j++) {
                            RegistPerson registPersonInfo = registPersonList.get(j);
                            guestModel.setName(registPersonInfo.getPersonName());
                            guestModel.setSex(registPersonInfo.getSex() == 0 ? "男" : "女");
                            guestModel.setiDCode(registPersonInfo.getIdCode());
                            guestModel.setAddress(registPersonInfo.getAddress());
                            guestModel.setGuestId(registPersonInfo.getGuestId());
                            guestModel.setGuestNo(registPersonInfo.getGuestNo());
                            baseService.PoliceChangeRoom(guestModel);
                        }
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public Map<String, Object> updateCheckinType(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (param.get("registId") == null) {
                throw new Exception("登记单不能空");
            }

            int registId = param.getInt("registId");

            Regist regist = registDao.selectById(registId);

            if (regist.getState() != 0) {
                throw new Exception("当前房间状态不允许转换入住类型");
            }
            Integer checkinType = regist.getCheckinType();
            /**
             * 日租房转钟点房
             */
            if (checkinType == 1) {
                /**
                 * 判断如果入住时间超过5个小时，不允许转钟点房
                 */
                Date checkinTime = regist.getCheckinTime();

                Date date = new Date();

                long times = date.getTime() - checkinTime.getTime();

                if (times / (1000 * 60 * 60) > 5) {
                    throw new Exception("开房时间已经超过5个小时不允许转钟点房");
                }


            }
            /**
             * 钟点房转日租房
             */
            else if (checkinType == 2) {

            }
            /**
             * 凌晨房转日租房
             */
            else if (checkinType == 3) {

            }


        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData setHostPerson(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (param.get("registId") == null) {
                throw new Exception("登记单不能空");
            }

            if (param.get("registPersonId") == null) {
                throw new Exception("设置信息不明确");
            }
            int registPersonId = param.getInt("registPersonId");

            int registId = param.getInt("registId");

            Regist regist = registDao.selectById(registId);

            if (regist.getState() != 0) {
                throw new Exception("当前订单不能修改，请检查订单状态");
            }

            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(registId);

            List<RegistPerson> registPeopleList = registPersonDao.selectBySearch(registPersonSearch);
            for (int i = 0; i < registPeopleList.size(); i++) {
                RegistPerson registPerson = registPeopleList.get(i);
                if (registPerson.getRegistPersonId() == registPersonId) {
                    registPerson.setIsOther(0);
                } else {
                    registPerson.setIsOther(1);
                }

                Integer update = registPersonDao.update(registPerson);
                if (update < 1) {
                    throw new Exception("修改失败");
                }
            }

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updateRegistState(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            /*1.获取用户信息*/
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (param.get("registId") == null) {
                throw new Exception("登记单不能空");
            }

            if (param.get("registPersonId") == null) {
                throw new Exception("设置信息不明确");
            }

            if (param.get("type") == null) {
                throw new Exception("类型不明确");
            }

            int registPersonId = param.getInt("registPersonId");

            int registId = param.getInt("registId");

            int type = param.getInt("type");

            Regist regist = registDao.selectById(registId);

            if (regist.getState() != 0) {
                throw new Exception("当前订单不能修改，请检查订单状态");
            }

            //查询当前入住状态的宾客集合
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(registId);
            registPersonSearch.setRegistState(0);
            List<RegistPerson> registPeopleList = registPersonDao.selectBySearch(registPersonSearch);

            //除了要修改的
            List<RegistPerson> personList = new ArrayList<RegistPerson>();

            RegistPerson registPerson = registPersonDao.selectById(registPersonId);

            /**
             * 如果是提前离店，或者挂账的话，获取当前客人是否主客
             */
            if (type == 1 || type == 2) {

                //如果当前操作人是主客，又没有其他客人的话
                if (registPerson.getIsOther() == 0 && registPeopleList.size() == 0) {
                    throw new Exception("请进行退房处理");
                }

                for (int i = 0; i < registPeopleList.size(); i++) {
                    RegistPerson registPersonInfo = registPeopleList.get(i);
                    if (registPersonInfo.getRegistPersonId() != registPersonId) {
                        personList.add(registPersonInfo);
                    }
                }

                if (personList.size() < 1) {
                    throw new Exception("请进行退房处理");
                }
                personList.get(0).setIsOther(0);

                Integer update = registPersonDao.update(personList.get(0));
                if (update < 1) {
                    throw new Exception("修改失败");
                }

                registPerson.setIsOther(1);
                registPerson.setRegistState(type);
                update = registPersonDao.update(registPerson);
                if (update < 1) {
                    throw new Exception("修改失败");
                }
            }
            //重新入住
            else {
                registPerson.setRegistState(0);
                Integer update = registPersonDao.update(registPerson);
                if (update < 1) {
                    throw new Exception("修改失败");
                }
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updateRegistInfo(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            if (param.get("type") == null && param.getInt("type") < 0) {
                throw new Exception("参数不正确");
            }
            int type = param.getInt("type");
            if (type == 1) {
                if (param.get("bookingOrderId") == null) {
                    throw new Exception("参数不正确");
                }
                int bookingOrderId = param.getInt("bookingOrderId");
//                BookingOrder bookingOrder = bookingOrderDao.selectById(bookingOrderId);
                BookingOrder bookingOrder = new BookingOrder();
                bookingOrder.setTeamCodeId(null);
                bookingOrder.setCreateTime(null);
                bookingOrder.setBookingOrderId(bookingOrderId);
                if (param.get("bookingPhone") != null && !param.getString("bookingPhone").equals("")) {
                    bookingOrder.setBookingPhone(param.getString("bookingPhone"));
                }

                if (param.get("bookingIdCode") != null && !param.getString("bookingIdCode").equals("")) {
                    bookingOrder.setBookingIdCode(param.getString("bookingIdCode"));
                }

                if (param.get("bookingName") != null && !param.getString("bookingName").equals("")) {
                    bookingOrder.setBookingName(URLDecoder.decode(param.getString("bookingName"), "utf-8"));
                }

                if (param.get("remark") != null) {
                    bookingOrder.setRemark(URLDecoder.decode(param.getString("remark"), "utf-8"));
                }
                Integer update = bookingOrderDao.editBookingOrder(bookingOrder);
                if (update < 1) {
                    throw new Exception("修改预订单信息失败");
                }
            } else if (type == 2) {
                if (param.get("registId") == null) {
                    throw new Exception("参数不正确");
                }
                int registId = param.getInt("registId");
//                Regist regist = registDao.selectById(registId);
                Regist regist = new Regist();
                regist.setRegistId(registId);
                regist.setSumPay(null);
                regist.setSumSale(null);
                regist.setTeamCodeId(null);
                regist.setCreateTime(null);
                if (param.get("remark") != null) {
                    regist.setRemark(URLDecoder.decode(param.getString("remark"), "utf-8"));
                }
                Integer update = registDao.update(regist);
                if (update < 1) {
                    throw new Exception("修改主单信息失败");
                }
            }

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData teamCombine(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);
            //

            if (param.get("type") == null) {
                throw new Exception("参数不明确");
            }
            int type = param.getInt("type");

            //联房
            if (type == 1) {

                responseData = this.jionRoom(param);

            }
            //团队
            else if (type == 2) {
                responseData = this.teamJion(param);
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    // 联房
    public ResponseData jionRoom(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            //日志记录
            ArrayList<OprecordInfoRequest> oprecordInfoRequests = new ArrayList<>();
            OprecordInfoRequest oprecordInfoRequest = new OprecordInfoRequest();
            oprecordInfoRequest.setSessionToken(sessionToken);


            JSONArray registIds = JSONArray.fromObject(URLDecoder.decode(param.getString("registIds"), "utf-8"));

            // 1. 根据传递房间找出所选 联房信息
            HashMap<Integer, Boolean> groupIdMap = new HashMap<>();
            // 当前团队下所有的房间信息

            // 修改的登记单信息
            ArrayList<Regist> regists = new ArrayList<>();

            ArrayList<RoomAuxiliaryRelation> addAuxRoom = new ArrayList<>();


            Regist mainRes = new Regist();

            // 撤销的团队信息
            ArrayList<RegistGroup> registGroups = new ArrayList<>();

            for (int i = 0; i < registIds.size(); i++) {
                JSONObject reg = registIds.getJSONObject(i);
                Regist regist = registDao.selectById(reg.getInt("registId"));
                if (i == registIds.size() - 1) {
                    oprecordInfoRequest.setRegistId(regist.getRegistId());
                    oprecordInfoRequest.setRoomNum(regist.getRoomNum());
                }
                int teamCodeId = regist.getTeamCodeId();
                if (teamCodeId > 0) {
                    groupIdMap.put(teamCodeId, true);
                } else {
                    int registId = reg.getInt("registId");
                    regists.add(regist);
                    RoomAuxiliaryRelation rar = new RoomAuxiliaryRelation();
                    rar.setHid(user.getHid());
                    rar.setHotelGroupId(user.getHotelGroupId());
                    rar.setRoomAuxiliaryId(ROOM_AUXILIARY.JOINT_HOUSING);
                    rar.setRoomNum(regist.getRoomNum());
                    rar.setRoomId(regist.getRoomNumId());
                    rar.setRegistId(registId);
                    rar.setBookingOrderId(regist.getBookingOrderId());
                    addAuxRoom.add(rar);
                }
            }

            // 2.创建新的联房信息
            String groupIdStr = "";
            Set<Integer> groupIds = groupIdMap.keySet();


            // 所有账务信息

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());

            String teamCodeIds = "";

            String teamCodeStrs = "";

            Boolean ast = false;

            for (Integer grpId : groupIds) {

                groupIdStr += grpId + "";

                RegistGroup registGroup = registGroupDao.selectById(grpId);

                registGroups.add(registGroup);

                teamCodeIds += grpId + ",";

                ast = true;

            }

            registSearch.setTeamCodeIdStr(teamCodeIds);

            List<Regist> regists1 = new ArrayList<>();
            if (ast) {
                regists1 = registDao.selectBySearch(registSearch);
            }

            regists.addAll(regists1);

            // 添加的团队信息
            RegistGroup registGroup = new RegistGroup();

            Date date = new Date();

            registGroup.setSn(OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.GROUP, stringRedisTemplate));
            registGroup.setGroupType(9);
            registGroup.setHid(user.getHid());
            registGroup.setHotelGroupId(user.getHotelGroupId());
            registGroup.setGroupName(registGroup.getSn());
            registGroup.setRemark("联房合并:" + groupIdStr);
            registGroup.setCreateTime(date);
            registGroup.setBusinessDay(user.getBusinessDay());
            registGroup.setState(1);

            if (registGroups.size() > 0) {
                RegistGroup rg1 = registGroups.get(0);

                registGroup.setProvince(rg1.getProvince());
                registGroup.setCity(rg1.getCity());
                registGroup.setCompanyId(rg1.getCompanyId());
                registGroup.setCompayName(rg1.getCompayName());
                registGroup.setRoomRateCodeId(rg1.getRoomRateCodeId());
                registGroup.setRoomRateCodeName(rg1.getRoomRateCodeName());
                registGroup.setMemberId(rg1.getMemberId());
                registGroup.setMemberCard(rg1.getMemberCard());

            }

            Integer sumPay = 0;
            Integer sumSale = 0;

            String regIdStr = "";

            Boolean isRegMain = true;

            for (Regist regist : regists) {

                regIdStr += regist.getRegistId() + ",";

                sumPay += regist.getSumPay();
                sumSale += regist.getSumSale();

                Integer isMainRoom = regist.getIsMainRoom();

                if (isMainRoom == 1) {
                    mainRes = regist;
                    isRegMain = false;
                }

                teamCodeStrs += regist.getRoomNum() + ",";
            }

            if (isRegMain) {
                mainRes = regists.get(0);
            }

            regIdStr += "-99";

            registGroup.setSumPay(sumPay);
            registGroup.setSumSales(sumSale);
            registGroup.setSumRooms(regists.size());


            // 查询账务信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setRegistIds(regIdStr);

            // 修改的账务信息
            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setHid(user.getHid());
            registPersonSearch.setRegInKeys(regIdStr);

            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);

            Integer integer = checkInTransactionService.roomJion(registGroup, mainRes, registGroups, regists, accounts, addAuxRoom, registPeople);

//            Oprecord oprecord = new Oprecord(user);
//
//            oprecord.setDescription("将:" + teamCodeStrs + "进行联房处理");

            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        baseService.push(user.getHotelGroupId(), user.getHid(), 9, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
                    } catch (Exception e) {

                    }

                }
            });

            oprecordInfoRequest.setOprecordTemplateId(11);
            JSONObject oprecordJson = new JSONObject();
            oprecordJson.put("teamCodeStrs", teamCodeStrs);
            oprecordJson.put("time", HotelUtils.currentTime());
            oprecordInfoRequest.setBusinessId2(oprecordJson.toString());
            oprecordInfoRequests.add(oprecordInfoRequest);
            //处理操作日志
            this.baseService.addOprecordReqs(oprecordInfoRequests);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public ResponseData teamJion(JSONObject param) {

        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            //日志记录
            ArrayList<OprecordInfoRequest> oprecordInfoRequests = new ArrayList<>();
            OprecordInfoRequest oprecordInfoRequest = new OprecordInfoRequest();
            oprecordInfoRequest.setSessionToken(sessionToken);

            if (param.get("registId") == null) {
                throw new Exception("缺少必要参数");
            }

            if (param.get("registGroupId") == null) {
                throw new Exception("缺少必要参数");
            }

            int registId = param.getInt("registId");

            int registGroupId = param.getInt("registGroupId");


            // 登记信息
            Regist regist = registDao.selectById(registId);

            // 团队信息
            RegistGroup registGroup = registGroupDao.selectById(registGroupId);

            // 需要撤销的团队信息
            RegistGroup delRegistGroup = new RegistGroup();
            delRegistGroup.setRegistGroupId(-1);

            // 修改的登记单号
            ArrayList<Regist> regists = new ArrayList<>();
            regists.add(regist);

            // 需要添加的辅助房态信息
            ArrayList<RoomAuxiliaryRelation> addRoomAux = new ArrayList<>();

            if (regist.getTeamCodeId() > 0) {

                RegistSearch registSearch = new RegistSearch();
                registSearch.setHid(user.getHid());
                registSearch.setTeamCodeId(regist.getTeamCodeId());

                List<Regist> regists1 = registDao.selectBySearch(registSearch);
                regists.addAll(regists1);

                delRegistGroup = registGroupDao.selectById(regist.getTeamCodeId());

            } else {

                RoomAuxiliaryRelation rar = new RoomAuxiliaryRelation();
                rar.setHid(user.getHid());
                rar.setHotelGroupId(user.getHotelGroupId());
                rar.setRoomAuxiliaryId(ROOM_AUXILIARY.TEAM);
                rar.setRoomNum(regist.getRoomNum());
                rar.setRoomId(regist.getRoomNumId());
                rar.setRegistId(registId);
                rar.setBookingOrderId(regist.getBookingOrderId());
                addRoomAux.add(rar);

            }

            String regIdStr = "";

            Integer sumPay = 0;
            Integer sumSale = 0;

            String teamCodeStrs = "";

            ArrayList<Regist> registList = new ArrayList<>();
            for (int i = 0; i < regists.size(); i++) {

                Regist registInfo = Regist.CreateRegist(regists.get(i).getRegistId());


                regists.get(i).setIsMainRoom(0);
                regists.get(i).setRegistGroupId(registGroup.getRegistGroupId());
                regists.get(i).setTeamCodeId(registGroup.getRegistGroupId());
                regists.get(i).setTeamCodeName(registGroup.getGroupName());

                registInfo.setIsMainRoom(0);
                registInfo.setRegistGroupId(registGroup.getRegistGroupId());
                registInfo.setTeamCodeId(registGroup.getRegistGroupId());
                registInfo.setTeamCodeName(registGroup.getGroupName());


                regIdStr += regists.get(i).getRegistId() + ",";

                sumPay += regists.get(i).getSumPay();
                sumSale += regists.get(i).getSumSale();

                teamCodeStrs += regists.get(i).getRoomNum() + ",";

                registList.add(registInfo);

            }

            regIdStr += "-99";

            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setRegistIds(regIdStr);

            registGroup.setSumRooms(registGroup.getSumRooms() + regists.size());
            registGroup.setSumSales(registGroup.getSumPay() + sumPay);
            registGroup.setSumSales(registGroup.getSumSales() + sumSale);

            // 修改的账务信息
            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            // 修改的登记人信息
            List<RegistPerson> registPeople = new ArrayList<>();

            checkInTransactionService.teamJion(registGroup, delRegistGroup, registList, accounts, registPeople, addRoomAux);

            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        baseService.push(user.getHotelGroupId(), user.getHid(), 9, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
                    } catch (Exception e) {

                    }

                }
            });

            oprecordInfoRequest.setOprecordTemplateId(12);
            JSONObject oprecordJson = new JSONObject();
            oprecordJson.put("teamCodeStrs", teamCodeStrs);
            oprecordJson.put("time", HotelUtils.currentTime());
            oprecordJson.put("teamName", registGroup.getGroupName());
            oprecordInfoRequest.setBusinessId2(oprecordJson.toString());
            oprecordInfoRequests.add(oprecordInfoRequest);
            //处理操作日志
            this.baseService.addOprecordReqs(oprecordInfoRequests);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData hourToDay(HourToDayRequest hourToDayRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = hourToDayRequest.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            //操作日志
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);


            if (hourToDayRequest.getRegistId() == null) {
                throw new Exception("此订单状态异常，请检查");
            }
            Integer registId = hourToDayRequest.getRegistId();
            Regist regist = registDao.selectById(registId);
            if (regist.getState() != 0) {
                throw new Exception("此订单状态异常，请检查");
            }
            regist.setCheckinType(1);
            regist.setCheckoutTime(hourToDayRequest.getNewDate());
            regist.setRoomRateCodeId(hourToDayRequest.getRateId());
            regist.setRoomRateCodeName(hourToDayRequest.getRateCode());

            //需要删除的价格目录
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setRegistId(registId);
            List<BookingOrderDailyPrice> delBookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);

            BookingOrderDailyPrice delBookPrice = delBookingOrderDailyPrices.get(0);

            // 冲账的账务
            Account cancelAccount = null;

            Boolean cancelAccountBus = true;


            Integer priceBus = user.getBusinessDay();
            // 不可修改 则冲账
            if (delBookPrice.getDailyTime().equals(user.getBusinessDay())) {

                AccountSearch accountSearch = new AccountSearch();
                accountSearch.setRegistId(registId);
                accountSearch.setThirdAccoutId(delBookPrice.getId() + "");

                List<Account> accounts = accountDao.selectBySearch(accountSearch);

                if (accounts != null && accounts.size() > 0) {

                    cancelAccount = accounts.get(0);

                    cancelAccount.setIsCancel(3);
                    cancelAccount.setRegistState(3);

                    cancelAccount.setRemark(cancelAccount.getRemark() + "转日租后进行冲账处理");

                    regist.setSumSale(regist.getSumSale() - cancelAccount.getPrice());

                }

                cancelAccountBus = false;

            } else {
                delBookingOrderDailyPrices = new ArrayList<>();
            }


            List<BookingOrderDailyPrice> bookingOrderDailyPrices = new ArrayList<BookingOrderDailyPrice>();
            for (int i = 0; i < hourToDayRequest.getPriceInfoList().size(); i++) {
                Integer dayTime = hourToDayRequest.getPriceInfoList().get(i).getDayTime();
                // 如果过了夜审，则不删除原有的钟点房价
                if (cancelAccountBus) {
                    if (dayTime.equals(priceBus)) {
                        continue;
                    }
                }
                BookingOrderDailyPrice bookingOrderDailyPrice = new BookingOrderDailyPrice();
                bookingOrderDailyPrice.setHid(user.getHid());
                bookingOrderDailyPrice.setHotelGroupId(user.getHotelGroupId());
                bookingOrderDailyPrice.setRegistId(regist.getRegistId());
                bookingOrderDailyPrice.setRoomNumId(regist.getRoomNumId());
                bookingOrderDailyPrice.setRoomTypeId(regist.getRoomTypeId());
                bookingOrderDailyPrice.setIsStayover(1);
                bookingOrderDailyPrice.setDailyState(1);
                bookingOrderDailyPrice.setUpdateUserName(user.getUserName());
                bookingOrderDailyPrice.setUpdateTime(new Date());
                bookingOrderDailyPrice.setUpdateUserId(user.getUserId());
                bookingOrderDailyPrice.setCreateUserName(user.getUserName());
                bookingOrderDailyPrice.setCreateUserId(user.getUserId());
                bookingOrderDailyPrice.setCreateTime(new Date());
                bookingOrderDailyPrice.setId(null);
                bookingOrderDailyPrice.setPrice(hourToDayRequest.getPriceInfoList().get(i).getPrice());
                bookingOrderDailyPrice.setDailyTime(dayTime);
                bookingOrderDailyPrices.add(bookingOrderDailyPrice);
            }


            /**
             * 对辅助房态进行修改
             */
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setHid(user.getHid());
            roomAuxiliaryRelationSearch.setRoomId(regist.getRoomNumId());
            List<RoomAuxiliaryRelation> roomAuxiliaryRelationList = new ArrayList<RoomAuxiliaryRelation>();
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);
            for (RoomAuxiliaryRelation roomAuxiliaryRelation : roomAuxiliaryRelations) {
                Integer roomAuxiliaryId = roomAuxiliaryRelation.getRoomAuxiliaryId();
                if (roomAuxiliaryId == 18) {
                    roomAuxiliaryRelationList.add(roomAuxiliaryRelation);
                }
            }
            oprecord.setRegistId(registId);
            oprecord.setRoomNum(regist.getRoomNum());
            oprecord.setDescription("房间:" + regist.getRoomNum() + "由钟点房转日租房");
            checkInTransactionService.hourToDayTransaction(regist, delBookingOrderDailyPrices, bookingOrderDailyPrices, roomAuxiliaryRelationList, user, oprecords, cancelAccount);
            // 更新房间缓存
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        baseService.push(user.getHotelGroupId(), user.getHid(), 7, new HashMap<String, String>(), new HashMap<String, String>(), true, true);

                    } catch (Exception e) {

                    }
                }
            });

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData aginCheckin(JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            String sessionToken = param.getString(ER.SESSION_TOKEN);
            final TbUserSession user = this.getTbUserSession(sessionToken);

            ArrayList<OprecordInfoRequest> oprecordInfoRequests = new ArrayList<>();

            if (param.get("registId") == null || param.getString("registId").equals("")) {
                throw new Exception("主单编号不能为空！");
            }

            int registId = param.getInt("registId");


            Regist regist = registDao.selectById(registId);

            if (regist == null || (regist.getState() != 1 && regist.getState() != 2)) {
                throw new Exception("当前入住单无法重新入住！");
            }


            if (!regist.getCheckoutBusinessDay().equals(user.getBusinessDay())) {
                throw new Exception("当前入住单无法重新入住！");
            }

            RoomInfo roomInfo = roomInfoDao.selectById(regist.getRoomNumId());

            if (roomInfo == null || roomInfo.getRoomNumState() > 2) {
                throw new Exception("请检查房间状态！");
            }
            //处理团队结账情况
            RegistGroup registGroup = null;
            if (regist.getTeamCodeId() > 0) {
                registGroup = registGroupDao.selectById(regist.getTeamCodeId());
            }
            /**
             * 修改登记人状态
             */
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(registId);
            List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
            if (registPersonList != null && registPersonList.size() > 0) {

                for (int i = 0; i < registPersonList.size(); i++) {
                    RegistPerson registPerson = registPersonList.get(i);
                    registPerson.setRegistState(0);
                    Integer update = registPersonDao.update(registPerson);
                    if (update < 1) {
                        throw new Exception("修改入住人信息失败！");
                    }
                }
            }


            /**
             * 修改房间状态  --修改成住净
             */
            roomInfo.setRoomNumState(3);


            Integer edit = roomInfoDao.editRoomInfo(roomInfo);

            if (edit < 1) {
                throw new Exception("修改房间信息失败！");
            }

            //设置成入住状态
            regist.setState(0);
            Integer update = registDao.update(regist);

            if (update < 1) {
                throw new Exception("修改主单信息失败！");
            }
            //处理辅助房态
            String roomAuxiliaryRecord = regist.getRoomAuxiliaryRecord() == null ? "" : regist.getRoomAuxiliaryRecord();
            String[] split1 = roomAuxiliaryRecord.split(",");
            if (split1 != null && !roomAuxiliaryRecord.equals("") && split1.length > 0) {
                for (int i = 0; i < split1.length; i++) {
                    String roomAuxiliaryId = split1[i];
                    RoomAuxiliaryRelation roomAuxiliaryRelation = new RoomAuxiliaryRelation();
                    roomAuxiliaryRelation.setHid(regist.getHid());
                    roomAuxiliaryRelation.setHotelGroupId(regist.getHotelGroupId());
                    roomAuxiliaryRelation.setSort(0);
                    roomAuxiliaryRelation.setRegistId(regist.getRegistId());
                    roomAuxiliaryRelation.setRoomNum(regist.getRoomNum());
                    roomAuxiliaryRelation.setRoomId(regist.getRoomNumId());
                    roomAuxiliaryRelation.setRoomAuxiliaryId(Integer.parseInt(roomAuxiliaryId));

                    Integer integer = roomAuxiliaryRelationDao.saveRoomAuxiliaryRelation(roomAuxiliaryRelation);

                    if (integer < 1) {
                        throw new Exception("添加辅助房态失败！");
                    }
                }
            }

            if (registGroup != null && registGroup.getState() == 2) {
                registGroup.setState(1);

                Integer integer = registGroupDao.update(registGroup);

                if (integer < 1) {
                    throw new Exception("修改团队结账状态失败！");
                }
            }

            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setRegistId(registId);
            accountSearch.setIsCancel(0);
            accountSearch.setRegistState(1);

            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            if (accounts.size() > 0) {
                accountDao.editAccountList(accounts);
            }


            Integer checkinType = regist.getCheckinType();


            // 钟点房验证
            if (checkinType == 2) {

                // 开始日期结束日期是否为同一天
                Boolean starEndOneDay = false;

                // 开始小时
                int inHours = regist.getCheckinTime().getHours();

                // 结束小时
                int outTours = regist.getCheckoutTime().getHours();

                HashMap<Integer, Boolean> hourStarMap = new HashMap<>();

                HashMap<Integer, Boolean> hourEndMap = new HashMap<>();

                String useStarHourStr = "";
                String useEndHourStr = "";

                // 开始时间
                Integer businessDayMin = HotelUtils.parseDate2Int(regist.getCheckinTime());

                // 结束时间
                Integer businessDayMax = HotelUtils.parseDate2Int(regist.getCheckoutTime());

                //  需要添加的钟点房使用
                ArrayList<HourRoomDayUse> addHourUse = new ArrayList<>();

                // 需要删除的钟点房使用详情
                ArrayList<HourRoomDayUse> upaHourUse = new ArrayList<>();
                // 钟点房验证
                HourRoomDayUseSearch hourRoomDayUseSearch = new HourRoomDayUseSearch();

                hourRoomDayUseSearch.setHid(user.getHid());
                hourRoomDayUseSearch.setBusinessDayMax(businessDayMax);
                hourRoomDayUseSearch.setBusinessDayMin(businessDayMin);

                // 每天使用情况
                Page<HourRoomDayUse> hourRoomDayUses = hourRoomDayUseDao.selectBySearch(hourRoomDayUseSearch);

                Map<Integer, List<HourRoomDayUse>> hourRoomMap = new HashMap<>();

                if (hourRoomDayUses.size() > 0) {

                    hourRoomMap = hourRoomDayUses.stream().collect(Collectors.groupingBy(HourRoomDayUse::getRoomInfoId));

                }


                // 如果不是同一天，则把第二天日期也计算出来。
                if (!businessDayMin.equals(businessDayMax)) {
                    starEndOneDay = true;

                    for (int i = 0; i <= outTours; i++) {
                        hourEndMap.put(i, true);
                        useEndHourStr += i + ",";
                    }
                    outTours = 23;
                    useEndHourStr = useEndHourStr.substring(0, useEndHourStr.length() - 1);
                }

                for (int i = inHours; i <= outTours; i++) {
                    hourStarMap.put(i, true);
                    useStarHourStr += i + ",";
                }
                useStarHourStr = useStarHourStr.substring(0, useStarHourStr.length() - 1);

                // 验证当前时段是否被租用
                List<HourRoomDayUse> roomHourUse = hourRoomMap.get(roomInfo.getRoomInfoId());

                // 说明当前时间没有
                if (roomHourUse == null || roomHourUse.size() < 1) {

                    HourRoomDayUse hourRoomDayUse = getHourRoomDayUse(regist);
                    hourRoomDayUse.setBusinessDay(businessDayMin);
                    hourRoomDayUse.setUseMsg(useStarHourStr);

                    addHourUse.add(hourRoomDayUse);

                    // 如果是隔一天
                    if (starEndOneDay) {

                        HourRoomDayUse hourRoomDayUseEnd = getHourRoomDayUse(regist);
                        hourRoomDayUseEnd.setBusinessDay(businessDayMax);
                        hourRoomDayUseEnd.setUseMsg(useEndHourStr);

                        addHourUse.add(hourRoomDayUseEnd);

                    }

                } else {

                    // 验证当天是否存在
                    Map<Integer, HourRoomDayUse> hourRoomDayUseMap = roomHourUse.stream().collect(Collectors.toMap(HourRoomDayUse::getBusinessDay, a -> a, (k1, k2) -> k1));

                    HourRoomDayUse hourRoomDayUse = hourRoomDayUseMap.get(businessDayMin);
                    if (hourRoomDayUse == null) {

                        hourRoomDayUse = getHourRoomDayUse(regist);
                        hourRoomDayUse.setBusinessDay(businessDayMin);
                        hourRoomDayUse.setUseMsg(useStarHourStr);

                        addHourUse.add(hourRoomDayUse);

                    } else {


                        String[] split = hourRoomDayUse.getUseMsg().split(",");
                        Arrays.sort(split);

                        Boolean noRoom = false;

                        String errMsg = "";

                        for (int ion = 0; ion < split.length; ion++) {

                            int i1 = Integer.parseInt(split[ion]);

                            Boolean aBoolean = hourStarMap.get(i1);
                            if (aBoolean != null && aBoolean) {
                                noRoom = true;
                                errMsg += split[ion];
                                errMsg += ",";
                            }
                        }

                        if (noRoom) {
                            // throw new Exception(regist.getRoomNum() + businessDayMin + "：" + errMsg + "时段不可以");
                        }

                        String newUseMsg = hourRoomDayUse.getUseMsg() + "," + useStarHourStr;
                        hourRoomDayUse.setUseMsg(newUseMsg);

                        upaHourUse.add(hourRoomDayUse);

                    }


                    // 如果是隔一天
                    if (starEndOneDay) {

                        HourRoomDayUse hourRoomDayUseEnd = hourRoomDayUseMap.get(businessDayMax);

                        if (hourRoomDayUseEnd == null) {
                            hourRoomDayUseEnd = getHourRoomDayUse(regist);
                            hourRoomDayUseEnd.setBusinessDay(businessDayMax);
                            hourRoomDayUseEnd.setUseMsg(useEndHourStr);

                            addHourUse.add(hourRoomDayUseEnd);
                        } else {

                            String[] split = hourRoomDayUseEnd.getUseMsg().split(",");
                            Arrays.sort(split);

                            Boolean noRoom = false;

                            String errMsg = "";

                            for (int ion = 0; ion < split.length; ion++) {

                                int i1 = Integer.parseInt(split[ion]);

                                Boolean aBoolean = hourStarMap.get(i1);
                                if (aBoolean != null && aBoolean) {
                                    noRoom = true;
                                    errMsg += split[ion];
                                    errMsg += ",";
                                }
                            }

                            if (noRoom) {
                                //  throw new Exception(regist.getRoomNum() + businessDayMin + "：" + errMsg + "时段被预订");
                            }

                            String newUseMsg = hourRoomDayUseEnd.getUseMsg() + "," + useEndHourStr;
                            hourRoomDayUseEnd.setUseMsg(newUseMsg);

                            upaHourUse.add(hourRoomDayUse);


                        }

                    }
                }

                if (addHourUse.size() > 0) {

                    for (HourRoomDayUse hourRoomDayUse : addHourUse) {

                        if (hourRoomDayUse.getRoomInfoId() == null || hourRoomDayUse.getRoomNo() == null) {
                            continue;
                        }
                        hourRoomDayUseDao.insert(hourRoomDayUse);

                    }

                }

                if (upaHourUse.size() > 0) {
                    for (HourRoomDayUse hourRoomDayUse : upaHourUse) {
                        String useMsg = hourRoomDayUse.getUseMsg();
                        if (useMsg.length() < 1) {
                            hourRoomDayUseDao.delete(hourRoomDayUse.getId());
                        } else {
                            hourRoomDayUseDao.update(hourRoomDayUse);
                        }
                    }
                }

            }


            // 更新房间缓存
            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    try {
                        turnAlwaysService.updateRoomListForCache(user);
                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
                        turnAlwaysService.turnAlwaysCacheFunc(user);
                        HashMap<String, String> filedMap = new HashMap<>();
                        filedMap.put("roomList", regist.getRoomNum());
                        baseService.push(user.getHotelGroupId(), user.getHid(), 3, filedMap, new HashMap<String, String>(), true, true);
                    } catch (Exception e) {
                        log.error("",e);
                    }

                }
            });

            //将入住单登记状态改为已经
            OprecordInfoRequest oprecordInfoRequest = new OprecordInfoRequest();
            oprecordInfoRequest.setSessionToken(sessionToken);
            oprecordInfoRequest.setRegistId(regist.getRegistId());
            oprecordInfoRequest.setRoomNum(regist.getRoomNum());
            oprecordInfoRequest.setOprecordTemplateId(5);
            JSONObject oprecordJson = new JSONObject();
            oprecordJson.put("roomNo", regist.getRoomNum());
            oprecordJson.put("time", HotelUtils.currentTime());
            oprecordInfoRequest.setBusinessId2(oprecordJson.toString());
            oprecordInfoRequests.add(oprecordInfoRequest);
            //处理操作日志
            this.baseService.addOprecordReqs(oprecordInfoRequests);
            //todo 重新入住

            if(registPersonList.size()>0){
                /**
                 * 数据推送iot酒店
                 */
                IotHotelCheckIn iotHotelCheckIn = new IotHotelCheckIn();
                iotHotelCheckIn.setCheckOutTime(HotelUtils.parseDate2Str(regist.getCheckoutTime()));
                iotHotelCheckIn.setCode(regist.getRegistId() + "");
                iotHotelCheckIn.setPmsRoomId(regist.getRoomNumId() + "");
                iotHotelCheckIn.setGuests(registPersonList.stream().map(
                        people -> {
                            IotHotelGuest hotelGuest = new IotHotelGuest();
                            hotelGuest.setIdentityCardNo(people.getIdCode());
                            hotelGuest.setName(people.getPersonName());
                            hotelGuest.setPhone(people.getPhone());
                            return hotelGuest;
                        }
                ).collect(Collectors.toList()));

                HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
                Map<String, String> authParamMap = new HashMap<>();
                authParamMap.put("eid", user.getHid().toString());
                authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
                authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
                authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
                try {
                    authParamMap.put("phone", AESUtil.encryptAesCbc(user.getPhone(),iotPlatConfig.getSassTokenSecret()));
                } catch (Exception e) {
                    log.error("手机号加密失败");
                }
                hotelIotStrategy.initStrategy(authParamMap);
                webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.CHECK_IN,
                        iotHotelCheckIn,
                        hotelIotStrategy);
            }



        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData updateRegistResource(RegistParam registParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = registParam.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if (registParam.getRegistId() == null) {
                throw new Exception("登记单ID不能为空");
            }
            Integer registId = registParam.getRegistId();
            Regist regist = registDao.selectById(registId);
            if (regist == null) {
                throw new Exception("查询不到登记单");
            }
            if (regist.getState() != 0) {
                throw new Exception("当前订单状态不允许修改");
            }
            Integer resourceId = registParam.getResourceId();
            if (resourceId == 2) {
                regist.setMemberCard(registParam.getMemberCard());
                regist.setMemberId(registParam.getMemberId());
                if (regist.getCompanyAccountId() != null) {
                    regist.setCompanyAccountId(null);
                    regist.setCompayName("");
                    regist.setCompanyId(null);
                }
            } else if (resourceId >= 3) {
                regist.setCompanyAccountId(registParam.getCompanyAccountId());
                regist.setCompanyId(registParam.getCompanyId());
                regist.setCompayName(registParam.getCompayName());
                if (regist.getMemberId() != null) {
                    regist.setMemberId(null);
                    regist.setMemberCard("");
                }
            } else if (resourceId == 1) {
                if (regist.getCompanyAccountId() != null) {
                    regist.setCompanyAccountId(null);
                    regist.setCompayName("");
                    regist.setCompanyId(null);
                }
                if (regist.getMemberId() != null) {
                    regist.setMemberId(null);
                    regist.setMemberCard("");
                }
            }
            regist.setResourceId(registParam.getResourceId());
            regist.setResourceName(registParam.getResourceName());
            if (regist.getCheckinType() != 2) {
                regist.setCheckoutTime(registParam.getCheckoutTime());
            }
            regist.setRoomRateCodeId(registParam.getRoomRateCodeId());
            regist.setRoomRateCodeName(registParam.getRoomRateCodeName());
            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setRegistId(registId);
            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);
            if (bookingOrderConfigs.size() == 1) {
                BookingOrderConfig bookingOrderConfig = bookingOrderConfigs.get(0);
                bookingOrderConfig.setAutoAr(registParam.getAutoAr() ? 1 : 0);
                bookingOrderConfig.setNoPrice(registParam.getNoPrice() ? 1 : 0);
                bookingOrderConfig.setNoDeposit(registParam.getNoDposit() ? 1 : 0);

                Integer integer = bookingOrderConfigDao.editBookingOrderConfig(bookingOrderConfig);
                if (integer < 1) {
                    throw new Exception("修改配置信息失败");
                }
            }
            Integer update = registDao.update(regist);
            if (update < 1) {
                throw new Exception("修改订单信息失败");
            }
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        baseService.push(user.getHotelGroupId(), user.getHid(), 7, null, new HashMap<String, String>(), true, true);
                    } catch (Exception e) {
                        log.error("",e);
                    }

                }
            });
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData cancelCheckout(RegistParam registParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = registParam.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if (registParam.getRegistId() == null) {
                throw new Exception("登记单ID不能为空");
            }
            Integer registId = registParam.getRegistId();
            Regist regist = registDao.selectById(registId);
            if (regist == null) {
                throw new Exception("查询不到登记单");
            }
            //设置状态为挂帐
            regist.setState(2);

            //修改账务状态，为 未结
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setRegistId(registId);
            accountSearch.setIsCancel(0);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);


            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(registId);

            List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);


            for (int i = 0; i < accounts.size(); i++) {
                Account account = accounts.get(i);
                account.setRegistState(0);
                Integer integer = accountDao.editAccount(account);
                if (integer < 1) {
                    throw new Exception("修改账务信息失败");
                }
            }

            for (int i = 0; i < registPersonList.size(); i++) {
                RegistPerson registPerson = registPersonList.get(i);
                registPerson.setRegistState(2);

                Integer update = registPersonDao.update(registPerson);
                if (update < 1) {
                    throw new Exception("修改宾客信息状态失败");
                }
            }

            Integer update = registDao.update(regist);

            if (update < 1) {
                throw new Exception("修改住单信息失败");
            }

            Integer registGroupId = regist.getRegistGroupId();
            if (registGroupId != null && registGroupId > 1) {

                RegistGroup registGroup = new RegistGroup();

                registGroup.setRegistGroupId(registGroupId);
                registGroup.setState(1);

                registGroupDao.update(registGroup);

            }

        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData cancelCheckin(RegistParam registParam) {
        return null;

        //1-创建一个表接口，与 regist 一模一样

        //2-删除价格信息 del

        //3-regist

        //4-
    }

    @Override
    public ResponseData updateCheckinType(RegistParam registParam) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = registParam.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            if (registParam.getRegistId() == null) {
                throw new Exception("登记单ID不能为空");
            }
            Integer registId = registParam.getRegistId();
            Regist regist = registDao.selectById(registId);
            if (regist == null) {
                throw new Exception("查询不到登记单");
            }

            if (registParam.getCheckinType() == null) {
                throw new Exception("将要修改的入住类型不能空");
            }


            Integer checkinType = registParam.getCheckinType();

            //查询每日房价
            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setRegistId(registId);
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);
            //自用房或者免费房
            if (checkinType == 4 || checkinType == 5) {
                for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
                    BookingOrderDailyPrice bookingOrderDailyPrice = bookingOrderDailyPrices.get(i);
                    if (bookingOrderDailyPrice.getDailyState() == 1) {
                        bookingOrderDailyPrice.setPrice(0);
                    }
                }
            } else {
                //根据房价码 查询 之前的每日房价

//                RoomDayPriceSearch roomDayPriceSearch = new RoomDayPriceSearch();
//                roomDayPriceSearch.setRoomTypeId(regist.getRoomTypeId());
//                roomDayPriceSearch.setRoomRateId(regist.getRoomRateCodeId());
//                roomDayPriceSearch.set
            }
            regist.setCheckinType(registParam.getCheckinType());

            this.executeUpdateCheckinType(regist, bookingOrderDailyPrices);
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getHotelDataInfo(GetHotelDataInfoParam param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Integer dataType = param.getDataType();
            if (dataType == 1) {
                List<ResourceView> hotelDataInfo = pmsMainDao.getHotelDataResource(param);
                for (ResourceView view : hotelDataInfo) {
                    if (view.getId() == 1) {
                        view.setItem("散客");
                    } else if (view.getId() == 2) {
                        view.setItem("会员");
                    } else if (view.getId() == 3) {
                        view.setItem("协议单位");
                    } else if (view.getId() == 4) {
                        view.setItem("旅行社");
                    } else if (view.getId() == 5) {
                        view.setItem("订房平台");
                    }
                }
                responseData.setData(hotelDataInfo);
                return responseData;
            } else if (dataType == 2) {
                List<ResourceView> hotelDataInfo = pmsMainDao.getHotelDataRoomType(param);
//                for (ResourceView view : hotelDataInfo) {
//                    if (view.getId() == 1) {
//                        view.setItem("散客");
//                    } else if (view.getId() == 2) {
//                        view.setItem("会员");
//                    } else if (view.getId() == 3) {
//                        view.setItem("协议单位");
//                    } else if (view.getId() == 4) {
//                        view.setItem("旅行社");
//                    } else if (view.getId() == 5) {
//                        view.setItem("订房平台");
//                    }
//                }
                responseData.setData(hotelDataInfo);
                return responseData;
            } else if (dataType == 3) {
                List<ResourceView> hotelDataInfo = pmsMainDao.getHotelDataCompayName(param);
                List<ResourceView> resourceViewList = new ArrayList<>();
                for (ResourceView view : hotelDataInfo) {
                    if (view.getId() != 0) {
                        resourceViewList.add(view);
                    }
                }
                responseData.setData(resourceViewList);
                return responseData;
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Override
    public ResponseData getHotelRoomTypeRegister(GetHotelDataInfoParam param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            Integer dataType = param.getDataType();
            String sessionToken = param.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            param.setHid(user.getHid());
            if (dataType == 1) {

                Integer classId = param.getClassId();
                param.setClassId(null);


                List<ResourceView> hotelDataInfo = pmsMainDao.getHotelRoomTypeRegister(param);
                //当日开房数
                Integer totalUseCount = 0;
                //当前班次开房数
                Integer dayUseCount = 0;

                JSONArray roomTypeInfoList = new JSONArray();

                for (ResourceView view : hotelDataInfo) {
                    totalUseCount += view.getCount();
                    if (view.getClassId().equals(classId)) {
                        dayUseCount += view.getCount();
                    }
                    JSONObject roomType = new JSONObject();
                    roomType.put("x", view.getItem());
                    roomType.put("y", view.getCount());
                    roomTypeInfoList.add(roomType);
                }
                JSONObject res = new JSONObject();
                res.put("userList", roomTypeInfoList);
                res.put("totalUseCount", totalUseCount);
                res.put("dayUseCount", dayUseCount);
                responseData.setData(res);
                return responseData;
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @Transactional(rollbackFor = Exception.class)
    public void executeUpdateCheckinType(Regist
                                                 regist, ArrayList<BookingOrderDailyPrice> bookingOrderDailyPrices) {
        try {
            Integer update = registDao.update(regist);
            if (update < 1) {
                throw new Exception("修改登记单失败");
            }

            for (int i = 0; i < bookingOrderDailyPrices.size(); i++) {
                Integer integer = bookingOrderDailyPriceDao.editBookingOrderDailyPrice(bookingOrderDailyPrices.get(i));

                if (integer < 1) {
                    throw new Exception("修改价格信息失败");
                }
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
        }

    }


    /**
     * 添加入住人
     *
     * @param guestList
     * @param user
     * @param regist
     * @throws Exception
     */
    public void addCheckinGuest(JSONArray guestList, TbUserSession user, Regist
            regist, List<Oprecord> oprecords, Oprecord oprecord) throws Exception {
        /**
         * 3.添加入住信息
         */
        List<RegistPerson> registPeople = addCheckinGuest(guestList, user, regist);
        for (RegistPerson person : registPeople) {
            Integer personValue = registPersonDao.insert(person);
            if (personValue < 1) {
                throw new Exception("添加入住人失败。" + person.getPersonName());
            }
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription("添加入住人:" + person.getPersonName());
            oprecords.add(oprecord);
        }
    }

    public List<RegistPerson> addCheckinGuest(JSONArray guestList, TbUserSession user, Regist regist) throws
            Exception {
        /**
         * 3.添加入住信息
         */
        ArrayList<RegistPerson> registPeople = new ArrayList<>();
        JSONObject imageData = new JSONObject();
        imageData.put(ER.SESSION_TOKEN, user.getSessionId());
        for (int i = 0; i < guestList.size(); i++) {

            JSONObject guest = guestList.getJSONObject(i);
            String code = guest.getString("idCode");

            RegistPerson person = new RegistPerson();

            person.setRegistState(0);
            person.setIdCode(guest.getString("idCode"));
            // 省市县获取
            AddressParam addressParam = new AddressParam(code);
            person.setCity(addressParam.getCity());
            person.setArea(addressParam.getArea());
            person.setProvice(addressParam.getProvice());
            if (guest.get("birthday") != null && !"".equals(guest.getString("birthday")) && !"null".equals(guest.getString("birthday"))) {
                int birthday = Integer.parseInt(guest.getString("birthday").replace("-", ""));
                person.setBirthday(birthday);
                person.setBirthYear(Integer.parseInt(String.valueOf(birthday).substring(0, 4)));
            } else {
                String birthday = code.length() >= 15 ? code.substring(6, 14) : "";
                String year = code.length() >= 15 ? code.substring(6, 10) : "";
                if (!birthday.equals("")) {
                    person.setBirthday(Integer.parseInt(birthday));
                }
                if (!year.equals("")) {
                    person.setBirthYear(Integer.parseInt(year));
                }

            }
            person.setRegistId(regist.getRegistId());
            person.setIdType(1);
            person.setSex(0);

            if (guest.get("sex") != null) {
                person.setSex(guest.getInt("sex"));
            }
            person.setPersonName(guest.getString("personName"));
            person.setIsOther(i == 0 ? 0 : 1);
            person.setHid(user.getHid());
            person.setHotelGroupId(user.getHotelGroupId());
            person.setRoomNum(regist.getRoomNum());
            person.setRoomNumId(regist.getRoomNumId());
            person.setTeamCodeId(regist.getTeamCodeId());

            if (guest.get("address") != null) {
                person.setAddress(guest.getString("address"));
            }

            if (guest.get("phone") != null) {
                person.setPhone(guest.getString("phone"));
            }

            if (guest.get("nation") != null) {
                String nation = guest.getString("nation");
                if (nation.indexOf("族") < 1) {
                    nation += "族";
                }
                person.setNation(HotelUtils.nationMap.getInt(nation));
            }

            /**
             * 身份证照  新版本
             */
            if (guest.get("image") != null && !"".equals(guest.getString("image"))) {
                String replace = guest.getString("image").replace(' ', '+');
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(replace, 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(code, "UTF-8") + ".jpeg");
                person.setIdImage(uploadObjectRsp.getFileName());
                JSONObject detect = FaceSetUtils.detect(replace, 0);

                Object faces = detect.get("faces");
                if (faces != null) {

                    JSONArray fa = JSONArray.fromObject(faces);
                    JSONObject face = fa.getJSONObject(0);
                    person.setIdImageFaceToken(face.getString("face_token"));

                }

            }

            /**
             * 证件照,新版本
             */
            if (guest.get("cameraPicture") != null && !"".equals(guest.getString("cameraPicture"))) {
                String s = code + regist.getHid() + regist.getRoomNumId() + HotelUtils.currentTime();
                String replace = guest.getString("cameraPicture").replace(' ', '+');
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(replace, 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                person.setCameraImage(uploadObjectRsp.getFileName());

                JSONObject detect = FaceSetUtils.detect(replace, 0);
                Object faces = detect.get("faces");
                if (faces != null) {

                    JSONArray fa = JSONArray.fromObject(faces);
                    JSONObject face = fa.getJSONObject(0);
                    person.setCameraImageFaceToken(face.getString("face_token"));

                }
            }

            /**
             * 证件照,老版本 Image
             */
            if (guest.get("photo") != null && !"".equals(guest.getString("photo"))) {
                String photo = guest.getString("photo").replace(' ', '+');
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(photo, 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(code, "UTF-8") + ".jpeg");
                person.setIdImage(uploadObjectRsp.getFileName());
            }


            /**
             * 身份证照  老版本
             */
            if (guest.get("cameraPhoto") != null && !"".equals(guest.getString("cameraPhoto"))) {
                String s = code + regist.getHid() + regist.getRoomNumId() + HotelUtils.currentTime();
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(guest.getString("cameraPhoto").replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                person.setCameraImage(uploadObjectRsp.getFileName());
            }

            /**
             * 相似度   0~1 之间  Similarity，相似度
             */
            if (guest.get("semblance") != null && !"".equals(guest.getString("semblance"))) {
                person.setConfidence(guest.getString("semblance"));
            }

            /**
             * 成功或失败  0-失败 1-成功
             */
            if (guest.get("faceResult") != null && !"".equals(guest.getString("faceResult"))) {
                try {
                    person.setFaceResult(guest.getInt("faceResult"));
                } catch (Exception ex) {
                    person.setFaceResult(0);
                }
            }

            /**
             * 公安网上传状态
             */
            if (guest.get("guestStatus") != null && !"".equals(guest.getString("guestStatus"))) {
                try {
                    person.setGuestType(guest.getInt("guestStatus"));
                } catch (Exception ex) {
                    person.setFaceResult(0);
                }
            }

            /**
             * 公安网流水号
             */
            if (guest.get("guestNo") != null && !"".equals(guest.getString("guestNo"))) {
                person.setGuestNo(guest.getString("guestNo"));
            }

            /**
             * 公安网唯一标识
             */
            if (guest.get("guestId") != null && !"".equals(guest.getString("guestId"))) {
                person.setGuestId(guest.getString("guestId"));
            }

            registPeople.add(person);

        }

        /**
         * 这里创建子线程处理 客史的问题
         */
        HotelUtils.cachedThreadPool.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    createPersonInfo(registPeople);
                } catch (Exception e) {
                    log.error("",e);
                }
            }
        });
        return registPeople;
    }

    @Override
    public ResponseData getHotelRegisterNum(GetHotelDataInfoParam param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = param.getSessionToken();
            TbUserSession user = this.getTbUserSession(sessionToken);
            RegistSearch registSearch = new RegistSearch();
            registSearch.setBusinessDay(param.getBusinessDay());
            registSearch.setHid(user.getHid());
            List<Regist> regists = registDao.selectBySearch(registSearch);

            JSONObject res = new JSONObject();

            int sanke = 0;
            int yuding = 0;


            for (Regist re : regists) {
                Integer bookingOrderId = re.getBookingOrderId();
                if (bookingOrderId != null && bookingOrderId > 0) {
                    yuding++;
                } else {
                    sanke++;
                }
            }


            registSearch = new RegistSearch();
            registSearch.setCheckoutBusinessDay(param.getBusinessDay());
            registSearch.setHid(user.getHid());
            regists = registDao.selectBySearch(registSearch);

            res.put("sankecheckin", sanke);
            res.put("yudingcheckin", yuding);
            res.put("checkout", regists.size());
            responseData.setData(res);


        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData withRoom(WithRoomRequest withRoomRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = withRoomRequest.getSessionToken();
            TbUserSession user = this.getTbUserSession(sessionToken);
            if (user == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }

            //根据传过来的registId 查询当前房间是否联过房
            List<Integer> registIds = withRoomRequest.getRegistIds();
            if (registIds == null || registIds.size() < 1) {
                throw new Exception("操作失败");
            }

            // 1. 根据传递房间找出所选 联房信息
            HashMap<Integer, Boolean> groupIdMap = new HashMap<>();
            ArrayList<RoomAuxiliaryRelation> addAuxRoom = new ArrayList<>();
            List<Regist> registList = new ArrayList<>();
            for (int i = 0; i < registIds.size(); i++) {
                Integer registId = registIds.get(i);
                Regist regist = registDao.selectById(registId);
                Integer teamCodeId = regist.getTeamCodeId();
                if (teamCodeId > 0) {
                    groupIdMap.put(teamCodeId, true);
                    RegistSearch registSearch = new RegistSearch();
                    registSearch.setHid(user.getHid());
                    registSearch.setTeamCodeId(teamCodeId);
                    List<Regist> regists = registDao.selectBySearch(registSearch);
                    registList.addAll(regists);
                } else {
                    registList.add(regist);
                }
            }
            //去重复
            registList = registList.stream().distinct().collect(Collectors.toList());
            String regIdStr = "";

            //查询之前是否有联房信息的主账房
            Regist mainRegist = null;
            Boolean isRegMain = true;

            for (int i = 0; i < registList.size(); i++) {
                regIdStr += registList.get(i).getRegistId() + ",";
                RoomAuxiliaryRelation rar = new RoomAuxiliaryRelation();
                rar.setHid(user.getHid());
                rar.setHotelGroupId(user.getHotelGroupId());
                rar.setRoomAuxiliaryId(ROOM_AUXILIARY.JOINT_HOUSING);
                rar.setRoomNum(registList.get(i).getRoomNum());
                rar.setRoomId(registList.get(i).getRoomNumId());
                rar.setRegistId(registList.get(i).getRegistId());
                rar.setBookingOrderId(registList.get(i).getBookingOrderId());
                addAuxRoom.add(rar);
                Integer isMainRoom = registList.get(i).getIsMainRoom();
                if (isMainRoom == 1) {
                    mainRegist = registList.get(i);
                    isRegMain = false;
                }
            }
            if (isRegMain) {
                mainRegist = registList.get(0);
            }
            regIdStr += "-99";
            //需要撤销的联房集合
            Set<Integer> groupIds = groupIdMap.keySet();
            List<RegistGroup> groupList = new ArrayList<>();
            String groupIdStr = "";

            for (Integer groupId : groupIds) {
                RegistGroup registGroup = new RegistGroup();
                registGroup.setRegistGroupId(groupId);
                registGroup.setState(2);
                groupList.add(registGroup);
                groupIdStr += groupId + "";
            }

            // 查询账务信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setRegistIds(regIdStr);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            //入住人信息
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setHid(user.getHid());
            registPersonSearch.setRegInKeys(regIdStr);
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);

            // 新的联房信息
            RegistGroup registGroup = new RegistGroup();
            Date date = new Date();
            registGroup.setSn(OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.GROUP, stringRedisTemplate));
            registGroup.setGroupType(9);
            registGroup.setHid(user.getHid());
            registGroup.setHotelGroupId(user.getHotelGroupId());
            registGroup.setGroupName(registGroup.getSn());
            registGroup.setRemark("联房合并:" + groupIdStr);
            registGroup.setCreateTime(date);
            registGroup.setBusinessDay(user.getBusinessDay());
            registGroup.setState(1);
            registGroup.setCompanyId(registList.get(0).getCompanyId());
            registGroup.setCompayName(registList.get(0).getCompayName());
            registGroup.setRoomRateCodeId(registList.get(0).getRoomRateCodeId());
            registGroup.setRoomRateCodeName(registList.get(0).getRoomRateCodeName());
            registGroup.setMemberId(registList.get(0).getMemberId());
            registGroup.setMemberCard(registList.get(0).getMemberCard());
            Integer integer = checkInTransactionService.withRoom(registGroup, mainRegist, groupList, registList, accounts, addAuxRoom, registPeople);

            //清空房间缓存
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        baseService.push(user.getHotelGroupId(), user.getHid(), 9, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
                    } catch (Exception e) {

                    }

                }
            });

//            ResponseData responseData = ResponseData.newSuccessData();
//            try {
//                String sessionToken = param.getString(ER.SESSION_TOKEN);
//                final TbUserSession user = this.getTbUserSession(sessionToken);
//
//                //日志记录
//                ArrayList<OprecordInfoRequest> oprecordInfoRequests = new ArrayList<>();
//                OprecordInfoRequest oprecordInfoRequest = new OprecordInfoRequest();
//                oprecordInfoRequest.setSessionToken(sessionToken);
//
//
//                JSONArray registIds = JSONArray.fromObject(URLDecoder.decode(param.getString("registIds"), "utf-8"));
//
//                // 1. 根据传递房间找出所选 联房信息
//                HashMap<Integer, Boolean> groupIdMap = new HashMap<>();
//                // 当前团队下所有的房间信息
//
//                // 修改的登记单信息
//                ArrayList<Regist> regists = new ArrayList<>();
//
//                ArrayList<RoomAuxiliaryRelation> addAuxRoom = new ArrayList<>();
//
//
//                Regist mainRes = new Regist();
//
//                // 撤销的团队信息
//                ArrayList<RegistGroup> registGroups = new ArrayList<>();
//
//                for (int i = 0; i < registIds.size(); i++) {
//                    JSONObject reg = registIds.getJSONObject(i);
//                    Regist regist = registDao.selectById(reg.getInt("registId"));
//                    if (i == registIds.size() - 1) {
//                        oprecordInfoRequest.setRegistId(regist.getRegistId());
//                        oprecordInfoRequest.setRoomNum(regist.getRoomNum());
//                    }
//                    int teamCodeId = regist.getTeamCodeId();
//                    if (teamCodeId > 0) {
//                        groupIdMap.put(teamCodeId, true);
//                    } else {
//                        int registId = reg.getInt("registId");
//                        regists.add(regist);
//                        RoomAuxiliaryRelation rar = new RoomAuxiliaryRelation();
//                        rar.setHid(user.getHid());
//                        rar.setHotelGroupId(user.getHotelGroupId());
//                        rar.setRoomAuxiliaryId(ROOM_AUXILIARY.JOINT_HOUSING);
//                        rar.setRoomNum(regist.getRoomNum());
//                        rar.setRoomId(regist.getRoomNumId());
//                        rar.setRegistId(registId);
//                        rar.setBookingOrderId(regist.getBookingOrderId());
//                        addAuxRoom.add(rar);
//                    }
//                }
//
//                // 2.创建新的联房信息
//                String groupIdStr = "";
//                Set<Integer> groupIds = groupIdMap.keySet();
//
//
//                // 所有账务信息
//
//                RegistSearch registSearch = new RegistSearch();
//                registSearch.setHid(user.getHid());
//
//                String teamCodeIds = "";
//
//                String teamCodeStrs = "";
//
//                Boolean ast = false;
//
//                for (Integer grpId : groupIds) {
//
//                    groupIdStr += grpId + "";
//
//                    RegistGroup registGroup = registGroupDao.selectById(grpId);
//
//                    registGroups.add(registGroup);
//
//                    teamCodeIds += grpId + ",";
//
//                    ast = true;
//
//                }
//
//                registSearch.setTeamCodeIdStr(teamCodeIds);
//
//                List<Regist> regists1 = new ArrayList<>();
//                if (ast) {
//                    regists1 = registDao.selectBySearch(registSearch);
//                }
//
//                regists.addAll(regists1);
//
//                // 添加的团队信息
//                RegistGroup registGroup = new RegistGroup();
//
//                Date date = new Date();
//
//                registGroup.setSn(OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.GROUP, stringRedisTemplate));
//                registGroup.setGroupType(9);
//                registGroup.setHid(user.getHid());
//                registGroup.setHotelGroupId(user.getHotelGroupId());
//                registGroup.setGroupName(registGroup.getSn());
//                registGroup.setRemark("联房合并:" + groupIdStr);
//                registGroup.setCreateTime(date);
//                registGroup.setBusinessDay(user.getBusinessDay());
//                registGroup.setState(1);
//
//                if (registGroups.size() > 0) {
//                    RegistGroup rg1 = registGroups.get(0);
//
//                    registGroup.setProvince(rg1.getProvince());
//                    registGroup.setCity(rg1.getCity());
//                    registGroup.setCompanyId(rg1.getCompanyId());
//                    registGroup.setCompayName(rg1.getCompayName());
//                    registGroup.setRoomRateCodeId(rg1.getRoomRateCodeId());
//                    registGroup.setRoomRateCodeName(rg1.getRoomRateCodeName());
//                    registGroup.setMemberId(rg1.getMemberId());
//                    registGroup.setMemberCard(rg1.getMemberCard());
//
//                }
//
//                Integer sumPay = 0;
//                Integer sumSale = 0;
//
//                String regIdStr = "";
//
//                Boolean isRegMain = true;
//
//                for (Regist regist : regists) {
//
//                    regIdStr += regist.getRegistId() + ",";
//
//                    sumPay += regist.getSumPay();
//                    sumSale += regist.getSumSale();
//
//                    Integer isMainRoom = regist.getIsMainRoom();
//
//                    if (isMainRoom == 1) {
//                        mainRes = regist;
//                        isRegMain = false;
//                    }
//
//                    teamCodeStrs += regist.getRoomNum() + ",";
//                }
//
//                if (isRegMain) {
//                    mainRes = regists.get(0);
//                }
//
//                regIdStr += "-99";
//
//                registGroup.setSumPay(sumPay);
//                registGroup.setSumSales(sumSale);
//                registGroup.setSumRooms(regists.size());
//
//
//                // 查询账务信息
//                AccountSearch accountSearch = new AccountSearch();
//                accountSearch.setHid(user.getHid());
//                accountSearch.setRegistIds(regIdStr);
//
//                // 修改的账务信息
//                List<Account> accounts = accountDao.selectBySearch(accountSearch);
//
//                RegistPersonSearch registPersonSearch = new RegistPersonSearch();
//                registPersonSearch.setHid(user.getHid());
//                registPersonSearch.setRegInKeys(regIdStr);
//
//                List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
//
//                Integer integer = checkInTransactionService.roomJion(registGroup, mainRes, registGroups, regists, accounts, addAuxRoom, registPeople);
//
////            Oprecord oprecord = new Oprecord(user);
////
////            oprecord.setDescription("将:" + teamCodeStrs + "进行联房处理");
//
//                //清空房间缓存
//                HotelUtils.cachedThreadPool.execute(new Runnable() {
//                    @Override
//                    public void run() {
//                        try {
//                            baseService.push(user.getHotelGroupId(), user.getHid(), 9, new HashMap<String, String>(), new HashMap<String, String>(), true, true);
//                        } catch (Exception e) {
//
//                        }
//
//                    }
//                });
//
//                oprecordInfoRequest.setOprecordTemplateId(11);
//                JSONObject oprecordJson = new JSONObject();
//                oprecordJson.put("teamCodeStrs", teamCodeStrs);
//                oprecordJson.put("time", HotelUtils.currentTime());
//                oprecordInfoRequest.setBusinessId2(oprecordJson.toString());
//                oprecordInfoRequests.add(oprecordInfoRequest);
//                //处理操作日志
//                this.baseService.addOprecordReqs(oprecordInfoRequests);


        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 创建客史档案
     *
     * @param registPersonList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData createPersonInfo(List<RegistPerson> registPersonList) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<PersonInfo> personInfoList = new ArrayList<>();
            for (int i = 0; i < registPersonList.size(); i++) {
                RegistPerson registPerson = registPersonList.get(i);
                if (null == registPerson.getPersonName() || registPerson.getPersonName().equals("")) {
                    continue;
                }
                if (null == registPerson.getIdCode() || registPerson.getIdCode().equals("")) {
                    continue;
                }
                //以证件号为唯一标准去查询客史档案
                String idCode = registPerson.getIdCode();
//                IdcardValidator iv = new IdcardValidator();
//                if (!iv.isValidatedAllIdcard(idCode)) {
//                    log.warn("非标准身份证号{}，不进行入库",idCode);
//                    continue;
//                }
                PersonInfoSearch personInfoSearch = new PersonInfoSearch();
                personInfoSearch.setHid(registPerson.getHid());
                personInfoSearch.setIdCode(idCode);
                Page<PersonInfo> personInfos = personInfoDao.selectBySearch(personInfoSearch);
                if (null == personInfos || personInfos.size() < 1) {
                    PersonInfo personInfo = new PersonInfo();
                    personInfo.setHid(registPerson.getHid());
                    personInfo.setHotelGroupId(registPerson.getHotelGroupId());
                    personInfo.setPersonName(registPerson.getPersonName());
                    personInfo.setIdCode(registPerson.getIdCode());
                    personInfo.setSex(registPerson.getSex());
                    personInfo.setBirthday(registPerson.getBirthday());
                    personInfo.setNation(registPerson.getNation());
                    personInfo.setIdImage(registPerson.getIdImage());
                    personInfo.setAddress(registPerson.getAddress());
                    personInfo.setCameraImage(registPerson.getCameraImage());
                    personInfo.setPhone(registPerson.getPhone());
                    personInfoList.add(personInfo);
                }
            }
            for (int i = 0; i < personInfoList.size(); i++) {
                Integer insert = personInfoDao.insert(personInfoList.get(i));
                if (insert < 1) {
                    throw new Exception("创建客历档案失败");
                }
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData upLoadPersonImage(UpLoadPersonImageRequest upLoadPersonImageRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = upLoadPersonImageRequest.getSessionToken();
            TbUserSession user = this.getTbUserSession(sessionToken);
            if (null == user) {
                throw new Exception(HOTEL_CONST.TOKENISNULL);
            }
            Integer registPersonId = upLoadPersonImageRequest.getRegistPersonId();
            if (null == registPersonId || registPersonId < 1) {
                throw new Exception(HOTEL_CONST.DATA_LIST_IS_NULL);
            }

            if (null == upLoadPersonImageRequest.getIdCode()) {
                throw new Exception(HOTEL_CONST.IDCODE_IS_ERROR);
            }

            IdcardValidator idcardValidator = new IdcardValidator();
            boolean validatedAllIdcard = idcardValidator.isValidatedAllIdcard(upLoadPersonImageRequest.getIdCode());

            if (!validatedAllIdcard) {
                throw new Exception(HOTEL_CONST.IDCODE_IS_ERROR);
            }

            RegistPerson registPerson = registPersonDao.selectById(registPersonId);
            if (null == registPerson || registPerson.getRegistState() != 0) {
                throw new Exception(HOTEL_CONST.PERSON_STATE_IS_ERROR);
            }
            RegistPerson registPersonInfo = new RegistPerson();
            registPersonInfo.setRegistPersonId(registPersonId);
            if (upLoadPersonImageRequest.getPhoto() != null && !"".equals(upLoadPersonImageRequest.getPhoto())) {
                String s = upLoadPersonImageRequest.getIdCode() + registPerson.getHid() + registPerson.getRoomNumId() + HotelUtils.currentTime();
                String replace = upLoadPersonImageRequest.getPhoto().replace(' ', '+');
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(replace, 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                registPersonInfo.setCameraImage(uploadObjectRsp.getFileName());
                JSONObject detect = FaceSetUtils.detect(replace, 0);
                Object faces = detect.get("faces");
                if (faces != null) {
                    JSONArray fa = JSONArray.fromObject(faces);
                    JSONObject face = fa.getJSONObject(0);
                    registPersonInfo.setCameraImageFaceToken(face.getString("face_token"));
                }
            }

            Integer update = registPersonDao.update(registPersonInfo);
            if (update < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData getLastCheckoutRegist(GetLastCheckoutRegistRequest getLastCheckoutRegistRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(getLastCheckoutRegistRequest);

            RegistSearch registSearch = new RegistSearch();
            registSearch.setHid(user.getHid());
            registSearch.setRoomNumId(getLastCheckoutRegistRequest.getRoomInfoId());
            registSearch.setState(1);
            Regist regist = registDao.getLastCheckoutRegist(registSearch);
            if (null != regist && regist.getRegistId() > 0) {
                responseData.setData(regist.getRegistId());
                responseData.setData1(regist.getCheckinTime());
            }
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
