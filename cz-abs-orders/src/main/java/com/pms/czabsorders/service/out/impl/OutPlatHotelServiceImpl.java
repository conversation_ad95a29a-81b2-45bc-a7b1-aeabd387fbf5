package com.pms.czabsorders.service.out.impl;

import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.out.OutHotelGroupInfo;
import com.pms.czabsorders.service.out.OutPlatHotelService;
import com.pms.czabsorders.web.out.OutPlatHotelController;
import com.pms.czhotelfoundation.bean.hotel.HotelGroupInfo;
import com.pms.czhotelfoundation.bean.hotel.search.HotelGroupInfoSearch;
import com.pms.czhotelfoundation.dao.hotel.HotelGroupInfoDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.sql.SQLException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Date;

@Service
@Primary
public class OutPlatHotelServiceImpl implements OutPlatHotelService {
    private static final Logger log = LoggerFactory.getLogger(OutPlatHotelController.class);

    @Autowired
    HotelGroupInfoDao hotelGroupInfoDao;

    @Override
    public ResponseData outCreateHotelGroup(OutHotelGroupInfo request) throws Exception {
        log.info("outside plate create hotel group request param :{}", request);
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (StringUtils.isEmpty(request.getUuid())) {
                throw new Exception("集团编码不能空");
            }
            HotelGroupInfo hotelGroupInfo = new HotelGroupInfo();
            HotelUtils.classCopy(request, hotelGroupInfo);
            hotelGroupInfo.setCreateTime(new Date());
            hotelGroupInfo.setEnable(1);
            hotelGroupInfo.setCreateUserId(String.valueOf(0));
            Integer res = hotelGroupInfoDao.insert(hotelGroupInfo);
            if (res < 1) {
                throw new Exception(HOTEL_CONST.INSERTERR);
            }
            responseData.setCode(1);
            responseData.setData(hotelGroupInfo.getChainId());
        } catch (DuplicateKeyException e) {
            log.error("",e);
            throw new Exception("集团编号不能重复");
        } catch (SQLException e) {
            log.error("外部系统添加酒店集团SQL异常，异常信息{}",e);
            throw new Exception(HOTEL_CONST.INSERTERR);
        } catch (Exception e) {
            log.error("外部系统添加酒店集团失败，异常信息{}",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData outUpdateGroup(OutHotelGroupInfo request) throws Exception {
        log.info("outside plate update hotel group request param :{}",request );
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (StringUtils.isEmpty(request.getChainId())) {
                throw new Exception("集团Id不能为空空");
            }
            HotelGroupInfo hotelGroupInfo = new HotelGroupInfo();
            HotelUtils.classCopy(request, hotelGroupInfo);
            hotelGroupInfo.setTelephone(request.getPhone());
            hotelGroupInfo.setUpdateTime(new Date());
            hotelGroupInfo.setUpdateUserId(String.valueOf(0));
            Integer res = hotelGroupInfoDao.outPlatUpdate(hotelGroupInfo);
            if (res < 1) {
                throw new Exception(HOTEL_CONST.UPDATEERR);
            }
            responseData.setCode(1);
        }  catch (SQLException e) {
            log.error("外部系统修改酒店集团SQL异常，异常信息{}",e);
            throw new Exception(HOTEL_CONST.UPDATEERR);
        } catch (Exception e) {
            log.error("外部系统修改酒店集团SQL异常，异常信息{}",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
