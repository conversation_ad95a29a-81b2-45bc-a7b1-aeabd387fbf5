package com.pms.czabsorders.service.checkout.transaction.impl;


import com.alibaba.fastjson.JSONArray;
import com.pms.czabsorders.service.checkout.transaction.CheckOutTransactionService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomRepairRecordHistory;
import com.pms.czhotelfoundation.dao.HourRoomDayUseDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomRepairRecordHistoryDao;
import com.pms.czhotelfoundation.service.room.transaction.RoomTransactionService;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.room.RoomUtils;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.dao.*;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Primary
public class CheckOutTransactionServiceImpl extends BaseService implements CheckOutTransactionService {
    private final static Logger log = LoggerFactory.getLogger(CheckOutTransactionServiceImpl.class);
    @Autowired
    protected RegistDao registDao;

    @Autowired
    private RegistGroupDao registGroupDao;

    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private BookingOrderDao bookingOrderDao;

    @Autowired
    private BookingOrderRoomTypeDao bookingOrderRoomTypeDao;

    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private RoomInfoDao roomInfoDao;

    @Autowired
    private RoomRepairRecordHistoryDao roomRepairRecordDao;

    /*@Autowired
    private SalesHotelCommissionDetailsDao salesHotelCommissionDetailsDao;*/

    @Autowired
    private RoomTransactionService roomTransactionService;

    @Autowired
    private RegistPersonDao registPersonDao;

    @Autowired
    private RegistCancelDao registCancelDao;

    @Autowired
    private HourRoomDayUseDao hourRoomDayUseDao;

    /**
     * 结账
     *  roomAuxiliaryRelations 为删除
     *  其他的全部修改。
     * @param registList                    登记集合
     * @param registGroups                  团队主表
     * @param bookingOrderList              预订单集合
     * @param bookingOrderRoomTypeList      预定房型集合
     * @param bookingOrderRoomNums          预定房间
     * @param roomAuxiliaryRelations        辅助房态集合
     * @param user
     * @param accountList                   账务集合
     * @param registPeople                  入住人集合
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void checkOut(List<Regist> registList, List<RegistGroup> registGroups, List<BookingOrder> bookingOrderList, List<BookingOrderRoomType> bookingOrderRoomTypeList,
                         List<BookingOrderRoomNum> bookingOrderRoomNums, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, TbUserSession user, List<Account> accountList, Map<Integer, RoomInfo> roomInfoMap,
                         ArrayList<Object> salesHotelCommissionDetailsList,List<RegistPerson> registPeople) throws Exception {


        //1.将登记信息改为已结
       /* for (Regist regist:registList){
            Integer update = registDao.update(regist);
        }*/
        registDao.updateRegistList(registList);

        //2.将团队信息改成已结
        for(RegistGroup registGroup:registGroups){

            Integer update = registGroupDao.update(registGroup);
            String descr = "结账";

            if(registGroup.getState()==3){
                descr="部分结账";
            }

            if(update<1){
                throw new Exception("将 "+ registGroup.getGroupName()+" 改为 "+ descr+" 失败");
            }
        }

        //3.修改预订单
        for(BookingOrder bookingOrder:bookingOrderList){

            Integer integer = bookingOrderDao.editBookingOrder(bookingOrder);

            if(integer<1){
                throw new Exception("修改预订单失败,订单号:"+bookingOrder.getSn()+"。编号:"+bookingOrder.getBookingOrderId());
            }

        }

        //4.预定房型
        for(BookingOrderRoomType bookingOrderRoomType:bookingOrderRoomTypeList){

            Integer integer = bookingOrderRoomTypeDao.editBookingOrderRoomType(bookingOrderRoomType);

            if(integer<1){
                throw new Exception("修改预定房型失败，编号:"+bookingOrderRoomType.getId());
            }

        }

        //5.预定房间
        HashMap<String, List> stringListHashMap = new HashMap<>();
        if(bookingOrderRoomNums.size()>0){
            stringListHashMap.put("list",bookingOrderRoomNums);
            bookingOrderRoomNumDao.updateBookingOrderRoomNumList(stringListHashMap);
        }
      /*  for(BookingOrderRoomNum bookingOrderRoomNum:bookingOrderRoomNums){

            Integer integer = bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNum);

            if(integer<1){
                throw new Exception("修改预定房间失败,编号："+bookingOrderRoomNum.getId());
            }

        }*/

        //6.辅助房态
        if(roomAuxiliaryRelations.size()>0){
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(roomAuxiliaryRelations);
        }
        /*for(RoomAuxiliaryRelation roomAuxiliaryRelation:roomAuxiliaryRelations){

            Integer integer = roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());

            if(integer<1){
                throw new Exception("删除辅助房态失败。编号:"+roomAuxiliaryRelation.getRelationId());
            }

        }*/

        //7.账务信息
        if(accountList.size()>0){
            log.info("修改账务信息 {}", JSONArray.toJSONString(accountList));
            accountDao.editAccountList(accountList);
        }
     /*   for(Account account:accountList){

            Integer integer = accountDao.editAccount(account);

            if(integer<1){
                throw new Exception("修改账务信息失败。编号:"+account.getAccountId());
            }

        }
*/
        //8.修改房间信息
        Set<Integer> roomKeys = roomInfoMap.keySet();

        ArrayList<RoomInfo> roomInfos = new ArrayList<>();

        ArrayList<RoomRepairRecordHistory> roomRepairRecordHistories = new ArrayList<>();

        ArrayList<Oprecord> oprecords = new ArrayList<>();

        Oprecord oprecord = new Oprecord(user);

        JSONObject param = new JSONObject();
        for (Integer key:roomKeys){
            RoomInfo roomInfo = roomInfoMap.get(key);
            roomInfo.setRoomNumState(ROOM_STATUS.VD);
            // 6 修改房间状态
            roomInfos.add(roomInfo);

            RoomRepairRecordHistory roomRepairRecord = new RoomRepairRecordHistory();

            roomRepairRecord.setRoomId(roomInfo.getRoomInfoId());
            roomRepairRecord.setRoomNum(roomInfo.getRoomNum());
            roomRepairRecord.setHid(roomInfo.getHid());
            roomRepairRecord.setHotelGroupId(roomInfo.getHotelGroupId());
            roomRepairRecord.setClassId(user.getClassId());
            roomRepairRecord.setCreateUserId(user.getUserId());
            roomRepairRecord.setCreateUserName(user.getUserName());
            roomRepairRecord.setCreateTime(new Date());
            roomRepairRecord.setUpdateUserId(user.getUserId());
            roomRepairRecord.setUpdateUserName(user.getUserName());
            roomRepairRecord.setUpdateTime(new Date());
            roomRepairRecord.setType(RoomUtils.getRoomRepairRecordType(roomInfo.getRoomNumState(), ROOM_STATUS.VD));
            roomRepairRecord.setState(RoomUtils.getRoomRepairRecordState(roomInfo.getRoomNumState(), ROOM_STATUS.VD));
            roomRepairRecord.setBusinessDay(user.getBusinessDay());
            roomRepairRecord.setDes(roomInfo.getRoomNum()+"入住。");

            roomRepairRecordHistories.add(roomRepairRecord);

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription("房间:" + roomInfo.getRoomNum() + " 进行结账，房态改为脏房。");
            oprecords.add(oprecord);
        }

        // 修改房间信息
        if(roomInfos.size()>0){
            roomInfoDao.updateRoomList(roomInfos);
        }

        // 添加房间修改记录
        if(roomRepairRecordHistories.size()>0){
            roomRepairRecordDao.addRoomRepairRecordHistoryList(roomRepairRecordHistories);
        }
        //9.修改提成信息
        /*for(SalesHotelCommissionDetails salesHotelCommissionDetails:salesHotelCommissionDetailsList){

            Integer integer = salesHotelCommissionDetailsDao.editSalesHotelCommissionDetails(salesHotelCommissionDetails);

            if(integer<1){
                throw new Exception("修改提成信息失败，流水号:"+salesHotelCommissionDetails.getSn()+", 编号:"+salesHotelCommissionDetails.getId());
            }

        }*/

        // 10 入住人更改

        ArrayList<RegistPerson> registPeople1 = new ArrayList<>();

        for(RegistPerson registPerson:registPeople){

            Integer registState = registPerson.getRegistState();
            if(registState==1){
                registPerson.setAuthenticateTime(new Date());
                registPeople1.add(registPerson);
                continue;
            }

            registPerson.setRegistState(1);
            registPerson.setAuthenticateTime(new Date());
            registPeople1.add(registPerson);
        }

        registPersonDao.updatePeople(registPeople1);
    }

    @Override
    public void checkOut(List<Regist> registList, List<RegistGroup> registGroups, List<BookingOrder> bookingOrderList, List<BookingOrderRoomType> bookingOrderRoomTypeList, List<BookingOrderRoomNum> bookingOrderRoomNums, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, TbUserSession user, List<Account> accountList, Map<Integer, RoomInfo> roomInfoMap, ArrayList<Object> salesHotelCommissionDetailsList, List<RegistPerson> registPeople, ArrayList<HourRoomDayUse> upaHourUse, ArrayList<HourRoomDayUse> delHourUse) throws Exception {

        CheckOutTransactionService.super.checkOut(registList, registGroups,
                bookingOrderList, bookingOrderRoomTypeList, bookingOrderRoomNums,
                roomAuxiliaryRelations, user, accountList, roomInfoMap,
                salesHotelCommissionDetailsList, registPeople, upaHourUse,
                delHourUse);

        //1.将登记信息改为已结
       /* for (Regist regist:registList){
            Integer update = registDao.update(regist);
        }*/
        registDao.updateRegistList(registList);

        //2.将团队信息改成已结
        for(RegistGroup registGroup:registGroups){

            Integer update = registGroupDao.update(registGroup);
            String descr = "结账";

            if(registGroup.getState()==3){
                descr="部分结账";
            }

            if(update<1){
                throw new Exception("将 "+ registGroup.getGroupName()+" 改为 "+ descr+" 失败");
            }
        }

        //3.修改预订单
        for(BookingOrder bookingOrder:bookingOrderList){

            Integer integer = bookingOrderDao.editBookingOrder(bookingOrder);

            if(integer<1){
                throw new Exception("修改预订单失败,订单号:"+bookingOrder.getSn()+"。编号:"+bookingOrder.getBookingOrderId());
            }

        }

        //4.预定房型
        for(BookingOrderRoomType bookingOrderRoomType:bookingOrderRoomTypeList){

            Integer integer = bookingOrderRoomTypeDao.editBookingOrderRoomType(bookingOrderRoomType);

            if(integer<1){
                throw new Exception("修改预定房型失败，编号:"+bookingOrderRoomType.getId());
            }

        }

        //5.预定房间
        HashMap<String, List> stringListHashMap = new HashMap<>();
        if(bookingOrderRoomNums.size()>0){
            stringListHashMap.put("list",bookingOrderRoomNums);
            bookingOrderRoomNumDao.updateBookingOrderRoomNumList(stringListHashMap);
        }
      /*  for(BookingOrderRoomNum bookingOrderRoomNum:bookingOrderRoomNums){

            Integer integer = bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNum);

            if(integer<1){
                throw new Exception("修改预定房间失败,编号："+bookingOrderRoomNum.getId());
            }

        }*/

        //6.辅助房态
        if(roomAuxiliaryRelations.size()>0){
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(roomAuxiliaryRelations);
        }
        /*for(RoomAuxiliaryRelation roomAuxiliaryRelation:roomAuxiliaryRelations){

            Integer integer = roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());

            if(integer<1){
                throw new Exception("删除辅助房态失败。编号:"+roomAuxiliaryRelation.getRelationId());
            }

        }*/

        //7.账务信息
        if(accountList.size()>0){
            accountDao.editAccountList(accountList);
        }
     /*   for(Account account:accountList){

            Integer integer = accountDao.editAccount(account);

            if(integer<1){
                throw new Exception("修改账务信息失败。编号:"+account.getAccountId());
            }

        }
*/
        //8.修改房间信息
        Set<Integer> roomKeys = roomInfoMap.keySet();

        ArrayList<RoomInfo> roomInfos = new ArrayList<>();

        ArrayList<RoomRepairRecordHistory> roomRepairRecordHistories = new ArrayList<>();

        ArrayList<Oprecord> oprecords = new ArrayList<>();

        Oprecord oprecord = new Oprecord(user);

        JSONObject param = new JSONObject();
        for (Integer key:roomKeys){
            RoomInfo roomInfo = roomInfoMap.get(key);
            roomInfo.setRoomNumState(ROOM_STATUS.VD);
            // 6 修改房间状态
            roomInfos.add(roomInfo);

            RoomRepairRecordHistory roomRepairRecord = new RoomRepairRecordHistory();

            roomRepairRecord.setRoomId(roomInfo.getRoomInfoId());
            roomRepairRecord.setRoomNum(roomInfo.getRoomNum());
            roomRepairRecord.setHid(roomInfo.getHid());
            roomRepairRecord.setHotelGroupId(roomInfo.getHotelGroupId());
            roomRepairRecord.setClassId(user.getClassId());
            roomRepairRecord.setCreateUserId(user.getUserId());
            roomRepairRecord.setCreateUserName(user.getUserName());
            roomRepairRecord.setCreateTime(new Date());
            roomRepairRecord.setUpdateUserId(user.getUserId());
            roomRepairRecord.setUpdateUserName(user.getUserName());
            roomRepairRecord.setUpdateTime(new Date());
            roomRepairRecord.setType(RoomUtils.getRoomRepairRecordType(roomInfo.getRoomNumState(), ROOM_STATUS.VD));
            roomRepairRecord.setState(RoomUtils.getRoomRepairRecordState(roomInfo.getRoomNumState(), ROOM_STATUS.VD));
            roomRepairRecord.setBusinessDay(user.getBusinessDay());
            roomRepairRecord.setDes(roomInfo.getRoomNum()+"入住。");

            roomRepairRecordHistories.add(roomRepairRecord);

            oprecord = new Oprecord(user);
            oprecord.setOccurTime(HotelUtils.currentTime());
            oprecord.setDescription("房间:" + roomInfo.getRoomNum() + " 进行结账，房态改为脏房。");
            oprecords.add(oprecord);
        }

        // 修改房间信息
        if(roomInfos.size()>0){
            roomInfoDao.updateRoomList(roomInfos);
        }

        // 添加房间修改记录
        if(roomRepairRecordHistories.size()>0){
            roomRepairRecordDao.addRoomRepairRecordHistoryList(roomRepairRecordHistories);
        }
        //9.修改提成信息
        /*for(SalesHotelCommissionDetails salesHotelCommissionDetails:salesHotelCommissionDetailsList){

            Integer integer = salesHotelCommissionDetailsDao.editSalesHotelCommissionDetails(salesHotelCommissionDetails);

            if(integer<1){
                throw new Exception("修改提成信息失败，流水号:"+salesHotelCommissionDetails.getSn()+", 编号:"+salesHotelCommissionDetails.getId());
            }

        }*/

        // 10 入住人更改

        ArrayList<RegistPerson> registPeople1 = new ArrayList<>();

        for(RegistPerson registPerson:registPeople){

            Integer registState = registPerson.getRegistState();
            if(registState==1){
                registPerson.setAuthenticateTime(new Date());
                registPeople1.add(registPerson);
                continue;
            }

            registPerson.setRegistState(1);
            registPerson.setAuthenticateTime(new Date());
            registPeople1.add(registPerson);
        }

        registPersonDao.updatePeople(registPeople1);

        if (upaHourUse.size() > 0) {
            for (HourRoomDayUse hourRoomDayUse : upaHourUse) {
                hourRoomDayUseDao.update(hourRoomDayUse);
            }
        }
        if (delHourUse.size() > 0) {

            for (int i = 0; i < delHourUse.size(); i++) {
                hourRoomDayUseDao.delete(delHourUse.get(i).getId());
            }

        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void onAccount(Regist regist, RoomInfo roomInfo,List<RegistPerson> personList, TbUserSession user,
                          ArrayList<Oprecord> oprecords, Oprecord oprecord,List<RoomAuxiliaryRelation> roomAuxiliaryRelations) throws Exception {
        Integer update = registDao.update(regist);
        if (update < 1){
            throw new Exception("修改登记单状态失败");
        }

        for (int i = 0; i < personList.size(); i++) {
            update = registPersonDao.update(personList.get(i));
            if (update < 1){
                throw new Exception("修改宾客信息失败");
            }
        }

        oprecord.setHid(user.getHid());
        oprecord.setRegistId(regist.getRegistId());
        oprecord.setDescription("修改账单状态入住为挂账未结");
        oprecords.add(oprecord);

        update = roomInfoDao.editRoomInfo(roomInfo);
        if (update < 1){
            throw new Exception("修改房态失败");
        }
        oprecord.setHid(user.getHid());
        oprecord.setRegistId(regist.getRegistId());
        oprecord.setDescription("挂账修改房态为脏房");
        oprecords.add(oprecord);

        for(RoomAuxiliaryRelation roomAuxiliaryRelation:roomAuxiliaryRelations){

            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());

        }

    }

    @Override
    public void onAccount(Regist regist, RoomInfo roomInfo, List<RegistPerson> personList, TbUserSession user, ArrayList<Oprecord> oprecords, Oprecord oprecord, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, ArrayList<HourRoomDayUse> upaHourUse, ArrayList<HourRoomDayUse> delHourUse) throws Exception {
        Integer update = registDao.update(regist);
        if (update < 1){
            throw new Exception("修改登记单状态失败");
        }

        for (int i = 0; i < personList.size(); i++) {
            update = registPersonDao.update(personList.get(i));
            if (update < 1){
                throw new Exception("修改宾客信息失败");
            }
        }

        oprecord.setHid(user.getHid());
        oprecord.setRegistId(regist.getRegistId());
        oprecord.setDescription("修改账单状态入住为挂账未结");
        oprecords.add(oprecord);

        update = roomInfoDao.editRoomInfo(roomInfo);
        if (update < 1){
            throw new Exception("修改房态失败");
        }
        oprecord.setHid(user.getHid());
        oprecord.setRegistId(regist.getRegistId());
        oprecord.setDescription("挂账修改房态为脏房");
        oprecords.add(oprecord);

        for(RoomAuxiliaryRelation roomAuxiliaryRelation:roomAuxiliaryRelations){

            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelation(roomAuxiliaryRelation.getRelationId());

        }
        if (upaHourUse.size() > 0) {
            for (HourRoomDayUse hourRoomDayUse : upaHourUse) {
                hourRoomDayUseDao.update(hourRoomDayUse);
            }
        }
        if (delHourUse.size() > 0) {

            for (int i = 0; i < delHourUse.size(); i++) {
                hourRoomDayUseDao.delete(delHourUse.get(i).getId());
            }

        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void registCancel(Regist regist, RoomInfo roomInfo, List<RegistPerson> personList, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, BookingOrder bookingOrder, BookingOrderRoomNum bookingOrderRoomNum, RoomRepairRecordHistory roomRepairRecord) throws Exception {
        // regiteCancelDao
        registCancelDao.insertRegist(regist);

        // 删除订单
        registDao.delete(regist.getRegistId());

        // 修复房间信息
        roomInfoDao.editRoomInfo(roomInfo);

        // 修改登记人信息
        registPersonDao.updatePeople(personList);

        if(roomAuxiliaryRelations.size()>0){
            roomAuxiliaryRelationDao.deleteRoomAuxiliaryRelationList(roomAuxiliaryRelations);
        }

        if(bookingOrder!=null&&bookingOrder.getBookingOrderId()>0){
            bookingOrderDao.editBookingOrder(bookingOrder);
        }

        if(bookingOrderRoomNum!=null&&bookingOrderRoomNum.getId()>0){
            bookingOrderRoomNumDao.editBookingOrderRoomNum(bookingOrderRoomNum);
        }


        roomRepairRecordDao.insert(roomRepairRecord);
    }




}
