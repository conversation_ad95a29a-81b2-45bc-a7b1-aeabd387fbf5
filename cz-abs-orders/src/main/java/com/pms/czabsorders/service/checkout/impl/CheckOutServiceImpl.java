package com.pms.czabsorders.service.checkout.impl;


import com.github.pagehelper.Page;
import com.pms.czabsorders.bean.AddArAccount;
import com.pms.czabsorders.feign.OtaFeign;
import com.pms.czabsorders.service.account.AccountService;
import com.pms.czabsorders.service.checkin.transaction.CheckInTransactionService;
import com.pms.czabsorders.service.checkout.CheckOutService;
import com.pms.czabsorders.service.checkout.transaction.CheckOutTransactionService;
import com.pms.czabsorders.service.ota.OtaChangePushUtil;
import com.pms.czabsorders.service.turnAlways.TurnAlwaysService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.bean.account.search.AccountSearch;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czhotelfoundation.bean.room.RoomRepairRecordHistory;
import com.pms.czhotelfoundation.bean.room.search.RoomAuxiliaryRelationSearch;
import com.pms.czhotelfoundation.bean.room.search.RoomInfoSearch;
import com.pms.czhotelfoundation.bean.search.HourRoomDayUseSearch;
import com.pms.czhotelfoundation.dao.HourRoomDayUseDao;
import com.pms.czhotelfoundation.dao.room.RoomAuxiliaryRelationDao;
import com.pms.czhotelfoundation.dao.room.RoomInfoDao;
import com.pms.czhotelfoundation.service.room.RoomService;
import com.pms.czhotelfoundation.service.room.transaction.RoomTransactionService;
import com.pms.czmembership.bean.company.HotelCompanyAccount;
import com.pms.czmembership.bean.company.HotelCompanyAccountInfo;
import com.pms.czmembership.bean.company.HotelCompanyArRecode;
import com.pms.czmembership.bean.company.HotelCompanyInfo;
import com.pms.czmembership.bean.member.CardCheckinRecord;
import com.pms.czmembership.bean.member.CardInfo;
import com.pms.czmembership.bean.member.CardMemberLevel;
import com.pms.czmembership.bean.member.search.CardCheckinRecordSearch;
import com.pms.czmembership.bean.member.search.CardMemberLevelSearch;
import com.pms.czmembership.dao.company.HotelCompanyAccountDao;
import com.pms.czmembership.dao.company.HotelCompanyAccountInfoDao;
import com.pms.czmembership.dao.company.HotelCompanyArRecodeDao;
import com.pms.czmembership.dao.company.HotelCompanyInfoDao;
import com.pms.czmembership.dao.member.CardCheckinRecordDao;
import com.pms.czmembership.dao.member.CardInfoDao;
import com.pms.czmembership.dao.member.CardMemberLevelDao;
import com.pms.czmembership.service.memeber.MemberService;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.conf.HotelIotPlatConfig;
import com.pms.czpmsutils.constant.*;
import com.pms.czpmsutils.constant.book.BOOK;
import com.pms.czpmsutils.constant.hotelsetting.HOTEL_SETTING;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.room.ROOM_STATUS;
import com.pms.czpmsutils.constant.room.RoomUtils;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.OprecordInfoRequest;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.enums.HmhOrderStatusEnum;
import com.pms.czpmsutils.request.*;
import com.pms.czpmsutils.thirdauth.HotelIotStrategy;
import com.pms.czpmsutils.view.AESUtil;
import com.pms.pmsorder.bean.*;
import com.pms.pmsorder.bean.search.*;
import com.pms.pmsorder.dao.*;
import com.pms.pmsorder.service.impl.FoodsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Primary
public class CheckOutServiceImpl extends BaseService implements CheckOutService {

    @Autowired
    private BookingOrderDao bookingOrderDao;


    @Autowired
    private BookingOrderRoomNumDao bookingOrderRoomNumDao;


    @Autowired
    private RegistDao registDao;


    @Autowired
    private RoomInfoDao roomInfoDao;


    @Autowired
    private RoomAuxiliaryRelationDao roomAuxiliaryRelationDao;

    @Autowired
    private RegistGroupDao registGroupDao;

    @Autowired
    protected RoomTransactionService roomTransactionService;

    @Autowired
    private RoomService roomService;

    @Autowired
    private AccountDao accountDao;

    @Autowired
    private RegistPersonDao registPersonDao;

/*    @Autowired
    private SalesHotelCommissionDetailsDao salesHotelCommissionDetailsDao;

    @Autowired
    private SalesHotelCommissionTypeDao salesHotelCommissionTypeDao;*/

    @Autowired
    private CheckOutTransactionService checkOutTransactionService;

    @Autowired
    private TurnAlwaysService turnAlwaysService;

    @Autowired
    private CheckInTransactionService checkInTransactionService;

    @Autowired
    OtaChangePushUtil otaChangePushService;

    private BaseService baseService = this;

    @Autowired
    private CardInfoDao cardInfoDao;

    @Autowired
    private CardCheckinRecordDao cardCheckinRecordDao;

    @Autowired
    private BookingOrderConfigDao bookingOrderConfigDao;

    @Autowired
    private HotelCompanyAccountDao hotelCompanyAccountDao;

    @Autowired
    private HotelCompanyArRecodeDao hotelCompanyArRecodeDao;

    @Autowired
    private AccountService accountService;

    @Autowired
    private HotelCompanyAccountInfoDao hotelCompanyAccountInfoDao;

    @Autowired
    private BookingOrderDailyPriceDao bookingOrderDailyPriceDao;

    @Autowired
    private MemberService memberService;

    @Autowired
    private HotelCompanyInfoDao hotelCompanyInfoDao;

    @Autowired
    private HourRoomDayUseDao hourRoomDayUseDao;

    @Resource
    private HotelIotPlatConfig iotPlatConfig;

    @Resource
    private WebClientUtil webClientUtil;

    @Resource
    private CardMemberLevelDao cardMemberLevelDao;
    @Autowired
    private OtaFeign otaFeign;

    /**
     * 结账相关
     *
     * @param checkoutParam
     * @return
     */
    @Override
    public ResponseData checkOut(CheckoutParam checkoutParam) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = checkoutParam.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);


            //操作日志
//            ArrayList<Oprecord> oprecords = new ArrayList<>();
//            Oprecord oprecord = new Oprecord(user);

            ArrayList<OprecordInfoRequest> oprecordInfoRequests = new ArrayList<>();


            StringBuilder roomBuilder = new StringBuilder();

            //获取结账类型
            //1.单房间结账
            //2.团队结账
            //3,入住人结账
            int type = checkoutParam.getType();
            if (type == 3) {
                return this.personCheckout(checkoutParam, user);
            }

            //登记单信息
            List<Regist> registList = new ArrayList<>();
            RegistGroup registGroup = new RegistGroup();

            Regist mainRegist = new Regist();

            // 查询团队账   账务信息。0不查询  其他 为团队id
            Integer orGroupAccountId = 0;

            if (type == 2) {

                int teamCodeId = checkoutParam.getTeamCodeId();

                registGroup = registGroupDao.selectById(teamCodeId);

                if (registGroup == null || !registGroup.getHid().equals(user.getHid())) {
                    throw new Exception("未查到当前团第信息");
                }

                if (registGroup.getState() != 1 && registGroup.getState() != 3) {
                    throw new Exception("当前团队房状态不允许结账。编号:" + registGroup.getRegistGroupId());
                }

                if (registGroup.getGroupType() != 9) {
                    orGroupAccountId = registGroup.getRegistGroupId();
                }

                RegistSearch registSearch = new RegistSearch();
                registSearch.setTeamCodeId(teamCodeId);

                registList = registDao.selectBySearch(registSearch);

                if (registList.size() < 1) {
                    throw new Exception("未查到团队下的登记信息");
                }

                roomBuilder.append("团队：");
                roomBuilder.append(registGroup.getGroupName());

                for (Regist regist : registList) {
                    if (regist.getIsMainRoom() == 1) {
                        mainRegist = regist;
                        break;
                    }
                }

            } else {

                int registId = checkoutParam.getRegistId();
                Regist regist = registDao.selectById(registId);
                if (regist == null || !regist.getHid().equals(user.getHid())) {
                    throw new Exception("未查到当前房间信息。编号:" + regist.getRegistId());
                }
                if (regist.getState() == 1 || regist.getState() == 4) {
                    throw new Exception("当前房间状态为 " + HotelUtils.stateMap.get(regist.getState()) + " ，不允许结账。编号:" + regist.getRegistId());
                }
                registList.add(regist);
                roomBuilder.append(regist.getRoomNum());
                mainRegist = regist;
            }

            //  遍历登记单集合
            // 1.1获取登记单里的团队信息
            // 1.2获取登记单里的预定信息
            // 1.3获取登记单里的账务信息
            // 1.4获取登记单里的房间信息
            // 1.5获取登记单里的辅助房态
            // 1.6获取登记单里的佣金信息
            // 1.7获取登记单里的入住人信息

            //团队信息
            ArrayList<RegistGroup> registGroupList = new ArrayList<>();

            //预订单信息
            HashMap<Integer, BookingOrder> bookingOrderMap = new HashMap<>();
            HashMap<Integer, BookingOrderRoomNum> bookingOrderRoomNumMap = new HashMap<>();
            ArrayList<BookingOrder> bookingOrderList = new ArrayList<>();
            ArrayList<BookingOrderRoomNum> bookingOrderRoomNumList = new ArrayList<>();


            //账务信息
            ArrayList<Account> accountList = new ArrayList<>();
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setIsCancel(0);

            //房间信息
            HashMap<Integer, RoomInfo> roomInfoMap = new HashMap<>();

            //待结账的登记信息
            ArrayList<Regist> checkoutRegistList = new ArrayList<>();

            //辅助房态信息
            ArrayList<RoomAuxiliaryRelation> roomAuxiliaryRelationList = new ArrayList<>();


            // 如果当前房间是会员入住，则记录regist
            final ArrayList<Regist> cardCheckinRecords = new ArrayList<>();


            //提成信息
            //   ArrayList<SalesHotelCommissionDetails> salesHotelCommissionDetailsList = new ArrayList<>();

            // 待结账的入住人信息
            ArrayList<RegistPerson> checkOutPeople = new ArrayList<>();


            int sumPay = 0;
            int sumSale = 0;

            //总房费
            int sumRoomSale = 0;

            // 修改的钟点房
            ArrayList<HourRoomDayUse> upaHourUse = new ArrayList<>();

            // 删除的钟点房房情
            ArrayList<HourRoomDayUse> delHourUse = new ArrayList<>();
            Integer nowDay = HotelUtils.parseDate2Int(new Date());


            final HashMap<Integer, Integer> registForRoomPrice = new HashMap<>();

            for (Regist regist : registList) {

                // 1已结 0未结 2挂账  4 预结 3撤销
                Integer state = regist.getState();
                if (state == 1 || state == 3) {
                    continue;
                }

                //查询房间信息
                if (regist.getState() == 0) {
                    RoomInfo roomInfo = roomInfoDao.selectById(regist.getRoomNumId());
                    roomInfoMap.put(regist.getRegistId(), roomInfo);
                }

                Date date = new Date();

                //查询账务信息
                accountSearch.setRegistId(regist.getRegistId());
                List<Account> accounts = accountDao.selectBySearch(accountSearch);

                int pay = 0;
                int sale = 0;
                int roomSale = 0;

                for (Account account : accounts) {

                    Integer payType = account.getPayType();

                    // 冲账的不记录
                    if (account.getIsCancel() != 0) {
                        continue;
                    }

                    if (payType == 1) {
                        sale += account.getPrice();

                        if (account.getAccountType() != null) {
                            if (account.getAccountType() == 1) {
                                roomSale += account.getPrice();
                            }
                        }


                    } else {
                        pay += account.getPrice();
                    }

                    //如果是已结，则不记录到修改列表
                    if (account.getRegistState() == 1) {
                        continue;
                    }

                    //如果是预授权，未完成不允许结账
                    if (account.getPayCodeId().equals("9100") && !account.getThirdRefundState().equals(2)) {
                        throw new Exception("预授权未完成，账单号:" + account.getAccountId());
                    }

                    account.setRegistState(1);
                    account.setUpdateUserName(user.getUserName());
                    if (!account.getPayCodeId().equals("9100") && !account.getPayCodeId().equals("9620")) {
                        account.setUpdateTime(date);
                    }
                    account.setUpdateUserId(user.getUserId());
                    account.setRegistId(mainRegist.getRegistId());

                    accountList.add(account);

                }

                //记录总消费和总付款
                sumPay += pay;
                sumSale += sale;

                //如果当前房单信息是未结状态，则跳出循环
                if (regist.getState() == 1) {
                    continue;
                }


//                oprecord.setOccurTime(HotelUtils.currentTime());
//                oprecord.setRoomNum(regist.getRoomNum());
//                oprecord.setMainId(regist.getSn());
//                oprecord.setBookingOrderId(regist.getBookingOrderId());
//                oprecord.setRegistId(regist.getRegistId());
//                oprecord.setSourceValue(regist.getState() + "");
//                oprecord.setChangedValue("1");
//
//                String descr = "将 " + regist.getRoomNum() + " 的状态从 " + HotelUtils.stateMap.get(regist.getState()) + " 改为 已结。";
//                oprecord.setDescription(descr);
//                oprecords.add(oprecord);

                //修改登记状态
                regist.setState(1);
                regist.setCheckoutBusinessDay(user.getBusinessDay());
                regist.setCheckoutClassId(user.getClassId());
                regist.setSettleAccountTime(date);
                regist.setCheckoutOperator(user.getUserId());
                regist.setUpdateUserId(user.getUserId());
                regist.setUpdateTime(date);

                //更新登记表中总消费和总付款
                //  regist.getIsMainRoom()!=0 --只设置主账房的，会导致其他房间的sumpay 和sumcost 不为0
//                if(type==2&&regist.getIsMainRoom()!=0){
                if (type == 2) {
                    regist.setSumPay(0);
                    regist.setSumSale(0);
                }
                checkoutRegistList.add(regist);

                Integer memberId = regist.getMemberId();

                if (memberId != null) {
                    cardCheckinRecords.add(regist);
                }


                //查询预订单信息
                if (regist.getBookingOrderId() != null && regist.getBookingOrderId() > 0) {

                    //如果预订单没有预定信息，则查询预定信息
                    if (bookingOrderMap.get(regist.getBookingOrderId()) == null) {

                        BookingOrder bookingOrder = bookingOrderDao.selectById(regist.getBookingOrderId());

                        bookingOrderMap.put(regist.getBookingOrderId(), bookingOrder);


                    }

                    BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                    bookingOrderRoomNumSearch.setRegistId(regist.getRegistId());
                    List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);
                    if (bookingOrderRoomNums.size() > 0) {
                        BookingOrderRoomNum bookingOrderRoomNum = bookingOrderRoomNums.get(0);
                        bookingOrderRoomNum.setIsCheckout(1);
                        bookingOrderRoomNumMap.put(bookingOrderRoomNum.getId(), bookingOrderRoomNum);
                        bookingOrderRoomNumList.add(bookingOrderRoomNum);
                    }

                }

                //查询当前房间的辅助房态
                RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
                roomAuxiliaryRelationSearch.setRoomId(regist.getRoomNumId());
                List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);
                if(!CollectionUtils.isEmpty(roomAuxiliaryRelations)){
                    Collections.sort(roomAuxiliaryRelations, (c1, c2) -> {
                        return c2.getRelationId().compareTo(c1.getRelationId()); // 降序排列
                    });
                    regist.setRoomAuxiliaryRecord(roomAuxiliaryRelations.get(0).getRoomAuxiliaryId()+"");
                }
//                String kes = "";
//                for (RoomAuxiliaryRelation rarl : roomAuxiliaryRelations) {
//
//                    kes += rarl.getRoomAuxiliaryId() + ",";
//
//                }
//                if (kes.length() > 1) {
//                    kes = kes.substring(0, kes.length() - 1);
//                }
                roomAuxiliaryRelationList.addAll(roomAuxiliaryRelations);

                Integer checkinType = regist.getCheckinType();

                // 如果是钟点房退房，则更改钟点房型数据


                //查询登记表中的销售信息
              /*  Integer saleId = regist.getSaleId();
                if (saleId == null || saleId < 1) {
                    continue;
                }*/

               /* //提成明细
                SalesHotelCommissionDetails salesHotelCommissionDetails = salesHotelCommissionDetailsDao.selectById(regist.getSaleId());
                salesHotelCommissionDetails.setAcount(regist.getDayCount());
                //佣金类型
                SalesHotelCommissionType salesHotelCommissionType = salesHotelCommissionTypeDao.selectById(salesHotelCommissionDetails.getHotelCommissionTypeId());

                salesHotelCommissionDetails = handleCommission(salesHotelCommissionDetails, salesHotelCommissionType, roomSale);
                salesHotelCommissionDetails.setSourceState(1);
                salesHotelCommissionDetailsList.add(salesHotelCommissionDetails);*/


                // 入住人
                RegistPersonSearch registPersonSearch = new RegistPersonSearch();
                registPersonSearch.setRegistId(regist.getRegistId());
                List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);

                registForRoomPrice.put(regist.getRegistId(), roomSale);
                //将入住单登记状态改为已经
                OprecordInfoRequest oprecordInfoRequest = new OprecordInfoRequest();
                oprecordInfoRequest.setSessionToken(sessionToken);
                oprecordInfoRequest.setRegistId(regist.getRegistId());
                oprecordInfoRequest.setRoomNum(regist.getRoomNum());
                oprecordInfoRequest.setOprecordTemplateId(3);
                JSONObject oprecordJson = new JSONObject();
                oprecordJson.put("roomNo", regist.getRoomNum());
                oprecordJson.put("guestName", registPeople.get(0).getPersonName());
                oprecordJson.put("time", HotelUtils.currentTime());
                oprecordInfoRequest.setBusinessId2(oprecordJson.toString());
                oprecordInfoRequests.add(oprecordInfoRequest);

                checkOutPeople.addAll(registPeople);

                // 不是钟点房，则跳出循环
                if (checkinType != 2) {
                    continue;
                }

                Integer start = HotelUtils.parseDate2Int(regist.getCheckinTime());
                Integer end = HotelUtils.parseDate2Int(regist.getCheckoutTime());

                int startHours = regist.getCheckinTime().getHours();
                int endHours = regist.getCheckoutTime().getHours();


                // 当前时间大于退房时间的小时。不处理
                if (nowDay >= end && date.getHours() >= endHours) {
                    continue;
                }


                // 判断开始日期和结束日期是否一致
                if (!start.equals(end)) {


                    HourRoomDayUseSearch hourRoomDayUseSearch = new HourRoomDayUseSearch();
                    hourRoomDayUseSearch.setHid(user.getHid());
                    hourRoomDayUseSearch.setRoomInfoId(regist.getRoomNumId());
                    hourRoomDayUseSearch.setBusinessDay(end);

                    Page<HourRoomDayUse> hourRoomDayUses = hourRoomDayUseDao.selectBySearch(hourRoomDayUseSearch);

                    if (hourRoomDayUses.size() > 0) {

                        HourRoomDayUse hourRoomDayUse = hourRoomDayUses.get(0);

                        String useMsg = hourRoomDayUse.getUseMsg();

                        String[] split = useMsg.split(",");

                        Boolean isUpa = false;

                        String newUseStr = "";

                        for (int i = 0; i < split.length; i++) {

                            int i1 = Integer.parseInt(split[i]);

                            if (i1 <= endHours) {
                                continue;
                            }
                            isUpa = true;

                            newUseStr += split[i];
                            newUseStr += ",";

                        }

                        if (isUpa) {
                            hourRoomDayUse.setUseMsg(newUseStr.substring(0, newUseStr.length() - 1));
                            upaHourUse.add(hourRoomDayUse);
                        } else {
                            delHourUse.add(hourRoomDayUse);
                        }

                    }

                    endHours = 23;

                }


                HourRoomDayUseSearch hourRoomDayUseSearch = new HourRoomDayUseSearch();
                hourRoomDayUseSearch.setHid(user.getHid());
                hourRoomDayUseSearch.setRoomInfoId(regist.getRoomNumId());
                hourRoomDayUseSearch.setBusinessDay(start);

                Page<HourRoomDayUse> hourRoomDayUses = hourRoomDayUseDao.selectBySearch(hourRoomDayUseSearch);


                if (hourRoomDayUses.size() > 0) {

                    HourRoomDayUse hourRoomDayUse = hourRoomDayUses.get(0);

                    // 查询使用记录
                    String useMsg = hourRoomDayUse.getUseMsg();

                    String[] split = useMsg.split(",");

                    Boolean isUpa = false;

                    String newUseStr = "";

                    for (int i = 0; i < split.length; i++) {

                        int i1 = Integer.parseInt(split[i]);

                        if (i1 >= startHours && i1 <= endHours) {
                            continue;
                        }
                        isUpa = true;

                        newUseStr += split[i];
                        newUseStr += ",";

                    }

                    if (isUpa) {
                        hourRoomDayUse.setUseMsg(newUseStr.substring(0, newUseStr.length() - 1));
                        upaHourUse.add(hourRoomDayUse);
                    } else {
                        delHourUse.add(hourRoomDayUse);
                    }

                }


            }

            //取出要结账的房单集合
            if (checkoutRegistList.size() < 1) {
                throw new Exception("未查到要结账的房间信息");
            }

            // 查询团账信息
            if (orGroupAccountId > 0) {

                Date date = new Date();

                AccountSearch accountSearch1 = new AccountSearch();
                accountSearch1.setHid(user.getHid());
                accountSearch1.setRegistId(orGroupAccountId);
                accountSearch1.setGroupAccount(1);
                accountSearch1.setRegistState(0);

                List<Account> accounts = accountDao.selectBySearch(accountSearch1);

                int pay = 0;
                int sale = 0;
                int roomSale = 0;

                for (Account account : accounts) {

                    Integer payType = account.getPayType();

                    // 冲账的不记录
                    if (account.getIsCancel() != 0) {
                        continue;
                    }

                    if (payType == 1) {
                        sale += account.getPrice();

                        if (account.getAccountType() != null) {
                            if (account.getAccountType() == 1) {
                                roomSale += account.getPrice();
                            }
                        }


                    } else {
                        pay += account.getPrice();
                    }

                    //如果是已结，则不记录到修改列表
                    if (account.getRegistState() == 1) {
                        continue;
                    }

                    //如果是预授权，未完成不允许结账
                    if (account.getPayCodeId().equals("9100") && !account.getThirdRefundState().equals(2)) {
                        throw new Exception("预授权未完成，账单号:" + account.getAccountId());
                    }

                    account.setRegistState(1);
                    account.setUpdateUserName(user.getUserName());
                    if (!account.getPayCodeId().equals("9100") && !account.getPayCodeId().equals("9620")) {
                        account.setUpdateTime(date);
                    }
                    account.setUpdateUserId(user.getUserId());
                    account.setRegistId(mainRegist.getRegistId());

                    accountList.add(account);

                }
                //记录总消费和总付款
                sumPay += pay;
                sumSale += sale;

            }

            //计算差额
            int diff = sumPay - sumSale;

            //当结账状态 不为 3.团队里单房间离店时，则验证消费和付款信息
            if (type != 3 && diff != 0) {
                JSONObject resultMap = new JSONObject();
                resultMap.put("errorType", 1);
                resultMap.put("diff", diff);
                responseData.setData(resultMap);
                throw new Exception("消费和付款不持平，请补足差价。");
            }

            //如果是团队则记录团队修改记录
            if (type > 1) {

                registGroup.setSumRooms(registList.size());
                registGroup.setSumSales(sumSale);
                registGroup.setSumPay(sumPay);
                registGroup.setState(type);

                registGroupList.add(registGroup);
            }

            /*2.查询预定信息，判断当前预定的状态*/
            Set<Integer> bookingKeys = bookingOrderMap.keySet();
            BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
            for (Integer bk : bookingKeys) {

                //默认标示为全部已结
                Boolean isFinish = true;
                BookingOrder bookingOrder = bookingOrderMap.get(bk);
                bookingOrderRoomNumSearch.setBookingOrderId(bookingOrder.getBookingOrderId());
                List<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

                //遍历预定房间集合，判断是否有未结账的房价
                for (BookingOrderRoomNum born : bookingOrderRoomNums) {

                    if (bookingOrderRoomNumMap.get(born.getId()) != null) {
                        continue;
                    }

                    //如果有未结账单
                    //则不对预订单做操作
                    if (born.getIsCheckout() == 0) {
                        isFinish = false;
                        break;
                    }

                }

                //代表预订单全部完成
                if (isFinish) {
                    bookingOrder.setOrderStatus(BOOK.STA_RZWC);
                    bookingOrder.setUpdateTime(new Date());
                    bookingOrder.setUpdateUserId(user.getUserId());
                    bookingOrder.setUpdateUserName(user.getUserName());
                    bookingOrder.setOtaStatus(HmhOrderStatusEnum.ALREADY_CHECKED_OUT.getType());
                    bookingOrderList.add(bookingOrder);
                    //调用ota订单状态以及价量态推送
                    otaChangePushService.pushOrderAndNumChange(bookingOrder, bookingOrderRoomNums);
                    //将入住单登记状态改为已经
//                    oprecord.setOccurTime(HotelUtils.currentTime());
//                    oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
//                    String descr = "将订单 " + bookingOrder.getSn() + " 的状态改为 入住完成。";
//                    oprecord.setDescription(descr);
//                    oprecords.add(oprecord);
                    OprecordInfoRequest oprecord = new OprecordInfoRequest();
                    oprecord.setBookingOrderId(bookingOrder.getBookingOrderId());
                    oprecord.setOprecordTemplateId(4);
                    JSONObject orderOprecord = new JSONObject();
                    orderOprecord.put("sn", bookingOrder.getSn());
                    orderOprecord.put("newSatate", "入住完成");
                    oprecord.setBusinessId2(orderOprecord.toString());
                    oprecordInfoRequests.add(oprecord);
                }

            }


            // 查询订单的在住人
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();


            //调用结账的业务方法
            checkOutTransactionService.checkOut(
                    registList, registGroupList, bookingOrderList,
                    new ArrayList<BookingOrderRoomType>(),
                    bookingOrderRoomNumList,
                    roomAuxiliaryRelationList,
                    user, accountList,
                    roomInfoMap, new ArrayList<>(),
                    checkOutPeople,upaHourUse,delHourUse);

            CheckOutServiceImpl checkOutService = this;

            final String s = roomBuilder.toString();

            final List<Regist> resList = registList;

            final Map<Integer, List<RegistPerson>> persons = checkOutPeople.stream().collect(Collectors.groupingBy(RegistPerson::getRegistId));

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    try {


                        HashMap<String, String> fieledMap = new HashMap<>();

                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();

                        checkOutService.updateRoomListForCache(user);

                        checkOutService.updateVipCheckInNum(cardCheckinRecords, user);

                        CardPointRequest cardPointRequest = new CardPointRequest();

                        for (Regist regist : resList) {
                            Integer memberId = regist.getMemberId();
                            if (memberId == null || memberId < 1) {
                                continue;
                            }

                            Integer integer = registForRoomPrice.get(regist.getRegistId());
                            if (integer == null || integer == 0) {
                                continue;
                            }
                            cardPointRequest = new CardPointRequest();
                            cardPointRequest.setSessionToken(user.getSessionId());
                            cardPointRequest.setCardId(regist.getMemberId());
                            cardPointRequest.setType(2);
                            cardPointRequest.setRegistId(regist.getRegistId());
                            cardPointRequest.setRoomInfoId(regist.getRoomNumId());
                            cardPointRequest.setRoomNo(regist.getRoomNum());
                            cardPointRequest.setPoint(integer);

                            memberService.memberTranslatePoint(cardPointRequest);
                        }

                       turnAlwaysService.turnAlwaysCacheFunc(user);

                        fieledMap.put("roomList", s);

                        baseService.push(user.getHotelGroupId(), user.getHid(), 7, fieledMap, new HashMap<String, String>(), true, true);
                    } catch (Exception e) {

                        log.info("-------------------结账线程处理报错-begin----------------");
                        log.error("",e);
                        log.info("-------------------结账线程处理报错-end----------------");
                    }
                }
            });


            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    for (int i = 0; i < resList.size(); i++) {
                        Regist regist = resList.get(i);
                        SmartLockRequest smartLockRequest = new SmartLockRequest();
                        smartLockRequest.setSessionToken(sessionToken);
                        smartLockRequest.setRegistId(regist.getRegistId());
                        smartLockRequest.setCheckinTime(regist.getCheckinTime());
                        smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                        smartLockRequest.setRoomTypeId(regist.getRoomTypeId());
                        smartLockRequest.setRoomTypeName(regist.getRoomTypeName());
                        smartLockRequest.setRoomNumId(regist.getRoomNumId());
                        smartLockRequest.setRoomNum(regist.getRoomNum());
                        smartLockRequest.setHid(regist.getHid());
                        smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                        smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                        smartLockRequest.setCheckinType(regist.getCheckinType());
                        smartLockRequest.setBussType(2);
                        List<RegistPerson> registPeople = persons.get(regist.getRegistId());
                        List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                        for (int j = 0; j < registPeople.size(); j++) {
                            RegistPerson registPersonInfo = registPeople.get(j);
                            SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                            BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                            peoplesList.add(registPersonDetail);
                        }
                        smartLockRequest.setPeoples(peoplesList);
                        baseService.SmartLockCheckout(smartLockRequest);
                    }
                }
            });

            //this.addOprecords(oprecords);

            //2022-02-09 添加会员消费撤销短信提醒
            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        FoodsServiceImpl foodsService = new FoodsServiceImpl();
                        Set<Integer> integers = roomInfoMap.keySet();
                        for (Integer key : integers) {
                            RoomInfo roomInfo = roomInfoMap.get(key);
                            foodsService.sendCardState(roomInfo.getLockNum());
                        }
                        JSONObject postData = new JSONObject();
                        postData.put("sessionToken", sessionToken);
                        JSONObject hotelBaseInfo = JSONObject.fromObject(baseService.getHotelBaseInfo(postData));
                        JSONObject data = hotelBaseInfo.getJSONObject("data");
                        String hotelName = data.getString("hotelName");
                        String telephone = data.getString("telephone");
                        String addr = data.getString("addr");
                        //尊敬的{1}，您好，感谢您选择入住{2}，期待您的再次光临，如有疑问请拨打酒店电话：{3}
                        for (int i = 0; i < checkOutPeople.size(); i++) {
                            RegistPerson registPerson = checkOutPeople.get(i);
                            if (registPerson != null && registPerson.getPhone() != null && registPerson.getPhone().length() == 11) {
                                final ArrayList<String> strings = new ArrayList<>();
                                strings.add(registPerson.getPersonName());
                                strings.add(hotelName);
                                strings.add(telephone);
                                SmsHotelSendRecordRequest smsHotelSendRecordRequest = new SmsHotelSendRecordRequest();
                                smsHotelSendRecordRequest.setLocationId(SMS_LOC.CHECK_OUT);
                                smsHotelSendRecordRequest.setSessionToken(user.getSessionId());
                                smsHotelSendRecordRequest.setPhoneNumber(registPerson.getPhone());
                                smsHotelSendRecordRequest.setParams(strings);
                                baseService.addSmsHotelSendRecordFunc(smsHotelSendRecordRequest);
                            }
                        }
                    } catch (Exception e) {
                        log.error("",e);
                    }
                }
            });

            this.baseService.addOprecordReqs(oprecordInfoRequests);

            /**
             * 非团客，则调用退房接口，推送到酒店
             */
            if(type != 2){
                IotHotelCheckOut checkOut = new IotHotelCheckOut();
                checkOut.setCode(mainRegist.getRegistId() + "");
                HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
                Map<String, String> authParamMap = new HashMap<>();
                authParamMap.put("eid",user.getHid().toString());
                authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
                authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
                authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
                try {
                    authParamMap.put("phone", AESUtil.encryptAesCbc(user.getPhone(),iotPlatConfig.getSassTokenSecret()));
                } catch (Exception e) {
                    log.error("手机号加密失败");
                }
                hotelIotStrategy.initStrategy(authParamMap);
                webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.CHECK_OUT,
                        checkOut,
                        hotelIotStrategy
                );
            }

            /**
             * 联房结账
             */
            if(type == 2){
                if(!registList.isEmpty()){
                    for (Regist regist : registList) {
                        IotHotelCheckOut checkOut = new IotHotelCheckOut();
                        checkOut.setCode(regist.getRegistId() + "");
                        HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
                        Map<String, String> authParamMap = new HashMap<>();
                        authParamMap.put("eid",user.getHid().toString());
                        authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
                        authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
                        authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
                        try {
                            authParamMap.put("phone", AESUtil.encryptAesCbc(user.getPhone(),iotPlatConfig.getSassTokenSecret()));
                        } catch (Exception e) {
                            log.error("手机号加密失败");
                        }
                        hotelIotStrategy.initStrategy(authParamMap);
                        webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.CHECK_OUT,
                                checkOut,
                                hotelIotStrategy
                        );
                    }
                }
            }

            /**
             * 会员退房会员等级升级判断
             */
            List<Regist> finalRegistList = registList;
            HotelUtils.cachedThreadPool.execute(() -> {
                try {
                    for(Regist regist: finalRegistList){
                        Integer cardInfoId = regist.getMemberId();
                        if (null == cardInfoId || 0 == cardInfoId) {
                            log.warn("散客入住，不进行会员等级升级判断");
                        } else {
                            RegistSearch registSearch = new RegistSearch();
                            registSearch.setMemberId(regist.getMemberId());
                            List<Regist> regists = registDao.selectBySearch(registSearch);
                            if (!CollectionUtils.isEmpty(regists)) {
                                Integer dayCount = 0;
                                for (Regist r : regists) {
                                    dayCount += r.getDayCount();
                                }
                                if (dayCount > 0) {
                                    CardMemberLevelSearch memberLevelSearch = new CardMemberLevelSearch();
                                    memberLevelSearch.setHid(user.getHid());
                                    memberLevelSearch.setHotelGroupId(user.getHotelGroupId());
                                    memberLevelSearch.setConditionType(1);
                                    List<CardMemberLevel> cardMemberLevels = cardMemberLevelDao.selectBySearch(memberLevelSearch);
                                    if (CollectionUtils.isEmpty(cardMemberLevels)) {
                                        log.warn("会员间夜数升级策略未设置");
                                    } else {
                                        Collections.sort(cardMemberLevels, (c1, c2) -> {
                                            return c2.getConditionValue().compareTo(c1.getConditionValue()); // 降序排列
                                        });
                                        for (CardMemberLevel memberLevel : cardMemberLevels) {
                                            if (dayCount >= memberLevel.getConditionValue() * 100) {
                                                CardInfo cardInfo = cardInfoDao.selectById(regist.getMemberId());
                                                log.info("会员间夜数满足条件，自动升级或降级会员等级：{}->{}", cardInfo.getCardLevel(), memberLevel.getLevelName());
                                                cardInfo.setCardLevelId(memberLevel.getId());
                                                cardInfo.setCardLevel(memberLevel.getLevelName());
                                                cardInfoDao.update(cardInfo);
                                                break;
                                            }
                                        }
                                    }
                                }
                                AccountSearch aSearch = new AccountSearch();
                                aSearch.setRegistIds(String.join(",", regists.stream().map(r -> r.getRegistId() + "").collect(Collectors.toList())));
                                aSearch.setPayType(1);
                                List<Account> accounts = accountDao.selectBySearch(aSearch);
                                Integer priceCount = 0;
                                for (Account a : accounts) {
                                    priceCount += a.getPrice();
                                }
                                if (priceCount > 0) {
                                    CardMemberLevelSearch memberLevelSearch = new CardMemberLevelSearch();
                                    memberLevelSearch.setHid(user.getHid());
                                    memberLevelSearch.setHotelGroupId(user.getHotelGroupId());
                                    memberLevelSearch.setConditionType(2);
                                    List<CardMemberLevel> cardMemberLevels = cardMemberLevelDao.selectBySearch(memberLevelSearch);
                                    if (CollectionUtils.isEmpty(cardMemberLevels)) {
                                        log.warn("会员消费升级策略未设置");
                                    } else {
                                        Collections.sort(cardMemberLevels, (c1, c2) -> {
                                            return c2.getConditionValue().compareTo(c1.getConditionValue()); // 降序排列
                                        });
                                        for (CardMemberLevel memberLevel : cardMemberLevels) {
                                            if (priceCount >= memberLevel.getConditionValue() * 100) {
                                                CardInfo cardInfo = cardInfoDao.selectById(regist.getMemberId());
                                                log.info("会员消费满足条件，自动升级或降级会员等级：{}->{}", cardInfo.getCardLevel(), memberLevel.getLevelName());
                                                cardInfo.setCardLevelId(memberLevel.getId());
                                                cardInfo.setCardLevel(memberLevel.getLevelName());
                                                cardInfoDao.update(cardInfo);
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }catch (Exception e){
                    log.error("会员自动升级判断异常",e);
                }
            });
        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public ResponseData personCheckout(CheckoutParam request, TbUserSession user) throws Exception {
        ResponseData responseData = new ResponseData(ER.SUCC);
        if (request.getRegistId() < 1) {
            throw new Exception("订单编号不能空");
        }
        Regist regist = registDao.selectById(request.getRegistId());

        if (regist.getState() == 1 || regist.getState() == 3) {
            throw new Exception("当前订单状态不能结账");
        }

        ArrayList<Account> accountList = new ArrayList<>();
        ArrayList<RegistPerson> registPeopleList = new ArrayList<>();

        if (request.getPersons() == null || request.getPersons().size() < 1) {
            throw new Exception("需要结账的宾客信息不能空");
        }

        int isOtherGuest = 0;

        int cash = 0;
        int cost = 0;

        for (int i = 0; i < request.getPersons().size(); i++) {
            int registPersonId = request.getPersons().get(i);
            //判断账务是否持平
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setRegistPersonId(registPersonId);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);
            for (int j = 0; j < accounts.size(); j++) {
                Account account = accounts.get(j);
                if (account.getRegistState() != 0 || account.getIsCancel() != 0) {
                    continue;
                }
                if (account.getPayType() == 1) {
                    cost += account.getPrice();
                }
                if (account.getPayType() == 2) {
                    cash += account.getPrice();
                }
                accountList.add(account);
            }
            RegistPerson registPerson = registPersonDao.selectById(registPersonId);
            if (registPerson.getRegistState() != 0) {
                throw new Exception("当前宾客状态异常");
            }
            Integer isOther = registPerson.getIsOther();
            if (isOther == 0) {
                isOtherGuest++;
            }
            registPeopleList.add(registPerson);
        }

        if (cash != cost) {
            throw new Exception("账务不平无法办理退房");
        }

        RegistPerson registPerson = null;

        if (isOtherGuest > 0) {
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(request.getRegistId());
            registPersonSearch.setRegistState(0);
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);
            for (int i = 0; i < request.getPersons().size(); i++) {
                for (int j = 0; j < registPeople.size(); j++) {
                    if (request.getPersons().get(i) != registPeople.get(j).getRegistPersonId()) {
                        registPerson = registPeople.get(j);
                    }
                }
            }
        }
        checkInTransactionService.personCheckout(accountList, registPeopleList, registPerson,user.getHid(),user.getPhone());
        return responseData;
    }

    @Override
    public ResponseData cancelRegistFunc(CheckoutParam checkoutParam) {

        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = checkoutParam.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            //操作日志
            Oprecord oprecord = new Oprecord(user);

            if (checkoutParam.getRegistId() == null) {
                throw new Exception("登记单号不能空");
            }
            int registId = checkoutParam.getRegistId();

            Regist regist = registDao.selectById(registId);

            // 营业日期不一致 ，不允许取消
            if (!regist.getBusinessDay().equals(user.getBusinessDay())) {
                throw new Exception("营业日期不一致，不允许取消");
            }

            if (regist.getState() != 0) {
                throw new Exception("当前订单不允许取消入住");
            }

            Date date = new Date();

            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setRegistId(registId);
            accountSearch.setIsCancel(0);

            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            if (accounts.size() > 0) {
                throw new Exception("有账务不允许取消");
            }

            // 登记人
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(registId);
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);

            List<RegistPerson> registPeople1 = new ArrayList<>();
            for (RegistPerson rp : registPeople) {
                rp.setRegistState(1);
                registPeople1.add(rp);
            }

            // 查询当前房间的辅助房态，删除所有
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setHid(user.getHid());
            roomAuxiliaryRelationSearch.setRegistId(regist.getRegistId());
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);

            String ralrStr = "";

            if (roomAuxiliaryRelations.size() > 0) {
                for (RoomAuxiliaryRelation rar : roomAuxiliaryRelations) {
                    ralrStr += rar.getRelationId();
                    ralrStr += ",";
                }
                ralrStr = ralrStr.substring(0, ralrStr.length() - 1);
            }
            regist.setRoomAuxiliaryRecord(ralrStr);


            // 预订单信息
            Integer bookingOrderId = regist.getBookingOrderId();

            BookingOrder bookingOrder = null;

            BookingOrderRoomNum bookingOrderRoomNum = null;

            if (bookingOrderId != null && bookingOrderId > 0) {

                bookingOrder = bookingOrderDao.selectById(bookingOrderId);
                if (bookingOrder == null) {
                } else {

                    // 查询当前预订单下是否有登记单信息。
                    RegistSearch registSearch = new RegistSearch();
                    registSearch.setHid(user.getHid());
                    registSearch.setBookingOrderId(bookingOrderId);

                    List<Regist> regists = registDao.selectBySearch(registSearch);
                    Integer size = regists.size();

                    Integer roomCount = bookingOrder.getRoomCount();

                    // 如果登记单信息 为1 ，则改成有效
                    if (size == 1) {

                        bookingOrder.setOrderStatus(BOOK.STA_YX);

                    } else if (size.equals(roomCount)) {

                        bookingOrder.setOrderStatus(BOOK.STA_BFRZ);

                    }
                    bookingOrder.setOtaStatus(HmhOrderStatusEnum.ORDER_CANCELLED.getType());
                    BookingOrderRoomNumSearch bookingOrderRoomNumSearch = new BookingOrderRoomNumSearch();
                    bookingOrderRoomNumSearch.setHid(user.getHid());
                    bookingOrderRoomNumSearch.setRegistId(registId);

                    Page<BookingOrderRoomNum> bookingOrderRoomNums = bookingOrderRoomNumDao.selectBySearch(bookingOrderRoomNumSearch);

                    if (bookingOrderRoomNums != null && bookingOrderRoomNums.size() > 0) {
                        bookingOrderRoomNum = bookingOrderRoomNums.get(0);
                        bookingOrderRoomNum.setIsCheckin(0);
                    }

                    //推送OTA订单状态信息
                    otaChangePushService.pushOrderChange(bookingOrder);
                }

            }

            RoomInfo roomInfo = new RoomInfo();
            roomInfo.setRoomInfoId(regist.getRoomNumId());
            roomInfo.setRoomNumState(ROOM_STATUS.VD);

            RoomRepairRecordHistory roomRepairRecord = new RoomRepairRecordHistory();

            roomRepairRecord.setRoomId(roomInfo.getRoomInfoId());
            roomRepairRecord.setRoomNum(roomInfo.getRoomNum());
            roomRepairRecord.setHid(regist.getHid());
            roomRepairRecord.setHotelGroupId(regist.getHotelGroupId());
            roomRepairRecord.setClassId(user.getClassId());
            roomRepairRecord.setCreateUserId(user.getUserId());
            roomRepairRecord.setCreateUserName(user.getUserName());
            roomRepairRecord.setCreateTime(date);
            roomRepairRecord.setUpdateUserId(user.getUserId());
            roomRepairRecord.setUpdateUserName(user.getUserName());
            roomRepairRecord.setUpdateTime(date);
            roomRepairRecord.setType(RoomUtils.getRoomRepairRecordType(ROOM_STATUS.OCC, ROOM_STATUS.VD));
            roomRepairRecord.setState(RoomUtils.getRoomRepairRecordState(ROOM_STATUS.OCC, ROOM_STATUS.VD));
            roomRepairRecord.setBusinessDay(user.getBusinessDay());
            roomRepairRecord.setDes(roomInfo.getRoomNum() + "取消入住。");

            oprecord.setRegistId(registId);
            oprecord.setMainId(regist.getSn());
            oprecord.setRoomNum(regist.getRoomNum());
            oprecord.setDescription(regist.getRoomNum() + " 取消入住。");


            checkOutTransactionService.registCancel(regist, roomInfo, registPeople1, roomAuxiliaryRelations, bookingOrder, bookingOrderRoomNum, roomRepairRecord);

            //推送OTA房型价态量信息
            otaChangePushService.pushNumChange(bookingOrder.getHid(), Arrays.asList(roomInfo.getRoomTypeId()));
            CheckOutServiceImpl checkOutService = this;

            final List<Regist> resList = new ArrayList<>();
            resList.add(regist);

            final Map<Integer, List<RegistPerson>> persons = new HashMap<>();
            persons.put(registId, registPeople);

            final String s = regist.getRoomNum();

            HotelUtils.cachedThreadPool.execute(new Runnable() {
                @Override
                public void run() {

                    try {

                        HashMap<String, String> fieledMap = new HashMap<>();

                        HashOperations<String, Object, Object> stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();

                        checkOutService.updateRoomListForCache(user);

                       turnAlwaysService.turnAlwaysCacheFunc(user);

                        fieledMap.put("roomList", s);

                        baseService.push(user.getHotelGroupId(), user.getHid(), 31, fieledMap, new HashMap<String, String>(), true, true);
                    } catch (Exception e) {

                        log.info("-------------------结账线程处理报错-begin----------------");
                        log.error("",e);
                        log.info("-------------------结账线程处理报错-end----------------");
                    }
                }
            });


            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    for (int i = 0; i < resList.size(); i++) {
                        Regist regist = resList.get(i);
                        SmartLockRequest smartLockRequest = new SmartLockRequest();
                        smartLockRequest.setSessionToken(sessionToken);
                        smartLockRequest.setRegistId(regist.getRegistId());
                        smartLockRequest.setCheckinTime(regist.getCheckinTime());
                        smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                        smartLockRequest.setRoomTypeId(regist.getRoomTypeId());
                        smartLockRequest.setRoomTypeName(regist.getRoomTypeName());
                        smartLockRequest.setRoomNumId(regist.getRoomNumId());
                        smartLockRequest.setRoomNum(regist.getRoomNum());
                        smartLockRequest.setHid(regist.getHid());
                        smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                        smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                        smartLockRequest.setCheckinType(regist.getCheckinType());
                        smartLockRequest.setBussType(2);
                        List<RegistPerson> registPeople = persons.get(regist.getRegistId());
                        List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                        for (int j = 0; j < registPeople.size(); j++) {
                            RegistPerson registPersonInfo = registPeople.get(j);
                            SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                            BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                            peoplesList.add(registPersonDetail);
                        }
                        smartLockRequest.setPeoples(peoplesList);
                        baseService.SmartLockCheckout(smartLockRequest);
                    }
                }
            });

            this.addOprecords(oprecord);
            //取消入住 调用退房接口
            IotHotelCheckOut checkOut = new IotHotelCheckOut();
            checkOut.setCode(registId + "");
            HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
            Map<String, String> authParamMap = new HashMap<>();
            authParamMap.put("eid", user.getHid().toString());
            authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
            authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
            authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
            try {
                authParamMap.put("phone", AESUtil.encryptAesCbc(user.getPhone(),iotPlatConfig.getSassTokenSecret()));
            } catch (Exception e) {
                log.error("手机号加密失败");
            }
            hotelIotStrategy.initStrategy(authParamMap);
            webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.CHECK_OUT,
                    checkOut,
                    hotelIotStrategy);

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData onAccount(CheckoutParam checkoutParam) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = checkoutParam.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);
            //操作日志
            ArrayList<Oprecord> oprecords = new ArrayList<>();
            Oprecord oprecord = new Oprecord(user);

            if (checkoutParam.getRegistId() == null) {
                throw new Exception("登记单号不能空");
            }
            int registId = checkoutParam.getRegistId();

            Regist regist = registDao.selectById(registId);

            if (regist.getState() != 0) {
                throw new Exception("当前订单不允许挂账");
            }

            /**
             * 修改登记表中状态为挂账未结
             */
            regist.setState(2);
            //修改挂帐时间为当前时间
            regist.setCheckoutBusinessDay(user.getBusinessDay());
            regist.setCheckoutClassId(user.getClassId());
            regist.setSettleAccountTime(new Date());

            RoomInfo roomInfo = roomInfoDao.selectById(regist.getRoomNumId());
            if (roomInfo.getRoomNumState() != 3 && roomInfo.getRoomNumState() != 4) {
                throw new Exception("当前房间不允许挂账");
            }

            //查询所有在住的宾客集合，全部改成挂账状态

            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistState(0);
            registPersonSearch.setRegistId(registId);
            List<RegistPerson> registPersonList = registPersonDao.selectBySearch(registPersonSearch);
            for (int i = 0; i < registPersonList.size(); i++) {
                RegistPerson registPerson = registPersonList.get(i);
                registPerson.setRegistState(2);
            }

            // 查询当前房间的辅助房态，删除所有
            RoomAuxiliaryRelationSearch roomAuxiliaryRelationSearch = new RoomAuxiliaryRelationSearch();
            roomAuxiliaryRelationSearch.setRoomId(roomInfo.getRoomInfoId());
            List<RoomAuxiliaryRelation> roomAuxiliaryRelations = roomAuxiliaryRelationDao.selectBySearch(roomAuxiliaryRelationSearch);


            // 修改的钟点房
            ArrayList<HourRoomDayUse> upaHourUse = new ArrayList<>();

            // 删除的钟点房房情
            ArrayList<HourRoomDayUse> delHourUse = new ArrayList<>();
            // 钟点房验证未来房情
            if(regist.getCheckinType()==2){
                Integer start = HotelUtils.parseDate2Int(regist.getCheckinTime());
                Integer end = HotelUtils.parseDate2Int(regist.getCheckoutTime());

                int startHours = regist.getCheckinTime().getHours();
                int endHours = regist.getCheckoutTime().getHours();


                Date date = new Date();
                Integer nowDay = HotelUtils.parseDate2Int(new Date());
                // 当前时间大于退房时间的小时。不处理
                if (nowDay >= end && date.getHours() >= endHours) {

                }else {


                    // 判断开始日期和结束日期是否一致
                    if (!start.equals(end)) {


                        HourRoomDayUseSearch hourRoomDayUseSearch = new HourRoomDayUseSearch();
                        hourRoomDayUseSearch.setHid(user.getHid());
                        hourRoomDayUseSearch.setRoomInfoId(regist.getRoomNumId());
                        hourRoomDayUseSearch.setBusinessDay(end);

                        Page<HourRoomDayUse> hourRoomDayUses = hourRoomDayUseDao.selectBySearch(hourRoomDayUseSearch);

                        if (hourRoomDayUses.size() > 0) {

                            HourRoomDayUse hourRoomDayUse = hourRoomDayUses.get(0);

                            String useMsg = hourRoomDayUse.getUseMsg();

                            String[] split = useMsg.split(",");

                            Boolean isUpa = false;

                            String newUseStr = "";

                            for (int i = 0; i < split.length; i++) {

                                int i1 = Integer.parseInt(split[i]);

                                if (i1 <= endHours) {
                                    continue;
                                }
                                isUpa = true;

                                newUseStr += split[i];
                                newUseStr += ",";

                            }

                            if (isUpa) {
                                hourRoomDayUse.setUseMsg(newUseStr.substring(0, newUseStr.length() - 1));
                                upaHourUse.add(hourRoomDayUse);
                            } else {
                                delHourUse.add(hourRoomDayUse);
                            }

                        }

                        endHours = 23;

                    }


                    HourRoomDayUseSearch hourRoomDayUseSearch = new HourRoomDayUseSearch();
                    hourRoomDayUseSearch.setHid(user.getHid());
                    hourRoomDayUseSearch.setRoomInfoId(regist.getRoomNumId());
                    hourRoomDayUseSearch.setBusinessDay(start);

                    Page<HourRoomDayUse> hourRoomDayUses = hourRoomDayUseDao.selectBySearch(hourRoomDayUseSearch);


                    if (hourRoomDayUses.size() > 0) {

                        HourRoomDayUse hourRoomDayUse = hourRoomDayUses.get(0);

                        // 查询使用记录
                        String useMsg = hourRoomDayUse.getUseMsg();

                        String[] split = useMsg.split(",");

                        Boolean isUpa = false;

                        String newUseStr = "";

                        for (int i = 0; i < split.length; i++) {

                            int i1 = Integer.parseInt(split[i]);

                            if (i1 >= startHours && i1 <= endHours) {
                                continue;
                            }
                            isUpa = true;

                            newUseStr += split[i];
                            newUseStr += ",";

                        }

                        if (isUpa) {
                            hourRoomDayUse.setUseMsg(newUseStr.substring(0, newUseStr.length() - 1));

                            upaHourUse.add(hourRoomDayUse);
                        } else {
                            delHourUse.add(hourRoomDayUse);
                        }

                    }

                }


            }



            HotelUtils.smartlockThreadPool.execute(new Runnable() {
                @Override
                public void run() {
                    SmartLockRequest smartLockRequest = new SmartLockRequest();
                    smartLockRequest.setSessionToken(sessionToken);
                    smartLockRequest.setRegistId(regist.getRegistId());
                    smartLockRequest.setCheckinTime(regist.getCheckinTime());
                    smartLockRequest.setCheckoutTime(regist.getCheckoutTime());
                    smartLockRequest.setRoomTypeId(regist.getRoomTypeId());
                    smartLockRequest.setRoomTypeName(regist.getRoomTypeName());
                    smartLockRequest.setRoomNumId(regist.getRoomNumId());
                    smartLockRequest.setRoomNum(regist.getRoomNum());
                    smartLockRequest.setHid(regist.getHid());
                    smartLockRequest.setHotelGroupId(regist.getHotelGroupId());
                    smartLockRequest.setBreakfastNum(regist.getBreakfastNum());
                    smartLockRequest.setCheckinType(regist.getCheckinType());
                    smartLockRequest.setBussType(2);
                    List<SmartLockRequest.RegistPerson> peoplesList = new ArrayList<SmartLockRequest.RegistPerson>();
                    for (int j = 0; j < registPersonList.size(); j++) {
                        RegistPerson registPersonInfo = registPersonList.get(j);
                        SmartLockRequest.RegistPerson registPersonDetail = new SmartLockRequest.RegistPerson();
                        BeanUtils.copyProperties(registPersonInfo, registPersonDetail);
                        peoplesList.add(registPersonDetail);
                    }
                    smartLockRequest.setPeoples(peoplesList);
                    baseService.SmartLockCheckout(smartLockRequest);
                }
            });

            /**
             * 挂账后转成脏房
             */
            roomInfo.setRoomNumState(2);
            checkOutTransactionService.onAccount(regist, roomInfo, registPersonList, user, oprecords, oprecord, roomAuxiliaryRelations,upaHourUse,delHourUse);
            this.updateRoomListForCache(user);
            this.addOprecords(oprecords);

            IotHotelCheckOut checkOut = new IotHotelCheckOut();
            checkOut.setCode(regist.getRegistId() + "");
            HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
            Map<String, String> authParamMap = new HashMap<>();
            authParamMap.put("eid",user.getHid().toString());
            authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
            authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
            authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
            try {
                authParamMap.put("phone", AESUtil.encryptAesCbc(user.getPhone(),iotPlatConfig.getSassTokenSecret()));
            } catch (Exception e) {
                log.error("手机号加密失败");
            }
            hotelIotStrategy.initStrategy(authParamMap);
            webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.CHECK_OUT,
                    checkOut,
                    hotelIotStrategy
            );

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * 更新缓存中的房间信息
     *
     * @param user
     * @return
     */
    public List<RoomInfo> updateRoomListForCache(final TbUserSession user) {

        RoomInfoSearch roomInfoSearch = new RoomInfoSearch();
        roomInfoSearch.setHid(user.getHid());

        List<RoomInfo> roomInfos = roomInfoDao.selectBySearch(roomInfoSearch);

        final HashOperations<String, Object, Object> ops = stringRedisTemplate.opsForHash();

        String key = ECache.ROOM_LIST + "_" + user.getHid();
        JSONArray noGroupList = new JSONArray();
        JsonConfig jsonConfig = new JsonConfig();
        jsonConfig.registerJsonValueProcessor(Date.class,
                new JsonDateValueProcessor());
        ops.put(ECache.ROOM_LIST, key, JSONArray.fromObject(roomInfos,jsonConfig).toString());

        return roomInfos;

    }

    /**
     * 更新会员入住天数
     */
    public void updateVipCheckInNum(final ArrayList<Regist> regists, final TbUserSession user) {

        HashMap<Integer, CardInfo> cardInfoHashMap = new HashMap<>();

        ArrayList<CardCheckinRecord> cardCheckinRecords = new ArrayList<>();

        CardCheckinRecordSearch cardCheckinRecordSearch = new CardCheckinRecordSearch();
        cardCheckinRecordSearch.setHid(user.getHid());

        for (Regist regist : regists) {

            Integer memberId = regist.getMemberId();
            // 查询会员信息
            CardInfo cardInfo = cardInfoHashMap.get(memberId);
            if (cardInfo == null) {
                CardInfo cardInfo1 = cardInfoDao.selectById(memberId);
                if (cardInfo1 == null) {
                    continue;
                }
                cardInfo = cardInfo1;
                cardInfoHashMap.put(memberId, cardInfo1);
            }

            CardCheckinRecord cardCheckinRecord = new CardCheckinRecord();

            // 结账班次不等于0 ，则说明已经结过账反结
            if (regist.getCheckoutClassId() != 0) {
                cardCheckinRecordSearch.setRegistId(regist.getRegistId());
                Page<CardCheckinRecord> cardCheckinRecords1 = cardCheckinRecordDao.selectBySearch(cardCheckinRecordSearch);
                if (cardCheckinRecords1.size() > 0) {
                    cardCheckinRecord = cardCheckinRecords1.get(0);
                }
            } else {
                cardCheckinRecord.setHid(user.getHid());
                cardCheckinRecord.setHotelGroupId(user.getHotelGroupId());
                cardCheckinRecord.setRegistId(regist.getRegistId());
                cardCheckinRecord.setBookingId(regist.getBookingOrderId());
            }
            cardCheckinRecord.setHotelGroupId(user.getHotelGroupId());
            cardCheckinRecord.setHid(user.getHid());
            cardCheckinRecord.setRoomInfoId(regist.getRoomNumId());
            cardCheckinRecord.setRoomNo(regist.getRoomNum());
            cardCheckinRecord.setRoomTypeId(regist.getRoomTypeId());
            cardCheckinRecord.setRoomTypeName(regist.getRoomTypeName());
            cardCheckinRecord.setCardGroupId(cardInfo.getCardGroupId());
            cardCheckinRecord.setCardId(cardInfo.getId());
            cardCheckinRecord.setCardGroupLevelId(cardInfo.getCardGroupLevelId());
            cardCheckinRecord.setCardGroupTypeId(cardInfo.getCardGroupTypeId());
            cardCheckinRecord.setCardLevelId(cardInfo.getCardLevelId());
            cardCheckinRecord.setCardTypeId(cardInfo.getCardTypeId());

            cardCheckinRecord.setCheckInTime(regist.getCheckinTime());
            cardCheckinRecord.setCheckOutTime(regist.getSettleAccountTime());

            List betweenDates = HotelUtils.getBetweenDates(regist.getCheckinTime(), regist.getSettleAccountTime());
            cardCheckinRecord.setDayNum(betweenDates.size());

            cardCheckinRecords.add(cardCheckinRecord);
        }

        for (CardCheckinRecord cardCheckinRecord : cardCheckinRecords) {

            if (cardCheckinRecord.getId() != null) {
                cardCheckinRecordDao.update(cardCheckinRecord);
            } else {
                cardCheckinRecordDao.insert(cardCheckinRecord);
            }

        }

    }

    /**
     * 根据佣金类型计算提成金额
     *
     * @param salesHotelCommissionDetails
     * @param salesHotelCommissionType
     * @param sumMoney                    总消费金额
     * @return
     */
   /* public SalesHotelCommissionDetails handleCommission(SalesHotelCommissionDetails salesHotelCommissionDetails, SalesHotelCommissionType salesHotelCommissionType, Integer sumMoney) {

        //佣金类型ID
        Integer commissionTypeId = salesHotelCommissionType.getCommissionTypeId();

        //提成
        Integer commissionRate = salesHotelCommissionDetails.getCommissionRate();

        //最高佣金金额
        Integer maxCommissionMoney = salesHotelCommissionType.getMaxCommissionMoney();
        //最低佣金金额
        Integer minCommissionMoney = salesHotelCommissionType.getMinCommissionMoney();
        //最低消费金额
        Integer minConsumptionMoney = salesHotelCommissionType.getMinConsumptionMoney();

        //如果总消费金额小于最低消费金额，则不添加提成
        if (minConsumptionMoney > sumMoney) {
            salesHotelCommissionDetails.setCommissionMoney(0);
            return salesHotelCommissionDetails;
        }

        //佣金金额
        int commissionMoney = 0;

        switch (commissionTypeId) {
            case 1:
                //房间总消费的百分比为佣金金额
                commissionMoney = sumMoney * commissionRate / 100;
                break;
            case 2:
                //每天固定佣金金额
                commissionMoney = salesHotelCommissionDetails.getAcount() * commissionRate;
                break;
            case 3:
                //不论住多久，房价，佣金金额都固定
                commissionMoney = commissionRate;
                break;
            case 4:
                //只统计当前销售人员售卖房间的次数
                commissionMoney = 0;
                break;
        }

        //判断佣金金额
        if (commissionMoney > maxCommissionMoney) {
            commissionMoney = maxCommissionMoney;
        } else if (minCommissionMoney > commissionMoney) {
            commissionMoney = minCommissionMoney;
        }
        salesHotelCommissionDetails.setCommissionMoney(commissionMoney);
        return salesHotelCommissionDetails;
    }*/
    @Override
    public ResponseData checkoutAccount(CheckoutParam checkoutParam) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            /*1.获取用户信息*/
            String sessionToken = checkoutParam.getSessionToken();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            Integer registId = checkoutParam.getRegistId();

            HashMap<String, Object> res = new HashMap<>();
            res.put("canCheckout", 0);
            // 是否可挂账
            Boolean canAr = false;

            Integer sumPay = 0;
            Integer sumSale = 0;

            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setRegistId(registId);

            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);

            if (bookingOrderConfigs != null && bookingOrderConfigs.size() > 0) {
                BookingOrderConfig bookingOrderConfig = bookingOrderConfigs.get(0);
                canAr = bookingOrderConfig.getAutoAr() == 1;
            }

            // 1.查询账务信息

            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setHid(user.getHid());
            accountSearch.setRegistState(0);
            accountSearch.setRegistId(registId);

            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            // 总房费信息
            Integer sumRoomSale = 0;

            // 可用退款金额
            Integer canRefundMoney = 0;

            // 9320-微信支付 9300-支付宝支付 9100-国内卡预授权 9620-会员储值卡冻结 9800-协议挂账
            // 可以退款的账务信息 微信支付宝
            ArrayList<Account> refundList = new ArrayList<>();

            // 预授权
            ArrayList<Account> ysqList = new ArrayList<>();

            // 会员预授权
            ArrayList<Account> vipysqList = new ArrayList<>();

            // 挂账信息
            ArrayList<Account> arList = new ArrayList<>();
            Integer arMoney = 0;

            SortedMap<String, Account> otherAccountMap = new TreeMap<>();

            // 账务信息
            for (Account account : accounts) {
                Integer payType = account.getPayType();

                // 消费
                if (payType == 1) {

                    sumSale += account.getPrice();

                    //  房费
                    if (1 == account.getAccountType()) {

                        sumRoomSale += account.getPrice();

                    }

                    continue;

                }

                // 付款
                sumPay += account.getPrice();

                String payCodeId = account.getPayCodeId();

                if ("9800".equals(payCodeId)) {
                    arMoney += account.getPrice();
                    arList.add(account);
                }

                if (account.getPrice() < 0) {
                    continue;
                }

                // 已付款的类型

                // 后期未可用的退款金额
                Integer thirdRefundState = account.getThirdRefundState();
                if (thirdRefundState == null || 0 != thirdRefundState) {

                    Account account1 = otherAccountMap.get(payCodeId);
                    if (account1 == null) {
                        account1 = new Account();
                        account1.setPayCodeId(account.getPayCodeId());
                        account1.setPayCodeName(account.getPayCodeName());
                        account1.setPayClassId(account.getPayClassId());
                        account1.setPayClassName(account.getPayClassName());
                        account1.setPrice(0);
                    }

                    account1.setPrice(account1.getPrice() + account.getPrice());

                    otherAccountMap.put(payCodeId, account1);


                } else {
                    canRefundMoney += account.getPrice();
                    switch (payCodeId) {
                        case "9320":
                            refundList.add(account);
                            break;
                        case "9300":
                            refundList.add(account);
                            break;
                        case "9100":
                            ysqList.add(account);
                            break;
                        case "9620":
                            vipysqList.add(account);
                            break;
                    }
                }


            }

            JSONArray resAccountList = new JSONArray();

            int canArMoney = 0;
            if (canAr) {
                canArMoney = sumRoomSale - arMoney;

                if (canArMoney < 0) {
                    canArMoney = 0;
                }

                JSONObject acn = new JSONObject();
                acn.put("money", canArMoney);
                acn.put("payCodeId", "9800");
                acn.put("payCodeName", "AR账");
                acn.put("payClassId", "9");
                acn.put("payClassName", "AR账");
                acn.put("type", 1);
                acn.put("accountId", "");

                resAccountList.add(acn);

            }

            // 消费和付款持平 则提示可以结账
            int roomDiff = sumPay - sumSale + canArMoney;
            if (sumPay - sumSale == 0) {

                // 可结账
                res.put("canCheckout", 1);
                responseData.setData(res);
                return responseData;

            }


            // 如果可以挂账

            // type 1.挂账 2.微信退款 3.支付宝退款  4.会员储值卡冻结 5.预授权 6.正常的按照返回的费用码录入


            // 2.会员冻结完成
            for (Account account : vipysqList) {

                Integer price = account.getPrice();

                Integer refundMoney = 0;

                if (price >= roomDiff) {
                    refundMoney = roomDiff;
                    roomDiff = 0;
                } else {

                    roomDiff -= price;
                    refundMoney = price;

                }

                JSONObject acn = new JSONObject();
                acn.put("money", refundMoney);
                acn.put("payCodeId", "9620");
                acn.put("payMoney", account.getPrice());
                acn.put("payCodeName", "会员储值卡冻结");
                acn.put("payClassId", "5");
                acn.put("payClassName", "会员");
                acn.put("accountId", account.getAccountId());
                acn.put("type", 4);

                resAccountList.add(acn);

            }

            // 3.预授权完成
            for (Account account : ysqList) {

                Integer price = account.getPrice();

                Integer refundMoney = 0;

                if (price >= roomDiff) {
                    refundMoney = roomDiff;
                    roomDiff = 0;
                } else {

                    roomDiff -= price;
                    refundMoney = price;

                }

                JSONObject acn = new JSONObject();
                acn.put("money", refundMoney);
                acn.put("payMoney", account.getPrice());
                acn.put("payCodeId", "9100");
                acn.put("payCodeName", "国内卡-预授权");
                acn.put("payClassId", "3");
                acn.put("payClassName", "国内卡");
                acn.put("accountId", account.getAccountId());
                acn.put("type", 5);

                resAccountList.add(acn);

            }

            // 4.微信、支付宝退款
            for (Account account : refundList) {

                Integer price = account.getPrice();

                if (roomDiff == 0) {
                    continue;
                }

                Integer refundMoney = 0;

                if (price >= roomDiff) {
                    refundMoney = roomDiff;
                    roomDiff = 0;
                } else {
                    roomDiff -= price;
                    refundMoney = price;

                }

                JSONObject acn = new JSONObject();
                acn.put("money", refundMoney);
                acn.put("payMoney", account.getPrice());
                acn.put("accountId", account.getAccountId());

                if (account.getPayClassId() == 11) { //支付宝
                    acn.put("type", 3);
                    acn.put("payCodeId", "9309");
                    acn.put("payCodeName", "支付宝退款");
                    acn.put("payClassId", "11");
                    acn.put("payClassName", "支付宝");
                } else { // 微信
                    acn.put("type", 2);
                    acn.put("payCodeId", "9329");
                    acn.put("payCodeName", "微信扫码支付退款");
                    acn.put("payClassId", "12");
                    acn.put("payClassName", "微信");
                }

                resAccountList.add(acn);

            }


            // 遍历剩余的付款金额
            Set<String> strings = otherAccountMap.keySet();

            for (String skeys : strings) {

                Account account = otherAccountMap.get(skeys);

                Integer price = account.getPrice();

                if (roomDiff == 0) {
                    continue;
                }

                Integer refundMoney = 0;

                if (price >= roomDiff) {
                    refundMoney = roomDiff;
                    roomDiff = 0;
                } else {
                    roomDiff -= price;
                    refundMoney = price;

                }

                Integer payClassId = account.getPayClassId();
                JSONObject acn = new JSONObject();
                acn.put("money", refundMoney);
                acn.put("type", "6");
                switch (payClassId) {
                    case 1:
                        acn.put("payCodeId", "9001");
                        acn.put("payCodeName", "现金退款");
                        acn.put("payClassId", "1");
                        acn.put("payClassName", "现金");
                        break;
                    case 11:
                        acn.put("payCodeId", "9309");
                        acn.put("payCodeName", "支付宝退款");
                        acn.put("payClassId", "11");
                        acn.put("payClassName", "支付宝");
                        break;
                    case 12:
                        acn.put("payCodeId", "9329");
                        acn.put("payCodeName", "微信扫码支付退款");
                        acn.put("payClassId", "12");
                        acn.put("payClassName", "微信");
                        break;
                    default:
                        acn.put("accountId", "");
                        acn.put("payCodeId", account.getPayCodeId());
                        acn.put("payCodeName", account.getPayCodeName());
                        acn.put("payClassId", account.getPayClassId());
                        acn.put("payClassName", account.getPayClassName());
                        break;
                }

                resAccountList.add(acn);

            }

            if (roomDiff > 0) {
                JSONObject acn = new JSONObject();
                acn.put("money", roomDiff);
                acn.put("type", "6");
                acn.put("payCodeId", "9001");
                acn.put("payCodeName", "现金退款");
                acn.put("payClassId", "1");
                acn.put("payClassName", "现金");
                resAccountList.add(acn);
            }


            res.put("accountList", resAccountList);
            responseData.setData(res);


        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData fastCheckout(CheckoutParam checkoutParam) {
        ResponseData responseData = new ResponseData(ER.SUCC);
        try {
            String tokenId = checkoutParam.getSessionToken();
            //获取tokenId
            final TbUserSession user = this.getTbUserSession(tokenId);

            Object registId = checkoutParam.getRegistId();
            if (registId == null) {
                throw new Exception(HOTEL_CONST.REGIST_IS_NULL);
            }

            Regist regist = registDao.selectById(Integer.parseInt(registId.toString()));

            if (regist == null || regist.getState() != 0) {
                throw new Exception("当前订单状态不允许结账");
            }

            Date date = new Date();

            // 1.配置信息
            BookingOrderConfig bookingOrderConfig = new BookingOrderConfig();
            BookingOrderConfigSearch bookingOrderConfigSearch = new BookingOrderConfigSearch();
            bookingOrderConfigSearch.setRegistId(regist.getRegistId());
            List<BookingOrderConfig> bookingOrderConfigs = bookingOrderConfigDao.selectBySearch(bookingOrderConfigSearch);
            if (bookingOrderConfigs != null && bookingOrderConfigs.size() > 0) {
                bookingOrderConfig = bookingOrderConfigs.get(0);
            }

            // 在住人
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setRegistId(regist.getRegistId());
            List<RegistPerson> registPeople = registPersonDao.selectBySearch(registPersonSearch);

            String peopleName = "";
            for (RegistPerson registPerson : registPeople) {

                peopleName += registPerson.getPersonName() + " ";

            }


            // 查询协议单位账户
            HotelCompanyAccount hotelCompanyAccount = hotelCompanyAccountDao.selectById(regist.getCompanyAccountId());

            // 房价码信息

            // 查询可挂账金额
            Integer sumArMoney = 0;

            // 所有的房费都已产生
            Boolean isAllCreate = true;

            BookingOrderDailyPriceSearch bookingOrderDailyPriceSearch = new BookingOrderDailyPriceSearch();
            bookingOrderDailyPriceSearch.setRegistId(regist.getRegistId());
            Page<BookingOrderDailyPrice> bookingOrderDailyPrices = bookingOrderDailyPriceDao.selectBySearch(bookingOrderDailyPriceSearch);


            for (BookingOrderDailyPrice bodp : bookingOrderDailyPrices) {

                Integer dailyState = bodp.getDailyState();

                if (dailyState == 0) {

                    sumArMoney += bodp.getPrice();

                } else {
                    isAllCreate = false;
                }

            }

            if (!isAllCreate) {
                sumArMoney += bookingOrderDailyPrices.get(0).getPrice();
            }


            // 2.查询账务信息
            AccountSearch accountSearch = new AccountSearch();
            accountSearch.setRegistId(regist.getRegistId());
            accountSearch.setRegistState(0);
            accountSearch.setIsCancel(0);
            List<Account> accounts = accountDao.selectBySearch(accountSearch);

            // 总付款
            Integer sumPay = 0;
            // 总消费
            Integer sumSale = 0;

            // 总房费信息
            Integer sumRoomSale = 0;

            // 可用退款金额
            Integer canRefundMoney = 0;

            // 9320-微信支付 9300-支付宝支付 9100-国内卡预授权 9620-会员储值卡冻结 9800-协议挂账
            // 可以退款的账务信息 微信支付宝
            ArrayList<Account> refundList = new ArrayList<>();

            // 预授权
            ArrayList<Account> ysqList = new ArrayList<>();

            // 会员预授权
            ArrayList<Account> vipysqList = new ArrayList<>();

            // 挂账信息
            ArrayList<Account> arList = new ArrayList<>();
            Integer arMoney = 0;

            // 其他付款方式
            HashMap<Integer, Integer> otherPay = new HashMap<>();
            otherPay.put(11, 0);
            otherPay.put(12, 0);
            otherPay.put(3, 0);
            otherPay.put(1, 0);

            Integer sumOtherMoeny = 0;

            // 账务信息
            for (Account account : accounts) {
                Integer payType = account.getPayType();

                // 消费
                if (payType == 1) {

                    sumSale += account.getPrice();

                    //  房费
                    if (1 == account.getAccountType()) {

                        sumRoomSale += account.getPrice();

                    }

                    continue;

                }
                // 付款
                sumPay += account.getPrice();

                String payCodeId = account.getPayCodeId();

                if ("9800".equals(payCodeId)) {
                    arMoney += account.getPrice();
                    arList.add(account);
                }

                Integer payClassId = account.getPayClassId();

                canRefundMoney += account.getPrice();

                Integer thirdRefundState = account.getThirdRefundState();

                if (thirdRefundState == null || 0 != thirdRefundState) {

                    switch (payClassId) {
                        case 1:
                            otherPay.put(1, otherPay.get(1) + canRefundMoney);
                            sumOtherMoeny += canRefundMoney;
                            break;
                        case 3:
                            otherPay.put(3, otherPay.get(3) + canRefundMoney);
                            sumOtherMoeny += canRefundMoney;
                            break;
                        case 11:
                            otherPay.put(11, otherPay.get(11) + canRefundMoney);
                            sumOtherMoeny += canRefundMoney;
                            break;
                        case 12:
                            otherPay.put(12, otherPay.get(12) + canRefundMoney);
                            sumOtherMoeny += canRefundMoney;
                            break;
                    }
                    continue;
                }

                switch (payCodeId) {
                    case "9320":
                        refundList.add(account);
                        break;
                    case "9300":
                        refundList.add(account);
                        break;
                    case "9100":
                        ysqList.add(account);
                        break;
                    case "9620":
                        vipysqList.add(account);
                        break;
                }
            }

            // 需要添加的账务信息
            ArrayList<Account> addAccounts = new ArrayList<>();

            Integer AddMoney = 0;
            Date date1 = new Date();
            Integer dayInt = HotelUtils.parseDate2Int(date1);

            Integer businessDay = user.getBusinessDay();
            Integer checkInBussday = regist.getBusinessDay();

            Boolean isNightRoom = false;

            // 判断是否是凌晨房
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setHid(user.getHid());
            hotelSettingByParamId.setParamId(HOTEL_SETTING.NIGHT_ROOM);

            Object minObj = this.findHotelSettingByParamId(hotelSettingByParamId);

            if (minObj != null) {
                int hour = Integer.parseInt(minObj.toString());

                int hours = regist.getCheckinTime().getHours();

                if (hour > hours) {
                    isNightRoom = true;
                }
            }

            // 如果营业日等于当前时间
            if ((dayInt.equals(businessDay) || dayInt > businessDay) && !checkInBussday.equals(businessDay)) {
                dayInt = HotelUtils.parseDate2Int(HotelUtils.addDayGetNewDate(date1, -1));
            }


            sumSale += AddMoney;
            sumRoomSale += AddMoney;


            // endregion


            // 计算是否可挂账,1可挂账
            Integer autoAr = bookingOrderConfig.getAutoAr();

            // 可以挂账金额
            Integer canArMoney = 0;

            // 可挂账
            if (autoAr == 1 && regist.getCompanyAccountId() != null) {

                // 判断当前还可以挂账多少。  房费减 - 当前挂账金额  = 可挂账金额
                canArMoney = sumRoomSale - arMoney;

                // 实际可挂账金额
                int realCanArMoney = sumArMoney - arMoney;

                if (canArMoney > realCanArMoney) {
                    throw new Exception("当前可用余额不足，结账失败");
                }

                if (canArMoney < 0) {
                    canArMoney = 0;
                }

            }

            sumPay += canArMoney;

            // 房费差额 如果付款小于消费 ，则提示报错
            int roomDiff = sumPay - sumSale;
            if (roomDiff + canArMoney < 0) {
                throw new Exception("可用付款不足，结账失败。");
            }

            // 退款金额 小于  可用退款金额+可挂账金额 则不可退款
            if (roomDiff > canRefundMoney + sumOtherMoeny) {
                throw new Exception("可用退款金额不足，请去前台处理。");
            }

            // region  进行退款操作
            ArrayList<Account> updateAccounts = new ArrayList<>();

            Oprecord oprecord = new Oprecord(user);
            oprecord.setRegistId(regist.getRegistId());
            oprecord.setRoomNum(regist.getRoomNum());
            oprecord.setBookingOrderId(regist.getBookingOrderId());
            oprecord.setOccurTime(HotelUtils.currentTime());

            // 1. 先把可挂房费 挂账
            ArrayList<AddArAccount> addArAccounts = new ArrayList<>();

            if (canArMoney > 0) {

                // 添加挂账信息
                Account arAccount = regAccounts(user, regist, registPeople.get(0), canArMoney, "9800", "AR帐", 9, "AR帐", 2);
                arAccount.setRegistState(1);

                HotelCompanyInfo hotelCompanyInfo = hotelCompanyInfoDao.selectById(hotelCompanyAccount.getHotelCompanyId());
                arAccount.setCompanyId(hotelCompanyAccount.getHotelCompanyId());
                arAccount.setCompanyName(hotelCompanyAccount.getName());

                HotelCompanyArRecode hotelCompanyArRecode = new HotelCompanyArRecode();

                hotelCompanyArRecode.setCompanyId(hotelCompanyAccount.getHotelCompanyId());
                hotelCompanyArRecode.setGroupCompanyId(hotelCompanyAccount.getGroupCompanyId());
                hotelCompanyArRecode.setCompanyAccountId(hotelCompanyAccount.getId());
                // 房间id
                hotelCompanyArRecode.setRoomInfoId(regist.getRoomNumId());
                // 房号
                hotelCompanyArRecode.setRoomNo(regist.getRoomNum());
                hotelCompanyArRecode.setMoney(canArMoney);
                // 房间id
                hotelCompanyArRecode.setRegistId(regist.getRegistId());

                // 操作人id
                //  hotelCompanyArRecode.setOperatorId(Integer.parseInt(user.getUserId()));
                hotelCompanyArRecode.setOperatorName(user.getUserName());
                // 备注
                hotelCompanyArRecode.setRemark(user.getUserName() + "：结账自动挂房费 ");

                hotelCompanyArRecode.setSettleId(regist.getRegistId());
                hotelCompanyArRecode.setOperatTime(date);
                hotelCompanyArRecode.setBusinessShiftId(user.getClassId());
                hotelCompanyArRecode.setBusinessDay(user.getBusinessDay());
                hotelCompanyArRecode.setCreateUserId(user.getUserId());
                hotelCompanyArRecode.setCreateUserName(user.getUserName());
                hotelCompanyArRecode.setCreateTime(date);
                hotelCompanyArRecode.setUpdateUserId(user.getUserId());
                hotelCompanyArRecode.setUpdateUserName(user.getUserName());
                hotelCompanyArRecode.setCreateTime(date);
                hotelCompanyArRecode.setHid(user.getHid());
                hotelCompanyArRecode.setHotelGroupId(user.getHotelGroupId());
                hotelCompanyArRecode.setPayState(0);
                hotelCompanyArRecode.setPersonName(peopleName);

                AddArAccount addArAccount = new AddArAccount();
                addArAccount.setAccount(arAccount);
                addArAccount.setHotelCompanyArRecode(hotelCompanyArRecode);

                Integer integer = accountDao.saveAccount(arAccount);

                if (integer < 1) {
                    throw new Exception("添加挂账信息失败");
                }

                hotelCompanyArRecode.setTransactionId(arAccount.getAccountId());

                Integer insert = hotelCompanyArRecodeDao.insert(hotelCompanyArRecode);
                if (insert < 1) {
                    accountDao.deleteAccount(arAccount.getAccountId());
                    throw new Exception("添加挂账信息失败");
                }


                HotelCompanyAccountInfo hotelCompanyAccountInfo = hotelCompanyAccountInfoDao.selectById(hotelCompanyAccount.getId());
                hotelCompanyAccountInfo.setId(hotelCompanyAccount.getId());
                hotelCompanyAccountInfo.setNoOffWriteMoney(hotelCompanyAccountInfo.getNoOffWriteMoney() + canArMoney);
                hotelCompanyAccountInfo.setMaxLimit(hotelCompanyAccountInfo.getMaxLimit() - canArMoney);
                Integer update = hotelCompanyAccountInfoDao.update(hotelCompanyAccountInfo);

                if (update < 1) {
                    throw new Exception("修改账户信息失败");
                }

                oprecord.setDescription("自助结账自动把:" + canArMoney / 100.0 + " 元房费转AR账");
                this.addOprecords(oprecord);

            }


            // 进行退款操作
            // 退款顺序  微信、支付宝、预授权、会员冻结
            // 批量修改账务信息集合

            // 2.会员冻结完成
            for (Account account : vipysqList) {

                Integer price = account.getPrice();

                Integer refundMoney = 0;

                if (price >= roomDiff) {
                    refundMoney = roomDiff;
                    roomDiff = 0;
                } else {

                    roomDiff -= price;
                    refundMoney = price;

                }

                JSONObject jsonObject = new JSONObject();
                jsonObject.put(ER.SESSION_TOKEN, user.getSessionId());
                jsonObject.put("money", account.getPrice() - refundMoney);
                jsonObject.put("accountId", account.getThirdAccoutId());

                responseData = memberService.finishMemberFreeze(jsonObject);
                if (responseData.getResult().equals(ER.ERR)) {
                    throw new Exception(responseData.getMsg());
                }
                account.setPrice(account.getPrice() - refundMoney);
                account.setThirdRefundState(2);
                account.setRefundPrice(account.getPrice() - refundMoney);
                account.setPayCodeId("9600");
//                account.setPayCodeId("会员储值卡");
                account.setBusinessDay(user.getBusinessDay());
                accountDao.editAccount(account);


            }

            // 3.预授权完成


            // 4.微信、支付宝退款
            for (Account account : refundList) {

                Integer price = account.getPrice();

                if (roomDiff == 0) {
                    continue;
                }

                Integer refundMoney = 0;

                if (price >= roomDiff) {
                    refundMoney = roomDiff;
                    roomDiff = 0;
                } else {
                    roomDiff -= price;
                    refundMoney = price;

                }

                JSONObject jsonObject = new JSONObject();
                jsonObject.put(ER.SESSION_TOKEN, user.getSessionId());
                jsonObject.put("money", refundMoney / 100.0);
                jsonObject.put("accountId", account.getAccountId());

                responseData = accountService.refundMoney(jsonObject);

                if (responseData.getResult().equals(ER.ERR)) {
                    throw new Exception(responseData.getMsg());
                }

                account.setThirdRefundState(1);
                account.setRefundPrice(refundMoney);


            }

            // 5.其他付款退款

            Set<Integer> otherPayKeys = otherPay.keySet();

            for (Integer keys : otherPayKeys) {

                Integer price = otherPay.get(keys);

                Integer refundMoney = 0;

                if (price >= roomDiff) {
                    refundMoney = roomDiff;
                    roomDiff = 0;
                } else {
                    roomDiff -= price;
                    refundMoney = price;

                }
                Account arAccount = new Account();
                switch (keys) {
                    case 1:
                        arAccount = regAccounts(user, regist, registPeople.get(0), refundMoney * -1, "9001", "现金退款", 1, "现金", 2);
                        arAccount.setRegistState(0);
                        break;
                    case 3:
                        arAccount = regAccounts(user, regist, registPeople.get(0), refundMoney * -1, "9101", "国内卡-消费", 3, "国内卡", 2);
                        arAccount.setRegistState(0);
                        break;
                    case 11:
                        arAccount = regAccounts(user, regist, registPeople.get(0), refundMoney * -1, "9309", "支付宝退款", 11, "支付宝", 2);
                        arAccount.setRegistState(0);
                        break;
                    case 12:
                        arAccount = regAccounts(user, regist, registPeople.get(0), refundMoney * -1, "9329", "微信扫码支付退款", 12, "微信支付", 2);
                        arAccount.setRegistState(0);
                        break;

                }
                if (refundMoney > 0) {
                    arAccount.setCreateUserId(user.getUserId());
                    accountDao.saveAccount(arAccount);
                }

            }


            // 进行结账
            checkoutParam = new CheckoutParam();
            checkoutParam.setType(1);
            checkoutParam.setRegistId(regist.getRegistId());
            checkoutParam.setSessionToken(user.getSessionId());
            responseData = this.checkOut(checkoutParam);



            IotHotelCheckOut checkOut = new IotHotelCheckOut();
            checkOut.setCode(regist.getRegistId() + "");
            HotelIotStrategy hotelIotStrategy = new HotelIotStrategy();
            Map<String, String> authParamMap = new HashMap<>();
            authParamMap.put("eid",user.getHid().toString());
            authParamMap.put("accessKey", iotPlatConfig.getAccessKey());
            authParamMap.put("appSecret", iotPlatConfig.getAppSecret());
            authParamMap.put("sassTokenSecret",iotPlatConfig.getSassTokenSecret());
            try {
                authParamMap.put("phone", AESUtil.encryptAesCbc(user.getPhone(),iotPlatConfig.getSassTokenSecret()));
            } catch (Exception e) {
                log.error("手机号加密失败");
            }
            hotelIotStrategy.initStrategy(authParamMap);
            webClientUtil.asyncSendPost(iotPlatConfig.getAddress() + Iot.CHECK_OUT,
                    checkOut,
                    hotelIotStrategy
            );

        } catch (Exception e) {
            log.error("",e);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    /**
     * @param user         用户信息
     * @param regist       登记信息
     * @param registPerson 登记人
     * @param money        金额
     * @param payCodeId    付款码小类
     * @param payCode
     * @return
     */
    private Account regAccounts(TbUserSession user, Regist regist, RegistPerson registPerson, Integer money, String payCodeId, String payCode, Integer payClassId, String payClass, Integer payType) {

        Date date = new Date();

        String a = OrderNumUtils.getNo(user.getHid(), user, OrderNumUtils.ACCOUNT, stringRedisTemplate);
        Account account = new Account();
        account.setAccountId(a);
        account.setHid(user.getHid());
        account.setCreateUserId(user.getUserId());
        account.setCreateUserName(user.getUserName());
        account.setCreateTime(new Date());
        account.setIsCancel(0);
        account.setAccountYear(user.getBusinessYear());
        account.setAccountYearMonth(user.getBusinessMonth());
        account.setBusinessDay(user.getBusinessDay());
        account.setClassId(user.getClassId());
        account.setAccountType(1);
        account.setSettleAccountTime(new Date());
        account.setThirdRefundState(0);
        account.setRoomNum(regist.getRoomNum());
        account.setRoomInfoId(regist.getRoomNumId());
        account.setRoomTypeId(regist.getRoomTypeId());
        account.setRegistState(0);
        account.setRegistId(regist.getRegistId());
        account.setBookingId(regist.getBookingOrderId());
        //类型 ：消费、付款
        account.setPayType(payType);
        account.setSaleNum(1);
        account.setPayClassId(payClassId);
        account.setPayClassName(payClass);
        //全天房费
        account.setPayCodeId(payCodeId);
        account.setPayCodeName(payCode);

        //用户信息
        account.setCreateTime(date);
        account.setCreateUserId(user.getUserId());
        account.setCreateUserName(user.getUserName());
        account.setUpdateTime(date);
        account.setUpdateUserId(user.getUserId());
        account.setUpdateUserName(user.getUserName());

        account.setClassId(user.getClassId());
        account.setUpdateCalssId(user.getClassId());

        // 营业日期
        account.setBusinessDay(user.getBusinessDay());


        //设置账务关联人id为0
        account.setRegistPersonId(registPerson.getRegistPersonId());
        account.setPrice(money);
        account.setUintPrice(money);
        return account;
    }


}
