package com.pms.czabsorders.service.machine;

import net.sf.json.JSONObject;

import java.util.Map;

/**
 * 自助机数据推送
 */
public interface MachineRegistService {

    /**
     * 保存自助机登记信息
     * @param param
     * @return
     */
    public Map<String,Object> machineAddRegistForOtherPms(JSONObject param);


    /**
     * 添加第三方PMS账务信息
     * @param param
     * @return
     */
    public Map<String, Object> machineAddAccountForOtherPms(JSONObject param);


    /**
     * 第三方PMS自助机退房
     * @param param
     * @return
     */
    public Map<String, Object> checkOutForOtherPms(JSONObject param);

    /**
     * 对接第三方支付平台退款
     *  并推送到第三方账务表中
     * @param param
     * @return
     */
    public Map<String,Object> refundMoneyForOtherPmsAndPush(JSONObject param);


    /**
     * 根据第三方的PMS账单号查询PMS的房单号和可用的账务信息
     * @return
     */
    public Map<String,Object> findRegistAndCanRefundAccountByOtherPmsId(JSONObject param);
}
