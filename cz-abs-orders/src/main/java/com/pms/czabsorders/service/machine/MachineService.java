package com.pms.czabsorders.service.machine;

import com.pms.czpmsutils.ResponseData;
import com.pms.pmsorder.bean.machine.MakeRoomCardParam;
import com.pms.pmsorder.bean.machine.model.AvailRoomRequest;
import com.pms.pmsorder.bean.machine.model.ExecuteAssignRoomRequest;
import com.pms.pmsorder.bean.machine.model.HotelOrderListRequest;
import com.pms.pmsorder.bean.machine.search.MachineMainSearch;
import com.pms.pmsorder.bean.machine.search.MachineParamTypeSearch;
import net.sf.json.JSONObject;

import java.util.Map;


public interface MachineService {

    /**
     * 查询酒店下所有自助机
     *
     * @param param
     * @return
     */
    public ResponseData findAllMachineByHid(MachineMainSearch param);

    /**
     * 验证自助机是否存在
     *
     * @param param
     * @return
     */
    public ResponseData hotelMachineExistence(MachineMainSearch param);

    /**
     * 根据自助机查询设置
     *
     * @param param
     * @return
     */
    public ResponseData findRecordByMachineUUID(JSONObject param);

    /**
     * 自助机设置
     *
     * @param param
     * @return
     */
    public ResponseData findAllMachineSetting(JSONObject param);

    public ResponseData findMachineByMac(JSONObject param);


    /**
     * 修改自助机设置
     *
     * @param param
     * @return
     */
    public ResponseData updateMachineSetting(JSONObject param);


    /**
     * 获取所有自助机参数
     *
     * @return
     */
    public ResponseData getMachineParam(MachineParamTypeSearch machineParamTypeSearch);


    /**
     * 根据mac地址获取自助机信息
     *
     * @param param
     * @return
     */
    public Map<String, Object> findMachineByMacId(JSONObject param);

    /**
     * 复制自助机配置
     *
     * @param param
     * @return
     */
    public Map<String, Object> copyMachineSetting(JSONObject param);


    /**
     * 根据hid 获取 门锁设置
     *
     * @param param
     * @return
     */
    public ResponseData findMachineSettingByHid(JSONObject param);


    /**
     * 查询订单
     *
     * @param hotelOrderListRequest
     * @return
     */
    public ResponseData findHotelOrderList(HotelOrderListRequest hotelOrderListRequest);

    public ResponseData getAvailRoomList(AvailRoomRequest availRoomRequest);

    public ResponseData availRoom(ExecuteAssignRoomRequest executeAssignRoomRequest);

    /**
     * 给酒店自助机发送指令制作房卡
     *
     * @return
     */
    public ResponseData machineMakeRoomCard(MakeRoomCardParam makeRoomCardParam);
}
