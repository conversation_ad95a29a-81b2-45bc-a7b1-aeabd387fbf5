package com.pms.czabsorders.service.machine.impl;

import com.pms.czabsorders.service.machine.LvyunInterfaceService;
import com.pms.czaccount.bean.account.Account;
import com.pms.czaccount.dao.account.AccountDao;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.constant.ECache;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.hotelsetting.HOTEL_SETTING;
import com.pms.czpmsutils.constant.lvyuninterface.PMS;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.otherpms.Lvyun;
import com.pms.pmsorder.bean.Regist;
import com.pms.pmsorder.dao.RegistDao;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class LvyunInterfaceServiceImpl extends BaseService implements LvyunInterfaceService {



    @Autowired
    private RegistDao registDao;

    @Autowired
    private AccountDao accountDao;

    /**
     * 专门调用绿云接口
     */
    private Lvyun lvyun = new Lvyun();


    @Override
    public Map<String, Object> addAccount(JSONObject param) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put(ER.RES, ER.SUCC);
        try {
            /**
             * 1. 获取登录信息
             */
            String sessionToken = param.get(ER.SESSION_TOKEN).toString();
            final TbUserSession user = this.getTbUserSession(sessionToken);

            /**
             * 2. 根据registId查询房单信息
             *    根据accountId查询账务信息
             */
            int registId = param.getInt("registId");
            Regist regist = registDao.selectById(registId);

            if (regist == null || regist.getOtherPmsRegistId() == null || "".equals(regist.getOtherPmsRegistId())) {
                throw new Exception("未查到相应的房单信息");
            }

            String accountId = param.getString("accountId");
            Account account = accountDao.selectById(accountId);

            if (account == null) {
                throw new Exception("未查到相应的账务信息");
            }

            /**
             * 3.获取所有参数
             *      取出绿云的参数
             */
            param.put("uuid",account.getCreateUserId());
            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            JSONObject setJson = JSONObject.fromObject(userCahe.get(ECache.MACHINE_SETTING_JSON, account.getCreateUserId()));

            String appKey = setJson.getJSONObject(PMS.Pms_AppKey).getString(HOTEL_SETTING.PARAM_VALUE);
            String appSecret = setJson.getJSONObject(PMS.Pms_AppSecret).getString(HOTEL_SETTING.PARAM_VALUE);
            String username = setJson.getJSONObject(PMS.Pms_Username).getString(HOTEL_SETTING.PARAM_VALUE);
            String password = setJson.getJSONObject(PMS.Pms_Password).getString(HOTEL_SETTING.PARAM_VALUE);
            String hotelCode = setJson.getJSONObject(PMS.Pms_HotelCode).getString(HOTEL_SETTING.PARAM_VALUE);
            String hotelGroupCode = setJson.getJSONObject(PMS.Pms_HotelGroupCode).getString(HOTEL_SETTING.PARAM_VALUE);
            String url = setJson.getJSONObject(PMS.Pms_Url).getString(HOTEL_SETTING.PARAM_VALUE);
            String groupUrl = setJson.getJSONObject(PMS.Pms_GroupUrl).getString(HOTEL_SETTING.PARAM_VALUE);

            JSONObject pushMsg = new JSONObject();
            //1.1接口所有请求参数，以获取sessionId接口为例，演示sign生成方式
            pushMsg.put("HotelCode",hotelCode);
            pushMsg.put("username",username);
            pushMsg.put("password",password);
            pushMsg.put("appKey",appKey);
            pushMsg.put("appSecret",appSecret);
            pushMsg.put("HotelGroupCode", hotelGroupCode);
            pushMsg.put("GroupUrl",groupUrl);
            pushMsg.put("Url", url);
            pushMsg.put("RegistID",regist.getOtherPmsRegistId());
            //微信
            String payType = "141";
            String payMsg = "";

            if (account.getPayClassId().equals(11)) {
                payType = "12";
            }
            payMsg += "0#";
            payMsg += payType;
            payMsg += "#";
            payMsg += param.getInt("refundMoney") / -100.0;
            payMsg += "##";
            payMsg += regist.getOtherPmsRegistId();
            payMsg += "##";
            payMsg += HotelUtils.currentTime();
            payMsg += "#0#";
            payMsg += "";
            payMsg += "#";
            payMsg += "";
            payMsg += "#";
            payMsg += "";
            pushMsg.put("PayMsg",payMsg);

            //返回结果
            JSONObject resultMsg = lvyun.Add_Transaction(pushMsg);



        } catch (Exception e) {
            log.error("",e);
            resultMap.put(ER.RES, ER.ERR);
            resultMap.put(ER.MSG, e.getMessage());
        }
        return resultMap;
    }




}
