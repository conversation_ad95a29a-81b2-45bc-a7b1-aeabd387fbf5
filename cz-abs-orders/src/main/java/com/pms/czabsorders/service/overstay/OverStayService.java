package com.pms.czabsorders.service.overstay;

import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.OverStayListRequest;
import com.pms.czpmsutils.request.OverStayOrderRequest;
import com.pms.czpmsutils.request.OverStayRequest;
import net.sf.json.JSONObject;

public interface OverStayService {
    /**
     * 续住
     * @param param
     * @return
     */
    public ResponseData overStay(JSONObject param);



    /**
     * 续住参数
     * @param overStayRequest
     * @return
     */
    public ResponseData overStayNew(OverStayRequest overStayRequest);

    /**
     *
     * 钟点房续住
     *
     * @return
     */
    public ResponseData overHourStay(OverStayRequest overStayRequest);


    /**
     * 换单续住
     * @param overStayOrderRequest
     * @return
     */
    public ResponseData overStayOrder(OverStayOrderRequest overStayOrderRequest);

    /**
     * 批量续住
     * @param overStayListRequest
     * @return
     */
    public ResponseData overStayList(OverStayListRequest overStayListRequest);


}
