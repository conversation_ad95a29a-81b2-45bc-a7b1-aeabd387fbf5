package com.pms.czabsorders.service.order.transaction;


import com.pms.czhotelfoundation.bean.HourRoomDayUse;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliary;
import com.pms.czhotelfoundation.bean.room.RoomAuxiliaryRelation;
import com.pms.czhotelfoundation.bean.room.RoomInfo;
import com.pms.czpmsutils.constant.oprecord.Oprecord;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface BookTransactionService {

    public Integer addBookService(BookingOrder bookingOrder, RegistGroup registGroup, List<BookingOrderRoomType> bookingOrderRoomTypes,  List<RoomAuxiliaryRelation> roomAuxiliaryRelations, ArrayList<Oprecord> oprecords,BookingOrderConfig bookingOrderConfig) throws Exception;

    public Integer addBookHourService(BookingOrder bookingOrder, RegistGroup registGroup, List<BookingOrderRoomType> bookingOrderRoomTypes,  List<RoomAuxiliaryRelation> roomAuxiliaryRelations, ArrayList<Oprecord> oprecords,BookingOrderConfig bookingOrderConfig
                                      , ArrayList<HourRoomDayUse> addHourUse, ArrayList<HourRoomDayUse> upaHourUse
    ) throws Exception;

    /**
     *   ArrayList<BookingOrderRoomType> updateRoomType = new ArrayList<>();
     *             ArrayList<BookingOrderRoomType> addRoomType = new ArrayList<>();
     *             ArrayList<BookingOrderRoomType> deleteRoomType = new ArrayList<>();
     *
     *             // 新增的房间,修改的房间,删除的房间
     *             ArrayList<BookingOrderRoomNum> addRooms = new ArrayList<>();
     *             ArrayList<BookingOrderRoomNum> deleteRooms = new ArrayList<>();
     *
     *             // 要新增,删除的房价
     *             ArrayList<BookingOrderDailyPrice> addPrices = new ArrayList<>();
     *             ArrayList<BookingOrderDailyPrice> deletePrices = new ArrayList<>();
     *
     *             // 要新增,删除的辅助房态
     *             ArrayList<RoomAuxiliaryRelation> addAuxi = new ArrayList<>();
     *             ArrayList<RoomAuxiliaryRelation> deleteAuxi = new ArrayList<>();
     */
    public void updateBookService(BookingOrder bookingOrder,List<BookingOrderRoomType> updateRoomType,List<BookingOrderRoomType> addRoomType,List<BookingOrderRoomType> deleteRoomType,List<BookingOrderRoomNum> addRooms,
                                  List<BookingOrderRoomNum> deleteRooms,List<BookingOrderDailyPrice> addPrices,List<BookingOrderDailyPrice> deletePrices,List<RoomAuxiliaryRelation> addAuxi,List<RoomAuxiliaryRelation> deleteAuxi,List<BookingOrderRoomNum> updateRoomNums) throws Exception;

    public void deleteBookRoomNum(BookingOrderRoomNum bookingOrderRoomNum) throws Exception;


    public void addBookOrderRoomService(RoomInfo roomInfo, BookingOrderRoomNum bookingOrderRoomNum, TbUserSession user, List<RoomAuxiliaryRelation> roomAuxiliaryRelations, RoomAuxiliary auxiliary, BookingOrderRoomType bookingOrderRoomType, List<BookingOrderDailyPrice> bookingOrderDailyPrices, List<BookingOrderDailyPrice> addBookingOrderDailyPrices) throws Exception;

    /**
     * 批量添加房间信息
     */
    public void addBookRoomList(ArrayList<RoomAuxiliaryRelation> deleteRoomAuRea,ArrayList<RoomAuxiliaryRelation> addRoomAuRea,ArrayList<RoomAuxiliaryRelation> upaRoomAuRea,ArrayList<BookingOrderDailyPrice> deletePrices,
                                ArrayList<BookingOrderDailyPrice> addPrices,ArrayList<BookingOrderDailyPrice> updaPrices,ArrayList<BookingOrderRoomNum> upaRooms,Map<Integer, BookingOrderRoomType> roomTypeMap

    )  throws Exception;

    public void cancelOrderService(List<BookingOrder> bookingOrders, List<BookingOrderRoomType> bookingOrderRoomTypes, List<BookingOrderRoomNum> bookingOrderRoomNums,
                                   TbUserSession user, List<RoomAuxiliaryRelation> roomAuxiliaryRelations)  throws Exception;


    public void cancelHourOrderService(List<BookingOrder> bookingOrders, List<BookingOrderRoomType> bookingOrderRoomTypes, List<BookingOrderRoomNum> bookingOrderRoomNums,
                                       TbUserSession user, List<RoomAuxiliaryRelation> roomAuxiliaryRelations,ArrayList<HourRoomDayUse> upaHourUse, ArrayList<HourRoomDayUse> delHourUse)  throws Exception;


    public void orderRecoveryService(BookingOrder bookingOrder, List<BookingOrderRoomType> bookingOrderRoomTypes, List<BookingOrderRoomNum> bookingOrderRoomNums, TbUserSession user, RoomAuxiliary auxiliary) throws Exception;

    public void cancelBookingRoomNoTran(BookingOrder bookingOrder, List<BookingOrderRoomNum> bookingOrderRoomNums, TbUserSession user, List<RoomAuxiliaryRelation> roomAuxiliaryRelations ,List<BookingOrderDailyPrice> bookingOrderDailyPrices) throws  Exception;


    public void updateBookRoomFunc( List<BookingOrderRoomNum> upaRooms,List<BookingOrderDailyPrice> addPrices,List<BookingOrderDailyPrice> deletePrices,List<BookingOrderDailyPrice> upaPrices) throws Exception;

    public void updatePriceList( List<BookingOrderDailyPrice> prices) throws Exception;

}
