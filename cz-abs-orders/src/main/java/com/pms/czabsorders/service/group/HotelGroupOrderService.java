package com.pms.czabsorders.service.group;

import com.pms.czabsorders.bean.CardGroupInfoSearchRequest;
import com.pms.czabsorders.bean.CardGroupLevelSearchRequest;
import com.pms.czabsorders.bean.CardGroupTypeSearchRequest;
import com.pms.czmembership.bean.company.search.HotelCompanyAccountInfoSearch;
import com.pms.czmembership.bean.company.search.HotelCompanyInfoSearch;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.BaseRequest;
import com.pms.pmsorder.bean.request.BookingOrderPageRequest;
import com.pms.pmsorder.bean.request.GetHotelGroupAvailableRoomRequest;
import com.pms.pmsorder.bean.search.RegistPersonSearch;


public interface HotelGroupOrderService {

    ResponseData getHotelGroupOrderList(BookingOrderPageRequest bookingOrderPageRequest);
    ResponseData getHotelGroupAvailableRoom(GetHotelGroupAvailableRoomRequest getHotelGroupAvailableRoomRequest);
    ResponseData getHotelGroupRoomTypeNum(BaseRequest baseRequest);
    ResponseData getHotelGroupMember(CardGroupInfoSearchRequest groupInfoSearch);
    ResponseData getHotelGroupType(CardGroupTypeSearchRequest cardGroupTypeSearchRequest);
    ResponseData getHotelGroupLevel(CardGroupLevelSearchRequest cardGroupLevelSearchRequest);
    ResponseData getHotelGroupRegistPerson(RegistPersonSearch registPersonSearch);
    ResponseData getHotelGroupCompany(HotelCompanyInfoSearch hotelCompanyInfoSearch);
    ResponseData getHotelGroupCompanyAccount(HotelCompanyAccountInfoSearch hotelCompanyAccountInfoSearch);
}
