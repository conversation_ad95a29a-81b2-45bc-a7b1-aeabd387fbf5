package com.pms.czabsorders.batchapply;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Map;

/**
 * pms业务批量开通excel文件内容
 */
@Data
public class BatchApplyInfo {

    // 酒店名称
    @ExcelProperty(value = "集团名称", index = 0)
    @NotBlank(message = "集团名称不能为空")
    private String hotelName;

    // 用户手机号
    @ExcelProperty(value = "客户联系人姓名", index = 1)
    @NotBlank(message = "客户联系人姓名不能为空")
    @Size(min = 2,max = 50,message = "客户联系人姓名长度必须在2到50个字符之间")
    private String name;

    // 用户名称
    @ExcelProperty(value = "客户联系人号码", index = 2)
    @NotBlank(message = "客户联系人号码不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$",message = "请输入客户联系人正确的手机号码")
    private String phone;

    // 酒店编码
    @ExcelProperty(value = "酒店编码", index = 3)
    @NotBlank(message = "酒店编码不能为空")
    private String hotelCode;

    // 集团编码
    @ExcelProperty(value = "所属集团", index = 4)
    @NotBlank(message = "所属集团不能为空")
    private String hotelGroupCode;

    // 集团编码
    @ExcelProperty(value = "客户经理", index = 5)
    private String accountManager;

    // 集团编码
    @ExcelProperty(value = "客户经理联系电话", index = 6)
    @NotBlank(message = "客户经理联系电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$",message = "请输入客户经理正确的手机号码")
    private String accountManagerPhone;

}
