package com.pms.czabsorders.config;

import com.pms.czpmsutils.RSAUtils;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
@ConfigurationProperties(prefix = "spring.datasource")
@Slf4j
public class DataSourceConfig {

    private String url;
    private String username;
    private String password;
    private HikariConfig hikari;

    @Bean
    public DataSource dataSource() {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl(decrypt(url));
        dataSource.setUsername(decrypt(username));
        dataSource.setPassword(decrypt(password));
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");

        // 设置 HikariCP 配置
        dataSource.setConnectionTimeout(hikari.getConnectionTimeout());
        dataSource.setMaximumPoolSize(hikari.getMaximumPoolSize());
        dataSource.setMinimumIdle(hikari.getMinimumIdle());
        dataSource.setIdleTimeout(hikari.getIdleTimeout());
        dataSource.setMaxLifetime(hikari.getMaxLifetime());
        dataSource.setConnectionTestQuery(hikari.getConnectionTestQuery());

        return dataSource;
    }

    private String decrypt(String encryptValue) {
        try {
            return RSAUtils.getStringDecrypt(encryptValue);
        } catch (Exception e){
            log.error("数据库连接信息解密失败",e);
            throw new RuntimeException("数据库连接信息解密失败");
        }
    }

    // Getters and Setters

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public HikariConfig getHikari() {
        return hikari;
    }

    public void setHikari(HikariConfig hikari) {
        this.hikari = hikari;
    }


    public static void main(String[] args) {
        try{
          String url =  RSAUtils.getStringDecrypt("2222f27cfecd13ba718e9e7f1d7774f06f9aeb7b527a1575b83875683ae2414f0a3ddc2b4d0a3bf6b4086f762ef8bb61ba39d77f472a2a0f6d8a62ef0c76e3927dc9fe43ee8bb83532b6e400428422adf7dc2df747804c5fdef888e74dfca515c5597791d3eb88f8af1eef4628d36499");

          System.out.println(url);
        }catch (Exception e ){
            log.info("数据库连接信息解密失败{}",e);
        }
    }
}
