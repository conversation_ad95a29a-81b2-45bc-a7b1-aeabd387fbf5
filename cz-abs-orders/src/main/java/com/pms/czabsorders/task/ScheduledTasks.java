package com.pms.czabsorders.task;


import com.pms.czhotelfoundation.bean.AipriceResp;
import com.pms.czhotelfoundation.bean.code.Area;
import com.pms.czhotelfoundation.bean.hotel.HotelBaseInfo;
import com.pms.czhotelfoundation.bean.room.RoomType;
import com.pms.czhotelfoundation.dao.code.AreaDao;
import com.pms.czhotelfoundation.dao.hotel.HotelBaseInfoDao;
import com.pms.czhotelfoundation.dao.room.RoomTypeDao;
import com.pms.czpmsutils.DateUtil;
import com.pms.czpmsutils.HttpUtil;
import com.pms.czpmsutils.enums.HotelTypeEnum;
import com.pms.czpmsutils.request.AiPriceReq;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Author: 陈星宇
 * @CreateTime: 2025-08-08
 * @Description:
 */
@Component
@Slf4j
public class ScheduledTasks {

    @Resource
    private HotelBaseInfoDao hotelBaseInfoDao;

    @Resource
    private RoomTypeDao roomTypeDao;

    @Resource
    private AreaDao areaDao;


    public static List<String> getFutureSevenDays(){
        List<String> futureSevenDays = new ArrayList<>();
        LocalDate date = LocalDate.now();
        for (int i = 0; i < 7; i++) {
            LocalDate localDate = date.plusDays(i);
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String format = localDate.format(dateTimeFormatter);
            futureSevenDays.add(format);
        }
        return futureSevenDays;
    }

    /**
     * 酒店周期预测接口
     */
    @Scheduled(cron = "0 0/30 * * * ?")
    public void periodPredict(){
        List<HotelBaseInfo> hotelBaseInfos = hotelBaseInfoDao.selectAllHotelBaseInfos();
        List<String> futureSevenDays = getFutureSevenDays();
        List<AiPriceReq> aiPriceReqList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(hotelBaseInfos)){
            for (HotelBaseInfo hotelBaseInfo : hotelBaseInfos) {
                List<RoomType> roomTypeList = roomTypeDao.selectRoomTypeByHidList(Arrays.asList(hotelBaseInfo.getHid()));
                Area area = areaDao.selectById(hotelBaseInfo.getCity());
                HotelTypeEnum hotelTypeEnum = HotelTypeEnum.fromCode(hotelBaseInfo.getHotelType());
                for (RoomType roomType : roomTypeList) {
                    for (String futureSevenDay : futureSevenDays) {
                        AiPriceReq aiPriceReq = new AiPriceReq();
                        aiPriceReq.setDate(futureSevenDay);
                        aiPriceReq.setHotelName(hotelBaseInfo.getHotelName());
                        aiPriceReq.setRoomType(roomType.getRoomTypeName());
                        aiPriceReq.setTypeId(hotelTypeEnum.getDescription());
                        aiPriceReq.setStar(Objects.isNull(hotelBaseInfo.getStar()) ? 2 : hotelBaseInfo.getStar());
                        aiPriceReq.setLongitude(hotelBaseInfo.getLon().floatValue());
                        aiPriceReq.setLatitude(hotelBaseInfo.getLat().floatValue());
                        aiPriceReq.setRoomTypeId(roomType.getRoomTypeId().toString());
                        aiPriceReq.setCity(area.getName());
                        aiPriceReq.setCityCode(hotelBaseInfo.getCity().toString());
                        aiPriceReq.setHotelId(hotelBaseInfo.getHid().toString());
                        aiPriceReqList.add(aiPriceReq);
                    }
                }
            }
        }

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        JSONObject param = new JSONObject();
        param.put("list", aiPriceReqList);
        try {
            log.info("send_url:{} body:{} header:{}","http://hotel-price-infer.com/price/period_predict",param,headers);
            HttpUtil.sendPostRequestWithHeader("http://hotel-price-infer.com/multiple_aiprice", param, headers,null);
        }catch (Exception e){
            log.error("",e);
        }
    }
}
