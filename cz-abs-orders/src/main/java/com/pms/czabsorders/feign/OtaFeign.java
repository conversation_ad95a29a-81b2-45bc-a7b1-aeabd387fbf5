package com.pms.czabsorders.feign;

import com.alibaba.fastjson.JSONObject;
import com.pms.czabsorders.bean.OpenDailyPriceReqDto;
import com.pms.czabsorders.bean.OpenOrderUpdateStatusDto;
import com.pms.czpmsutils.GURL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.client.RestTemplate;

@Component

public class OtaFeign {
    private static final Logger logger = LoggerFactory.getLogger(OtaFeign.class);

    @Autowired
    private RestTemplate restTemplate;

    @PostMapping(value = "/supplier/v1/open/order/updateStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    public void openOrderUpdateStatus(OpenOrderUpdateStatusDto dto) {
        try {
            String openOrderUpdateStatusDto = JSONObject.toJSONString(dto);
            logger.info("openOrderUpdateStatus start：dto:{}", openOrderUpdateStatusDto);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> httpEntity = new HttpEntity<>(openOrderUpdateStatusDto, headers);
            String response = restTemplate.postForEntity(
                    "http://" + GURL.PMSOTA + "/supplier/v1/open/order/updateStatus",
                    httpEntity,
                    String.class
            ).getBody();
            logger.info("openOrderUpdateStatus end： dto={}，response={}", openOrderUpdateStatusDto, response);
        } catch (Exception e) {
            logger.info("openOrderUpdateStatus send exception:{}", e);
        }
    }

    @PostMapping(value = "/supplier/v1/open/dailyPrice", produces = MediaType.APPLICATION_JSON_VALUE)
    public void productDailyPricePush(OpenDailyPriceReqDto dto) {
        logger.info("openOrderUpdateStatus end： dto={}", dto);
        try {
            String openDailyPriceReqDto = JSONObject.toJSONString(dto);
            logger.info("openDailyPriceReqDto start：dto:{}", openDailyPriceReqDto);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> httpEntity = new HttpEntity<>(openDailyPriceReqDto, headers);
            String response = restTemplate.postForEntity(
                    "http://" + GURL.PMSOTA + "/supplier/v1/open/dailyPrice",
                    httpEntity,
                    String.class
            ).getBody();
            logger.info("openDailyPriceReqDto end： dto={}，response={}", openDailyPriceReqDto, response);
        } catch (Exception e) {
            logger.info("openDailyPriceReqDto send exception:{}", e);
        }
    }
}
