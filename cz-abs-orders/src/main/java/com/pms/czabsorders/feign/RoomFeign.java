package com.pms.czabsorders.feign;


import com.pms.czpmsutils.ResponseData;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = "cz-abs-orders",contextId = "roomFeign")
public interface RoomFeign {

    @RequestMapping(method = RequestMethod.POST,value = "/hotel/room/findAllHotelRoomInfo.do", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseData findAllHotelRoomInfo();
}
