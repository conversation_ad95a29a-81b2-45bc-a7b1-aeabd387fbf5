package com.pms.czabsorders;

import com.pms.czhotelfoundation.dao.hotel.HotelBaseInfoDao;
import com.pms.czpmsutils.constant.user.BaseService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@ComponentScan({"com.pms.czpmsutils.aspect.**","com.pms.czabsorders.*"})
@SpringBootTest
@Slf4j
public class CzAbsOrdersApplicationTests extends BaseService {

    @Autowired
    private HotelBaseInfoDao hotelBaseInfoDao;


    @Test
    public void contextLoads() throws Exception {

        //   String key = user.getHid() + "addNightAccount" + user.getBusinessDay();

        String mainId = "2082" + "addNightAccount" + "********";

        final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
        userCahe.put("hotelaliPay",mainId,"100");
        Object hotelWechatPay = userCahe.get("hotelaliPay", mainId);
        if(hotelWechatPay!=null){
            int i = Integer.parseInt(hotelWechatPay.toString());
            if(i>0){
                throw new Exception("以退过款不允许退款");
            }
        }

        log.info("可以退款");


    }

    @Test
    public void test(){
        List<String> list = new ArrayList<>();
        list.add("久荷宴礼大酒店");
        list.add("物联网演示酒店");
        list.add("物联网演示酒店222");
        log.info("",hotelBaseInfoDao.getHotelNameList(list));
    }

}
