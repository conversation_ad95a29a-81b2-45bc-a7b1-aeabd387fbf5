# 数据库操作
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: 2222f27cfecd13ba718e9e7f1d7774f06f9aeb7b527a1575b83875683ae2414f0a3ddc2b4d0a3bf6b4086f762ef8bb61ba39d77f472a2a0f6d8a62ef0c76e3927dc9fe43ee8bb83532b6e400428422adf7dc2df747804c5fdef888e74dfca515c5597791d3eb88f8af1eef4628d36499
    username: 8336bf9f284f20c3
    password: d1f2341cd0a3bb9cfeb959b7d4642fcb
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: none
  application:
    name: CZ-ABS-POLICE-INTERFACE
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true
  jta:
    atomikos:
      properties:
        log-base-dir: /home/<USER>/police-interface/atomikos
        log-base-name: police-interface_atomikos.log
  redis:
    host: **************
    port: 6379
    password: 643e145d0e5c42ef073f4f39c975ff0efeb959b7d4642fcb
    database: 5
    timeout: 30000
eureka:
  client:
    service-url:
      defaultZone: http://**************:8761/eureka
  instance:
    hostname: ${eureka.instance.ip-address}
    prefer-ip-address: false
    ip-address: ************
    instance-id: CZ-ABS-POLICE-INTERFACE
server:
  port: 8134
  tomcat:
    max-http-post-size: 10485760
