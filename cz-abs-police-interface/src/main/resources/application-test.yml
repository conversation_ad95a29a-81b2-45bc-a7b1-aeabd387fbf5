# 数据库操作
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: 2222f27cfecd13ba146f595ba63fd3bd93e2608996a398b2415c39b5192dfcaa6ba8213c7ad15e41
    username: 750a2aa24bf8807efeb959b7d4642fcb
    password: f5dd7cf74eb6fb0afeb959b7d4642fcb
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: none
  application:
    name: CZ-ABS-POLICE-INTERFACE
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  main:
    allow-bean-definition-overriding: true
  jta:
    atomikos:
      properties:
        log-base-dir: /home/<USER>/police-interface/atomikos
        log-base-name: police-interface_atomikos.log
  redis:
    host: ***********
    port: 6379
    password: 643e145d0e5c42ef073f4f39c975ff0efeb959b7d4642fcb
    database: 10
    timeout: 30000
eureka:
  client:
    service-url:
      defaultZone: http://**************:8761/eureka
  instance:
    hostname: ${eureka.instance.ip-address}
    prefer-ip-address: false
    ip-address: ************
    instance-id: CZ-ABS-POLICE-INTERFACE
server:
  port: 8134
  tomcat:
    max-http-post-size: 10485760
