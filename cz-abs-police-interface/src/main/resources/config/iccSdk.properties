#服务地址 端口默认是443
#icc.sdk.host=*************:9021
icc.sdk.host=**************:4077
#是否启用https访问，默认：是
#icc.sdk.enable.https=false
#认证类型=[client_credentials],[password],client_credentials：客户端鉴权模式;password：用户密码鉴权模式
icc.sdk.grantType=password
#-----------客户端鉴权模式--------------
#客户端鉴权模式申请访问凭证id
icc.sdk.clientId=CompanyName
#客户端鉴权模式申请访问凭证密钥
icc.sdk.clientSecret=42bec152-8f04-476a-9aec-e7d616ff3cb3
#客户端鉴权模式默认用户，默认是1，超级管理员
#icc.sdk.config.client.userId=1
#-----------用户密码鉴权模式--------------
#用户密码鉴权模式申请访问凭证id
icc.sdk.pwdClientId=CompanyName
#用户密码鉴权模式申请访问凭证密钥
icc.sdk.pwdClientSecret=42bec152-8f04-476a-9aec-e7d616ff3cb3
#用户名
icc.sdk.username=TEST
#密码
icc.sdk.password=Admin123
#http调试模式 false：开启，true：关闭
icc.sdk.enable.https=true
