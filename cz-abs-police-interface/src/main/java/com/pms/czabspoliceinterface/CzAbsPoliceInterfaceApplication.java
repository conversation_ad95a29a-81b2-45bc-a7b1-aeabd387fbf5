package com.pms.czabspoliceinterface;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

@SpringBootApplication
@MapperScan({"com.pms.*.*","com.pms.*.*.*"})
@ComponentScan("com.pms.*")
public class CzAbsPoliceInterfaceApplication {

    public static void main(String[] args) {
        SpringApplication.run(CzAbsPoliceInterfaceApplication.class, args);
    }
    @LoadBalanced
    @Bean
    RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
