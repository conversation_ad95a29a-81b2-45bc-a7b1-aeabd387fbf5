package com.pms.czabspoliceinterface.factory;

import com.pms.czabspoliceinterface.utils.EncryptUtils;
import com.pms.czabspoliceinterface.utils.HttpUtil;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;

public class HealthCode {
    public GuestResult getHealthCode(GuestModel guestModel){
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        guestResult.setMsg("查询失败");
        try {
            JSONObject postData = new JSONObject();
            postData.put("userName" , guestModel.getName());
            postData.put("cardNumber" , guestModel.getiDCode());
            String url = guestModel.getUrl();
            String key = guestModel.getKey();
            String data = EncryptUtils.aesEncrypt(postData.toString(), key);
            String result = HttpUtil.sendPost(url, data);
            if (result == null || result.equals("")){
                return guestResult;
            }
            JSONObject json = JSONObject.fromObject(result);
            if (!json.containsKey("code") || json.getInt("code") != 200){
                guestResult.setMsg(result);
                return guestResult;
            }
            String res = json.getString("data");
            String dataSource = EncryptUtils.aesDecrypt(res, key);
            JSONObject healthData = JSONObject.fromObject(dataSource);
            guestResult.setHealthData(healthData.toString());
            guestResult.setResult(true);
            guestResult.setMsg("查询成功");
        }catch (Exception e){
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }
}
