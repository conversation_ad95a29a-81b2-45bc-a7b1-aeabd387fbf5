package com.pms.czabspoliceinterface.factory;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czabspoliceinterface.utils.AESUtilShanXiXian;
import com.pms.czabspoliceinterface.utils.CryptoUtil;
import com.pms.czpmsutils.IdcardInfoExtractor;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class ShanXiXiAn extends PoliceBase implements IPolice {

    private static final Logger log = LoggerFactory.getLogger(ShanXiXiAn.class);
    public static String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDdeuEldV5+2B7u4iNp06AevZ5P\n" +
            "G3KhgkxOpJFotPKxfftIwTmY6ISbfS5Nz4REMt6fxdPYD8XxKTEr4zy+KVfFnJZn\n" +
            "u/I4GFWM41dJxNVaZUb4h1sVyfUr0sj2Xb9xxryU970gykgfsDdKXAmLu4ey8zc6\n" +
            "sb+YNrn0ieAoQHuA+wIDAQAB";
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        try {
            JSONObject guest = new JSONObject();
            guest.put("nation", this.getNationCode(guestModel.getNation()));
            guest.put("idcode", guestModel.getiDCode());
            guest.put("idtype", "11");
            guest.put("address", guestModel.getAddress());
            //旅馆编码"(6101010001)
            guest.put("nohotel", guestModel.getHotelCode());
            //"ltime":"入住时间"格式:年月日时分秒(20210121120000),
            DateTime dateTime = new DateTime();
            guest.put("ltime", DateUtil.format(new DateTime(guestModel.getCheckInTime()), DatePattern.PURE_DATETIME_PATTERN));
            guest.put("createtime", DateUtil.format(dateTime, DatePattern.PURE_DATETIME_PATTERN));
            guest.put("noroom", guestModel.getRoomNo());
            guest.put("name", guestModel.getName());
            guest.put("inccode", guestModel.getHotelCode() + DateUtil.format(dateTime, DatePattern.PURE_DATETIME_PATTERN));
            IdcardInfoExtractor idcardInfoExtractor = new IdcardInfoExtractor(guestModel.getiDCode());
            String gender = idcardInfoExtractor.getGender();
            guest.put("sex", this.getSexCode(gender));
            guest.put("type", "1");
            guest.put("bdate", guestModel.getBirthday().replace("-", ""));
            //   "picture":"证件图片Base64",
            guest.put("picture", guestModel.getPhoto());
            //   "identitypic":"旅客照片Base64",
            if (StrUtil.isEmptyIfStr(guestModel.getCameraPhoto())){
                guestModel.setCameraPhoto(guestModel.getPhoto());
            }
            guest.put("identitypic", guestModel.getCameraPhoto());
            //{"state":"0","mess":"xxxxx"}
            String url = guestModel.getUrl();
            url += "/v1/platform/checkin";
            AESUtilShanXiXian aesUtilShanXiXian = new AESUtilShanXiXian();
            String key = aesUtilShanXiXian.getKey();
            String datakey = CryptoUtil.rsaEncrypt(key, publicKey, CryptoUtil.DEFAULT_CHARSET);
            Map<String, Object> postData = new HashMap<>();
            AESUtilShanXiXian.IV = key;
            AESUtilShanXiXian.KEY = key;
            postData.put("data", aesUtilShanXiXian.encrypt(guest.toString()));
            //平台编码
            postData.put("platform", guestModel.getOperators());
            log.info("ShanXiXiAn:公安网入住上传url:{} 参数:{} ", url, postData.toString());
            String body = HttpRequest.post(url).header("Content-Type", "application/x-www-form-urlencoded").header("key", datakey).timeout(5000).form(postData).execute().body();
            log.info("ShanXiXiAn:公安网入住上传返回 {} ", body);
            if (StrUtil.isEmptyIfStr(body)) {
                guestResult.setMsg("上传失败:" + body);
                guestResult.setResult(false);
                return guestResult;
            }
            JSONObject resultMap = JSONObject.fromObject(body);
            if (!resultMap.containsKey("state") || resultMap.getInt("state") != 0) {
                guestResult.setMsg("上传失败:" + body);
                guestResult.setResult(false);
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setGuestId(guestModel.getiDCode());
            guestResult.setGuestNo(guestModel.getiDCode());

        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        try {
            JSONObject guest = new JSONObject();
            //旅馆编码"(6101010001)
            guest.put("nohotel", guestModel.getHotelCode());
            //"ltime":"入住时间"格式:年月日时分秒(20210121120000),
            DateTime dateTime = new DateTime();
            guest.put("etime", DateUtil.format(new DateTime(guestModel.getCheckInTime()), DatePattern.PURE_DATETIME_PATTERN));
            guest.put("noroom", guestModel.getRoomNo());
            guest.put("inccode", guestModel.getHotelCode() + DateUtil.format(dateTime, DatePattern.PURE_DATETIME_PATTERN));
            guest.put("type", "1");
            String url = guestModel.getUrl();
            url += "/v1/platform/checkout";
            AESUtilShanXiXian aesUtilShanXiXian = new AESUtilShanXiXian();
            String key = aesUtilShanXiXian.getKey();
            String datakey = CryptoUtil.rsaEncrypt(key, publicKey, CryptoUtil.DEFAULT_CHARSET);
            Map<String, Object> postData = new HashMap<>();
            AESUtilShanXiXian.IV = key;
            AESUtilShanXiXian.KEY = key;
            postData.put("data", aesUtilShanXiXian.encrypt(guest.toString()));
            //平台编码
            postData.put("platform", guestModel.getOperators());
            log.info("ShanXiXiAn:公安网退房上传url:{} 参数:{} ", url, postData.toString());
            String body = HttpRequest.put(url).header("Content-Type", "application/x-www-form-urlencoded").header("key", datakey).timeout(5000).form(postData).execute().body();
            log.info("ShanXiXiAn:公安网退房上传返回 {} ", body);
            if (StrUtil.isEmptyIfStr(body)) {
                guestResult.setMsg("上传失败:" + body);
                guestResult.setResult(false);
                return guestResult;
            }
            JSONObject resultMap = JSONObject.fromObject(body);
            if (!resultMap.containsKey("state") || resultMap.getInt("state") != 0) {
                guestResult.setMsg("上传失败:" + body);
                guestResult.setResult(false);
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setGuestId(guestModel.getiDCode());
            guestResult.setGuestNo(guestModel.getiDCode());

        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        return null;
    }
}
