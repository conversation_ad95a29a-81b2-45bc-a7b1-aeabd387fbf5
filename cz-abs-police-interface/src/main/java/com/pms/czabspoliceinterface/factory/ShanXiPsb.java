package com.pms.czabspoliceinterface.factory;


import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czpmsutils.Encrypt3DESAndBase64Utils;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;

public class ShanXiPsb extends PoliceBase implements IPolice {
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        Encrypt3DESAndBase64Utils.build3DesKey("");
        return null;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        return null;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        guestResult.setMsg("暂未实现");
        return guestResult;
    }

}
