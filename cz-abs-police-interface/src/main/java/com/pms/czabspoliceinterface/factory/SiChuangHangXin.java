package com.pms.czabspoliceinterface.factory;

import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czabspoliceinterface.utils.HttpUtil;
import com.pms.czabspoliceinterface.utils.SsoDesUtil2;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;


public class SiChuangHangXin extends PoliceBase implements IPolice {
    private static final Logger log = LoggerFactory.getLogger(SiChuangHangXin.class);

    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            String param = this.checkinParam(guestModel);
            if (param != null) {
                guestResult.setMsg(param);
                return guestResult;
            }
            String token = getToken(guestModel);
            if (token == null || token.equals("")) {
                guestResult.setMsg("获取Token失败");
                return guestResult;
            }
            JSONObject guestData = new JSONObject();
            String lkdm = guestModel.getHotelCode() + DateFormatUtils.format(new Date(), "yyyyMMddHHmm");
            guestData.put("SINE", "1");
            guestData.put("CZLX", "0");
            guestData.put("SJBH", lkdm + "00000");
            guestData.put("LKDM", lkdm);
            guestData.put("LGBH", guestModel.getHotelCode());
            guestData.put("QYBM", guestModel.getHotelCode());
            guestData.put("XM", guestModel.getName());
            guestData.put("XB", this.getSexCode(guestModel.getSex()));
            guestData.put("MZDM", this.getNationCode(guestModel.getNation()));
            guestData.put("CSRQ", guestModel.getBirthday());
            guestData.put("ZJLXDM", "11");
            guestData.put("ZJHM", guestModel.getiDCode());
            guestData.put("SSX", guestModel.getiDCode().substring(0, 6));
            guestData.put("XXDZ", guestModel.getAddress());
            guestData.put("RZSJ", guestModel.getCheckInTime());
            guestData.put("RZFH", guestModel.getRoomNo());
            guestData.put("ZJZP", guestModel.getPhoto());
            guestData.put("XCZP", guestModel.getCameraPhoto() == null || guestModel.getCameraPhoto().equals("") ? guestModel.getPhoto() : guestModel.getCameraPhoto());
            guestData.put("SCSJ", HotelUtils.currentTime());
            String data = SsoDesUtil2.desCbcEncrypt(guestData.toString(), guestModel.getKey().substring(0, 8), guestModel.getKey().substring(8, 16));
            JSONObject postData = new JSONObject();
            postData.put("encryptStr", data);
            postData.put("md5", SsoDesUtil2.getMd5(guestData.toString()));
            String postUrl = guestModel.getUrl() + "/public/aisinoOpenApi/scgn-new/recieveJnHotelDataWithSecret";
            log.info("checkin url:{} , data:{}", postUrl, postData.toString());
            String res = HttpUtil.sendPost(postUrl, token, postData.toString());
            log.info("checkin result:{}", res);
            JSONObject resData = JSONObject.fromObject(res);
            if (!resData.containsKey("code") || !resData.getString("code").equals("0000")) {
                guestResult.setMsg("上传失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestNo(lkdm);
            guestResult.setGuestId(lkdm);
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            String token = getToken(guestModel);
            if (token == null || token.equals("")) {
                guestResult.setMsg("获取Token失败");
                return guestResult;
            }
            JSONObject guestData = new JSONObject();
            guestData.put("SINE", "1");
            guestData.put("CZLX", "1");
            guestData.put("SJBH", guestModel.getGuestNo() + "00000");
            guestData.put("LKDM", guestModel.getGuestNo());
            guestData.put("LGBH", guestModel.getHotelCode());
            guestData.put("QYBM", guestModel.getHotelCode());
            guestData.put("XM", guestModel.getName());
            guestData.put("XB", this.getSexCode(guestModel.getSex()));
            guestData.put("MZDM", this.getNationCode(guestModel.getNation()));
            guestData.put("CSRQ", guestModel.getBirthday());
            guestData.put("ZJLXDM", "11");
            guestData.put("ZJHM", guestModel.getiDCode());
            guestData.put("SSX", guestModel.getiDCode().substring(0, 6));
            guestData.put("XXDZ", guestModel.getAddress());
            guestData.put("RZSJ", HotelUtils.currentTime());
            guestData.put("RZFH", guestModel.getRoomNo());
            guestData.put("TFSJ", HotelUtils.currentTime());
            guestData.put("SCSJ", HotelUtils.currentTime());
            System.out.println(guestData.toString());
            String data = SsoDesUtil2.desCbcEncrypt(guestData.toString(), guestModel.getKey().substring(0, 8), guestModel.getKey().substring(8, 16));
            JSONObject postData = new JSONObject();
            postData.put("encryptStr", data);
            postData.put("md5", SsoDesUtil2.getMd5(guestData.toString()));
            String postUrl = guestModel.getUrl() + "/public/aisinoOpenApi/scgn-new/recieveJnHotelDataWithSecret";
            String res = HttpUtil.sendPost(postUrl, token, postData.toString());
            System.out.println(res);
            JSONObject resData = JSONObject.fromObject(res);
            if (!resData.containsKey("code") || !resData.getString("code").equals("0000")) {
                guestResult.setMsg("上传失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        guestResult.setMsg("暂未实现");
        return guestResult;
    }

    public String getToken(GuestModel guestModel) {
        StringBuilder postData = new StringBuilder();
        postData.append(guestModel.getUrl() + "/cd/aisinoOpenApi.Auth/verify/getToken?appCode=" + guestModel.getOperators() + "&appSecret=" + guestModel.getKey());
        log.info("get token:URL:" + postData.toString() );
        String res = HttpUtil.sendGet(postData.toString(), "");
        log.info("get token:res:" + res );
        JSONObject data = JSONObject.fromObject(res);
        if (!data.containsKey("code") || !data.getString("code").equals("0000")) {
            return null;
        }
        String token = data.getString("token");
        return token;
    }
}
