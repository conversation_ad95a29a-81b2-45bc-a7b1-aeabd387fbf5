package com.pms.czabspoliceinterface.factory;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

public class ChongQingNew extends PoliceBase implements IPolice {
    private static final Logger log = LoggerFactory.getLogger(ChongQingNew.class);
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            Map<String, Object> postData = new HashMap<>();
            //旅馆编码
            String domesticGuestId = guestModel.getiDCode() + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
            postData.put("stationId", guestModel.getHotelCode().split("-")[0]);
            postData.put("domesticGuestId", domesticGuestId);
            postData.put("name", guestModel.getName());
            postData.put("sex", this.getGender(guestModel.getiDCode()));
            postData.put("nation", this.getNationCode(guestModel.getNation()));
            postData.put("dateOfBirth", guestModel.getBirthday());
            postData.put("certificateType", "11");
            postData.put("certificateNumber", guestModel.getiDCode());
            postData.put("county", guestModel.getiDCode().substring(0, 6));
            postData.put("address", guestModel.getAddress());
            postData.put("agreeVerify", "0");
            postData.put("certificatePhoto", guestModel.getPhoto());
            postData.put("manualCheck", 0);
            //现场照片
//            postData.put("scenePhoto", guestModel.getCameraPhoto());
            postData.put("roomNumber", guestModel.getRoomNo());
            postData.put("checkInTime", "2024-01-30 16:00:00");
            long timestamp = DateUtil.current();
            postData.put("appid", guestModel.getOperators());
            postData.put("timestamp", timestamp);
            String merchantKey = guestModel.getKey(); // 替换成实际的商户密钥
            // 将所有请求参数（除了sign参数）以ASCII码大小做递增排序
            List<String> paramList = new ArrayList<>(postData.keySet());
            Collections.sort(paramList);
            // 生成签名字符串
            StringBuilder sb = new StringBuilder();
            for (String key : paramList) {
                if (!"sign".equals(key)) {
                    sb.append(key).append("=").append(postData.get(key)).append("&");
                }
            }
            String signStr = sb.toString();
            // 去除末尾的"&"
            signStr = StrUtil.removeSuffix(signStr, "&");
            // 结合商户密钥使用md5生成签名
//            String merchantKey = guestModel.getKey(); // 替换成实际的商户密钥
            String sign = DigestUtil.md5Hex(signStr + "&key=" + merchantKey).toUpperCase();
            System.out.println(sign);
//            String sign =  getSign(postData , merchantKey);
            String url = guestModel.getUrl() + "/v1/secure/upload/hotel/domestic/checkin";
            postData.put("sign", sign);
            for (int i = 0; i < 3; i++) {
                log.info("checkin post url:{}，data:{}", url, postData);
                String body = HttpRequest.post(url).timeout(5000).header("Content-Type", "application/json")
                        .body(JSONObject.fromObject(postData).toString()).execute().body();
                log.info("checkin result:{}", body);
                JSONObject resultMap = JSONObject.fromObject(body);
                if (resultMap.containsKey("code") && resultMap.getString("code").equals("200")) {
                    guestResult.setGuestId(domesticGuestId);
                    guestResult.setGuestNo(domesticGuestId);
                    guestResult.setResult(true);
                    guestResult.setMsg("上传成功");
                    return guestResult;
                }
            }
            guestResult.setResult(false);
            guestResult.setMsg("上传失败");

        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            Map<String, Object> postData = new HashMap<>();

            postData.put("stationId", guestModel.getHotelCode().split("-")[0]);
            postData.put("domesticGuestId", guestModel.getGuestNo());
            postData.put("checkOutTime", guestModel.getCheckOutTime());

            long timestamp = DateUtil.current();
            postData.put("appid", guestModel.getOperators());
            postData.put("timestamp", timestamp);
            // 替换成实际的商户密钥
            String merchantKey = guestModel.getKey();
            // 将所有请求参数（除了sign参数）以ASCII码大小做递增排序
            List<String> paramList = new ArrayList<>(postData.keySet());
            Collections.sort(paramList);
            // 生成签名字符串
            StringBuilder sb = new StringBuilder();
            for (String key : paramList) {
                if (!"sign".equals(key)) {
                    sb.append(key).append("=").append(postData.get(key)).append("&");
                }
            }
            String signStr = sb.toString();
            // 去除末尾的"&"
            signStr = StrUtil.removeSuffix(signStr, "&");
            String sign = DigestUtil.md5Hex(signStr + "&key=" + merchantKey).toUpperCase();
            System.out.println(sign);
            String url = guestModel.getUrl() + "/v1/secure/upload/hotel/domestic/checkout";
            postData.put("sign", sign);
            for (int i = 0; i < 3; i++) {
                log.info("checkin post url:{}，data:{}", url, postData);
                String body = HttpRequest.post(url).timeout(5000).header("Content-Type", "application/json")
                        .body(JSONObject.fromObject(postData).toString()).execute().body();
                log.info("checkin result:{}", body);
                JSONObject resultMap = JSONObject.fromObject(body);
                if (resultMap.containsKey("code") && resultMap.getString("code").equals("200")) {
                    guestResult.setResult(true);
                    guestResult.setMsg("上传成功");
                    return guestResult;
                }
            }
            guestResult.setResult(false);
            guestResult.setMsg("上传失败");
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        return null;
    }


    private static String encodingCharset = "UTF-8";

    /**
     * 获取签名串
     *
     * @param map
     * @return urlParam.append(key).append(" = ").append(paraMap.get ( key) == null ? "" : paraMap.get(key) );
     */
    public static String getStrSort(Map<String, Object> map) {
        ArrayList<String> list = new ArrayList<String>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (null != entry.getValue() && !"".equals(entry.getValue())) {
                list.add(entry.getKey() + "=" + entry.getValue() + "&");
            }
        }
        int size = list.size();
        String[] arrayToSort = list.toArray(new String[size]);
        Arrays.sort(arrayToSort, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < size; i++) {
            sb.append(arrayToSort[i]);
        }
        return sb.toString();
    }

    /**
     * <p><b>Description: </b>计算签名摘要
     *
     * @param map 参数Map
     * @param key 商户秘钥
     * @return
     */
    public static String getSign(Map<String, Object> map, String key) {
        String result = getStrSort(map);
        result += "key=" + key;
        result = md5(result, encodingCharset).toUpperCase();
        return result;
    }

    public static String getSign(String signStr, String key) {
        signStr += "key=" + key;
        String result = md5(signStr, encodingCharset).toUpperCase();
        return result;
    }

    public static String getSignApiKey(String signStr, String key) {
        signStr += "" + key;
        String result = md5(signStr, encodingCharset).toUpperCase();
        return result;
    }

    public static String getSign(String signStr) {
        return md5(signStr, encodingCharset).toUpperCase();
    }

    /**
     * <p><b>Description: </b>MD5
     * <p>2018年9月30日 上午11:33:19
     *
     * @param value
     * @param charset
     * @return
     */
    public static String md5(String value, String charset) {
        MessageDigest md = null;
        try {
            byte[] data = value.getBytes(charset);
            md = MessageDigest.getInstance("MD5");
            byte[] digestData = md.digest(data);
            return toHex(digestData);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String toHex(byte input[]) {
        if (input == null)
            return null;
        StringBuffer output = new StringBuffer(input.length * 2);
        for (int i = 0; i < input.length; i++) {
            int current = input[i] & 0xff;
            if (current < 16)
                output.append("0");
            output.append(Integer.toString(current, 16));
        }

        return output.toString();
    }
}
