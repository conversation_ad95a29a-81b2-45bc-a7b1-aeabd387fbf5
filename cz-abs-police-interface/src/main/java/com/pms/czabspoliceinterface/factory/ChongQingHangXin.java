package com.pms.czabspoliceinterface.factory;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;
import java.util.Base64;
import java.util.Date;

public class ChongQingHangXin extends PoliceBase implements IPolice {
    private static final Logger log = LoggerFactory.getLogger(ChongQingHangXin.class);

    //加密公钥
    //MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCIgXW4A4eW7NHJtJ2WUTSZ/0XWyvd6Ds9APz//6JweMfk9r5vadZBXxxTQi1Wu9WH5bb98sMiBb1Kp70N93AUtoBW98yWM6tzcnaLfiOr20OddzAN3YcF+Vn+6bHuQT+dmlsncnPdPmqJAKR30LwnzTVCUqnpmB4T0KhAZWJQdCQIDAQAB
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        try {
            JSONObject postData = new JSONObject();
            postData.put("platformId", guestModel.getHotelCode().split("-")[1]);
            postData.put("stationId", guestModel.getHotelCode().split("-")[0]);
            String domesticGuestId = guestModel.getiDCode() + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
            postData.put("domesticGuestId", domesticGuestId);
            postData.put("name", guestModel.getName());
            postData.put("sex", this.getSexCode(guestModel.getSex()));
            postData.put("nation", this.getNationCode(guestModel.getNation()));
            postData.put("dateOfBirth", guestModel.getBirthday().length() == 8 ? guestModel.getBirthday().substring(0, 4) + "-" + guestModel.getBirthday().substring(4, 6) + "-" + guestModel.getBirthday().substring(6, 8) : guestModel.getBirthday());
            postData.put("certificateType", "11");
            postData.put("certificateNumber", guestModel.getiDCode());
            postData.put("county", guestModel.getiDCode().substring(0, 6));
            postData.put("address", guestModel.getAddress());
            postData.put("phone", guestModel.getPhoto());
            postData.put("agreeVerify", "0");
            //不是无证的则这样上传
            if (StrUtil.isNotEmpty(guestModel.getPhoto()) && guestModel.getIsNoCard() != 1) {
                postData.put("certificatePhoto", guestModel.getPhoto());
            }
//            if (StrUtil.isNotEmpty(guestModel.getCameraPhoto())) {
//                postData.put("scenePhoto", guestModel.getCameraPhoto());
//            }
            postData.put("roomNumber", guestModel.getRoomNo());
            postData.put("checkInTime", guestModel.getCheckInTime());
            postData.put("manualCheck", "0");
            postData.put("uploadType", "0");
            String url = guestModel.getUrl() + "/PSBinterface/api/upload/standardHotel";
            String encry = encry("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCIgXW4A4eW7NHJtJ2WUTSZ/0XWyvd6Ds9APz//6JweMfk9r5vadZBXxxTQi1Wu9WH5bb98sMiBb1Kp70N93AUtoBW98yWM6tzcnaLfiOr20OddzAN3YcF+Vn+6bHuQT+dmlsncnPdPmqJAKR30LwnzTVCUqnpmB4T0KhAZWJQdCQIDAQAB", postData.toString());
            String token = "Bearer " + getToken(guestModel);
            for (int i = 0; i < 3; i++) {
                log.info("checkin:url:{}，token:{}，data:{}", url, token, encry);
                String body = HttpRequest.post(url).header(Header.CONTENT_TYPE, "application/json").header(Header.AUTHORIZATION, token).timeout(5000).body(encry).execute().body();
                log.info("checkin:result:{}", body);
                JSONObject resultMap = JSONObject.fromObject(body);
                if (resultMap.containsKey("code") && resultMap.getString("code").equals("0")) {
                    guestResult.setGuestId(domesticGuestId);
                    guestResult.setGuestNo(domesticGuestId);
                    guestResult.setResult(true);
                    guestResult.setMsg("上传成功");
                    return guestResult;
                }
            }
            guestResult.setResult(false);
            guestResult.setMsg("上传失败");
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        try {
            JSONObject postData = new JSONObject();
            postData.put("platformId", guestModel.getHotelCode().split("-")[1]);
            postData.put("stationId", guestModel.getHotelCode().split("-")[0]);
            postData.put("domesticGuestId", guestModel.getGuestNo());
            postData.put("checkOutTime", guestModel.getCheckOutTime());
            postData.put("uploadType", "2");
            String url = guestModel.getUrl() + "/PSBinterface/api/upload/standardHotel";
            String encry = encry("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCIgXW4A4eW7NHJtJ2WUTSZ/0XWyvd6Ds9APz//6JweMfk9r5vadZBXxxTQi1Wu9WH5bb98sMiBb1Kp70N93AUtoBW98yWM6tzcnaLfiOr20OddzAN3YcF+Vn+6bHuQT+dmlsncnPdPmqJAKR30LwnzTVCUqnpmB4T0KhAZWJQdCQIDAQAB", postData.toString());
            String token = "Bearer " + getToken(guestModel);
            for (int i = 0; i < 3; i++) {
                log.info("checkout:url:{}，token:{}，data:{}", url, token, encry);
                String body = HttpRequest.post(url).header(Header.CONTENT_TYPE, "application/json").header(Header.AUTHORIZATION, token).timeout(5000).body(encry).execute().body();
                log.info("checkout:result:{}", body);
                JSONObject resultMap = JSONObject.fromObject(body);
                if (resultMap.containsKey("code") && resultMap.getString("code").equals("0")) {
                    guestResult.setResult(true);
                    guestResult.setMsg("上传成功");
                    return guestResult;
                }
            }
            guestResult.setResult(false);
            guestResult.setMsg("上传失败");
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        return null;
    }

    public String getToken(GuestModel guestModel) {
        String token = "";
        String url = guestModel.getUrl() + "/PSBinterface/api/uploadToken";
        JSONObject postData = new JSONObject();
        postData.put("appKey", guestModel.getOperators().split("-")[0]);
        postData.put("appSecret", guestModel.getOperators().split("-")[1]);
        String encry = encry("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCIgXW4A4eW7NHJtJ2WUTSZ/0XWyvd6Ds9APz//6JweMfk9r5vadZBXxxTQi1Wu9WH5bb98sMiBb1Kp70N93AUtoBW98yWM6tzcnaLfiOr20OddzAN3YcF+Vn+6bHuQT+dmlsncnPdPmqJAKR30LwnzTVCUqnpmB4T0KhAZWJQdCQIDAQAB", postData.toString());
        log.info("getToken:url:{}，data:{}", url, encry);
        String body = HttpRequest.post(url).header(Header.CONTENT_TYPE, "application/json").timeout(5000).body(encry).execute().body();
        log.info("getToken:result:{}", body);
        JSONObject resultMap = JSONObject.fromObject(body);
        if (resultMap.containsKey("code") || resultMap.getString("code").equals("200")) {
            token = "";
        }
        token = resultMap.getString("token");
        return token;
    }


    public static String encry(String publicKeyString, String plainText) {
        String result = "";
        try {
            log.info("加密前：" + plainText);
            // 将公钥字符串转换为公钥对象
            byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyString);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(keySpec);
            // 创建RSA加密器
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            Integer MAX_ENCRYPT_BLOCK = 117;
            Integer offSet = 0;
            byte[] resultBytes = {};
            byte[] cache = {};
            int inputLength = plainText.getBytes().length;
            while (plainText.getBytes().length - offSet > 0) {
                if (inputLength - offSet > MAX_ENCRYPT_BLOCK) {
                    cache = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8), offSet, MAX_ENCRYPT_BLOCK);
                    offSet += MAX_ENCRYPT_BLOCK;
                } else {
                    cache = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8), offSet, inputLength - offSet);
                    offSet = inputLength;
                }
                resultBytes = Arrays.copyOf(resultBytes, resultBytes.length + cache.length);
                System.arraycopy(cache, 0, resultBytes, resultBytes.length - cache.length, cache.length);
            }
            // 将密文转换为Base64编码的字符串
            result = Base64.getEncoder().encodeToString(resultBytes);
            log.info("加密后：" + result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
}
