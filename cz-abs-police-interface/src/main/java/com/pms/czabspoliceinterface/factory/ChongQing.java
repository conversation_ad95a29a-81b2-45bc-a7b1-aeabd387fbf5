package com.pms.czabspoliceinterface.factory;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Encoder;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;


public class ChongQing extends PoliceBase implements IPolice {
    private static final Logger log = LoggerFactory.getLogger(ChongQing.class);

    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            Map<String, Object> postData = new HashMap<>();
            //旅馆编码
            String domesticGuestId = guestModel.getiDCode() + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
            postData.put("stationId", guestModel.getHotelCode().split("-")[0]);
            postData.put("domesticGuestId", domesticGuestId);
            postData.put("name", guestModel.getName());
            postData.put("sex", this.getGender(guestModel.getiDCode()));
            postData.put("nation", this.getNationCode(guestModel.getNation()));
            postData.put("dateOfBirth", guestModel.getBirthday().length() == 8 ? guestModel.getBirthday().substring(0, 4) + "-" + guestModel.getBirthday().substring(4, 6) + "-" + guestModel.getBirthday().substring(6, 8) : guestModel.getBirthday());
            postData.put("certificateType", "11");
            postData.put("certificateNumber", guestModel.getiDCode());
            postData.put("county", guestModel.getiDCode().substring(0, 6));
            postData.put("address", guestModel.getAddress());
            postData.put("agreeVerify", "0");
            postData.put("certificatePhoto", guestModel.getPhoto());
            postData.put("manualCheck", 0);
            //现场照片
//            postData.put("scenePhoto", guestModel.getCameraPhoto());
            postData.put("roomNumber", guestModel.getRoomNo());
            postData.put("checkInTime", "2024-01-30 16:00:00");
            long timestamp = DateUtil.current();
            postData.put("appid", guestModel.getOperators());
            postData.put("timestamp", timestamp);
            String merchantKey = guestModel.getKey(); // 替换成实际的商户密钥
            // 将所有请求参数（除了sign参数）以ASCII码大小做递增排序
            List<String> paramList = new ArrayList<>(postData.keySet());
            Collections.sort(paramList);
            // 生成签名字符串
            StringBuilder sb = new StringBuilder();
            for (String key : paramList) {
                if (!"sign".equals(key)) {
                    sb.append(key).append("=").append(postData.get(key)).append("&");
                }
            }
            String signStr = sb.toString();
            // 去除末尾的"&"
            signStr = StrUtil.removeSuffix(signStr, "&");
            // 结合商户密钥使用md5生成签名
//            String merchantKey = guestModel.getKey(); // 替换成实际的商户密钥
            String sign = DigestUtil.md5Hex(signStr + "&key=" + merchantKey).toUpperCase();
            log.info("sign:{}", sign);
//            String sign =  getSign(postData , merchantKey);
            String url = guestModel.getUrl() + "/v1/secure/upload/hotel/domestic/checkin";
            postData.put("sign", sign);
            for (int i = 0; i < 3; i++) {
                log.info("checkin post url:{}，data:{}", url, postData);
                String body = HttpRequest.post(url).timeout(5000).header("Content-Type", "application/json")
                        .body(net.sf.json.JSONObject.fromObject(postData).toString()).execute().body();
                log.info("checkin result:{}", body);
                net.sf.json.JSONObject resultMap = net.sf.json.JSONObject.fromObject(body);
                if (resultMap.containsKey("code") && resultMap.getString("code").equals("200")) {
                    guestResult.setGuestId(domesticGuestId);
                    guestResult.setGuestNo(domesticGuestId);
                    guestResult.setResult(true);
                    guestResult.setMsg("上传成功");
                    return guestResult;
                }
            }
            guestResult.setResult(false);
            guestResult.setMsg("上传失败");

        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            Map<String, Object> postData = new HashMap<>();

            postData.put("stationId", guestModel.getHotelCode().split("-")[0]);
            postData.put("domesticGuestId", guestModel.getGuestNo());
            postData.put("checkOutTime", guestModel.getCheckOutTime());

            long timestamp = DateUtil.current();
            postData.put("appid", guestModel.getOperators());
            postData.put("timestamp", timestamp);
            // 替换成实际的商户密钥
            String merchantKey = guestModel.getKey();
            // 将所有请求参数（除了sign参数）以ASCII码大小做递增排序
            List<String> paramList = new ArrayList<>(postData.keySet());
            Collections.sort(paramList);
            // 生成签名字符串
            StringBuilder sb = new StringBuilder();
            for (String key : paramList) {
                if (!"sign".equals(key)) {
                    sb.append(key).append("=").append(postData.get(key)).append("&");
                }
            }
            String signStr = sb.toString();
            // 去除末尾的"&"
            signStr = StrUtil.removeSuffix(signStr, "&");
            String sign = DigestUtil.md5Hex(signStr + "&key=" + merchantKey).toUpperCase();
            System.out.println(sign);
            String url = guestModel.getUrl() + "/v1/secure/upload/hotel/domestic/checkout";
            postData.put("sign", sign);
            for (int i = 0; i < 3; i++) {
                log.info("checkin post url:{}，data:{}", url, postData);
                String body = HttpRequest.post(url).timeout(5000).header("Content-Type", "application/json")
                        .body(net.sf.json.JSONObject.fromObject(postData).toString()).execute().body();
                log.info("checkin result:{}", body);
                net.sf.json.JSONObject resultMap = net.sf.json.JSONObject.fromObject(body);
                if (resultMap.containsKey("code") && resultMap.getString("code").equals("200")) {
                    guestResult.setResult(true);
                    guestResult.setMsg("上传成功");
                    return guestResult;
                }
            }
            guestResult.setResult(false);
            guestResult.setMsg("上传失败");
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        guestResult.setMsg("暂未实现");
        return guestResult;
    }

    /**
     * 根据身份证号判断性别 奇数代表男 偶数代表女
     *
     * @param idNumber
     * @return
     */
    public static String gender(String idNumber) {
        int gender = 0;
        if (idNumber.length() == 18) {
            //如果身份证号18位，取身份证号倒数第二位
            char c = idNumber.charAt(idNumber.length() - 2);
            gender = Integer.parseInt(String.valueOf(c));
        } else {
            //如果身份证号15位，取身份证号最后一位
            char c = idNumber.charAt(idNumber.length() - 1);
            gender = Integer.parseInt(String.valueOf(c));
        }
        if (gender % 2 == 1) {
            return "1";
        } else {
            return "2";
        }
    }

    /**
     * 图片转base64字符串
     *
     * @param imgFile 图片路径
     * @return
     */
    public static String imageToBase64Str(String imgFile) {
        InputStream inputStream = null;
        byte[] data = null;
        try {
            inputStream = new FileInputStream(imgFile);
            data = new byte[inputStream.available()];
            inputStream.read(data);
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 加密
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(data);
    }
}
