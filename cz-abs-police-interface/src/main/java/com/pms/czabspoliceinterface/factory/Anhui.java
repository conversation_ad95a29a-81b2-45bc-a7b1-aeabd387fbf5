package com.pms.czabspoliceinterface.factory;

import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;

public class <PERSON>hui extends PoliceBase implements IPolice {
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            JSONArray guestList =  new JSONArray();
            JSONObject guestData = new JSONObject();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            guestData.put("postid", guestModel.getiDCode() + simpleDateFormat.format(new Date()));
            guestData.put("lkbm" ,"");
            guestData.put("xm",guestModel.getName());
            guestData.put("xb",this.getSexCode(guestModel.getSex()));
            guestData.put("mz",this.getNationCode(guestModel.getNation()));
            guestData.put("csrq",guestModel.getBirthday().replace("-",""));
            guestData.put("zjlx","11");
            guestData.put("zjhm",guestModel.getiDCode());
            guestData.put("ssxq",guestModel.getiDCode().substring(0,6));//是否存在问题？
            guestData.put("xz",guestModel.getAddress());
            guestData.put("rzsj",new SimpleDateFormat("yyyyMMddHHmm").format(new Date()));
            guestData.put("rzlc",guestModel.getRoomNo().substring(0,1));
            guestData.put("rzfh",guestModel.getRoomNo());
            guestData.put("tfsj","");
            guestData.put("xyklx","");
            guestData.put("xykhm","");
            guestData.put("zy","");
            guestData.put("hcl","");
            guestData.put("hcq","");
            guestData.put("lcsy","");
            guestData.put("djr","");
            guestData.put("bz","");
            guestData.put("fzqx","");
            guestData.put("sfsjdx","");
            guestData.put("cphm","");
            guestData.put("cpsz","");
            guestData.put("lxdh","");
            guestData.put("picture",guestModel.getPhoto());
            guestList.add(guestData);

        }catch (Exception e){
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;

    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        return null;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        guestResult.setMsg("暂未实现");
        return guestResult;
    }
}
