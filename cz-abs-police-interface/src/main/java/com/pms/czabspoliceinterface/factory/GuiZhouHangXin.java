package com.pms.czabspoliceinterface.factory;

import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czabspoliceinterface.utils.HttpUtil;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;

import java.util.Date;

public class GuiZhouHangXin extends PoliceBase implements IPolice {
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            String param = this.checkinParam(guestModel);
            if (param != null) {
                guestResult.setMsg(param);
                return guestResult;
            }
            String token = getToken(guestModel);
            if (token == null || token.equals("")) {
                guestResult.setMsg("获取Token失败");
                return guestResult;
            }
            String guestNo = HotelUtils.dateToNoLineStrYmdhms(new Date());
            JSONObject guestData = new JSONObject();
            guestData.put("fh", guestModel.getRoomNo());
            guestData.put("rzsj", guestNo);
            guestData.put("zwxm", guestModel.getName());
            guestData.put("zjhm", guestModel.getiDCode());
            guestData.put("zjlx", "1");
            guestData.put("xb", this.getGender(guestModel.getiDCode()));
            guestData.put("csrq", guestModel.getBirthday().replace("-", ""));
            guestData.put("mz", this.getNationCode(guestModel.getNation()));
            guestData.put("jg", guestModel.getiDCode().substring(0, 6));
            guestData.put("address", guestModel.getAddress());
            guestData.put("idcardzp", guestModel.getPhoto());
            guestData.put("camerazp", guestModel.getCameraPhoto() == null || guestModel.getCameraPhoto().equals("") ? guestModel.getPhoto() : guestModel.getCameraPhoto());
            System.out.println("上传地址:" + guestModel.getUrl() + "/uploadDomesticPersonnelInfo");
            System.out.println("token:" + token);
            System.out.println("参数:" + guestData.toString());
            String res = HttpUtil.createPostHttpRequestUserid(guestModel.getUrl() + "/uploadDomesticPersonnelInfo", token, guestData.toString());
            System.out.println("res");
            JSONObject data = JSONObject.fromObject(res);
            if (!data.containsKey("succeed") || !data.getString("succeed").equals("true")) {
                guestResult.setMsg("上传失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestNo(guestNo);
            guestResult.setGuestId(guestNo);
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            String token = getToken(guestModel);
            if (token == null || token.equals("")) {
                guestResult.setMsg("获取Token失败");
                return guestResult;
            }
            JSONObject guestData = new JSONObject();
            guestData.put("rzsj", guestModel.getGuestNo());
            guestData.put("tfsj", HotelUtils.dateToNoLineStrYmdhms(new Date()));
            guestData.put("zjhm", guestModel.getiDCode());
            System.out.println("上传地址:" + guestModel.getUrl() + "/leaveHotel");
            System.out.println("token:" + token);
            System.out.println("参数:" + guestData.toString());
            String res = HttpUtil.createPostHttpRequestUserid(guestModel.getUrl() + "/leaveHotel", token, guestData.toString());
            System.out.println("res");
            JSONObject data = JSONObject.fromObject(res);
            if (!data.containsKey("succeed") || !data.getString("succeed").equals("true")) {
                guestResult.setMsg("上传失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("贵州旅馆业数据退房上传成功");
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        guestResult.setMsg("暂未实现");
        return guestResult;
    }


    public String getToken(GuestModel guestModel) {
        try {
            JSONObject postData = new JSONObject();
            postData.put("key", guestModel.getKey());
            postData.put("hotelsn", guestModel.getHotelCode());
            String res = HttpUtil.sendPost(guestModel.getUrl() + "/authdevice", "", postData.toString());
            JSONObject data = JSONObject.fromObject(res);
            if (!data.containsKey("succeed") || !data.getString("succeed").equals("true")) {
                return "";
            }
            return data.getJSONObject("result").getString("sessionid");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
}
