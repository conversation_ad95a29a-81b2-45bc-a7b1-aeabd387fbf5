package com.pms.czabspoliceinterface.factory;

import com.pms.czpmsutils.HttpRequest;
import com.pms.czpmsutils.ResponseData;
import net.sf.json.JSONObject;

public class DianJing {
    public ResponseData pushData(JSONObject data){
        ResponseData responseData = ResponseData.newSuccessData();
        try {

            if (null == data.get("url") || data.getString("url").equals("")){
                throw new Exception("推送地址不能空");
            }
            if (null == data.get("data") || data.getString("data").equals("")){
                throw new Exception("推送地址不能空");
            }
            JSONObject postData = new JSONObject();
            postData.put("data",data.getJSONArray("data"));
            postData.put("type",data.getInt("type"));
            System.out.println("推送参数:" + postData.toString());
            String result = HttpRequest.doPost(data.getString("url"), postData.toString());

            if (null == result || !result.equals("success")){
                throw  new Exception("推送失败");
            }
            responseData.setMsg("推送成功");
        }catch (Exception e){
            e.printStackTrace();
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
