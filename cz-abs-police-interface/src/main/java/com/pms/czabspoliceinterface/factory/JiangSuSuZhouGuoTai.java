package com.pms.czabspoliceinterface.factory;

import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czabspoliceinterface.utils.HttpUtil;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;

import java.net.URLEncoder;

public class JiangSuSuZhouGuoTai extends PoliceBase implements IPolice {
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            JSONObject guestData = new JSONObject();
            guestData.put("pType" ,"0");
            guestData.put("psbId" ,guestModel.getHotelCode());
            guestData.put("idNo" , guestModel.getiDCode());
            guestData.put("name" ,guestModel.getName());
            guestData.put("sex" ,this.getSexCode(guestModel.getSex()));
            guestData.put("nationCode" ,this.getNationCode(guestModel.getNation()));
            guestData.put("birth" ,guestModel.getBirthday().replace("/","-"));
            guestData.put("idType" ,"11");
            guestData.put("province" ,guestModel.getiDCode().substring(0,6));
            guestData.put("address" ,guestModel.getAddress());
            guestData.put("checkInDate" ,guestModel.getCheckInTime() != null  ? guestModel.getCheckInTime() : HotelUtils.currentTime());
            guestData.put("roomNo" , guestModel.getRoomNo());
            guestData.put("photo" , URLEncoder.encode(guestModel.getPhoto()));
            guestData.put("ScenePhoto" ,URLEncoder.encode(guestModel.getCameraPhoto()));
            //2022-01-06 新增的几项
            guestData.put("tw" ,guestModel.getTemperature() != null  ? guestModel.getTemperature() :"36.5");
            guestData.put("jkm" ,guestModel.getHealthCode()!=null ? guestModel.getHealthCode() : "1");//1-绿码 2-黄码  3-红码
//            guestData.put("jkmflag","1");
//            guestData.put("jkmimg" ,"36.5");
            guestData.put("xcgj" ,guestModel.getGoAddress()!=null ? guestModel.getGoAddress() :"上海市,苏州市");
//            guestData.put("xcgjimg" ,"");
            guestData.put("sjh",guestModel.getPhone());
            guestData.put("Semblance" ,"0.87");
            guestData.put("faceResult" ,"1");
            guestData.put("Operator" ,"管理员");
            System.out.println(guestData.toString());
            String url = guestModel.getUrl() + "/CheckIn";
            String params = "json=" + guestData.toString();
            String res = HttpUtil.sendPostGBK(url, params);
            System.out.println("请求返回:" + res);
            Boolean success = res.contains(guestModel.getiDCode()) && res.contains("已经办理了入住");
            if (!res.equals("1") && !success){
                throw new Exception("上传失败:" + res);
            }
            guestResult.setResult(true);
            guestResult.setGuestId(guestModel.getiDCode());
            guestResult.setGuestNo(guestModel.getRoomNo());
        }catch (Exception e){
            e.printStackTrace();
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            StringBuilder postData = new StringBuilder();
            postData.append("psbId=").append(guestModel.getHotelCode());
            postData.append("&roomNo=").append(guestModel.getRoomNo());
            postData.append("&checkOutDate=").append(HotelUtils.currentTime());
            String url = guestModel.getUrl() + "/CheckOut";
            String res = HttpUtil.sendPostGBK(url, postData.toString());
            System.out.println("请求参数:" + url + " " + postData.toString() + "请求返回:" + res);
            if (res == null || res.equals("") || !res.equals("1")){
                throw new Exception("上传失败:" + res);
            }
            guestResult.setResult(true);
            guestResult.setMsg("退宿数据上传成功");

        }catch (Exception e){
            e.printStackTrace();
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        guestResult.setMsg("暂未实现");
        return guestResult;
    }

    public GuestResult noCardVer(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            JSONObject postData = new JSONObject();
            postData.put("sfzmhm", guestModel.getiDCode());
            postData.put("xm", guestModel.getName());
            postData.put("photo", URLEncoder.encode(guestModel.getCameraPhoto()));//头像
            postData.put("machineId", guestModel.getHotelCode());
            postData.put("hotelId", guestModel.getHotelCode());
            postData.put("ly", "3");
            String url = "http://58.210.180.134:8891/HotelSer/szwzyz";
            System.out.println("参数:" + postData.toString());
            String params = "json=" + postData.toString();
            String res = HttpUtil.sendPostGBK(url, params);
            System.out.println("请求返回:" + res);
            JSONObject result = JSONObject.fromObject(res);
            String code = result.getString("code");
            if (!code.equals("0")) {
                throw new Exception("认证失败:" + res);
            }
            guestResult.setResult(true);
            guestResult.setGuestId(guestModel.getiDCode());
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }
}
