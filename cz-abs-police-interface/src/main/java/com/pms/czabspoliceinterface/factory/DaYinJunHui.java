package com.pms.czabspoliceinterface.factory;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.webservice.SoapClient;
import cn.hutool.json.JSONUtil;
import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czpmsutils.IdcardInfoExtractor;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


public class DaYinJunHui extends PoliceBase implements IPolice {
    public static Logger log = LogManager.getLogger(DaYinJunHui.class);
    StringRedisTemplate stringRedisTemplate;

    public DaYinJunHui(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }


    public static String getNationECode1(String nation) {
        if (nation.equals(null) || nation == "") {
            return "HA";
        }
        switch (nation) {
            case "汉":
                return "HA";
            case "汉族":
                return "HA";
            case "蒙古族":
                return "MG";
            case "回族":
                return "HU";
            case "藏族":
                return "ZA";
            case "维吾尔族":
                return "UG";
            case "苗族":
                return "MH";
            case "彝族":
                return "YI";
            case "壮族":
                return "ZH";
            case "布依族":
                return "BY";
            case "朝鲜族":
                return "CS";
            case "满族":
                return "MA";
            case "侗族":
                return "DO";
            case "瑶族":
                return "YA";
            case "白族":
                return "BA";
            case "土家族":
                return "TJ";
            case "哈尼族":
                return "HN";
            case "哈萨克族":
                return "KZ";
            case "傣族":
                return "DA";
            case "黎族":
                return "LI";
            case "傈僳族":
                return "LS";
            case "佤族":
                return "VA";
            case "畲族":
                return "SH";
            case "高山族":
                return "GS";
            case "拉祜族":
                return "LH";
            case "水族":
                return "SU";
            case "东乡族":
                return "DX";
            case "纳西族":
                return "NX";
            case "景颇族":
                return "JP";
            case "柯尔克孜族":
                return "KG";
            case "土族":
                return "TU";
            case "达斡尔族":
                return "DU";
            case "仫佬族":
                return "ML";
            case "羌族":
                return "QI";
            case "布朗族":
                return "BL";
            case "撒拉族":
                return "SL";
            case "毛南族":
                return "MN";
            case "仡佬族":
                return "GL";
            case "锡伯族":
                return "XB";
            case "阿昌族":
                return "AC";
            case "普米族":
                return "PM";
            case "塔吉克族":
                return "TA";
            case "怒族":
                return "NU";
            case "乌孜别克族":
                return "UZ";
            case "俄罗斯族":
                return "RS";
            case "鄂温克族":
                return "EW";
            case "德昂族":
                return "DE";
            case "保安族":
                return "BN";
            case "裕固族":
                return "YG";
            case "京族":
                return "GI";
            case "塔塔尔族":
                return "TT";
            case "独龙族":
                return "DR";
            case "鄂伦春族":
                return "OR";
            case "赫哲族":
                return "HZ";
            case "门巴族":
                return "MB";
            case "珞巴族":
                return "LB";
            case "基诺族":
                return "JN";
            default:
                break;
        }
        return "HA";
    }
    public String getToken(GuestModel guestModel) {
        String token = "";
        try {
            final HashOperations<String, Object, Object> userCahe = stringRedisTemplate.opsForHash();
            String key = guestModel.getHotelCode() + DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
            Object value = userCahe.get("dayinjunhui", key);
            if (value != null) {
                token = value.toString();
                return token;
            }
            JSONObject userInfo = new JSONObject();
            userInfo.put("userName", guestModel.getOperators().split("-")[0]);
            userInfo.put("userPwd", guestModel.getOperators().split("-")[1]);
            Map<String, Object> postData = new HashMap<>();
            postData.put("jsonString", userInfo.toString());
            String helloDyne = SoapClient.create(guestModel.getUrl()).setMethod("getToken").setParams(postData).send();
            cn.hutool.json.JSONObject resultMap = JSONUtil.parseFromXml(helloDyne);
            System.out.println("get token:" + resultMap.toString());
            if (!resultMap.containsKey("soap:Envelope")) {
                return "";
            }
            String result = resultMap.getJSONObject("soap:Envelope").getJSONObject("soap:Body").getJSONObject("ns1:getTokenResponse").get("ns1:result").toString();
            JSONObject resultData = JSONObject.fromObject(result);
            if (resultData.getInt("resultCode") != 0) {
                return "";
            }
            token = resultData.getString("tokeninfo");
            userCahe.put("dayinjunhui", key, token);
        } catch (Exception e) {
            e.printStackTrace();
            token = "";
        }
        return token;
    }

    public static String encry(String publicKeyString, String plainText) {
        String result = "";
        try {
            // 将公钥字符串转换为公钥对象
            byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyString);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(keySpec);

            // 创建RSA加密器
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, publicKey);
            // 加密明文
            byte[] cipherTextBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            // 将密文转换为Base64编码的字符串
            result = Base64.getEncoder().encodeToString(cipherTextBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;

    }

    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            String token = getToken(guestModel);
            Map<String, Object> guest = new HashMap<>();
//            String publicKey = guestModel.getKey();
            String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAghoo4xEkkJxtR4T6e0VoF9rehNT5+AVkDW+0HwditrwI4MfU5zasqqPazK+D7tOOc045IFC3h+Q2KHMGqth7/jyTywXorV1W8tBJrgojtBAbN6VMPnnUy9tNb0ABmA1HXKuxdsGBCmtbXWUuJ3VOkq70U5tHQPcBPRg0IMBKzPELQ2YLbkZ3qlfg2Zm0+fBOtorpX240TEKbB9pcT5wGzguSAIphHuN9NkpkvnFYGt3KP2lWYaPVeOPdWdRBqPn9oJK2HoJgNOEquqder2EGLJ7S0eeoL4L8smEqZkVg2+j45jnD6Z28KPKyB1Wdn75b9m9V5xTHfpTKJuJa1KgZawIDAQAB";
            log.info("publicKey:{}", publicKey);
//            publicKey.replaceAll(" ","+");
            guest.put("name", encry(publicKey, guestModel.getName()));
            guest.put("gender", encry(publicKey, this.getSexCode(new IdcardInfoExtractor(guestModel.getiDCode()).getGender())));
            //民族 见字典
            guest.put("nation", encry(publicKey, getNationECode1(guestModel.getNation())));
            //出生日期 YYYYMMDD
            guest.put("bdate", encry(publicKey, guestModel.getBirthday().replaceAll("-", "")));
            //证件类型
            guest.put("idName", encry(publicKey, "11"));
            //证件号码
            guest.put("idCode", encry(publicKey, guestModel.getiDCode()));
            //户籍地  证件号前6位
            guest.put("xzqh", encry(publicKey, guestModel.getiDCode().substring(0, 6)));
            //家庭住址
            guest.put("address", encry(publicKey, guestModel.getAddress()));
            //入住时间 YYYYMMDDHHMM
            guest.put("inTime", encry(publicKey, DateUtil.format(new Date(), "yyyyMMddHHmmss").substring(0, 12)));
            //终端类型 PMS
            guest.put("pdaflag", encry(publicKey, "PMS"));
            //酒店编号
            guest.put("hotelid", encry(publicKey, guestModel.getHotelCode()));
            //入住房号
            guest.put("noRoom", encry(publicKey, guestModel.getRoomNo()));
            guest.put("phone", encry(publicKey, guestModel.getPhone()));
            String pic = guestModel.getPhoto();
            //证件照片 入住必填 base64编码  大小不能超过50k 不加密
            guest.put("pic", pic);
            //照片长度  10 字节 不加密
            guest.put("piclen", pic.length());
            guest.put("tokeninfo", token);
            //现场照片 按配置 base64编码  大小不能超过100k 不加密
            guest.put("face", guestModel.getCameraPhoto());
//        //相似度 按配置 base64编码  大小不能超过100k 不加密
            guest.put("xsd", "97");
            Map<String, Object> postData = new HashMap<>();
            postData.put("jsonString", JSONObject.fromObject(guest).toString());
            log.info("checkin:url:{}，token:{}，data:{}", guestModel.getUrl(), token, JSONObject.fromObject(guest).toString());
            String data = SoapClient.create(guestModel.getUrl()).setMethod("checkIn_Json").setParams(postData).send();
            cn.hutool.json.JSONObject resultMap = JSONUtil.parseFromXml(data);
            log.info("checkin:result:{}", resultMap.toString());
            if (!resultMap.containsKey("soap:Envelope")) {
                return guestResult;
            }
            String result = resultMap.getJSONObject("soap:Envelope").getJSONObject("soap:Body").getJSONObject("ns1:checkIn_JsonResponse").get("ns1:result").toString();
            JSONObject resultData = JSONObject.fromObject(result);
            if (resultData.getInt("resultCode") != 0) {
                return guestResult;
            }
            String guestId = resultData.getString("guestId");
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestNo(guestId);
            guestResult.setGuestId(guestId);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            Map<String, Object> guest = new HashMap<>();
            String token = getToken(guestModel);
            guest.put("id", guestModel.getGuestNo());
            guest.put("outTime", DateUtil.format(new Date(), "yyyyMMddHHmmss").substring(0, 12));
            guest.put("hotelid", guestModel.getHotelCode());
            guest.put("tokeninfo", token);
            Map<String, Object> postData = new HashMap<>();
            postData.put("jsonString", JSONObject.fromObject(guest).toString());
            log.info("checkout:url:{}，token:{}，data:{}", guestModel.getUrl(), token, JSONObject.fromObject(guest).toString());
            String data = SoapClient.create(guestModel.getUrl()).setMethod("checkOut_Json").setParams(postData).send();
            cn.hutool.json.JSONObject resultMap = JSONUtil.parseFromXml(data);
            log.info("checkin:result:{}", resultMap.toString());
            if (!resultMap.containsKey("soap:Envelope")) {
                return guestResult;
            }
            String result = resultMap.getJSONObject("soap:Envelope").getJSONObject("soap:Body").getJSONObject("ns1:checkOut_JsonResponse").get("ns1:result").toString();
            JSONObject resultData = JSONObject.fromObject(result);
            if (resultData.getInt("resultCode") != 0) {
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        return null;
    }
}
