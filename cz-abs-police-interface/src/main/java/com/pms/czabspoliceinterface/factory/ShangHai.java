package com.pms.czabspoliceinterface.factory;

import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czabspoliceinterface.utils.HttpUtil;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.codec.binary.Base64;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

public class ShangHai extends PoliceBase implements IPolice {
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);

        String tokenId = guestModel.getTokenId();
        Boolean getToken = false;

        System.out.println(JSONObject.fromObject(guestModel).toString());

        try {
            /**
             * 0.获取参数配置
             **/
            String operatorS = guestModel.getOperators();      //存放用户名，密码，从业人员证件号码
            String[] operatorArray = operatorS.split(",");
            String loginName = operatorArray[0];        //用户名
            String pwd = operatorArray[1];              //密码
            String identificationNumber = operatorArray[2];     //从业人员证件号码
            String key = guestModel.getKey();                //存放接口密钥，加密密钥
            String[] keyArray = key.split(",");
            String interfaceKey = keyArray[0];          //接口密钥
            String encryptKey = keyArray[1];            //加密密钥
            String hotelCode = guestModel.getHotelCode();    //存放设备编号，设备类型
            String[] hotelCodeArray = hotelCode.split(",");
            String deviceCode = hotelCodeArray[0];      //设备编号
            String deviceType = hotelCodeArray[1];      //设备类型
            if(tokenId==null||"".equals(tokenId)){

                JSONObject userLoginResult = userLogin(guestModel);
                if (!userLoginResult.getString("Result").equals("True")){
                    guestResult.setMsg("发送失败:" + userLoginResult.getString("Msg").toString());
                    return guestResult;
                }
                tokenId = userLoginResult.getString("tokenId");
                getToken = true;
            }
            String cidcardRegisterUrl = "";

            if (guestModel.getIsChildren() == 0){
                /**
                 * 非同住人，调用境内证件登记接口
                 **/
                cidcardRegisterUrl = guestModel.getUrl() + "/rest/register/cidcardRegister" + "?deviceCode=" + deviceCode + "&interfaceKey=" + interfaceKey + "&tokenId=" + tokenId;
                JSONObject appCidcardRegisterVo = new JSONObject();
                appCidcardRegisterVo.put("similarityDegree",encryptAES(guestModel.getSemblance(),encryptKey,encryptKey));
                appCidcardRegisterVo.put("deviceType",encryptAES(deviceType,encryptKey,encryptKey));
                appCidcardRegisterVo.put("htCidcardRoomnum",encryptAES(guestModel.getRoomNo(),encryptKey,encryptKey));
                appCidcardRegisterVo.put("htCidcardCardtype",encryptAES("身份证",encryptKey,encryptKey));
                appCidcardRegisterVo.put("htCidcardCardnumber",encryptAES(guestModel.getiDCode(),encryptKey,encryptKey));
                appCidcardRegisterVo.put("htCidcardUsername",encryptAES(guestModel.getName(),encryptKey,encryptKey));
                appCidcardRegisterVo.put("htCidcardSex",encryptAES(this.getSexCode(guestModel.getSex()),encryptKey,encryptKey));
                appCidcardRegisterVo.put("htCidcardBirthday",encryptAES(guestModel.getBirthday(),encryptKey,encryptKey));
                appCidcardRegisterVo.put("htCidcardPermanentaddress",encryptAES(guestModel.getAddress(),encryptKey,encryptKey));
                appCidcardRegisterVo.put("htCidcardNation",encryptAES(guestModel.getNation(),encryptKey,encryptKey));
                appCidcardRegisterVo.put("htCidcardRegisterType",encryptAES("15",encryptKey,encryptKey));
                appCidcardRegisterVo.put("profilePhoto",encryptAES(guestModel.getPhoto(),encryptKey,encryptKey));
                appCidcardRegisterVo.put("photo",encryptAES(guestModel.getCameraPhoto(),encryptKey,encryptKey));
                appCidcardRegisterVo.put("htCardtypeCode",encryptAES("1",encryptKey,encryptKey));
                appCidcardRegisterVo.put("htNationCode",encryptAES(this.getNationCode(guestModel.getNation()),encryptKey,encryptKey));
                appCidcardRegisterVo.put("htStoragePhone",encryptAES(guestModel.getPhone(),encryptKey,encryptKey));
                appCidcardRegisterVo.put("htCidcardPhone",encryptAES(guestModel.getPhone(),encryptKey,encryptKey));

                String res = HttpUtil.sendPost(cidcardRegisterUrl, appCidcardRegisterVo.toString());
                JSONObject cidcardRegisterResult = JSONObject.fromObject(res);

                if (!cidcardRegisterResult.get("status").toString().equals("200")){
                    guestResult.setMsg("发送失败:" + cidcardRegisterResult.getString("msg").toString());
                    return guestResult;
                }


            }else {
                /**
                 * 同住人，调用境内同住人登记接口
                 **/
                String addCidcardCohabitRegisterUrl = guestModel.getUrl() + "/rest/register/addCidcardCohabitRegister" + "?deviceCode=" + deviceCode + "&interfaceKey=" + interfaceKey + "&tokenId=" + tokenId;
                JSONObject appCidcardCohabitRegisterVo = new JSONObject();
                appCidcardCohabitRegisterVo.put("similarityDegree",encryptAES(guestModel.getSemblance(),encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("deviceType",encryptAES(deviceType,encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("originalCardNumber",encryptAES(guestModel.getGuestNo(),encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("originalCardType",encryptAES("1",encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("htCidcardCardtype",encryptAES("身份证",encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("htCidcardCardnumber",encryptAES(guestModel.getiDCode(),encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("htCidcardUsername",encryptAES(guestModel.getName(),encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("htCidcardSex",encryptAES(this.getSexCode(guestModel.getSex()),encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("htCidcardBirthday",encryptAES(guestModel.getBirthday(),encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("htCidcardPermanentaddress",encryptAES(guestModel.getAddress(),encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("htCidcardRegisterType",encryptAES("15",encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("profilePhoto",encryptAES(guestModel.getPhoto(),encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("photo",encryptAES(guestModel.getCameraPhoto(),encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("htCardtypeCode",encryptAES("1",encryptKey,encryptKey));
                appCidcardCohabitRegisterVo.put("htNationCode",encryptAES(this.getNationCode(guestModel.getNation()),encryptKey,encryptKey));
                String res = HttpUtil.sendPost(addCidcardCohabitRegisterUrl, appCidcardCohabitRegisterVo.toString());
                JSONObject cidcardRegisterResult = JSONObject.fromObject(res);

                if (!cidcardRegisterResult.get("status").toString().equals("200")){
                    guestResult.setMsg("发送失败:" + cidcardRegisterResult.getString("msg").toString());
                    return guestResult;
                }
            }
            guestResult.setResult(true);
            guestResult.setMsg("入住旅馆业系统上传成功");

        }catch (Exception e){
            guestResult.setMsg(e.getMessage());
        }
        finally {
            if(getToken){
                if (!tokenId.equals("") && tokenId.length() > 1){
                    userExit(guestModel ,tokenId);
                }
            }

        }
        return  guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        String tokenId = guestModel.getTokenId();
        Boolean getToken = false;
        try {
            /**
             * 0.获取参数配置
             **/
            String operatorS = guestModel.getOperators();      //存放用户名，密码，从业人员证件号码
            String[] operatorArray = operatorS.split(",");
            String loginName = operatorArray[0];        //用户名
            String pwd = operatorArray[1];              //密码
            String identificationNumber = operatorArray[2];     //从业人员证件号码
            String key = guestModel.getKey();                //存放接口密钥，加密密钥
            String[] keyArray = key.split(",");
            String interfaceKey = keyArray[0];          //接口密钥
            String encryptKey = keyArray[1];            //加密密钥
            String hotelCode = guestModel.getHotelCode();    //存放设备编号，设备类型
            String[] hotelCodeArray = hotelCode.split(",");
            String deviceCode = hotelCodeArray[0];      //设备编号
            String deviceType = hotelCodeArray[1];      //设备类型
            if(tokenId==null||"".equals(tokenId)) {

                JSONObject userLoginResult = userLogin(guestModel);
                if (!userLoginResult.getString("Result").equals("True")) {
                    guestResult.setMsg("发送失败:" + userLoginResult.getString("Msg").toString());
                    return guestResult;
                }
                tokenId = userLoginResult.getString("tokenId");
                getToken = true;
            }

            /**
             * 2.境内旅客离店接口
             **/
            String cidcardRegisterUrl = guestModel.getUrl() + "/rest/record/cdepartureRecordByCardId?";

            String cardId = encryptAES(guestModel.getiDCode(), encryptKey, encryptKey);
            String cardType = encryptAES("1", encryptKey, encryptKey);
            String htCidcardRegisterType = encryptAES("15", encryptKey, encryptKey);
            String cidcardRegisterDataStr = "cardId=" + URLEncoder.encode(cardId) + "&deviceCode=" + deviceCode + "&interfaceKey=" + interfaceKey + "&tokenId=" + tokenId + "&cardType=" + URLEncoder.encode(cardType) + "&htCidcardRegisterType=" + URLEncoder.encode(htCidcardRegisterType);


            String res = HttpUtil.sendGet(cidcardRegisterUrl + cidcardRegisterDataStr,"");

            JSONObject resultMap = JSONObject.fromObject(res);

            if (!resultMap.get("status").toString().equals("200")){
                guestResult.setMsg("发送失败:" + resultMap.getString("msg").toString());
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("退房旅馆业系统上传成功");
        }
        catch (Exception e){
            guestResult.setMsg(e.getMessage());
        }
        finally {
           if(getToken){
               if (!tokenId.equals("") && tokenId.length() > 1){
                   userExit(guestModel ,tokenId);
               }
           }
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        guestResult.setMsg("暂未实现");
        return guestResult;
    }

    public JSONObject userLogin(GuestModel guestModel){
        JSONObject result = new JSONObject();
        result.put("Result","Failed");
        result.put("Msg","从业人员登录失败");
        try {
            String key = guestModel.getKey();                //存放接口密钥，加密密钥
            String[] keyArray = key.split(",");
            String interfaceKey = keyArray[0];          //接口密钥
            String encryptKey = keyArray[1];            //加密密钥
            String operatorS = guestModel.getOperators();      //存放用户名，密码，从业人员证件号码
            String[] operatorArray = operatorS.split(",");
            String identificationNumber = encryptAES(operatorArray[2], encryptKey,encryptKey);     //从业人员证件号码
            String hotelCode = guestModel.getHotelCode();    //存放设备编号，设备类型
            String[] hotelCodeArray = hotelCode.split(",");
            String deviceCode = hotelCodeArray[0];      //设备编号
            String userLoginUrl = guestModel.getUrl() + "/rest/appLogin/userLogin" + "?identificationNumber=" +  URLEncoder.encode(identificationNumber) + "&interfaceKey=" + interfaceKey + "&deviceCode=" + deviceCode;
            JSONObject postData= new JSONObject();
            postData.put("profilePhoto",encryptAES(guestModel.getLoginPhoto(),encryptKey,encryptKey));
            postData.put("photo",encryptAES(guestModel.getLoginPhoto(),encryptKey,encryptKey));
            postData.put("similarityDegree",encryptAES("90.94",encryptKey,encryptKey));
            postData.put("deviceType",encryptAES(hotelCodeArray[1],encryptKey,encryptKey));
            String res = HttpUtil.sendPost(userLoginUrl, postData.toString());
            JSONObject userLoginResult = JSONObject.fromObject(res);
            //发送成功，收到失败回复
            if (!userLoginResult.get("status").toString().equals("200"))
            {
                result.put("Msg","发送失败:" + userLoginResult.getString("msg").toString());
                return result;
            }
            String tokenId = URLEncoder.encode((decryptAES(userLoginResult.getJSONObject("data").getString("tokenId"), encryptKey, encryptKey)));      //获取唯一标示(直接解密结果,并进行转码)
            result.put("Result","True");
            result.put("Msg","从业人员登录成功");
            result.put("tokenId",tokenId);
        }catch (Exception ex){
            result.put("Msg",ex.getMessage());
        }
        return result;
    }


    /**
     * 退出接口
     * @param guestModel
     * @param tokenId
     * @return
     */
    public JSONObject userExit(GuestModel guestModel ,String tokenId){
        JSONObject result = new JSONObject();
        result.put("Result","Failed");
        result.put("Msg","从业人员登录失败");
        try
        {
            String userExitUrl = guestModel.getUrl() + "/rest/appLogin/userExit?";
            String interfaceKey = guestModel.getKey().split(",")[0];          //接口密钥
            String deviceCode = guestModel.getHotelCode().split(",")[0];      //设备编号
            String userExitDataStr = "deviceCode=" + deviceCode + "&interfaceKey=" + interfaceKey + "&tokenId=" + tokenId;
            String res = HttpUtil.sendGet(userExitUrl + userExitDataStr ,"");
            JSONObject userExitResult = JSONObject.fromObject(res);
            if (!userExitResult.get("status").toString().equals("200")){
                result.put("Msg","发送失败:" + userExitResult.getString("msg").toString());
                return result;
            }
            result.put("Result","True");
            result.put("Msg","退出系统成功");
        }
        catch (Exception ex)
        {
            result.put("Msg",ex.getMessage());
        }
        return result;
    }

    public List<String> getRoomList(GuestModel guestModel){
        String tokenId = "";
        List<String>  roomList = new ArrayList<>();
        try {
            /**
             * 0.获取参数配置
             **/
            String operatorS = guestModel.getOperators();      //存放用户名，密码，从业人员证件号码
            String[] operatorArray = operatorS.split(",");
            String loginName = operatorArray[0];        //用户名
            String pwd = operatorArray[1];              //密码
            String identificationNumber = operatorArray[2];     //从业人员证件号码
            String key = guestModel.getKey();                //存放接口密钥，加密密钥
            String[] keyArray = key.split(",");
            String interfaceKey = keyArray[0];          //接口密钥
            String encryptKey = keyArray[1];            //加密密钥
            String hotelCode = guestModel.getHotelCode();    //存放设备编号，设备类型
            String[] hotelCodeArray = hotelCode.split(",");
            String deviceCode = hotelCodeArray[0];      //设备编号
            String deviceType = hotelCodeArray[1];      //设备类型
            JSONObject userLoginResult = userLogin(guestModel);
            if (!userLoginResult.getString("Result").equals("True")){
                return null;
            }
            tokenId = userLoginResult.getString("tokenId");
            String userFindCompanyRoom = guestModel.getUrl() + "/rest/appOther/findCompanyRoom?";
            String userExitDataStr = "deviceCode=" + deviceCode + "&interfaceKey=" + interfaceKey + "&tokenId=" + tokenId;
            String findRoomResult = HttpUtil.sendGet(userFindCompanyRoom + userExitDataStr ,"");
            JSONObject resultMap = JSONObject.fromObject(findRoomResult);
            if (!resultMap.getString("status").equals("200")){
                return null;
            }
            JSONArray dataList = resultMap.getJSONArray("data");
            for (int i = 0; i < dataList.size(); i++) {
                roomList.add(decryptAES(dataList.getString(i),encryptKey,encryptKey));
            }
        }catch (Exception e){
            return null;
        }

        return roomList;
    }


    public static String encryptAES(String data,String key,String iv) throws Exception {
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            int blockSize = cipher.getBlockSize();
            byte[] dataBytes = data.getBytes();
            int plaintextLength = dataBytes.length;
            if (plaintextLength % blockSize != 0) {
                plaintextLength = plaintextLength + (blockSize - (plaintextLength % blockSize));
            }
            byte[] plaintext = new byte[plaintextLength];
            System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivspec = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, keyspec, ivspec);
            byte[] encrypted = cipher.doFinal(plaintext);
            return new String(new BASE64Encoder().encode(encrypted));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * <AUTHOR>
     * @see AES算法解密密文
     * @param data 密文
     * @param key 密钥，长度16
     * @param iv 偏移量，长度16
     * @return 明文
     */
    public static String decryptAES(String data,String key,String iv) throws Exception {
        try
        {
            byte[] encrypted1 =  new Base64().decode(data);

            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivspec = new IvParameterSpec(iv.getBytes());

            cipher.init(Cipher.DECRYPT_MODE, keyspec, ivspec);

            byte[] original = cipher.doFinal(encrypted1);
            String originalString = new String(original);
            return originalString.trim();
        }
        catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
