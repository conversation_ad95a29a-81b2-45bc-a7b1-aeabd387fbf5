package com.pms.czabspoliceinterface.factory;

import cn.hutool.http.Header;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.IdcardInfoExtractor;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;


public class FuJianMoChuang extends PoliceBase implements IPolice {
    private static final Logger log = LoggerFactory.getLogger(FuJianMoChuang.class);

    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            guestModel.setiDCode(guestModel.getiDCode().trim());
            IdcardInfoExtractor idcardInfoExtractor = new IdcardInfoExtractor(guestModel.getiDCode());

            StringBuilder xml = new StringBuilder();
            xml.append("<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"yes\"?>");
            xml.append("<XmlPackage>");
            xml.append("<pkid type=\"string\">");
            xml.append(guestModel.getiDCode());
            xml.append("</pkid>");

            xml.append("<username type=\"string\">");
            xml.append(guestModel.getHotelCode());
            xml.append("</username>");

            xml.append("<password type=\"string\">");
            xml.append(guestModel.getKey());
            xml.append("</password>");

            xml.append("<dcsrq type=\"date\">");
            xml.append(idcardInfoExtractor.getBirthday());
            xml.append("</dcsrq>");

            xml.append("<drzsj type=\"date\">");
            xml.append(HotelUtils.currentTime());
            xml.append("</drzsj>");

            xml.append("<sdjr type=\"string\">");
            xml.append("管理员");
            xml.append("</sdjr>");

            xml.append("<sfjh type=\"string\">");
            xml.append(guestModel.getRoomNo());
            xml.append("</sfjh>");

            xml.append("<slkxm type=\"string\">");
            xml.append(guestModel.getName());
            xml.append("</slkxm>");

            xml.append("<smz type=\"string\">");
            xml.append(guestModel.getNation());
            xml.append("</smz>");

            xml.append("<srzjbr type=\"string\">");
            xml.append("管理员");
            xml.append("</srzjbr>");

            xml.append("<srzyy type=\"string\">");
            xml.append("出差");
            xml.append("</srzyy>");

            xml.append("<sxb type=\"string\">");
            xml.append(idcardInfoExtractor.getGender());
            xml.append("</sxb>");

            xml.append("<szjh type=\"string\">");
            xml.append(guestModel.getiDCode());
            xml.append("</szjh>");

            xml.append("<szjlx type=\"string\">");
            xml.append("居民身份证");
            xml.append("</szjlx>");

            xml.append("<szzxx type=\"string\">");
            xml.append(guestModel.getAddress());
            xml.append("</szzxx>");

            xml.append("<szzxzqh type=\"string\">");
            xml.append(guestModel.getiDCode().substring(0, 6));
            xml.append("</szzxzqh>");

            xml.append("<imgBase64 type=\"string\">");
            xml.append(guestModel.getPhoto());
            xml.append("</imgBase64>");
            xml.append("</XmlPackage>");
            log.info("checkin:url:{}, data:{}", guestModel.getUrl(), xml.toString());
            ResultResp post = post(guestModel.getUrl(), MethodEnum.CHECK_IN, xml.toString());
            log.info("checkin:result", net.sf.json.JSONObject.fromObject(post).toString());
            if (post.getCode() != 1) {
                guestResult.setResult(false);
                guestResult.setMsg(net.sf.json.JSONObject.fromObject(post).toString());
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setGuestId(guestModel.getiDCode());
            guestResult.setGuestNo(guestModel.getiDCode());
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            guestModel.setiDCode(guestModel.getiDCode().trim());
            StringBuilder xml = new StringBuilder();
            xml.append("<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"yes\"?>");
            xml.append("<XmlPackage>");
            xml.append("<pkid type=\"string\">");
            xml.append(guestModel.getiDCode());
            xml.append("</pkid>");

            xml.append("<username type=\"string\">");
            xml.append(guestModel.getHotelCode());
            xml.append("</username>");

            xml.append("<password type=\"string\">");
            xml.append(guestModel.getKey());
            xml.append("</password>");

            xml.append("<sfjh type=\"string\">");
            xml.append(guestModel.getRoomNo());
            xml.append("</sfjh>");

            xml.append("<slkxm type=\"string\">");
            xml.append(guestModel.getName());
            xml.append("</slkxm>");

            xml.append("<szjh type=\"string\">");
            xml.append(guestModel.getiDCode());
            xml.append("</szjh>");

            xml.append("</XmlPackage>");
            log.info("checkout:url:{}, data:{}", guestModel.getUrl(), xml.toString());
            ResultResp post = post(guestModel.getUrl(), MethodEnum.CHECK_OUT, xml.toString());
            log.info("checkout:result", net.sf.json.JSONObject.fromObject(post).toString());
            if (post.getCode() != 1) {
                guestResult.setMsg(net.sf.json.JSONObject.fromObject(post).toString());
                guestResult.setResult(false);
                return guestResult;
            }
            guestResult.setResult(true);
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        guestResult.setMsg("暂未实现");
        return guestResult;
    }


    public enum MethodEnum {

        CHECK_IN(1, "境内入住"),
        CHANGE_ROOM(5, "境内换房"),
        CHECK_OUT(2, "境内退房");

        private int code;
        private String name;

        MethodEnum(int code, String name) {
            this.code = code;
            this.name = name;
        }

        public int getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    @Data
    public class ResultResp {
        private String error;
        private Integer code;

        public boolean isSuccess() {
            return 1 == code;
        }
    }
    private static ResultResp post(String url, MethodEnum method, String xml) {
        Map<String, Object> todoInfo = new HashMap<>();
        todoInfo.put("requestStr", xml);
        todoInfo.put("type", method.getCode());
        String params = makeXml("Hotel_lk", todoInfo);
        String result = HttpUtil.createPost(url).header(Header.CONTENT_TYPE, "text/xml; charset=utf-8").body(params).execute().body();
        result = result.replaceAll("&lt;", "<").replaceAll("&gt;", ">");
        String subResult = result.substring(result.indexOf("<XmlPackage"), result.indexOf("</Hotel_lkResult>"));
        JSONObject jsonObject = JSONUtil.xmlToJson(subResult);
        jsonObject = jsonObject.getJSONObject("XmlPackage");
        return JSONUtil.toBean(jsonObject.toString(), ResultResp.class);
    }


    private static String makeXml(String methodName, Map<String, Object> todoInfo) {

        StringBuffer sb = new StringBuffer();
        sb.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
                "  <soap:Body>\n" +
                "    <" + methodName + " xmlns=\"http://tempuri.org/\">\n");

        for (String key : todoInfo.keySet()) {
            sb.append("<" + key + ">");
            sb.append("<![CDATA[" + todoInfo.get(key) + "]]>");
            sb.append("</" + key + ">");
        }
        sb.append("</" + methodName + ">\n" +
                "  </soap:Body>\n" +
                "</soap:Envelope>");

        return sb.toString();
    }
}
