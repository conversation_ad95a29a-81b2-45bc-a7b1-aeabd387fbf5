package com.pms.czabspoliceinterface.factory;

import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czabspoliceinterface.utils.AESUtil;
import com.pms.czabspoliceinterface.utils.HttpUtil;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.IdcardInfoExtractor;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Date;

public class ZheJiangJinCheng extends PoliceBase implements IPolice {
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            MultiValueMap requestMap = new LinkedMultiValueMap();
            requestMap.add("appKey", guestModel.getOperators());
            requestMap.add("method", "processAddGuest");
            requestMap.add("format", "json");
            requestMap.add("v", "1.0");
            String uid = (DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + guestModel.getiDCode());
            requestMap.add("uid", uid);
            requestMap.add("guestType", 1);
            requestMap.add("hotelCode", guestModel.getHotelCode());
            JSONObject guestInfo = new JSONObject();
            guestInfo.put("roomNum", guestModel.getRoomNo());
            guestInfo.put("idType", "11");
            guestInfo.put("idNum", guestModel.getiDCode());
            guestInfo.put("name", guestModel.getName());
            IdcardInfoExtractor idcardInfoExtractor = new IdcardInfoExtractor(guestModel.getiDCode());
            String gender = idcardInfoExtractor.getGender();
            guestInfo.put("gender", this.getSexCode(gender));
            guestInfo.put("nation", this.getNationCode(guestModel.getNation()));
            guestInfo.put("birthday", guestModel.getiDCode().substring(6, 14));
            guestInfo.put("address", guestModel.getAddress());
            guestInfo.put("headPhoto", guestModel.getPhoto());
            guestInfo.put("checkPhoto", guestModel.getCameraPhoto());
            if (null != guestModel.getCheckInTime() && guestModel.getCheckInTime().length() > 14) {
                guestModel.setCheckInTime(HotelUtils.dateToNoLineStrYmdhms(HotelUtils.parseStr2Date(guestModel.getCheckInTime())));
            }
            guestInfo.put("checkinTime", guestModel.getCheckInTime());
            String encrypt = AESUtil.encrypt(guestInfo.toString(), guestModel.getKey());
            requestMap.add("guest", encrypt);
            String signStr = AESUtil.getSignStr(requestMap, guestModel.getKey());
            System.out.println("sign:" + signStr);
            requestMap.add("sign", signStr);
            String s = HttpUtil.sendPostRequest(guestModel.getUrl(), requestMap);
            System.out.println("浙江省旅馆业入住数据返回:" + s);
            JSONObject res = JSONObject.fromObject(s);
            if (res.getInt("code") != 200) {
                guestResult.setResult(false);
                guestResult.setMsg("请求失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestId(signStr);
            guestResult.setGuestNo(uid);
            guestResult.setIdCode(guestModel.getiDCode());
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    public GuestResult checkinOther(GuestModel guestModel, JSONObject data) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            MultiValueMap requestMap = new LinkedMultiValueMap();
            requestMap.add("appKey", guestModel.getOperators());
            requestMap.add("method", "processAddGuest");
            requestMap.add("format", "json");
            requestMap.add("v", "1.0");
            requestMap.add("uid", DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + guestModel.getiDCode());
            requestMap.add("guestType", 2);
            requestMap.add("hotelCode", guestModel.getHotelCode());

            JSONObject guestInfo = data;
            String encrypt = AESUtil.encrypt(guestInfo.toString(), guestModel.getKey());
            requestMap.add("guest", encrypt);
            String signStr = AESUtil.getSignStr(requestMap, guestModel.getKey());
            System.out.println("sign:" + signStr);
            requestMap.add("sign", signStr);
            String s = HttpUtil.sendPostRequest(guestModel.getUrl(), requestMap);
            System.out.println("浙江省旅馆业入住数据返回:" + s);
            JSONObject res = JSONObject.fromObject(s);
            if (res.getInt("code") != 200) {
                guestResult.setResult(false);
                guestResult.setMsg("请求失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestId(signStr);
            guestResult.setGuestNo(guestModel.getiDCode());
            guestResult.setIdCode(guestModel.getiDCode());
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            MultiValueMap requestMap = new LinkedMultiValueMap();
            requestMap.add("appKey", guestModel.getOperators());
            requestMap.add("method", "processCheckoutGuest");
            requestMap.add("format", "json");
            requestMap.add("v", "1.0");
            requestMap.add("idNumber", guestModel.getiDCode());
            requestMap.add("hotelCode", guestModel.getHotelCode());
            requestMap.add("checkoutTime", DateFormatUtils.format(new Date(), "yyyyMMddHHmmss"));
            String signStr = AESUtil.getSignStr(requestMap, guestModel.getKey());
            requestMap.add("sign", signStr);
            String s = HttpUtil.sendPostRequest(guestModel.getUrl(), requestMap);
            System.out.println("浙江省旅馆业退房数据返回:" + s);
            JSONObject res = JSONObject.fromObject(s);
            if (res.getInt("code") != 200) {
                guestResult.setResult(false);
                guestResult.setMsg("请求失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestId(guestModel.getiDCode());
            guestResult.setGuestNo(guestModel.getiDCode());
            guestResult.setIdCode(guestModel.getiDCode());
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }


    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            System.out.println("公安网换房业务开始执行");
            MultiValueMap requestMap = new LinkedMultiValueMap();
            requestMap.add("appKey", guestModel.getOperators());
            requestMap.add("method", "processChangeRoom");
            requestMap.add("format", "json");
            requestMap.add("v", "1.0");
            requestMap.add("uid", guestModel.getGuestNo());
            requestMap.add("roomNum", guestModel.getNewRoomNo());
            String signStr = AESUtil.getSignStr(requestMap, guestModel.getKey());
            requestMap.add("sign", signStr);
            String s = HttpUtil.sendPostRequest(guestModel.getUrl(), requestMap);
            System.out.println("浙江省旅馆业换房数据返回:" + s);
            JSONObject res = JSONObject.fromObject(s);
            if (res.getInt("code") != 200) {
                guestResult.setResult(false);
                guestResult.setMsg("请求失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestId(guestModel.getiDCode());
            guestResult.setGuestNo(guestModel.getiDCode());
            guestResult.setIdCode(guestModel.getiDCode());
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }
}
