package com.pms.czabspoliceinterface.factory;

import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.axis.encoding.XMLType;
import org.apache.axis.message.SOAPHeaderElement;

import javax.xml.namespace.QName;
import javax.xml.rpc.ParameterMode;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPException;

public class <PERSON><PERSON><PERSON><PERSON><PERSON>Y<PERSON> extends PoliceBase implements IPolice {
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            String url = guestModel.getUrl();
            Object[] parsms = new Object[18];
            parsms[0] = guestModel.getName();
            parsms[1] = this.getSexCode(guestModel.getSex());
            parsms[2] = guestModel.getNation().replaceAll("族", "");
            parsms[3] = guestModel.getBirthday();
            parsms[4] = "2023.04.20-2043.04.20";
            parsms[5] = "身份证";
            parsms[6] = guestModel.getiDCode();
            parsms[7] = "";
            parsms[8] = guestModel.getiDCode().substring(0, 6);
            parsms[9] = guestModel.getAddress();
            parsms[10] = guestModel.getPhone();
            parsms[11] = guestModel.getHotelCode();
            parsms[12] = guestModel.getRoomNo();
            parsms[13] = guestModel.getPhoto();
            parsms[14] = guestModel.getCameraPhoto();
            parsms[15] = "Y";
            parsms[16] = 95;
            parsms[17] = guestModel.getCheckInTime();
            //上面代码为从缓存中取到我们需求传递到认证头的数据 下面开始添加认证头
            SOAPHeaderElement head = new SOAPHeaderElement(new QName(guestModel.getKey(), "authentication"));
            try {
                // 账号密码
                SOAPElement a1 = head.addChildElement("username");
                a1.addTextNode(guestModel.getHotelCode());
                a1 = head.addChildElement("password");
                a1.addTextNode(guestModel.getOperators());
                head.setPrefix("");
                head.setActor(null);
                //head.setMustUnderstand(true);
            } catch (SOAPException e) {
                e.printStackTrace();
            }
            Service service = new Service();
            Call call = (Call) service.createCall();
            call.setTargetEndpointAddress(url);
            call.setOperationName(new QName(guestModel.getKey(), "gnlkAdd"));// 设置要调用哪个方法
            call.addHeader(head);
            for (int i = 0; i < parsms.length; i++) {
                if (i == 16) {
                    call.addParameter("arg" + i,
                            XMLType.XSD_INT, ParameterMode.IN);
                } else {
                    call.addParameter("arg" + i,
                            XMLType.XSD_STRING, ParameterMode.IN);
                }
            }
            call.setEncodingStyle("UTF-8");
            call.setReturnType(org.apache.axis.encoding.XMLType.XSD_STRING);//设置结果返回类型
            String result = (String) call.invoke(parsms);//方法执行后的返回值
            System.out.println(result);
            if (!result.equals("登记成功")) {
                guestResult.setResult(false);
                guestResult.setMsg("请求失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestId(guestModel.getCheckInTime());
            guestResult.setGuestNo(guestModel.getCheckInTime());
            guestResult.setIdCode(guestModel.getiDCode());
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            String url = guestModel.getUrl();
            Object[] parsms = new Object[18];
            parsms[0] = guestModel.getiDCode();
            parsms[1] = guestModel.getGuestNo();
            parsms[2] = guestModel.getCheckOutTime();
            parsms[3] = guestModel.getHotelCode();
            //上面代码为从缓存中取到我们需求传递到认证头的数据 下面开始添加认证头
            SOAPHeaderElement head = new SOAPHeaderElement(new QName(guestModel.getKey(), "authentication"));
            try {
                // 账号密码
                SOAPElement a1 = head.addChildElement("username");
                a1.addTextNode(guestModel.getHotelCode());
                a1 = head.addChildElement("password");
                a1.addTextNode(guestModel.getOperators());
                head.setPrefix("");
                head.setActor(null);
                //head.setMustUnderstand(true);
            } catch (SOAPException e) {
                e.printStackTrace();
            }
            Service service = new Service();
            Call call = (Call) service.createCall();
            call.setTargetEndpointAddress(url);
            call.setOperationName(new QName(guestModel.getKey(), "checkOut"));// 设置要调用哪个方法
            call.addHeader(head);
            for (int i = 0; i < parsms.length; i++) {
                call.addParameter("arg" + i,
                        XMLType.XSD_STRING, ParameterMode.IN);
            }
            call.setEncodingStyle("UTF-8");
            call.setReturnType(org.apache.axis.encoding.XMLType.XSD_STRING);//设置结果返回类型
            String result = (String) call.invoke(parsms);//方法执行后的返回值
            System.out.println(result);
            if (!result.equals("退房成功")) {
                guestResult.setResult(false);
                guestResult.setMsg("请求失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestId(guestModel.getiDCode());
            guestResult.setGuestNo(guestModel.getiDCode());
            guestResult.setIdCode(guestModel.getiDCode());
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        return null;
    }
}
