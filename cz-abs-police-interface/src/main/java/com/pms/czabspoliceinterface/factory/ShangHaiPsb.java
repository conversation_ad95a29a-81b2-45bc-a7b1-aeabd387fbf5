package com.pms.czabspoliceinterface.factory;


import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czabspoliceinterface.utils.HttpUtil;
import com.pms.czpmsutils.StringUtil;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.time.DateFormatUtils;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Date;

public class ShangHaiPsb extends PoliceBase implements IPolice {
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            String token = getToken(guestModel);
            if (StringUtil.isEmpty(token)) {
                throw new Exception("token is null");
            }

            String operatorS = guestModel.getOperators();      //存放用户名，密码，从业人员证件号码
            String[] operatorArray = operatorS.split(",");
            String loginName = operatorArray[0];        //用户名
            String pwd = operatorArray[1];              //密码
            String identificationNumber = operatorArray[2];     //从业人员证件号码


            String key = guestModel.getKey();                //存放接口密钥，加密密钥
            String[] keyArray = key.split(",");
            String interfaceKey = keyArray[0];          //接口密钥
            String encryptKey = keyArray[1];            //加密密钥


            String hotelCodes = guestModel.getHotelCode();
            String[] hotelCodeArr = hotelCodes.split(",");
            String companykey = hotelCodeArr[0];        //用户名
            String chainId = hotelCodeArr[1];              //密码

            JSONObject postData = new JSONObject();
            postData.put("chainId", encryptAES(chainId, encryptKey, encryptKey));
            postData.put("companykey", companykey);
            postData.put("tokenId", token);
            postData.put("staffName", encryptAES(loginName, encryptKey, encryptKey));
            postData.put("staffDoctNumber", encryptAES(identificationNumber, encryptKey, encryptKey));
            postData.put("phoneNum", encryptAES(guestModel.getPhone(), encryptKey, encryptKey));
            postData.put("roomNum", encryptAES(guestModel.getRoomNo(), encryptKey, encryptKey));
            postData.put("isHousemate", encryptAES("0", encryptKey, encryptKey));
            postData.put("refNum", encryptAES(guestModel.getiDCode() + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss"), encryptKey, encryptKey));
            postData.put("xm", encryptAES(guestModel.getName(), encryptKey, encryptKey));
            postData.put("xb", encryptAES(guestModel.getSex(), encryptKey, encryptKey));
            postData.put("mz", encryptAES(guestModel.getNation(), encryptKey, encryptKey));
            postData.put("csnyr", encryptAES(guestModel.getBirthday().replaceAll("-", ""), encryptKey, encryptKey));
            postData.put("zz", encryptAES(guestModel.getAddress(), encryptKey, encryptKey));
            postData.put("gmsfzh", encryptAES(guestModel.getiDCode(), encryptKey, encryptKey));
            postData.put("zjz", encryptAES(guestModel.getiDCode() + "_c.jpg", encryptKey, encryptKey));
            postData.put("zjlx", encryptAES("身份证", encryptKey, encryptKey));
            postData.put("sfzzp", guestModel.getPhoto());
            System.out.println(postData.toString());
            String url = "http://*************:8080/checkFaceCardOnce";
            String res = com.pms.czpmsutils.HttpUtil.http(url, postData);
            System.out.println(res);
            JSONObject resultData = JSONObject.fromObject(res);
            if (resultData.getInt("status") != 200) {
                throw new Exception("获取token失败");
            }

        } catch (Exception e) {
            guestResult.setMsg(e.getMessage());
        }


        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        return null;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        guestResult.setMsg("暂未实现");
        return guestResult;
    }

    public static String getToken(GuestModel guestModel) {
        String token = "";
        try {
            String operatorS = guestModel.getOperators();      //存放用户名，密码，从业人员证件号码
            String[] operatorArray = operatorS.split(",");
            String loginName = operatorArray[0];        //用户名
            String pwd = operatorArray[1];              //密码
            String identificationNumber = operatorArray[2];     //从业人员证件号码


            String key = guestModel.getKey();                //存放接口密钥，加密密钥
            String[] keyArray = key.split(",");
            String interfaceKey = keyArray[0];          //接口密钥
            String encryptKey = keyArray[1];            //加密密钥


            String hotelCodes = guestModel.getHotelCode();
            String[] hotelCodeArr = hotelCodes.split(",");
            String companykey = hotelCodeArr[0];        //用户名
            String chainId = hotelCodeArr[1];              //密码

            JSONObject postData = new JSONObject();
            postData.put("interfaceKey", interfaceKey);
            postData.put("companykey", companykey);
            String url = guestModel.getUrl() + "/inn-app-rest/rest/pms/findToken";
            String res = HttpUtil.sendPost(url, postData.toString());
            System.out.println(res);
            JSONObject resultData = JSONObject.fromObject(res);
            if (resultData.getInt("status") != 200) {
                throw new Exception("获取token失败");
            }
            token = resultData.getJSONObject("data").getString("tokenId");
        } catch (Exception e) {
            e.printStackTrace();
            token = "";
        }
        return token;
    }


    public static String encryptAES(String data, String key, String iv) throws Exception {
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            int blockSize = cipher.getBlockSize();
            byte[] dataBytes = data.getBytes();
            int plaintextLength = dataBytes.length;
            if (plaintextLength % blockSize != 0) {
                plaintextLength = plaintextLength + (blockSize - (plaintextLength % blockSize));
            }
            byte[] plaintext = new byte[plaintextLength];
            System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivspec = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, keyspec, ivspec);
            byte[] encrypted = cipher.doFinal(plaintext);
            return new String(new BASE64Encoder().encode(encrypted));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String decryptAES(String data, String key, String iv) throws Exception {
        try {
            byte[] encrypted1 = new Base64().decode(data);

            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivspec = new IvParameterSpec(iv.getBytes());

            cipher.init(Cipher.DECRYPT_MODE, keyspec, ivspec);

            byte[] original = cipher.doFinal(encrypted1);
            String originalString = new String(original);
            return originalString.trim();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
