package com.pms.czabspoliceinterface.factory;

import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czabspoliceinterface.utils.EncryptDecodeUtil;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.HttpRequest;
import com.pms.czpmsutils.StringUtil;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.Date;
import java.util.UUID;

public class Alad extends PoliceBase implements IPolice {
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            String token = getToken(guestModel);
            String result = HttpRequest.doGet(guestModel.getUrl() + "/ownerInfo/findOwnerRoomsList?ownerInfoId=" + guestModel.getOperators(), token);
            System.out.println(result);
            JSONObject resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("code") || !resultData.getString("code").equals("0")) {
                throw new Exception("获取房间信息失败:" + result);
            }
            JSONArray roomList = resultData.getJSONObject("data").getJSONArray("ownerRoomList");
            if (roomList.size() < 1) {
                throw new Exception("获取房间信息失败:" + result);
            }
            String roomInfoId = "";
            for (int i = 0; i < roomList.size(); i++) {
                if (roomList.getJSONObject(i).getString("roomNum").equals(guestModel.getRoomNo())) {
                    roomInfoId = roomList.getJSONObject(i).getString("roomInfoId");
                    break;
                }
            }
            String hid = resultData.getJSONObject("data").getString("hid");
            JSONObject postData = new JSONObject();
            postData.put("checkInId", UUID.randomUUID().toString().trim().replaceAll("-", ""));
            postData.put("name", EncryptDecodeUtil.aesPKCS7PaddingEncrypt(guestModel.getName(), "28e15bf1f1fc4cf88e3c721c965f8480"));
            postData.put("sex", this.getSexCode(guestModel.getSex()));
            postData.put("nation", this.getNationCode(guestModel.getNation()));
            postData.put("idCode", EncryptDecodeUtil.aesPKCS7PaddingEncrypt(guestModel.getiDCode(), "28e15bf1f1fc4cf88e3c721c965f8480"));
            postData.put("idImage", guestModel.getPhoto());
            postData.put("liveImage", guestModel.getCameraPhoto());
            postData.put("contrast", "95");
            postData.put("contrastStatus", "0");
            postData.put("roomNumber", guestModel.getRoomNo());
            postData.put("roomInfoId", roomInfoId);
            postData.put("hid", hid);
            postData.put("checkInTime", HotelUtils.parseStr2Date(guestModel.getCheckInTime()).getTime());
            postData.put("idAddress", EncryptDecodeUtil.aesPKCS7PaddingEncrypt(guestModel.getAddress(), "28e15bf1f1fc4cf88e3c721c965f8480"));
            postData.put("checkInType", "1");
            postData.put("issuingAuthority", "公安局");
            postData.put("psbWebFlag" , "0");
            postData.put("issuePeriodStart", HotelUtils.parseStr2Date("2011-05-13 00:00:00").getTime());
            postData.put("issuePeriodEnd", HotelUtils.parseStr2Date("2021-05-13 00:00:00").getTime());
            postData.put("phone", EncryptDecodeUtil.aesPKCS7PaddingEncrypt(StringUtil.isEmpty(guestModel.getPhone()) ? "18018205311" : guestModel.getPhone(), "28e15bf1f1fc4cf88e3c721c965f8480"));
            result = HttpRequest.sendPost(guestModel.getUrl() + "/personnel/checkIn", postData.toString(), "Token", token, "");
            System.out.println(result);
            resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("code") || !resultData.getString("code").equals("0")) {
                throw new Exception("获取房间信息失败:" + result);
            }
            String operatingId = resultData.getJSONObject("data").getString("operatingId");
            guestResult.setGuestNo(operatingId);
            guestResult.setGuestId(operatingId);
            guestResult.setResult(true);
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            String token = getToken(guestModel);

            String result = HttpRequest.doGet(guestModel.getUrl() + "/ownerInfo/findOwnerRoomsList?ownerInfoId=" + guestModel.getOperators(), token);
            System.out.println(result);
            JSONObject resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("code") || !resultData.getString("code").equals("0")) {
                throw new Exception("获取房间信息失败:" + result);
            }
            String hid = resultData.getJSONObject("data").getString("hid");
            JSONObject postData = new JSONObject();
            postData.put("operatingId", guestModel.getGuestNo());
            postData.put("checkOutTime",  new Date().getTime());
            postData.put("hid",  hid);
            postData.put("psbWebFlag", 0);
            result = HttpRequest.sendPost(guestModel.getUrl() + "/personnel/checkOut", postData.toString(), "Token", token, "");
            System.out.println(result);
            resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("code") || !resultData.getString("code").equals("0")) {
                throw new Exception("获取房间信息失败:" + result);
            }
            guestResult.setResult(true);
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        return null;
    }


    public String getToken(GuestModel guestModel) {
        String token = "";
        try {
            JSONObject postData = new JSONObject();
            postData.put("authorizationCode", guestModel.getKey());
            postData.put("authorizationSecret", guestModel.getHotelCode());
            String result = HttpRequest.sendPost(guestModel.getUrl() + "/token/getToken", postData);
            System.out.println(result);
            JSONObject resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("code") || !resultData.getString("code").equals("0")) {
                throw new Exception("获取token失败:" + result);
            }
            token = resultData.getJSONObject("data").getString("token");
        } catch (Exception e) {
            e.printStackTrace();
            token = "";
        }
        return token;

    }
}
