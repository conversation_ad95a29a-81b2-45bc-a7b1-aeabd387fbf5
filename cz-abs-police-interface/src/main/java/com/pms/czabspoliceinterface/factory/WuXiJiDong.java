package com.pms.czabspoliceinterface.factory;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class WuXiJiDong implements IPolice {
    private static final Logger log = LoggerFactory.getLogger(WuXiJiDong.class);
    public JSONObject getToken(GuestModel guestModel) {
        String postUrl = guestModel.getUrl() + "/public/user/login";
        Map<String, Object> postData = new JSONObject();
        postData.put("account", guestModel.getOperators().split("-")[0]);
        postData.put("pwd", guestModel.getOperators().split("-")[1]);
        String body = HttpRequest.post(postUrl).form(postData).execute().body();
        log.info("get token res:" + body);
        JSONObject resultMap = JSONObject.fromObject(body);
        if (!resultMap.containsKey("success") || !resultMap.containsKey("data") || !resultMap.getBoolean("success")) {
            return null;
        }
        return resultMap.getJSONObject("data");
    }

    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        try {
            JSONObject tokenInfo = this.getToken(guestModel);
            Map<String, Object> postData = new HashMap<>();
            postData.put("rentUser", guestModel.getName());
            postData.put("idCard", guestModel.getiDCode());
            postData.put("rentPhone", guestModel.getPhone());
            postData.put("roomId", guestModel.getRoomNo());
            postData.put("startDate", guestModel.getCheckInTime());
            postData.put("endDate", guestModel.getCheckOutTime());
            postData.put("rentPrice", 1);
            postData.put("shortRent", "1");
            postData.put("createUser", tokenInfo.getInt("userId"));
            postData.put("rentMembers", new JSONArray());
            String postUrl = guestModel.getUrl() + "/user/shortRent";
            log.info("checkin url:{},param:{}:", postUrl, postData);
            String authorization = "Bearer " + tokenInfo.getString("token");
            String body = HttpRequest.post(postUrl).header("Authorization", authorization).header("Content-Type", "application/x-www-form-urlencoded").timeout(5000).form(postData).execute().body();
            log.info("checkin result :{}", body);
            JSONObject resultMap = JSONObject.fromObject(body);
            if (!resultMap.containsKey("success") || !resultMap.containsKey("data") || !resultMap.getBoolean("success") || StrUtil.isEmpty(resultMap.getString("data"))
            ) {
                guestResult.setResult(false);
                guestResult.setMsg("上传失败");
                return guestResult;
            }
            String data = resultMap.getString("data");
            guestResult.setGuestId(data);
            guestResult.setGuestNo(data);
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
        } catch (Exception e) {
            e.printStackTrace();
            log.info("checkin error:" + e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        try {
            JSONObject tokenInfo = this.getToken(guestModel);
            Map<String, Object> postData = new HashMap<>();
            postData.put("rentContractId", guestModel.getGuestNo());
            String postUrl = guestModel.getUrl() + "/user/stopRentContract";
            log.info("checkout url:{},param:{}:", postUrl, postData);
            String authorization = "Bearer " + tokenInfo.getString("token");
            String body = HttpRequest.post(postUrl).header("Authorization", authorization).header("Content-Type", "application/x-www-form-urlencoded").timeout(5000).form(postData).execute().body();
            log.info("checkout result :{}", body);
            JSONObject resultMap = JSONObject.fromObject(body);
            if (!resultMap.containsKey("success") || !resultMap.containsKey("data") || !resultMap.getBoolean("success")) {
                guestResult.setResult(false);
                guestResult.setMsg("上传失败");
                return guestResult;
            }
            guestResult.setGuestId(guestModel.getGuestId());
            guestResult.setGuestNo(guestModel.getGuestNo());
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
        } catch (Exception e) {
            e.printStackTrace();
            log.info(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        return null;
    }
}
