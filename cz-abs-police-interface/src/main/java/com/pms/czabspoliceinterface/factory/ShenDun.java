package com.pms.czabspoliceinterface.factory;

import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czabspoliceinterface.utils.SsoDesUtil2;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;
import org.apache.commons.lang.time.DateFormatUtils;

import java.util.Date;

public class ShenDun extends PoliceBase implements IPolice {
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            String param = this.checkinParam(guestModel);
            if (param !=null){
                guestResult.setMsg(param);
                return guestResult;
            }

            JSONObject guestInfo = new JSONObject();

            guestInfo.put("idType" ,"11");
            guestInfo.put("idNo",guestModel.getiDCode());
            guestInfo.put("name",guestModel.getName());
            guestInfo.put("nationCode", guestModel.getNation());
            guestInfo.put("sex",this.getSexCode(guestModel.getSex()));
            guestInfo.put("birth",guestModel.getBirthday());
            guestInfo.put("address",guestModel.getAddress());
            guestInfo.put("province",guestModel.getiDCode().substring(0,6));
            guestInfo.put("photo",guestModel.getPhoto());
            guestInfo.put("cameraImg",guestModel.getCameraPhoto());
            guestInfo.put("rzyz","1");
            guestInfo.put("verifiedSign","0");
            guestInfo.put("mobilePhone",guestModel.getPhone());
            guestInfo.put("gIsMinor","0");


            JSONObject postData = new JSONObject();
            postData.put("pType",0);
            //酒店编号(鲸哲系统注册生成)
            postData.put("psbId",guestModel.getHotelCode());
            postData.put("roomNo",guestModel.getRoomNo());
            postData.put("checkInDate",guestModel.getCheckInTime());
            postData.put("personInfo", guestInfo);

            postData.put("durationDays","1");
            postData.put("hoursRoomFlag","0");

            postData.put("macAddr",guestModel.getKey());
            postData.put("remark", "AUTO");

            JSONObject guestData = new JSONObject();
            guestData.put("SINE","1");
            guestData.put("CZLX","0");
            guestData.put("SJBH", guestModel.getHotelCode() + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss") + "000");
            guestData.put("LKDM", guestModel.getHotelCode() + DateFormatUtils.format(new Date(), "yyyyMMddHHmm"));
            guestData.put("LGBH",guestModel.getHotelCode());
            guestData.put("QYBM",guestModel.getHotelCode());
            guestData.put("XM",guestModel.getName());
            guestData.put("XB",this.getSexCode(guestModel.getSex()));
            guestData.put("MZDM",this.getNationCode(guestModel.getNation()));
            guestData.put("CSRQ",guestModel.getBirthday());
            guestData.put("ZJLXDM","11");
            guestData.put("ZJHM",guestModel.getiDCode());
            guestData.put("SSX",guestModel.getiDCode().substring(0,6));
            guestData.put("XXDZ",guestModel.getAddress());
            guestData.put("RZSJ",guestModel.getCheckInTime());
            guestData.put("RZFH",guestModel.getRoomNo());
            guestData.put("ZJZP",guestModel.getPhoto());
            guestData.put("XCZP", guestModel.getCameraPhoto() == null || guestModel.getCameraPhoto().equals("") ? guestModel.getPhoto() : guestModel.getCameraPhoto());
            guestData.put("SCSJ", HotelUtils.currentTime());
            System.out.println(guestData.toString());
            String data = SsoDesUtil2.desCbcEncrypt(guestData.toString(), guestModel.getKey().substring(0,8), guestModel.getKey().substring(8,16));
        /*    JSONObject postData = new JSONObject();
            postData.put("encryptStr" , data);
            postData.put("md5" , SsoDesUtil2.getMd5(guestData.toString()));
            String postUrl = guestModel.getUrl() + "/public/aisinoOpenApi/scgn-new/recieveJnHotelDataWithSecret";
            System.out.println("URL:" + postUrl);
            String res = HttpUtil.sendPost(postUrl, token, postData.toString());
            JSONObject resData = JSONObject.fromObject(res);
            if (!resData.containsKey("code") || !resData.getString("code").equals("0000")){
                guestResult.setMsg("上传失败");
                return  guestResult;
            }*/
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestNo(guestData.getString("SJBH"));
            guestResult.setGuestId(guestData.getString("LKDM"));
        }catch (Exception e){
            e.printStackTrace();
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        return null;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        guestResult.setMsg("暂未实现");
        return guestResult;
    }
}
