package com.pms.czabspoliceinterface.factory;

import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czabspoliceinterface.utils.HttpUtil;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.StringUtil;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;

public class YK extends PoliceBase implements IPolice {
    private static final Logger log = LoggerFactory.getLogger(YK.class);
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            JSONObject postData = new JSONObject();
            postData.put("HotelId", guestModel.getHotelCode());
            postData.put("RoomNo", guestModel.getRoomNo());
            postData.put("Floor", "");
            postData.put("Building", "");
            SimpleDateFormat date = new SimpleDateFormat("yyyyMMddHHmmss");
            postData.put("DtIn", date.format(HotelUtils.parseStr2Date(guestModel.getCheckInTime())));
            postData.put("DtOut", date.format(HotelUtils.parseStr2Date(StringUtil.isEmpty(guestModel.getCheckOutTime()) ? HotelUtils.currentTime() : guestModel.getCheckOutTime())));
            postData.put("TrueName", guestModel.getName());
            postData.put("Nation", guestModel.getNation().replace("族", ""));
            postData.put("No", guestModel.getiDCode());
            postData.put("Address", guestModel.getAddress());
            postData.put("IdImage", guestModel.getPhoto());
            postData.put("Photo", guestModel.getCameraPhoto());
            postData.put("Score", "0.85");
            postData.put("Depart", "");
            postData.put("UniqueCheck", "0");
            System.out.println(postData.toString());
            log.info("checkin post url:{}, data: {}", "https://psb.yikett.com/api/check/in", postData.toString());
            String result = HttpUtil.sendPost("https://psb.yikett.com/api/check/in", postData.toString());
            System.out.println(result);
            log.info("checkin result" + result);
            JSONObject resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("Code") && (resultData.getInt("Code") != 0 || resultData.getInt("Code") != 200)) {
                throw new Exception("上传失败:" + result);
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestId(guestModel.getiDCode());
            guestResult.setGuestNo(guestModel.getiDCode());
            guestResult.setIdCode(guestModel.getiDCode());
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            JSONObject postData = new JSONObject();
            postData.put("HotelId", guestModel.getHotelCode());
            postData.put("RoomNo", guestModel.getRoomNo());
            postData.put("No", guestModel.getiDCode());
            SimpleDateFormat date = new SimpleDateFormat("yyyyMMddHHmmss");
            postData.put("DtIn", date.format(HotelUtils.parseStr2Date(StringUtil.isEmpty(guestModel.getCheckInTime()) ? HotelUtils.currentTime() : guestModel.getCheckInTime())));
            postData.put("DtOut", date.format(HotelUtils.parseStr2Date(StringUtil.isEmpty(guestModel.getCheckOutTime()) ? HotelUtils.currentTime() : guestModel.getCheckOutTime())));
            System.out.println(postData.toString());
            log.info("checkout post url:{}, data: {}", "https://psb.yikett.com/api/check/out", postData.toString());
            String result = HttpUtil.sendPost("https://psb.yikett.com/api/check/out", postData.toString());
            System.out.println(result);
            log.info("checkin result" + result);
            JSONObject resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("Code") && (resultData.getInt("Code") != 0 || resultData.getInt("Code") != 200)) {
                throw new Exception("上传失败:" + result);
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestId(guestModel.getiDCode());
            guestResult.setGuestNo(guestModel.getiDCode());
            guestResult.setIdCode(guestModel.getiDCode());

        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(false);
        try {
            JSONObject postData = new JSONObject();
            postData.put("HotelId", guestModel.getHotelCode());
            postData.put("RoomNo", guestModel.getNewRoomNo());
            postData.put("OldRoomNo", guestModel.getRoomNo());
            postData.put("No", guestModel.getiDCode());
            SimpleDateFormat date = new SimpleDateFormat("yyyyMMddHHmmss");
            postData.put("DtIn", date.format(HotelUtils.parseStr2Date(StringUtil.isEmpty(guestModel.getCheckInTime()) ? HotelUtils.currentTime() : guestModel.getCheckInTime())));
            postData.put("DtOut", date.format(HotelUtils.parseStr2Date(StringUtil.isEmpty(guestModel.getCheckOutTime()) ? HotelUtils.currentTime() : guestModel.getCheckOutTime())));
            System.out.println(postData.toString());
            log.info("changeroom post url:{}, data: {}", "https://psb.yikett.com/api/check/changeroom", postData.toString());
            String result = HttpUtil.sendPost("https://psb.yikett.com/api/check/changeroom", postData.toString());
            System.out.println(result);
            log.info("changeroom result" + result);
            JSONObject resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("Code") && (resultData.getInt("Code") != 0 || resultData.getInt("Code") != 200)) {
                throw new Exception("上传失败:" + result);
            }
            guestResult.setResult(true);
            guestResult.setMsg("上传成功");
            guestResult.setGuestId(guestModel.getiDCode());
            guestResult.setGuestNo(guestModel.getiDCode());
            guestResult.setIdCode(guestModel.getiDCode());

        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }
}
