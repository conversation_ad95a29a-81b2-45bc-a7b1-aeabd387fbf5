package com.pms.czabspoliceinterface.factory;

import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czabspoliceinterface.utils.EncryptDecodeUtil;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.HttpRequest;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

// 江苏鲸哲
public class JiangSuJingZhe  extends PoliceBase implements IPolice {

    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        try {

            JSONObject senParam = new JSONObject();
            guestModel.setCheckInTime(HotelUtils.currentTime());
            senParam.put("pType",0);
            senParam.put("psbId",guestModel.getHotelCode());
            senParam.put("roomNo",guestModel.getRoomNo());
            senParam.put("checkInDate",guestModel.getCheckInTime()+":222");
          //  senParam.put("hourLeaveTime",guestModel.getCheckOutTime()+":222");
            senParam.put("key",guestModel.getKey());
            List<String> allDayListBetweenDate = HotelUtils.getAllDayListBetweenDate(guestModel.getCheckInTime(), guestModel.getCheckOutTime());
            senParam.put("durationDays",allDayListBetweenDate.size()+"");
            senParam.put("hoursRoomFlag",guestModel.getCheckInType()==1?"0":"1");
            String city = guestModel.getiDCode().substring(0, 4);
            senParam.put("city",city);
            senParam.put("macAddr",guestModel.getMac());
            senParam.put("remark","AUTO"+guestModel.getOperators()+"自助办理");


        //    guestModel.setHeatherCode("data:image/jpeg;base64,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");
        //    guestModel.setTripCode("data:image/jpeg;base64,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");
        //    guestModel.setGroCode( guestModel.getHeatherCode());

            System.out.println(senParam.toString());

            Set<String> set = senParam.keySet();
            ArrayList<String> strings = new ArrayList<>();
            for(String key:set){
                strings.add(senParam.getString(key));
            }
            String sign =EncryptDecodeUtil.sign(strings) ;
            boolean b = EncryptDecodeUtil.verifySign(strings, sign);

            JSONObject person = new JSONObject();
            person.put("idType","111");
            person.put("idNo",guestModel.getiDCode());
            person.put("name",guestModel.getName());
            person.put("nationCode",getNationCode(guestModel.getNation()));
            person.put("sex",getGender(guestModel.getiDCode()));
            String brith = guestModel.getiDCode().substring(6,10)+"-"+guestModel.getiDCode().substring(10,12)+"-"+guestModel.getiDCode().substring(12,14);
            person.put("birth",brith);
            person.put("address",guestModel.getAddress());
            String s2 = guestModel.getiDCode();
            String pro = s2.substring(0, 2) + "0000";
            String cit = s2.substring(0, 4) + "00";
            String area = s2.substring(0, 6) ;

            person.put("province",pro+","+cit+","+area);
            person.put("photo","data:image/jpeg;base64,"+guestModel.getPhoto());
            person.put("cameraImg","data:image/jpeg;base64,"+guestModel.getCameraPhoto());
            person.put("bdfz",guestModel.getFaceResult());

            //   person.put("strLXFS","18521009400");
            person.put("strBGSJCJG","1");
            ArrayList<String> strings1 = new ArrayList<>();
            strings1.add("321200");
            person.put("strWLZGFXDQ",strings1 );
            person.put("rzyz",1);
            person.put("strBGS",1);
            person.put("verifiedSign",0);
            person.put("mobilePhone",guestModel.getPhone());
            person.put("gIsMinor",guestModel.getIsChildren());

            person.put("healthCode",1);
            person.put("travelCardPic",guestModel.getTripCode());
            person.put("healthCodePic",guestModel.getHeatherCode());
            person.put("locationCodePic",guestModel.getGroCode());
//
            senParam.put("personInfo",person);

            JSONObject psss = new JSONObject();
            String s3 = EncryptDecodeUtil.aesPKCS7PaddingEncrypt(senParam.toString(), guestModel.getKey());
            psss.put("params",s3);
            psss.put("sign",sign);
            System.out.println("请求参数：：：：");
            System.out.println(psss.toString());
            String s = guestModel.getUrl() + "/checkIn";
            System.out.println(s);
            String s1 = HttpRequest.sendPost(s, psss);
            JSONObject jsonObject = JSONObject.fromObject(s1);
            System.out.println(jsonObject);
            if(!jsonObject.getString("code").equals("200")&&!jsonObject.getString("code").equals("0")){
                throw new Exception(jsonObject.getString("msg"));
            }
            guestResult.setGuestId(guestModel.getiDCode());
            guestResult.setGuestNo(guestModel.getiDCode());
        }catch (Exception e){
            e.printStackTrace();
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        try {

            System.out.println("浙江鲸哲退房请求：：：");
            System.out.println(JSONObject.fromObject(guestModel).toString());

            JSONObject senParam = new JSONObject();
            senParam.put("pType",2);
            senParam.put("psbId",guestModel.getHotelCode());
            senParam.put("key",guestModel.getKey());
            senParam.put("roomNo",guestModel.getRoomNo());
            senParam.put("checkOutDate",HotelUtils.currentTime()+":222");
            String city = guestModel.getiDCode().substring(0, 4);
            senParam.put("cardNum",guestModel.getiDCode());
            senParam.put("durationDays",1);
            senParam.put("city","3212");
            senParam.put("macAddr",guestModel.getMac());
            senParam.put("remark","AUTO"+guestModel.getOperators()+"自助退房");

            Set<String> set = senParam.keySet();
            ArrayList<String> strings = new ArrayList<>();
            for(String key:set){
                strings.add(senParam.get(key)+"");
            }

            System.out.println(senParam.toString());

            String sign = EncryptDecodeUtil.sign(strings);
            String s3 = EncryptDecodeUtil.aesPKCS7PaddingEncrypt(senParam.toString(), guestModel.getKey());
            JSONObject psss = new JSONObject();
            psss.put("params",s3);
            psss.put("sign",sign);

            String s = guestModel.getUrl() + "/checkOut";
            String s1 = HttpRequest.sendPost(s, psss);
            JSONObject jsonObject = JSONObject.fromObject(s1);
            if(!jsonObject.getString("code").equals("200")&&!jsonObject.getString("code").equals("0")){
                throw new Exception(jsonObject.getString("msg"));
            }

        }catch (Exception e){
            e.printStackTrace();
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        guestResult.setResult(true);
        guestResult.setMsg("暂未实现");
        return guestResult;
    }
}
