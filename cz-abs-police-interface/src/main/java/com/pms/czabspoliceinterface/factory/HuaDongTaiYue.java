package com.pms.czabspoliceinterface.factory;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pms.czabspoliceinterface.bean.IPolice;
import com.pms.czabspoliceinterface.bean.PoliceBase;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class HuaDongTaiYue extends PoliceBase implements IPolice {
    @Override
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        try {
            long currentSeconds = DateUtil.currentSeconds();
            String key = guestModel.getKey();
            String code = guestModel.getCode();
            /**
             * 请求id
             */
            String operators = guestModel.getOperators();
            StringBuilder content = new StringBuilder();
            content.append(operators).append(currentSeconds).append(key);
            ArrayList<String> list = new ArrayList<>();
            list.add(operators);
            list.add(String.valueOf(currentSeconds));
            list.add(key);
            Collections.sort(list);
            StringBuilder stringBuilder = new StringBuilder();
            for (String item : list) {
                stringBuilder.append(item);
            }
            String signature = DigestUtil.sha256Hex(stringBuilder.toString());
            String aAeskey = DigestUtil.md5Hex16(code);
            AES aes = new AES(Mode.CBC, Padding.ZeroPadding, aAeskey.getBytes(), aAeskey.toUpperCase().getBytes());
            JSONObject postData = new JSONObject();
            postData.put("name", guestModel.getName());
            postData.put("sex", this.getSexCode(guestModel.getSex()));
            postData.put("nation", this.getNationCode(guestModel.getNation()));
            postData.put("birthday", IdcardUtil.getBirth(guestModel.getiDCode()));
            postData.put("cardtype", "11");
            postData.put("cardnum", guestModel.getiDCode());
            postData.put("region", guestModel.getiDCode().substring(0, 6));
            postData.put("address", guestModel.getAddress());
            postData.put("logintime", DateUtil.format(DateUtil.parse(guestModel.getCheckInTime()), "yyyyMMddHHmm"));
            postData.put("loginroom", guestModel.getRoomNo());
            postData.put("loginday", DateUtil.between(DateUtil.parse(guestModel.getCheckInTime()), DateUtil.parse(guestModel.getCheckOutTime()), DateUnit.DAY));
            postData.put("logouttime", DateUtil.format(DateUtil.parse(guestModel.getCheckOutTime()), "yyyyMMddHHmm"));
            postData.put("gatflag", "0");
            postData.put("collectmode", "10");
            postData.put("facenum", "");
            JSONArray photos = new JSONArray();
            JSONObject photo = new JSONObject();
            JSONObject cameraPhoto = new JSONObject();
            photo.put("photoflag", "0");
            photo.put("photo", guestModel.getPhoto());
            photos.add(photo);
            cameraPhoto.put("photoflag", "1");
            cameraPhoto.put("photo", guestModel.getCameraPhoto());
            photos.add(cameraPhoto);
            postData.put("photos", photos);
            postData.put("companynum", guestModel.getHotelCode());
            System.out.println(postData.toString());
            String encryptData = aes.encryptBase64(postData.toString(), "utf-8");
            Map<String, Object> data = new HashMap<>();
            data.put("data", encryptData);
            String result = HttpRequest.post(guestModel.getUrl() + "/restful/localguest/add").header(Header.CONTENT_TYPE, "application/json").header("requestid", operators).header("timestamp", String.valueOf(currentSeconds)).header("signature", signature).timeout(20000).body(encryptData).execute().body();
            JSONObject resultMap = JSONObject.parseObject(result);
            if (!resultMap.containsKey("code") || resultMap.getInteger("code") != 0) {
                guestResult.setResult(false);
                guestResult.setMsg("上传失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setGuestNo(guestModel.getiDCode());
            guestResult.setGuestId(resultMap.getString("data"));
            guestResult.setMsg("上传成功");
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg("上传失败");
        }
        return guestResult;
    }


    @Override
    public GuestResult checkout(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        try {
            long currentSeconds = DateUtil.currentSeconds();
            String key = guestModel.getKey();
            String code = guestModel.getCode();
            /**
             * 请求id
             */
            String operators = guestModel.getOperators();
            StringBuilder content = new StringBuilder();
            content.append(operators).append(currentSeconds).append(key);
            ArrayList<String> list = new ArrayList<>();
            list.add(operators);
            list.add(String.valueOf(currentSeconds));
            list.add(key);
            Collections.sort(list);
            StringBuilder stringBuilder = new StringBuilder();
            for (String item : list) {
                stringBuilder.append(item);
            }
            String signature = DigestUtil.sha256Hex(stringBuilder.toString());
            String aAeskey = DigestUtil.md5Hex16(code);
            AES aes = new AES(Mode.CBC, Padding.ZeroPadding, aAeskey.getBytes(), aAeskey.toUpperCase().getBytes());
            JSONObject postData = new JSONObject();
            postData.put("cardNum", guestModel.getiDCode());
            postData.put("companyNum", guestModel.getHotelCode());
            postData.put("logoutTime", DateUtil.format(DateUtil.parse(guestModel.getCheckOutTime()), "yyyyMMddHHmm"));
            System.out.println(postData.toString());
            String encryptData = aes.encryptBase64(postData.toString(), "utf-8");
            Map<String, Object> data = new HashMap<>();
            data.put("data", encryptData);
            String result = HttpRequest.post(guestModel.getUrl() + "/restful/localguest/quit").header(Header.CONTENT_TYPE, "application/json").header("requestid", operators).header("timestamp", String.valueOf(currentSeconds)).header("signature", signature).timeout(20000).body(encryptData).execute().body();
            JSONObject resultMap = JSONObject.parseObject(result);
            if (resultMap.containsKey("code") || resultMap.getInteger("code") != 0) {
                guestResult.setResult(false);
                guestResult.setMsg("上传失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setGuestNo(guestModel.getiDCode());
            guestResult.setGuestId(guestModel.getGuestId());
            guestResult.setMsg("上传成功");
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg("上传失败");
        }
        return guestResult;
    }

    @Override
    public GuestResult changeRoom(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        try {
            long currentSeconds = DateUtil.currentSeconds();
            String key = guestModel.getKey();
            String code = guestModel.getCode();
            /**
             * 请求id
             */
            String operators = guestModel.getOperators();
            StringBuilder content = new StringBuilder();
            content.append(operators).append(currentSeconds).append(key);
            ArrayList<String> list = new ArrayList<>();
            list.add(operators);
            list.add(String.valueOf(currentSeconds));
            list.add(key);
            Collections.sort(list);
            StringBuilder stringBuilder = new StringBuilder();
            for (String item : list) {
                stringBuilder.append(item);
            }
            String signature = DigestUtil.sha256Hex(stringBuilder.toString());
            String aAeskey = DigestUtil.md5Hex16(code);
            AES aes = new AES(Mode.CBC, Padding.ZeroPadding, aAeskey.getBytes(), aAeskey.toUpperCase().getBytes());
            JSONObject postData = new JSONObject();
            postData.put("cardNum", guestModel.getiDCode());
            postData.put("newRoom", guestModel.getNewRoomNo());
            postData.put("loginRoom", guestModel.getRoomNo());
            postData.put("companyNum", guestModel.getHotelCode());
            System.out.println(postData.toString());
            String encryptData = aes.encryptBase64(postData.toString(), "utf-8");
            Map<String, Object> data = new HashMap<>();
            data.put("data", encryptData);
            String result = HttpRequest.post(guestModel.getUrl() + "/restful/localguest/replace").header(Header.CONTENT_TYPE, "application/json").header("requestid", operators).header("timestamp", String.valueOf(currentSeconds)).header("signature", signature).timeout(20000).body(encryptData).execute().body();
            JSONObject resultMap = JSONObject.parseObject(result);
            if (resultMap.containsKey("code") || resultMap.getInteger("code") != 0) {
                guestResult.setResult(false);
                guestResult.setMsg("上传失败");
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setGuestNo(guestModel.getiDCode());
            guestResult.setGuestId(guestModel.getGuestId());
            guestResult.setMsg("上传成功");
        } catch (Exception e) {
            guestResult.setResult(false);
            guestResult.setMsg("上传失败");
        }
        return guestResult;
    }
}
