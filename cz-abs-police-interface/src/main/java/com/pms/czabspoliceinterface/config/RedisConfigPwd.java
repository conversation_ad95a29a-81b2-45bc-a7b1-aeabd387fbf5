package com.pms.czabspoliceinterface.config;

import com.pms.czpmsutils.RSAUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

@Configuration
public class RedisConfigPwd extends CachingConfigurerSupport {

    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private String port;

    @Value("${spring.redis.database}")
    private String database;

    @Value("${spring.redis.password}")
    private String password;

    @Bean
    public RedisConnectionFactory myLettuceConnectionFactory() throws Exception{
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration(redisHost,Integer.parseInt(port));
        redisStandaloneConfiguration.setDatabase(Integer.parseInt(database));
        //获取application.yml 中的密码（密文）
        //解密密码并停驾到配置中
        String pwd= RSAUtils.getStringDecrypt(password);//此处用于生成加密后的密码，配置在配置文件中
        redisStandaloneConfiguration.setPassword(pwd);
        redisStandaloneConfiguration.setDatabase(0);
        return new LettuceConnectionFactory(redisStandaloneConfiguration);
    }

}
