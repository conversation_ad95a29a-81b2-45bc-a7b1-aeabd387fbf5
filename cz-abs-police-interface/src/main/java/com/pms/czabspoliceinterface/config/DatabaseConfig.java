package com.pms.czabspoliceinterface.config;

import com.pms.czpmsutils.RSAUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
@ConfigurationProperties(prefix = "spring.datasource")
public class DatabaseConfig {

    private String url;
    private String username;
    private String password;
    private String driverClassName = "com.mysql.cj.jdbc.Driver";

    // Getters and Setters for all properties
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    // 创建数据源 Bean
    @Bean
    public DataSource dataSource() throws Exception{
        DataSourceBuilder dataSourceBuilder = DataSourceBuilder.create();
        dataSourceBuilder.driverClassName(driverClassName);
        dataSourceBuilder.url(RSAUtils.getStringDecrypt(url));
        dataSourceBuilder.username(RSAUtils.getStringDecrypt(username));
        dataSourceBuilder.password(RSAUtils.getStringDecrypt(password));
        return dataSourceBuilder.build();
    }
}
