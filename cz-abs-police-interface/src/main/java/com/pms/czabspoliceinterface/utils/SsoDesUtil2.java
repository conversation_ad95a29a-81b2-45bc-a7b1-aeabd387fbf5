package com.pms.czabspoliceinterface.utils;

import org.springframework.util.DigestUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;


/**
 * 功能描述
 * 加密常用类
 */
public class SsoDesUtil2 {
    // 密钥是16位长度的byte[]进行Base64转换后得到的字符串
    public static String key = "LmMGStGtOpF4xNyvYt54EQ==";


    /**
     * <li>
     * 方法名称:DES_CBC_Encrypt</li> <li>
     * 功能描述:
     *
     * <pre>
     * 经过封装的DES/CBC加密算法，如果包含中文，请注意编码。
     * </pre>
     *
     * </li>
     *
     * @param sourceBuf 需要加密内容的字节数组。
     * @param deskey    KEY 由8位字节数组通过SecretKeySpec类转换而成。
     * @param ivParam   IV偏转向量，由8位字节数组通过IvParameterSpec类转换而成。
     * @return 加密后的字节数组
     * @throws Exception
     */
    public static byte[] DES_CBC_Encrypt(byte[] sourceBuf,
                                         SecretKeySpec deskey, IvParameterSpec ivParam) throws Exception {
        byte[] cipherByte;
        // 使用DES对称加密算法的CBC模式加密
        Cipher encrypt = Cipher.getInstance("DES/CBC/PKCS5Padding");

        encrypt.init(Cipher.ENCRYPT_MODE, deskey, ivParam);

        cipherByte = encrypt.doFinal(sourceBuf, 0, sourceBuf.length);
        // 返回加密后的字节数组
        return cipherByte;
    }

    /**
     * <li>
     * 方法名称:byte2hex</li> <li>
     * 功能描述:
     *
     * <pre>
     * 字节数组转换为二行制表示
     * </pre>
     *
     * </li>
     *
     * @param inStr 需要转换字节数组。
     * @return 字节数组的二进制表示。
     */
    public static String byte2hex(byte[] inStr) {
        String stmp;
        StringBuffer out = new StringBuffer(inStr.length * 2);

        for (int n = 0; n < inStr.length; n++) {
            // 字节做"与"运算，去除高位置字节 11111111
            stmp = Integer.toHexString(inStr[n] & 0xFF);
            if (stmp.length() == 1) {
                // 如果是0至F的单位字符串，则添加0
                out.append("0" + stmp);
            } else {
                out.append(stmp);
            }
        }
        return out.toString();
    }
    //加密方法
    public static String desCbcEncrypt(String params, String deskey, String ivParam) throws Exception {
        String desOrder = "";
        SecretKeySpec keyspec = new SecretKeySpec(deskey.getBytes(), "DES");
        IvParameterSpec ivspec = new IvParameterSpec(ivParam.getBytes());
        byte[] desOrderByte = DES_CBC_Encrypt(params.getBytes(StandardCharsets.UTF_8),
                keyspec, ivspec);
        desOrder = byte2hex(desOrderByte);
        return desOrder;
    }

    public static String getMd5(String content){
        return DigestUtils.md5DigestAsHex(content.getBytes());
    }
}
