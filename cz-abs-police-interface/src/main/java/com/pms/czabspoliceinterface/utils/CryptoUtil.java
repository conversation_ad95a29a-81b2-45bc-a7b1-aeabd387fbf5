package com.pms.czabspoliceinterface.utils;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

public class CryptoUtil {

    /** 秘钥算法RSA */
    public static final String RSA = "RSA";

    /** 秘钥算法AES */
    public static final String AES = "AES";

    /** RSA加解密算法 */
    public static final String RSA_CIPHER_ALGO = "RSA/ECB/PKCS1Padding";

    /** AES加解密算法 */
    public static final String AES_CIPHER_ALGO = "AES/ECB/PKCS5Padding";

    /** 签名算法 */
    public static final String SIGNATURE_ALGO = "SHA256withRSA";

    /** 默认编码UTF-8 */
    public static final String DEFAULT_CHARSET = "UTF-8";

    private static final String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1bAdtmTui6BRJ/lBUSfFrRUn+Gd8WiJC751XvyxljkCPO9w0MZp0DLzbMbjOGOudjolYwni+VrcOIPad38GMGMIFoS+Zt7xi3pd1QvU4VNP5nXkjEqwUYHG1bRKG6mAcC5uAsWlsaZPDksrEcKtRBEEHdZYq2ubX4kH+sPx5nhxmDZavvTbLEQ5BkbiRFPPYl/2IN+/j3HB79DDDZ2bRgcEPD9cElnyuW4BIXMY0Y9ZfHZH2zoOMcnQLEkMYfWpbfKZ/FJEhKxHH0dhdIlOU0IWyIE4Lcv/jUwDZgN/ELI1TWdIUpC7a86jIcSvoy6mw2IrmvRsJsZkgA+3Nck++yQIDAQAB";

    private static final String privateKey ="MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDVsB22ZO6LoFEn+UFRJ8WtFSf4Z3xaIkLvnVe/LGWOQI873DQxmnQMvNsxuM4Y652OiVjCeL5Wtw4g9p3fwYwYwgWhL5m3vGLel3VC9ThU0/mdeSMSrBRgcbVtEobqYBwLm4CxaWxpk8OSysRwq1EEQQd1lira5tfiQf6w/HmeHGYNlq+9NssRDkGRuJEU89iX/Yg37+PccHv0MMNnZtGBwQ8P1wSWfK5bgEhcxjRj1l8dkfbOg4xydAsSQxh9alt8pn8UkSErEcfR2F0iU5TQhbIgTgty/+NTANmA38QsjVNZ0hSkLtrzqMhxK+jLqbDYiua9GwmxmSAD7c1yT77JAgMBAAECggEAHPkyLiblST1Fg7wCJTTVUrCJqN6vN7Fpjf+xhBRK+My8HwuucANXNNruhYFfQHXB+S+aP1QLysorOkD2Cv69NiYoiUBhAU6jwTacCaYiKy5KrBVesgtu/9yrEszuSh0Z8GD3RBbdQjtsyxx/Ih+4bnE6x7DCwseGAgGcvPaertc/dYMUV5xbgmHzyFI9M4bOt8OZ5mtH1Pp0JZSQh9Gqah7lmxJZTHAM1aieSDNmAyzx/4FGaYFr4O4tKLJFjDbNpKqSwv218FlGsE/fr3UTgj07GbFDVGGnES226S3ESclfjjkCnA61NoH+eoHEbQLDRcEyrxVUxWKedIvCI6jVWQKBgQDvgl1eUjUSo5n17NRLZfNV2qgL6ZVsmp2e/uRys/gi5Vs6ZVRieGIC8vCAVUnkInUFGz7uCaTNT30qPw/yQtEasU0lH26qp4HjDd7yYToHMkMt+8k6m86vXduOfinc8dNX4r1Ou31o5V2XtE8pLYglpsXjpyZUAU7Q5DSFY7RTmwKBgQDkZp7Yr9dH4HoNdzMM/le9CIRlmoyHf4Zto0lCHMD0c3NhBrOZOL4npI759glM5Z+pR4ueRGKYlFs/WijFW5qj2zAc5O4eTEVOGrtcmW+qVQFPkVmhQf0waz3WBNKrm1zjriX6I20heEcDb1un2zryB2gAtx1zSo2cwqnR5de3awKBgBB7ai15fsl/lLLFz344z9wSpgnb0zqcEUjFxbbXB7ZvDi4glwQHwMbvLPnH5MPcUy08yMHczAiM9dIYcOJ9EqLBnMt3NEhuKpQTeko3wqSIIvNhJmHwIvMShG9z4zGfOqoLonduk5zKW/x6ng/5YCsnJOsbr5mMVX4uliavy9MNAoGBALWzcgE+eoyi51ZZEtLucIxg/zr5ZXzSFfaY5UnkKBkQ06RHuY9WlehhwKDYe/Oh5Y+87DS/gJSTybevmSnQw0hBspf6A/k0ghotqAp1wmEGeSF8p4pjPfJMDoh5k8OTuKUkwO+rSUFMN2pjJ8502QL8o1+WTaaPk/M25fSm3iL7AoGBAJf32jKH7sczhC9Azg6DiDQTE2bhMrKZTQ3TfFni+wNoQ3Lkf1aFEPWSpjLgaOvNqgR06C5nWtTlg6aXzO+G+bSm6wKUfk9ZrRKW0miC7Cvpc44iWCbW/3iZaVILHgDht5gm/lcJVPD07sqCafSvw4LHaz1wf0dWxXuNsUWUWmXd";


    /**
     * RSA加密
     *
     * RSA2048(秘钥为2048位)要求被加密的数据不能大于245bytes，过长数据需要做分段。
     * 因为RSA加密比较耗资源，不建议大段数据使用RSA加密。
     * 该方法未做分段，因此不支持大块数据加密。
     *
     * @param originalData 原始业务数据（不可大于245bytes）
     * @param publicKey 公钥
     * @param charsetName 编码格式
     * @return 加密后的数据
     * @throws Exception 异常
     */
    public static String rsaEncrypt(String originalData, String publicKey, String charsetName) throws Exception {
        if (StringUtils.isBlank(originalData) || StringUtils.isBlank(publicKey) || StringUtils.isBlank(charsetName)) {
            throw new RuntimeException("RSA加密入参错误");
        }
        byte[] originalDataBytes = originalData.getBytes(charsetName);
        final Cipher cipher = Cipher.getInstance(RSA_CIPHER_ALGO);
        Key rsaKey = getPublicKey(Base64.decodeBase64(publicKey), RSA);
        cipher.init(Cipher.ENCRYPT_MODE, rsaKey);
        byte[] encryptedData = cipher.doFinal(originalDataBytes);
        return Base64.encodeBase64String(encryptedData);
    }

    /**
     * RAS解密
     *
     * @param encryptedData 需要解密的数据
     * @param privateKey 私钥
     * @param charsetName 编码格式
     * @return 解密后的数据
     * @throws Exception 异常
     */
    public static String rsaDecrypt(String encryptedData, String privateKey, String charsetName) throws Exception {
        if (StringUtils.isBlank(encryptedData) || StringUtils.isBlank(privateKey) || StringUtils.isBlank(charsetName)) {
            throw new RuntimeException("RSA解密入参错误");
        }
        byte[] encryptedDataBytes = Base64.decodeBase64(encryptedData);
        PrivateKey priKey = getPrivateKey(Base64.decodeBase64(privateKey), RSA);
        Cipher cipher = Cipher.getInstance(RSA_CIPHER_ALGO);
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        byte[] decryptedData = cipher.doFinal(encryptedDataBytes);
        return new String(decryptedData, charsetName);
    }

    /**
     * AES加密
     * @param originalData 原始数据
     * @param aesKey AES秘钥
     * @param charsetName 编码格式
     * @return 加密后的数据
     * @throws Exception 异常
     */
    public static String aesEncrypt(String originalData, String aesKey, String charsetName) throws Exception {
        if (StringUtils.isBlank(originalData) || StringUtils.isBlank(aesKey) || StringUtils.isBlank(charsetName)) {
            throw new RuntimeException("AES加密入参错误");
        }
        byte[] originalDataBytes = originalData.getBytes(charsetName);
        final Cipher cipher = Cipher.getInstance(AES_CIPHER_ALGO);
        SecretKeySpec keySpec = new SecretKeySpec(Base64.decodeBase64(aesKey), AES);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);
        byte[] encryptedData = cipher.doFinal(originalDataBytes);
        return Base64.encodeBase64String(encryptedData);
    }

    /**
     * AES解密
     * @param encryptedData 加密后的数据
     * @param aesKey AES秘钥
     * @param charsetName 编码格式
     * @return 解密后数据
     * @throws Exception 异常
     */
    public static String aesDecrypt(String encryptedData, String aesKey, String charsetName) throws Exception {
        if (StringUtils.isBlank(encryptedData) || StringUtils.isBlank(aesKey) || StringUtils.isBlank(charsetName)) {
            throw new RuntimeException("AES解密入参错误");
        }
        byte[] encryptDataBytes = Base64.decodeBase64(encryptedData);
        final Cipher cipher = Cipher.getInstance(AES_CIPHER_ALGO);
        SecretKeySpec keySpec = new SecretKeySpec(Base64.decodeBase64(aesKey), AES);
        cipher.init(Cipher.DECRYPT_MODE, keySpec);
        byte[] decryptedData = cipher.doFinal(encryptDataBytes);
        return new String(decryptedData, charsetName);
    }

    /**
     * 生成AES秘钥
     * @return Base64编码的秘钥值
     * @throws Exception 异常
     */
    public static String generateAESKey() throws Exception {
        KeyGenerator gen = KeyGenerator.getInstance(AES);
        /* 256-bit AES */
        gen.init(256);
        SecretKey secret = gen.generateKey();
        byte[] binary = secret.getEncoded();
        return Base64.encodeBase64String(binary);
    }


    /**
     * 对原始数据进行签名，返回Base64编码的签名值
     *
     * @param data  待签名数据
     * @param privateKey   秘钥
     * @param algorithm 算法名称
     * @param charsetName 编码格式
     * @return 签名结果
     * @throws Exception 异常
     */
    public static String sign(String data, String privateKey, String algorithm, String charsetName) throws Exception {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(privateKey) || StringUtils.isBlank(charsetName) || StringUtils.isBlank(algorithm)) {
            throw new RuntimeException("SIGN签名入参错误");
        }
        byte[] dataBytes = data.getBytes(charsetName);
        PrivateKey key = getPrivateKey(Base64.decodeBase64(privateKey), RSA);
        Signature signature = Signature.getInstance(algorithm);
        signature.initSign(key);
        signature.update(dataBytes);
        return Base64.encodeBase64String(signature.sign());
    }

    /**
     * 验签
     * @param data 待验签数据
     * @param sign 签名值
     * @param publicKey   公钥
     * @param algorithm 算法
     * @param charsetName 编码格式
     * @return 验签结果
     * @throws Exception 异常
     */
    public static boolean verifySign(String data, String sign, String publicKey, String algorithm, String charsetName) throws Exception {
        if (StringUtils.isBlank(data) || StringUtils.isBlank(sign) || StringUtils.isBlank(charsetName)
                || StringUtils.isBlank(algorithm) || StringUtils.isBlank(publicKey)) {
            throw new RuntimeException("VERIFY验签入参错误");
        }
        byte[] dataBytes = data.getBytes(charsetName);
        PublicKey key = getPublicKey(Base64.decodeBase64(publicKey), RSA);
        Signature signature = Signature.getInstance(algorithm);
        signature.initVerify(key);
        signature.update(dataBytes);
        return signature.verify(Base64.decodeBase64(sign));
    }

    /**
     * 生成私钥
     * @param keyData 秘钥数据
     * @param algorithm 算法
     * @return 私钥
     * @throws Exception 异常
     */
    private static PrivateKey getPrivateKey(final byte[] keyData, String algorithm) throws Exception {
        final PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyData);
        final KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        return keyFactory.generatePrivate(keySpec);
    }

    /**
     * 生成公钥
     * @param keyData 秘钥数据
     * @param algorithm 算法
     * @return 公钥
     * @throws Exception 异常
     */
    private static PublicKey getPublicKey(final byte[] keyData, String algorithm) throws Exception {
        final X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyData);
        final KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        return keyFactory.generatePublic(keySpec);
    }

    /***
     *
     * @param data 加密后数据
     * @param aesKeyEncrypt 加密后密钥
     * @return
     * @throws Exception
     */
    public static String decodeData(String data,String aesKeyEncrypt) throws Exception {
        // 1.RSA解密AES秘钥
        String aesKeyDecrypt = rsaDecrypt(aesKeyEncrypt, privateKey, DEFAULT_CHARSET);
        System.out.println("aesKeyDecrypt = " + aesKeyDecrypt);
        // 2.AES秘钥解密原始数据
        String decryptText = aesDecrypt(data, aesKeyDecrypt, DEFAULT_CHARSET);
        return  decryptText;
    }
}
