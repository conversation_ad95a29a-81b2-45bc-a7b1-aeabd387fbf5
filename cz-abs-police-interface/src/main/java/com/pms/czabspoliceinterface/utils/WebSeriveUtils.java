package com.pms.czabspoliceinterface.utils;

import net.sf.json.JSONObject;
import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.axis.encoding.XMLType;
import org.apache.axis.message.SOAPHeaderElement;

import javax.xml.namespace.QName;
import javax.xml.rpc.ParameterMode;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPException;

public class WebSeriveUtils {
    /**
     *
     * @return
     * @throws Exception
     */
    public static String endMsgTest(String url,String namespace) throws Exception {

        /**
         * 根目录 interface
         */
        JSONObject k = new JSONObject();
        /**
         * 次级 item目录
         */
        JSONObject j = new JSONObject();
        j.put("name", "崇泽测试");
        String param = "崇泽测试";

        //上面代码为从缓存中取到我们需求传递到认证头的数据 下面开始添加认证头
        SOAPHeaderElement head = new SOAPHeaderElement(new QName(namespace,"authentication"));
        try {
            // 账号密码
            SOAPElement a1 = head.addChildElement("username");
            a1.addTextNode("4203811214");
            a1 = head.addChildElement("password");
            a1.addTextNode("cz230901");

            head.setPrefix("");
            head.setActor(null);
            //head.setMustUnderstand(true);
        } catch (SOAPException e) {
            e.printStackTrace();
        }

        Service service = new Service();

        Call call = (Call) service.createCall();

        call.setTargetEndpointAddress(url);
        call.setOperationName(new QName(namespace, "test"));// 设置要调用哪个方法
        call.addHeader(head);

        call.addParameter( "arg0",
                XMLType.XSD_STRING, ParameterMode.IN);
        call.setEncodingStyle("UTF-8");
        call.setReturnType(org.apache.axis.encoding.XMLType.XSD_STRING);//设置结果返回类型
        String result = (String) call.invoke( new Object[]{param});//方法执行后的返回值
        System.out.println(result);
        return result;

    }
}
