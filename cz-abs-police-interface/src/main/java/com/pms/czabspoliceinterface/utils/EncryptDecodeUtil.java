package com.pms.czabspoliceinterface.utils;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Security;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工具类.
 */
public class EncryptDecodeUtil {

    EncryptDecodeUtil() {}

    private static final String EncryptAlg = "AES";
    private static final String Cipher_Mode = "AES/ECB/PKCS5Padding";
    private static final String Encode = "UTF-8";
    private static final int Secret_Key_Size = 32;
    private static final String Key_Encode = "UTF-8";

    /**
     * AES/ECB/PKCS7Padding 加密
     *
     * @param content
     * @param key     密钥
     * @return aes加密后 转base64
     * @throws Exception
     */
    public static String aesPKCS7PaddingEncrypt(String content, String key) throws Exception{
        try {
            Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());

            Cipher cipher = Cipher.getInstance(Cipher_Mode);
            byte[] realKey = getSecretKey(key);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(realKey, EncryptAlg));
            byte[] data = cipher.doFinal(content.getBytes(Encode));
            String result = new Base64().encodeToString(data);
            return result;
        } catch (Exception e) {
            String s = "AES加密失败：content=" + content + " key=" + key;
            throw  new Exception(s,e);
        }
    }

    /**
     * AES/ECB/PKCS7Padding 解密
     *
     * @param content
     * @param key     密钥
     * @return 先转base64 再解密
     * @throws Exception
     */
    public static String aesPKCS7PaddingDecrypt(String content, String key) throws Exception{
        try {

            byte[] decodeBytes = Base64.decodeBase64(content);

            Cipher cipher = Cipher.getInstance(Cipher_Mode);
            byte[] realKey = getSecretKey(key);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(realKey, EncryptAlg));
            byte[] realBytes = cipher.doFinal(decodeBytes);

            return new String(realBytes, Encode);
        } catch (Exception e) {
            throw new Exception("AES解密失败：Aescontent = " + e.fillInStackTrace(), e);
        }
    }

    /**
     * 对密钥key进行处理：如密钥长度不够位数的则 以指定paddingChar 进行填充； 此处用#字符填充
     *
     * @param key
     * @return
     * @throws Exception
     */
    private static byte[] getSecretKey(String key) throws UnsupportedEncodingException {
        final byte paddingChar = '#';

        byte[] realKey = new byte[Secret_Key_Size];
        byte[] byteKey = key.getBytes(Key_Encode);
        for (int i = 0; i < realKey.length; i++) {
            if (i < byteKey.length) {
                realKey[i] = byteKey[i];
            } else {
                realKey[i] = paddingChar;
            }
        }

        return realKey;
    }

    /**
     * 签名生成
     *
     * @param params 接口对象vo里面的全部实例变量的集合
     * @return
     * @throws Exception
     */
    public static String sign(List<String> params) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        if (CollectionUtils.isEmpty(params))
            return "";

        MessageDigest md = MessageDigest.getInstance("SHA-256");
        // 反复调用update输入数据:
        md.update(getSignString(params).getBytes("UTF-8"));
        byte[] result = md.digest();
        return new BigInteger(1, result).toString(16);
    }

    /**
     * 接口的参数值，全部放入集合list列表；
     * 按照字母升序排序，然后按照^Parma1^Parma2等拼接起来；
     * 按照字母降序排序，然后按照%Parma1%Parma2等拼接起来；
     * 将上面2个字符串拼接起来；
     * @param param 接口对象vo里面的全部实例变量的集合
     * @return
     */
    private static String getSignString(List<String> param) {
        List<String> newParamASC = param.stream().filter(x -> StringUtils.isNotEmpty(x)).sorted(Comparator.comparing(x -> x.toString())).collect(Collectors.toList());
        System.out.println(newParamASC.toString());
        List<String> newParamDESC = param.stream().filter(x -> StringUtils.isNotEmpty(x)).sorted(Comparator.comparing(x -> x.toString()).reversed()).collect(Collectors.toList());
        System.out.println(newParamDESC.toString());
        StringBuffer joinParam = new StringBuffer();
        if (!CollectionUtils.isEmpty(newParamASC)) {
            newParamASC.stream().forEach(x -> {
                joinParam.append("^").append(x);
            });
        }
        System.out.println("joinParamjoinParamjoinParamjoinParam"+joinParam);
        if (!CollectionUtils.isEmpty(newParamASC)) {
            newParamDESC.stream().forEach(x -> {
                joinParam.append("%").append(x);
            });
        }
        System.out.println("joinParamjoinParamjoinParamjoinParam11"+joinParam);
        if (StringUtils.isEmpty(joinParam.toString())) {
            return "";
        } else
            return joinParam.toString();
    }

    /**
     * 签名验证
     *
     * @param params 接口对象vo里面的全部实例变量的集合
     * @param sign
     * @return
     * @throws Exception
     */
    public static boolean verifySign(List<String> params, String sign) throws UnsupportedEncodingException, NoSuchAlgorithmException {
        if (sign == null)
            sign = "";
        String getSign = sign(params);
        if (getSign.compareTo(sign) != 0)
            return false;
        else
            return true;
    }
}
