package com.pms.czabspoliceinterface.utils;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.MultiValueMap;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class AESUtil {
    private static final String KEY_ALGORITHM = "AES";
    private static final String CIPHER_ALGORITHM_CBC = "AES/CBC/PKCS5Padding";

    /**
     * IV length: must be 16 bytes long
     */
    private static final String IV = "566b7bcede97d208";

    /**
     * 解密
     *
     * @param data 待解密数据
     * @param key  密钥
     * @return 明文
     * @throws Exception
     */
    public static String decrypt(String data, String key) throws Exception {
        Key k = new SecretKeySpec(getKey(key), KEY_ALGORITHM);
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_CBC);
        cipher.init(Cipher.DECRYPT_MODE, k, new IvParameterSpec(getIv()));
        // 执行操作
        byte[] result = cipher.doFinal(Base64.decodeBase64((data)));
        return new String(result, StandardCharsets.UTF_8);
    }

    /**
     * 加密
     *
     * @param data 待加密数据
     * @param key  密钥
     * @return 密文
     * @throws Exception
     */
    public static String encrypt(String data, String key) throws Exception {
        byte[] encryptKey = getKey(key);
        Key k = new SecretKeySpec(encryptKey, KEY_ALGORITHM);
        Cipher cipher = Cipher.getInstance(CIPHER_ALGORITHM_CBC);
        cipher.init(Cipher.ENCRYPT_MODE, k, new IvParameterSpec(getIv()));
        // 执行操作
        byte[] result = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeBase64String(result);
    }



    //固定IV值
    private static byte[] getIv() {
        return IV.getBytes(StandardCharsets.UTF_8);
    }

    public static byte[] getKey(String originalStr) {
        return DigestUtils.md5Hex(originalStr).substring(8, 24).getBytes(StandardCharsets.UTF_8);
    }



    //动态的IV获取，是传入字符串MD5后的 左8位与右8位 yq 20190605
    private static byte[] getJcIV(String originalStr) {
        String md5=DigestUtils.md5Hex(originalStr);
        String iv =md5.substring(0,8)+md5.substring(md5.length()-8);
        return iv.getBytes(StandardCharsets.UTF_8);
    }


    public static String getSignStr(MultiValueMap<String, Object> map, String APP_SECRET) {
        List<String> paramNames = new ArrayList<>(map.keySet());
        Collections.sort(paramNames);
        StringBuilder signStr = new StringBuilder();
        signStr.append(APP_SECRET);
        for (String paramName : paramNames) {
            signStr.append(paramName).append(map.get(paramName).get(0));
        }
        signStr.append(APP_SECRET);

        String signResult = DigestUtils.sha1Hex(signStr.toString()).toUpperCase();

        System.out.println("签名前:  " + signStr);
        System.out.println("签名后:  " + signResult);
        return signResult;
    }
}
