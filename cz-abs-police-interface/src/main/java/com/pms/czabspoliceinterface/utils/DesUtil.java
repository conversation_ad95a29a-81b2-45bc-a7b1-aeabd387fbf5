package com.pms.czabspoliceinterface.utils;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.io.IOException;
import java.security.SecureRandom;

public class DesUtil {
    private static final String DES = "DES";
    private static final String KEY = "szzahotel";

    public DesUtil() {
    }
    public static String encrypt(String data) {
        String strs = "";

        try {
            byte[] bt = encrypt(data.getBytes(), "szzahotel".getBytes());
            strs = (new BASE64Encoder()).encode(bt);
        } catch (Exception var4) {
            strs = "0";
        }

        return strs;
    }

    public static String decrypt(String data) {
        if (data == null) {
            return null;
        } else {
            BASE64Decoder decoder = new BASE64Decoder();
            String result = "";

            try {
                byte[] buf = decoder.decodeBuffer(data);

                try {
                    byte[] bt = decrypt(buf, "szzahotel".getBytes());
                    result = new String(bt);
                } catch (Exception var6) {
                    result = "0";
                }
            } catch (IOException var7) {
                result = "0";
            }

            return result;
        }
    }

    private static byte[] encrypt(byte[] data, byte[] key) throws Exception {
        SecureRandom sr = new SecureRandom();
        DESKeySpec dks = new DESKeySpec(key);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey securekey = keyFactory.generateSecret(dks);
        Cipher cipher = Cipher.getInstance("DES");
        cipher.init(1, securekey, sr);
        return cipher.doFinal(data);
    }

    private static byte[] decrypt(byte[] data, byte[] key) throws Exception {
        SecureRandom sr = new SecureRandom();
        DESKeySpec dks = new DESKeySpec(key);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey securekey = keyFactory.generateSecret(dks);
        Cipher cipher = Cipher.getInstance("DES");
        cipher.init(2, securekey, sr);
        return cipher.doFinal(data);
    }
}
