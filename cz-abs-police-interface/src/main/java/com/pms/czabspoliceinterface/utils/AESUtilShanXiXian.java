package com.pms.czabspoliceinterface.utils;

import cn.hutool.core.util.StrUtil;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Security;
import java.security.spec.AlgorithmParameterSpec;
import java.util.HashMap;
import java.util.Random;

public class AESUtilShanXiXian {
    private static final String CHARSET_NAME = "UTF-8";
    private static final String AES_NAME = "AES";
    // 加密模式
    public static final String ALGORITHM = "AES/CBC/PKCS7Padding";
    // 密钥
    public static String KEY = "";
    // 偏移量
    public static String IV = "";

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    /**
     * 加密
     *
     * @param content
     * @param content
     * @return
     */
    public String encrypt(String content) {
        byte[] result = null;
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(KEY.getBytes(CHARSET_NAME), AES_NAME);
            AlgorithmParameterSpec paramSpec = new IvParameterSpec(IV.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, paramSpec);
            result = cipher.doFinal(content.getBytes(CHARSET_NAME));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Base64.encodeBase64String(result);
    }

    /**
     * 解密
     *
     * @param content
     * @param content
     * @return
     */
    public String decrypt(String content) {
        try {
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(KEY.getBytes(CHARSET_NAME), AES_NAME);
            AlgorithmParameterSpec paramSpec = new IvParameterSpec(IV.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, keySpec, paramSpec);
            return new String(cipher.doFinal(Base64.decodeBase64(content)), CHARSET_NAME);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return StrUtil.EMPTY;
    }
    /**
     * 获取加密数据和datakey值
     *
     * @return
     */
    public static HashMap<String, String> getData(String data, String publicKey) throws Exception {
        AESUtilShanXiXian aes = new AESUtilShanXiXian();
        String key = getKey();
        String datakey = CryptoUtil.rsaEncrypt(key, publicKey, CryptoUtil.DEFAULT_CHARSET);
        KEY = key;
        IV = key;
        String encrypt = aes.encrypt(data);
        HashMap<String, String> hashMap = new HashMap<>();
        hashMap.put("encryptData", encrypt);
        hashMap.put("datakey", datakey);
        return hashMap;
    }

    public static String getKey() {
        String az = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        int max = az.length();
        int min = 0;
        Random random = new Random();
        String zifu = "";

        for (int i = 1; i <= 16; i++) {
            int s = random.nextInt(max) % (max - min + 1) + min;
            char n = az.charAt(s);
            zifu += n;
        }
        System.out.println(zifu);
        return zifu;
    }

}