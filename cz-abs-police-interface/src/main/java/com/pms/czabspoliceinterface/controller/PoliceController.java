package com.pms.czabspoliceinterface.controller;


import com.pms.czabspoliceinterface.bean.WaitCheckinPersonInfoReqeust;
import com.pms.czabspoliceinterface.factory.*;
import com.pms.czabspoliceinterface.lock.factory.LockLink;
import com.pms.czabspoliceinterface.lock.factory.TuYa;
import com.pms.czabspoliceinterface.lock.service.SmartLockServiceImpl;
import com.pms.czabspoliceinterface.service.PoliceServiceImpl;
import com.pms.czabspoliceinterface.service.WaitCheckinPersonImpl;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.request.DeviceControlRequest;
import com.pms.czpmsutils.request.OpenDoorRequest;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import com.pms.pmsorder.bean.search.PersonInfoSearch;
import com.pms.pmsorder.dao.RegistPersonHealthCodeDao;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@Controller
@RequestMapping("/hotel/police/")
public class PoliceController {

    @Autowired
    PoliceServiceImpl policeServiceImpl;
    @Autowired
    WaitCheckinPersonImpl waitCheckinPersonImpl;
    @Autowired
    RegistPersonHealthCodeDao registPersonHealthCodeDao;

    @Autowired
    private SmartLockServiceImpl smartLockService;

    /**
     * 获取缓存方法的对象
     */
    @Resource(name = "stringRedisTemplate")
    protected StringRedisTemplate stringRedisTemplate;
    @RequestMapping("checkin.do")
    @ResponseBody
    public GuestResult checkin(@RequestBody JSONObject param) {
        GuestResult guestResult = new GuestResult();
        try {
            if (!param.containsKey("Factory")) {
                guestResult.setResult(false);
                guestResult.setMsg("厂家信息不能为空");
                return guestResult;
            }
            String factory = param.getString("Factory");

            GuestModel guestModel = new GuestModel();
            guestModel.setUrl(param.containsKey("Url") ? param.getString("Url") : "");
            guestModel.setHotelCode(param.containsKey("HotelCode") ? param.getString("HotelCode") : "");
            guestModel.setOperators(param.containsKey("Operators") ? param.getString("Operators") : "");
            guestModel.setKey(param.containsKey("Key") ? param.getString("Key") : "");
            guestModel.setCode(param.containsKey("Code") ? param.getString("Code") : "");
            guestModel.setName(param.getString("Name"));
            guestModel.setSex(param.getString("Sex"));
            guestModel.setAddress(param.getString("Address"));
            guestModel.setiDCode(param.getString("IDCode"));
            guestModel.setBirthday(param.getString("Birthday"));
            guestModel.setPhone(param.containsKey("Phone") ? param.getString("Phone") : "");
            guestModel.setPhoto(param.containsKey("Photo") ? param.getString("Photo") : "");
            guestModel.setCameraPhoto(param.containsKey("CameraPhoto") ? param.getString("CameraPhoto") : "");
            guestModel.setIsNoCard(param.containsKey("IsNoCard") ? param.getInt("IsNoCard") : 0);
            guestModel.setNation(param.containsKey("Nation") ? param.getString("Nation") : "");
            guestModel.setIsChildren(param.containsKey("IsChildren") ? param.getInt("IsChildren") : 0);
            guestModel.setRoomNo(param.getString("RoomNo"));
            guestModel.setCheckInTime(param.containsKey("CheckInTime") ? param.getString("CheckInTime") : "");
            guestModel.setCheckOutTime(param.containsKey("CheckOutTime") ? param.getString("CheckOutTime") : "");
            guestModel.setSemblance(param.containsKey("Semblance") ? param.getString("Semblance") : "");
            guestModel.setLoginPhoto(param.containsKey("LoginPhoto") ? param.getString("LoginPhoto") : "");
            guestModel.setLoginCamera(param.containsKey("LoginCamera") ? param.getString("LoginCamera") : "");


            //验证证件号是否合法
            IdcardValidator iv = new IdcardValidator();
            if (!iv.isValidatedAllIdcard(guestModel.getiDCode())) {
                guestResult.setResult(false);
                guestResult.setMsg("证件号不合法");
                return guestResult;
            }

            IdcardInfoExtractor idcardInfoExtractor = new IdcardInfoExtractor(guestModel.getiDCode());
            String gender = idcardInfoExtractor.getGender();
            guestModel.setSex(gender);

            //2020-01-07新增健康码相关业务
            if (param.containsKey("goAddress") && !param.getString("goAddress").equals("")) {
                guestModel.setGoAddress(param.getString("goAddress"));
            }

            if (param.containsKey("temperatureStr") && !param.getString("temperatureStr").equals("")) {
                guestModel.setTemperature(param.getString("temperatureStr"));
            }

            if (param.containsKey("nuclein") && !param.getString("nuclein").equals("")) {
                guestModel.setNuclein(param.getString("nuclein"));
            }

            if (param.containsKey("vaccinum") && !param.getString("vaccinum").equals("")) {
                guestModel.setVaccinum(param.getString("vaccinum"));
            }

            if (param.containsKey("healthCode") && !param.getString("healthCode").equals("")) {
                guestModel.setHealthCode(param.getString("healthCode"));
            }

            Object tokenId = param.get("tokenId");
            if (tokenId != null && !"".equals(tokenId.toString())) {
                guestModel.setTokenId(tokenId.toString());
            }
            if (guestModel.getTokenId() == null) {
                guestModel.setTokenId(param.getString("sessionToken"));
            }
            switch (factory) {
                case "JinChengBs":
                    guestResult = new ZheJiangJinCheng().checkin(guestModel);
                    break;
                case "Shanghai":
                    guestResult = new ShangHai().checkin(guestModel);
                    break;
                case "ChongQing":
                    guestResult = new ChongQing().checkin(guestModel);
                    break;
                case "SiChuangHangXin":
                    guestResult = new SiChuangHangXin().checkin(guestModel);
                    break;
                case "JiangSuSuZhouGuoTai":
                    guestResult = new JiangSuSuZhouGuoTai().checkin(guestModel);
                    break;
                case "GuiZhouHangXin":
                    guestResult = new GuiZhouHangXin().checkin(guestModel);
                    break;
                case "JiangSuShenDunJingZhe":
                    guestResult = new JiangSuJingZhe().checkin(guestModel);
                    break;
                case "ShenDun":
                    guestResult = new JiangSuJingZhe().checkin(guestModel);
                    break;
                case "LockLink":
                    guestResult = new LockLink().checkin(guestModel);
                    break;
                case "FuJianMoChuang":
                    guestResult = new FuJianMoChuang().checkin(guestModel);
                    break;
                case "YK":
                    guestResult = new YK().checkin(guestModel);
                    break;
                case "Alad":
                    guestResult = new Alad().checkin(guestModel);
                    break;
                case "HuBeiShiYan":
                    guestResult = new HuBeiShiYan().checkin(guestModel);
                    break;
                case "DaYinJunHui":
                    guestResult = new DaYinJunHui(stringRedisTemplate).checkin(guestModel);
                    break;
                case "ShanXiXiAn":
                    guestResult = new ShanXiXiAn().checkin(guestModel);
                    break;
                case "WuXiJiDong":
                    guestResult = new WuXiJiDong().checkin(guestModel);
                    break;
                case "ChongQingHangXin":
                    guestResult = new ChongQingHangXin().checkin(guestModel);
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @RequestMapping("changeRoomForPolice.do")
    @ResponseBody
    public GuestResult changeRoom(@RequestBody GuestModel guestModel) {
        System.out.println("公安网换房业务执行:" + JSONObject.fromObject(guestModel).toString());
        GuestResult guestResult = new GuestResult();
        try {
            if (null == guestModel.getFactory() || guestModel.getFactory().equals("")) {
                throw new Exception(HOTEL_CONST.FACTORY_IS_NULL);
            }
            String factory = guestModel.getFactory();


            //验证证件号是否合法
            IdcardValidator iv = new IdcardValidator();
            if (!iv.isValidatedAllIdcard(guestModel.getiDCode())) {
                guestResult.setResult(false);
                guestResult.setMsg("证件号不合法");
                return guestResult;
            }

            switch (factory) {
                case "JinChengBs":
                    guestResult = new ZheJiangJinCheng().changeRoom(guestModel);
                    break;
                case "Shanghai":
                    guestResult = new ShangHai().changeRoom(guestModel);
                    break;
                case "ChongQing":
                    guestResult = new ChongQing().changeRoom(guestModel);
                    break;
                case "SiChuangHangXin":
                    guestResult = new SiChuangHangXin().changeRoom(guestModel);
                    break;
                case "JiangSuSuZhouGuoTai":
                    guestResult = new JiangSuSuZhouGuoTai().changeRoom(guestModel);
                    break;
                case "GuiZhouHangXin":
                    guestResult = new GuiZhouHangXin().changeRoom(guestModel);
                    break;
                case "JiangSuShenDunJingZhe":
                    guestResult = new JiangSuJingZhe().changeRoom(guestModel);
                    break;
                case "ShenDun":
                    guestResult = new JiangSuJingZhe().changeRoom(guestModel);
                    break;
                case "FuJianMoChuang":
                    guestResult = new FuJianMoChuang().changeRoom(guestModel);
                    break;
                case "YK":
                    guestResult = new YK().changeRoom(guestModel);
                    break;
                case "Alad":
                    guestResult = new Alad().changeRoom(guestModel);
                    break;
                case "DaYinJunHui":
                    guestResult = new DaYinJunHui(stringRedisTemplate).changeRoom(guestModel);
                    break;
                case "ShanXiXiAn":
                    guestResult = new ShanXiXiAn().changeRoom(guestModel);
                    break;
                case "WuXiJiDong":
                    guestResult = new WuXiJiDong().changeRoom(guestModel);
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @RequestMapping("checkout.do")
    @ResponseBody
    public GuestResult checkout(@RequestBody JSONObject param) {
        GuestResult guestResult = new GuestResult();
        try {

            if (!param.containsKey("Factory")) {
                guestResult.setResult(false);
                guestResult.setMsg("厂家信息不能为空");
            }
            System.out.println("公安退房数据:" + JSONObject.fromObject(guestResult).toString());
            String factory = param.getString("Factory");
            GuestModel guestModel = new GuestModel();
            guestModel.setUrl(param.containsKey("Url") ? param.getString("Url") : "");
            guestModel.setHotelCode(param.containsKey("HotelCode") ? param.getString("HotelCode") : "");
            guestModel.setOperators(param.containsKey("Operators") ? param.getString("Operators") : "");
            guestModel.setKey(param.containsKey("Key") ? param.getString("Key") : "");
            guestModel.setiDCode(param.containsKey("IDCode") ? param.getString("IDCode") : "");
            guestModel.setRoomNo(param.containsKey("RoomNo") ? param.getString("RoomNo") : "");
            guestModel.setCode(param.containsKey("Code") ? param.getString("Code") : "");
            guestModel.setGuestNo(param.containsKey("GuestNo") ? param.getString("GuestNo") : "");
            guestModel.setGuestId(param.containsKey("GuestId") ? param.getString("GuestId") : "");
            guestModel.setSex(param.containsKey("Sex") ? param.getString("Sex") : "");
            guestModel.setNation(param.containsKey("Nation") ? param.getString("Nation") : "");
            guestModel.setAddress(param.containsKey("Address") ? param.getString("Address") : "");
            guestModel.setBirthday(param.containsKey("Birthday") ? param.getString("Birthday") : "");
            guestModel.setName(param.containsKey("Name") ? param.getString("Name") : "");

            switch (factory) {
                case "JinChengBs":
                    return new ZheJiangJinCheng().checkout(guestModel);
                case "Shanghai":
                    return new ShangHai().checkout(guestModel);
                case "ChongQing":
                    return new ChongQing().checkout(guestModel);
                case "SiChuangHangXin":
                    return new SiChuangHangXin().checkout(guestModel);
                case "JiangSuSuZhouGuoTai":
                    return new JiangSuSuZhouGuoTai().checkout(guestModel);
                case "GuiZhouHangXin":
                    guestResult = new GuiZhouHangXin().checkout(guestModel);
                    break;
                case "JiangSuShenDunJingZhe":
                    guestResult = new JiangSuJingZhe().checkout(guestModel);
                    break;
                case "ShenDun":
                    guestResult = new JiangSuJingZhe().checkout(guestModel);
                    break;
                case "FuJianMoChuang":
                    guestResult = new FuJianMoChuang().checkout(guestModel);
                    break;
                case "YK":
                    guestResult = new YK().checkout(guestModel);
                    break;
                case "Alad":
                    guestResult = new Alad().checkout(guestModel);
                    break;
                case "HuBeiShiYan":
                    guestResult = new HuBeiShiYan().checkout(guestModel);
                    break;
                case "DaYinJunHui":
                    guestResult = new DaYinJunHui(stringRedisTemplate).checkout(guestModel);
                    break;
                case "ShanXiXiAn":
                    guestResult = new ShanXiXiAn().checkout(guestModel);
                    break;
                case "WuXiJiDong":
                    guestResult = new WuXiJiDong().checkout(guestModel);
                    break;
                case "ChongQingHangXin":
                    guestResult = new ChongQingHangXin().checkout(guestModel);
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }


    @RequestMapping("changeRoom.do")
    @ResponseBody
    public GuestResult changeRoom(@RequestBody JSONObject param) {
        GuestResult guestResult = new GuestResult();
        try {

            if (!param.containsKey("Factory")) {
                guestResult.setResult(false);
                guestResult.setMsg("厂家信息不能为空");
            }
            System.out.println("公安退房数据:" + JSONObject.fromObject(guestResult).toString());
            String factory = param.getString("Factory");
            GuestModel guestModel = new GuestModel();
            guestModel.setUrl(param.containsKey("Url") ? param.getString("Url") : "");
            guestModel.setHotelCode(param.containsKey("HotelCode") ? param.getString("HotelCode") : "");
            guestModel.setOperators(param.containsKey("Operators") ? param.getString("Operators") : "");
            guestModel.setKey(param.containsKey("Key") ? param.getString("Key") : "");
            guestModel.setiDCode(param.containsKey("IDCode") ? param.getString("IDCode") : "");
            guestModel.setRoomNo(param.containsKey("RoomNo") ? param.getString("RoomNo") : "");
            guestModel.setCode(param.containsKey("Code") ? param.getString("Code") : "");
            guestModel.setGuestNo(param.containsKey("GuestNo") ? param.getString("GuestNo") : "");
            guestModel.setGuestId(param.containsKey("GuestId") ? param.getString("GuestId") : "");
            guestModel.setSex(param.containsKey("Sex") ? param.getString("Sex") : "");
            guestModel.setNation(param.containsKey("Nation") ? param.getString("Nation") : "");
            guestModel.setAddress(param.containsKey("Address") ? param.getString("Address") : "");
            guestModel.setBirthday(param.containsKey("Birthday") ? param.getString("Birthday") : "");
            guestModel.setName(param.containsKey("Name") ? param.getString("Name") : "");

            switch (factory) {
                case "JinChengBs":
                    return new ZheJiangJinCheng().changeRoom(guestModel);
                case "Shanghai":
                    return new ShangHai().checkout(guestModel);
                case "ChongQing":
                    return new ChongQing().checkout(guestModel);
                case "SiChuangHangXin":
                    return new SiChuangHangXin().checkout(guestModel);
                case "JiangSuSuZhouGuoTai":
                    return new JiangSuSuZhouGuoTai().checkout(guestModel);
                case "GuiZhouHangXin":
                    guestResult = new GuiZhouHangXin().checkout(guestModel);
                    break;
                case "JiangSuShenDunJingZhe":
                    guestResult = new JiangSuJingZhe().checkout(guestModel);
                    break;
                case "ShenDun":
                    guestResult = new JiangSuJingZhe().checkout(guestModel);
                    break;
                case "FuJianMoChuang":
                    guestResult = new FuJianMoChuang().checkout(guestModel);
                    break;
                case "YK":
                    guestResult = new YK().checkout(guestModel);
                    break;
                case "Alad":
                    guestResult = new Alad().checkout(guestModel);
                    break;
                case "HuBeiShiYan":
                    guestResult = new HuBeiShiYan().checkout(guestModel);
                    break;
                case "DaYinJunHui":
                    guestResult = new DaYinJunHui(stringRedisTemplate).checkout(guestModel);
                    break;
                case "ShanXiXiAn":
                    guestResult = new ShanXiXiAn().checkout(guestModel);
                    break;
                case "WuXiJiDong":
                    guestResult = new WuXiJiDong().checkout(guestModel);
                    break;
                case "ChongQingHangXin":
                    guestResult = new ChongQingHangXin().checkout(guestModel);
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }


    @RequestMapping("pmsCheckin.do")
    @ResponseBody
    public ResponseData pmsCheckin(@RequestBody JSONObject param) {
        //如果存在type 则 推送 数据
        if (param.containsKey("type")) {
            return new DianJing().pushData(param);
        }
        ResponseData responseData = ResponseData.newSuccessData();
        GuestResult result = this.checkin(param);
        if (!result.getResult()) {
            responseData.setCode(-1);
            responseData.setData(result);
            System.out.println(JSONObject.fromObject(responseData).toString());
            return responseData;
        }
        responseData.setData(result);
        System.out.println(JSONObject.fromObject(responseData).toString());

        //更新guestNo guestId
        if (responseData.getCode() == 1) {
            GuestModel guestModel = new GuestModel();
            guestModel.setiDCode(param.getString("IDCode"));
            guestModel.setTokenId(param.getString("sessionToken"));
            guestModel.setGuestNo(result.getGuestNo());
            guestModel.setGuestId(result.getGuestId());
            guestModel.setRoomNo(param.getString("RoomNo"));

            //2020-01-07新增健康码相关业务
            if (param.containsKey("goAddress") && !param.getString("goAddress").equals("")) {
                guestModel.setGoAddress(param.getString("goAddress"));
            }

            if (param.containsKey("temperatureStr") && !param.getString("temperatureStr").equals("")) {
                guestModel.setTemperature(param.getString("temperatureStr"));
            }

            if (param.containsKey("nuclein") && !param.getString("nuclein").equals("")) {
                guestModel.setNuclein(param.getString("nuclein"));
            }

            if (param.containsKey("vaccinum") && !param.getString("vaccinum").equals("")) {
                guestModel.setVaccinum(param.getString("vaccinum"));
            }

            if (param.containsKey("healthCode") && !param.getString("healthCode").equals("")) {
                guestModel.setHealthCode(param.getString("healthCode"));
            }
            policeServiceImpl.updateRegistPersonInfo(guestModel);
        }
        return responseData;
    }


    @RequestMapping("pmsDeviceControll.do")
    @ResponseBody
    public ResponseData pmsDeviceControll(@RequestBody DeviceControlRequest deviceControlRequest) {
        if (deviceControlRequest.getBussType() == 4) {
            OpenDoorRequest openDoorRequest = new OpenDoorRequest();
            openDoorRequest.setFactory("TuYaSmartLock");
            openDoorRequest.setRoomNo(deviceControlRequest.getRoomNo());
            openDoorRequest.setUuid(deviceControlRequest.getDeviceId());
            openDoorRequest.setSessionToken(deviceControlRequest.getSessionToken());
            return smartLockService.openDoor(openDoorRequest);
        }
        ResponseData responseData = smartLockService.deviceControl(deviceControlRequest);
        responseData.setData("");
        responseData.setData1("");
        return responseData;
    }

    @RequestMapping("pmsCheckout.do")
    @ResponseBody
    public ResponseData pmsCheckout(@RequestBody JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        GuestResult result = this.checkout(param);
        if (!result.getResult()) {
            responseData.setCode(-1);
            return responseData;
        }
        responseData.setData(result);
        return responseData;
    }

    @RequestMapping("pmsAddWaitCheckinPersonInfo.do")
    @ResponseBody
    public ResponseData pmsWaitCheckinPersonInfo(@RequestBody WaitCheckinPersonInfoReqeust waitCheckinPersonInfoReqeust) {
        return waitCheckinPersonImpl.addWaitCheckinPersonInfo(waitCheckinPersonInfoReqeust);
    }

    @RequestMapping("pmsGetWaitCheckinPersonInfo.do")
    @ResponseBody
    public ResponseData getWaitCheckinPersonInfo(@RequestBody PersonInfoSearch personInfoSearch) {
        return waitCheckinPersonImpl.getWaitCheckinPersonInfo(personInfoSearch);
    }

    @RequestMapping("login.do")
    @ResponseBody
    public JSONObject login(@RequestBody JSONObject param) {
        JSONObject guestResult = new JSONObject();
        try {

            if (!param.containsKey("Factory")) {
                guestResult.put("Result", false);
                guestResult.put("Msg", "厂家信息不能为空");
            }
            String factory = param.getString("Factory");
            GuestModel guestModel = new GuestModel();
            guestModel.setUrl(param.containsKey("Url") ? param.getString("Url") : "");
            guestModel.setHotelCode(param.containsKey("HotelCode") ? param.getString("HotelCode") : "");
            guestModel.setOperators(param.containsKey("Operators") ? param.getString("Operators") : "");
            guestModel.setKey(param.containsKey("Key") ? param.getString("Key") : "");
            guestModel.setLoginPhoto(param.containsKey("LoginPhoto") ? param.getString("LoginPhoto") : "");
            guestModel.setLoginCamera(param.containsKey("LoginCamera") ? param.getString("LoginCamera") : "");
            switch (factory) {
                case "Shanghai":
                    return new ShangHai().userLogin(guestModel);
            }
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.put("Result", false);
            guestResult.put("Msg", e.getMessage());
        }
        return guestResult;
    }

    @RequestMapping("getHealthCode.do")
    @ResponseBody
    public GuestResult getHealthCode(@RequestBody JSONObject param) {
        GuestResult guestResult = new GuestResult();
        try {
            HealthCode healthCode = new HealthCode();
            GuestModel guestModel = new GuestModel();
            guestModel.setName(param.getString("Name"));
            guestModel.setiDCode(param.getString("IDCode"));
            guestModel.setUrl(param.getString("Url"));
            guestModel.setKey(param.getString("Key"));
            return healthCode.getHealthCode(guestModel);
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @RequestMapping("pmsGetHealthCode.do")
    @ResponseBody
    public GuestResult pmsGetHealthCode(@RequestBody JSONObject param) {
        GuestResult guestResult = new GuestResult();
        try {
            HealthCode healthCode = new HealthCode();
            GuestModel guestModel = new GuestModel();
            guestModel.setName(param.getString("Name"));
            guestModel.setiDCode(param.getString("IDCode"));
            guestModel.setUrl(param.getString("Url"));
            guestModel.setKey(param.getString("Key"));
            guestResult = healthCode.getHealthCode(guestModel);
            guestResult.setIdCode(param.getString("IDCode"));
            guestResult.setGuestNo(param.getString("Name"));
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }

    @RequestMapping("/DoorController/visitorRegistrationRecordTraffic")
    @ResponseBody
    public GuestResult pmsAddWaitCheckinPersonInfo(@RequestBody OpenDoorRequest openDoorRequest) {
        GuestResult guestResult = new GuestResult();
        try {
            if (StringUtil.isEmpty(openDoorRequest.getFactory())) {
                guestResult.setResult(false);
                guestResult.setMsg(HOTEL_CONST.DATA_LIST_IS_NULL);
            }
            if (StringUtil.isEmpty(openDoorRequest.getRoomNo())) {
                guestResult.setResult(false);
                guestResult.setMsg(HOTEL_CONST.DATA_LIST_IS_NULL);
            }
            ResponseData responseData = smartLockService.openDoor(openDoorRequest);
            if (responseData.getCode() != 1) {
                guestResult.setResult(false);
                guestResult.setMsg(responseData.getMsg());
                return guestResult;
            }
            guestResult.setResult(true);
            guestResult.setMsg("开门成功");
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }


    @RequestMapping("/record/uploadRecord")
    @ResponseBody
    public JSONObject pmsAddWaitCheckinPersonInfo11(@RequestBody JSONObject param) {
        JSONObject result = new JSONObject();
        try {
            System.out.println(param.toString());
            result.put("deviceCode", "");
            result.put("resultCode", 200);
            boolean b = waitCheckinPersonImpl.addPersonInfo(param);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("deviceCode", "");
            result.put("resultCode", -1);
        }
        return result;
    }

    @RequestMapping("/device/heartbeat")
    @ResponseBody
    public JSONObject heartbeat(@RequestBody JSONObject param) {
        JSONObject result = new JSONObject();
        try {
            System.out.println(param.toString());
            result.put("deviceCode", "");
            result.put("updateState", 0);
            result.put("apkUrl", "http://192.168.2.11:8080/apk/1.12.apk");
        } catch (Exception e) {
            e.printStackTrace();
            result.put("deviceCode", "");
            result.put("resultCode", 200);
        }
        return result;
    }

    @RequestMapping("pmsGetBase64.do")
    @ResponseBody
    public ResponseData pmsGetBase64(@RequestBody JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            JSONObject res = new JSONObject();
            Object photo = param.get("photo");

            if (photo != null && !photo.toString().equals("")) {
                String image2Base64 = ImageUtils.image2Base64(photo.toString());
                res.put("photo", image2Base64);
            }
            Object cameraPhoto = param.get("cameraPhoto");
            if (cameraPhoto != null && !cameraPhoto.toString().equals("")) {
                String image2Base64 = ImageUtils.image2Base64(cameraPhoto.toString());
                res.put("cameraPhoto", image2Base64);
            }
            responseData.setData(res);
        } catch (Exception e) {
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }

    @RequestMapping("facePictur.do")
    @ResponseBody
    public ResponseData facePictur(@RequestBody JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            GuestModel guestModel = new GuestModel();
            Object cameraPhoto = param.get("cameraPhoto");
            if (cameraPhoto != null && !cameraPhoto.toString().equals("")) {
                guestModel.setCameraPhoto(cameraPhoto.toString());
            }
            Object hotelCode = param.get("hotelCode");
            if (hotelCode != null && !hotelCode.toString().equals("")) {
                guestModel.setHotelCode(hotelCode.toString());
            }
            TuYa tuYa = new TuYa();
            return tuYa.facePictur(guestModel);
        } catch (Exception e) {
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }


}
