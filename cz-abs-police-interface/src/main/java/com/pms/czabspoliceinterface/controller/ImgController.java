package com.pms.czabspoliceinterface.controller;


import com.pms.czpmsutils.FaceSetUtils;
import com.pms.czpmsutils.ResponseData;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/police/")
public class ImgController {


    @RequestMapping("imgCon.do")
    @ResponseBody
    public ResponseData pmsCheckout(@RequestBody JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        int type = param.getInt("type");
        String apiKey = param.getString("apiKey");
        String apiSecret = param.getString("apiSecret");
        JSONObject jsonObject = new JSONObject();
        switch (type){
            case 1:
                // 在人脸库添加人脸
                String img = param.getString("img");


                try {
                    jsonObject = FaceSetUtils.detect(apiKey,apiSecret,img,0);
                    Thread.sleep(1200);
                }catch (Exception e){
                    try {
                        Thread.sleep(1000);
                        jsonObject = FaceSetUtils.detect(apiKey,apiSecret,img,0);
                        Thread.sleep(1000);
                    }catch (Exception e1){
                    }
                }
                JSONArray faces = jsonObject.getJSONArray("faces");
                JSONObject jsonObject1 = faces.getJSONObject(0);
                String face_token = jsonObject1.getString("face_token");

                String outerId = param.getString("outerId");
                // 添加人脸
                FaceSetUtils.addface(apiKey,apiSecret,null,outerId,face_token);
                responseData.setData(face_token);
                responseData.setData1(11);
                break;
                // 删除人脸
            case 2:
                String outerId1 = param.getString("outerId");
                String faceToken = param.getString("faceToken");
                jsonObject = FaceSetUtils.removeface(apiKey,apiSecret,null,outerId1,faceToken);
                break;
            case 3:
                // 寻找人脸
                String imgStr = param.getString("img");
                String outerId2 = param.getString("outerId");
                jsonObject =  FaceSetUtils.search(apiKey,apiSecret,imgStr,null,outerId2,"0");
                break;
        }

        return responseData;
    }

}
