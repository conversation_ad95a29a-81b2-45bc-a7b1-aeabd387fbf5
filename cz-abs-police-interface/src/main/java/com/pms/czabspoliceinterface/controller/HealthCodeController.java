package com.pms.czabspoliceinterface.controller;

import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/DoorController")
public class HealthCodeController {
    @RequestMapping("visitorRegistrationRecordTraffic")
    @ResponseBody
    public GuestResult pmsAddWaitCheckinPersonInfo(@RequestBody JSONObject param) {
        GuestResult guestResult = new GuestResult();
        try {
            System.out.println(param.toString());
        } catch (Exception e) {
            e.printStackTrace();
            guestResult.setResult(false);
            guestResult.setMsg(e.getMessage());
        }
        return guestResult;
    }
}
