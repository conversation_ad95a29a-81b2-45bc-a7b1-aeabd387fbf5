package com.pms.czabspoliceinterface.bean;

import com.pms.czpmsutils.request.BaseRequest;
import com.pms.pmsorder.bean.PersonInfo;
import com.pms.pmsorder.bean.RegistPersonHealthCode;

public class WaitCheckinPersonInfoReqeust extends BaseRequest {
    private PersonInfo personInfo;

    private RegistPersonHealthCode registPersonHealthCode;

    public PersonInfo getPersonInfo() {
        return personInfo;
    }

    public void setPersonInfo(PersonInfo personInfo) {
        this.personInfo = personInfo;
    }

    public RegistPersonHealthCode getRegistPersonHealthCode() {
        return registPersonHealthCode;
    }

    public void setRegistPersonHealthCode(RegistPersonHealthCode registPersonHealthCode) {
        this.registPersonHealthCode = registPersonHealthCode;
    }
}
