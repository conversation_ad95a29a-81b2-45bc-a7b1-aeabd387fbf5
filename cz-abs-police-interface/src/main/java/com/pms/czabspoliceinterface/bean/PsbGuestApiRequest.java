package com.pms.czabspoliceinterface.bean;

public class PsbGuestApiRequest {
    private String sysId;//用来保证住房和退房信息是同一条记录，客户端需保证唯一性，生成规则建议带上时间戳，最大长度50

    private Integer sysType;//固定 11

    private String name;//姓名为空

    private String cardType = "11";//证件类型(11 身份证 99其他证件)

    private String cardNo;//证件号

    private String sex;//1男2女0未知

    private String birthDay;//出生日期 2018-08-25

    private String region;//行政区编码(GB/T2260-1999)

    private String nation;//民族(GB/T3304-1991)

    private String address;//详细地址

    private String roomNo;//房间号

    private String checkInTime;//入住时间 2019-08-23 12:00:00

    private String checkOutTime;//退房时间 2019-08-25 12:00:00

    private String cardImg;//证件照片Base64，建议图片大小为100KB内

    private String sceneImg;//现场照片Base64,建议图片大小为100KB内

    private Integer isUploadPsb;

    private String hotelSn;

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public Integer getSysType() {
        return sysType;
    }

    public void setSysType(Integer sysType) {
        this.sysType = sysType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCardType() {
        return cardType;
    }

    public void setCardType(String cardType) {
        this.cardType = cardType;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getBirthDay() {
        return birthDay;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getAddress() {
        return address;
    }

    public void setBirthDay(String birthDay) {
        this.birthDay = birthDay;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    public String getCheckInTime() {
        return checkInTime;
    }

    public void setCheckInTime(String checkInTime) {
        this.checkInTime = checkInTime;
    }

    public String getCheckOutTime() {
        return checkOutTime;
    }

    public void setCheckOutTime(String checkOutTime) {
        this.checkOutTime = checkOutTime;
    }

    public String getCardImg() {
        return cardImg;
    }

    public void setCardImg(String cardImg) {
        this.cardImg = cardImg;
    }

    public String getSceneImg() {
        return sceneImg;
    }

    public void setSceneImg(String sceneImg) {
        this.sceneImg = sceneImg;
    }

    public Integer getIsUploadPsb() {
        return isUploadPsb;
    }

    public void setIsUploadPsb(Integer isUploadPsb) {
        this.isUploadPsb = isUploadPsb;
    }

    public String getHotelSn() {
        return hotelSn;
    }

    public void setHotelSn(String hotelSn) {
        this.hotelSn = hotelSn;
    }
}
