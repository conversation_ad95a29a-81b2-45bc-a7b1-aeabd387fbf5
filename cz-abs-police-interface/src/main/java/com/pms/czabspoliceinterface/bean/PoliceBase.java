package com.pms.czabspoliceinterface.bean;

import cn.hutool.core.util.StrUtil;
import com.pms.czpmsutils.view.GuestModel;

public class PoliceBase {
    public String getSexCode(String sex) {
        if (StrUtil.isEmpty(sex)) {
            return "1";
        }
        return sex.equals("男") ? "1" : "2";
    }

    public static String getGender(String idNumber) {
        int gender = 0;
        if(idNumber.length() == 18){
            //如果身份证号18位，取身份证号倒数第二位
            char c = idNumber.charAt(idNumber.length() - 2);
            gender = Integer.parseInt(String.valueOf(c));
        }else{
            //如果身份证号15位，取身份证号最后一位
            char c = idNumber.charAt(idNumber.length() - 1);
            gender = Integer.parseInt(String.valueOf(c));
        }
        if(gender % 2 == 1){
            return "1";
        }else{
            return "2";
        }
    }

    public String getNationCode(String nation) {
        if (nation.equals(null) || nation == "")
            return "01";
        switch (nation) {
            case "汉":
                return "01";
            case "汉族":
                return "01";
            case "蒙古族":
                return "02";
            case "回族":
                return "03";
            case "藏族":
                return "04";
            case "维吾尔族":
                return "05";
            case "苗族":
                return "06";
            case "彝族":
                return "07";
            case "壮族":
                return "08";
            case "布依族":
                return "09";
            case "朝鲜族":
                return "10";
            case "满族":
                return "11";
            case "侗族":
                return "12";
            case "瑶族":
                return "13";
            case "白族":
                return "14";
            case "土家族":
                return "15";
            case "哈尼族":
                return "16";
            case "哈萨克族":
                return "17";
            case "傣族":
                return "18";
            case "黎族":
                return "19";
            case "傈僳族":
                return "20";
            case "佤族":
                return "21";
            case "畲族":
                return "22";
            case "高山族":
                return "23";
            case "拉祜族":
                return "24";
            case "水族":
                return "25";
            case "东乡族":
                return "26";
            case "纳西族":
                return "27";
            case "景颇族":
                return "28";
            case "柯尔克孜族":
                return "29";
            case "土族":
                return "30";
            case "达斡尔族":
                return "31";
            case "仫佬族":
                return "32";
            case "羌族":
                return "33";
            case "布朗族":
                return "34";
            case "撒拉族":
                return "35";
            case "毛南族":
                return "36";
            case "仡佬族":
                return "37";
            case "锡伯族":
                return "38";
            case "阿昌族":
                return "39";
            case "普米族":
                return "40";
            case "塔吉克族":
                return "41";
            case "怒族":
                return "42";
            case "乌孜别克族":
                return "43";
            case "俄罗斯族":
                return "44";
            case "鄂温克族":
                return "45";
            case "德昂族":
                return "46";
            case "保安族":
                return "47";
            case "裕固族":
                return "48";
            case "京族":
                return "49";
            case "塔塔尔族":
                return "50";
            case "独龙族":
                return "51";
            case "鄂伦春族":
                return "52";
            case "赫哲族":
                return "53";
            case "门巴族":
                return "54";
            case "珞巴族":
                return "55";
            case "基诺族":
                return "56";
            default:
                break;
        }
        return "01";
    }

    public String getNationECode(String nation) {
        if (nation.equals(null) || nation == "")
            return "HA";
        switch (nation) {
            case "汉":
                return "HA";
            case "汉族":
                return "HA";
            case "蒙古族":
                return "MG";
            case "回族":
                return "HU";
            case "藏族":
                return "ZA";
            case "维吾尔族":
                return "UG";
            case "苗族":
                return "MH";
            case "彝族":
                return "YI";
            case "壮族":
                return "ZH";
            case "布依族":
                return "BY";
            case "朝鲜族":
                return "CS";
            case "满族":
                return "MA";
            case "侗族":
                return "DO";
            case "瑶族":
                return "YA";
            case "白族":
                return "BA";
            case "土家族":
                return "TJ";
            case "哈尼族":
                return "HN";
            case "哈萨克族":
                return "KZ";
            case "傣族":
                return "DA";
            case "黎族":
                return "LI";
            case "傈僳族":
                return "LS";
            case "佤族":
                return "VA";
            case "畲族":
                return "SH";
            case "高山族":
                return "GS";
            case "拉祜族":
                return "LH";
            case "水族":
                return "SU";
            case "东乡族":
                return "DX";
            case "纳西族":
                return "NX";
            case "景颇族":
                return "JP";
            case "柯尔克孜族":
                return "KG";
            case "土族":
                return "TU";
            case "达斡尔族":
                return "DU";
            case "仫佬族":
                return "ML";
            case "羌族":
                return "QI";
            case "布朗族":
                return "BL";
            case "撒拉族":
                return "SL";
            case "毛南族":
                return "MN";
            case "仡佬族":
                return "GL";
            case "锡伯族":
                return "XB";
            case "阿昌族":
                return "AC";
            case "普米族":
                return "PM";
            case "塔吉克族":
                return "TA";
            case "怒族":
                return "NU";
            case "乌孜别克族":
                return "UZ";
            case "俄罗斯族":
                return "RS";
            case "鄂温克族":
                return "EW";
            case "德昂族":
                return "DE";
            case "保安族":
                return "BN";
            case "裕固族":
                return "YG";
            case "京族":
                return "GI";
            case "塔塔尔族":
                return "TT";
            case "独龙族":
                return "DR";
            case "鄂伦春族":
                return "OR";
            case "赫哲族":
                return "HZ";
            case "门巴族":
                return "MB";
            case "珞巴族":
                return "LB";
            case "基诺族":
                return "JN";
            default:
                break;
        }
        return "HA";
    }

    public String checkinParam(GuestModel guestModel){
        if (guestModel.getName() == null || guestModel.getName().equals("")){
            return "宾客姓名不能为空";
        }
        if (guestModel.getSex() == null || guestModel.getSex().equals("")){
            return "宾客性别不能为空";
        }
        if (guestModel.getRoomNo() == null || guestModel.getRoomNo().equals("")){
            return "宾客入住房号不能为空";
        }
        if (guestModel.getNation() == null || guestModel.getNation().equals("")){
            return "宾客民族信息不能为空";
        }
        if (guestModel.getAddress() == null || guestModel.getAddress().equals("")){
            return "宾客住址不能空";
        }
        if (guestModel.getiDCode() == null || guestModel.getiDCode().equals("")){
            return "宾客证件号不能空";
        }
        if (guestModel.getPhoto() == null || guestModel.getPhoto().equals("")){
            return "宾客证件照片不能空";
        }
        return null;
    }
}
