package com.pms.czabspoliceinterface.bean;

public class UploadRecordRequestEntity {
    private String deviceCode;
    //记录时间，唯一
    private Long recordTime;
    //记录类型
    // 1：人脸验证
    // 2：人脸抓拍
    // 3：刷身份证
    // 4：刷ic卡
    // 5：人证合一
    private int recordType;
    //人员id
    private String personId = "";
    //姓名
    private String name = "";
    //记录图片路径
    private String imageBase64 = "";
    //识别阈值
    private int confidence;
    //身份证信息(刷身份证和人证类型才有数据)
    private IdCardInfo idCardInfo;
    //二维码数据（二维码类型才有数据）
    private String qrCode = "";
    private int occlusion;
    /**
     * idCardInfo : null
     * guokangCode : null
     * temperature : 36.5
     * icCard : 123456
     * personType : 0
     */

    private float temperature;
    private String icCard;
    private int personType;

    //核酸记录 "0":未知 ,"1":未查询到记录 ,"20210913核酸阴性..."
    private String hsjl;
    //疫苗记录 0未知，1，未接种，2，接种未完成 3，接种完成
    private String ymjl;
    //行程记录
    private String xcjl;
    //身份证号
    private String idCardNo;

    public String getHsjl() {
        return hsjl;
    }

    public void setHsjl(String hsjl) {
        this.hsjl = hsjl;
    }

    public String getYmjl() {
        return ymjl;
    }

    public void setYmjl(String ymjl) {
        this.ymjl = ymjl;
    }

    public String getXcjl() {
        return xcjl;
    }

    public void setXcjl(String xcjl) {
        this.xcjl = xcjl;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public Long getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Long recordTime) {
        this.recordTime = recordTime;
    }

    public int getRecordType() {
        return recordType;
    }

    public void setRecordType(int recordType) {
        this.recordType = recordType;
    }

    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getImageBase64() {
        return imageBase64;
    }

    public void setImageBase64(String imageBase64) {
        this.imageBase64 = imageBase64;
    }

    public int getConfidence() {
        return confidence;
    }

    public void setConfidence(int confidence) {
        this.confidence = confidence;
    }

    public IdCardInfo getIdCardInfo() {
        return idCardInfo;
    }

    public void setIdCardInfo(IdCardInfo idCardInfo) {
        this.idCardInfo = idCardInfo;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    private GuokangCodeBean guokangCode;

    public GuokangCodeBean getGuokangCode() {
        return guokangCode;
    }

    public void setGuokangCode(GuokangCodeBean guokangCode) {
        this.guokangCode = guokangCode;
    }

    public float getTemperature() {
        return temperature;
    }

    public void setTemperature(float temperature) {
        this.temperature = temperature;
    }

    public String getIcCard() {
        return icCard;
    }

    public void setIcCard(String icCard) {
        this.icCard = icCard;
    }


    public int getPersonType() {
        return personType;
    }

    public void setPersonType(int personType) {
        this.personType = personType;
    }


    public static class GuokangCodeBean {
        /**
         * code : 0
         * cardName : 王**
         * cardId : **************1234
         * overCity :
         * status : 绿码
         */

        private int code;
        private String cardName;
        private String cardId;
        private String overCity;
        private String status;

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public String getCardName() {
            return cardName;
        }

        public void setCardName(String cardName) {
            this.cardName = cardName;
        }

        public String getCardId() {
            return cardId;
        }

        public void setCardId(String cardId) {
            this.cardId = cardId;
        }

        public String getOverCity() {
            return overCity;
        }

        public void setOverCity(String overCity) {
            this.overCity = overCity;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        @Override
        public String toString() {
            return "GuokangCodeBean{" +
                    "code=" + code +
                    ", cardName='" + cardName + '\'' +
                    ", cardId='" + cardId + '\'' +
                    ", overCity='" + overCity + '\'' +
                    ", status='" + status + '\'' +
                    '}';
        }
    }

    public static class IdCardInfo {
        /**
         * address : XX省XX
         * birth : 1988年08月08日
         * country : 中国
         * idCardNo : XXXXX
         * issuingAuthority : XX县公安局
         * name : 张三
         * nation : 汉
         * sex : 男
         * validityTime : 2008.01.01-2028.01.01
         * idCardPicBase64 : iVBORw0KGgouQmCC\n
         */

        private String address;
        private String birth;
        private String country;
        private String idCardNo;
        private String issuingAuthority;
        private String name;
        private String nation;
        private String sex;
        private String validityTime;
        private String idCardPicBase64;

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }

        public String getBirth() {
            return birth;
        }

        public void setBirth(String birth) {
            this.birth = birth;
        }

        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public String getIdCardNo() {
            return idCardNo;
        }

        public void setIdCardNo(String idCardNo) {
            this.idCardNo = idCardNo;
        }

        public String getIssuingAuthority() {
            return issuingAuthority;
        }

        public void setIssuingAuthority(String issuingAuthority) {
            this.issuingAuthority = issuingAuthority;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getNation() {
            return nation;
        }

        public void setNation(String nation) {
            this.nation = nation;
        }

        public String getSex() {
            return sex;
        }

        public void setSex(String sex) {
            this.sex = sex;
        }

        public String getValidityTime() {
            return validityTime;
        }

        public void setValidityTime(String validityTime) {
            this.validityTime = validityTime;
        }

        public String getIdCardPicBase64() {
            return idCardPicBase64;
        }

        public void setIdCardPicBase64(String idCardPicBase64) {
            this.idCardPicBase64 = idCardPicBase64;
        }

        @Override
        public String toString() {
            return "IdCardInfo{" +
                    "address='" + address + '\'' +
                    ", birth='" + birth + '\'' +
                    ", country='" + country + '\'' +
                    ", idCardNo='" + idCardNo + '\'' +
                    ", issuingAuthority='" + issuingAuthority + '\'' +
                    ", name='" + name + '\'' +
                    ", nation='" + nation + '\'' +
                    ", sex='" + sex + '\'' +
                    ", validityTime='" + validityTime + '\'' +
                    '}';
        }
    }

    public int getOcclusion() {
        return occlusion;
    }

    public void setOcclusion(int occlusion) {
        this.occlusion = occlusion;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    @Override
    public String toString() {
        return "UploadRecordRequestEntity{" +
                "deviceCode='" + deviceCode + '\'' +
                ", recordTime=" + recordTime +
                ", recordType=" + recordType +
                ", personId='" + personId + '\'' +
                ", name='" + name + '\'' +
                ", confidence=" + confidence +
                ", idCardInfo=" + idCardInfo +
                ", qrCode='" + qrCode + '\'' +
                ", occlusion=" + occlusion +
                ", temperature=" + temperature +
                ", icCard='" + icCard + '\'' +
                ", personType=" + personType +
                ", hsjl='" + hsjl + '\'' +
                ", ymjl='" + ymjl + '\'' +
                ", xcjl='" + xcjl + '\'' +
                ", idCardNo='" + idCardNo + '\'' +
                ", guokangCode=" + guokangCode +
                '}';
    }
}
