package com.pms.czabspoliceinterface.bean;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.pms.czpmsutils.HotelUtils;

import java.util.Comparator;
import java.util.Date;

public class ApiRequest<T> {
    /**
     * 请求时间 示例 20191021120010
     */
    private String timestamp = HotelUtils.dateToNoLineStrYmdhms(new Date());

    /**
     * 版本号 固定1.0.1
     */
    private String version = "1.0";

    /**
     * MD5加密串
     */
    private String sign;

    /**
     * 信息参数主体对象
     */
    private T data;

    public String toSign(String apiKey) {
        JSONObject param = JSON.parseObject(JSON.toJSONString(this,
                SerializerFeature.DisableCircularReferenceDetect));
        StringBuilder sb = new StringBuilder();

        //参数主体data排序后拼接
        JSONObject data = param.getJSONObject("data");
        if (data != null) {
            data.keySet().stream().sorted(Comparator.naturalOrder()).forEach(key -> {
                if (isEmpty(data.getString(key))) return;
                sb.append(key).append("=").append(data.getString(key)).append("&");
            });
            if (sb.length() > 0) param.put("data", sb.deleteCharAt(sb.length() - 1).toString());
            sb.setLength(0);
        }

        //请求参数排序后拼接
        param.keySet().stream().sorted(Comparator.naturalOrder()).forEach(key -> {
            if (isEmpty(param.getString(key)) ||
                    "sign".equals(key) || "apiKey".equals(key)) return;
            sb.append(key).append("=").append(param.getString(key)).append("&");
        });
        sb.append(apiKey);//TODO 拼接加密密钥
        return HotelUtils.encrypt32(sb.toString()).toLowerCase();


    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public static boolean isEmpty(String str) {
        return ((str == null) || (str.length() == 0));
    }
}
