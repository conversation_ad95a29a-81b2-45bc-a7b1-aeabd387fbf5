
package com.pms.czabspoliceinterface.lock.boonlive;

import javax.xml.bind.annotation.*;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CheckIn2Result" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="errorMsg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="checkInID" type="{http://www.w3.org/2001/XMLSchema}long"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "checkIn2Result",
    "errorMsg",
    "checkInID"
})
@XmlRootElement(name = "CheckIn2Response")
public class CheckIn2Response {

    @XmlElement(name = "CheckIn2Result")
    protected boolean checkIn2Result;
    protected String errorMsg;
    protected long checkInID;

    /**
     * 获取checkIn2Result属性的值。
     * 
     */
    public boolean isCheckIn2Result() {
        return checkIn2Result;
    }

    /**
     * 设置checkIn2Result属性的值。
     * 
     */
    public void setCheckIn2Result(boolean value) {
        this.checkIn2Result = value;
    }

    /**
     * 获取errorMsg属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getErrorMsg() {
        return errorMsg;
    }

    /**
     * 设置errorMsg属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setErrorMsg(String value) {
        this.errorMsg = value;
    }

    /**
     * 获取checkInID属性的值。
     * 
     */
    public long getCheckInID() {
        return checkInID;
    }

    /**
     * 设置checkInID属性的值。
     * 
     */
    public void setCheckInID(long value) {
        this.checkInID = value;
    }

}
