package com.pms.czabspoliceinterface.lock.factory;

import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.SmartLockRequest;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class YLock implements IPmsLock {
    private static final String NONCESTR = "1522115166482";
    public static String getSignParamStr(Map<String, String> param, String APPKEY) {
        List<String> paramList = new ArrayList<>();
        for (Map.Entry<String, String> entry : param.entrySet()) {
            paramList.add(entry.getKey() + "=" + entry.getValue());
        }
        paramList.sort(String::compareTo);
        String paramStr = StringUtils.join(paramList, "&");
        String sign = MD5Util.MD5EncodeUTF8(paramStr + "&APPKEY=" + APPKEY).toUpperCase();
        return paramStr + "&SIGN=" + sign;
    }


    /**
     * 请求方式：
     * POST
     * 参数：
     * 参数名	    必选	类型	说明
     * APPID	    是	    string	APPID
     * AT	        是	    string	当前时间的时间戳（毫秒为单位）
     * NONCESTR	    是	    string	随机码（任意16进制的随机数）
     * PASSWORD	    是	    string	用户密码
     * PHONEIP	    否	    string	登录ip
     * PHONEID	    否	    string	登录信息
     * USERNAME	    是	    string	用户帐号（二次开发手机号）
     * SIGN	        是	    string	签名
     *
     * @return FB55AC9329624040A7ADD60FF7CFDA88
     */
    public static String login(String url,String code ,String key,String userName ,String userPassword) throws Exception {
        HashMap<String, String> map = new HashMap<>();
        map.put("APPID", code);
        map.put("AT", String.format("%d", System.currentTimeMillis()));
        map.put("NONCESTR", NONCESTR);
        map.put("PASSWORD", userPassword);
        map.put("USERNAME", userName);
        String paramStr = getSignParamStr(map, key);
        String traceId = UUID.randomUUID().toString();
        System.out.println("url:" + url + "/dms/app/dmsLogin?" + "param:" + paramStr);
        String logInRt = HttpClientPool.processPostJson(url + "/dms/app/dmsLogin?" + paramStr, "", traceId);
        //log.info("TraceId={},login result={}", traceId, logInRt);
        Map map1 = GsonUtil.json2Bean(logInRt, Map.class);
        double result = (double) map1.get("result");
        if (result != 0) {
            //log.error("login error code = {}, msg={}", result, map.get("msg"));
            throw new Exception("login field");
        }
        return (String) map1.get("token");
    }

    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        JSONObject jsonObject = JSONObject.fromObject(smartLockRequest);
        System.out.println("系统请求参数:" + jsonObject.toString());
        System.out.println("系统配置参数:" + param);
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String url = param.getString("575");
            String key = param.getString("578");
            String code = param.getString("579");
            String sendPhone = param.getString("653");
            String userName = param.getString("655");
            String userPassword = param.getString("656");
            String token = this.login(url,code,key,userName,userPassword);
            HashMap<String, String> map = new HashMap<>();
            map.put("APPID", code);
            map.put("AT", String.format("%d", System.currentTimeMillis()));
            map.put("NONCESTR", NONCESTR);
            map.put("PASSWORD", userPassword);
            map.put("USERNAME", userName);
            map.put("TOKEN", token);
            map.put("CARDTYPE", "23");
            map.put("DEVICE_ID", smartLockRequest.getLockNo());
            map.put("ENDDATE", HotelUtils.getStringToday(smartLockRequest.getCheckoutTime()));
            map.put("STARTDATE", HotelUtils.getStringToday(smartLockRequest.getCheckinTime()));
            map.put("SAVEFLAGE","1");
            map.put("DELETEPSWFLAG","1");
            String phone = smartLockRequest.getPeoples().get(0).getPhone();
            if (phone.equals("") || phone == null){
                throw new Exception("手机号为空");
            }
            map.put("KEYUSERID",phone);
            String paramStr = getSignParamStr(map, key);
            String traceId = UUID.randomUUID().toString();
            System.out.println("url:" + url + "/dms/app/getDeviceVistCode?" + "param:" + paramStr);
            String logInRt = HttpClientPool.processPostJson(url + "/dms/app/getDeviceVistCode?" + paramStr, "", traceId);
            System.out.println("getPasswordResult:" + logInRt);
            //log.info("TraceId={},login result={}", traceId, logInRt);
            Map map1 = GsonUtil.json2Bean(logInRt, Map.class);
            Double result =  (double) map1.get("result");
            Integer i = result.intValue();
            if (!i.equals(0) && !i.equals(147)) {
                //log.error("getDeviceVistCode error code = {}, msg={}", result, map.get("msg"));
                throw new Exception("getDeviceVistCode field");
            }
            String passWord = (String) map1.get("data");

            map = new HashMap<>();
            map.put("APPID", code);
            map.put("NONCESTR", NONCESTR);
            map.put("PASSWORD", userPassword);
            map.put("USERNAME", userName);
            map.put("AT", String.format("%d", System.currentTimeMillis()));
            map.put("TOKEN", token);
            map.put("CARDTYPE", "23");
            map.put("ENDDATE", HotelUtils.getStringToday(smartLockRequest.getCheckoutTime()));
            map.put("KEYLOCKNAME","101");
            map.put("OPENDOORCODE",passWord);
            map.put("RECEIVEPHONE",phone);
            map.put("SENDPHONE",sendPhone);
            map.put("SMSTYPE","0");
            map.put("STARTDATE", HotelUtils.getStringToday(smartLockRequest.getCheckinTime()));
            paramStr = getSignParamStr(map, key);
            traceId = UUID.randomUUID().toString();
            System.out.println("url:" + url + "/dms/app/sendCheckInMsg?" + "param:" + paramStr);
            logInRt = HttpClientPool.processPostJson(url + "/dms/app/sendCheckInMsg?" + paramStr, "", traceId);
            System.out.println("sendMessage:" + logInRt);
            map1 = GsonUtil.json2Bean(logInRt, Map.class);
            result = (double) map1.get("result");
            if (result != 0) {
                throw new Exception("getDeviceVistCode field");
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            System.out.println("结账退房");
        }catch (Exception e){

        }
        return responseData;
    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        return this.checkin(smartLockRequest,param);
    }

    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }
}
