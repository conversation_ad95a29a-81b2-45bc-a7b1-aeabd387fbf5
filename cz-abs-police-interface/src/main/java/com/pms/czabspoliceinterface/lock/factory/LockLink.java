package com.pms.czabspoliceinterface.lock.factory;

import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.MD5Util;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.request.SmartLockRequest;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.czpmsutils.view.GuestResult;
import net.sf.json.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.IOException;
import java.net.URL;
import java.util.*;

public class LockLink implements IPmsLock {
    public GuestResult checkin(GuestModel guestModel) {
        GuestResult guestResult = new GuestResult();
        SmartLockRequest smartLockRequest = new SmartLockRequest();
        smartLockRequest.setRegistId(121213);
        smartLockRequest.setCheckinTime(new Date());
        smartLockRequest.setCheckoutTime(HotelUtils.addDayGetNewDate(new Date(), 1));
        smartLockRequest.setRoomNum("8806");
        List<SmartLockRequest.RegistPerson> personList = new ArrayList<>();
        SmartLockRequest.RegistPerson person = new SmartLockRequest.RegistPerson();
        person.setPhone("17724498681");
        personList.add(person);
        smartLockRequest.setPeoples(personList);
        JSONObject param = new JSONObject();
        param.put("575", "https://api.lock.link");
        param.put("578", "FyusfCumKKdgTbJk");
        param.put("579", "urZJj7gY3sfUajrVbS");
        param.put("655", "2d74c6f2-ee47-4b3b-86b1-dba7499927fe");
        LockLink lockLink = new LockLink();
        ResponseData checkin = lockLink.checkin(smartLockRequest, param);
        if (checkin.getCode() != 1) {
            guestResult.setResult(false);
            return guestResult;
        }
        guestResult.setResult(true);
        guestResult.setGuestNo(checkin.getData().toString());
        return guestResult;
    }

    public static String getSignStr(MultiValueMap<String, Object> map, String APP_SECRET) {
        List<String> paramNames = new ArrayList<>(map.keySet());
        Collections.sort(paramNames);
        StringBuilder signStr = new StringBuilder();
        for (String paramName : paramNames) {
            signStr.append(paramName).append("=").append(map.get(paramName).get(0)).append("&");
        }
        signStr.append("key=" + APP_SECRET);
        System.out.println("签名前:  " + signStr);
        String signResult = MD5Util.MD5EncodeUTF8(signStr.toString());
        System.out.println("签名后:  " + signResult);
        return signResult;
    }

    public static String post(String url, String appId, String params, JSONObject param) throws Exception {
        String key = param.getString("578");
        String code = param.getString("579");

        long times = new Date().getTime() / 1000;
        String nonceStr = UUID.randomUUID().toString();

        MultiValueMap requestMap = new LinkedMultiValueMap();
        requestMap.add("AppId", key);
        requestMap.add("Timestamp", times);
        requestMap.add("NonceStr", nonceStr);
        requestMap.add("Mode", "normal");
        String signStr = getSignStr(requestMap, code);
        System.out.println(signStr);

        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);// 创建httpPost
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("AppId", appId);
        httpPost.setHeader("Timestamp", String.valueOf(times));
        httpPost.setHeader("Mode", "normal");
        httpPost.setHeader("NonceStr", nonceStr);
        httpPost.setHeader("Sign", signStr);

        String charSet = "UTF-8";
        StringEntity entity = new StringEntity(params, charSet);
        httpPost.setEntity(entity);
        CloseableHttpResponse response = null;

        try {

            response = httpclient.execute(httpPost);
            StatusLine status = response.getStatusLine();
            int state = status.getStatusCode();
            if (state == HttpStatus.SC_OK) {
                HttpEntity responseEntity = response.getEntity();
                String jsonString = EntityUtils.toString(responseEntity);
                return jsonString;
            } else {
            }
        } finally {
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            try {
                httpclient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            URL url = new URL(param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_URL));
            String key = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_KEY);
            String code = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_CODE);
            String userName = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT);//department_uuid
            JSONObject data = new JSONObject();
            data.put("department_uuid", userName);
            data.put("event_type", "checkin");
            data.put("mobile", smartLockRequest.getPeoples().get(0).getPhone());
            data.put("room_no", smartLockRequest.getRoomNum());
            data.put("order_id", smartLockRequest.getRegistId());
            data.put("arr_time", smartLockRequest.getCheckinTime().getTime() / 1000);
            data.put("dep_time", smartLockRequest.getCheckoutTime().getTime() / 1000);
            System.out.println(data.toString());
            String res = post(url + "/apiv3/pms_events", key, data.toString(), param);
            System.out.println(res);
            JSONObject resData = JSONObject.fromObject(res);
            if (resData.getInt("errcode") != 0) {
                throw new Exception("获取二维码失败:" + res);
            }
            String qrCode = resData.getJSONObject("data").getString("qrcode").replace("\\u0026", "&");
            System.out.println(qrCode);
            responseData.setData(qrCode);
        } catch (Exception e) {
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }
}
