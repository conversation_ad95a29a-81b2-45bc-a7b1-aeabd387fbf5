
package com.pms.czabspoliceinterface.lock.boonlive;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.ws.Holder;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "blwwsSoap", targetNamespace = "http://www.blw.com/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface BlwwsSoap {


    /**
     * 开房<br/>key：验证码（我方提供），code：编码（我方提供），roomNumber：房号，checkInDate：入住日期，xmlString：客人信息，errorMsg：返回错误信息，phoneNumber：手机号码（多个以英文逗号,隔开），idNumber：身份证号（多个以英文逗号,隔开）：获取微信登录验证码
     * 
     * @param checkInResult
     * @param code
     * @param roomNumber
     * @param phoneNumber
     * @param xmlString
     * @param idNumber
     * @param checkInDate
     * @param key
     * @param errorMsg
     */
    @WebMethod(operationName = "CheckIn", action = "http://www.blw.com/CheckIn")
    @RequestWrapper(localName = "CheckIn", targetNamespace = "http://www.blw.com/", className = "com.pms.czabspoliceinterface.lock.boonlive.CheckIn")
    @ResponseWrapper(localName = "CheckInResponse", targetNamespace = "http://www.blw.com/", className = "com.pms.czabspoliceinterface.lock.boonlive.CheckInResponse")
    public void checkIn(
        @WebParam(name = "key", targetNamespace = "http://www.blw.com/")
        String key,
        @WebParam(name = "code", targetNamespace = "http://www.blw.com/")
        String code,
        @WebParam(name = "roomNumber", targetNamespace = "http://www.blw.com/")
        String roomNumber,
        @WebParam(name = "checkInDate", targetNamespace = "http://www.blw.com/")
        XMLGregorianCalendar checkInDate,
        @WebParam(name = "xmlString", targetNamespace = "http://www.blw.com/")
        String xmlString,
        @WebParam(name = "errorMsg", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.INOUT)
        Holder<String> errorMsg,
        @WebParam(name = "phoneNumber", targetNamespace = "http://www.blw.com/")
        String phoneNumber,
        @WebParam(name = "idNumber", targetNamespace = "http://www.blw.com/")
        String idNumber,
        @WebParam(name = "CheckInResult", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.OUT)
        Holder<Boolean> checkInResult);

    /**
     * 开房<br/>key：验证码（我方提供），code：编码（我方提供），roomNumber：房号，checkInDate：入住日期，xmlString：客人信息，errorMsg：返回错误信息，checkInID：返回入住记录ID，phoneNumber：手机号码（多个以英文逗号,隔开），idNumber：身份证号（多个以英文逗号,隔开）：获取微信登录验证码
     * 
     * @param code
     * @param roomNumber
     * @param phoneNumber
     * @param checkInID
     * @param xmlString
     * @param checkIn2Result
     * @param idNumber
     * @param checkInDate
     * @param key
     * @param errorMsg
     */
    @WebMethod(operationName = "CheckIn2", action = "http://www.blw.com/CheckIn2")
    @RequestWrapper(localName = "CheckIn2", targetNamespace = "http://www.blw.com/", className = "com.pms.czabspoliceinterface.lock.boonlive.CheckIn2")
    @ResponseWrapper(localName = "CheckIn2Response", targetNamespace = "http://www.blw.com/", className = "com.pms.czabspoliceinterface.lock.boonlive.CheckIn2Response")
    public void checkIn2(
        @WebParam(name = "key", targetNamespace = "http://www.blw.com/")
        String key,
        @WebParam(name = "code", targetNamespace = "http://www.blw.com/")
        String code,
        @WebParam(name = "roomNumber", targetNamespace = "http://www.blw.com/")
        String roomNumber,
        @WebParam(name = "checkInDate", targetNamespace = "http://www.blw.com/")
        XMLGregorianCalendar checkInDate,
        @WebParam(name = "xmlString", targetNamespace = "http://www.blw.com/")
        String xmlString,
        @WebParam(name = "errorMsg", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.INOUT)
        Holder<String> errorMsg,
        @WebParam(name = "checkInID", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.INOUT)
        Holder<Long> checkInID,
        @WebParam(name = "phoneNumber", targetNamespace = "http://www.blw.com/")
        String phoneNumber,
        @WebParam(name = "idNumber", targetNamespace = "http://www.blw.com/")
        String idNumber,
        @WebParam(name = "CheckIn2Result", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.OUT)
        Holder<Boolean> checkIn2Result);

    /**
     * 上传入住人信息<br/>key：验证码（我方提供），code：编码（我方提供），checkInID：入住记录ID，idType：证件类型(0身份证，1护照，2军官证，3其他)，idCard：证件号码，name：姓名，sex：性别(0女，1男，2其他)，birthday：出生日期(1999-01-01)，photoUrl：图片路径（与photo二选一），photo：图片（二进制），errorMsg：错误信息
     * 
     * @param birthday
     * @param photoUrl
     * @param code
     * @param idType
     * @param checkInID
     * @param idCard
     * @param sex
     * @param name
     * @param photo
     * @param key
     * @param errorMsg
     * @param uploadPhotoResult
     */
    @WebMethod(operationName = "UploadPhoto", action = "http://www.blw.com/UploadPhoto")
    @RequestWrapper(localName = "UploadPhoto", targetNamespace = "http://www.blw.com/", className = "com.pms.czabspoliceinterface.lock.boonlive.UploadPhoto")
    @ResponseWrapper(localName = "UploadPhotoResponse", targetNamespace = "http://www.blw.com/", className = "com.pms.czabspoliceinterface.lock.boonlive.UploadPhotoResponse")
    public void uploadPhoto(
        @WebParam(name = "key", targetNamespace = "http://www.blw.com/")
        String key,
        @WebParam(name = "code", targetNamespace = "http://www.blw.com/")
        String code,
        @WebParam(name = "checkInID", targetNamespace = "http://www.blw.com/")
        long checkInID,
        @WebParam(name = "idType", targetNamespace = "http://www.blw.com/")
        int idType,
        @WebParam(name = "idCard", targetNamespace = "http://www.blw.com/")
        String idCard,
        @WebParam(name = "name", targetNamespace = "http://www.blw.com/")
        String name,
        @WebParam(name = "sex", targetNamespace = "http://www.blw.com/")
        int sex,
        @WebParam(name = "birthday", targetNamespace = "http://www.blw.com/")
        String birthday,
        @WebParam(name = "photoUrl", targetNamespace = "http://www.blw.com/")
        String photoUrl,
        @WebParam(name = "photo", targetNamespace = "http://www.blw.com/")
        byte[] photo,
        @WebParam(name = "errorMsg", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.INOUT)
        Holder<String> errorMsg,
        @WebParam(name = "UploadPhotoResult", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.OUT)
        Holder<Boolean> uploadPhotoResult);

    /**
     * 变更手机号<br/>key：验证码（我方提供），code：编码（我方提供），roomNumber：房号，errorMsg：错误信息，phoneNumber：手机号码（多个以英文逗号,隔开），idNumber：身份证号：获取验证码
     * 
     * @param code
     * @param roomNumber
     * @param phoneNumber
     * @param changePhoneNumberResult
     * @param idNumber
     * @param key
     * @param errorMsg
     */
    @WebMethod(operationName = "ChangePhoneNumber", action = "http://www.blw.com/ChangePhoneNumber")
    @RequestWrapper(localName = "ChangePhoneNumber", targetNamespace = "http://www.blw.com/", className = "com.pms.czabspoliceinterface.lock.boonlive.ChangePhoneNumber")
    @ResponseWrapper(localName = "ChangePhoneNumberResponse", targetNamespace = "http://www.blw.com/", className = "com.pms.czabspoliceinterface.lock.boonlive.ChangePhoneNumberResponse")
    public void changePhoneNumber(
        @WebParam(name = "key", targetNamespace = "http://www.blw.com/")
        String key,
        @WebParam(name = "code", targetNamespace = "http://www.blw.com/")
        String code,
        @WebParam(name = "roomNumber", targetNamespace = "http://www.blw.com/")
        String roomNumber,
        @WebParam(name = "phoneNumber", targetNamespace = "http://www.blw.com/")
        String phoneNumber,
        @WebParam(name = "idNumber", targetNamespace = "http://www.blw.com/")
        String idNumber,
        @WebParam(name = "errorMsg", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.INOUT)
        Holder<String> errorMsg,
        @WebParam(name = "ChangePhoneNumberResult", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.OUT)
        Holder<Boolean> changePhoneNumberResult);

    /**
     * 退房<br/>key：验证码（我方提供），code：编码（我方提供），roomNumber：房号，checkOutDate：退房日期，errorMsg：错误信息
     * 
     * @param code
     * @param roomNumber
     * @param checkOutDate
     * @param checkOutResult
     * @param key
     * @param errorMsg
     */
    @WebMethod(operationName = "CheckOut", action = "http://www.blw.com/CheckOut")
    @RequestWrapper(localName = "CheckOut", targetNamespace = "http://www.blw.com/", className = "com.pms.czabspoliceinterface.lock.boonlive.CheckOut")
    @ResponseWrapper(localName = "CheckOutResponse", targetNamespace = "http://www.blw.com/", className = "com.pms.czabspoliceinterface.lock.boonlive.CheckOutResponse")
    public void checkOut(
        @WebParam(name = "key", targetNamespace = "http://www.blw.com/")
        String key,
        @WebParam(name = "code", targetNamespace = "http://www.blw.com/")
        String code,
        @WebParam(name = "roomNumber", targetNamespace = "http://www.blw.com/")
        String roomNumber,
        @WebParam(name = "checkOutDate", targetNamespace = "http://www.blw.com/")
        XMLGregorianCalendar checkOutDate,
        @WebParam(name = "errorMsg", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.INOUT)
        Holder<String> errorMsg,
        @WebParam(name = "CheckOutResult", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.OUT)
        Holder<Boolean> checkOutResult);

    /**
     * 待租<br/>key：验证码（我方提供），code：编码（我方提供），roomNumber：房号，rentDate：变更待租日期，errorMsg：错误信息
     * 
     * @param code
     * @param roomNumber
     * @param rentDate
     * @param rentRoomResult
     * @param key
     * @param errorMsg
     */
    @WebMethod(operationName = "RentRoom", action = "http://www.blw.com/RentRoom")
    @RequestWrapper(localName = "RentRoom", targetNamespace = "http://www.blw.com/", className = "com.pms.czabspoliceinterface.lock.boonlive.RentRoom")
    @ResponseWrapper(localName = "RentRoomResponse", targetNamespace = "http://www.blw.com/", className = "com.pms.czabspoliceinterface.lock.boonlive.RentRoomResponse")
    public void rentRoom(
        @WebParam(name = "key", targetNamespace = "http://www.blw.com/")
        String key,
        @WebParam(name = "code", targetNamespace = "http://www.blw.com/")
        String code,
        @WebParam(name = "roomNumber", targetNamespace = "http://www.blw.com/")
        String roomNumber,
        @WebParam(name = "rentDate", targetNamespace = "http://www.blw.com/")
        XMLGregorianCalendar rentDate,
        @WebParam(name = "errorMsg", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.INOUT)
        Holder<String> errorMsg,
        @WebParam(name = "RentRoomResult", targetNamespace = "http://www.blw.com/", mode = WebParam.Mode.OUT)
        Holder<Boolean> rentRoomResult);

}
