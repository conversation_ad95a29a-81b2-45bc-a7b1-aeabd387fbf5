package com.pms.czabspoliceinterface.lock.factory;

import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.SmartLockRequest;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.*;

/**
 * 海康门禁的相关业务实现
 */
public class HaiKang implements IPmsLock {
    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            //单个人员进行下发  /api/acps/v1/authDownload/task/simpleDownload
            String url = param.getString("575");
            String key = param.getString("578");
            String code = param.getString("579");
            /**
             * STEP1：设置平台参数，根据实际情况,设置host appkey appsecret 三个参数.
             */
            // 平台的ip端口
            ArtemisConfig.host = url;
            // 密钥appkey
            ArtemisConfig.appKey = key;
            // 密钥appSecret
            ArtemisConfig.appSecret = code;
            /**
             * STEP2：设置OpenAPI接口的上下文
             */
            final String ARTEMIS_PATH = "/artemis";
            //查询通道状态
            final String previewURLsApi = ARTEMIS_PATH + "/api/acs/v1/door/states";

            /**
             * STEP3：设置接口的URI地址
             */
//            final String previewURLsApi = ARTEMIS_PATH + "/api/acps/v1/authDownload/task/simpleDownload";
            Map<String, String> path = new HashMap<String, String>(2) {
                {
                    put("https://", previewURLsApi);//根据现场环境部署确认是http还是https
                }
            };


            /**
             * STEP4：设置参数提交方式
             */
            String contentType = "application/json";
            String result = ArtemisHttpUtil.doPostStringArtemis(path, new JSONObject().toString(), null, null, contentType, null);// post请求application/json类型参数
            System.out.println(result);


            //查询门禁点列表v2
            final String searchApi = ARTEMIS_PATH + "/api/resource/v2/door/search";

            JSONObject searchData = new JSONObject();
            searchData.put("pageNo", 1);
            searchData.put("pageSize", 1000);

            /**
             * STEP3：设置接口的URI地址
             */
//            final String previewURLsApi = ARTEMIS_PATH + "/api/acps/v1/authDownload/task/simpleDownload";
            Map<String, String> searchPath = new HashMap<String, String>(2) {
                {
                    put("https://", searchApi);//根据现场环境部署确认是http还是https
                }
            };
            result = ArtemisHttpUtil.doPostStringArtemis(path, searchData.toString(), null, null, contentType, null);// post请求application/json类型参数
            System.out.println(result);
            JSONObject resultData = JSONObject.fromObject(result);


            //查询通道状态 /api/acs/v1/door/doControl
            final String openDaoApi = ARTEMIS_PATH + "/api/acs/v1/door/doControl";
            Map<String, String> openDaoPath = new HashMap<String, String>(2) {
                {
                    put("https://", openDaoApi);//根据现场环境部署确认是http还是https
                }
            };
            JSONObject openData = new JSONObject();
            List<String> doorIndexCodes = new ArrayList<>();
            for (int i = 0; i < resultData.getJSONObject("data").getJSONArray("authDoorList").size(); i++) {
                doorIndexCodes.add(resultData.getJSONObject("data").getJSONArray("authDoorList").getJSONObject(i).getString("doorIndexCode"));
            }
            openData.put("doorIndexCodes", doorIndexCodes);
            openData.put("controlType", 2);

            result = ArtemisHttpUtil.doPostStringArtemis(openDaoPath, openData.toString(), null, null, contentType, null);// post请求application/json类型参数
            System.out.println(result);



            //查询控制事件 /api/acs/v2/door/events
            final String eventsApi = ARTEMIS_PATH + "/api/acs/v2/door/events";

            Map<String, String> eventsPath = new HashMap<String, String>(2) {
                {
                    put("https://", eventsApi);//根据现场环境部署确认是http还是https
                }
            };

            JSONObject eventsData = new JSONObject();
            eventsData.put("pageNo", 1);
            eventsData.put("pageSize", 1000);
            result = ArtemisHttpUtil.doPostStringArtemis(eventsPath, eventsData.toString(), null, null, contentType, null);// post请求application/json类型参数
            System.out.println(result);



            JSONObject postData = new JSONObject();
            JSONObject resourceInfo = new JSONObject();//资源设备对象信息
            resourceInfo.put("resourceIndexCode", doorIndexCodes.get(0));//资源的唯一标识
            resourceInfo.put("resourceType", "door");//资源类型，参考附录A.50 设备通道类型
//            resourceInfo.put("channelNos", "");//资源通道号
            postData.put("resourceInfo", resourceInfo);
            JSONObject personInfo = new JSONObject();
            personInfo.put("personId", UUID.randomUUID().toString().replace("-" ,""));//人员Id，系统内人员从获取人员列表v2 接口获取返回参数中personId，系统外人员，人员Id由调用方指定，作为人员唯一标志，最大长度为64，包含数字、字母（小写）和中划线（-）
            personInfo.put("operatorType", "0");//操作类型，0新增；1修改；2删除

            personInfo.put("startTime", HotelUtils.getISO8601Timestamp(new Date()));//
            personInfo.put("endTime", HotelUtils.getISO8601Timestamp(HotelUtils.addDayGetNewDate(new Date() ,1)));

            personInfo.put("startTime", "2022-08-22T17:30:08.000+08:00");//
            personInfo.put("endTime", "2022-09-22T17:30:08.000+08:00");

            personInfo.put("personType", "2");
            personInfo.put("name", "曹小明");

            //开信息
            JSONObject cardInfo = new JSONObject();
            cardInfo.put("card", "3422211");//卡号
            cardInfo.put("status", 0);//卡号
            cardInfo.put("cardType", "1");//卡类型，为空时默认正常普通卡，1普通卡 2残疾人卡 3黑名单卡 4巡查卡 5胁迫卡 6超级卡
            JSONArray cards = new JSONArray();
            cards.add(cardInfo);
            personInfo.put("cards", cards);
            JSONObject faceInfo = new JSONObject();
            faceInfo.put("card", "3422211");

            JSONObject faceImageData = new JSONObject();
            faceImageData.put("f1","https://hotel-file-1258829933.cos.ap-shanghai.myqcloud.com/2126/room-type/81d767150c68499b8103f8b4c263ccb4.jpg");
            faceInfo.put("data", faceImageData);//人脸数据，Key：为人脸编号 Value：为人脸图片https下载的URL，图片要求浏览器可以直接打开，不支持重定向  f1": "http://10.40.239.69:8080/babel/images/100.jpg


            personInfo.put("face", faceInfo);
            postData.put("personInfo", personInfo);

            postData.put("taskType",5);


            System.out.println(postData.toString());

            final String simpleDownloadApi = ARTEMIS_PATH + "/api/acps/v1/authDownload/task/simpleDownload";

            /**
             * STEP3：设置接口的URI地址
             */
//            final String previewURLsApi = ARTEMIS_PATH + "/api/acps/v1/authDownload/task/simpleDownload";
            Map<String, String> simpleDownloadPath = new HashMap<String, String>(2) {
                {
                    put("https://", simpleDownloadApi);//根据现场环境部署确认是http还是https
                }
            };

            System.out.println("下发参数:" + postData.toString());
            /**
             * STEP6：调用接口
             */
            result = ArtemisHttpUtil.doPostStringArtemis(simpleDownloadPath, postData.toString(), null, null, contentType, null);// post请求application/json类型参数
            System.out.println(result);

            return responseData;
        } catch (Exception e) {
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            //单个人员进行下发  /api/acps/v1/authDownload/task/simpleDownload
            String url = param.getString("575");
            String key = param.getString("578");
            String code = param.getString("579");
            /**
             * STEP1：设置平台参数，根据实际情况,设置host appkey appsecret 三个参数.
             */
            ArtemisConfig.host = "127.0.0.1:443"; // 平台的ip端口
            ArtemisConfig.host = url;
            ArtemisConfig.appKey = "29180881";  // 密钥appkey
            ArtemisConfig.appKey = key;
            ArtemisConfig.appSecret = "XO0wCAYGi4KV70ybjznx";// 密钥appSecret
            ArtemisConfig.appSecret = code;
            /**
             * STEP2：设置OpenAPI接口的上下文
             */
//        final String ARTEMIS_PATH = "/artemis";
            final String ARTEMIS_PATH = "";
            /**
             * STEP3：设置接口的URI地址
             */
            final String previewURLsApi = ARTEMIS_PATH + "/api/acps/v1/authDownload/task/simpleDownload";
            Map<String, String> path = new HashMap<String, String>(2) {
                {
                    put("https://", previewURLsApi);//根据现场环境部署确认是http还是https
                }
            };

            /**
             * STEP4：设置参数提交方式
             */
            String contentType = "application/json";

            /**
             * STEP5：组装请求参数
             */
//        JSONObject jsonBody = new JSONObject();
//        jsonBody.put("cameraIndexCode", "748d84750e3a4a5bbad3cd4af9ed5101");
//        jsonBody.put("streamType", 0);
//        jsonBody.put("protocol", "rtsp");
//        jsonBody.put("transmode", 1);
//        jsonBody.put("expand", "streamform=ps");
//        String body = jsonBody.toString();

            JSONObject postData = new JSONObject();
            JSONObject resourceInfo = new JSONObject();//资源设备对象信息
            resourceInfo.put("resourceIndexCode", "");//资源的唯一标识
            resourceInfo.put("resourceType", "");//资源类型，参考附录A.50 设备通道类型
            resourceInfo.put("channelNos", "");//资源通道号
            postData.put("resourceInfo", resourceInfo);
            JSONObject personInfo = new JSONObject();
            personInfo.put("personId", "");//人员Id，系统内人员从获取人员列表v2 接口获取返回参数中personId，系统外人员，人员Id由调用方指定，作为人员唯一标志，最大长度为64，包含数字、字母（小写）和中划线（-）
            personInfo.put("operatorType", "");//操作类型，0新增；1修改；2删除
            personInfo.put("startTime", "");//
            personInfo.put("endTime", "");
            personInfo.put("personType", "");
            personInfo.put("name", "");
            personInfo.put("cards", "");
            //开信息
            JSONObject cardInfo = new JSONObject();
            cardInfo.put("card", "");//卡号
            cardInfo.put("status", 0);//卡号
            cardInfo.put("cardType", "");//卡类型，为空时默认正常普通卡，1普通卡 2残疾人卡 3黑名单卡 4巡查卡 5胁迫卡 6超级卡
            JSONArray cards = new JSONArray();
            cards.add(cardInfo);
            personInfo.put("cards", cards);
            JSONObject faceInfo = new JSONObject();
            faceInfo.put("card", "");
            faceInfo.put("data", "");//人脸数据，Key：为人脸编号 Value：为人脸图片https下载的URL，图片要求浏览器可以直接打开，不支持重定向  f1": "http://10.40.239.69:8080/babel/images/100.jpg
            personInfo.put("face", faceInfo);
            postData.put("personInfo", personInfo);

            /**
             * STEP6：调用接口
             */
            String result = ArtemisHttpUtil.doPostStringArtemis(path, postData.toString(), null, null, contentType, null);// post请求application/json类型参数

            System.out.println(result);
            return null;
        } catch (Exception e) {
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }
}
