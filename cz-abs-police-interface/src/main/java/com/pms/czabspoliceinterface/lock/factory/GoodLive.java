package com.pms.czabspoliceinterface.lock.factory;

import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.HttpUtil;
import com.pms.czpmsutils.ImageUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.request.SmartLockRequest;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GoodLive implements IPmsLock {
    private static final Logger log = LoggerFactory.getLogger(GoodLive.class);
    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<SmartLockRequest.RegistPerson> peoples = smartLockRequest.getPeoples();
            String url = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_URL) + "/hotel/face/add";
            String hotelName = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_KEY);
            for (int i = 0; i < peoples.size(); i++) {
                SmartLockRequest.RegistPerson person = peoples.get(i);
                Map<String, String> postData = new HashMap<>();
                String image = "https://hotel-file-1258829933.cos.ap-shanghai.myqcloud.com/" + person.getCameraImage();
                log.info("图片数据url:" + image);
                String image2Base64 = ImageUtils.image2Base64(image);
                postData.put("image", image2Base64);
                postData.put("hotelName", hotelName);
                postData.put("idCard", person.getIdCode().trim());
                postData.put("phone", person.getPhone().trim());
                postData.put("sex", person.getSex() == 0 ? "1" : "0");
                postData.put("name", person.getPersonName().trim());
                postData.put("beginTime", HotelUtils.parseDate2Str(smartLockRequest.getCheckinTime()));
                postData.put("endTime", HotelUtils.parseDate2Str(smartLockRequest.getCheckoutTime()));
                postData.put("room", smartLockRequest.getRoomNum());
                for (int j = 0; j < 3; j++) {
                    log.info("GoodLive:checkin param:" + JSONObject.fromObject(postData).toString());
                    String result = HttpUtil.goodLivePost(url, postData, "POST");
                    log.info("GoodLive:checkin result:" + result);
                    JSONObject resultData = JSONObject.fromObject(result);
                    if (resultData.containsKey("code") && resultData.getInt("code") == 200){
                        responseData.setData(HOTEL_CONST.HOTEL_SMART_LOCK_SUCCESS);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<SmartLockRequest.RegistPerson> peoples = smartLockRequest.getPeoples();
            String url = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_URL) + "/hotel/face/delete";
            String hotelName = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_KEY);
            for (int i = 0; i < peoples.size(); i++) {
                SmartLockRequest.RegistPerson person = peoples.get(i);
                Map<String, String> postData = new HashMap<>();
                postData.put("hotelName", hotelName);
                postData.put("idCard", person.getIdCode().trim());
                for (int j = 0; j < 3; j++) {
                    log.info("GoodLive:checkout param:" + JSONObject.fromObject(postData).toString());
                    String result = HttpUtil.goodLiveDelete(url, postData);
                    log.info("GoodLive:checkout result:" + result);
                    JSONObject resultData = JSONObject.fromObject(result);
                    if (resultData.containsKey("code") && resultData.getInt("code") == 200){
                        responseData.setData(HOTEL_CONST.HOTEL_SMART_LOCK_SUCCESS);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;


    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            List<SmartLockRequest.RegistPerson> peoples = smartLockRequest.getPeoples();
            String url = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_URL) + "/hotel/extension";
            String hotelName = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_KEY);
            for (int i = 0; i < peoples.size(); i++) {
                SmartLockRequest.RegistPerson person = peoples.get(i);
                Map<String, String> postData = new HashMap<>();
                postData.put("hotelName", hotelName);
                postData.put("idCard", person.getIdCode());
                postData.put("room", smartLockRequest.getRoomNum());
                postData.put("beginTime", HotelUtils.parseDate2Str(smartLockRequest.getCheckinTime()));
                postData.put("endTime", HotelUtils.parseDate2Str(smartLockRequest.getCheckoutTime()));
                for (int j = 0; j < 3; j++) {
                    log.info("GoodLive:stayover param:" + JSONObject.fromObject(postData).toString());
                    String result = HttpUtil.goodLivePost(url, postData, "POST");
                    log.info("GoodLive:stayover result:" + result);
                    JSONObject resultData = JSONObject.fromObject(result);
                    if (resultData.containsKey("code") && resultData.getInt("code") == 200){
                        responseData.setData(HOTEL_CONST.HOTEL_SMART_LOCK_SUCCESS);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        this.checkout(smartLockRequest, param);
        return this.checkin(smartLockRequest, param);
    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String url = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_URL) + "/hotel/change-room";
            String hotelName = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_KEY);
            List<SmartLockRequest.RegistPerson> peoples = smartLockRequest.getPeoples();
            for (int i = 0; i < peoples.size(); i++) {
                SmartLockRequest.RegistPerson person = peoples.get(i);
                Map<String, String> postData = new HashMap<>();
                postData.put("hotelName", hotelName);
                postData.put("idCard", person.getIdCode());
                postData.put("oldRoom", smartLockRequest.getOldRoomNo());
                postData.put("newRoom", smartLockRequest.getRoomNum());
                for (int j = 0; j < 3; j++) {
                    log.info("GoodLive:stayover changeRoom:" + JSONObject.fromObject(postData).toString());
                    String result = HttpUtil.goodLivePost(url, postData, "POST");
                    log.info("GoodLive:changeRoom result:" + result);
                    JSONObject resultData = JSONObject.fromObject(result);
                    if (resultData.containsKey("code") && resultData.getInt("code") == 200){
                        responseData.setData(HOTEL_CONST.HOTEL_SMART_LOCK_SUCCESS);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
