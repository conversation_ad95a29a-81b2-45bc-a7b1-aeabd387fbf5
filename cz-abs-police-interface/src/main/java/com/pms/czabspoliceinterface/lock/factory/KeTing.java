package com.pms.czabspoliceinterface.lock.factory;

import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czabspoliceinterface.utils.HttpUtil;
import com.pms.czpmsutils.CosFileUtil;
import com.pms.czpmsutils.ImageHelper;
import com.pms.czpmsutils.ImageUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.request.SmartLockRequest;
import com.pms.pmsorder.bean.SmartLockRecord;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;

public class KeTing implements IPmsLock {
    private static final Logger log = LoggerFactory.getLogger(KeTing.class);
    public static String getPhone() {
        // 定义字符串前缀
        String prefix = "v-";
        // 定义可用字符
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        // 创建Random对象
        Random random = new Random();
        // 创建StringBuilder用于拼接字符
        StringBuilder sb = new StringBuilder(prefix);

        // 循环17次，每次随机选择一个字符
        for (int i = 0; i < 17; i++) {
            int randomIndex = random.nextInt(chars.length());
            sb.append(chars.charAt(randomIndex));
        }

        return sb.toString();
    }

    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        SmartLockRecord smartLockRecord = new SmartLockRecord();
        try {
            String token = this.getToken(param);
            List<SmartLockRequest.RegistPerson> peoples = smartLockRequest.getPeoples();
            if (null == peoples || peoples.size() < 1) {
                throw new Exception(HOTEL_CONST.DATA_LIST_IS_NULL);
            }
            String projectId = param.getString("579");
            String url = param.getString("575");
            for (int i = 0; i < peoples.size(); i++) {
                SmartLockRequest.RegistPerson registPerson = peoples.get(i);
                smartLockRecord.setHid(smartLockRequest.getHid());
                smartLockRecord.setRegistId(smartLockRequest.getRegistId());
                smartLockRecord.setBookingOrderId(smartLockRequest.getBookingOrderId());
                smartLockRecord.setRoomNo(smartLockRequest.getRoomNum());
                smartLockRecord.setBusinessType(1);
                smartLockRecord.setRegistPersonId(registPerson.getRegistPersonId());
                smartLockRecord.setCreateTime(new Date());
                smartLockRecord.setLockFactory("KeTing");
                smartLockRecord.setHotelGroupId(smartLockRequest.getHotelGroupId());
                JSONObject postData = new JSONObject();
                postData.put("projectId", projectId);
                JSONArray areaNames = new JSONArray();
                areaNames.add(smartLockRequest.getRoomNum());
                postData.put("areaNames", areaNames);
                /**
                 * 梯控相关问题
                 */

                JSONArray elevatorFloors = new JSONArray();
                elevatorFloors.add("1");
                elevatorFloors.add("2");
                elevatorFloors.add("3");
                elevatorFloors.add("4");
                elevatorFloors.add("5");
                elevatorFloors.add("6");
                elevatorFloors.add("7");
                elevatorFloors.add("8");
                postData.put("elevatorFloors", elevatorFloors);
                JSONArray authUsers = new JSONArray();
                JSONObject authUser = new JSONObject();
                if (i == 0) {
                    authUser.put("phone", registPerson.getPhone().trim());
                } else {
                    authUser.put("phone", "v-" + registPerson.getIdCode().trim().substring(0, 17));
                }

                authUser.put("name", registPerson.getPersonName().trim());

                if (null == registPerson.getCameraImage() || "".equals(registPerson.getCameraImage())) {
                    throw new Exception("照片数据为空");
                }

                String pic = "https://hotel-file-1258829933.cos.ap-shanghai.myqcloud.com/" + registPerson.getCameraImage();
                String image2Base64 = ImageUtils.image2Base64(pic);
                //照片先送检
                Boolean aBoolean = facePictur(url, image2Base64);
                if (!aBoolean) {
                    throw new Exception("照片质量不合格");
                }
                authUser.put("faceImageBase64", image2Base64);
                authUsers.add(authUser);
                postData.put("authUsers", authUsers);
                postData.put("begin", smartLockRequest.getCheckinTime().getTime() / 1000);
                postData.put("end", smartLockRequest.getCheckoutTime().getTime() / 1000);
                smartLockRecord.setPostUrl(url + "/api/v1/hms/openapi/addAuth");
//                smartLockRecord.setPostData(postData.toString());
                System.out.println(postData.toString());
                log.info("smartlock-checkin-url:" + url + "/api/v1/hms/openapi/addAuth" + " data:" + postData.toString());
                String result = HttpUtil.sendPostKeTing(url + "/api/v1/hms/openapi/addAuth", postData.toString(), token);
                log.info("smartlock-checkin-result:" + result);
                if (null == result || result.equals("")) {
                    throw new Exception("下发人脸异常");
                }
                JSONObject resultData = JSONObject.fromObject(result);
                if (!resultData.containsKey("code") || resultData.getInt("code") != 0) {
                    throw new Exception("下发人脸异常");
                }
                smartLockRecord.setResultCode(1);
                smartLockRecord.setResult("下发人脸成功");
            }
            responseData.setData("下发人脸成功");
            responseData.setData1("下发人脸成功");
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            smartLockRecord.setResultCode(0);
            smartLockRecord.setResult(e.getMessage());
        } finally {
            responseData.setData1(smartLockRecord);
        }
        return responseData;
    }


    /**
     * 下载文件---返回下载后的文件存储路径
     *
     * @param url      文件地址
     * @param dir      存储目录
     * @param fileName 存储文件名
     * @return
     */
    public static String downloadHttpUrl(String url, String dir, String fileName) {
        try {
            File dirfile = new File(dir);
            if (!dirfile.exists()) {
                dirfile.mkdirs();
            }
            SSLConnectionSocketFactory scsf = new SSLConnectionSocketFactory(
                    SSLContexts.custom().loadTrustMaterial(null, new TrustSelfSignedStrategy()).build(),
                    NoopHostnameVerifier.INSTANCE);
            CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(scsf).build();
            HttpGet httpget = new HttpGet(url);
            HttpResponse response = client.execute(httpget);
            HttpEntity entity = response.getEntity();
            InputStream is = entity.getContent();

            int cache = 10 * 1024;
            FileOutputStream fileout = new FileOutputStream(dir + "/" + fileName);
            byte[] buffer = new byte[cache];
            int ch = 0;
            while ((ch = is.read(buffer)) != -1) {
                fileout.write(buffer, 0, ch);
            }
            is.close();
            fileout.flush();
            fileout.close();
            ImageHelper.scaleImageWithParams(dir + "/" + fileName, dir + "/new" + fileName, 640, 480, true, "jpg");
            String s = TransformPhotoToBase64Data(dir + "/new", fileName);
            UUID uuid = UUID.randomUUID();
            CosFileUtil.UploadObjectRsp dir1 = CosFileUtil.uploadObjectBase64(s, 0, "dir", "image/jpg", uuid + ".jpeg");
            return dir1.getUrl();

        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }


    public static String TransformPhotoToBase64Data(String LoadPath, String DataName) {

        System.out.println("加载路径" + LoadPath);
        Base64.Encoder encoder = Base64.getEncoder();  //获取Base64编码器
        byte[] ImgContainer = null;    //数据集缓存器
        FileInputStream fileInputStream = null; //文件输入流
        try {
            System.out.println(LoadPath + DataName);
            fileInputStream = new FileInputStream(LoadPath + DataName);    //到指定路径寻找文件
            ImgContainer = new byte[fileInputStream.available()];          //设置图片字节数据缓冲区大小
            fileInputStream.read(ImgContainer);           //将数据流中的图片数据读进缓冲区
            String Base64ImgData = encoder.encodeToString(ImgContainer);  //将图片编码转换成Base64格式的数据集
            fileInputStream.close();      //关闭数据流
            return Base64ImgData;  //将缓冲区数据转换成字符数据返回
        } catch (FileNotFoundException e) {
            return "找不到指定文件!";
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "null";
    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String token = this.getToken(param);
            String projectId = param.getString("579");
            String url = param.getString("575");

            List<SmartLockRequest.RegistPerson> peoples = smartLockRequest.getPeoples();
            if (null == peoples || peoples.size() < 1) {
                throw new Exception(HOTEL_CONST.DATA_LIST_IS_NULL);
            }

            for (int i = 0; i < peoples.size(); i++) {
                SmartLockRequest.RegistPerson registPerson = peoples.get(i);
                JSONObject postData = new JSONObject();
                postData.put("projectId", projectId);
                JSONArray areaNames = new JSONArray();
                areaNames.add(smartLockRequest.getRoomNum());
                postData.put("areaNames", areaNames);
                JSONArray userPhone = new JSONArray();
                if (i == 0) {
                    userPhone.add(registPerson.getPhone());
                } else {
                    userPhone.add("v-" + registPerson.getIdCode().substring(0, 17));
                }
                postData.put("userPhone", userPhone);
                System.out.println(postData.toString());
                log.info("smartlock-checkout-url:" + url + "/api/v1/hms/openapi/revokeUserAuth" + " data:" + postData.toString());
                String result = HttpUtil.sendPostKeTing(url + "/api/v1/hms/openapi/revokeUserAuth", postData.toString(), token);
                log.info("smartlock-checkout-result:" + result);
                if (null == result || result.equals("")) {
                    throw new Exception("取消授权异常");
                }
                JSONObject resultData = JSONObject.fromObject(result);
                if (!resultData.containsKey("code") || resultData.getInt("code") != 0) {
                    throw new Exception("取消授权异常");
                }
            }
            responseData.setData("取消成功");
            responseData.setData1("取消成功");
        } catch (Exception e) {

        }
        return responseData;
    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        checkout(smartLockRequest, param);
        return checkin(smartLockRequest, param);
    }

    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        checkout(smartLockRequest, param);
        return checkin(smartLockRequest, param);
    }


    public Boolean facePictur(String url, String base64Image) {
        try {
            JSONObject postData = new JSONObject();
            postData.put("imageBase64", base64Image);
            postData.put("cropFace", false);
            String result = HttpUtil.sendPost(url + "/api/v1/face0/check", postData.toString());
            if (null == result || result.equals("")) {
                throw new Exception("人脸打分失败");
            }
            JSONObject resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("code") || resultData.getInt("code") != 0) {
                throw new Exception("人脸打分失败");
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public String getToken(JSONObject param) {
        String token = "";
        try {
            JSONObject postData = new JSONObject();

            String userName = param.getString("655");
            String password = param.getString("656");
            String url = param.getString("575");
            postData.put("account", userName);
            postData.put("password", password);

            String result = HttpUtil.sendPost(url + "/api/v1/hms/openapi/login", postData.toString());
            if (null == result || result.equals("")) {
                throw new Exception("获取token失败");
            }
            JSONObject resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("code") || resultData.getInt("code") != 0) {
                throw new Exception("获取token失败");
            }
            token = resultData.getJSONObject("data").getString("sessionId");
            System.out.println(result);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return token;
    }
}
