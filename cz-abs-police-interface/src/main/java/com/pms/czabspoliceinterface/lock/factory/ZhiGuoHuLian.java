package com.pms.czabspoliceinterface.lock.factory;


import cn.hutool.core.codec.Base64;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import com.google.gson.Gson;
import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czabspoliceinterface.utils.TuYaUtil;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.StringUtil;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.SmartLockRequest;
import com.pms.pmsorder.bean.SmartLockRecord;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.DefaultPutRet;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Decoder;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class ZhiGuoHuLian implements IPmsLock {

    private static final Logger log = LoggerFactory.getLogger(ZhiGuoHuLian.class);

    private static final String appId = "20230901514241";
    private static final String appSecret = "66c627e1bae5bacf2336afad15c5bbce";

    private static final String phone = "13970032911";
    private static final String password = "13970032911";

    private static final String proId = "4cc82c34bfa6faed8a879033ffc7abe3";


    private static final JSONObject roomInfo = JSONObject.fromObject("{\"8801\":\"802b9337f62ed9460bf3c6f78d3d9036\",\"8802\":\"14e5d8cee755f4abc7adc690fed1d343\",\"8805\":\"18364f3d44208a84ea4b58e8a46a8029\",\"8806\":\"ed587d444119bbbc62fed6bb3a958708\",\"8807\":\"5997f7d4017110e81eb85de877b33b84\",\"8808\":\"4e1cc56fafaefd5a13cba2ab24ce8933\",\"8809\":\"ffe402f46119c9fc2382d643c1c18fed\",\"8810\":\"9bdafc9b988c80fdd6200fa66dc1a846\",\"8811\":\"d043f69aded45b92c48959813685b5f8\",\"8812\":\"8226ea8e690a1422d3f7a848b33a4dd5\",\"8815\":\"5af51467eb448694013770e15c71feb2\",\"8816\":\"c639c6663dc7569961cc9969576748b2\",\"8817\":\"c95e58ea97eaf4d427b1f03a1817c953\",\"8818\":\"f0a9ac276ddd294eaeb23c665f41c48d\",\"8819\":\"833d9d6766289cd60e3b0ba2a58e6c75\",\"8820\":\"d669d0c4c75940e64c0f5d67b0a9d8fb\",\"8901\":\"7e90cf23c1fd67244d9b18e047bcd6bf\",\"8902\":\"fe30f0a61e189b3dbf5cf2022429f004\",\"8905\":\"ec605cd328dbf33d3802595f5e9b25db\",\"8906\":\"c91cc9a2366bc66e7cf43e1491027264\",\"8907\":\"995e1c16096e5df21ec209111ad20c42\",\"8908\":\"2b19e6b1e3e8f6c5e076174fa1a6364e\",\"8909\":\"07766c4468a0d2943b3ff9c3399de130\",\"8910\":\"bce08ad70220e84c8364623e2a9ae4d2\",\"8911\":\"39e3ff4c638a235ae10e1e04f7154364\",\"8912\":\"0678afb9f1efa6c39f5cafeb4a9a7ff5\",\"8915\":\"95332663ad6c37c54fe55d7f502c541e\",\"8916\":\"e34302923c49bfb731181932890bcc2c\",\"8917\":\"6d0e64728fc3c4e8a3baa75650c26137\",\"8918\":\"34acddb0c6166ac4a52603cf423f90ff\",\"8919\":\"5ad431c05b3dbd9f9a674b48b293d98b\"}");

//    private static final String phone = "18018205686";
//    private static final String password = "l8930038";

    private String getToken() {
        JSONObject postData = new JSONObject();
        postData.put("app_id", ZhiGuoHuLian.appId);
        postData.put("app_secret", ZhiGuoHuLian.appSecret);
        BASE64Decoder decoder = new BASE64Decoder();
        String encode = Base64.encode(postData.toString());
        System.out.println(encode);
        JSONObject data = new JSONObject();
        data.put("userphone", ZhiGuoHuLian.phone);
        data.put("password", ZhiGuoHuLian.password);
        System.out.println(data.toString());
        String res = HttpRequest.post("https://api-developer.zhiguohulian.com/openapi/v3/login").header(Header.AUTHORIZATION, encode).header(Header.CONTENT_TYPE, "application/json").timeout(30000).body(data.toString()).execute().body();
        System.out.println(res);
        JSONObject resData = JSONObject.fromObject(res);
        if (!resData.containsKey("code") || !resData.getString("code").equals("200")) {
            return "";
        }
        return resData.getJSONObject("data").getString("access_token");
    }


    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        SmartLockRecord smartLockRecord = new SmartLockRecord();
        smartLockRecord.setLockFactory("ZhiGuoHuLian");
        try {
            String token = this.getToken();
            if (StringUtil.isEmpty(token)) {
                throw new Exception("获取token失败");
            }
            //https://api-developer.zhiguohulian.com/openapi/v3/projectConfig 获取项目配置
            //获取房屋列表,多个项目下的房屋列表 https://api-developer.zhiguohulian.com/openapi/v3/room
            String uid = roomInfo.containsKey(smartLockRequest.getRoomNum()) ? roomInfo.getString(smartLockRequest.getRoomNum()) : "";
            String res = "";
            JSONObject resultMap = new JSONObject();
            if (StringUtil.isEmpty(uid)) {
                res = HttpRequest.get("https://api-developer.zhiguohulian.com/openapi/v3/room?size=500&project_uid=" + ZhiGuoHuLian.proId).header(Header.AUTHORIZATION, token).timeout(30000).execute().body();
                System.out.println(res);
                resultMap = JSONObject.fromObject(res);
                if (!resultMap.containsKey("code") || !resultMap.getString("code").equals("200")) {
                    throw new Exception(res);
                }
                JSONArray roomList = resultMap.getJSONObject("data").getJSONArray("data");

                for (int i = 0; i < roomList.size(); i++) {
                    JSONObject roomInfo = roomList.getJSONObject(i);
                    if (roomInfo.getString("room_name").equals(smartLockRequest.getRoomNum())) {
                        uid = roomInfo.getString("uid");
                        break;
                    }
                }
                if (StringUtil.isEmpty(uid)) {
                    throw new Exception("未查询到可用的房间信息");
                }
            }

            //查询详情
            //https://api-developer.zhiguohulian.com/openapi/v3/room/info
            res = HttpRequest.get("https://api-developer.zhiguohulian.com/openapi/v3/room/info?uid=" + uid).header(Header.AUTHORIZATION, token).timeout(30000).execute().body();
            System.out.println(res);

            resultMap = JSONObject.fromObject(res);

            if (!resultMap.containsKey("code") || !resultMap.getString("code").equals("200")) {
                throw new Exception(res);
            }
            JSONObject roomInfo = resultMap.getJSONObject("data");
            //https://api-developer.zhiguohulian.com/openapi/v3/tenant
            //上传图片到七牛
            for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                smartLockRecord.setHid(smartLockRequest.getHid());
                smartLockRecord.setRegistId(smartLockRequest.getRegistId());
                smartLockRecord.setBookingOrderId(smartLockRequest.getBookingOrderId());
                smartLockRecord.setRoomNo(smartLockRequest.getRoomNum());
                smartLockRecord.setBusinessType(1);
                smartLockRecord.setRegistPersonId(person.getRegistPersonId());
                smartLockRecord.setRegistId(person.getRegistId());
                smartLockRecord.setCreateTime(new Date());
                smartLockRecord.setLockFactory("ZhiGuoHuLian");
                smartLockRecord.setHotelGroupId(smartLockRequest.getHotelGroupId());
                //数据记录
                JSONObject postData = new JSONObject();
                postData.put("project_uid", ZhiGuoHuLian.proId);
                postData.put("building_uid", roomInfo.getString("building_id"));
                postData.put("room_uid", roomInfo.getString("uid"));
                postData.put("user_realname", person.getPersonName());
                postData.put("user_phone", person.getPhone());
                postData.put("ur_expired", Math.floorDiv(smartLockRequest.getCheckoutTime().getTime() / 1000, 1));
                if (!StringUtil.isEmpty(person.getCameraImage())) {
                    postData.put("face_image", uploadFaceImage(token, "https://hotel-file-1258829933.cos.ap-shanghai.myqcloud.com/" + person.getCameraImage()));
                }
                postData.put("user_id_card", person.getIdCode());
                smartLockRecord.setPostUrl("https://api-developer.zhiguohulian.com/openapi/v3/tenant");
                smartLockRecord.setPostData(postData.toString());
                System.out.println("开始添加租户信息:" + postData.toString());
                String body = HttpRequest.post("https://api-developer.zhiguohulian.com/openapi/v3/tenant").header(Header.AUTHORIZATION, token).header(Header.CONTENT_TYPE, "application/json").timeout(30000).body(postData.toString()).execute().body();
                System.out.println(body);
                JSONObject resData = JSONObject.fromObject(body);
                smartLockRecord.setResult(body);
                if (!resData.containsKey("code") || !resData.getString("code").equals("200")) {
                    throw new Exception(res);
                }
                smartLockRecord.setResultCode(1);
                smartLockRecord.setResult("数据推送成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            smartLockRecord.setResultCode(0);
            smartLockRecord.setResult(e.getMessage());
        } finally {
            responseData.setData1(smartLockRecord);
        }
        return responseData;
    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        SmartLockRecord smartLockRecord = new SmartLockRecord();
        smartLockRecord.setLockFactory("ZhiGuoHuLian");
        try {
            String token = this.getToken();
            if (StringUtil.isEmpty(token)) {
                throw new Exception("获取token失败");
            }
            JSONObject postData = new JSONObject();
            String body = "";
            JSONObject resultMap = new JSONObject();
            //入住人员uid
            List<String> uidList = new ArrayList<>();
            String roomUid = roomInfo.containsKey(smartLockRequest.getRoomNum()) ? roomInfo.getString(smartLockRequest.getRoomNum()) : "";
            /**
             * 将要退房的人员信息列表
             */
            JSONArray personList = new JSONArray();
            if (StringUtil.isEmpty(roomUid)) {
                /**
                 * 首先获取租户列表
                 */
                postData.put("page", "1");
                postData.put("size", "500");
                postData.put("project_uid", ZhiGuoHuLian.proId);
                String url = "https://api-developer.zhiguohulian.com/openapi/v3/tenant?page=1&size=500&project_uid=" + ZhiGuoHuLian.proId;
                System.out.println(url);
                body = HttpRequest.get(url).header(Header.AUTHORIZATION, token).timeout(30000).execute().body();
                resultMap = JSONObject.fromObject(body);
                if (!resultMap.containsKey("code") || resultMap.getInt("code") != 200) {
                    throw new Exception("获取租户信息失败");
                }
                personList = resultMap.getJSONObject("data").getJSONArray("data");
                System.out.println(JSONObject.fromObject(body).toString());
                for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                    for (int j = 0; j < personList.size(); j++) {
                        SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                        smartLockRecord.setHid(smartLockRequest.getHid());
                        smartLockRecord.setRegistId(smartLockRequest.getRegistId());
                        smartLockRecord.setBookingOrderId(smartLockRequest.getBookingOrderId());
                        smartLockRecord.setRoomNo(smartLockRequest.getRoomNum());
                        smartLockRecord.setBusinessType(2);
                        smartLockRecord.setRegistPersonId(person.getRegistPersonId());
                        smartLockRecord.setRegistId(person.getRegistId());
                        smartLockRecord.setCreateTime(new Date());
                        smartLockRecord.setLockFactory("ZhiGuoHuLian");
                        smartLockRecord.setHotelGroupId(smartLockRequest.getHotelGroupId());
                        if (smartLockRequest.getRoomNum().equals(personList.getJSONObject(j).getString("room_number")) && person.getPersonName().equals(personList.getJSONObject(j).getString("user_name"))) {
                            uidList.add(personList.getJSONObject(j).getString("uid"));
                            break;
                        }
                    }
                }
            } else {
                //通过房屋查询人员信息
                postData = new JSONObject();
                postData.put("uid", roomUid);
                System.out.println("通过房屋查询人员信息:" + ":" + "https://api-developer.zhiguohulian.com/openapi/v3/room/tenant?uid=" + roomUid);
                System.out.println("token:" + token);
                body = HttpRequest.get("https://api-developer.zhiguohulian.com/openapi/v3/room/tenant?uid=" + roomUid).header(Header.AUTHORIZATION, token).timeout(30000).execute().body();
                System.out.println("通过房屋查询人员信息返回:" + body);
                resultMap = JSONObject.fromObject(body);
                if (!resultMap.containsKey("code") || resultMap.getInt("code") != 200) {
                    throw new Exception("获取租户信息失败");
                }
                personList = resultMap.getJSONArray("data");

                for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                    for (int j = 0; j < personList.size(); j++) {
                        SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                        smartLockRecord.setHid(smartLockRequest.getHid());
                        smartLockRecord.setRegistId(smartLockRequest.getRegistId());
                        smartLockRecord.setBookingOrderId(smartLockRequest.getBookingOrderId());
                        smartLockRecord.setRoomNo(smartLockRequest.getRoomNum());
                        smartLockRecord.setBusinessType(2);
                        smartLockRecord.setRegistPersonId(person.getRegistPersonId());
                        smartLockRecord.setRegistId(person.getRegistId());
                        smartLockRecord.setCreateTime(new Date());
                        smartLockRecord.setLockFactory("ZhiGuoHuLian");
                        smartLockRecord.setHotelGroupId(smartLockRequest.getHotelGroupId());
                        if (person.getPersonName().equals(personList.getJSONObject(j).getString("user_realname"))) {
                            uidList.add(personList.getJSONObject(j).getString("uid"));
                            break;
                        }
                    }
                }
            }


            for (int i = 0; i < uidList.size(); i++) {
                String uid = uidList.get(i);
                postData = new JSONObject();
                postData.put("uid", uid); //租户的关系
                smartLockRecord.setPostUrl(TuYaUtil.url + "https://api-developer.zhiguohulian.com/openapi/v3/tenant");
                smartLockRecord.setPostData(postData.toString());
                body = HttpRequest.delete("https://api-developer.zhiguohulian.com/openapi/v3/tenant").header(Header.AUTHORIZATION, token).timeout(30000).body(postData.toString()).execute().body();
                log.info("zhiguohulian 退房 param --- [{}]", postData.toString());
                smartLockRecord.setResult(body);
                log.info("zhiguohulian 退房 res --- [{}]", body);
                JSONObject resData = JSONObject.fromObject(body);
                smartLockRecord.setResult(body);
                if (!resData.containsKey("code") || !resData.getString("code").equals("200")) {
                    throw new Exception(body);
                }
                smartLockRecord.setResultCode(1);
                smartLockRecord.setResult("数据推送成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");

            smartLockRecord.setResultCode(0);
            smartLockRecord.setResult(e.getMessage());
        } finally {
            responseData.setData1(smartLockRecord);
        }
        return responseData;
    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        SmartLockRecord smartLockRecord = new SmartLockRecord();
        smartLockRecord.setLockFactory("ZhiGuoHuLian");
        try {
            String token = this.getToken();
            if (StringUtil.isEmpty(token)) {
                throw new Exception("获取token失败");
            }

            JSONObject postData = new JSONObject();
            String body = "";
            JSONObject resultMap = new JSONObject();
            //入住人员uid
            List<String> uidList = new ArrayList<>();
            String roomUid = roomInfo.containsKey(smartLockRequest.getOldRoomNo()) ? roomInfo.getString(smartLockRequest.getOldRoomNo()) : "";
            /**
             * 将要退房的人员信息列表
             */
            JSONArray personList = new JSONArray();
            if (StringUtil.isEmpty(roomUid)) {
                /**
                 * 首先获取租户列表
                 */
                postData.put("page", "1");
                postData.put("size", "500");
                postData.put("project_uid", ZhiGuoHuLian.proId);
                String url = "https://api-developer.zhiguohulian.com/openapi/v3/tenant?page=1&size=500&project_uid=" + ZhiGuoHuLian.proId;
                System.out.println(url);
                body = HttpRequest.get(url).header(Header.AUTHORIZATION, token).timeout(30000).execute().body();
                resultMap = JSONObject.fromObject(body);
                if (!resultMap.containsKey("code") || resultMap.getInt("code") != 200) {
                    throw new Exception("获取租户信息失败");
                }
                personList = resultMap.getJSONObject("data").getJSONArray("data");
                System.out.println(JSONObject.fromObject(body).toString());
                for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                    for (int j = 0; j < personList.size(); j++) {
                        SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                        smartLockRecord.setHid(smartLockRequest.getHid());
                        smartLockRecord.setRegistId(smartLockRequest.getRegistId());
                        smartLockRecord.setBookingOrderId(smartLockRequest.getBookingOrderId());
                        smartLockRecord.setRoomNo(smartLockRequest.getRoomNum());
                        smartLockRecord.setBusinessType(2);
                        smartLockRecord.setRegistPersonId(person.getRegistPersonId());
                        smartLockRecord.setRegistId(person.getRegistId());
                        smartLockRecord.setCreateTime(new Date());
                        smartLockRecord.setLockFactory("ZhiGuoHuLian");
                        smartLockRecord.setHotelGroupId(smartLockRequest.getHotelGroupId());
                        if (smartLockRequest.getRoomNum().equals(personList.getJSONObject(j).getString("room_number")) && person.getPersonName().equals(personList.getJSONObject(j).getString("user_name"))) {
                            uidList.add(personList.getJSONObject(j).getString("uid"));
                            break;
                        }
                    }
                }
            } else {
                //通过房屋查询人员信息
                String url = "https://api-developer.zhiguohulian.com/openapi/v3/room/tenant";
                postData = new JSONObject();
                postData.put("uid", roomUid);
                System.out.println("通过房屋查询人员信息:" + "https://api-developer.zhiguohulian.com/openapi/v3/room/tenant?uid=" + roomUid);
//                body = HttpRequest.post(url).header(Header.AUTHORIZATION, token).header(Header.CONTENT_TYPE, "application/json").timeout(10000).body(postData.toString()).execute().body();
                System.out.println("token:" + token);
                body = HttpRequest.get("https://api-developer.zhiguohulian.com/openapi/v3/room/tenant?uid=" + roomUid).header(Header.AUTHORIZATION, token).timeout(30000).execute().body();
                System.out.println("通过房屋查询人员信息返回:" + body);
                resultMap = JSONObject.fromObject(body);
                if (!resultMap.containsKey("code") || resultMap.getInt("code") != 200) {
                    throw new Exception("获取租户信息失败");
                }
                personList = resultMap.getJSONArray("data");

                for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                    for (int j = 0; j < personList.size(); j++) {
                        SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                        smartLockRecord.setHid(smartLockRequest.getHid());
                        smartLockRecord.setRegistId(smartLockRequest.getRegistId());
                        smartLockRecord.setBookingOrderId(smartLockRequest.getBookingOrderId());
                        smartLockRecord.setRoomNo(smartLockRequest.getRoomNum());
                        smartLockRecord.setBusinessType(2);
                        smartLockRecord.setRegistPersonId(person.getRegistPersonId());
                        smartLockRecord.setRegistId(person.getRegistId());
                        smartLockRecord.setCreateTime(new Date());
                        smartLockRecord.setLockFactory("ZhiGuoHuLian");
                        smartLockRecord.setHotelGroupId(smartLockRequest.getHotelGroupId());
                        if (person.getPersonName().equals(personList.getJSONObject(j).getString("user_realname"))) {
                            uidList.add(personList.getJSONObject(j).getString("uid"));
                            break;
                        }
                    }
                }
            }

            System.out.println("uidList.size:" + uidList.size());
            for (int i = 0; i < uidList.size(); i++) {
                String uid = uidList.get(i);
                postData = new JSONObject();
                postData.put("uid", uid); //租户的关系
                smartLockRecord.setPostUrl(TuYaUtil.url + "https://api-developer.zhiguohulian.com/openapi/v3/tenant");
                smartLockRecord.setPostData(postData.toString());
                log.info("删除人员信息:{} ,postData:{}", "https://api-developer.zhiguohulian.com/openapi/v3/tenant", postData.toString());
                body = HttpRequest.delete("https://api-developer.zhiguohulian.com/openapi/v3/tenant").header(Header.AUTHORIZATION, token).timeout(30000).body(postData.toString()).execute().body();
                smartLockRecord.setResult(body);
                System.out.println(body);
                JSONObject resData = JSONObject.fromObject(body);
                smartLockRecord.setResult(body);
                if (!resData.containsKey("code") || !resData.getString("code").equals("200")) {
                    throw new Exception(body);
                }
                smartLockRecord.setResultCode(1);
                smartLockRecord.setResult("数据推送成功");
            }

            /**
             * 办理入住
             */

            //https://api-developer.zhiguohulian.com/openapi/v3/projectConfig 获取项目配置
            //获取房屋列表,多个项目下的房屋列表 https://api-developer.zhiguohulian.com/openapi/v3/room
            log.info("查询房间信息列表:{}", "https://api-developer.zhiguohulian.com/openapi/v3/room?size=500&project_uid=" + ZhiGuoHuLian.proId);
            String res = HttpRequest.get("https://api-developer.zhiguohulian.com/openapi/v3/room?size=500&project_uid=" + ZhiGuoHuLian.proId).header(Header.AUTHORIZATION, token).timeout(30000).execute().body();
            log.info("查询房间信息列表返回:{}", res);
            resultMap = JSONObject.fromObject(res);
            if (!resultMap.containsKey("code") || !resultMap.getString("code").equals("200")) {
                throw new Exception(res);
            }
            JSONArray roomList = resultMap.getJSONObject("data").getJSONArray("data");
            String uid = "";
            for (int i = 0; i < roomList.size(); i++) {
                JSONObject roomInfo = roomList.getJSONObject(i);
                if (roomInfo.getString("room_name").equals(smartLockRequest.getRoomNum())) {
                    uid = roomInfo.getString("uid");
                    break;
                }
            }
            if (StringUtil.isEmpty(uid)) {
                throw new Exception("未查询到可用的房间信息");
            }


            //查询详情
            //https://api-developer.zhiguohulian.com/openapi/v3/room/info

            log.info("查询房间信息详情:{}", "https://api-developer.zhiguohulian.com/openapi/v3/room/info?uid=" + uid);
            res = HttpRequest.get("https://api-developer.zhiguohulian.com/openapi/v3/room/info?uid=" + uid).header(Header.AUTHORIZATION, token).timeout(30000).execute().body();
            log.info("查询房间信息详情返回:{}", res);
            resultMap = JSONObject.fromObject(res);

            if (!resultMap.containsKey("code") || !resultMap.getString("code").equals("200")) {
                throw new Exception(res);
            }
            JSONObject roomInfo = resultMap.getJSONObject("data");
            //https://api-developer.zhiguohulian.com/openapi/v3/tenant
            //上传图片到七牛
            for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                smartLockRecord.setHid(smartLockRequest.getHid());
                smartLockRecord.setRegistId(smartLockRequest.getRegistId());
                smartLockRecord.setBookingOrderId(smartLockRequest.getBookingOrderId());
                smartLockRecord.setRoomNo(smartLockRequest.getRoomNum());
                smartLockRecord.setBusinessType(1);
                smartLockRecord.setRegistPersonId(person.getRegistPersonId());
                smartLockRecord.setRegistId(person.getRegistId());
                smartLockRecord.setCreateTime(new Date());
                smartLockRecord.setLockFactory("ZhiGuoHuLian");
                smartLockRecord.setHotelGroupId(smartLockRequest.getHotelGroupId());
                //数据记录
                postData = new JSONObject();
                postData.put("project_uid", ZhiGuoHuLian.proId);
                postData.put("building_uid", roomInfo.getString("building_id"));
                postData.put("room_uid", roomInfo.getString("uid"));
                postData.put("user_realname", person.getPersonName());
                postData.put("user_phone", person.getPhone());
                postData.put("ur_expired", Math.floorDiv(smartLockRequest.getCheckoutTime().getTime() / 1000, 1));
                if (!StringUtil.isEmpty(person.getCameraImage())) {
                    postData.put("face_image", uploadFaceImage(token, "https://hotel-file-1258829933.cos.ap-shanghai.myqcloud.com/" + person.getCameraImage()));
                }
                postData.put("user_id_card", person.getIdCode());
                smartLockRecord.setPostUrl("https://api-developer.zhiguohulian.com/openapi/v3/tenant");
                smartLockRecord.setPostData(postData.toString());
                System.out.println("开始添加租户信息:" + postData.toString());
                log.info("开始添加租户信息:{}",  postData.toString());
                body = HttpRequest.post("https://api-developer.zhiguohulian.com/openapi/v3/tenant").header(Header.AUTHORIZATION, token).header(Header.CONTENT_TYPE, "application/json").timeout(30000).body(postData.toString()).execute().body();
                System.out.println(body);
                log.info("开始添加租户信息返回:{}",  postData.toString());
                JSONObject resData = JSONObject.fromObject(body);
                smartLockRecord.setResult(body);
                if (!resData.containsKey("code") || !resData.getString("code").equals("200")) {
                    throw new Exception(res);
                }
                smartLockRecord.setResultCode(1);
                smartLockRecord.setResult("数据推送成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");

            smartLockRecord.setResultCode(0);
            smartLockRecord.setResult(e.getMessage());
        } finally {
            responseData.setData1(smartLockRecord);
        }
        return responseData;
    }
    public static String uploadFaceImage(String authorizationToken, String imageUrl) {
        String url = "https://api-developer.zhiguohulian.com/openapi/v3/face/token";
        new HttpRequest(url).method(Method.GET).auth(authorizationToken);
        String res = HttpRequest.get(url).auth(authorizationToken).header(Header.CONTENT_TYPE, "application/json").execute().body();
        System.out.println(res);
        JSONObject resData = JSONObject.fromObject(res);
        if (!resData.containsKey("code") || !resData.getString("code").equals("200")) {
            return "";
        }
        JSONObject data = resData.getJSONObject("data");
        String token = data.getString("token");
        String fileName = data.getString("fileName");
        //url图片转bytes数据
        byte[] imageBytes = new HttpRequest(imageUrl).method(Method.GET).execute().bodyBytes();
        return upload(token, fileName, imageBytes);
    }

    public static String upload(String upToken, String key, byte[] uploadBytes) {
        //构造一个带指定 Region 对象的配置类
        Configuration cfg = new Configuration(Region.region2());
        cfg.resumableUploadAPIVersion = Configuration.ResumableUploadAPIVersion.V2;// 指定分片上传版本
        //其他参数参考类注释
        UploadManager uploadManager = new UploadManager(cfg);
        //生成上传凭证，然后准备上传
        try {
            // inputstream 上传
            /*
            byte[] uploadBytes = "hello qiniu cloud".getBytes("utf-8");
            InputStream byteInputStream=new ByteArrayInputStream(uploadBytes);
            Response response = uploadManager.put(byteInputStream,key,upToken,null, null);
             */

            Response response = uploadManager.put(uploadBytes, key, upToken);
            //解析上传成功的结果
            DefaultPutRet putRet = new Gson().fromJson(response.bodyString(), DefaultPutRet.class);
            System.out.println(putRet.key);
            System.out.println(putRet.hash);
            return putRet.key;
        } catch (QiniuException ex) {
            ex.printStackTrace();
            if (ex.response != null) {
                System.err.println(ex.response);
                try {
                    String body = ex.response.toString();
                    System.err.println(body);
                } catch (Exception ignored) {
                }
            }
        }
        return "";
    }


}
