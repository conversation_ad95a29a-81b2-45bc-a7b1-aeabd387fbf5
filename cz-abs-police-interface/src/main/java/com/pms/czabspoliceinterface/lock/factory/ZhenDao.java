package com.pms.czabspoliceinterface.lock.factory;

import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.HttpRequest;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.SmartLockRequest;
import net.sf.json.JSONObject;

public class <PERSON>henDao implements IPmsLock {
    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String url = param.getString("575");
            String code = param.getString("579");
            Integer num = 0;
            for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                SmartLockRequest.RegistPerson registPerson = smartLockRequest.getPeoples().get(i);
                JSONObject postData = new JSONObject();
                postData.put("room_num", smartLockRequest.getRoomNum());
                postData.put("id_card", registPerson.getIdCode());
                postData.put("name", registPerson.getPersonName());
                postData.put("project_id", Integer.parseInt(code));
                postData.put("check_in_time", HotelUtils.dateToStamp(smartLockRequest.getCheckinTime()));
                postData.put("check_out_time", HotelUtils.dateToStamp(smartLockRequest.getCheckoutTime()));
                String cameraImage = registPerson.getCameraImage();
                if (cameraImage == null){
                    if (registPerson.getIdImage() == null){
                        responseData.setCode(-1);
                        responseData.setMsg("照片信息为空");
                        return responseData;
                    }
                    cameraImage = HotelUtils.getImgStr("https://hotel-file-1258829933.cos.ap-shanghai.myqcloud.com/"  + registPerson.getIdImage()) ;
                }
                postData.put("face", cameraImage);
                System.out.println(postData.toString());
                String res = HttpRequest.basic(url + "/checkin", postData.toString());
                System.out.println("返回结果:" + res);
                JSONObject jsonObject = JSONObject.fromObject(res);
                if (jsonObject.containsKey("code") && jsonObject.getInt("code") == 0) {
                    num++;
                }
            }
            if (num != smartLockRequest.getPeoples().size()) {
                throw new Exception("入住失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String url = param.getString("575");
            String code = param.getString("579");
            Integer num = 0;
            for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                SmartLockRequest.RegistPerson registPerson = smartLockRequest.getPeoples().get(i);
                JSONObject postData = new JSONObject();
                postData.put("room_num", smartLockRequest.getRoomNum());
                postData.put("id_card", registPerson.getIdCode());
                postData.put("project_id", Integer.parseInt(code));
                System.out.println(postData.toString());
                String res = HttpRequest.basic(url + "/checkout", postData.toString());
                System.out.println("返回结果:" + res);
                JSONObject jsonObject = JSONObject.fromObject(res);
                if (jsonObject.containsKey("code") && jsonObject.getInt("code") == 0) {
                    num++;
                }
            }
            if (num != smartLockRequest.getPeoples().size()) {
                throw new Exception("退房失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String url = param.getString("575");
            String code = param.getString("579");

            JSONObject postData = new JSONObject();
            postData.put("room_num", smartLockRequest.getRoomNum());
            postData.put("check_out_time", HotelUtils.dateToStamp(smartLockRequest.getCheckoutTime()));
            postData.put("project_id", Integer.parseInt(code));
            System.out.println(postData.toString());
            String res = HttpRequest.basic(url + "/masterchg", postData.toString());
            System.out.println("返回结果:" + res);
            JSONObject jsonObject = JSONObject.fromObject(res);
            if (!jsonObject.containsKey("code") || jsonObject.getInt("code") != 0) {
                throw new Exception("续住失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String url = param.getString("575");
            String code = param.getString("579");
            JSONObject postData = new JSONObject();
            postData.put("room_num", smartLockRequest.getRoomNum());
            postData.put("old_room_num", smartLockRequest.getOldRoomNo());
            postData.put("project_id", Integer.parseInt(code));
            postData.put("check_in_time", HotelUtils.dateToStamp(smartLockRequest.getCheckinTime()));
            postData.put("check_out_time", HotelUtils.dateToStamp(smartLockRequest.getCheckoutTime()));
            String res = HttpRequest.basic(url + "/rmchg", postData.toString());
            JSONObject jsonObject = JSONObject.fromObject(res);
            if (!jsonObject.containsKey("code") || jsonObject.getInt("code") != 0) {
                throw new Exception("换房失败");
            }

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
