package com.pms.czabspoliceinterface.lock.factory;

import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czpmsutils.HttpUtil;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.SmartLockRequest;
import net.sf.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class TTLock implements IPmsLock {
    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String url = param.getString("575");//智能门锁接口请求地址
            String clientId = param.getString("579");//智能门锁请求编码
            String token = this.getToken(param);
            Map<String, Object> postData = new HashMap<>();
            postData.put("clientId", clientId);
            postData.put("accessToken", token);
            postData.put("lockId", smartLockRequest.getLockNo());
            postData.put("keyboardPwdName", "房间密码");
            postData.put("startDate", smartLockRequest.getCheckinTime().getTime());
            postData.put("endDate", smartLockRequest.getCheckoutTime().getTime());
            postData.put("date", System.currentTimeMillis());
            String res = "";
            if (true) {
                //随机密码
                postData.put("keyboardPwdType", 3);
                res = HttpUtil.http(url + "/v3/keyboardPwd/get", postData);
            } else {
                //固定密码
                postData.put("keyboardPwd", smartLockRequest.getPeoples().get(0).getIdCode().substring(0, 6));
                postData.put("addType", 2);
                res = HttpUtil.http(url + "/v3/keyboardPwd/add", postData);
            }
            System.out.println(res);
            JSONObject resultData = JSONObject.fromObject(res);
            //需要存储，删除密码的时候要传
            String keyboardPwdId = resultData.getString("keyboardPwdId");
            String keyboardPwd = resultData.getString("keyboardPwd");
            responseData.setData(keyboardPwd);
            responseData.setData1(keyboardPwdId);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String url = param.getString("575");//智能门锁接口请求地址
            String clientId = param.getString("579");//智能门锁请求编码
            String token = this.getToken(param);
            Map<String, Object> postData = new HashMap<>();
            postData.put("clientId", clientId);
            postData.put("accessToken", token);
            postData.put("lockId", smartLockRequest.getLockNo());
            //发送密码的时候需要保存 ，离店的时候才可以删除
            postData.put("keyboardPwdId", smartLockRequest.getSessionToken());
            postData.put("date", System.currentTimeMillis());
            String res = HttpUtil.http(url + "/v3/keyboardPwd/delete", postData);
            System.out.println(res);
            JSONObject resultData = JSONObject.fromObject(res);



        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    public String getToken(JSONObject param) {
        String token = "";
        try {
            String url = param.getString("575");//智能门锁接口请求地址
            String clientId = param.getString("579");//智能门锁请求编码
            String clientSecret = param.getString("578");//智能门锁请求密钥
            String userName = param.getString("655");//	智能门锁系统登录账号
            String password = param.getString("656");//智能门锁系统登录密码
            Map<String, Object> postData = new HashMap<>();
            postData.put("clientId", clientId);
            postData.put("clientSecret", clientSecret);
            postData.put("username", userName);
            postData.put("password", password);
            String res = HttpUtil.http(url + "/oauth2/token", postData);
            System.out.println(res);
            JSONObject resultData = JSONObject.fromObject(res);
            if (!resultData.containsKey("access_token") || resultData.getString("access_token").length() < 32) {
                throw new Exception(res);
            }
            token = resultData.getString("access_token");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return token;
    }
}
