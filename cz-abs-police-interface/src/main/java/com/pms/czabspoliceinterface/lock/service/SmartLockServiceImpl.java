package com.pms.czabspoliceinterface.lock.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.pms.czabspoliceinterface.lock.factory.*;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.request.*;
import com.pms.pmsorder.bean.SmartLockRecord;
import com.pms.pmsorder.service.RegistService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SmartLockServiceImpl extends BaseService {
    private static final Logger log = LoggerFactory.getLogger(SmartLockServiceImpl.class);
    @Autowired
    RegistService registService;



    public ResponseData checkin(SmartLockRequest smartLockRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(smartLockRequest);
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setParentId(217);
            hotelSettingByParamId.setHid(user.getHid());
            JSONObject hotelPrintSettingMap = this.findHotelPrintSettingMap(hotelSettingByParamId);
            Integer open = hotelPrintSettingMap.getInt(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_ENABLE);
//            //等于1说明开启，则查询厂家
            if (open != 1) {
                return responseData;
            }
            String fatory = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_FACTORY).toString();
            String url = hotelPrintSettingMap.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).toString();
            if (fatory.equals("BoonLive")) {
                BoonLive boonLive = new BoonLive();
                responseData = boonLive.checkin(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("YLock")) {
                YLock yLock = new YLock();
                responseData = yLock.checkin(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("ZhenDao")) {
                ZhenDao zhenDao = new ZhenDao();
                responseData = zhenDao.checkin(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("TuYa")) {
                TuYa tuYa = new TuYa();
                responseData = tuYa.checkin(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("KeTing")) {
                KeTing keTing = new KeTing();
                responseData = keTing.checkin(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("GoodLive")) {
                GoodLive goodLive = new GoodLive();
                responseData = goodLive.checkin(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("ZhiGuoHuLian")) {
                ZhiGuoHuLian zhiGuoHuLian = new ZhiGuoHuLian();
                responseData = zhiGuoHuLian.checkin(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("ChaiJiaKeJi")) {
                ChaiJiaKeJi chaiJiaKeJi = new ChaiJiaKeJi();
                responseData = chaiJiaKeJi.checkin(smartLockRequest, hotelPrintSettingMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        } finally {
            Object data1 = responseData.getData1();
            if (null != data1) {
                SmartLockRecord smartLockRecord = new SmartLockRecord();
                try {
                    HotelUtils.classCopy(data1, smartLockRecord);
                    registService.updateSmartLockRecord(smartLockRecord);
                } catch (Exception e) {
                    System.out.println(e.getMessage());
                }
            }
        }
        return responseData;
    }

    public ResponseData updateGuest(SmartLockRequest smartLockRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(smartLockRequest);
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setParentId(217);
            hotelSettingByParamId.setHid(user.getHid());
            JSONObject hotelPrintSettingMap = this.findHotelPrintSettingMap(hotelSettingByParamId);
            Integer open = hotelPrintSettingMap.getInt(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_ENABLE);
            if (open != 1) {
                return responseData;
            }
            String fatory = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_FACTORY).toString();
            String url = hotelPrintSettingMap.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).toString();
            if (fatory.equals("BoonLive")) {
                BoonLive boonLive = new BoonLive();
                return boonLive.changeRoom(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("YLock")) {
                YLock yLock = new YLock();
                return yLock.changeRoom(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("ZhenDao")) {
                ZhenDao zhenDao = new ZhenDao();
                return zhenDao.changeRoom(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("TuYa")) {
                TuYa tuYa = new TuYa();
                return tuYa.updateGuestInfo(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("KeTing")) {
                KeTing keTing = new KeTing();
                keTing.updateGuestInfo(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("ChaiJiaKeJi")) {
                ChaiJiaKeJi chaiJiaKeJi = new ChaiJiaKeJi();
                chaiJiaKeJi.updateGuestInfo(smartLockRequest, hotelPrintSettingMap);
            }
            else if (fatory.equals("GoodLive")) {
                GoodLive goodLive = new GoodLive();
                goodLive.updateGuestInfo(smartLockRequest, hotelPrintSettingMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public ResponseData changeRoom(SmartLockRequest smartLockRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(smartLockRequest);
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setParentId(217);
            hotelSettingByParamId.setHid(user.getHid());
            JSONObject hotelPrintSettingMap = this.findHotelPrintSettingMap(hotelSettingByParamId);
            Integer open = hotelPrintSettingMap.getInt(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_ENABLE);
            if (open != 1) {
                return responseData;
            }
            String fatory = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_FACTORY).toString();
            String url = hotelPrintSettingMap.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).toString();
            if (fatory.equals("BoonLive")) {
                BoonLive boonLive = new BoonLive();
                return boonLive.changeRoom(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("YLock")) {
                YLock yLock = new YLock();
                return yLock.changeRoom(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("ZhenDao")) {
                ZhenDao zhenDao = new ZhenDao();
                return zhenDao.changeRoom(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("TuYa")) {
                TuYa tuYa = new TuYa();
                return tuYa.changeRoom(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("KeTing")) {
                KeTing keTing = new KeTing();
                keTing.changeRoom(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("ChaiJiaKeJi")) {
                ChaiJiaKeJi chaiJiaKeJi = new ChaiJiaKeJi();
                chaiJiaKeJi.changeRoom(smartLockRequest, hotelPrintSettingMap);
            }else if (fatory.equals("GoodLive")) {
                GoodLive goodLive = new GoodLive();
                goodLive.changeRoom(smartLockRequest, hotelPrintSettingMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public ResponseData checkout(SmartLockRequest smartLockRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(smartLockRequest);
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setParentId(217);
            hotelSettingByParamId.setHid(user.getHid());
            JSONObject hotelPrintSettingMap = this.findHotelPrintSettingMap(hotelSettingByParamId);
            Integer open = hotelPrintSettingMap.getInt(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_ENABLE);
            if (open != 1) {
                return responseData;
            }
            String fatory = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_FACTORY).toString();
            String url = hotelPrintSettingMap.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).toString();
            if (fatory.equals("BoonLive")) {
                BoonLive boonLive = new BoonLive();
                return boonLive.checkout(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("YLock")) {
                YLock yLock = new YLock();
                return yLock.checkout(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("ZhenDao")) {
                ZhenDao zhenDao = new ZhenDao();
                return zhenDao.checkout(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("TuYa")) {
                TuYa tuYa = new TuYa();
                responseData = tuYa.checkout(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("KeTing")) {
                KeTing keTing = new KeTing();
                responseData = keTing.checkout(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("GoodLive")) {
                GoodLive goodLive = new GoodLive();
                responseData = goodLive.checkout(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("ZhiGuoHuLian")) {
                ZhiGuoHuLian zhiGuoHuLian = new ZhiGuoHuLian();
                responseData = zhiGuoHuLian.checkout(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("ChaiJiaKeJi")) {
                ChaiJiaKeJi chaiJiaKeJi = new ChaiJiaKeJi();
                responseData = chaiJiaKeJi.checkout(smartLockRequest, hotelPrintSettingMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }

        return responseData;

    }


    public ResponseData stayover(SmartLockRequest smartLockRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(smartLockRequest);
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setParentId(217);
            hotelSettingByParamId.setHid(user.getHid());
            JSONObject hotelPrintSettingMap = this.findHotelPrintSettingMap(hotelSettingByParamId);
            Integer open = hotelPrintSettingMap.getInt(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_ENABLE);
            if (open != 1) {
                return responseData;
            }
            String fatory = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_FACTORY).toString();
            String url = hotelPrintSettingMap.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).toString();
            if (fatory.equals("BoonLive")) {
                BoonLive boonLive = new BoonLive();
                return boonLive.stayover(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("YLock")) {
                YLock yLock = new YLock();
                return yLock.stayover(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("ZhenDao")) {
                ZhenDao zhenDao = new ZhenDao();
                return zhenDao.stayover(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("TuYa")) {
                TuYa tuYa = new TuYa();
                tuYa.stayover(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("KeTing")) {
                KeTing keTing = new KeTing();
                keTing.stayover(smartLockRequest, hotelPrintSettingMap);
            } else if (fatory.equals("ChaiJiaKeJi")) {
                ChaiJiaKeJi chaiJiaKeJi = new ChaiJiaKeJi();
                chaiJiaKeJi.stayover(smartLockRequest, hotelPrintSettingMap);
            }else if (fatory.equals("GoodLive")) {
                GoodLive goodLive = new GoodLive();
                goodLive.stayover(smartLockRequest, hotelPrintSettingMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public ResponseData updateRoomInfo(SmartRoomInfoRequest smartRoomInfoRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(smartRoomInfoRequest);
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setParentId(217);
            hotelSettingByParamId.setHid(user.getHid());
            JSONObject hotelPrintSettingMap = this.findHotelPrintSettingMap(hotelSettingByParamId);
            Integer open = hotelPrintSettingMap.getInt(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_ENABLE);
            if (open != 1) {
                return responseData;
            }
            String fatory = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_FACTORY).toString();
            String url = hotelPrintSettingMap.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).toString();
            if (fatory.equals("BoonLive")) {
                BoonLive boonLive = new BoonLive();
                return responseData;
            } else if (fatory.equals("YLock")) {
                YLock yLock = new YLock();
                return responseData;
            } else if (fatory.equals("ZhenDao")) {
                ZhenDao zhenDao = new ZhenDao();
                return responseData;
            } else if (fatory.equals("TuYa")) {
                TuYa tuYa = new TuYa();
                responseData = tuYa.roomInfo(smartRoomInfoRequest, hotelPrintSettingMap);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public ResponseData deviceControl(DeviceControlRequest deviceControlRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TbUserSession user = this.getTbUserSession(deviceControlRequest);
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setParentId(217);
            hotelSettingByParamId.setHid(user.getHid());
            JSONObject hotelPrintSettingMap = this.findHotelPrintSettingMap(hotelSettingByParamId);
            Integer open = hotelPrintSettingMap.getInt(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_ENABLE);
            if (open != 1) {
                return responseData;
            }
            String fatory = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_FACTORY).toString();
            String url = hotelPrintSettingMap.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).toString();
            String projectId = hotelPrintSettingMap.getString("653").toString();

            String accessId = hotelPrintSettingMap.getString("655").toString();
            String accessKey = hotelPrintSettingMap.getString("656").toString();

            deviceControlRequest.setUrl(url);
            deviceControlRequest.setProjectId(projectId);
            deviceControlRequest.setAccessId(accessId);
            deviceControlRequest.setAccessKey(accessKey);


            if (fatory.equals("BoonLive")) {
                BoonLive boonLive = new BoonLive();
                return responseData;
            } else if (fatory.equals("YLock")) {
                YLock yLock = new YLock();
                return responseData;
            } else if (fatory.equals("ZhenDao")) {
                ZhenDao zhenDao = new ZhenDao();
                return responseData;
            } else if (fatory.equals("TuYa")) {
                TuYa tuYa = new TuYa();
                responseData = tuYa.deviceControl(deviceControlRequest);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    public ResponseData openDoor(OpenDoorRequest openDoorRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String factory = openDoorRequest.getFactory();
            //通过酒店配置获取配置信息
            HotelSettingByParamId hotelSettingByParamId = new HotelSettingByParamId();
            hotelSettingByParamId.setParentId(217);
            if (ObjectUtil.isNotNull(openDoorRequest.getHid())) {
                hotelSettingByParamId.setHid(openDoorRequest.getHid());
            } else {
                String sessionToken = openDoorRequest.getSessionToken();
                System.out.println("sessionToken:" + sessionToken);
                TbUserSession user = this.getTbUserSession(sessionToken);
                hotelSettingByParamId.setHid(user.getHid());
            }
            JSONObject hotelPrintSettingMap = this.findHotelPrintSettingMap(hotelSettingByParamId);
            String proId = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_KEY);
            System.out.println("proId:" + proId);
            if (StrUtil.isEmpty(proId)) {
                log.info("proID is null");
                responseData.setCode(-1);
                responseData.setMsg("proID is null");
                return responseData;
            }
            String code = hotelPrintSettingMap.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_CODE);
            System.out.println("code:" + code);
            if (StrUtil.isEmpty(code)) {
                log.info("code is null");
                responseData.setCode(-1);
                responseData.setMsg("code is null");
                return responseData;
            }
            openDoorRequest.setProId(proId);
            openDoorRequest.setCode(code);

            if (StrUtil.isEmpty(openDoorRequest.getUuid()) ){
                String roomNo = openDoorRequest.getRoomNo();
                JSONObject postData = new JSONObject();
                postData.put("hid", openDoorRequest.getHid());
                postData.put("sessionToken" ,openDoorRequest.getSessionToken());
                JSONObject roomInfo = this.getRoomInfo(postData);
                JSONArray roomList = roomInfo.getJSONObject("data").getJSONArray("data");
                for (int i = 0; i < roomList.size(); i++) {
                    if (roomNo.equals(roomList.getJSONObject(i).getString("roomNum"))){
                        openDoorRequest.setUuid(roomList.getJSONObject(i).getString("bckStr"));
                        break;
                    }
                }
            }


            if (factory.equals("TuYaSmartLock")) {
                TuYaSmartLock tuYaSmartLock = new TuYaSmartLock();
                responseData = tuYaSmartLock.openDoor(openDoorRequest);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }
}
