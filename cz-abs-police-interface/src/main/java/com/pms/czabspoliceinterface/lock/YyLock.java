package com.pms.czabspoliceinterface.lock;


import com.pms.czpmsutils.GsonUtil;
import com.pms.czpmsutils.HttpClientPool;
import com.pms.czpmsutils.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class YyLock {

    private static final String APPID = "FB55AC9329624040A7ADD60FF7CFDA88";
    private static final String PASSWORD = "123456";
    private static final String USERNAME = "18018205686";
    private static final String APPKEY = "D2C4E83D79914974B08E7D2CC408C9A3";
    private static final String NONCESTR = "1522115166482";

    public static String getSignParamStr(Map<String, String> param, String APPKEY) {
        List<String> paramList = new ArrayList<>();
        for (Map.Entry<String, String> entry : param.entrySet()) {
            paramList.add(entry.getKey() + "=" + entry.getValue());
        }
        paramList.sort(String::compareTo);
        String paramStr = StringUtils.join(paramList, "&");
        String sign = MD5Util.MD5EncodeUTF8(paramStr + "&APPKEY=" + APPKEY).toUpperCase();
        return paramStr + "&SIGN=" + sign;
    }
    /**
     * 请求方式：
     * POST
     * 参数：
     * 参数名	    必选	类型	说明
     * APPID	    是	    string	APPID
     * AT	        是	    string	当前时间的时间戳（毫秒为单位）
     * NONCESTR	    是	    string	随机码（任意16进制的随机数）
     * PASSWORD	    是	    string	用户密码
     * PHONEIP	    否	    string	登录ip
     * PHONEID	    否	    string	登录信息
     * USERNAME	    是	    string	用户帐号（二次开发手机号）
     * SIGN	        是	    string	签名
     *
     * @return
     */
    public static String login() throws Exception {
        HashMap<String, String> map = new HashMap<>();
        map.put("APPID", APPID);
        map.put("AT", String.format("%d", System.currentTimeMillis()));
        map.put("NONCESTR", NONCESTR);
        map.put("PASSWORD", PASSWORD);
        map.put("USERNAME", USERNAME);
        String paramStr = getSignParamStr(map, APPKEY);
        String traceId = UUID.randomUUID().toString();
        String logInRt = HttpClientPool.processPostJson("https://yylock.eeun.cn/dms/app/dmsLogin?" + paramStr, "", traceId);
        //log.info("TraceId={},login result={}", traceId, logInRt);
        Map map1 = GsonUtil.json2Bean(logInRt, Map.class);
        double result = (double) map1.get("result");
        if (result != 0) {
            //log.error("login error code = {}, msg={}", result, map.get("msg"));
            throw new Exception("login field");
        }
        return (String) map1.get("token");
    }

    public static String loginNew() throws Exception {
        HashMap<String, String> map = new HashMap<>();
        map.put("APPID", APPID);
        map.put("AT", String.format("%d", System.currentTimeMillis()));
        map.put("NONCESTR", NONCESTR);
        map.put("PASSWORD", PASSWORD);
        map.put("USERNAME", USERNAME);
        String paramStr = getSignParamStr(map, APPKEY);
        String traceId = UUID.randomUUID().toString();
        String logInRt = HttpClientPool.processPostJson("https://cloud.eeun.cn/dms/app/dmsLogin?" + paramStr, "", traceId);
        //log.info("TraceId={},login result={}", traceId, logInRt);
        Map map1 = GsonUtil.json2Bean(logInRt, Map.class);
        double result = (double) map1.get("result");
        if (result != 0) {
            //log.error("login error code = {}, msg={}", result, map.get("msg"));
            throw new Exception("login field");
        }
        return (String) map1.get("token");
    }


    /**
     * 入住密码/访客码
     * <p>
     * 参数名	        必选	类型	说明
     * APPID	        是	    string	APPID
     * AT	            是	    string	时间戳（毫秒为单位）
     * CARDTYPE	    是	    string	密码类型（参考首页密码类型字典表，支持类型：21、22、23、24）
     * DELETEPSWFLAG	否	    string	1：新密码删除旧密码 0：新密码不删除旧密码 （此参数仅限制为生效时间与失效时间相差30天内的限时密码使用，其他情况使用该参数无效）
     * DEVICE_ID	    是	    string	设备码
     * ENDDATE	    是	    string	结束时间 (格式：年月日时分秒)
     * NONCESTR	    是	    string	随机码
     * SAVEFLAGE	    否	    string	是否保存标志，1：保存 0：不保存。 默认值为0。当值为1时，KEYUSERID字段不能为空
     * STARTDATE	    是	    string	开始时间 (格式：年月日时分秒)
     * KEYUSERID	    否	    string	用户的帐号，一般为11位数字的手机号，当 SAVEFLAGE = 1时，这个字段才会被使用
     * TOKEN	        是	    string	会话token
     * SIGN	        是	    string	签名
     *
     * @return
     */
    public static String getDeviceVistCode(String TOKEN, String CARDTYPE, String DEVICE_ID, String ENDDATE, String STARTDATE ,String Phone) throws Exception {
        HashMap<String, String> map = new HashMap<>();
        map.put("APPID", APPID);
        map.put("AT", String.format("%d", System.currentTimeMillis()));
        map.put("NONCESTR", NONCESTR);
        map.put("PASSWORD", PASSWORD);
        map.put("USERNAME", USERNAME);
        map.put("TOKEN", TOKEN);
        map.put("CARDTYPE", CARDTYPE);
        map.put("DEVICE_ID", DEVICE_ID);
        map.put("ENDDATE", ENDDATE);
        map.put("STARTDATE", STARTDATE);
        map.put("SAVEFLAGE","1");
        map.put("KEYUSERID",Phone);
        String paramStr = getSignParamStr(map, APPKEY);
        String traceId = UUID.randomUUID().toString();
        String logInRt = HttpClientPool.processPostJson("https://yylock.eeun.cn/dms/app/getDeviceVistCode?" + paramStr, "", traceId);
        //log.info("TraceId={},login result={}", traceId, logInRt);
        Map map1 = GsonUtil.json2Bean(logInRt, Map.class);
        double result = (double) map1.get("result");
        if (result != 0) {
            //log.error("getDeviceVistCode error code = {}, msg={}", result, map.get("msg"));
            throw new Exception("getDeviceVistCode field");
        }
        String passWord = (String) map1.get("data");

        map = new HashMap<>();
        map.put("APPID", APPID);
        map.put("NONCESTR", NONCESTR);
        map.put("PASSWORD", PASSWORD);
        map.put("USERNAME", USERNAME);
        map.put("AT", String.format("%d", System.currentTimeMillis()));
        map.put("TOKEN", TOKEN);
        map.put("CARDTYPE", CARDTYPE);
        map.put("ENDDATE", ENDDATE);
        map.put("KEYLOCKNAME","101");
        map.put("OPENDOORCODE",passWord);
        map.put("RECEIVEPHONE",Phone);
        map.put("SENDPHONE","13933623670");
        map.put("SMSTYPE","0");
        map.put("STARTDATE", STARTDATE);
        paramStr = getSignParamStr(map, APPKEY);
        traceId = UUID.randomUUID().toString();
        logInRt = HttpClientPool.processPostJson("https://yylock.eeun.cn/dms/app/sendCheckInMsg?" + paramStr, "", traceId);
        map1 = GsonUtil.json2Bean(logInRt, Map.class);
        result = (double) map1.get("result");
        if (result != 0) {
            throw new Exception("getDeviceVistCode field");
        }

        return passWord;


    }

}
