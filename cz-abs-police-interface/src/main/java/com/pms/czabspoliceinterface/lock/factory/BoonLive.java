package com.pms.czabspoliceinterface.lock.factory;

import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czabspoliceinterface.lock.bean.Item;
import com.pms.czabspoliceinterface.lock.boonlive.Blwws;
import com.pms.czabspoliceinterface.lock.boonlive.BlwwsSoap;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.request.SmartLockRequest;
import net.sf.json.JSONObject;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.ws.Holder;
import java.net.URL;
import java.sql.Date;
import java.util.GregorianCalendar;

//深圳住好
public class BoonLive implements IPmsLock {
    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        System.out.println("SmartLockRequest:" + JSONObject.fromObject(smartLockRequest).toString());
        try {
            URL url = new URL(param.getString("575"));
            Blwws blwws = new Blwws(url);
            GregorianCalendar c = new GregorianCalendar();
            c.setTime(new Date(System.currentTimeMillis()));
            XMLGregorianCalendar date2 = DatatypeFactory.newInstance().newXMLGregorianCalendar(c);
            Integer num = 0;
            String key = param.getString("578");
            String code = param.getString("579");
            for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                SmartLockRequest.RegistPerson registPerson = smartLockRequest.getPeoples().get(i);
                Item item = new Item();
                item.idtype = "0";
                item.idcard = registPerson.getIdCode();
                item.customer = registPerson.getPersonName();
                item.sex = registPerson.getSex() == 0 ? "男" : "女";
                item.country = "0";
                item.checkindate = HotelUtils.currentTime();
                System.out.println(item.toString());
                String xmlString = item.toString();

                BlwwsSoap blwwsSoap = blwws.getBlwwsSoap();
                Holder<String> errMsg = new Holder<>();
                Holder<Boolean> rt = new Holder<>();
                Holder<Long> checkInID = new Holder<>();
                blwwsSoap.checkIn2(key,
                        code,
                        smartLockRequest.getRoomNum(),
                        date2,
                        xmlString,
                        errMsg,
                        checkInID,
                        registPerson.getPhone(),
                        registPerson.getIdCode(),
                        rt);
                System.out.println(errMsg.value + "  ---  " + rt.value + "  " + checkInID.value);

                if ((registPerson.getPhotoPath() != null && !registPerson.getPhotoPath().equals("")) || (registPerson.getCameraImage() != null && !registPerson.getCameraImage().equals(""))) {
                    errMsg = new Holder<>();
                    rt = new Holder<>();
                    String imagePath = "";
                    if (registPerson.getPhotoPath() != null && !registPerson.getPhotoPath().equals("")) {
                        imagePath = registPerson.getPhotoPath();
                    } else {
                        imagePath = "https://hotel-file-1258829933.cos.ap-shanghai.myqcloud.com/" + registPerson.getCameraImage();
                    }
                    blwwsSoap.uploadPhoto(key, code, checkInID.value, 0, registPerson.getIdCode(),
                            registPerson.getPersonName(), registPerson.getSex().equals("男") ? 1 : 2, registPerson.getBirthday().toString(), imagePath,
                            null,
                            errMsg,
                            rt
                    );
                    System.out.println("上传图片rt: " + errMsg.value + "  ---  " + rt.value + "   " + checkInID.value);
                }

                // byte[] base64 = Base64.encode(registPerson.getIdImage());
//                blwwsSoap.checkIn(key,
//                        code,
//                        smartLockRequest.getRoomNum(),
//                        date2,
//                        xmlString,
//                        errMsg,
//                        registPerson.getPhone(),
//                        registPerson.getIdCode(),
//                        rt);
//                System.out.println(errMsg.value + "---" + rt.value);
//
//
//                blwwsSoap.uploadPhoto(key,code,Long.parseLong(registPerson.getIdCode()),0,registPerson.getIdCode(),registPerson.getPersonName(),registPerson.getSex() , "","",null,errMsg,rt);
//                if (rt.value){
//                    num ++;
//                }
            }


            if (num != smartLockRequest.getPeoples().size()) {
                responseData.setCode(-1);
                responseData.setMsg("BookLive-Checkin-Error");
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            URL url = new URL(param.getString("575"));
            Blwws blwws = new Blwws(url);
            GregorianCalendar c = new GregorianCalendar();
            c.setTime(new Date(System.currentTimeMillis()));
            XMLGregorianCalendar date2 = DatatypeFactory.newInstance().newXMLGregorianCalendar(c);
            String key = param.getString("578");
            String code = param.getString("579");
            BlwwsSoap blwwsSoap = blwws.getBlwwsSoap();
            Holder<String> errMsg = new Holder<>();
            Holder<Boolean> rt = new Holder<>();

            blwwsSoap.checkOut(key, code, smartLockRequest.getRoomNum(), date2, errMsg, rt);
            System.out.println(errMsg.value + "---" + rt.value);
            if (!rt.value) {
                responseData.setMsg(errMsg.value);
                responseData.setCode(-1);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }


}
