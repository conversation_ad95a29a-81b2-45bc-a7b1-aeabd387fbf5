package com.pms.czabspoliceinterface.lock.controller;

import com.pms.czabspoliceinterface.lock.service.SmartLockServiceImpl;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.StringUtil;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.request.DeviceControlRequest;
import com.pms.czpmsutils.request.OpenDoorRequest;
import com.pms.czpmsutils.request.SmartLockRequest;
import com.pms.czpmsutils.request.SmartRoomInfoRequest;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/hotel/smartlock")
public class SmartController {
    private static final Logger log = LoggerFactory.getLogger(SmartController.class);

    @Autowired
    private SmartLockServiceImpl smartLockService;

    @RequestMapping("/checkin")
    @ResponseBody
    public ResponseData checkin(@RequestBody SmartLockRequest smartLockRequest) {
        return smartLockService.checkin(smartLockRequest);
    }

    @RequestMapping("/stayover")
    @ResponseBody
    public ResponseData stayover(@RequestBody SmartLockRequest smartLockRequest) {
        return smartLockService.stayover(smartLockRequest);
    }

    @RequestMapping("/changeRoom")
    @ResponseBody
    public ResponseData changeRoom(@RequestBody SmartLockRequest smartLockRequest) {
        return smartLockService.changeRoom(smartLockRequest);
    }

    @RequestMapping("/updateGuest")
    @ResponseBody
    public ResponseData updateGuest(@RequestBody SmartLockRequest smartLockRequest) {
        return smartLockService.updateGuest(smartLockRequest);
    }

    @RequestMapping("/checkout")
    @ResponseBody
    public ResponseData checkout(@RequestBody SmartLockRequest smartLockRequest) {
        return smartLockService.checkout(smartLockRequest);
    }

    @RequestMapping("/updateRoomInfo")
    @ResponseBody
    public ResponseData updateRoomInfo(@RequestBody SmartRoomInfoRequest smartRoomInfoRequest) {
        return smartLockService.updateRoomInfo(smartRoomInfoRequest);
    }

    @RequestMapping("/deviceControl")
    @ResponseBody
    public ResponseData deviceControl(@RequestBody DeviceControlRequest deviceControlRequest) {
        ResponseData responseData = smartLockService.deviceControl(deviceControlRequest);
        responseData.setData("");
        responseData.setData1("");
        return responseData;
    }

    @RequestMapping("/openDoor.do")
    @ResponseBody
    public ResponseData openDoor(@RequestBody OpenDoorRequest openDoorRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (StringUtil.isEmpty(openDoorRequest.getFactory())) {
                responseData.setCode(-1);
                responseData.setMsg(HOTEL_CONST.DATA_LIST_IS_NULL);
                return responseData;
            }
            if (StringUtil.isEmpty(openDoorRequest.getRoomNo())) {
                responseData.setCode(-1);
                responseData.setMsg(HOTEL_CONST.DATA_LIST_IS_NULL);
                return responseData;
            }
            return smartLockService.openDoor(openDoorRequest);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    @RequestMapping("/guestinCallBack")
    @ResponseBody
    public ResponseData guestinCallBack(@RequestBody JSONObject data) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            log.info(data.toString());

            if (!data.containsKey("area")){
                responseData.setCode(-1);
                responseData.setMsg(HOTEL_CONST.PARAM_ERROR);
                return responseData;
            }


            if (!data.containsKey("projectId")){
                responseData.setCode(-1);
                responseData.setMsg(HOTEL_CONST.PARAM_ERROR);
                return responseData;
            }

            JSONObject areaInfo = data.getJSONObject("area");

            if (!areaInfo.containsKey("name")){
                responseData.setCode(-1);
                responseData.setMsg(HOTEL_CONST.PARAM_ERROR);
                return responseData;
            }
            String roomNo = areaInfo.getString("name");
            String projectId = data.getString("projectId");
            Integer hid = 0;
            String token = "";
            String factory = "";
            if (projectId.equals("783C8DA02EB549FD9BBD70A2CBDEB16B")){
                hid = 2464;
                token = "D4F3E58004694C15A7D6501F882A03AF";
                factory = "TuYaSmartLock";
            }
            OpenDoorRequest openDoorRequest = new OpenDoorRequest();
            openDoorRequest.setHid(hid);
            openDoorRequest.setRoomNo(roomNo);
            openDoorRequest.setFactory(factory);
            openDoorRequest.setSessionToken(token);
            smartLockService.openDoor(openDoorRequest);
            System.out.printf(data.toString());
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


}
