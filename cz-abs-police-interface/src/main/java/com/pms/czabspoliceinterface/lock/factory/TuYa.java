package com.pms.czabspoliceinterface.lock.factory;

import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czabspoliceinterface.utils.TuYaDevUtil;
import com.pms.czabspoliceinterface.utils.TuYaUtil;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.StringUtil;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.request.DeviceControlRequest;
import com.pms.czpmsutils.request.SmartLockRequest;
import com.pms.czpmsutils.request.SmartRoomInfoRequest;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.pmsorder.bean.SmartLockRecord;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class TuYa implements IPmsLock {
    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        SmartLockRecord smartLockRecord = new SmartLockRecord();
        try {
            if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
                TuYaUtil.accessId = "dh8g3j95fd7e9pqdgdhn";
                TuYaUtil.accessKey = "1ceee2fd7f004c8cbeb4b281b5cc4f81";
            }
            String token = TuYaUtil.getToken();
            System.out.println("token:" + token);
            if (token.equals("")) {
                throw new Exception("获取token失败");
            }
            Map<String, String> heads = new HashMap<>();
            JSONObject postData = new JSONObject();
            postData.put("hotel_code", smartLockRequest.getHid());
            System.out.println("SmartLockRequest:" + JSONObject.fromObject(smartLockRequest).toString());
            JSONArray guestList = new JSONArray();
            for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                //数据记录
                smartLockRecord.setHid(smartLockRequest.getHid());
                smartLockRecord.setRegistId(smartLockRequest.getRegistId());
                smartLockRecord.setBookingOrderId(smartLockRequest.getBookingOrderId());
                smartLockRecord.setRoomNo(smartLockRequest.getRoomNum());
                smartLockRecord.setBusinessType(1);
                smartLockRecord.setRegistPersonId(person.getRegistPersonId());
                smartLockRecord.setRegistId(person.getRegistId());
                smartLockRecord.setCreateTime(new Date());
                smartLockRecord.setLockFactory("TuYa");
                smartLockRecord.setHotelGroupId(smartLockRequest.getHotelGroupId());
                JSONObject guestInfo = new JSONObject();
                guestInfo.put("checkin_id", person.getRegistPersonId());
                guestInfo.put("room_no", smartLockRequest.getRoomNum());
                guestInfo.put("building", "1");
                guestInfo.put("floor", "8");
                guestInfo.put("checkin_time", smartLockRequest.getCheckinTime().getTime());
                guestInfo.put("checkout_time", smartLockRequest.getCheckoutTime().getTime());
                guestInfo.put("cust_name", null == person.getPersonName() || person.getPersonName().equals("") ? "NoName" : person.getPersonName());
                guestInfo.put("gender", person.getSex());
                guestInfo.put("id_card_no", person.getIdCode());
                guestInfo.put("id_card_type", "SFZ");
                guestInfo.put("phone_no", "86-" + (null == person.getPhone() || person.getPhone().equals("") ? "***********" : person.getPhone().trim()));
                if (i == 1 && null == person.getPhone() && person.getPhone().equals("")) {
                    guestInfo.put("phone_no", "86-***********");
                } else if (i == 2 && null == person.getPhone() && person.getPhone().equals("")) {
                    guestInfo.put("phone_no", "86-***********");
                } else if (i == 3 && null == person.getPhone() && person.getPhone().equals("")) {
                    guestInfo.put("phone_no", "86-15270020660");
                } else if (i == 4 && null == person.getPhone() && person.getPhone().equals("")) {
                    guestInfo.put("phone_no", "86-15057545530");
                }
                if (null != person.getCameraImage() && !person.getCameraImage().equals("")) {
                    guestInfo.put("face_picture_url", "https://hotel-file-1258829933.cos.ap-shanghai.myqcloud.com/" + person.getCameraImage());
                }
                guestList.add(guestInfo);
            }
            postData.put("data", guestList);
            System.out.println("data:" + postData.toString());
            if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
            }
            System.out.println("accessId:" + TuYaUtil.accessId);
            System.out.println("accessKey:" + TuYaUtil.accessKey);
            smartLockRecord.setPostUrl(TuYaUtil.url + "/v1.0/hotel/pms/checkin");
            smartLockRecord.setPostData(postData.toString());
            Object data = TuYaUtil.execute(token, "/v1.0/hotel/pms/checkin", "POST", postData.toString(), heads);
            String result = JSONObject.fromObject(data).toString();
            smartLockRecord.setResult(result);
            System.out.println(result);
            JSONObject resultData = JSONObject.fromObject(data);
            if (!resultData.getBoolean("success")) {
                throw new Exception(result);
            }
            responseData.setMsg("推送入住数据成功");
            smartLockRecord.setResultCode(1);
            smartLockRecord.setResult("数据推送成功");
            //
            if (param.containsKey(HOTEL_CONST.SMART_FACE_PUSH_URL) && !StringUtil.isEmpty(param.getString(HOTEL_CONST.SMART_FACE_PUSH_URL)) && param.getString(HOTEL_CONST.SMART_FACE_PUSH_URL).equals("ZhiGuoHuLian")) {
                ZhiGuoHuLian zhiGuoHuLian = new ZhiGuoHuLian();
                zhiGuoHuLian.checkin(smartLockRequest, param);
            }
            //推送取电  取电的 accessId 和 accessKey 是用商户的
            if (param.containsKey(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PHONE) && param.containsKey(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && param.containsKey(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PASSWORD) && param.containsKey(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT)) {
                DeviceControlRequest deviceControlRequest = new DeviceControlRequest();
                deviceControlRequest.setUrl(param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL));
                deviceControlRequest.setProjectId(param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PHONE));
                deviceControlRequest.setAccessId(param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT));
                deviceControlRequest.setAccessKey(param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PASSWORD));
                deviceControlRequest.setRoomNo(smartLockRequest.getRoomNum());
                //1-取电开关控制  2-窗帘开关控制  3-空调开关控制  4-门锁开关控制  5-情景模式控制  6-电视控制  7-灯的控制
                deviceControlRequest.setBussType(1);
                deviceControlRequest.setControlValue(1);
                this.deviceControl(deviceControlRequest);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
            smartLockRecord.setResultCode(0);
            smartLockRecord.setResult(e.getMessage());
        } finally {
            responseData.setData1(smartLockRecord);
        }
        return responseData;
    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        SmartLockRecord smartLockRecord = new SmartLockRecord();
        try {
            if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
                TuYaUtil.accessId = "dh8g3j95fd7e9pqdgdhn";
                TuYaUtil.accessKey = "1ceee2fd7f004c8cbeb4b281b5cc4f81";
            }
            String token = TuYaUtil.getToken();
            System.out.println("token:" + token);
            if (token.equals("")) {
                throw new Exception("获取token失败");
            }
            Map<String, String> heads = new HashMap<>();
            JSONObject postData = new JSONObject();
            postData.put("hotel_code", smartLockRequest.getHid());
            JSONArray guestList = new JSONArray();
            for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                smartLockRecord.setHid(smartLockRequest.getHid());
                smartLockRecord.setRegistId(smartLockRequest.getRegistId());
                smartLockRecord.setBookingOrderId(smartLockRequest.getBookingOrderId());
                smartLockRecord.setRoomNo(smartLockRequest.getRoomNum());
                smartLockRecord.setBusinessType(2);
                smartLockRecord.setRegistPersonId(person.getRegistPersonId());
                smartLockRecord.setRegistId(person.getRegistId());
                smartLockRecord.setCreateTime(new Date());
                smartLockRecord.setLockFactory("TuYa");
                smartLockRecord.setHotelGroupId(smartLockRequest.getHotelGroupId());
                JSONObject guestInfo = new JSONObject();
                guestInfo.put("checkin_id", person.getRegistPersonId());
                guestInfo.put("departure_time", new Date().getTime());
                guestInfo.put("phone_no", "86-" + (null == person.getPhone() || person.getPhone().equals("") ? "***********" : person.getPhone().trim()));
                if (i == 1) {
                    guestInfo.put("phone_no", "86-***********");
                } else if (i == 2) {
                    guestInfo.put("phone_no", "86-***********");
                } else if (i == 3) {
                    guestInfo.put("phone_no", "86-15270020660");
                } else if (i == 4) {
                    guestInfo.put("phone_no", "86-15057545530");
                }
                guestList.add(guestInfo);
            }

            postData.put("data", guestList);
            System.out.println("data:" + postData.toString());
            if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
            }
            smartLockRecord.setPostUrl(TuYaUtil.url + "/v1.0/hotel/pms/checkout");
            smartLockRecord.setPostData(postData.toString());
            Object data = TuYaUtil.execute(token, "/v1.0/hotel/pms/checkout", "PUT", postData.toString(), heads);
            String result = JSONObject.fromObject(data).toString();
            smartLockRecord.setResult(result);
            System.out.println(result);
            JSONObject resultData = JSONObject.fromObject(data);
            if (!resultData.getBoolean("success")) {
                throw new Exception(result);
            }
            responseData.setMsg("推送入住数据成功");
            smartLockRecord.setResultCode(1);
            smartLockRecord.setResult("数据推送成功");

            if (param.containsKey(HOTEL_CONST.SMART_FACE_PUSH_URL) && !StringUtil.isEmpty(param.getString(HOTEL_CONST.SMART_FACE_PUSH_URL)) && param.getString(HOTEL_CONST.SMART_FACE_PUSH_URL).equals("ZhiGuoHuLian")) {
                ZhiGuoHuLian zhiGuoHuLian = new ZhiGuoHuLian();
                zhiGuoHuLian.checkout(smartLockRequest, param);
            }

            if (param.containsKey(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PHONE) && param.containsKey(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && param.containsKey(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PASSWORD) && param.containsKey(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT)) {
                DeviceControlRequest deviceControlRequest = new DeviceControlRequest();
                deviceControlRequest.setUrl(param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL));
                deviceControlRequest.setProjectId(param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PHONE));
                deviceControlRequest.setAccessId(param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT));
                deviceControlRequest.setAccessKey(param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PASSWORD));
                deviceControlRequest.setRoomNo(smartLockRequest.getRoomNum());
                //1-取电开关控制  2-窗帘开关控制  3-空调开关控制  4-门锁开关控制  5-情景模式控制  6-电视控制  7-灯的控制
                deviceControlRequest.setBussType(1);
                deviceControlRequest.setControlValue(0);
                this.deviceControl(deviceControlRequest);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");

            smartLockRecord.setResultCode(0);
            smartLockRecord.setResult(e.getMessage());
        } finally {
            responseData.setData1(smartLockRecord);
        }
        return responseData;

    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
                TuYaUtil.accessId = "dh8g3j95fd7e9pqdgdhn";
                TuYaUtil.accessKey = "1ceee2fd7f004c8cbeb4b281b5cc4f81";
            }
            String token = TuYaUtil.getToken();
            System.out.println("token:" + token);
            if (token.equals("")) {
                throw new Exception("获取token失败");
            }
            Map<String, String> heads = new HashMap<>();
            JSONObject postData = new JSONObject();
            postData.put("hotel_code", smartLockRequest.getHid());
            JSONArray guestList = new JSONArray();
            for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                JSONObject guestInfo = new JSONObject();
                guestInfo.put("checkin_id", person.getRegistPersonId());
                guestInfo.put("checkout_time", smartLockRequest.getCheckoutTime().getTime());
                guestList.add(guestInfo);
            }

            postData.put("data", guestList);

            System.out.println("data:" + postData.toString());
            if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
            }
            Object data = TuYaUtil.execute(token, "/v1.0/hotel/pms/renew", "PUT", postData.toString(), heads);
            System.out.println(JSONObject.fromObject(data).toString());
            JSONObject resultData = JSONObject.fromObject(data);
            if (!resultData.getBoolean("success")) {
                throw new Exception("推送入住数据到涂鸦失败");
            }
            responseData.setMsg("推送入住数据成功");
            if (param.containsKey(HOTEL_CONST.SMART_FACE_PUSH_URL) && !StringUtil.isEmpty(param.getString(HOTEL_CONST.SMART_FACE_PUSH_URL)) && param.getString(HOTEL_CONST.SMART_FACE_PUSH_URL).equals("ZhiGuoHuLian")) {
                ZhiGuoHuLian zhiGuoHuLian = new ZhiGuoHuLian();
                zhiGuoHuLian.stayover(smartLockRequest, param);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
                TuYaUtil.accessId = "dh8g3j95fd7e9pqdgdhn";
                TuYaUtil.accessKey = "1ceee2fd7f004c8cbeb4b281b5cc4f81";
            }
            String token = TuYaUtil.getToken();
            System.out.println("token:" + token);
            if (token.equals("")) {
                throw new Exception("获取token失败");
            }
            Map<String, String> heads = new HashMap<>();
            JSONObject postData = new JSONObject();
            postData.put("hotel_code", smartLockRequest.getHid());
            Integer bussType = smartLockRequest.getBussType();
            JSONArray guestList = new JSONArray();
            Object data = null;
            //新增入住人
            if (bussType == 4) {
                for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                    SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                    JSONObject guestInfo = new JSONObject();
                    guestInfo.put("checkin_id", person.getRegistPersonId());
                    guestInfo.put("room_no", smartLockRequest.getRoomNum());
                    guestInfo.put("building", "1");
                    guestInfo.put("floor", "8");
                    guestInfo.put("checkin_time", smartLockRequest.getCheckinTime().getTime());
                    guestInfo.put("checkout_time", smartLockRequest.getCheckoutTime().getTime());
                    guestInfo.put("cust_name", person.getPersonName());
                    guestInfo.put("gender", person.getSex());
                    guestInfo.put("phone_no", "86-" + person.getPhone().trim());
                    guestInfo.put("id_card_no", person.getIdCode());
                    guestInfo.put("id_card_type", "SFZ");

                    if (i == 1 && null == person.getPhone() && person.getPhone().equals("")) {
                        guestInfo.put("phone_no", "86-***********");
                    } else if (i == 2 && null == person.getPhone() && person.getPhone().equals("")) {
                        guestInfo.put("phone_no", "86-***********");
                    } else if (i == 3 && null == person.getPhone() && person.getPhone().equals("")) {
                        guestInfo.put("phone_no", "86-15270020660");
                    } else if (i == 4 && null == person.getPhone() && person.getPhone().equals("")) {
                        guestInfo.put("phone_no", "86-15057545530");
                    }
                    if (null != person.getCameraImage() && !person.getCameraImage().equals("")) {
                        guestInfo.put("face_picture_url", "https://hotel-file-1258829933.cos.ap-shanghai.myqcloud.com/" + person.getCameraImage());
                    }
                    guestList.add(guestInfo);
                }
                postData.put("data", guestList);
                System.out.println("data:" + postData.toString());
                if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                    TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
                }
                data = TuYaUtil.execute(token, "/v1.0/hotel/pms/checkin/guest", "POST", postData.toString(), heads);
                System.out.println(JSONObject.fromObject(data).toString());
            }
            //修改入住人
            else if (bussType == 3) {
                for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                    SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                    JSONObject guestInfo = new JSONObject();
                    guestInfo.put("checkin_id", person.getRegistPersonId());
                    guestInfo.put("room_no", smartLockRequest.getRoomNum());
                    guestInfo.put("building", "1");
                    guestInfo.put("floor", "8");
                    guestInfo.put("checkin_time", smartLockRequest.getCheckinTime().getTime());
                    guestInfo.put("checkout_time", smartLockRequest.getCheckoutTime().getTime());
                    guestInfo.put("cust_name", person.getPersonName());
                    guestInfo.put("gender", person.getSex());
                    guestInfo.put("new_phone_no", "86-" + person.getPhone());
                    guestInfo.put("old_phone_no", "86-" + person.getPhone());
                    guestList.add(guestInfo);
                }
                postData.put("data", guestList);
                System.out.println("data:" + postData.toString());
                if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                    TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
                }
                data = TuYaUtil.execute(token, "/v1.0/hotel/pms/checkin/guest", "PUT", postData.toString(), heads);
                System.out.println(JSONObject.fromObject(data).toString());
            }
            //删除入住人
            else if (bussType == 6) {
                for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                    SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                    JSONObject guestInfo = new JSONObject();
                    guestInfo.put("checkin_id", person.getRegistPersonId());
                    guestInfo.put("phone_no", "86-" + person.getPhone());
                    guestList.add(guestInfo);
                }
                postData.put("data", guestList);
                System.out.println("data:" + postData.toString());
                if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                    TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
                }
                data = TuYaUtil.execute(token, "/v1.0/hotel/pms/checkin/guest", "DELETE", postData.toString(), heads);
                System.out.println(JSONObject.fromObject(data).toString());
            }

            JSONObject resultData = JSONObject.fromObject(data);
            if (!resultData.getBoolean("success")) {
                throw new Exception("推送数据到涂鸦失败");
            }
            responseData.setMsg("推送入住数据成功");
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;

    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
                TuYaUtil.accessId = "dh8g3j95fd7e9pqdgdhn";
                TuYaUtil.accessKey = "1ceee2fd7f004c8cbeb4b281b5cc4f81";
            }
            String token = TuYaUtil.getToken();
            System.out.println("token:" + token);
            if (token.equals("")) {
                throw new Exception("获取token失败");
            }
            Map<String, String> heads = new HashMap<>();
            JSONObject postData = new JSONObject();
            postData.put("hotel_code", smartLockRequest.getHid());

            JSONArray guestList = new JSONArray();
            for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                JSONObject guestInfo = new JSONObject();
                guestInfo.put("new_checkin_id", person.getRegistPersonId());
                guestInfo.put("old_checkin_id", person.getRegistPersonId());
                guestInfo.put("room_no", smartLockRequest.getRoomNum());
                guestInfo.put("building", "1");
                guestInfo.put("floor", "8");
                guestInfo.put("checkin_time", smartLockRequest.getCheckinTime().getTime());
                guestInfo.put("checkout_time", smartLockRequest.getCheckoutTime().getTime());
                guestInfo.put("cust_name", person.getPersonName());
                guestInfo.put("gender", person.getSex());
                guestInfo.put("phone_no", "86-" + person.getPhone());
                guestInfo.put("id_card_no", person.getIdCode());
                guestInfo.put("id_card_type", "SFZ");
                guestList.add(guestInfo);
            }

            postData.put("data", guestList);

            System.out.println("data:" + postData.toString());
            if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
            }
            Object data = TuYaUtil.execute(token, "/v1.0/hotel/pms/change-room", "PUT", postData.toString(), heads);
            System.out.println(JSONObject.fromObject(data).toString());
            JSONObject resultData = JSONObject.fromObject(data);
            if (!resultData.getBoolean("success")) {
                throw new Exception("推送入住数据到涂鸦失败");
            }
            responseData.setMsg("推送入住数据成功");

            //推送取电  取电的 accessId 和 accessKey 是用商户的
            //新房间通电
            DeviceControlRequest deviceControlRequest = new DeviceControlRequest();
            deviceControlRequest.setUrl(param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL));
            deviceControlRequest.setProjectId(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PHONE);
            deviceControlRequest.setAccessId(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT);
            deviceControlRequest.setAccessKey(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PASSWORD);
            deviceControlRequest.setRoomNo(smartLockRequest.getRoomNum());
            //1-取电开关控制  2-窗帘开关控制  3-空调开关控制  4-门锁开关控制  5-情景模式控制  6-电视控制  7-灯的控制
            deviceControlRequest.setBussType(1);
            deviceControlRequest.setControlValue(1);
            this.deviceControl(deviceControlRequest);

            //老房间断电
            deviceControlRequest = new DeviceControlRequest();
            deviceControlRequest.setUrl(param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL));
            deviceControlRequest.setProjectId(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PHONE);
            deviceControlRequest.setAccessId(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT);
            deviceControlRequest.setAccessKey(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PASSWORD);
            deviceControlRequest.setRoomNo(smartLockRequest.getOldRoomNo());
            //1-取电开关控制  2-窗帘开关控制  3-空调开关控制  4-门锁开关控制  5-情景模式控制  6-电视控制  7-灯的控制
            deviceControlRequest.setBussType(1);
            deviceControlRequest.setControlValue(0);
            this.deviceControl(deviceControlRequest);

            if (param.containsKey(HOTEL_CONST.SMART_FACE_PUSH_URL) && !StringUtil.isEmpty(param.getString(HOTEL_CONST.SMART_FACE_PUSH_URL)) && param.getString(HOTEL_CONST.SMART_FACE_PUSH_URL).equals("ZhiGuoHuLian")) {
                ZhiGuoHuLian zhiGuoHuLian = new ZhiGuoHuLian();
                zhiGuoHuLian.changeRoom(smartLockRequest, param);
            }

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    public ResponseData facePictur(GuestModel guestModel) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TuYaUtil.url = "https://openapi-cn.wgine.com";
            String token = TuYaUtil.getToken();
            System.out.println("token:" + token);
            if (token.equals("")) {
                throw new Exception("获取token失败");
            }
            Map<String, String> heads = new HashMap<>();
            JSONObject postData = new JSONObject();
            postData.put("hotel_code", guestModel.getHotelCode());
            postData.put("picture", guestModel.getCameraPhoto());
            System.out.println("data:" + postData.toString());
            TuYaUtil.url = "https://openapi-cn.wgine.com";
            Object data = TuYaUtil.execute(token, "/v2.0/hotel/pms/face/picture/score", "POST", postData.toString(), heads);
//            Object data = TuYaUtil.execute1(token, "https://openapi-cn.wgine.com/v2.0/hotel/pms/face/picture/score", "POST", postData.toString(), heads);
            System.out.println(JSONObject.fromObject(data).toString());
            JSONObject resultData = JSONObject.fromObject(data);
            if (!resultData.getBoolean("success")) {
                throw new Exception("照片打分异常");
            }
            responseData.setMsg("照片打分成功");
            double score = resultData.getJSONObject("result").getDouble("score");
            responseData.setData(score);
            responseData.setData1(score);
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    public ResponseData postPictur(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
                TuYaUtil.accessId = "dh8g3j95fd7e9pqdgdhn";
                TuYaUtil.accessKey = "1ceee2fd7f004c8cbeb4b281b5cc4f81";
            }
            String token = TuYaUtil.getToken();
            System.out.println("token:" + token);
            if (token.equals("")) {
                throw new Exception("获取token失败");
            }
            for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                Map<String, String> heads = new HashMap<>();
                SmartLockRequest.RegistPerson person = smartLockRequest.getPeoples().get(i);
                JSONObject postData = new JSONObject();
                postData.put("sn", smartLockRequest.getHid());
                postData.put("checkin_id", person.getRegistPersonId());
                postData.put("brand_code", "vF3u9xfxIn488NRqjI6I");
                postData.put("room_no", smartLockRequest.getRoomNum());
                postData.put("phone_no", "86-" + person.getPhone());
                postData.put("face_picture_url", "https://hotel-file-1258829933.cos.ap-shanghai.myqcloud.com/2126/room-type/0c5ff5d4b051415291ff81e50c37867e.jpg");
                System.out.println("data:" + postData.toString());
                if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                    TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
                }
                Object data = TuYaUtil.execute(token, "/v1.0/hotel/pms/face/picture", "POST", postData.toString(), heads);
                System.out.println(JSONObject.fromObject(data).toString());
                JSONObject resultData = JSONObject.fromObject(data);
                if (!resultData.getBoolean("success")) {
                    throw new Exception("推送入住数据到涂鸦失败");
                }
                responseData.setMsg("推送人脸数据成功");
            }

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public ResponseData roomInfo(SmartRoomInfoRequest smartRoomInfoRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
                TuYaUtil.accessId = "dh8g3j95fd7e9pqdgdhn";
                TuYaUtil.accessKey = "1ceee2fd7f004c8cbeb4b281b5cc4f81";
            }
            String token = TuYaUtil.getToken();
            System.out.println("token:" + token);
            if (token.equals("")) {
                throw new Exception("获取token失败");
            }
            Map<String, String> heads = new HashMap<>();
            JSONObject postData = new JSONObject();
            postData.put("hotel_code", smartRoomInfoRequest.getHid());
            Integer bussType = smartRoomInfoRequest.getBussType();
            Object data = null;
            JSONArray rooms = new JSONArray();
            for (int i = 0; i < smartRoomInfoRequest.getRoomInfoList().size(); i++) {
                JSONObject room = new JSONObject();
                room.put("room_no", smartRoomInfoRequest.getRoomInfoList().get(i).getRoomNo());
                room.put("floor", smartRoomInfoRequest.getRoomInfoList().get(i).getFloor());
                room.put("building", smartRoomInfoRequest.getRoomInfoList().get(i).getBuilding());
                room.put("old_room_no", smartRoomInfoRequest.getRoomInfoList().get(i).getOldRoomNo());
                room.put("room_name", smartRoomInfoRequest.getRoomInfoList().get(i).getRoomName());
                room.put("room_type", smartRoomInfoRequest.getRoomInfoList().get(i).getRoomType());
                room.put("tel", smartRoomInfoRequest.getRoomInfoList().get(i).getTel());
                room.put("description", smartRoomInfoRequest.getRoomInfoList().get(i).getDescription());
                rooms.add(room);
            }
            postData.put("rooms", rooms);
            System.out.println("data:" + postData.toString());
            if (null != param.get(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL) && !param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL).equals("")) {
                TuYaUtil.url = param.getString(HOTEL_CONST.SMART_DEVICE_CONTROL_PUSH_URL);
            }
            //添加房间
            if (bussType == 1) {
                data = TuYaUtil.execute(token, "/v1.0/hotel/pms/rooms", "POST", postData.toString(), heads);
            }
            //修改房间
            else if (bussType == 2) {
                data = TuYaUtil.execute(token, "/v1.0/hotel/pms/rooms/sync", "POST", postData.toString(), heads);
            }
            //删除房间
            else if (bussType == 3) {
                data = TuYaUtil.execute(token, "/v2.0/hotel/pms/rooms", "DELETE", postData.toString(), heads);
            }
            System.out.println(JSONObject.fromObject(data).toString());
            JSONObject resultData = JSONObject.fromObject(data);
            if (!resultData.getBoolean("success")) {
                throw new Exception("推送数据到涂鸦失败");
            }
            responseData.setMsg("推送入住数据成功");

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }


    public ResponseData deviceControl(DeviceControlRequest deviceControlRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            System.out.println("设备控制参数：" + JSONObject.fromObject(deviceControlRequest));
            if (null != deviceControlRequest.getUrl() && !deviceControlRequest.getUrl().equals("")) {
                TuYaDevUtil.url = deviceControlRequest.getUrl();
                TuYaDevUtil.accessId = deviceControlRequest.getAccessId();
                TuYaDevUtil.accessKey = deviceControlRequest.getAccessKey();
            }
            String token = TuYaDevUtil.getToken();
            System.out.println("token:" + token);
            if (token.equals("")) {
                throw new Exception("获取token失败");
            }
            Map<String, String> heads = new HashMap<>();
            JSONObject postData = new JSONObject();
            postData.put("hotel_code", deviceControlRequest.getHid());
            System.out.println("data:" + postData.toString());
            if (null != deviceControlRequest.getUrl() && !deviceControlRequest.getUrl().equals("")) {
                TuYaDevUtil.url = deviceControlRequest.getUrl();
                TuYaDevUtil.accessId = deviceControlRequest.getAccessId();
                TuYaDevUtil.accessKey = deviceControlRequest.getAccessKey();
            }

            String roomNo = deviceControlRequest.getRoomNo();
            String room_id = "";
//          /v1.0/osaas/projects/{project_id}/rooms  查询酒店房间列表
            Object data = TuYaDevUtil.execute(token, "/v1.0/osaas/projects/" + deviceControlRequest.getProjectId() + "/rooms?page_no=1&page_size=100", "GET", "", new HashMap<>());
            System.out.println(JSONObject.fromObject(data).toString());
            //遍历房间列表找到房间room_id
            JSONObject resultData = JSONObject.fromObject(data);
            if (!resultData.getBoolean("success") || resultData.getJSONObject("result").getJSONArray("rooms").size() < 1) {
                throw new Exception("操作失败");
            }
            for (int i = 0; i < resultData.getJSONObject("result").getJSONArray("rooms").size(); i++) {
                JSONObject roomInfo = resultData.getJSONObject("result").getJSONArray("rooms").getJSONObject(i);
                String room_no = roomInfo.getString("room_no");
                if (room_no.equals(roomNo)) {
                    room_id = roomInfo.getString("room_id");
                    break;
                }
            }
            if (room_id.equals("") || null == room_id) {
                throw new Exception("操作失败,未查询到房间信息！");
            }
            TuYaDevUtil.url = deviceControlRequest.getUrl();
            TuYaDevUtil.accessId = deviceControlRequest.getAccessId();
            TuYaDevUtil.accessKey = deviceControlRequest.getAccessKey();
            //v1.0/osaas/rooms/{room_id}/devices	分页查询房屋下的设备列表
            data = TuYaDevUtil.execute(token, "/v1.0/osaas/rooms/" + room_id + "/devices", "GET", "", new HashMap<>());
            resultData = JSONObject.fromObject(data);
            System.out.println(resultData.toString());
            if (!resultData.getBoolean("success") || resultData.getJSONObject("result").getJSONArray("devices").size() < 1) {
                throw new Exception("操作失败");
            }

            //根据业务类型查询设备

//            String device_id = deviceControlRequest.getDeviceId();

            String device_id = "";


//            String device_id = deviceControlRequest.getDeviceId();


            //查询功能集 /v1.0/osaas/devices/{device_id}/functions 按照设备
//            data = TuYaUtil.execute(token, "/v1.0/osaas/devices/" + device_id + "/functions", "GET", "", new HashMap<>());
//            System.out.println(JSONObject.fromObject(data).toString());

            ///v1.0/osaas/devices/{device_id}/status  获取设备最新状态
//            data = TuYaUtil.execute(token, "/v1.0/osaas/devices/" + device_id + "/status", "GET", "", new HashMap<>());
//            System.out.println(JSONObject.fromObject(data).toString());

            //获取业务类型
            Integer bussType = deviceControlRequest.getBussType();
            //组装命令
            JSONArray commands = new JSONArray();
            //控制指令
            JSONObject command = new JSONObject();
            //取电开关
            if (bussType == 1) {
                for (int i = 0; i < resultData.getJSONObject("result").getJSONArray("devices").size(); i++) {
                    JSONObject deviceInfo = resultData.getJSONObject("result").getJSONArray("devices").getJSONObject(i);
                    if (deviceInfo.getString("category_code").equals("ckqdkg")) {
                        device_id = deviceInfo.getString("device_id");
                        break;
                    }
                }
                if (null == device_id || device_id.equals("")) {
                    throw new Exception("操作失败，未查询到设备信息");
                }
                command.put("code", "switch_1");
                command.put("value", deviceControlRequest.getControlValue() == 1 ? true : false);
                commands.add(command);
            } else if (bussType == 2) {
                //现在无法区分是窗帘 还是窗纱
                for (int i = 0; i < resultData.getJSONObject("result").getJSONArray("devices").size(); i++) {
                    JSONObject deviceInfo = resultData.getJSONObject("result").getJSONArray("devices").getJSONObject(i);
                    if (deviceInfo.getString("name").equals("窗帘")) {
                        device_id = deviceInfo.getString("device_id");
                        break;
                    }
                }
                if (null == device_id || device_id.equals("")) {
                    throw new Exception("操作失败，未查询到设备信息");
                }
                //窗帘
                command.put("code", "control");
                command.put("value", deviceControlRequest.getControlValue() == 1 ? "open" : "close");
            } else if (bussType == 3) {
                for (int i = 0; i < resultData.getJSONObject("result").getJSONArray("devices").size(); i++) {
                    JSONObject deviceInfo = resultData.getJSONObject("result").getJSONArray("devices").getJSONObject(i);
                    if (deviceInfo.getString("category_code").equals("infrared_ac")) {
                        device_id = deviceInfo.getString("device_id");
                        break;
                    }
                }
                if (null == device_id || device_id.equals("")) {
                    throw new Exception("操作失败，未查询到设备信息");
                }
                //空调
                command.put("code", "PowerOn");
                command.put("value", deviceControlRequest.getControlValue() == 1 ? "PowerOn" : "PowerOn");
            } else if (bussType == 8) {
                //现在无法区分是窗帘 还是窗纱
                for (int i = 0; i < resultData.getJSONObject("result").getJSONArray("devices").size(); i++) {
                    JSONObject deviceInfo = resultData.getJSONObject("result").getJSONArray("devices").getJSONObject(i);
                    if (deviceInfo.getString("name").equals("窗纱")) {
                        device_id = deviceInfo.getString("device_id");
                        break;
                    }
                }
                if (null == device_id || device_id.equals("")) {
                    throw new Exception("操作失败，未查询到设备信息");
                }
                //窗帘
                command.put("code", "control");
                command.put("value", deviceControlRequest.getControlValue() == 1 ? "open" : "close");
            }


//            JSONObject values = new JSONObject();
//            ArrayList<String> range = new ArrayList<>();
//            range.add("open");
//            values.put("range", range);

            commands.add(command);
            postData = new JSONObject();

            postData.put("device_id", device_id);
            postData.put("commands", commands);
//
            System.out.println(postData.toString());
            //POST /v1.0/osaas/devices/{device_id}/commands 下发设备指令
            data = TuYaDevUtil.execute(token, "/v1.0/osaas/devices/" + device_id + "/commands", "POST", postData.toString(), heads);
            System.out.println(JSONObject.fromObject(data).toString());
            responseData.setMsg("设备控制成功");
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public ResponseData getDeviceState(DeviceControlRequest deviceControlRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (null != deviceControlRequest.getUrl() && !deviceControlRequest.getUrl().equals("")) {
                TuYaDevUtil.url = deviceControlRequest.getUrl();
                TuYaDevUtil.accessId = deviceControlRequest.getAccessId();
                TuYaDevUtil.accessKey = deviceControlRequest.getAccessKey();
            }
            String token = TuYaDevUtil.getToken();
            System.out.println("token:" + token);
            if (token.equals("")) {
                throw new Exception("获取token失败");
            }
            Map<String, String> heads = new HashMap<>();
            JSONObject postData = new JSONObject();
            postData.put("hotel_code", deviceControlRequest.getHid());
            System.out.println("data:" + postData.toString());
            if (null != deviceControlRequest.getUrl() && !deviceControlRequest.getUrl().equals("")) {
                TuYaDevUtil.url = deviceControlRequest.getUrl();
                TuYaDevUtil.accessId = deviceControlRequest.getAccessId();
                TuYaDevUtil.accessKey = deviceControlRequest.getAccessKey();
            }


            String roomNo = deviceControlRequest.getRoomNo();
            String room_id = "";
//          /v1.0/osaas/projects/{project_id}/rooms  查询酒店房间列表
            Object data = TuYaDevUtil.execute(token, "/v1.0/osaas/projects/" + deviceControlRequest.getProjectId() + "/rooms?page_no=1&page_size=100", "GET", "", new HashMap<>());
            System.out.println(JSONObject.fromObject(data).toString());
            //遍历房间列表找到房间room_id
            JSONObject resultData = JSONObject.fromObject(data);
            if (!resultData.getBoolean("success") || resultData.getJSONObject("result").getJSONArray("rooms").size() < 1) {
                throw new Exception("操作失败");
            }
            for (int i = 0; i < resultData.getJSONObject("result").getJSONArray("rooms").size(); i++) {
                JSONObject roomInfo = resultData.getJSONObject("result").getJSONArray("rooms").getJSONObject(i);
                String room_no = roomInfo.getString("room_no");
                if (room_no.equals(roomNo)) {
                    room_id = roomInfo.getString("room_id");
                    break;
                }
            }
            if (room_id.equals("") || null == room_id) {
                throw new Exception("操作失败,未查询到房间信息！");
            }

            //v1.0/osaas/rooms/{room_id}/devices	分页查询房屋下的设备列表
            data = TuYaDevUtil.execute(token, "/v1.0/osaas/rooms/" + room_id + "/devices", "GET", "", new HashMap<>());
            resultData = JSONObject.fromObject(data);
            System.out.println(resultData.toString());
            if (!resultData.getBoolean("success") || resultData.getJSONObject("result").getJSONArray("devices").size() < 1) {
                throw new Exception("操作失败");
            }

            //获取业务类型
            Integer bussType = deviceControlRequest.getBussType();
            String device_id = "";
            //取电开关
            if (bussType == 1) {
                for (int i = 0; i < resultData.getJSONObject("result").getJSONArray("devices").size(); i++) {
                    JSONObject deviceInfo = resultData.getJSONObject("result").getJSONArray("devices").getJSONObject(i);
                    if (deviceInfo.getString("category_code").equals("ckqdkg")) {
                        device_id = deviceInfo.getString("device_id");
                        break;
                    }
                }
                if (null == device_id || device_id.equals("")) {
                    throw new Exception("操作失败，未查询到设备信息");
                }
            } else if (bussType == 2) {
                //现在无法区分是窗帘 还是窗纱
                for (int i = 0; i < resultData.getJSONObject("result").getJSONArray("devices").size(); i++) {
                    JSONObject deviceInfo = resultData.getJSONObject("result").getJSONArray("devices").getJSONObject(i);
                    if (deviceInfo.getString("name").equals("窗帘")) {
                        device_id = deviceInfo.getString("device_id");
                        break;
                    }
                }
                if (null == device_id || device_id.equals("")) {
                    throw new Exception("操作失败，未查询到设备信息");
                }
            } else if (bussType == 3) {
                for (int i = 0; i < resultData.getJSONObject("result").getJSONArray("devices").size(); i++) {
                    JSONObject deviceInfo = resultData.getJSONObject("result").getJSONArray("devices").getJSONObject(i);
                    if (deviceInfo.getString("category_code").equals("infrared_ac")) {
                        device_id = deviceInfo.getString("device_id");
                        break;
                    }
                }
                if (null == device_id || device_id.equals("")) {
                    throw new Exception("操作失败，未查询到设备信息");
                }
            } else if (bussType == 8) {
                //现在无法区分是窗帘 还是窗纱
                for (int i = 0; i < resultData.getJSONObject("result").getJSONArray("devices").size(); i++) {
                    JSONObject deviceInfo = resultData.getJSONObject("result").getJSONArray("devices").getJSONObject(i);
                    if (deviceInfo.getString("name").equals("窗纱")) {
                        device_id = deviceInfo.getString("device_id");
                        break;
                    }
                }
                if (null == device_id || device_id.equals("")) {
                    throw new Exception("操作失败，未查询到设备信息");
                }
            }
            //门锁
            else if (bussType == 4) {
                //POST /v1.0/devices/{device_id}/door-lock/password-ticket


            }


            //根据业务类型查询设备
            ///v1.0/osaas/devices/{device_id}/status  获取设备最新状态
            data = TuYaDevUtil.execute(token, "/v1.0/osaas/devices/" + device_id + "/status", "GET", "", new HashMap<>());
            System.out.println(JSONObject.fromObject(data).toString());

            if (bussType == 1) {
                //{"result":[{"code":"switch_1","value":"true"},{"code":"timer_cut","value":"0"}],"success":true,"t":1.678518185383E12,"tid":"c551d6b6bfda11eda9288209b3668e38"}
                JSONObject result = JSONObject.fromObject(data);
                if (!result.containsKey("result") || result.getJSONArray("result").size() < 1) {
                    responseData.setData(0);
                }
                for (int i = 0; i < result.getJSONArray("result").size(); i++) {
                    JSONObject deviceState = result.getJSONArray("result").getJSONObject(i);
                    if (deviceState.getString("code").equals("switch_1")) {
                        responseData.setData(deviceState.getString("value").equals("true") ? 1 : 0);
                    }
                }
            }


            if (responseData.getData() == null) {
                responseData.setData(0);
            }

            responseData.setMsg("查询设备状态成功");
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

}
