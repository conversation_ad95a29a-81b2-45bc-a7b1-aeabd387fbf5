package com.pms.czabspoliceinterface.lock.factory;

import cn.hutool.http.HttpRequest;
import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czpmsutils.*;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.request.SmartLockRequest;
import com.pms.pmsorder.bean.SmartLockRecord;
import net.sf.json.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;

public class ChaiJiaKeJi implements IPmsLock {

    private static final Logger log = LoggerFactory.getLogger(ChaiJiaKeJi.class);
    /**
     * 下载文件---返回下载后的文件存储路径
     *
     * @param url      文件地址
     * @param dir      存储目录
     * @param fileName 存储文件名
     * @return
     */
    public static String downloadHttpUrl(String url, String dir, String fileName) {
        try {
            File dirfile = new File(dir);
            if (!dirfile.exists()) {
                dirfile.mkdirs();
            }
            SSLConnectionSocketFactory scsf = new SSLConnectionSocketFactory(
                    SSLContexts.custom().loadTrustMaterial(null, new TrustSelfSignedStrategy()).build(),
                    NoopHostnameVerifier.INSTANCE);
            CloseableHttpClient client = HttpClients.custom().setSSLSocketFactory(scsf).build();
            HttpGet httpget = new HttpGet(url);
            HttpResponse response = client.execute(httpget);
            HttpEntity entity = response.getEntity();
            InputStream is = entity.getContent();

            int cache = 10 * 1024;
            FileOutputStream fileout = new FileOutputStream(dir + "/" + fileName);
            byte[] buffer = new byte[cache];
            int ch = 0;
            while ((ch = is.read(buffer)) != -1) {
                fileout.write(buffer, 0, ch);
            }
            is.close();
            fileout.flush();
            fileout.close();
            ImageHelper.scaleImageWithParams(dir + "/" + fileName, dir + "/new" + fileName , 640, 480, true, "jpg");
            String s = TransformPhotoToBase64Data(dir + "/new", fileName);
            UUID uuid = UUID.randomUUID();
            CosFileUtil.UploadObjectRsp dir1 = CosFileUtil.uploadObjectBase64(s, 0, "dir", "image/jpg", uuid + ".jpeg");
            return dir1.getUrl();

        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }


    public static String TransformPhotoToBase64Data(String LoadPath, String DataName) {

        System.out.println("加载路径" + LoadPath);
        Base64.Encoder encoder = Base64.getEncoder();  //获取Base64编码器
        byte[] ImgContainer = null;    //数据集缓存器
        FileInputStream fileInputStream = null; //文件输入流
        try {
            System.out.println(LoadPath + DataName);
            fileInputStream = new FileInputStream(LoadPath + DataName);    //到指定路径寻找文件
            ImgContainer = new byte[fileInputStream.available()];          //设置图片字节数据缓冲区大小
            fileInputStream.read(ImgContainer);           //将数据流中的图片数据读进缓冲区
            String Base64ImgData = encoder.encodeToString(ImgContainer);  //将图片编码转换成Base64格式的数据集
            fileInputStream.close();      //关闭数据流
            return Base64ImgData;  //将缓冲区数据转换成字符数据返回
        } catch (FileNotFoundException e) {
            return "找不到指定文件!";
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "null";
    }

    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        SmartLockRecord smartLockRecord = new SmartLockRecord();
        try {
            String clientKey = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_KEY);
            String clientId = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_CODE);
            String url = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_URL);
            String projectId = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT);
            List<SmartLockRequest.RegistPerson> peoples = smartLockRequest.getPeoples();
            if (null == peoples || peoples.size() < 1) {
                throw new Exception(HOTEL_CONST.DATA_LIST_IS_NULL);
            }
            for (int i = 0; i < peoples.size(); i++) {
                SmartLockRequest.RegistPerson registPerson = peoples.get(i);
                smartLockRecord.setHid(smartLockRequest.getHid());
                smartLockRecord.setRegistId(smartLockRequest.getRegistId());
                smartLockRecord.setBookingOrderId(smartLockRequest.getBookingOrderId());
                smartLockRecord.setRoomNo(smartLockRequest.getRoomNum());
                smartLockRecord.setBusinessType(1);
                smartLockRecord.setRegistPersonId(registPerson.getRegistPersonId());
                smartLockRecord.setCreateTime(new Date());
                smartLockRecord.setLockFactory("ChaiJiaKeJi");
                smartLockRecord.setHotelGroupId(smartLockRequest.getHotelGroupId());
                Map<String, Object> postData = new JSONObject();
                postData.put("hotelId", projectId);
                postData.put("deviceSn", smartLockRequest.getRoomNum());
                if (null == registPerson.getCameraImage() || "".equals(registPerson.getCameraImage())) {
                    throw new Exception("照片数据为空");
                }
                String pic = "https://hotel-file-1258829933.cos.ap-shanghai.myqcloud.com/" + registPerson.getCameraImage();
                String path = downloadHttpUrl(pic, "/home/<USER>/", HotelUtils.getUUID() + ".jpg");
                String image2Base64 = ImageUtils.image2Base64(path);
                postData.put("faceImg", image2Base64);
                smartLockRecord.setPostUrl(url + "/faceInfo/uploadFace");
                log.info("柴家科技 url:{} clientId:{}   postData:{}", url + "/faceInfo/uploadFace", clientId, postData.toString());
                String res = HttpRequest.post(url + "/faceInfo/uploadFace").header("clientId", clientId).header("clientKey", clientKey).form(postData).timeout(5000).execute().body();
                log.info("柴家科技 res :{}", res);
                smartLockRecord.setResult(res);
                JSONObject result = JSONObject.fromObject(res);
                if (null == result || result.equals("") || !result.containsKey("code") || !result.getString("code").equals("0")) {
                    throw new Exception("下发人脸异常");
                }
                smartLockRecord.setResultCode(1);
                smartLockRecord.setResult("下发人脸成功");
                String personGuid = result.getJSONObject("data").getString("personGuid");
            }
            responseData.setCode(1);
            responseData.setData("下发人脸成功");
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            smartLockRecord.setResultCode(0);
            smartLockRecord.setResult(e.getMessage());
        } finally {
            responseData.setData1(smartLockRecord);
        }
        return responseData;
    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        SmartLockRecord smartLockRecord = new SmartLockRecord();
        try {
            String clientKey = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_KEY);
            String clientId = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_CODE);
            String url = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_URL);
            String projectId = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT);
            List<SmartLockRequest.RegistPerson> peoples = smartLockRequest.getPeoples();
            if (null == peoples || peoples.size() < 1) {
                throw new Exception(HOTEL_CONST.DATA_LIST_IS_NULL);
            }
            Map<String, Object> postData = new JSONObject();
            postData.put("hotelId", projectId);
            postData.put("deviceSn", peoples.get(0).getRoomNum());
            smartLockRecord.setHid(peoples.get(0).getHid());
            smartLockRecord.setRegistId(peoples.get(0).getRegistId());
            smartLockRecord.setBookingOrderId(peoples.get(0).getBookingOrderId());
            smartLockRecord.setRoomNo(peoples.get(0).getRoomNum());
            smartLockRecord.setBusinessType(1);
            smartLockRecord.setRegistPersonId(peoples.get(0).getRegistPersonId());
            smartLockRecord.setCreateTime(new Date());
            smartLockRecord.setLockFactory("ChaiJiaKeJi");
            smartLockRecord.setHotelGroupId(smartLockRequest.getHotelGroupId());
            smartLockRecord.setPostUrl(url + "/faceInfo/deleteFace");
            log.info("柴家科技 url:{} clientId:{}  clientKey {}  postData:{}", url + "/faceInfo/uploadFace", clientId, clientKey, postData.toString());
            String res = HttpRequest.post(url + "/faceInfo/deleteFace").header("clientId", clientId).header("clientKey", clientKey).form(postData).timeout(5000).execute().body();
            log.info("柴家科技 res :{}", res);
            smartLockRecord.setResult(res);
            responseData.setData("取消成功");
            responseData.setData1("取消成功");
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
            smartLockRecord.setResultCode(0);
            smartLockRecord.setResult(e.getMessage());
        }finally {
            responseData.setData1(smartLockRecord);
        }
        return responseData;
    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }
}
