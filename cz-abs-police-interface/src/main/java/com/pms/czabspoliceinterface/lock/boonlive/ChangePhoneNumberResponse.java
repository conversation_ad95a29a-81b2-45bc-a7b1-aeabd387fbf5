
package com.pms.czabspoliceinterface.lock.boonlive;

import javax.xml.bind.annotation.*;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ChangePhoneNumberResult" type="{http://www.w3.org/2001/XMLSchema}boolean"/>
 *         &lt;element name="errorMsg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "changePhoneNumberResult",
    "errorMsg"
})
@XmlRootElement(name = "ChangePhoneNumberResponse")
public class ChangePhoneNumberResponse {

    @XmlElement(name = "ChangePhoneNumberResult")
    protected boolean changePhoneNumberResult;
    protected String errorMsg;

    /**
     * 获取changePhoneNumberResult属性的值。
     * 
     */
    public boolean isChangePhoneNumberResult() {
        return changePhoneNumberResult;
    }

    /**
     * 设置changePhoneNumberResult属性的值。
     * 
     */
    public void setChangePhoneNumberResult(boolean value) {
        this.changePhoneNumberResult = value;
    }

    /**
     * 获取errorMsg属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getErrorMsg() {
        return errorMsg;
    }

    /**
     * 设置errorMsg属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setErrorMsg(String value) {
        this.errorMsg = value;
    }

}
