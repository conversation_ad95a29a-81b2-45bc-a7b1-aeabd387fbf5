package com.pms.czabspoliceinterface.lock.bean;

import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.request.SmartLockRequest;
import net.sf.json.JSONObject;

public interface IPmsLock {
    //入住
    ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param);
    //退房
    ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param);
    //续住
    ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param);
    //更新信息
    ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param);
    //换房
    ResponseData changeRoom(SmartLockRequest smartLockRequest, JSO<PERSON>Object param);
}
