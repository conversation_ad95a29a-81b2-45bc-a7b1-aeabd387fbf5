package com.pms.czabspoliceinterface.lock.factory;

import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.HttpRequest;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.StringUtil;
import com.pms.czpmsutils.request.SmartLockRequest;
import net.sf.json.JSONObject;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.net.URLConnection;
import java.security.MessageDigest;
import java.util.Base64;

public class AnZhuBao implements IPmsLock {
    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        String token = getToken(param);
        try {
            String url = param.getString("575");


            if (true) {
                JSONObject postData = new JSONObject();
                postData.put("method", "apartmentReadCardNo");
                postData.put("tokenId", token);
                postData.put("data", new JSONObject());
                System.out.println(postData.toString());
                String result = sendPost(url, postData, null);
                JSONObject resultData = JSONObject.fromObject(result);
                if (!resultData.containsKey("resultCode") || resultData.getInt("resultCode") != 0) {
                    throw new Exception("读取卡号失败：" + result);
                }
                String cardNo = resultData.getJSONObject("data").getString("cardNo");
                postData = new JSONObject();
                postData.put("method", "apartmentAddOffLineCardKey");
                postData.put("tokenId", token);
                JSONObject data = new JSONObject();
                data.put("roomId", smartLockRequest.getLockNo());
                data.put("beginTime", smartLockRequest.getCheckinTime().getTime() / 1000);
                data.put("endTime", smartLockRequest.getCheckoutTime().getTime() / 1000);
                data.put("keyName", "11111");
                data.put("cardNo", cardNo);
                postData.put("data", data);
                result = sendPost(url, postData, null);
                resultData = JSONObject.fromObject(result);
                if (!resultData.containsKey("resultCode") || resultData.getInt("resultCode") != 0) {
                    throw new Exception("制卡失败：" + result);
                }
            } else {
                JSONObject postData = new JSONObject();
                postData.put("method", "apartmentAddOffLineKey");
                postData.put("tokenId", token);
                JSONObject data = new JSONObject();
                data.put("roomId", smartLockRequest.getLockNo());
                data.put("beginTime", smartLockRequest.getCheckinTime().getTime() / 1000);
                data.put("endTime", smartLockRequest.getCheckoutTime().getTime() / 1000);
                data.put("keyName", "离线密码");
                data.put("authorMode", 1);
                postData.put("data", data);

                System.out.println(postData.toString());
                String result = sendPost(url, postData, "1.1");
                JSONObject resultData = JSONObject.fromObject(result);
                if (!resultData.containsKey("resultCode") || resultData.getInt("resultCode") != 0) {
                    throw new Exception("制卡失败：" + result);
                }
                String password = resultData.getJSONObject("data").getString("password");

                String word = decryptKey(token, password);
                System.out.println(word);
            }


        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
        }
        return responseData;
    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        String token = getToken(param);
        try {
            String url = param.getString("575");
            JSONObject postData = new JSONObject();
            postData.put("method", "apartmentReadCardNo");
            postData.put("tokenId", token);
            postData.put("data", new JSONObject());
            String result = HttpRequest.sendPost(url, postData);
            System.out.println(result);
            JSONObject resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("resultCode") || resultData.getInt("resultCode") != 0) {
                throw new Exception("读取卡号失败：" + result);
            }
            String cardNo = resultData.getJSONObject("data").getString("cardNo");
            postData = new JSONObject();
            postData.put("method", "apartmentCardManage");
            postData.put("tokenId", token);
            JSONObject data = new JSONObject();
            data.put("cardNo", cardNo);
            postData.put("data", data);
            result = HttpRequest.sendPost(url, postData);
            resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("resultCode") || resultData.getInt("resultCode") != 0) {
                throw new Exception("制卡失败：" + result);
            }

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
        }
        return responseData;
    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    public ResponseData readCard(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        String token = getToken(param);
        try {
            String url = param.getString("575");
            JSONObject postData = new JSONObject();
            postData.put("method", "apartmentReadCardNo");
            postData.put("tokenId", token);
            postData.put("data", new JSONObject());
            System.out.println(postData.toString());
            String result = sendPost(url, postData, null);
            JSONObject resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("resultCode") || resultData.getInt("resultCode") != 0) {
                throw new Exception("读取卡号失败：" + result);
            }

            if (!resultData.getJSONObject("data").containsKey("list") || resultData.getJSONObject("data").getJSONArray("list").size() < 1) {
                throw new Exception("读取卡号失败：" + result);
            }
            String lockNo = resultData.getJSONObject("data").getJSONArray("list").getJSONObject(0).getString("roomId");
            String beginTime = HotelUtils.getTime(resultData.getJSONObject("data").getJSONArray("list").getJSONObject(0).getLong("beginTime") * 1000);
            String endTime = HotelUtils.getTime(resultData.getJSONObject("data").getJSONArray("list").getJSONObject(0).getLong("endTime") * 1000);
            String cardNo = resultData.getJSONObject("data").getString("cardNo");

            System.out.println(cardNo);

        } catch (Exception e) {
            e.printStackTrace();
            responseData.setCode(-1);
        }
        return responseData;
    }


    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    public static String getToken(JSONObject param) {
        String token = "";
        try {
            String url = param.getString("575");
            String userName = param.getString("579");
            String password = param.getString("578");
            JSONObject postData = new JSONObject();
            postData.put("method", "apartmentLogin");
            JSONObject data = new JSONObject();
            data.put("accountName", userName);
            data.put("password", password);
            postData.put("data", data);
            postData.put("method", "apartmentLogin");
            String result = sendPost(url, postData, null);
            JSONObject resultData = JSONObject.fromObject(result);
            if (!resultData.containsKey("resultCode") || resultData.getInt("resultCode") != 0) {
                throw new Exception("获取token失败：" + result);
            }
            token = resultData.getJSONObject("data").getString("tokenId");

        } catch (Exception e) {
            e.printStackTrace();
            token = "";

        }
        return token;
    }

    public static String sendPost(String url, JSONObject param, String v) {
        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");

            if (!StringUtil.isEmpty(v)) {
                conn.setRequestProperty("Content-Version", v);
            }
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent",
                    "Mozilla/5.0 (compatible; MSIE 6.1; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type", "application/json");
            // 发送POST请求必须设置如下两行

            conn.setDoOutput(true);


            conn.setDoInput(true);

            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数

            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            System.out.println(result);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
        //使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result;
    }

    public static String decryptKey(String tokenId, String pw) throws Exception {

// MD5加密

        byte[] md5Byte = MessageDigest.getInstance("MD5").digest(tokenId.getBytes());

// 转为16进制字符串

        String hexStr = bytesToHex(md5Byte);

// 截取

        String aesKey = hexStr.substring(8, 24);

// AES解密，返回结果

        return decrypt(pw, aesKey, aesKey);

    }

    private static String bytesToHex(byte[] bytes) {

        StringBuilder sb = new StringBuilder(bytes.length * 2);

        for (int i = 0; i < bytes.length; i++) {

            sb.append(Character.toUpperCase(Character.forDigit((bytes[i] >> 4) & 0x0F, 16)));

            sb.append(Character.toUpperCase(Character.forDigit(bytes[i] & 0x0F, 16)));

        }

        return sb.toString();

    }

    private static String decrypt(String pw, String aesKey, String viPara) throws Exception {

        IvParameterSpec zeroIv = new IvParameterSpec(viPara.getBytes());

        SecretKeySpec key = new SecretKeySpec(aesKey.getBytes(), "AES");

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");

        cipher.init(Cipher.DECRYPT_MODE, key, zeroIv);

        return new String(cipher.doFinal(Base64.getDecoder().decode(pw)), "UTF-8");

    }
}
