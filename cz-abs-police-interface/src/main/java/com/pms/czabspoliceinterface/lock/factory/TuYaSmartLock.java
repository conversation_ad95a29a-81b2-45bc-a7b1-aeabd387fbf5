package com.pms.czabspoliceinterface.lock.factory;

import com.pms.czabspoliceinterface.utils.TuYaUtil;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.request.OpenDoorRequest;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;


@Service
public class TuYaSmartLock extends BaseService {
    private static final Logger log = LoggerFactory.getLogger(TuYaSmartLock.class);
    public ResponseData getSmartLockPassword() {
        ResponseData responseData = ResponseData.newSuccessData();

        return responseData;
    }

    public static void main(String[] args) {
        TuYaSmartLock tuYaSmartLock = new TuYaSmartLock();
        OpenDoorRequest openDoorRequest = new OpenDoorRequest();
        openDoorRequest.setCode("e117b6d9d5d3571a3c4bc0ddf2fbb16a");
        openDoorRequest.setUuid("6c408e1299d94440b1kvx1");
        tuYaSmartLock.openDoor(openDoorRequest);
    }

    public ResponseData openDoor(OpenDoorRequest openDoorRequest) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            TuYaUtil.url = "https://openapi.tuyacn.com";
            TuYaUtil.accessId = "nwtxyj7g3n9gmy7eh3vs";
            TuYaUtil.accessKey = "e8241ad015924b4c93e0815b3fd2976f";
            String token = getToken();
            /**  1699735828919492677 code：cdce1c732de129f76862cbd1f623df35 -门店授权给主账号的实体ID  proId 雀二代
             * 做三方转换   /v1.0/iot-03/1699692551155490832/associates GET
             */
//            String proId = "1699735828919492677";
//            String code = "cdce1c732de129f76862cbd1f623df35";
            String device_id = openDoorRequest.getUuid();

//            String url = "/v1.0/iot-03/" + openDoorRequest.getProId() + "/associates";
//            TuYaUtil.url = "https://openapi.tuyacn.com";
//            TuYaUtil.accessId = "dh8g3j95fd7e9pqdgdhn";
//            TuYaUtil.accessKey = "1ceee2fd7f004c8cbeb4b281b5cc4f81";
//            String data = TuYaUtil.execute(token, url, "GET", "", new HashMap<>());
//            JSONObject result = JSONObject.fromObject(data);
//            System.out.println(result.toString());
//            if (!result.containsKey("success") || !result.getBoolean("success")) {
//                throw new Exception("获取associates失败");
//            }
//
//            String associate_id = result.getJSONArray("result").getJSONObject(0).getString("associate_id");
//            if (StringUtil.isEmpty(associate_id)) {
//                throw new Exception("获取associate_id失败");
//            }
            String associate_id = "1699692652800249883";
            openDoorRequest.setCode("e117b6d9d5d3571a3c4bc0ddf2fbb16a");
            String auth = "/v1.0/authorize_token?grant_type=3&code=" + openDoorRequest.getCode() + "&associate_id=" + associate_id;
            TuYaUtil.url = "https://openapi.tuyacn.com";
            TuYaUtil.accessId = "nwtxyj7g3n9gmy7eh3vs";
            TuYaUtil.accessKey = "e8241ad015924b4c93e0815b3fd2976f";
            String data = TuYaUtil.execute(token, auth, "GET", "", new HashMap<>());
            JSONObject result = JSONObject.fromObject(data);
            System.out.println(result.toString());

            if (!result.containsKey("success") || !result.getBoolean("success")) {
                throw new Exception("获取token失败");
            }
            token = result.getJSONObject("result").getString("access_token");
            /**
             * 第一步先获取临时密钥 POST /v1.0/devices/{device_id}/door-lock/password-ticket
             */
            JSONObject postData = new JSONObject();
            //设备唯一编号
            postData.put("device_id", device_id);
            Map<String, String> heads = new HashMap<>();
            TuYaUtil.url = "https://openapi.tuyacn.com";
            TuYaUtil.accessId = "nwtxyj7g3n9gmy7eh3vs";
            TuYaUtil.accessKey = "e8241ad015924b4c93e0815b3fd2976f";
            data = TuYaUtil.execute(token, "/v1.0/devices/" + device_id + "/door-lock/password-ticket", "POST", postData.toString(), heads);
            result = JSONObject.fromObject(data);
            System.out.println(result.toString());
            if (!result.containsKey("success") || !result.getBoolean("success")) {
                throw new Exception("获取token失败");
            }
            String ticket_id = result.getJSONObject("result").getString("ticket_id");
            /**
             * 第二步  免密 开门
             */
            postData = new JSONObject();
            //设备唯一编号
            postData.put("device_id", device_id);
            postData.put("ticket_id", ticket_id);
            TuYaUtil.url = "https://openapi.tuyacn.com";
            TuYaUtil.accessId = "nwtxyj7g3n9gmy7eh3vs";
            TuYaUtil.accessKey = "e8241ad015924b4c93e0815b3fd2976f";
            data = TuYaUtil.execute(token, "/v1.0/devices/" + device_id + "/door-lock/password-free/open-door", "POST", postData.toString(), heads);
            result = JSONObject.fromObject(data);
            System.out.println(result.toString());
            if (!result.containsKey("success") || !result.getBoolean("success")) {
                throw new Exception("获取token失败");
            }
            responseData.setCode(1);
            responseData.setMsg("开门成功");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return responseData;
    }

    public static String getToken() {
        String getTokenPath = "/v1.0/token?grant_type=1";
        TuYaUtil.accessId = "dh8g3j95fd7e9pqdgdhn";
        TuYaUtil.accessKey = "1ceee2fd7f004c8cbeb4b281b5cc4f81";
        Object result = TuYaUtil.execute(getTokenPath, "GET", "", new HashMap<>());
        JSONObject data = JSONObject.fromObject(result);
        System.out.println(data.toString());
        if (!data.getBoolean("success")) {
            return "";
        }
        return data.getJSONObject("result").getString("access_token");
    }


}
