package com.pms.czabspoliceinterface.lock.factory;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.pms.czabspoliceinterface.lock.bean.IPmsLock;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ER;
import com.pms.czpmsutils.constant.HOTEL_CONST;
import com.pms.czpmsutils.request.SmartLockRequest;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class TclSmart implements IPmsLock {
    private static Logger log = LoggerFactory.getLogger(TclSmart.class);
    @Override
    public ResponseData checkin(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String token = this.getToken(smartLockRequest, param);
            if (StrUtil.isEmpty(token)) {
                log.info("get token error");
                throw new Exception("get token error");
            }
            String url = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_URL);
            JSONArray guestInfoList = new JSONArray();
            for (int i = 0; i < smartLockRequest.getPeoples().size(); i++) {
                SmartLockRequest.RegistPerson registPerson = smartLockRequest.getPeoples().get(i);
                JSONObject guest = new JSONObject();
                guest.put("idNo", registPerson.getIdCode());
                guest.put("name", registPerson.getPersonName());
                guest.put("checkInTime", smartLockRequest.getCheckinTime().getTime() / 1000);
                guest.put("checkOutTime", smartLockRequest.getCheckoutTime().getTime() / 1000);
                guest.put("email", registPerson.getEmail());
                guest.put("phone", registPerson.getPhone());
                guest.put("sex", registPerson.getSex() == 0 ? "男" : "女");
                guest.put("birthday", IdcardUtil.getBirth(registPerson.getIdCode()));
                guestInfoList.add(guest);
            }
            String key = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_KEY);
            String hotelId = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT);
            String sign = this.getSign(guestInfoList.toString(), key);
            String body = HttpRequest.post(url + "/pms/room/" + smartLockRequest.getRoomNum()).body(guestInfoList.toString()).header("sign", sign).header("hotel-id", hotelId).header("access-token", token).header("Content-Type", "application/json; charset=utf-8").timeout(5000).execute().body();
            log.info("body:{}", body);
            JSONObject resultMap = JSONObject.fromObject(body);
            if (!resultMap.containsKey("code") || !resultMap.getString("code").equals("200")) {
                throw new Exception("tcl checkin error:" + body);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public String getToken(SmartLockRequest smartLockRequest, JSONObject param) {
        String url = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_URL);
        String key = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_KEY);
        String hotelId = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT);
        String password = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_SEND_PASSWORD);
        JSONObject postData = new JSONObject();
        postData.put("hotel_id", hotelId);
        postData.put("password", password);
        String sign = this.getSign(postData.toString(), key);
        log.info("sign:" + sign);
        String body = HttpRequest.post(url + "/pms/token").body(postData.toString()).header("sign", sign).header("Content-Type", "application/json; charset=utf-8").timeout(5000).execute().body();
        log.info("body:" + body);
        JSONObject resultMap = JSONObject.fromObject(body);
        if (!resultMap.containsKey("code") || !resultMap.getString("code").equals("200")) {
            return "";
        }
        String token = resultMap.getString("message");
        log.info("token:" + token);
        return token;
    }

    public String getSign(String data, String key) {
        try {
            Mac sha256Hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "HmacSHA256");
            sha256Hmac.init(secretKey);
            byte[] hmacResult = sha256Hmac.doFinal(data.getBytes("UTF-8"));
            String base64Result = Base64.getEncoder().encodeToString(hmacResult);
            return base64Result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    public ResponseData checkout(SmartLockRequest smartLockRequest, JSONObject param) {

        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String token = this.getToken(smartLockRequest, param);
            if (StrUtil.isEmpty(token)) {
                log.info("get token error");
                throw new Exception("get token error");
            }
            String url = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_URL);
            String key = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_KEY);
            String hotelId = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT);
            String sign = this.getSign(smartLockRequest.getRoomNum(), key);
            String body = HttpRequest.delete(url + "/pms/room/" + smartLockRequest.getRoomNum()).header("sign", sign).header("hotel-id", hotelId).header("access-token", token).header("Content-Type", "application/json; charset=utf-8").body(new JSONObject().toString()).timeout(5000).execute().body();
            log.info("body:{}", body);
            JSONObject resultMap = JSONObject.fromObject(body);
            if (!resultMap.containsKey("code") || !resultMap.getString("code").equals("200")) {
                throw new Exception("tcl checkin error:" + body);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData stayover(SmartLockRequest smartLockRequest, JSONObject param) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String token = this.getToken(smartLockRequest, param);
            if (StrUtil.isEmpty(token)) {
                log.info("get token error");
                throw new Exception("get token error");
            }
            String url = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_URL);
            Map<String, Object> postData = new HashMap<>();
            postData.put("checkInTime", smartLockRequest.getCheckinTime().getTime() / 1000);
            postData.put("checkOutTime", smartLockRequest.getCheckoutTime().getTime() / 1000);
            log.info("postData:{}", postData.toString());
            String key = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_KEY);
            String hotelId = param.getString(HOTEL_CONST.HOTEL_SETTING_SMART_LOCK_LOGIN_ACCOUNT);
            String sign = this.getSign(smartLockRequest.getRoomNum(), key);
            String body = HttpRequest.put(url + "/pms/room/" + smartLockRequest.getRoomNum()).header("sign", sign).header("hotel-id", hotelId).header("access-token", token).form(postData).timeout(5000).execute().body();
            log.info("body:{}", body);
            JSONObject resultMap = JSONObject.fromObject(body);
            if (!resultMap.containsKey("code") || !resultMap.getString("code").equals("200")) {
                throw new Exception("tcl checkin error:" + body);
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseData.setResult(ER.ERR);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    @Override
    public ResponseData updateGuestInfo(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }

    @Override
    public ResponseData changeRoom(SmartLockRequest smartLockRequest, JSONObject param) {
        return null;
    }
}
