
package com.pms.czabspoliceinterface.lock.boonlive;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.pms.czabspoliceinterface.lock.boonlive package.
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.pms.czabspoliceinterface.lock.boonlive
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link CheckIn2 }
     * 
     */
    public CheckIn2 createCheckIn2() {
        return new CheckIn2();
    }

    /**
     * Create an instance of {@link RentRoom }
     * 
     */
    public RentRoom createRentRoom() {
        return new RentRoom();
    }

    /**
     * Create an instance of {@link CheckInResponse }
     * 
     */
    public CheckInResponse createCheckInResponse() {
        return new CheckInResponse();
    }

    /**
     * Create an instance of {@link CheckOut }
     * 
     */
    public CheckOut createCheckOut() {
        return new CheckOut();
    }

    /**
     * Create an instance of {@link CheckOutResponse }
     * 
     */
    public CheckOutResponse createCheckOutResponse() {
        return new CheckOutResponse();
    }

    /**
     * Create an instance of {@link ChangePhoneNumber }
     * 
     */
    public ChangePhoneNumber createChangePhoneNumber() {
        return new ChangePhoneNumber();
    }

    /**
     * Create an instance of {@link ChangePhoneNumberResponse }
     * 
     */
    public ChangePhoneNumberResponse createChangePhoneNumberResponse() {
        return new ChangePhoneNumberResponse();
    }

    /**
     * Create an instance of {@link CheckIn2Response }
     * 
     */
    public CheckIn2Response createCheckIn2Response() {
        return new CheckIn2Response();
    }

    /**
     * Create an instance of {@link RentRoomResponse }
     * 
     */
    public RentRoomResponse createRentRoomResponse() {
        return new RentRoomResponse();
    }

    /**
     * Create an instance of {@link CheckIn }
     * 
     */
    public CheckIn createCheckIn() {
        return new CheckIn();
    }

    /**
     * Create an instance of {@link UploadPhoto }
     * 
     */
    public UploadPhoto createUploadPhoto() {
        return new UploadPhoto();
    }

    /**
     * Create an instance of {@link UploadPhotoResponse }
     * 
     */
    public UploadPhotoResponse createUploadPhotoResponse() {
        return new UploadPhotoResponse();
    }

}
