
package com.pms.czabspoliceinterface.lock.boonlive;

import javax.xml.namespace.QName;
import javax.xml.ws.*;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "blwws", targetNamespace = "http://www.blw.com/", wsdlLocation = "http://pms.boonlive-rcu.com:89/blwws.asmx?WSDL")
public class Blwws
    extends Service
{

    private final static URL BLWWS_WSDL_LOCATION;
    private final static WebServiceException BLWWS_EXCEPTION;
    private final static QName BLWWS_QNAME = new QName("http://www.blw.com/", "blwws");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://pms.boonlive-rcu.com:89/blwws.asmx?WSDL");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        BLWWS_WSDL_LOCATION = url;
        BLWWS_EXCEPTION = e;
    }

    public Blwws() {
        super(__getWsdlLocation(), BLWWS_QNAME);
    }

    public Blwws(WebServiceFeature... features) {
        super(__getWsdlLocation(), BLWWS_QNAME, features);
    }

    public Blwws(URL wsdlLocation) {
        super(wsdlLocation, BLWWS_QNAME);
    }

    public Blwws(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, BLWWS_QNAME, features);
    }

    public Blwws(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public Blwws(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns BlwwsSoap
     */
    @WebEndpoint(name = "blwwsSoap")
    public BlwwsSoap getBlwwsSoap() {
        return super.getPort(new QName("http://www.blw.com/", "blwwsSoap"), BlwwsSoap.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns BlwwsSoap
     */
    @WebEndpoint(name = "blwwsSoap")
    public BlwwsSoap getBlwwsSoap(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.blw.com/", "blwwsSoap"), BlwwsSoap.class, features);
    }

    /**
     * 
     * @return
     *     returns BlwwsSoap
     */
    @WebEndpoint(name = "blwwsSoap12")
    public BlwwsSoap getBlwwsSoap12() {
        return super.getPort(new QName("http://www.blw.com/", "blwwsSoap12"), BlwwsSoap.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns BlwwsSoap
     */
    @WebEndpoint(name = "blwwsSoap12")
    public BlwwsSoap getBlwwsSoap12(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.blw.com/", "blwwsSoap12"), BlwwsSoap.class, features);
    }

    private static URL __getWsdlLocation() {
        if (BLWWS_EXCEPTION!= null) {
            throw BLWWS_EXCEPTION;
        }
        return BLWWS_WSDL_LOCATION;
    }

}
