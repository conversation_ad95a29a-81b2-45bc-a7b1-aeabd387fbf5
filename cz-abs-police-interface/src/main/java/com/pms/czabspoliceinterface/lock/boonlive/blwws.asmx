<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://www.blw.com/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" targetNamespace="http://www.blw.com/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://www.blw.com/">
      <s:element name="CheckIn">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="key" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="roomNumber" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="checkInDate" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="xmlString" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="errorMsg" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="phoneNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="idNumber" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckInResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="CheckInResult" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="errorMsg" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckIn2">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="key" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="roomNumber" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="checkInDate" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="xmlString" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="errorMsg" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="checkInID" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="phoneNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="idNumber" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckIn2Response">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="CheckIn2Result" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="errorMsg" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="checkInID" type="s:long" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UploadPhoto">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="key" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="checkInID" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="idType" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="idCard" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="name" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="sex" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="birthday" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="photoUrl" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="photo" type="s:base64Binary" />
            <s:element minOccurs="0" maxOccurs="1" name="errorMsg" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UploadPhotoResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UploadPhotoResult" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="errorMsg" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ChangePhoneNumber">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="key" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="roomNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="phoneNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="idNumber" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="errorMsg" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ChangePhoneNumberResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="ChangePhoneNumberResult" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="errorMsg" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckOut">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="key" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="roomNumber" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="checkOutDate" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="errorMsg" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CheckOutResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="CheckOutResult" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="errorMsg" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="RentRoom">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="key" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="code" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="roomNumber" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="rentDate" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="errorMsg" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="RentRoomResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="RentRoomResult" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="errorMsg" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="CheckInSoapIn">
    <wsdl:part name="parameters" element="tns:CheckIn" />
  </wsdl:message>
  <wsdl:message name="CheckInSoapOut">
    <wsdl:part name="parameters" element="tns:CheckInResponse" />
  </wsdl:message>
  <wsdl:message name="CheckIn2SoapIn">
    <wsdl:part name="parameters" element="tns:CheckIn2" />
  </wsdl:message>
  <wsdl:message name="CheckIn2SoapOut">
    <wsdl:part name="parameters" element="tns:CheckIn2Response" />
  </wsdl:message>
  <wsdl:message name="UploadPhotoSoapIn">
    <wsdl:part name="parameters" element="tns:UploadPhoto" />
  </wsdl:message>
  <wsdl:message name="UploadPhotoSoapOut">
    <wsdl:part name="parameters" element="tns:UploadPhotoResponse" />
  </wsdl:message>
  <wsdl:message name="ChangePhoneNumberSoapIn">
    <wsdl:part name="parameters" element="tns:ChangePhoneNumber" />
  </wsdl:message>
  <wsdl:message name="ChangePhoneNumberSoapOut">
    <wsdl:part name="parameters" element="tns:ChangePhoneNumberResponse" />
  </wsdl:message>
  <wsdl:message name="CheckOutSoapIn">
    <wsdl:part name="parameters" element="tns:CheckOut" />
  </wsdl:message>
  <wsdl:message name="CheckOutSoapOut">
    <wsdl:part name="parameters" element="tns:CheckOutResponse" />
  </wsdl:message>
  <wsdl:message name="RentRoomSoapIn">
    <wsdl:part name="parameters" element="tns:RentRoom" />
  </wsdl:message>
  <wsdl:message name="RentRoomSoapOut">
    <wsdl:part name="parameters" element="tns:RentRoomResponse" />
  </wsdl:message>
  <wsdl:portType name="blwwsSoap">
    <wsdl:operation name="CheckIn">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">开房&lt;br/&gt;key：验证码（我方提供），code：编码（我方提供），roomNumber：房号，checkInDate：入住日期，xmlString：客人信息，errorMsg：返回错误信息，phoneNumber：手机号码（多个以英文逗号,隔开），idNumber：身份证号（多个以英文逗号,隔开）：获取微信登录验证码</wsdl:documentation>
      <wsdl:input message="tns:CheckInSoapIn" />
      <wsdl:output message="tns:CheckInSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckIn2">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">开房&lt;br/&gt;key：验证码（我方提供），code：编码（我方提供），roomNumber：房号，checkInDate：入住日期，xmlString：客人信息，errorMsg：返回错误信息，checkInID：返回入住记录ID，phoneNumber：手机号码（多个以英文逗号,隔开），idNumber：身份证号（多个以英文逗号,隔开）：获取微信登录验证码</wsdl:documentation>
      <wsdl:input message="tns:CheckIn2SoapIn" />
      <wsdl:output message="tns:CheckIn2SoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UploadPhoto">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">上传入住人信息&lt;br/&gt;key：验证码（我方提供），code：编码（我方提供），checkInID：入住记录ID，idType：证件类型(0身份证，1护照，2军官证，3其他)，idCard：证件号码，name：姓名，sex：性别(0女，1男，2其他)，birthday：出生日期(1999-01-01)，photoUrl：图片路径（与photo二选一），photo：图片（二进制），errorMsg：错误信息</wsdl:documentation>
      <wsdl:input message="tns:UploadPhotoSoapIn" />
      <wsdl:output message="tns:UploadPhotoSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ChangePhoneNumber">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">变更手机号&lt;br/&gt;key：验证码（我方提供），code：编码（我方提供），roomNumber：房号，errorMsg：错误信息，phoneNumber：手机号码（多个以英文逗号,隔开），idNumber：身份证号：获取验证码</wsdl:documentation>
      <wsdl:input message="tns:ChangePhoneNumberSoapIn" />
      <wsdl:output message="tns:ChangePhoneNumberSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CheckOut">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">退房&lt;br/&gt;key：验证码（我方提供），code：编码（我方提供），roomNumber：房号，checkOutDate：退房日期，errorMsg：错误信息</wsdl:documentation>
      <wsdl:input message="tns:CheckOutSoapIn" />
      <wsdl:output message="tns:CheckOutSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="RentRoom">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">待租&lt;br/&gt;key：验证码（我方提供），code：编码（我方提供），roomNumber：房号，rentDate：变更待租日期，errorMsg：错误信息</wsdl:documentation>
      <wsdl:input message="tns:RentRoomSoapIn" />
      <wsdl:output message="tns:RentRoomSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="blwwsSoap" type="tns:blwwsSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="CheckIn">
      <soap:operation soapAction="http://www.blw.com/CheckIn" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckIn2">
      <soap:operation soapAction="http://www.blw.com/CheckIn2" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadPhoto">
      <soap:operation soapAction="http://www.blw.com/UploadPhoto" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangePhoneNumber">
      <soap:operation soapAction="http://www.blw.com/ChangePhoneNumber" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckOut">
      <soap:operation soapAction="http://www.blw.com/CheckOut" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RentRoom">
      <soap:operation soapAction="http://www.blw.com/RentRoom" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="blwwsSoap12" type="tns:blwwsSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="CheckIn">
      <soap12:operation soapAction="http://www.blw.com/CheckIn" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckIn2">
      <soap12:operation soapAction="http://www.blw.com/CheckIn2" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UploadPhoto">
      <soap12:operation soapAction="http://www.blw.com/UploadPhoto" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangePhoneNumber">
      <soap12:operation soapAction="http://www.blw.com/ChangePhoneNumber" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckOut">
      <soap12:operation soapAction="http://www.blw.com/CheckOut" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RentRoom">
      <soap12:operation soapAction="http://www.blw.com/RentRoom" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="blwws">
    <wsdl:port name="blwwsSoap" binding="tns:blwwsSoap">
      <soap:address location="http://pms.boonlive-rcu.com:89/blwws.asmx" />
    </wsdl:port>
    <wsdl:port name="blwwsSoap12" binding="tns:blwwsSoap12">
      <soap12:address location="http://pms.boonlive-rcu.com:89/blwws.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>