package com.pms.czabspoliceinterface.service;

import com.github.pagehelper.Page;
import com.pms.czabspoliceinterface.bean.UploadRecordRequestEntity;
import com.pms.czabspoliceinterface.bean.WaitCheckinPersonInfoReqeust;
import com.pms.czpmsutils.CosFileUtil;
import com.pms.czpmsutils.HotelUtils;
import com.pms.czpmsutils.MD5Util;
import com.pms.czpmsutils.ResponseData;
import com.pms.czpmsutils.constant.ERROR_MSG;
import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.pmsorder.bean.PersonInfo;
import com.pms.pmsorder.bean.RegistPersonHealthCode;
import com.pms.pmsorder.bean.search.PersonInfoSearch;
import com.pms.pmsorder.dao.PersonInfoDao;
import com.pms.pmsorder.dao.RegistPersonHealthCodeDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.Date;

@Service
@Primary
public class WaitCheckinPersonImpl extends BaseService {

    @Autowired
    PersonInfoDao personInfoDao;
    @Autowired
    RegistPersonHealthCodeDao registPersonHealthCodeDao;


    @Transactional(rollbackFor = Exception.class)
    public ResponseData addWaitCheckinPersonInfo(WaitCheckinPersonInfoReqeust waitCheckinPersonInfoReqeust) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            if (waitCheckinPersonInfoReqeust.getPersonInfo() == null) {
                throw new Exception("宾客信息不能空");
            }
            TbUserSession tbUserSession = this.getTbUserSession(waitCheckinPersonInfoReqeust.getSessionToken());
            if (tbUserSession == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            PersonInfo personInfo = waitCheckinPersonInfoReqeust.getPersonInfo();
            personInfo.setCreateUserId(tbUserSession.getUserId());
            personInfo.setCreateTime(new Date());
            personInfo.setHid(tbUserSession.getHid());
            personInfo.setHotelGroupId(tbUserSession.getHotelGroupId());
            String idImage = personInfo.getIdImage();
            if (null != idImage && !idImage.equals("") && idImage.length() > 255) {
                String photo = idImage.replace(' ', '+');
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(photo, 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(personInfo.getIdCode(), "UTF-8") + ".jpeg");
                personInfo.setIdImage(uploadObjectRsp.getFileName());
            }
            String cameraImage = personInfo.getCameraImage();
            if (null != cameraImage && !cameraImage.equals("") && cameraImage.length() > 255) {
                String s = personInfo.getIdCode() + personInfo.getHid() + HotelUtils.currentTime();
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(cameraImage.replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                personInfo.setCameraImage(uploadObjectRsp.getFileName());
            }
            Integer insert = personInfoDao.insert(personInfo);
            if (insert < 1) {
                throw new Exception("添加待入住宾客信息失败");
            }

            if (waitCheckinPersonInfoReqeust.getRegistPersonHealthCode() != null) {
                RegistPersonHealthCode registPersonHealthCode = waitCheckinPersonInfoReqeust.getRegistPersonHealthCode();
                registPersonHealthCode.setHid(tbUserSession.getHid());
                registPersonHealthCode.setPersonInfoId(personInfo.getId());
                registPersonHealthCode.setCreateTime(new Date());
                registPersonHealthCode.setCreateUserId(tbUserSession.getUserId());
                insert = registPersonHealthCodeDao.insert(registPersonHealthCode);
                if (insert < 1) {
                    throw new Exception("添加入住人健康码数据失败");
                }
            }
        } catch (Exception e) {
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return responseData;
    }

    public ResponseData getWaitCheckinPersonInfo(PersonInfoSearch personInfoSearch) {
        ResponseData responseData = ResponseData.newSuccessData();
        try {
            String sessionToken = personInfoSearch.getSessionToken();
            TbUserSession tbUserSession = this.getTbUserSession(sessionToken);
            if (tbUserSession == null) {
                throw new Exception(ERROR_MSG.INVALID_SESSION);
            }
            personInfoSearch.setHid(tbUserSession.getHid());
            Page<PersonInfo> personInfos = personInfoDao.selectBySearch(personInfoSearch);
            responseData.setData(personInfos);
        } catch (Exception e) {
            responseData.setCode(-1);
            responseData.setMsg("业务处理异常");
        }
        return responseData;
    }

    public boolean addPersonInfo(JSONObject param) {

        try {
            UploadRecordRequestEntity uploadRecordRequestEntity = (UploadRecordRequestEntity) JSONObject.toBean(param, UploadRecordRequestEntity.class);
            if (uploadRecordRequestEntity.getIdCardInfo() == null) {
                return false;
            }
            //设备号，查询设备号，找到跟哪家酒店进行关联的。
            String deviceCode = uploadRecordRequestEntity.getDeviceCode();
            JSONObject postData = new JSONObject();
            postData.put("mac", deviceCode);
            Object machineByMac = this.findMachineByMac(postData);
            JSONObject res = JSONObject.fromObject(machineByMac);
            if (res.getInt("code") != 1) {
                return false;
            }
            JSONArray machineList = res.getJSONObject("data").getJSONArray("data");
            if (machineList == null || machineList.size() != 1) {
                return false;
            }
            String uuid = machineList.getJSONObject(0).getString("uuid");
            TbUserSession tbUserSession = this.getTbUserSession(uuid);
            Integer hid = tbUserSession.getHid();
            PersonInfo personInfo = new PersonInfo();
            personInfo.setCreateTime(new Date());
            personInfo.setCreateUserId(tbUserSession.getUserId());
            personInfo.setCreateUserName(tbUserSession.getUserName());
            personInfo.setHid(hid);
            personInfo.setHotelGroupId(tbUserSession.getHotelGroupId());
            personInfo.setPersonName(uploadRecordRequestEntity.getIdCardInfo().getName());
            personInfo.setSex(uploadRecordRequestEntity.getIdCardInfo().getSex().equals("男") ? 0 : 1);
            personInfo.setAddress(uploadRecordRequestEntity.getIdCardInfo().getAddress());
            personInfo.setIdCode(uploadRecordRequestEntity.getIdCardInfo().getIdCardNo());
            String birth = uploadRecordRequestEntity.getIdCardInfo().getBirth();
            personInfo.setBirthday(Integer.parseInt(birth));
            String nation = uploadRecordRequestEntity.getIdCardInfo().getNation();
            if (nation.indexOf("族") < 1) {
                nation += "族";
            }
            personInfo.setNation(HotelUtils.nationMap.getInt(nation));
            //处理照片
            String idCardPicBase64 = uploadRecordRequestEntity.getIdCardInfo().getIdCardPicBase64();
            if (idCardPicBase64 != null) {
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(idCardPicBase64.replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(personInfo.getIdCode(), "UTF-8") + ".jpeg");
                personInfo.setIdImage(uploadObjectRsp.getFileName());
            }
            String imageBase64 = uploadRecordRequestEntity.getImageBase64();
            if (imageBase64 != null) {
                String s = uploadRecordRequestEntity + hid.toString() + HotelUtils.currentTime();
                CosFileUtil.UploadObjectRsp uploadObjectRsp = CosFileUtil.uploadObjectBase64(imageBase64.replace(' ', '+'), 0, "idcimg", "image/jpeg", MD5Util.MD5Encode(s, "UTF-8") + ".jpeg");
                personInfo.setCameraImage(uploadObjectRsp.getFileName());
            }
            Integer insert = personInfoDao.insert(personInfo);
            if (insert < 1) {
                return false;
            }
            RegistPersonHealthCode registPersonHealthCode = new RegistPersonHealthCode();
            registPersonHealthCode.setHid(hid);
            registPersonHealthCode.setCreateUserId(tbUserSession.getUserId());
            registPersonHealthCode.setCreateTime(new Date());
            registPersonHealthCode.setCreateUserName(tbUserSession.getUserName());
            registPersonHealthCode.setPersonInfoId(personInfo.getId());
            registPersonHealthCode.setPersonName(personInfo.getPersonName());
            registPersonHealthCode.setTemperature(String.valueOf(uploadRecordRequestEntity.getTemperature()));
            String status = uploadRecordRequestEntity.getGuokangCode().getStatus();
            registPersonHealthCode.setIdCode(personInfo.getIdCode());
            if (status != null && !status.equals("")) {
                registPersonHealthCode.setCode(Integer.parseInt(status));
                int code = Integer.parseInt(status);
                if (code == 1) {
                    registPersonHealthCode.setCodeStr("绿码");
                } else if (code == 2) {
                    registPersonHealthCode.setCodeStr("绿码");
                } else if (code == 3) {
                    registPersonHealthCode.setCodeStr("红码");
                }
            }
            insert = registPersonHealthCodeDao.insert(registPersonHealthCode);
            if (insert < 1) {
                return false;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
