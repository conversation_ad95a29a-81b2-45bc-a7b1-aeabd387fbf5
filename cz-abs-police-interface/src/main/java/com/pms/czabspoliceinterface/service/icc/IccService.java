package com.pms.czabspoliceinterface.service.icc;

import com.dahuatech.icc.brm.model.v202010.card.BrmCard;
import com.dahuatech.icc.brm.model.v202010.card.BrmCardBatchAddRequest;
import com.dahuatech.icc.brm.model.v202010.person.BrmPersonQueryByIdCardRequest;
import com.dahuatech.icc.brm.model.v202010.person.BrmPersonQueryByIdCardResponse;
import com.dahuatech.icc.brm.model.v202010.person.PersonData;
import com.dahuatech.icc.exception.ClientException;
import com.dahuatech.icc.oauth.http.DefaultClient;
import com.dahuatech.icc.oauth.http.IClient;
import com.dahuatech.icc.oauth.http.IccResponse;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
public class IccService {
    public IccService() throws ClientException {
        iClient = new DefaultClient();
    }

    private IClient iClient;

    /**
     * 根据证件号查询人员
     *
     * @return
     */
    public PersonData queryPerson(String idCard) throws ClientException {
        BrmPersonQueryByIdCardRequest request = new BrmPersonQueryByIdCardRequest(idCard);
        BrmPersonQueryByIdCardResponse response = iClient.doAction(request, request.getResponseClass());
        if (response == null)
            return null;
        return response.getData();
    }

    /**
     * 批量开卡
     *
     * @param cardList
     * @return
     */
    public IccResponse addCardList(List<BrmCard> cardList) {
        BrmCardBatchAddRequest.Builder builder = BrmCardBatchAddRequest.builder();
        builder.cardList(cardList);
        try {
            BrmCardBatchAddRequest request = builder.build();
            return iClient.doAction(request, request.getResponseClass());
        } catch (ClientException e) {
            e.printStackTrace();
        }

        return null;
    }
}
