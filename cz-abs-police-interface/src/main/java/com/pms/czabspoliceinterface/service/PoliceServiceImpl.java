package com.pms.czabspoliceinterface.service;

import com.pms.czpmsutils.constant.user.BaseService;
import com.pms.czpmsutils.constant.user.TbUserSession;
import com.pms.czpmsutils.view.GuestModel;
import com.pms.pmsorder.bean.RegistPerson;
import com.pms.pmsorder.bean.RegistPersonHealthCode;
import com.pms.pmsorder.bean.search.RegistPersonSearch;
import com.pms.pmsorder.dao.RegistPersonDao;
import com.pms.pmsorder.dao.RegistPersonHealthCodeDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class PoliceServiceImpl extends BaseService {

    @Autowired
    RegistPersonDao registPersonDao;
    @Autowired
    RegistPersonHealthCodeDao registPersonHealthCodeDao;

    public boolean updateRegistPersonInfo(GuestModel guestModel) {
        if (guestModel.getGuestNo() == null) {
            return false;
        }
        if (guestModel.getiDCode() == null) {
            return false;
        }
        try {
            String tokenId = guestModel.getTokenId();
            TbUserSession tbUserSession = this.getTbUserSession(tokenId);
            Integer hid = tbUserSession.getHid();
            RegistPersonSearch registPersonSearch = new RegistPersonSearch();
            registPersonSearch.setHid(hid);
            registPersonSearch.setIdCode(guestModel.getiDCode());
            registPersonSearch.setRegistState(0);
            registPersonSearch.setRoomNum(guestModel.getRoomNo());
            RegistPerson registPerson = registPersonDao.selectBySearchOne(registPersonSearch);
            if (registPerson == null ){
                return false;
            }
            registPerson.setGuestNo(guestModel.getGuestNo());
            registPerson.setGuestId(guestModel.getGuestId());
            Integer update = registPersonDao.update(registPerson);
            if (update < 1){
                return false;
            }
            RegistPersonHealthCode registPersonHealthCode = new RegistPersonHealthCode();
            registPersonHealthCode.setHid(hid);
            registPersonHealthCode.setRegistPersonId(registPerson.getRegistPersonId());
            registPersonHealthCode.setIdCode(registPerson.getIdCode());
            registPersonHealthCode.setCreateTime(new Date());
            registPersonHealthCode.setCreateUserId(tbUserSession.getUserId());
            registPersonHealthCode.setPersonName(registPerson.getPersonName());
            if (guestModel.getGoAddress()!=null){
                registPersonHealthCode.setGoAddress(guestModel.getGoAddress());
            }
            if (guestModel.getTemperature()!=null){
                registPersonHealthCode.setTemperature(guestModel.getTemperature());
            }
            if (guestModel.getNuclein()!=null){
                registPersonHealthCode.setNuclein(guestModel.getNuclein());
            }
            if (guestModel.getVaccinum()!=null){
                registPersonHealthCode.setVaccinum(guestModel.getVaccinum());
            }

            if (guestModel.getHealthCode()!=null && !guestModel.getHealthCode().equals("")){
                registPersonHealthCode.setCode(Integer.parseInt(guestModel.getHealthCode()) );
                String healthCode = guestModel.getHealthCode();
                if (healthCode.equals("1")){
                    registPersonHealthCode.setCodeStr("绿码");
                }
                else if (healthCode.equals("2")){
                    registPersonHealthCode.setCodeStr("黄码");
                }
                else if (healthCode.equals("3")){
                    registPersonHealthCode.setCodeStr("红码");
                }
            }

            Integer insert = registPersonHealthCodeDao.insert(registPersonHealthCode);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;


    }
}
